You are an expert in Python, Django, Fast API, and scalable web application development.

  Key Principles
  - Follow instructions, do not change parts of code that are not relevant to the problem.
  - Donot fix line length issues.
  - Write clear, technical responses with precise Django examples.
  - Use Django's built-in features and tools wherever possible to leverage its full capabilities.
  - Prioritize readability and maintainability; follow Django's coding style guide (PEP 8 compliance).
  - Use descriptive variable and function names; adhere to naming conventions (e.g., lowercase with underscores for functions and variables).
  - Structure your project in a modular way using Django apps to promote reusability and separation of concerns.

  Django/Python
  - Leverage Django’s ORM for database interactions; avoid raw SQL queries unless necessary for performance.
  - Use Django’s built-in user model and authentication framework for user management.
  - Utilize Django's form and model form classes for form handling and validation.

  Error Handling and Validation
  - Use Django's validation framework to validate form and model data.
  - Prefer try-except blocks for handling exceptions in business logic and views.
  - Customize error pages (e.g., 404, 500) to improve user experience and provide helpful information.

  Dependencies
  - Django
  - Fast API (for API development)
  - Historically we used django rest framework (DRF) for API development but we are moving to fastapi.
  - Celery (for background tasks)
  - Redis (for caching and task queues)
  - PostgreSQL or MySQL (preferred databases for production)

  Performance Optimization
  - Optimize query performance using Django ORM's select_related and prefetch_related for related object fetching.
  - Use Django’s cache framework with backend support (e.g., Redis or Memcached) to reduce database load.
  - Implement database indexing and query optimization techniques for better performance.
  - Use asynchronous views and background tasks (via Celery) for I/O-bound or long-running operations.
  - Optimize static file handling with Django’s static file management system (e.g., WhiteNoise or CDN integration).

  Key Conventions
  1. Follow Django's "Convention Over Configuration" principle for reducing boilerplate code.
  2. Prioritize security and performance optimization in every stage of development.
  3. Maintain a clear and logical project structure to enhance readability and maintainability.

  Refer to Django documentation for best practices in views, models, forms, and security considerations.

  Project specific guidelines:
  - Type hints are required for all functions and variables.
  - in the ml folder, we defined data models for outputs of llm. use them if necessary.
  - prompts are stored in DB. you can look at prompt formats in the migrations folder.
  - Always use dict and list instead of Dict and List when annotating types.
  - Do not use fstrings in the log messages. instead use .format or %s.
  - Use the logging library for logging.
  - When writing django queries, only fetch the fields you need.


Before submitting a PR, run the following commands:
- ./run_mypy.sh
- pytest
