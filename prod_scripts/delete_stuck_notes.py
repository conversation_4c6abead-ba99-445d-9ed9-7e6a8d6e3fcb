from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


def execute(email="<EMAIL>"):  # type: ignore[no-untyped-def]
    uploaded_notes = Note.objects.defer("raw_asr_response", "diarized_trans_with_names", "summary_by_topics").filter(
        status="uploaded"
    )
    tk = User.objects.get(email=email)
    uploaded_notes = uploaded_notes.defer("raw_asr_response", "diarized_trans_with_names", "summary_by_topics").filter(
        note_owner=tk
    )
    for note in uploaded_notes:
        note.is_deleted = True
        note.save()
