# Zeplyn Codebase Structure Guide

This document provides an overview of the code structure and logical modules in the Zeplyn webapp. It's designed to help developers and LLMs navigate and understand the codebase when making changes or adding features.

## 1. High-Level Architecture

Zeplyn is built as a Django application with FastAPI endpoints, using a PostgreSQL database and Celery for background processing. The main components are:

- **Django Apps**: Core business logic and data models
- **FastAPI Routers**: RESTful API endpoints
- **Celery Tasks**: Background and scheduled processing
- **ML Services**: Integration with LLMs and other AI services

## 2. Core Django Apps

### deepinsights.core

Location: `/deepinsights/core/`

Contains base functionality used throughout the application:
- AWS integration (`aws.py`)
- Behavior mixins (`behaviours.py`)
- Core exceptions (`exceptions.py`)
- Response formatting (`response.py`)

#### ML Services

Location: `/deepinsights/core/ml/`

Key files:
- `genai.py`: LLM service integration (OpenAI, Claude)
- `process_transcript.py`: Processes raw transcripts into structured data
- `asr.py`: Voice transcription services
- `prompt_builder.py`: Constructs and formats prompts for AI models
- `agenda.py`: Meeting agenda generation
- `search.py`: Semantic search functionality

#### Integrations

Location: `/deepinsights/core/integrations/`

- **Calendar**: `/deepinsights/core/integrations/calendar/`
  - Google Calendar (`google.py`)
  - Microsoft Calendar (`microsoft.py`)
  - Calendar data parsing (`calendar_data_parser.py`)

- **CRM**: `/deepinsights/core/integrations/crm/`
  - Base CRM functionality (`crm_base.py`)
  - Base CRM models (`crm_models.py`)
  - CRM manager (`crm_manager.py`)
  - Salesforce (`salesforce.py`)
  - Wealthbox (`wealthbox.py`)
  - Redtail (`redtail.py`)

- **Meeting Bot**: `/deepinsights/core/integrations/meetingbot/`
  - Bot controller (`bot_controller.py`)
  - Recall.ai integration (`recall_ai.py`)
  - Twilio integration (`twilio.py`)

- **OAuth**: `/deepinsights/core/integrations/oauth/`
  - OAuth flows for various services

### deepinsights.meetingsapp

Location: `/deepinsights/meetingsapp/`

The main application handling meetings, notes, tasks, and clients.

#### Models

Location: `/deepinsights/meetingsapp/models/`

Key models:
- `note.py`: Central model for meeting notes and transcripts
- `attendees.py`: Meeting participants
- `client.py`: Client information
- `meeting_bot.py`: Bot configuration and status
- `client_recap.py`: Client recap materials
- `meeting_type.py`: Categories of meetings
- `organization.py`: Multi-tenant organization settings
- `task.py`: Action items from meetings
- `prompt.py`: LLM prompts storage
- `structured_meeting_data.py`: Structured data from meetings

#### Background Tasks

Location: `/deepinsights/meetingsapp/tasks.py`

Important tasks:
- Processing recordings and transcripts
- Generating meeting summaries
- Synchronizing with CRM systems
- Managing calendar events
- Creating follow-up materials
- Generating agendas

### deepinsights.users

Location: `/deepinsights/users/`

Handles user authentication, preferences, and settings.

Key files:
- User model: `/deepinsights/users/models/user.py`
- Feature flags: `/deepinsights/users/models/flags.py`

### deepinsights.flags

Location: `/deepinsights/flags/`

Feature flag system for controlled rollout of new features.

## 3. API Structure

Location: `/api/routers/`

FastAPI routers for different resource types:
- `note.py`: Meeting notes API
- `task.py`: Tasks and action items
- `attendee.py`: Meeting participants
- `bot.py` & `bot_webhooks.py`: Meeting bot integration
- `calendar.py`: Calendar events
- `client.py` & `client_search.py`: Client management
- `crm.py`: CRM integration
- `auth.py`: Authentication
- `search.py`: Search functionality
- `settings.py`: User and organization settings

Main API configuration: `/app/fastapi.py`

## 4. Background Processing (tasks.py)

Celery configuration: `/app/celery.py`
Main tasks: `/deepinsights/meetingsapp/tasks.py`

The tasks.py file is one of the most critical parts of the application, containing all the background processing logic that powers Zeplyn's core functionality. It handles the entire processing pipeline from raw recordings to finished meeting notes.

### Key Task Functions

1. **Recording Processing**
   - `process_bot_recording`: Processes meeting recordings from Recall.ai bots
   - `process_note_recording`: Processes voice memos uploaded through the app
   - `process_phone_call_recording`: Processes recordings from phone calls
   - `_process_note_common`: Shared logic for all recording types

2. **Transcript Processing**
   - Functions to extract transcripts from various sources
   - Speaker identification and diarization
   - Name substitution in transcripts (`substitute_names_in_transcript`)

3. **Content Generation**
   - Meeting summary generation
   - Task/action item extraction
   - Generation of structured follow-up data
   - Agenda preparation (`generate_agenda`)

4. **Data Management**
   - CRM client synchronization (`sync_crm_clients`)
   - Client recap (`process_client_recap`)
   - Structured data template generation
   - Deletion of old data (`delete_old_transcripts`, `delete_older_notes`)

5. **Calendar and Bot Management**
   - Updating bots for calendar events (`update_bots_for_calendar_events`)
   - Processing recurring events

6. **Organizational Tasks**
   - Export organization metrics
   - Email reports and notifications
   - Organization data deletion

7. **Utilities**
   - Thread pool management for parallel processing
   - File generation and email sending
   - Data reprocessing options for various scenarios

The application uses Celery to orchestrate these tasks, ensuring they run asynchronously and can be retried on failure.

## 5. Data Flow

Typical data flows in the application:

1. **Meeting Recording Processing**:
   - Bot records meeting → raw recording stored → ASR transcription → transcript processing → note generation → task extraction → CRM sync

2. **Meeting Preparation**:
   - Calendar event detected → client data fetched → previous interactions analyzed → agenda generated → prep materials created

3. **Search and Retrieval**:
   - User query → semantic encoding → vector search → relevant meeting segments returned

## 6. Configuration and Settings

Main settings: `/config/settings/settings.py`

Key configuration areas:
- Database connection
- AWS service integration
- AI service credentials
- CRM integration settings
- Feature flags

## 7. Django Admin Interface

Location: `/deepinsights/meetingsapp/admin.py`

The Django Admin interface is a critical component for managing and troubleshooting the application. It provides administrators with powerful tools to view, edit, and manage all aspects of the system.

### Key Admin Classes

1. **NoteAdmin**
   - Central interface for viewing and managing meeting notes
   - Actions for processing and reprocessing recordings
   - Displays transcripts, summaries, and metadata
   - Links to related objects (tasks, bots, attendees)
   - Functionality to delete transcripts

2. **OrganizationAdmin**
   - Manages organization settings and preferences
   - Tools for user onboarding via CSV import
   - Organization creation wizard
   - Actions to update bot preferences
   - Ability to disable or delete organizations

3. **MeetingBotAdmin**
   - Manages meeting bot configurations
   - Actions to reprocess recordings
   - Links to associated notes

4. **TaskAdmin**
   - View and manage action items from meetings
   - Filter by completion status and due dates
   - Links to related notes

5. **StructuredDataAdmin Classes**
   - Manage meeting templates, schemas, and rules
   - Template generation tools
   - JSON schema editors for structured data

6. **AttendeeAdmin**
   - Manages meeting participants
   - Shows speaking time and percentages
   - Links to associated users and clients

7. **ClientAdmin**
   - Manages client information
   - CRM integration data

8. **AudioBufferAdmin**
   - Tools for working with audio recordings
   - Download functionality for debugging

The admin interface provides custom actions, filters, and search capabilities for efficient data management and troubleshooting.

## 8. Testing

Test configuration: `/conftest.py`

Test approaches:
- Unit tests for individual components
- Integration tests for workflows
- API tests for endpoints

Most files have corresponding `*_test.py` files with tests for that module.

## 9. Common Development Tasks

When working on the codebase, here are the key files to look at for common tasks:

- **Adding a new API endpoint**: Create or modify files in `/api/routers/`
- **Changing data models**: Edit relevant files in `/deepinsights/meetingsapp/models/`
- **Modifying AI prompts**: Check `/deepinsights/meetingsapp/models/prompt.py` and `/deepinsights/core/ml/prompt_builder.py`
- **Adding CRM functionality**: Work with files in `/deepinsights/core/integrations/crm/`
- **Changing background tasks**: Modify `/deepinsights/meetingsapp/tasks.py`
- **Adding new meeting features**: Look at both the model in `/deepinsights/meetingsapp/models/` and corresponding API in `/api/routers/`
- **Adding a new feature flag**: Modify `/deepinsights/flags/flagdefs.py`

## 10. Development Workflow

When adding new features, the typical process is:

1. Define models in appropriate model files
2. Create migrations with `python manage.py makemigrations`
3. Add API endpoints in the proper router
4. Add necessary background tasks
5. Write tests for the new functionality
6. Update relevant documentation
7. Register models with the Django admin interface if needed

Always use feature flags for significant changes to control rollout to production. Feature flags are defined in `/deepinsights/flags/flagdefs.py` and can be used to gradually enable new functionality for specific users or organizations.
