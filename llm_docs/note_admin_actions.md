# Note Admin Processing Actions

This document describes the various actions available in the Django admin page for reprocessing notes and their differences.

## Context

In the Zeplyn application, meeting notes are processed through a pipeline that involves transcript generation, content extraction, and structured data creation. The Django admin interface provides several actions to reprocess notes or specific parts of notes when needed.

## Available Actions

### Processing Actions Based on Source

These actions reprocess the entire note based on the source of the transcript:

1. **Force reprocess note recording (mic)**
   - **Action name**: `process_note_recording_action`
   - **What it does**: Forces reprocessing of the note from the original audio recording
   - **Use case**: When the original processing failed or produced incorrect results
   - **Source**: Uses the original S3 stored audio file
   - **Function**: Calls `process_note_recording` with `force_process=True`

2. **Force reprocess note recording (mic) (audio buffers)**
   - **Action name**: `process_note_recording_from_audio_buffers_action`
   - **What it does**: Reprocesses the note using audio buffers instead of S3 media
   - **Use case**: When S3 media is unavailable or corrupted but audio buffers exist
   - **Source**: Uses audio chunks stored in AudioBuffer objects
   - **Function**: Calls `process_note_recording` with `force_process=True` and `use_audio_buffers=True`

3. **Reprocess note recording (bot)**
   - **Action name**: `process_bot_recording_action`
   - **What it does**: Reprocesses notes that were created from meeting bot recordings
   - **Use case**: When bot recordings were not properly processed initially
   - **Source**: Uses the meeting bot's recording
   - **Function**: Calls the bot's `processing_task` method

### Partial Processing Actions

These actions reprocess only specific parts of a note without regenerating the transcript:

1. **Reprocess tasks and takeaways only**
   - **Action name**: `reprocess_tasks`
   - **What it does**: Regenerates only the tasks and key takeaways
   - **Use case**: When tasks or action items were missed or incorrectly identified
   - **Function**: Calls `reprocess_note` with `tasks=True, summary=False, follow_ups=False`

2. **Reprocess summary only**
   - **Action name**: `reprocess_summary`
   - **What it does**: Regenerates only the meeting summary
   - **Use case**: When the summary is inadequate or needs improvement
   - **Function**: Calls `reprocess_note` with `tasks=False, summary=True, follow_ups=False`

3. **Reprocess templates only**
   - **Action name**: `reprocess_templates`
   - **What it does**: Regenerates only the structured follow-up data and templates
   - **Use case**: When follow-up content needs to be updated or when templates change
   - **Function**: Calls `reprocess_note` with `tasks=False, summary=False, follow_ups=True`

### Other Actions

1. **Delete transcript**
   - **Action name**: `delete_transcript_action`
   - **What it does**: Removes the transcript data from the note
   - **Use case**: When transcripts need to be purged for privacy or when they're incorrect
   - **Function**: Calls `delete_transcripts_for_notes`

## How to Use These Actions

1. Navigate to the Notes list in the Django admin interface (`/api/admin/meetingsapp/note/`)
2. Select one or more notes by checking the boxes on the left
3. Choose the appropriate action from the dropdown menu
4. Click "Go" to execute the action
5. Monitor Celery job status for progress updates

## Best Practices

- **For complete reprocessing**: Use one of the source-based actions (1-3)
- **For targeted updates**: Use one of the partial processing actions (4-6)
- **Choose the correct source**: Select the action that matches the note's original source (mic recording vs bot)
- **Audio buffers**: Only use the audio buffer option when S3 media is unavailable
- **Performance considerations**: Processing can be resource-intensive, so avoid mass-processing many notes at once

## Technical Implementation

The reprocessing actions are implemented in the `NoteAdmin` class in `deepinsights/meetingsapp/admin.py` and leverage Celery tasks defined in `deepinsights/meetingsapp/tasks.py`:

- `process_note_recording`: Processes notes from audio recordings
- `reprocess_note`: Selectively reprocesses parts of existing notes
- `delete_transcripts_for_notes`: Removes transcript data from notes
