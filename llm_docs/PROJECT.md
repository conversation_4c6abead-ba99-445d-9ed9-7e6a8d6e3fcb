# Zeplyn: AI-Powered Meeting Intelligence for Financial Advisors

## Project Overview

Zeplyn is a comprehensive AI-powered workflow automation platform designed specifically for financial advisors. The platform transforms how financial professionals conduct, document, and follow up on client meetings by automating repetitive tasks and extracting valuable insights from meeting content.

## Core Features

### Meeting Management
- Automated recording and transcription of client meetings
- Smart meeting join functionality via integration with calendar systems
- Support for various meeting platforms through Recall.ai

### AI-Powered Analysis
- Intelligent speaker identification and diarization
- Extraction of key topics, action items, and client needs
- Generation of comprehensive meeting summaries
- Client intelligence gathering from meeting context

### Document Generation
- Automated meeting notes with customizable templates
- Follow-up email generation
- Agenda creation for upcoming meetings
- Structured data extraction for specific meeting types

### Task Management
- Automatic creation of tasks/action items from meeting content
- Task assignment and tracking
- Reminders and notifications for upcoming deadlines

### CRM Integration
- Bidirectional sync with popular financial CRM systems (Salesforce, Wealthbox, Redtail)
- Client record updates based on meeting content
- Document attachment to CRM records

### Calendar Integration
- Sync with Google Calendar and Microsoft Calendar
- Automated scheduling and meeting preparation
- Meeting context and history before appointments

### Search & Discovery
- Full-text search across all meeting content
- Client-specific search capabilities
- Topic and theme discovery across multiple interactions

## Technical Stack

### Backend
- Django framework with PostgreSQL database
- FastAPI for REST API endpoints
- Celery for task processing and background jobs

### AI/ML Components
- Integration with multiple LLM providers (OpenAI GPT, Anthropic Claude)
- Custom prompt engineering for financial context
- ASR (Automatic Speech Recognition) for voice transcription

### Security & Authentication
- OAuth 2.0 authentication
- JWT for API security
- Robust data privacy controls

### Infrastructure
- AWS-based cloud deployment
- S3 for secure file storage
- Redis for caching and messaging

## Value Proposition

Zeplyn dramatically reduces the administrative burden on financial advisors by:
- Eliminating manual note-taking during client meetings
- Automating CRM updates and task creation
- Providing richer client insights from conversation analysis
- Ensuring consistent follow-up and task completion
- Creating a comprehensive, searchable record of all client interactions

This allows financial advisors to focus more on building client relationships and providing high-quality financial guidance, rather than documentation and administrative tasks.
