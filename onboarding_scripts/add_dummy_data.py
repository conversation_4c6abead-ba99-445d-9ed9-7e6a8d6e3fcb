import datetime
import json
import logging
import os
import uuid
from typing import Any

from django.db import transaction

from deepinsights.meetingsapp.management.commands.loadnotes import load_dumped_data
from deepinsights.users.models.user import User

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
DEMO_DATA_PATH = os.path.join(CURRENT_DIR, "dummy_note_data", "ChenFamily.json")


def identify_date_range(
    json_data: list[dict[str, Any]],
) -> tuple[datetime.datetime | None, datetime.datetime | None]:
    oldest = None
    newest = None

    for item in json_data:
        if "fields" in item and "created" in item["fields"]:
            created_date = datetime.datetime.fromisoformat(item["fields"]["created"].replace("Z", "+00:00"))

            if oldest is None or created_date < oldest:
                oldest = created_date

            if newest is None or created_date > newest:
                newest = created_date

    return oldest, newest


def calculate_time_shift(
    oldest_date: datetime.datetime | None, newest_date: datetime.datetime | None, days_to_shift: int = 5
) -> tuple[datetime.timedelta, float]:
    now = datetime.datetime.now(datetime.timezone.utc)

    if oldest_date and newest_date:
        original_timespan = (newest_date - oldest_date).total_seconds()
        time_shift = (now - datetime.timedelta(days=days_to_shift)) - oldest_date
    else:
        # Default time shift if no dates found
        time_shift = datetime.timedelta(0)
        original_timespan = 0

    return time_shift, original_timespan


def update_item_data(
    item: dict[str, Any],
    user: User,
    uuid_map: dict[str, str],
    date_references: tuple[datetime.datetime | None, datetime.datetime | None],
    time_shift: datetime.timedelta,
    original_timespan: float,
    days_to_shift: int = 5,
) -> dict[str, Any]:
    """
    Update the data for a single item

    Args:
        item: The item data to update
        user: The user to associate the data with
        uuid_map: Map of old UUIDs to new UUIDs
        date_references: Tuple of (oldest_date, newest_date)
        time_shift: The time shift to apply
        original_timespan: The original timespan of the data
        days_to_shift: Number of days to shift the data

    Returns:
        Updated item data
    """
    oldest_date, newest_date = date_references
    now = datetime.datetime.now(datetime.timezone.utc)

    # Skip user objects (we'll use the current user)
    if item.get("model") == "users.user":
        return item

    # Generate new UUID for this object if it has one
    if "fields" in item and "uuid" in item["fields"]:
        old_uuid = item["fields"]["uuid"]
        if old_uuid not in uuid_map:
            uuid_map[old_uuid] = str(uuid.uuid4())
        item["fields"]["uuid"] = uuid_map[old_uuid]

    # Update note owner to current user id if present
    if item.get("model") == "meetingsapp.note" and "note_owner" in item["fields"]:
        item["fields"]["note_owner"] = str(user.id)

        # Add user to authorized_users
        if "authorized_users" not in item["fields"]:
            item["fields"]["authorized_users"] = [[str(user.uuid)]]
        else:
            # Check if authorized_users structure matches expected format
            if isinstance(item["fields"]["authorized_users"], list):
                # If it's empty, add user
                if len(item["fields"]["authorized_users"]) == 0:
                    item["fields"]["authorized_users"] = [[str(user.uuid)]]
                else:
                    # If it has content, check structure and add user if needed
                    if isinstance(item["fields"]["authorized_users"][0], list):
                        # It has the expected structure
                        item["fields"]["authorized_users"][0] = [str(user.uuid)]
                    else:
                        # It doesn't have the expected structure, replace it
                        item["fields"]["authorized_users"] = [[str(user.uuid)]]
            else:
                # Not a list at all, replace it
                item["fields"]["authorized_users"] = [[str(user.uuid)]]

        # Update metadata if present
        if "metadata" in item["fields"]:
            item["fields"]["metadata"]["user_uuid"] = str(user.uuid)
            item["fields"]["metadata"]["user_name"] = user.get_full_name() or user.username

    # Special handling for task due dates and ownership
    if item.get("model") == "meetingsapp.task" and "fields" in item:
        if "due_date" in item["fields"] and item["fields"]["due_date"]:
            # Parse the original date
            original_date = datetime.datetime.fromisoformat(item["fields"]["due_date"].replace("Z", "+00:00"))

            # Apply the same time shift logic used for created/modified dates
            if original_timespan > 0 and oldest_date is not None:
                # Preserve relative spacing within the timeline
                progress = (original_date - oldest_date).total_seconds() / original_timespan
                new_date = (now - datetime.timedelta(days=days_to_shift)) + datetime.timedelta(
                    seconds=progress * original_timespan
                )
            else:
                # Simple shift if we couldn't determine timespan
                new_date = original_date + time_shift

            # Update the field with the new date
            item["fields"]["due_date"] = new_date.isoformat()

        # Update task owner and assignee to current user
        if "task_owner" in item["fields"]:
            item["fields"]["task_owner"] = [str(user.uuid)]

        if "assignee" in item["fields"]:
            item["fields"]["assignee"] = [str(user.uuid)]

    # Update date fields (created, modified, etc.)
    if "fields" in item:
        for date_field in ["created", "modified"]:
            if date_field in item["fields"] and item["fields"][date_field]:
                # Parse the original date
                original_date = datetime.datetime.fromisoformat(item["fields"][date_field].replace("Z", "+00:00"))

                # Calculate the new date by applying the time shift
                if original_timespan > 0 and oldest_date is not None:
                    # Preserve relative spacing within the timeline
                    progress = (original_date - oldest_date).total_seconds() / original_timespan
                    new_date = (now - datetime.timedelta(days=days_to_shift)) + datetime.timedelta(
                        seconds=progress * original_timespan
                    )
                else:
                    # Simple shift if we couldn't determine timespan
                    new_date = original_date + time_shift

                # Update the field with the new date
                item["fields"][date_field] = new_date.isoformat()

        # Update metadata dates/times if present
        if "metadata" in item["fields"] and isinstance(item["fields"]["metadata"], dict):
            for meta_key, meta_value in item["fields"]["metadata"].items():
                if isinstance(meta_value, str) and "time" in meta_key.lower():
                    try:
                        # Try to parse as a date string
                        original_date = datetime.datetime.fromisoformat(meta_value.replace("Z", "+00:00"))
                        new_date = original_date + time_shift
                        item["fields"]["metadata"][meta_key] = new_date.isoformat()
                    except (ValueError, TypeError):
                        # Not a valid date string, skip it
                        pass

        # Update related fields that use UUIDs
        for field_name, field_value in item["fields"].items():
            if isinstance(field_value, list):
                # This might be a many-to-many or foreign key field with UUIDs
                new_values = []
                for val in field_value:
                    if isinstance(val, str) and val in uuid_map:
                        new_values.append(uuid_map[val])
                    else:
                        new_values.append(val)
                item["fields"][field_name] = new_values

    return item


def load_demo_data_for_user(
    user: User, demo_data_path: str = DEMO_DATA_PATH, user_uuid: str = "725efb9d-8c32-42a1-9e17-dc87ef6843ac"
) -> bool:
    """
    Load demo data for a specific user.
    Args:
        user: The user to load demo data for
        demo_data_path: Path to the demo data file
        user_uuid: UUID in the demo data file to replace with the user's UUID
    Returns:
        bool: True if data was loaded successfully, False otherwise
    """
    if not os.path.exists(demo_data_path):
        logging.error("Demo data file not found at %s", demo_data_path)
        return False

    try:
        # Load and prepare the data
        with open(demo_data_path, "r", encoding="utf-8") as f:
            data = f.read()

        data = data.replace(user_uuid, str(user.uuid))
        json_data = json.loads(data)

        # For each object, generate a new UUID to avoid collisions
        # This approach preserves relationships between related objects
        uuid_map: dict[str, str] = {}

        # First pass - identify date range in original data
        oldest_date, newest_date = identify_date_range(json_data)

        # Calculate time shift
        time_shift, original_timespan = calculate_time_shift(oldest_date, newest_date)

        # Second pass - update data
        updated_items = []
        for item in json_data:
            updated_item = update_item_data(
                item=item,
                user=user,
                uuid_map=uuid_map,
                date_references=(oldest_date, newest_date),
                time_shift=time_shift,
                original_timespan=original_timespan,
            )
            if updated_item:  # Only add if not skipped
                updated_items.append(updated_item)

        # Convert back to JSON
        updated_data = json.dumps(updated_items)

        # Use the existing load function to import the data
        with transaction.atomic():
            load_dumped_data(updated_data, rewrite_uuids=False, num_tries=1)

        return True
    except Exception as e:
        logging.exception("Error loading demo data for user %s: %s", user, e)
        return False
