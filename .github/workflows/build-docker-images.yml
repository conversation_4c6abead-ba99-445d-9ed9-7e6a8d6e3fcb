name: Build and push Docker image to ECR
run-name: Build and push Docker image to ECR -- ${{ inputs.repository || 'zeplyn-backend' }}
on:
  # Create an image for every commit to main, and every release tag.
  push:
    branches:
      - main
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+'
  # Allow developers to create builds for specific development branches.
  workflow_dispatch:
  # Allow the frontend-app repo to reuse this workflow.
  workflow_call:
    inputs:
      repository:
        required: true
        type: string

env:
  REPOSITORY: ${{ inputs.repository || 'zeplyn-backend' }}

jobs:
  push-image:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_CONTAINER_BUILDER_IAM_ROLE }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      # Look for an existing image.
      - name: Check if Tag already exists
        id: checktag
        uses: tyriis/docker-image-tag-exists@v2.1.0
        with:
          registry: ${{ secrets.ECR_REGISTRY }}
          repository: ${{ env.REPOSITORY }}
          tag: ${{ github.sha }}
      # If the image already exists, retag it.
      - name: Retag existing image
        if: steps.checktag.outputs.tag == 'found'
        uses: abronin/ecr-retag-action@v1
        with:
          repository: ${{ env.REPOSITORY }}
          tag: ${{ github.sha }}
          new-tags: ${{ github.ref_name }}
      # If the image does not already exist, build a new one.
      - name: Set up Docker Buildx
        if: steps.checktag.outputs.tag == 'not found'
        uses: docker/setup-buildx-action@v3
      - name: Get date to use in the Datadog app version.
        run: |
          echo "DATE=$(date "+%Y%m%d")" >> ${GITHUB_ENV}
      - name: Get date to use in the Datadog app version.
        run: |
          echo "TIME=$(date "+%H%M%S")" >> ${GITHUB_ENV}
      # Checking out the repo isn't required:
      # https://github.com/docker/build-push-action?tab=readme-ov-file#git-context
      - name: Build and push image
        uses: docker/build-push-action@v4
        if: steps.checktag.outputs.tag == 'not found'
        with:
          platforms: linux/arm64
          file: Dockerfile
          push: true
          # The .git directory is required by the tool that uploads sourcemap information.
          build-args: |
            DD_GIT_REPOSITORY_URL=github.com/${{ github.repository }}
            DD_GIT_COMMIT_SHA=${{ github.sha }}
            BUILD_VERSION=v${{ env.DATE }}.${{ env.TIME }}.${{ github.sha }}
            BUILDKIT_CONTEXT_KEEP_GIT_DIR=1
          secrets: |
            "DATADOG_API_KEY=${{ secrets.DATADOG_API_KEY_FOR_SOURCEMAPS_UPLOAD }}"
          tags: |
            ${{ secrets.ECR_REGISTRY }}/${{ env.REPOSITORY }}:${{ github.sha }}
            ${{ secrets.ECR_REGISTRY }}/${{ env.REPOSITORY }}:${{ github.ref_name }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
