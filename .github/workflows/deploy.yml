name: Deploy on EC2
run-name: Deploy on EC2 -- ${{ inputs.environment }}
on:
  workflow_dispatch:
    inputs:
      environment:
        required: true
        type: environment
      backend-tag:
        required: false
        description: >
          Backend image tag: A Docker image tag to use for the backend binary.
          Leave this blank to use the Git ref associated with the workflow.
          This should only be used for pre-release releases to non-production
          environments!
        type: string
      frontend-tag:
        required: false
        description: >
          Frontend image tag: A Docker image tag to use for the web frontend
          binary. Leave this blank to use the Git ref associated with the
          workflow. This should only be used for pre-release releases to
          non-production environments!
        type: string

env:
  BACKEND_TAG: ${{ inputs.backend-tag || github.ref_name }}
  FRONTEND_TAG: ${{ inputs.frontend-tag || github.ref_name }}

concurrency:
  group: ${{ github.workflow }}-${{ inputs.environment }}
  cancel-in-progress: false

jobs:
  deploy:
    environment:
      name: ${{ inputs.environment }}
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      # Configure the runner to be able to SSH into the EC2 instance.
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_DEPLOY_IAM_ROLE }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Send SSH public key to EC2 instance connect endpoint
        run: |
          aws ec2-instance-connect send-ssh-public-key \
            --instance-id "${{ vars.EC2_INSTANCE_ID }}" \
            --instance-os-user ec2-user \
            --ssh-public-key "${{ secrets.AWS_SSH_PUBLIC_KEY }}"
      - name: Set up hosts file with EC2 host
        run: |
          mkdir -p ~/.ssh && \
          echo "${{ secrets.EC2_KNOWN_HOSTS }}" > ~/.ssh/known_hosts
      - name: Add private key to local SSH agent
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.AWS_SSH_PRIVATE_KEY }}
      - name: Create the file with the image tags
        run: |
          cat <<EOF > tags.compose.env
          BACKEND_TAG=${{ env.BACKEND_TAG }}
          FRONTEND_TAG=${{ env.FRONTEND_TAG }}
          EOF
      - name: Push the tag file
        run: |
          scp \
            -o "ProxyCommand aws ec2-instance-connect open-tunnel --instance-id %h" \
            "tags.compose.env" \
            ec2-user@${{ vars.EC2_INSTANCE_ID }}:deploy/
      - name: Create the shell environment file for running docker compose
        env:
          LOCAL_DATABASE: ${{ vars.USE_LOCAL_DATABASE && 'local_database' || '' }}
        run: |
          cat <<EOF > compose_setup
          export COMPOSE_PROJECT_NAME=zeplyn
          export COMPOSE_PROFILES=datadog,$LOCAL_DATABASE
          export COMPOSE_ENV_FILES=base.compose.env,tags.compose.env
          EOF
      - name: Push the configuration files from the repository
        run: |
          scp \
            -o "ProxyCommand aws ec2-instance-connect open-tunnel --instance-id %h" \
            -r \
            "compose_setup" \
            "docker/docker-compose.yml" \
            "docker/nginx" \
            ec2-user@${{ vars.EC2_INSTANCE_ID }}:deploy/
      - uses: chrnorm/deployment-action@v2
        id: fe-deployment
        name: Create deployment in frontend-app repo
        with:
          token: ${{ secrets.FE_DEPLOYMENT_PAT }}
          repo: frontend-app
          ref: ${{ env.FRONTEND_TAG }}
          environment: ${{ inputs.environment }}
      - name: Deploy the updated configuration and images
        run: |
          ssh \
          -o ProxyCommand="aws ec2-instance-connect open-tunnel --instance-id %h" \
          ec2-user@${{ vars.EC2_INSTANCE_ID }} <<EOF
          cd deploy &&
          source compose_setup &&
          docker-compose run --rm certbot renew &&
          docker-compose pull && docker-compose up -d &&
          docker system prune -a -f
          EOF
      - name: Update frontend-app deployment status (success)
        if: success()
        uses: chrnorm/deployment-status@v2
        with:
          token: ${{ secrets.FE_DEPLOYMENT_PAT }}
          repo: frontend-app
          deployment-id: ${{ steps.fe-deployment.outputs.deployment_id }}
          state: 'success'
      - name: Update frontend-app deployment status (failure)
        if: failure()
        uses: chrnorm/deployment-status@v2
        with:
          token: ${{ secrets.FE_DEPLOYMENT_PAT }}
          repo: frontend-app
          deployment-id: ${{ steps.fe-deployment.outputs.deployment_id }}
          state: 'failure'
      - name: Generate summary
        run: |
          cat <<EOF >> $GITHUB_STEP_SUMMARY
          ## 🚀 Deployment info
          ### Environment: ${{ inputs.environment }}
          - Frontend tag: $FRONTEND_TAG
          - Backend tag: $BACKEND_TAG
          EOF
