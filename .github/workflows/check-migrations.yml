name: Check Django migration status

on:
  pull_request:
    types: [opened, synchronize]
  push:
    branches:
      - main

jobs:
  # Lints migrations in the current PR to indicate whether they are backwards compatible.
  lint-migrations:
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    steps:
      # Check out main, so we can get a migration base.
      - name: Check out main
        uses: actions/checkout@v4
        with:
          ref: "main"
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - uses: astral-sh/setup-uv@05273c154d09957eb9a2377d9c988fbda431d1c5  # v6.4.0
      - name: Install Dependencies
        run: uv pip install --system -r requirements/local.txt
      - name: Install 1Password CLI
        uses: 1password/install-cli-action@v1
      - name: Configure the 1Password CLI credentials
        uses: 1password/load-secrets-action/configure@v2
        with:
          service-account-token: ${{ secrets.ONEPASSWORD_SECURITY_ACCOUNT_TOKEN }}
      - name: Fetch secrets file
        run: |
          op document get 32zcmblturf42ilfs7wqfvc4zi -o .env.secrets
      - name: Set up Docker image cache
        uses: AndreKurait/docker-cache@0.6.0
        with:
          key: docker-${{ runner.os }}-${{ hashFiles('docker/docker-compose.development.yml') }}
      - name: Start the database
        run: |
          source .env.vars
          source .env.secrets
          docker compose -f docker/docker-compose.development.yml up db -d
      # Do the migrations, so the database is up-to-date with main.
      - name: Do migrations
        run: |
          source .env.vars
          source .env.secrets
          ./manage.py migrate
      # Check out the PR branch.
      - name: Check out the PR branch
        uses: actions/checkout@v4
      # Install dependencies on the branch, in case a new dependency
      # was added.
      - name: Install dependencies (from branch)
        run: uv pip install --system -r requirements/local.txt
      - name: Fetch secrets file
        run: |
          op document get 32zcmblturf42ilfs7wqfvc4zi -o .env.secrets
      # Check that the migrations are backwards compatible.
      - name: Lint the migrations
        run: |
          source .env.vars
          source .env.secrets
          ./manage.py lintmigrations
      - name: Stop the database
        if: always()
        run: docker compose -f docker/docker-compose.development.yml down

   # Ensures that migrations are valid and that the state of the models in code matches the
   # migration state (i.e., Django will not auto-create any new migrations), starting with
   # "main" as the base.
  check-migrations:
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    steps:
      # Check out main, so we can get a migration base.
      - name: Check out main
        uses: actions/checkout@v4
        with:
          ref: "main"
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - uses: astral-sh/setup-uv@05273c154d09957eb9a2377d9c988fbda431d1c5  # v6.4.0
      - name: Install Dependencies
        run: uv pip install --system -r requirements/local.txt
      - name: Install 1Password CLI
        uses: 1password/install-cli-action@v1
      - name: Configure the 1Password CLI credentials
        uses: 1password/load-secrets-action/configure@v2
        with:
          service-account-token: ${{ secrets.ONEPASSWORD_SECURITY_ACCOUNT_TOKEN }}
      - name: Fetch secrets file
        run: |
          op document get 32zcmblturf42ilfs7wqfvc4zi -o .env.secrets
      - name: Set up Docker image cache
        uses: AndreKurait/docker-cache@0.6.0
        with:
          key: docker-${{ runner.os }}-${{ hashFiles('docker/docker-compose.development.yml') }}
      - name: Start the database
        run: |
          source .env.vars
          source .env.secrets
          docker compose -f docker/docker-compose.development.yml up db -d
      # Do the migrations, so the database is up-to-date with main.
      - name: Do migrations
        run: |
          source .env.vars
          source .env.secrets
          ./manage.py migrate
      # Check out the PR branch.
      - name: Check out the PR branch
        uses: actions/checkout@v4
      # Install dependencies on the branch, in case a new dependency
      # was added.
      - name: Install dependencies (from branch)
        run: uv pip install --system -r requirements/local.txt
      - name: Fetch secrets file
        run: |
          op document get 32zcmblturf42ilfs7wqfvc4zi -o .env.secrets
      # First do a dry run, which will print out information; then,
      # run the check to get the failure error code to cause the
      # action to fail if there are any new migrations.
      - name: Make new migrations
        run: |
          source .env.vars
          source .env.secrets
          ./manage.py makemigrations --dry-run
          ./manage.py makemigrations --check
      # Migrate the database to ensure that the migrations are
      # valid (and log verbosely to help with debugging).
      - name: Migrate the database
        run: |
          source .env.vars
          source .env.secrets
          ./manage.py migrate -v 3
      - name: Stop the database
        if: always()
        run: docker compose -f docker/docker-compose.development.yml down

   # Ensures that migrations are valid and that the state of the models in code matches the
   # migration state (i.e., Django will not auto-create any new migrations), starting with
   # the SHA of the last production deployment as the base.
  check-migrations-against-prod-release:
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    permissions:
      deployments: read
      contents: read
    steps:
      - name: Get latest production deployment SHA
        uses: octokit/request-action@05a2312de9f8207044c4c9e41fe19703986acc13
        id: get_latest_deployment
        with:
          route: GET /repos/${{github.repository}}/deployments?environment=prod
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Check out the SHA of the latest production deployment
        uses: actions/checkout@v4
        with:
          ref: "${{ fromJson(steps.get_latest_deployment.outputs.data)[0].sha }}"
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
      - uses: astral-sh/setup-uv@05273c154d09957eb9a2377d9c988fbda431d1c5  # v6.4.0
      - name: Install Dependencies
        run: uv pip install --system -r requirements/local.txt
      - name: Install 1Password CLI
        uses: 1password/install-cli-action@v1
      - name: Configure the 1Password CLI credentials
        uses: 1password/load-secrets-action/configure@v2
        with:
          service-account-token: ${{ secrets.ONEPASSWORD_SECURITY_ACCOUNT_TOKEN }}
      - name: Fetch secrets file
        run: |
          op document get 32zcmblturf42ilfs7wqfvc4zi -o .env.secrets
      - name: Set up Docker image cache
        uses: AndreKurait/docker-cache@0.6.0
        with:
          key: docker-${{ runner.os }}-${{ hashFiles('docker/docker-compose.development.yml') }}
      - name: Start the database
        run: |
          source .env.vars
          source .env.secrets
          docker compose -f docker/docker-compose.development.yml up db -d
      # Do the migrations, so the database is up-to-date with main.
      - name: Do migrations
        run: |
          source .env.vars
          source .env.secrets
          ./manage.py migrate
      # Check out the PR branch.
      - name: Check out the PR branch
        uses: actions/checkout@v4
      # Install dependencies on the branch, in case a new dependency
      # was added.
      - name: Install dependencies (from branch)
        run: uv pip install --system -r requirements/local.txt
      - name: Fetch secrets file
        run: |
          op document get 32zcmblturf42ilfs7wqfvc4zi -o .env.secrets
      # First do a dry run, which will print out information; then,
      # run the check to get the failure error code to cause the
      # action to fail if there are any new migrations.
      - name: Make new migrations
        run: |
          source .env.vars
          source .env.secrets
          ./manage.py makemigrations --dry-run
          ./manage.py makemigrations --check
      # Migrate the database to ensure that the migrations are
      # valid (and log verbosely to help with debugging).
      - name: Migrate the database
        run: |
          source .env.vars
          source .env.secrets
          ./manage.py migrate -v 3
      - name: Stop the database
        if: always()
        run: docker compose -f docker/docker-compose.development.yml down
