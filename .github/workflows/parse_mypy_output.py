"""Parses the output of a mypy run and reports errors in a format that <PERSON><PERSON><PERSON> can use to render errors inline."""
# Local modifications made by <PERSON>.
#
# Original source license:
# Copyright (c) 2023 <PERSON><PERSON>
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.

import sys

from pydantic import BaseModel, ValidationError


# A representation of the JSON mypy error format.
class MypyError(BaseModel):
    file: str
    line: int
    column: int
    message: str
    hint: str | None
    code: str | None
    severity: str | None


# Prints an error to stdout, in a format that Github can use to render errors inline.
def report_github_error(error: MypyError) -> None:
    command = "error" if error.severity == "error" else "notice"
    title = f"Mypy ({error.code})" if error.code is not None else "Mypy"
    message = f"{error.message}"
    if error.hint:
        message += f"%0A%0A{error.hint}"

    print(
        f"::{command} "
        f"file={error.file},"
        f"line={error.line},"
        f"col={error.column if error.column > 0 else 0},"
        f"title={title}"
        f"::{error.message}"
    )


# Parses errors from the outputs of a mypy run.
def parse_mypy_errors(file_path: str) -> None:
    with open(file_path, "r", encoding="utf-8") as file:
        data = file.read()
    for line in data.split("\n"):
        if not line.strip():
            continue
        try:
            if error := MypyError.model_validate_json(line):
                report_github_error(error)
        except ValidationError:
            print(f"::error::Failed to parse Mypy output line: {line}")


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("You must specify a file path to file containing mypy output!")
        sys.exit(1)
    parse_mypy_errors(sys.argv[1])
