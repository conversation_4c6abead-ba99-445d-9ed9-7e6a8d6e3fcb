import json
from typing import Any, <PERSON>

from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


def load_aaron_notes(path: str, email: str) -> dict[str, Any]:
    """
    <PERSON>ad Aron's meeting notes into the database.

    Args:
        path: Path to the JSON file containing notes
        email: Email of the user to assign as note owner
    """
    # Get the user instance
    try:
        user = User.objects.get(email=email)
    except User.DoesNotExist:
        raise ValueError(f"User with email {email} does not exist")

    if not user.organization:
        raise ValueError(f"User {email} does not have an associated organization")

    # Create client info

    # Create client record
    client = Client.objects.create(
        name="Aaron & Ariel Price Household",
        organization=user.organization,
    )

    client_info = {"name": client.name, "uuid": str(client.uuid)}

    print(f"Created client: {client.name} ({client.uuid})")

    # Track results
    created = 0
    updated = 0
    errors: list[str] = []

    # Read the JSON file
    with open(path, "r") as file:
        notes_data = json.load(file)

    for note_data in notes_data:
        try:
            # Clean up fields
            fields = note_data["fields"]
            fields_to_remove = [
                "_state",
                "id",
                "uuid",
                "note_owner",
                "history",
                "salesforce_case_id",
                "meeting_type_id",
            ]
            for field in fields_to_remove:
                fields.pop(field, None)
            print(fields)
            # Create note
            note = Note.objects.create(**fields)

            note.client = client_info
            note.note_owner = user
            note.save()

            created += 1

        except Exception as e:
            errors.append(f"Error processing note: {str(e)}")

    results: dict[str, Union[int, list[str]]] = {"created": created, "updated": updated, "errors": errors}

    # Print results
    print("Successfully processed notes:")
    print(f"Created: {results['created']}")
    print(f"Updated: {results['updated']}")

    return results
