from unittest.mock import MagicMock, patch

import pytest
from django.db import DatabaseError
from django.test import TestCase
from pydantic import ValidationError

from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.ml_agents.models.agent_config import (
    AgentConfig,
    AgentInput,
    AgentOutputEnumeratedListSubset,
    AgentOutputReviewEntry,
    AgentOutputStructuredText,
    ListMetadata,
    ValidPromptTemplate,
    expect_list_items,
    expect_structured_headers,
    get_agent_config_and_run_agent,
)


class AgentConfigModelTests(TestCase):
    def setUp(self) -> None:
        self.organization = Organization.objects.create(name="test")

    def test_valid_prompt_validator_fails_schema_mismatch_extra(self) -> None:
        with pytest.raises(ValidationError):
            _ = ValidPromptTemplate(
                text="test", text_replacement_examples=AgentInput(transcript="test"), content_schema=[]
            )

    def test_valid_prompt_validator_fails_schema_mismatch_missing(self) -> None:
        with pytest.raises(ValidationError):
            _ = ValidPromptTemplate(
                text="test",
                text_replacement_examples=AgentInput(transcript="test"),
                content_schema=["transcript", "agenda"],
            )

    def test_valid_prompt_generation_schema_succeeds(self) -> None:
        template = ValidPromptTemplate(
            text="test", text_replacement_examples=AgentInput(transcript="test"), content_schema=["transcript"]
        )
        result = template.get_formatted_prompt(AgentInput(transcript="test2"))
        assert "test2" in result

    def test_valid_prompt_generation_schema_succeeds_sample_inputs(self) -> None:
        template = ValidPromptTemplate(
            text="test", text_replacement_examples=AgentInput(transcript="test"), content_schema=["transcript"]
        )
        template.get_formatted_prompt_sample_inputs()  # does not throw
        assert template

    def test_valid_prompt_generation_fails_schema_mismatch_extra(self) -> None:
        template = ValidPromptTemplate(
            text="test", text_replacement_examples=AgentInput(transcript="test"), content_schema=["transcript"]
        )

        with pytest.raises(ValueError):
            template.get_formatted_prompt(AgentInput(transcript="test", agenda_template="test"))

    def test_valid_prompt_generation_fails_schema_mismatch_missing(self) -> None:
        template = ValidPromptTemplate(
            text="test", text_replacement_examples=AgentInput(transcript="test"), content_schema=["transcript"]
        )

        with pytest.raises(ValueError):
            template.get_formatted_prompt(AgentInput(agenda_template="test"))

    def test_structured_text_output_has_only_valid_headers_no_required_headers(self) -> None:
        with expect_structured_headers([]):
            entries = AgentOutputReviewEntry(id="A test header", kind="header", topic="Topic 1")
            output = AgentOutputStructuredText(review_entries=[entries])
            assert output

    def test_structured_text_output_has_only_valid_headers_required_headers_mismatch(self) -> None:
        with expect_structured_headers(["topic 1"]):
            entries = AgentOutputReviewEntry(id="A test header", kind="header", topic="Topic 2")
            with pytest.raises(ValidationError) as ve:
                _ = AgentOutputStructuredText(review_entries=[entries])
            assert "Expected headers ['topic 1'] but got headers ['topic 2']" in str(ve.value)

    def test_structured_text_output_has_only_valid_headers_required_headers_match(self) -> None:
        with expect_structured_headers(["topic 1"]):
            entries = AgentOutputReviewEntry(id="A test header", kind="header", topic="Topic 1")
            output = AgentOutputStructuredText(review_entries=[entries])
            assert output

    def test_list_subset_output_has_only_valid_result(self) -> None:
        with expect_list_items(ListMetadata(permitted_items=["item"])):
            output = AgentOutputEnumeratedListSubset(items=["item"])
        assert output

    def test_list_subset_output_has_multiple_valid_result(self) -> None:
        with expect_list_items(ListMetadata(permitted_items=["item", "Item2"])):
            output = AgentOutputEnumeratedListSubset(items=["Item2", "item"])
        assert output

    def test_list_subset_output_fails_not_subset(self) -> None:
        with expect_list_items(ListMetadata(permitted_items=["item"])):
            with pytest.raises(ValidationError) as ve:
                _ = AgentOutputEnumeratedListSubset(items=["item2", "item"])

    def test_list_subset_output_fails_too_many(self) -> None:
        with expect_list_items(ListMetadata(permitted_items=["item", "2", "3"], max_items=2)):
            with pytest.raises(ValidationError) as ve:
                _ = AgentOutputEnumeratedListSubset(items=["item", "2", "3"])

    def test_list_subset_succeeds_no_spec(self) -> None:
        output = AgentOutputEnumeratedListSubset(items=["item"])
        assert output

    def test_two_null_org_configs_can_exist(self) -> None:
        first = AgentConfig.objects.create(
            name="agenda_followup_generator",
            user_prompt="This is a user prompt",
            version_notes="First version",
            user_prompt_metadata_example={"agenda_template": "test", "transcript": "test"},
            user_prompt_metadata_schema=["agenda_template", "transcript"],
            output_validation_schema="structured_text",
            llm_model="gpt-4o-2024-08-06",
        )
        second = AgentConfig.objects.create(
            name="agenda_followup_generator",
            user_prompt="This is a user prompt",
            version_notes="First version",
            user_prompt_metadata_example={"agenda_template": "test", "transcript": "test"},
            user_prompt_metadata_schema=["agenda_template", "transcript"],
            output_validation_schema="structured_text",
            llm_model="gpt-4o-2024-08-06",
        )
        assert first
        assert second

    def test_two_notnull_org_configs_cannot_exist(self) -> None:
        _ = AgentConfig.objects.create(
            name="agenda_followup_generator",
            user_prompt="This is a user prompt",
            version_notes="First version",
            user_prompt_metadata_example={"agenda_template": "test", "transcript": "test"},
            user_prompt_metadata_schema=["agenda_template", "transcript"],
            output_validation_schema="structured_text",
            llm_model="gpt-4o-2024-08-06",
            owner=self.organization,
        )
        with pytest.raises(DatabaseError) as exc:
            _ = AgentConfig.objects.create(
                name="agenda_followup_generator",
                user_prompt="This is a user prompt",
                version_notes="First version",
                user_prompt_metadata_example={"agenda_template": "test", "transcript": "test"},
                user_prompt_metadata_schema=["agenda_template", "transcript"],
                output_validation_schema="structured_text",
                llm_model="gpt-4o-2024-08-06",
                owner=self.organization,
            )

    def test_valid_agent_config_user_only(self) -> None:
        agent_config = AgentConfig.objects.create(
            name="agenda_followup_generator",
            user_prompt="This is a user prompt",
            version_notes="First version",
            user_prompt_metadata_example={"agenda_template": "test", "transcript": "test"},
            user_prompt_metadata_schema=["agenda_template", "transcript"],
            output_validation_schema="structured_text",
            llm_model="gpt-4o-2024-08-06",
        )
        validated_config = agent_config.to_valid_agent_config()
        base_agent = validated_config.to_agent()
        assert base_agent

    def test_valid_agent_config_user_and_system(self) -> None:
        agent_config = AgentConfig.objects.create(
            name="agenda_followup_generator",
            user_prompt="This is a user prompt",
            version_notes="First version",
            user_prompt_metadata_example={"agenda_template": "test", "transcript": "test"},
            user_prompt_metadata_schema=["agenda_template", "transcript"],
            system_prompt="This is a system prompt",
            system_prompt_metadata={"transcript": "Test"},
            output_validation_schema="unstructured_text",
            llm_model="gpt-4o-2024-08-06",
        )
        validated_config = agent_config.to_valid_agent_config()
        base_agent = validated_config.to_agent()
        assert base_agent

    def test_valid_agent_config_user_and_system_no_system_metadata(self) -> None:
        agent_config = AgentConfig.objects.create(
            name="agenda_followup_generator",
            user_prompt="This is a user prompt",
            version_notes="First version",
            user_prompt_metadata_example={"agenda_template": "test", "transcript": "test"},
            user_prompt_metadata_schema=["agenda_template", "transcript"],
            system_prompt="This is a system prompt",
            system_prompt_metadata=None,
            output_validation_schema="structured_tabular",
            llm_model="gpt-4o-2024-08-06",
        )
        validated_config = agent_config.to_valid_agent_config()
        base_agent = validated_config.to_agent()
        assert base_agent
        assert agent_config.name in str(agent_config)

    def test_invalid_agent_config(self) -> None:
        agent_config = AgentConfig.objects.create(
            name="test",
            owner=self.organization,
            user_prompt="Is there financial info in {placeholder}",
            user_prompt_metadata_example={"placeholder": "test content"},
            system_prompt="You're an organization named {org_name}",
            system_prompt_metadata={"org_name": "Test Org"},
            output_validation_schema="not a valid schema",
            llm_model="not a model",
        )
        with pytest.raises(Exception) as e:
            agent_config.to_valid_agent_config()

    def test_invalid_agent_config_prompt(self) -> None:
        agent_config = AgentConfig.objects.create(
            name="test",
            owner=self.organization,
            user_prompt="Is there financial info in {placeholder}",
            user_prompt_metadata_example={"placeholder": None},
            system_prompt="You're an organization named {org_name}",
            system_prompt_metadata={"org_name": "Test Org"},
            output_validation_schema="unstructured_text",
            llm_model="gpt-4o-2024-08-06",
        )
        with pytest.raises(Exception) as e:
            agent_config.to_valid_agent_config()

    def test_get_agent_config_and_run_agent_no_agent(self) -> None:
        name = "test_agent"
        org = Organization.objects.create(name="test org")
        _ = [a.delete() for a in AgentConfig.objects.filter(name=name, owner=org)]

        assert not get_agent_config_and_run_agent(agent_owner=org, agent_name=name, agent_input=AgentInput())

    def test_get_agent_config_and_run_agent_invalid_agent(self) -> None:
        name = "test_agent"
        org = Organization.objects.create(name="test org")
        _ = [a.delete() for a in AgentConfig.objects.filter(name=name, owner=org)]

        agent_config = AgentConfig.objects.update_or_create(
            owner=org,
            name=name,
            user_prompt="",  # Empty is invalid
            version_notes="First version",
            user_prompt_metadata_example={},
            user_prompt_metadata_schema=[],
            system_prompt=None,
            system_prompt_metadata=None,
            output_validation_schema="list_subset",
            llm_model="gpt-4o-2024-08-06",
        )

        assert not get_agent_config_and_run_agent(agent_owner=org, agent_name=name, agent_input=AgentInput())

    @patch("pydantic_ai.Agent.run_sync")
    def test_get_agent_config_and_run_agent_invalid_output(self, mock_agent: MagicMock) -> None:
        name = "test_agent"
        org = Organization.objects.create(name="test org")
        _ = [a.delete() for a in AgentConfig.objects.filter(name=name, owner=org)]

        agent_config = AgentConfig.objects.update_or_create(
            owner=org,
            name=name,
            user_prompt="Test",
            version_notes="First version",
            user_prompt_metadata_example={},
            user_prompt_metadata_schema=[],
            system_prompt=None,
            system_prompt_metadata=None,
            output_validation_schema="list_subset",
            llm_model="gpt-4o-2024-08-06",
        )

        mock_agent.side_effect = [Exception("Issue with agent")]

        assert not get_agent_config_and_run_agent(agent_owner=org, agent_name=name, agent_input=AgentInput())

    @patch("pydantic_ai.Agent.run_sync")
    def test_get_agent_config_and_run_agent_success(self, mock_agent: MagicMock) -> None:
        name = "test_agent"
        org = Organization.objects.create(name="test org")
        _ = [a.delete() for a in AgentConfig.objects.filter(name=name, owner=org)]

        agent_config = AgentConfig.objects.update_or_create(
            owner=org,
            name=name,
            user_prompt="Test",
            version_notes="First version",
            user_prompt_metadata_example={},
            user_prompt_metadata_schema=[],
            system_prompt=None,
            system_prompt_metadata=None,
            output_validation_schema="list_subset",
            llm_model="gpt-4o-2024-08-06",
        )

        expected_out = AgentOutputEnumeratedListSubset(items=[])
        mock_agent.return_value.output = expected_out

        assert (
            get_agent_config_and_run_agent(agent_owner=org, agent_name=name, agent_input=AgentInput()) == expected_out
        )
