import logging
from contextlib import contextmanager
from contextvars import <PERSON><PERSON>V<PERSON>
from enum import Str<PERSON>num
from typing import Annotated, Any, Generator, Literal, Self

from django.conf import settings
from django.db import models
from pydantic import BaseModel, ConfigDict, Field, StringConstraints, ValidationError, ValidationInfo, model_validator
from pydantic_ai import Agent
from pydantic_ai.models import Model
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from pydantic_ai.settings import ModelSettings
from simple_history.models import HistoricalRecords

from deepinsights.core.behaviours import StatusMixin, UUIDMixin
from deepinsights.meetingsapp.models.organization import Organization

NonEmptyString = Annotated[str, StringConstraints(min_length=1)]

### PROMPT METADATA INPUT SCHEMAS IN PYDANTIC
# Dependencies we need to pass in to get successful agent runs out
# These will be mostly wrapped strings
# An AgentConfig should know which dependencies are required for it to successfully run


class AgentInput(BaseModel):
    """Common inputs that are available at meeting end are needed to construct the agent and its prompt"""

    transcript: NonEmptyString | None = None
    agenda_template: NonEmptyString | None = None
    note_context: NonEmptyString | None = None


### PROMPT OUTPUT SCHEMAS IN PYDANTIC
# We have three schema formats, previously stored in JSON blobs in the database, and they don't change often
# In order to use them as structured Pydantic types, they are defined here moving forward
# These are directly referenced by strings in the Django database, and used to strongly type an agent


# Format: Unstructured Text
class AgentOutputUnstructuredText(BaseModel):
    model_config = ConfigDict(extra="forbid")
    format: Literal["markdown"]
    content: str


# Format: Enumerated List
# If we have a set of elements we need to match, instantiate it ahead of time
class ListMetadata(BaseModel):
    max_items: int | None = None
    permitted_items: list[str] = []


_permitted_list_metadata: ContextVar[ListMetadata] = ContextVar("_permitted_list_metadata", default=ListMetadata())


@contextmanager
def expect_list_items(value: ListMetadata) -> Generator[None, None, None]:
    token = _permitted_list_metadata.set(value)
    try:
        yield
    finally:
        _permitted_list_metadata.reset(token)


class AgentOutputEnumeratedListSubset(BaseModel):
    items: list[str]

    def __init__(self, /, **data: Any) -> None:
        self.__pydantic_validator__.validate_python(
            data,
            self_instance=self,
            context=_permitted_list_metadata.get(),
        )

    @model_validator(mode="after")
    def check_list_content_match(self, info: ValidationInfo) -> Self:
        if not isinstance(info.context, ListMetadata):
            return self

        if info.context.max_items and len(self.items) > info.context.max_items:
            raise ValueError(f"Expected {info.context.max_items} items but got {len(self.items)}")

        required_items = set([entry.casefold() for entry in info.context.permitted_items])
        actual_items = set([entry.casefold() for entry in self.items])
        items_subset = actual_items.difference(required_items)
        if required_items and items_subset:  # if there's a set to check and items_subset not falsy
            raise ValueError(f"Expected {actual_items} to be a subset of {required_items}")
        return self


# Subentities for Structured Text format
class AgentOutputEvidence(BaseModel):
    quote: str
    timestamp: str


class AgentOutputReviewEntry(BaseModel):
    id: str
    kind: Literal["header", "toggle", "select", "multiselect", "bullet"]
    topic: str
    options: list[str] | None = None
    evidence: list[AgentOutputEvidence] | None = None
    selected: str | None = None
    discussed: bool | None = None
    subentries: list[
        "AgentOutputReviewEntry"
    ] | None = None  # https://docs.pydantic.dev/latest/concepts/forward_annotations/#cyclic-references
    explanation: str | None = None
    multi_selected: list[str] | None = None


# For structured text, if we have a set of headers we need to match, instantiate it ahead of time
_permitted_structured_headers: ContextVar[list[str]] = ContextVar("_permitted_structured_headers", default=[])


@contextmanager
def expect_structured_headers(value: list[str]) -> Generator[None, None, None]:
    token = _permitted_structured_headers.set(value)
    try:
        yield
    finally:
        _permitted_structured_headers.reset(token)


# Format: Structured Text
class AgentOutputStructuredText(BaseModel):
    model_config = ConfigDict(extra="forbid")
    review_entries: list[AgentOutputReviewEntry]

    def __init__(self, /, **data: Any) -> None:
        self.__pydantic_validator__.validate_python(
            data,
            self_instance=self,
            context=_permitted_structured_headers.get(),
        )

    @model_validator(mode="after")
    def check_header_content_match(self, info: ValidationInfo) -> Self:
        if not isinstance(info.context, list):
            return self

        required_headers = [entry.casefold() for entry in info.context]
        actual_headers = [entry.topic.casefold() for entry in self.review_entries]
        headers_match = actual_headers == required_headers
        if required_headers and not headers_match:
            raise ValueError(f"Expected headers {required_headers} but got headers {actual_headers}")
        return self


# Subentities for Tabular Numeric format
class AgentOutputDataFormat(BaseModel):
    """Data formatting information, for several numeric types of data"""

    value_type: Literal["number", "percentage", "currency", "date"]
    date_format: str | None
    show_symbol: bool | None
    decimal_places: int | None


class AgentOutputData(BaseModel):
    values: dict[str, Any]
    format: AgentOutputDataFormat
    evidence: list[AgentOutputEvidence] | None


class AgentOutputTableColumn(BaseModel):
    id: str
    caption: str
    data_type: str


class AgentOutputTableDefinition(BaseModel):
    columns: list[AgentOutputTableColumn] = Field(min_length=3)
    title: str | None
    description: str | None


# Format: Tabular Numeric
class AgentOutputTabularNumeric(BaseModel):
    model_config = ConfigDict(extra="forbid")
    table_definition: AgentOutputTableDefinition
    data: list[AgentOutputData]


AgentOutput = (
    AgentOutputStructuredText
    | AgentOutputUnstructuredText
    | AgentOutputTabularNumeric
    | AgentOutputEnumeratedListSubset
)

### PROMPTS IN PYDANTIC


class ValidPromptTemplate(BaseModel):
    """Pydantic model to store LLM Prompts.
    Should contain the prompt text, and metadata with placeholder values for any string replacements in the text."""

    @model_validator(mode="after")
    def schema_matches_examples(self) -> Self:
        model_content = self.text_replacement_examples.model_dump(exclude_none=True)
        if not sorted(self.content_schema) == sorted(model_content.keys()):
            raise ValueError(
                f"Not all text replacements we need exist in the metadata for this prompt, expected {sorted(self.content_schema)}, got {sorted(model_content.keys())}"
            )
        return self

    text: NonEmptyString
    text_replacement_examples: AgentInput
    content_schema: list[str]

    def get_formatted_prompt(self, text_replacements: AgentInput) -> str:
        context = text_replacements.model_dump(exclude_none=True)

        if not sorted(self.content_schema) == sorted(context.keys()):
            raise ValueError(
                f"Not all text replacements we need exist in the metadata for this prompt, expected {sorted(self.content_schema)}, got {sorted(context.keys())}"
            )
        result = f"{self.text}\n# ADDITIONAL CONTEXT\n"
        for k, v in context.items():
            result += f"## {k.upper()}\n{v}\n"
        return result

    def get_formatted_prompt_sample_inputs(self) -> str:
        context = self.text_replacement_examples.model_dump(exclude_none=True)
        result = f"{self.text}\n# ADDITIONAL CONTEXT\n"
        for k, v in context.items():
            result += f"## {k.upper()}\n{v}\n"
        return result


class ValidAgentConfig(BaseModel):
    """Pydantic model to store and version LLM Agents.
    Should contain the context required to create and use a PydanticAI Agent in any Zeplyn environment (not Django specific)."""

    class Config:
        # Avoid this error:
        # pydantic.errors.PydanticSchemaGenerationError:
        # Unable to generate pydantic-core schema for <class 'openai.Timeout'>. Set `arbitrary_types_allowed=True`...
        # https://github.com/pydantic/pydantic/discussions/5767#discussioncomment-5919490
        arbitrary_types_allowed = True

    class OutputSchemaKind(StrEnum):
        STRUCTURED_TEXT = "structured_text"
        UNSTRUCTURED_TEXT = "unstructured_text"
        STRUCTURED_TABULAR = "structured_tabular"
        ENUMERATED_LIST_SUBSET = "list_subset"

    name: Annotated[str, StringConstraints(max_length=200)]
    user_prompt: ValidPromptTemplate
    system_prompt: ValidPromptTemplate | None
    output_validation_schema: OutputSchemaKind
    model: Model
    model_settings: ModelSettings | None
    retries: int = 3

    def to_agent(self) -> Agent[AgentInput, AgentOutput]:
        match self.output_validation_schema:
            case self.OutputSchemaKind.UNSTRUCTURED_TEXT:
                return Agent[AgentInput, AgentOutput](
                    model=self.model,
                    name=self.name,
                    retries=self.retries,
                    model_settings=self.model_settings,
                    deps_type=AgentInput,
                    output_type=AgentOutputUnstructuredText,
                    instructions=self.system_prompt.get_formatted_prompt_sample_inputs()
                    if self.system_prompt
                    else None,
                )
            case self.OutputSchemaKind.STRUCTURED_TEXT:
                return Agent[AgentInput, AgentOutput](
                    model=self.model,
                    name=self.name,
                    retries=self.retries,
                    model_settings=self.model_settings,
                    deps_type=AgentInput,
                    output_type=AgentOutputStructuredText,
                    instructions=self.system_prompt.get_formatted_prompt_sample_inputs()
                    if self.system_prompt
                    else None,
                )
            case self.OutputSchemaKind.STRUCTURED_TABULAR:
                return Agent[AgentInput, AgentOutput](
                    model=self.model,
                    name=self.name,
                    retries=self.retries,
                    model_settings=self.model_settings,
                    deps_type=AgentInput,
                    output_type=AgentOutputTabularNumeric,
                    instructions=self.system_prompt.get_formatted_prompt_sample_inputs()
                    if self.system_prompt
                    else None,
                )
            case self.OutputSchemaKind.ENUMERATED_LIST_SUBSET:
                return Agent[AgentInput, AgentOutput](
                    model=self.model,
                    name=self.name,
                    retries=self.retries,
                    model_settings=self.model_settings,
                    deps_type=AgentInput,
                    output_type=AgentOutputEnumeratedListSubset,
                    instructions=self.system_prompt.get_formatted_prompt_sample_inputs()
                    if self.system_prompt
                    else None,
                )
            case _:
                raise ValidationError("Invalid configuration for agent")


class AgentConfig(StatusMixin, UUIDMixin):
    """Django model to store and version LLM Agents. Should contain all the context required to create and use an Agent."""

    ### Object level fields for a new database object
    name = models.CharField(
        max_length=200,
        help_text="A unique-to-org machine-readable identifier for this agent.",
    )

    version_notes = models.TextField(
        blank=True, null=True, help_text="Notes about this version - what changed, what's being tested, etc."
    )
    history = HistoricalRecords()

    ### Foreign keys to other Django database objects
    ### These exist only in the Django object and not Pydantic, as they aren't part of the agent itself, just metadata
    ### AgentConfig is owned by an organization
    owner = models.ForeignKey(
        Organization, blank=True, null=True, on_delete=models.CASCADE, help_text="The organization owning this agent"
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["owner", "name"],
                name="One agent with this name per owner",
                condition=models.Q(owner__isnull=False),
            )
        ]

    ### Fields from previous prompt object
    user_prompt = models.TextField(
        blank=True,
        null=True,
        help_text="User level instructions for the LLM which passes user specific data to the model, and may contain placeholders.",
    )

    system_prompt = models.TextField(
        blank=True,
        null=True,
        help_text="System-level instructions for the LLM. This provides context and constraints for the model's behavior, and may contain placeholders.",
    )

    ### Metadata added to existing prompt object for validity
    user_prompt_metadata_example = models.JSONField(
        blank=True,
        null=True,
        help_text="Metadata samples used to fully configure placeholders in the user prompt - changes with each call",
    )
    user_prompt_metadata_schema = models.JSONField(
        blank=True,
        null=True,
        help_text="A list with names of all required additional data fields, that we will append to the prompt at runtime",
    )
    system_prompt_metadata = models.JSONField(
        blank=True,
        null=True,
        help_text="Metadata used to fully configure placeholders in the system prompt - per-owner",
    )

    ### Fields from previous StructuredMeetingDataSchema object
    output_validation_schema = models.TextField(
        help_text="The name of the schema, defined in code, that will be used for agent output validation."
    )

    ### Additional fields needed for PydanticAI Agent configuration
    llm_model = models.TextField(help_text="The LLM Model we're using for our PydanticAI Agent e.g. 'openai:gpt-4o'")
    model_settings = models.JSONField(blank=True, null=True, help_text="Additional settings for the LLM Model")

    def __str__(self) -> str:
        return f"AgentConfig: {self.name} version {self.created} for {self.owner.name if self.owner else None}"

    def to_valid_agent_config(self) -> ValidAgentConfig:
        system_prompt = None
        if self.system_prompt:
            if self.system_prompt_metadata:
                post_meeting_context = AgentInput(**self.system_prompt_metadata)
                system_prompt = ValidPromptTemplate(
                    text=self.system_prompt,
                    text_replacement_examples=post_meeting_context,
                    content_schema=self.system_prompt_metadata.keys(),
                )
            else:
                system_prompt = ValidPromptTemplate(
                    text=self.system_prompt, text_replacement_examples=AgentInput(), content_schema=[]
                )

        model = OpenAIModel(model_name=self.llm_model, provider=OpenAIProvider(api_key=settings.OPENAI_API_KEY))
        return ValidAgentConfig(
            name=self.name,
            user_prompt=ValidPromptTemplate(
                text=self.user_prompt,
                text_replacement_examples=self.user_prompt_metadata_example,
                content_schema=self.user_prompt_metadata_schema,
            ),
            system_prompt=system_prompt,
            output_validation_schema=self.output_validation_schema,
            model=model,
            # ModelSettings is a typechecked typedict, DB isn't
            # we want to pass in anything from db here and maybe have it throw -- so need to ignore at typechecking time
            model_settings=ModelSettings(**self.model_settings) if self.model_settings else None,  # type: ignore
        )


def get_agent_config_and_run_agent(
    agent_owner: Organization, agent_name: str, agent_input: AgentInput
) -> AgentOutput | None:
    """Helper function to reduce boilerplate needed to call an agent

    Can be used in the calling location wrapped with a context manager for any validation context:
    with expect_list_items(ListMetadata(max_items=1, permitted_items=potential_items)):
        result = get_agent_config_and_run_agent(owner, name, input)

    Output validation may still be necessary:
    * to confirm the AgentOutput is the expected output type
    * to confirm the result was not empty
    * to confirm actual existence of any database objects the LLM thinks should exist
    * This is not a comprehensive list, depends on use case
    """
    agent_config_db = AgentConfig.objects.filter(owner=agent_owner, name=agent_name).order_by("-created")

    if not agent_config_db.exists():
        logging.warning(
            "Can't get agent %s for org %s - no agent exists",
            agent_name,
            agent_owner.uuid,
        )
        return None

    try:
        agent_config = agent_config_db[0].to_valid_agent_config()
        agent = agent_config.to_agent()

        user_prompt = agent_config.user_prompt

        # Call agent, does Pydantic validation on the way out automatically
        result = agent.run_sync(user_prompt.get_formatted_prompt(agent_input))
    except Exception as e:
        logging.error("Exception with %s agent for org %s", agent_name, agent_owner.uuid, exc_info=e)
        return None

    if not result.output:
        logging.info(
            "No output was returned from LLM for %s for org %s",
            agent_name,
            agent_owner.uuid,
        )
        return None

    return result.output
