from django.db import migrations

from deepinsights.ml_agents.migrations import workflow_for_task_prompt


def create_config(apps, schema_editor) -> None:
    AgentConfig = apps.get_model("ml_agents", "AgentConfig")

    AgentConfig.objects.create(
        name="workflow_for_task",
        user_prompt=workflow_for_task_prompt,
        version_notes="First version",
        user_prompt_metadata_example={"note_context": "test", "transcript": "test"},
        user_prompt_metadata_schema=["note_context", "transcript"],
        system_prompt=None,
        system_prompt_metadata=None,
        output_validation_schema="list_subset",
        llm_model="gpt-4o-2024-08-06",
    )


def delete_config(apps, schema_editor) -> None:
    AgentConfig = apps.get_model("ml_agents", "AgentConfig")
    AgentConfig.objects.filter(
        name=[
            "workflow_for_task",
        ]
    ).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("ml_agents", "0003_agentconfig_internal"),
    ]

    operations = [
        migrations.RunPython(create_config, delete_config),
    ]
