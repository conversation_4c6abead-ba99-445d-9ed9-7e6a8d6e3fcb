from django.db import migrations

from deepinsights.ml_agents.migrations import agenda_followup_agentconfig_user_prompt


def create_config(apps, schema_editor) -> None:
    AgentConfig = apps.get_model("ml_agents", "AgentConfig")

    AgentConfig.objects.create(
        name="agenda_followup_generator",
        user_prompt=agenda_followup_agentconfig_user_prompt,
        version_notes="First version",
        user_prompt_metadata_example={"agenda_template": "test", "transcript": "test"},
        user_prompt_metadata_schema=["agenda_template", "transcript"],
        system_prompt=None,
        system_prompt_metadata=None,
        output_validation_schema="structured_text",
        llm_model="gpt-4o",
    )


def delete_config(apps, schema_editor) -> None:
    AgentConfig = apps.get_model("ml_agents", "AgentConfig")
    AgentConfig.objects.filter(
        name=[
            "agenda_followup_generator",
        ]
    ).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("ml_agents", "0002_agentconfig"),
    ]

    operations = [
        migrations.RunPython(create_config, delete_config),
    ]
