from django.db import migrations

from deepinsights.ml_agents.migrations import meetingtype_for_note_prompt


def create_config(apps, schema_editor) -> None:
    AgentConfig = apps.get_model("ml_agents", "AgentConfig")

    AgentConfig.objects.create(
        name="meetingtype_for_note",
        user_prompt=meetingtype_for_note_prompt,
        version_notes="First version",
        user_prompt_metadata_example={"note_context": "test"},
        user_prompt_metadata_schema=["note_context"],
        system_prompt=None,
        system_prompt_metadata=None,
        output_validation_schema="list_subset",
        llm_model="gpt-4o-2024-08-06",
    )


def delete_config(apps, schema_editor) -> None:
    AgentConfig = apps.get_model("ml_agents", "AgentConfig")
    AgentConfig.objects.filter(
        name=[
            "meetingtype_for_note",
        ]
    ).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("ml_agents", "0004_agentconfig_tasks"),
    ]

    operations = [
        migrations.RunPython(create_config, delete_config),
    ]
