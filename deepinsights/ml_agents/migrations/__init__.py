
agenda_followup_agentconfig_user_prompt = """You are analyzing a meeting transcript to create structured meeting data based on an agenda template.
Your task is to extract how each agenda item was discussed and present it as a structured JSON object following a specific schema.

## INSTRUCTIONS:
1. First, parse the markdown agenda template to identify headers and bullet points:
   - Headers are typically denoted by "#", "##", etc.
   - Bullet points are typically denoted by "-", "*", or numeric lists

2. For each header in the agenda, create a header entry in the output JSON

3. For each bullet point under a header, create a toggle entry that shows whether the topic was discussed:
   - Set "discussed" to true if there is evidence the topic was discussed in the transcript
   - Set "discussed" to false if the topic was not discussed
   - Provide an "explanation" with bullet points explaining what was discussed for that topic
   - Use "•" for bullet points in the explanation field with line breaks between points

## OUTPUT FORMAT:
Your output must be valid JSON that follows this structure:
- The root object contains a single "review_entries" array
- Each entry in the array is either a header (for sections) or a toggle (for topics)
- Headers have "kind":"header" and contain the section name
- Toggles have "kind":"toggle", with "discussed" and "explanation" fields

## EXAMPLE:

Given a markdown agenda template like:
```markdown
# Investment Overview
- Current portfolio performance
- Asset allocation strategy
- Market outlook

# Retirement Planning
- Contribution updates
- Withdrawal strategy
- Tax considerations
```

The output JSON should look like:
```json
{{
  "review_entries": [
    {{
      "id": "investment_overview",
      "kind": "header",
      "topic": "Investment Overview"
    }},
    {{
      "id": "current_portfolio_performance",
      "kind": "toggle",
      "topic": "Current portfolio performance",
      "discussed": true,
      "explanation": "• Portfolio grew by 8.2% over the past quarter
• Technology stocks outperformed expectations
• Fixed income investments underperformed due to interest rate changes"
    }},
    {{
      "id": "asset_allocation_strategy",
      "kind": "toggle",
      "topic": "Asset allocation strategy",
      "discussed": true,
      "explanation": "• Agreed to increase equity allocation from 60% to 65%
• Will reduce bond exposure due to interest rate environment
• Considering adding alternative investments for diversification"
    }},
    {{
      "id": "market_outlook",
      "kind": "toggle",
      "topic": "Market outlook",
      "discussed": false,
      "explanation": ""
    }},
    {{
      "id": "retirement_planning",
      "kind": "header",
      "topic": "Retirement Planning"
    }},
    {{
      "id": "contribution_updates",
      "kind": "toggle",
      "topic": "Contribution updates",
      "discussed": true,
      "explanation": "• Client increased 401(k) contribution to the maximum allowable
• Will make additional $6,000 IRA contribution before tax deadline"
    }},
    {{
      "id": "withdrawal_strategy",
      "kind": "toggle",
      "topic": "Withdrawal strategy",
      "discussed": false,
      "explanation": ""
    }},
    {{
      "id": "tax_considerations",
      "kind": "toggle",
      "topic": "Tax considerations",
      "discussed": true,
      "explanation": "• Discussed Roth conversion strategy for this year
• Projected tax impact of retirement withdrawals
• Recommended tax-loss harvesting opportunities"
    }}
  ]
}}
```

IMPORTANT:
1. First parse the markdown agenda template to identify headers and bullet points
2. Generate valid JSON with no trailing commas
3. Include ALL agenda topics as toggles
4. Make IDs lowercase with underscores
5. For each toggle, set "discussed" to true ONLY if there is clear evidence in the transcript
6. For discussed topics, provide detailed bullet points in the explanation field
7. Format explanations with bullet points using the "•" character and line breaks
8. Return ONLY the JSON with no additional commentary
"""

workflow_for_task_prompt = """You are analyzing a meeting transcript to assign a workflow to a follow-up action item.
Your task is to extract the correct workflow in the CRM to use to follow up for the task.

## EXAMPLE:

Given a task with the title "Process IRA rollover paperwork for James Chen's old 401(k)"


The output should look like:
```
{external_id: 102, name: "Client Info Update"}
```

IMPORTANT:
1. Use only workflows that exist for the CRM configuration for the organization owning the task.
2. Return ONLY the output with no additional commentary"""

meetingtype_for_note_prompt = """You are analyzing meeting note metadata for an upcoming meeting, to assign a meeting type.
Your task is to extract the correct meeting type to use to generate an agenda for the upcoming meeting.

## EXAMPLE:

Given a meeting note with the following context:

The note owner is John Smith, at Sequoia Financial Group.
The note metadata is {'id': '6b7dlnru9pb10ricl4kkmfvi91',
 'body': 'Financial advising discussion',
 'title': 'Meeting with Jane Doe',
 'all_day': False,
 'end_time': '2025-07-17T12:00:00-04:00',
 'provider': 'google',
 'start_time': '2025-07-17T11:00:00-04:00',
 'meeting_urls': ['https://meet.google.com/ufn-sejr-voa',
  'https://tel.meet/ufn-sejr-voa?pin=4436807908466'],
 'participants': [{'id': None,
   'name': None,
   'zeplyn_kind': None,
   'zeplyn_uuid': None,
   'email_address': '<EMAIL>'},
  {'id': None,
   'name': None,
   'zeplyn_kind': None,
   'zeplyn_uuid': None,
   'email_address': '<EMAIL>'}],
 'user_specific_id': '6b7dlnru9pb10ricl4kkmfvi91'}.
The potential meeting types for this note are: [{"const": "ecbd4fdd-578e-48f6-b332-13068ed17187", "title": "Client Meeting"}, {"const": "697dbf67-7a10-475b-a427-fde433c64c48", "title": "Internal Meeting"}]


The output should look like:
```
{"const": "ecbd4fdd-578e-48f6-b332-13068ed17187", "title": "Client Meeting"}
```

IMPORTANT:
1. Use only meeting types that exist for the user owning the task.
2. Return ONLY the output with no additional commentary"""