import uuid

import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("ml_agents", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0145_midmeeting_prompt_update"),
    ]

    operations = [
        migrations.CreateModel(
            name="HistoricalAgentConfig",
            fields=[
                ("id", models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                ("uuid", models.UUIDField(db_index=True, default=uuid.uuid4, editable=False)),
                (
                    "name",
                    models.CharField(
                        help_text="A unique-to-org machine-readable identifier for this agent.", max_length=200
                    ),
                ),
                (
                    "version_notes",
                    models.TextField(
                        blank=True,
                        help_text="Notes about this version - what changed, what's being tested, etc.",
                        null=True,
                    ),
                ),
                (
                    "user_prompt",
                    models.TextField(
                        blank=True,
                        help_text="User level instructions for the LLM which passes user specific data to the model, and may contain placeholders.",
                        null=True,
                    ),
                ),
                (
                    "system_prompt",
                    models.TextField(
                        blank=True,
                        help_text="System-level instructions for the LLM. This provides context and constraints for the model's behavior, and may contain placeholders.",
                        null=True,
                    ),
                ),
                (
                    "user_prompt_metadata_example",
                    models.JSONField(
                        blank=True,
                        help_text="Metadata samples used to fully configure placeholders in the user prompt - changes with each call",
                        null=True,
                    ),
                ),
                (
                    "user_prompt_metadata_schema",
                    models.JSONField(
                        blank=True,
                        help_text="A list with names of all required additional data fields, that we will append to the prompt at runtime",
                        null=True,
                    ),
                ),
                (
                    "system_prompt_metadata",
                    models.JSONField(
                        blank=True,
                        help_text="Metadata used to fully configure placeholders in the system prompt - per-owner",
                        null=True,
                    ),
                ),
                (
                    "output_validation_schema",
                    models.TextField(
                        help_text="The name of the schema, defined in code, that will be used for agent output validation."
                    ),
                ),
                (
                    "llm_model",
                    models.TextField(
                        help_text="The LLM Model we're using for our PydanticAI Agent e.g. 'openai:gpt-4o'"
                    ),
                ),
                (
                    "model_settings",
                    models.JSONField(blank=True, help_text="Additional settings for the LLM Model", null=True),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")], max_length=1),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="The organization owning this agent",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="meetingsapp.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical agent config",
                "verbose_name_plural": "historical agent configs",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="AgentConfig",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                (
                    "name",
                    models.CharField(
                        help_text="A unique-to-org machine-readable identifier for this agent.", max_length=200
                    ),
                ),
                (
                    "version_notes",
                    models.TextField(
                        blank=True,
                        help_text="Notes about this version - what changed, what's being tested, etc.",
                        null=True,
                    ),
                ),
                (
                    "user_prompt",
                    models.TextField(
                        blank=True,
                        help_text="User level instructions for the LLM which passes user specific data to the model, and may contain placeholders.",
                        null=True,
                    ),
                ),
                (
                    "system_prompt",
                    models.TextField(
                        blank=True,
                        help_text="System-level instructions for the LLM. This provides context and constraints for the model's behavior, and may contain placeholders.",
                        null=True,
                    ),
                ),
                (
                    "user_prompt_metadata_example",
                    models.JSONField(
                        blank=True,
                        help_text="Metadata samples used to fully configure placeholders in the user prompt - changes with each call",
                        null=True,
                    ),
                ),
                (
                    "user_prompt_metadata_schema",
                    models.JSONField(
                        blank=True,
                        help_text="A list with names of all required additional data fields, that we will append to the prompt at runtime",
                        null=True,
                    ),
                ),
                (
                    "system_prompt_metadata",
                    models.JSONField(
                        blank=True,
                        help_text="Metadata used to fully configure placeholders in the system prompt - per-owner",
                        null=True,
                    ),
                ),
                (
                    "output_validation_schema",
                    models.TextField(
                        help_text="The name of the schema, defined in code, that will be used for agent output validation."
                    ),
                ),
                (
                    "llm_model",
                    models.TextField(
                        help_text="The LLM Model we're using for our PydanticAI Agent e.g. 'openai:gpt-4o'"
                    ),
                ),
                (
                    "model_settings",
                    models.JSONField(blank=True, help_text="Additional settings for the LLM Model", null=True),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        blank=True,
                        help_text="The organization owning this agent",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="meetingsapp.organization",
                    ),
                ),
            ],
        ),
        migrations.AddConstraint(
            model_name="agentconfig",
            constraint=models.UniqueConstraint(
                condition=models.Q(("owner__isnull", False)),
                fields=("owner", "name"),
                name="One agent with this name per owner",
            ),
        ),
    ]
