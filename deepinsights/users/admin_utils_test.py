from unittest import mock
from unittest.mock import MagicMock, patch

from django.contrib.messages import get_messages
from django.contrib.messages.storage.fallback import FallbackStorage
from django.http import HttpRequest
from django.test import RequestFactory, TestCase

from deepinsights.users.admin_utils import (
    RANDOM_STRING_CHARS,
    CrmSystemFilter,
    generate_random_password,
    handle_bulk_user_onboarding,
    process_password_generation_and_email,
)
from deepinsights.users.models.user import User


class TestPasswordManagement(TestCase):
    def setUp(self) -> None:
        self.factory = RequestFactory()
        # Create users with unique usernames
        self.user1 = User.objects.create(email="<EMAIL>", username="testuser1")
        self.user2 = User.objects.create(email="<EMAIL>", username="testuser2")

        # Setup request object with messages middleware
        self.request = self.factory.get("/")
        setattr(self.request, "session", {})
        messages = FallbackStorage(self.request)
        setattr(self.request, "_messages", messages)

    def test_generate_random_password(self) -> None:
        # Test default length
        password = generate_random_password()
        self.assertEqual(len(password), 14)
        self.assertTrue(all(c in RANDOM_STRING_CHARS for c in password))

        # Test custom length
        password = generate_random_password(length=20)
        self.assertEqual(len(password), 20)

        # Test custom characters
        custom_chars = "ABC123"
        password = generate_random_password(allowed_chars=custom_chars)
        self.assertTrue(all(c in custom_chars for c in password))

        # Test that passwords are random
        passwords = [generate_random_password() for _ in range(5)]
        self.assertEqual(len(set(passwords)), 5)  # All passwords should be unique

    @patch("deepinsights.users.admin_utils.email_service.send_email")
    def test_process_password_generation_and_email(self, mock_send_email: MagicMock) -> None:
        mock_send_email.return_value = {"message_id": "django-email-123", "status": "Sent", "mails_sent": 1}

        users = [self.user1, self.user2]
        success_count, errors = process_password_generation_and_email(users, self.request)

        self.assertEqual(success_count, 2)
        self.assertEqual(len(errors), 0)
        self.assertEqual(mock_send_email.call_count, 2)

        messages = list(get_messages(self.request))
        self.assertEqual(len(messages), 2)
        self.assertTrue(all("successfully" in str(m).lower() for m in messages))

        # Verify passwords were actually changed
        for user in users:
            db_user = User.objects.get(id=user.id)
            self.assertNotEqual(db_user.password, "")

        # Test failure scenario
        mock_send_email.side_effect = Exception("Email sending failed")
        success_count, errors = process_password_generation_and_email(users, self.request)

        # Assertions for failure
        self.assertEqual(success_count, 0)
        self.assertEqual(len(errors), 2)
        self.assertTrue(all("failed" in error.lower() for error in errors))

    @patch("deepinsights.users.admin_utils.email_service.send_email")
    def test_handle_bulk_user_onboarding(self, mock_send_email: MagicMock) -> None:
        mock_send_email.return_value = {"message_id": "django-email-123", "status": "Sent", "mails_sent": 1}

        # Test with empty user list
        result = handle_bulk_user_onboarding([], self.request)
        self.assertTrue(result)
        messages = list(get_messages(self.request))
        self.assertEqual(len(messages), 0)

        # Test successful processing
        users = [self.user1, self.user2]
        result = handle_bulk_user_onboarding(users, self.request, log_prefix="TEST: ")
        self.assertTrue(result)

        messages = list(get_messages(self.request))
        self.assertTrue(any("Successfully sent welcome emails to all 2 users" in str(m) for m in messages))

        # Test partial failure
        mock_send_email.side_effect = [
            {"message_id": "django-email-123", "status": "Sent", "mails_sent": 1},
            Exception("Email sending failed"),
        ]
        result = handle_bulk_user_onboarding(users, self.request, log_prefix="TEST: ")
        self.assertFalse(result)

        # Test complete failure
        mock_send_email.side_effect = Exception("Email sending failed")
        result = handle_bulk_user_onboarding(users, self.request, log_prefix="TEST: ")
        self.assertFalse(result)


class TestCrmSystemFilter(TestCase):
    def setUp(self) -> None:
        self.filter = CrmSystemFilter(request=HttpRequest(), params={}, model=User, model_admin=MagicMock())
        self.user1 = User.objects.create(
            email="<EMAIL>", username="salesforce_user", crm_configuration={"crm_system": "salesforce"}
        )
        self.user2 = User.objects.create(
            email="<EMAIL>", username="redtail_user", crm_configuration={"crm_system": "redtail"}
        )
        self.user3 = User.objects.create(
            email="<EMAIL>", username="none_user", crm_configuration={"crm_system": "none"}
        )

    def test_lookups(self) -> None:
        expected = (
            ("sharefile", "Sharefile"),
            ("sharepoint", "Sharepoint"),
            ("none", "None"),
            ("wealthbox", "Wealthbox"),
            ("redtail", "Redtail"),
            ("salesforce", "Salesforce"),
        )
        self.assertEqual(self.filter.lookups(HttpRequest(), MagicMock()), expected)

    def test_queryset_filtering(self) -> None:
        for crm_system in ["salesforce", "redtail", "none"]:
            with self.subTest(crm_system=crm_system):
                with mock.patch.object(self.filter, "value", return_value=crm_system):
                    filtered = self.filter.queryset(HttpRequest(), User.objects.all())
                    self.assertEqual(filtered.count(), 1)
                    if first := filtered.first():
                        self.assertEqual(first.crm_configuration["crm_system"], crm_system)

    def test_no_filter_value(self) -> None:
        with mock.patch.object(self.filter, "value", return_value=None):
            all_users = User.objects.all()
            filtered = self.filter.queryset(HttpRequest(), all_users)
            self.assertEqual(filtered, all_users)

    def test_invalid_filter_value(self) -> None:
        with mock.patch.object(self.filter, "value", return_value="invalid_crm"):
            filtered = self.filter.queryset(HttpRequest(), User.objects.all())
            self.assertEqual(filtered.count(), 0)
