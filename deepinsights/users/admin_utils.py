import logging
import secrets
from typing import Iterable

from django.contrib import admin, messages
from django.contrib.admin.options import ModelAdmin
from django.db.models.query import QuerySet
from django.http import HttpRequest

from deepinsights.core.email_service import ZeplynEmailService
from deepinsights.users.models.user import User

logger = logging.getLogger(__name__)


email_service = ZeplynEmailService()


RANDOM_STRING_CHARS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"


def generate_random_password(length: int = 14, allowed_chars: str = RANDOM_STRING_CHARS) -> str:
    return "".join(secrets.choice(allowed_chars) for i in range(length))


def process_password_generation_and_email(
    users: list[User], request: HttpRequest | None = None
) -> tuple[int, list[str]]:
    """
    Process password generation and email sending for users.

    Args:
        users: List of User objects to process
        request: Optional HttpRequest object for admin messages

    Returns:
        tuple[int, list[str]]: (success_count, error_messages)
    """
    success_count = 0
    errors = []

    for user in users:
        try:
            # Generate and set password
            new_password = generate_random_password()
            user.set_password(new_password)
            user.save()

            # Send email
            email_body = (
                f"Dear Zeplyn User\n\n"
                f"Your new Zeplyn password is: {new_password}\n"
                f"We recommend using SSO for a seamless experience, but please keep this email as a backup."
                f"If you have any questions or trouble logging in, please email <NAME_EMAIL>\n\n"
                f"Regards,\nYour Team at Zeplyn"
            )

            try:
                response = email_service.send_email(
                    recipients=user.email, body=email_body, subject="Zeplyn Login Credentials"
                )
            except Exception as e:
                logging.error("Failed to send email to %s", user.email, exc_info=e)
                raise
            success_count += 1

            # Add success message if request provided
            if request:
                messages.success(request, f"Password successfully generated and emailed to {user.email}.")

            logger.info("Successfully processed user: %s", user.email)

        except Exception as e:
            error_msg = f"Failed to process user {user.email}: {str(e)}"
            errors.append(error_msg)
            logger.error(error_msg)

            # Add error message if request provided
            if request:
                messages.error(request, error_msg)

    return success_count, errors


def handle_bulk_user_onboarding(users: list[User], request: HttpRequest | None = None, log_prefix: str = "") -> bool:
    """
    Handle bulk user onboarding including password generation and email sending.

    Args:
        users: List of User objects to process
        request: Optional HttpRequest object for admin messages
        log_prefix: Optional prefix for log messages

    Returns:
        bool: True if all users were processed successfully, False otherwise
    """
    try:
        if not users:
            logger.info("%sNo users require password generation", log_prefix)
            return True

        success_count, errors = process_password_generation_and_email(users, request)

        # Log results
        total_users = len(users)
        if success_count == total_users:
            logger.info("%sSuccessfully processed all %d users", log_prefix, total_users)
            if request:
                messages.success(request, f"Successfully sent welcome emails to all {total_users} users.")
            return True
        else:
            logger.warning(
                "%sProcessed %d out of %d users. Errors: %s", log_prefix, success_count, total_users, ", ".join(errors)
            )
            return False

    except Exception as e:
        error_msg = f"{log_prefix}Error during bulk user onboarding: {str(e)}"
        logger.error(error_msg)
        if request:
            messages.error(request, error_msg)
        return False


class CrmSystemFilter(admin.SimpleListFilter):
    title = "CRM System"
    parameter_name = "crm_system"

    def lookups(self, request: HttpRequest, model_admin: ModelAdmin[User]) -> Iterable[tuple[str, str]]:
        return (
            ("sharefile", "Sharefile"),
            ("sharepoint", "Sharepoint"),
            ("none", "None"),
            ("wealthbox", "Wealthbox"),
            ("redtail", "Redtail"),
            ("salesforce", "Salesforce"),
        )

    def queryset(self, request: HttpRequest, queryset: QuerySet[User]) -> QuerySet[User]:
        if not self.value():
            return queryset
        return queryset.filter(crm_configuration__contains={"crm_system": self.value()})
