from typing import Any

import pytest
from django import forms
from django.utils.translation import gettext_lazy as _

from deepinsights.users.forms import UserAdminChangeForm, UserAdminCreationForm
from deepinsights.users.models.user import User


@pytest.mark.django_db
def test_username_validation_error_msg(django_user_model: User) -> None:
    """
    Tests UserAdminCreation Form's unique validator functions correctly by testing:
        1) A new user with an existing username cannot be added.
        2) Only 1 error is raised by the UserCreation Form
        3) The desired error message is raised
    """

    user = django_user_model.objects.create(username="testuser")
    user.set_password("testpassword")
    # The user already exists,
    # hence cannot be created.
    form = UserAdminCreationForm(
        {
            "username": user.username,
            "password1": user.password,
            "password2": user.password,
        }
    )

    assert not form.is_valid()
    assert len(form.errors) == 1
    assert "username" in form.errors
    assert form.errors["username"][0] == _("This username has already been taken.")


@pytest.mark.parametrize(
    "pin_value,expected_result,expected_error",
    [
        ("1234", "1234", None),
        (None, None, None),
        ("", "", None),
        ("abc", None, _("PIN must be numeric.")),
        (1234, None, _("PIN must be a string.")),
    ],
)
def test_user_admin_change_form_clean_pin(
    pin_value: Any | None, expected_result: Any | None, expected_error: str | None
) -> None:
    form = UserAdminChangeForm()
    form.cleaned_data = {"pin": pin_value}

    if expected_error:
        with pytest.raises(forms.ValidationError) as excinfo:
            form.clean_pin()
        assert excinfo.value.message == expected_error
    else:
        result = form.clean_pin()
        assert result == expected_result
