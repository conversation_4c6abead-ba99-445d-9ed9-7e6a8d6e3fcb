from django.contrib import admin, messages
from django.contrib.auth.models import AbstractUser
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, QuerySet
from django.http import HttpRequest
from django.utils.html import format_html
from django.utils.safestring import SafeString
from django.utils.translation import gettext_lazy as _
from django_google_sso.admin import get_current_user_and_admin
from django_google_sso.main import GoogleAuth
from django_google_sso.models import GoogleSSOUser
from django_json_widget.widgets import JSONEditorWidget

from deepinsights.core.admin_mixins import BotPreferencesMixin, PreferencesSchemasExamplesMixin
from deepinsights.core.integrations.calendar.auto_join import update_recall_auto_join_integration
from deepinsights.core.integrations.meetingbot.recall_ai import RecallBotController
from deepinsights.core.integrations.oauth.DummyOAuthCalendarIntegration import DummyOAuthCalendarIntegration
from deepinsights.core.integrations.oauth.google import GoogleOAuth
from deepinsights.core.integrations.oauth.microsoft import MicrosoftOAuth
from deepinsights.meetingsapp.admin_utils import get_user_active_agendas_html, get_user_active_templates_html
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.meetingsapp.models.phone_number import PhoneNumber
from deepinsights.meetingsapp.tasks import sync_crm_clients
from deepinsights.users.admin_utils import CrmSystemFilter, handle_bulk_user_onboarding
from deepinsights.users.forms import UserAdminChangeForm, UserAdminCreationForm
from deepinsights.users.models.flags import Flag
from deepinsights.users.models.subscription_plan import SubscriptionPlan
from deepinsights.users.models.user import User
from onboarding_scripts.add_dummy_data import load_demo_data_for_user


# Monkey-patch GoogleAuth.get_netloc to return the location derived from the
# current request.
def get_netloc(self: GoogleAuth):  # type: ignore[no-untyped-def]
    return self.request.get_host()


GoogleAuth.get_netloc = get_netloc


# The django-google-sso library force-adds admin registrations for the
# GoogleSSOUser and current user model. The code here undoes this;
# we don't care about information we can see in the admin console, only
# the SSO sign in ability.

CurrentUserModel, _unused, LastUserAdmin = get_current_user_and_admin()


# Remove the GoogleSSOUser model from the admin site.
admin.site.unregister(GoogleSSOUser)


# Unregister the admin for the User model so we can register our custom one.
if admin.site.is_registered(CurrentUserModel):
    admin.site.unregister(CurrentUserModel)

# Make the password change sites inaccessible.
admin.site.password_change_template = "admin/404.html"
admin.site.password_change_done_template = "admin/404.html"


class PhoneNumberInline(admin.TabularInline):  # type: ignore[type-arg]
    verbose_name = "Phone Number"
    model = PhoneNumber
    extra = 0


# Register our admin page for the User model.
@admin.register(CurrentUserModel)
class UserAdmin(LastUserAdmin, BotPreferencesMixin, PreferencesSchemasExamplesMixin):  # type: ignore[misc, valid-type]
    form = UserAdminChangeForm
    add_form = UserAdminCreationForm
    ordering = ("-date_joined",)
    fieldsets = (
        (None, {"fields": ("username", "password")}),
        (_("Personal info"), {"fields": ("name", "first_name", "last_name", "email", "uuid")}),
        PreferencesSchemasExamplesMixin.examples_fieldsets(),
        (
            ("Account Settings"),
            {
                "fields": (
                    "manager",
                    "pin",
                    "preferences",
                    "status",
                    "organization",
                    "metadata",
                    "crm_configuration",
                    "license_type",
                    "entitlements",
                )
            },
        ),
        (
            _("Permissions"),
            {
                "fields": (
                    "is_active",
                    "is_staff",
                    "is_superuser",
                    "groups",
                    "user_permissions",
                ),
            },
        ),
        (
            ("Feature Flags"),
            {
                "fields": ("flags_enabled_for_user",),
            },
        ),
        (
            ("Templates"),
            {
                "fields": ("active_templates", "active_agendas"),
            },
        ),
        (_("Important dates"), {"fields": ("last_login", "date_joined")}),
    )
    list_display = ["id", "email", "username", "name", "is_superuser", "notes_link", "license_type", "organization"]
    search_fields = ["email", "uuid", "username", "name"]
    list_filter = [
        CrmSystemFilter,
        "status",
        "license_type",
        "organization",
        "is_staff",
        "is_superuser",
        "groups",
    ]
    actions = [
        "update_bot_preferences",
        "generate_password_and_send_email",
        "fetch_clients_from_crm",
        "update_auto_join_calendar_events",
        "delete_recall_autojoin_calendar_integration",
        "try_to_relink_disconnected_calendar",
        "delete_and_create_autojoin_calendar_integration",
        "update_autojoin_calendar_oauth_credentials",
        "load_demo_data",
    ]
    formfield_overrides = {JSONField: {"widget": JSONEditorWidget(mode="view")}}
    readonly_fields = [
        "uuid",
        "flags_enabled_for_user",
        "active_templates",
        "active_agendas",
    ] + PreferencesSchemasExamplesMixin.examples_fieldsets_fields()
    inlines = [PhoneNumberInline]

    @admin.display(description="Enabled Flags")
    def flags_enabled_for_user(self, obj: "User") -> SafeString:
        enabled_flags = [flag.name for flag in Flag.objects.all() if flag.is_active_for_user(obj)]

        if not enabled_flags:
            return format_html("<em>No flags enabled</em>")

        return format_html(
            "<ul class='enabled-flags'>{}</ul>",
            format_html("".join([f"<li>{flag}</li>" for flag in sorted(enabled_flags)])),
        )

    @admin.action(description="Generate Password and Send Email")
    def generate_password_and_send_email(self, request: HttpRequest, queryset):  # type: ignore[no-untyped-def]
        """Admin action to generate new passwords and send them to selected users."""
        handle_bulk_user_onboarding(users=queryset, request=request, log_prefix="UserAdmin:")

    @admin.display(description="Notes")
    def notes_link(self, obj: AbstractUser) -> SafeString:
        notes_url = f"/api/admin/meetingsapp/note/?note_owner__pk__exact={obj.pk}"
        return format_html(f'<a href="{notes_url}" target="_blank">View Notes</a>')

    @admin.action(description="Fetch clients from CRM")
    def fetch_clients_from_crm(self, request: HttpRequest, queryset: QuerySet[AbstractUser]) -> None:
        """Admin action to fetch clients from CRM."""
        for user in queryset:
            if not isinstance(user, User):
                return
            sync_crm_clients.delay(user.uuid)
        self.message_user(
            request,
            "CRM fetch initiated for user(s). Check Celery job status for updates.",
            messages.SUCCESS,
        )

    @admin.action(description="Delete Recall auto-join calendar integrations")
    def delete_recall_autojoin_calendar_integration(self, request: HttpRequest, queryset: QuerySet[User]) -> None:
        for user in queryset:
            user_name = user.name or f"{user.first_name} {user.last_name}".strip()
            if (
                OAuthCredentials.objects.filter(user=user, integration="microsoft").exists()
                or OAuthCredentials.objects.filter(user=user, integration="google").exists()
            ):
                update_recall_auto_join_integration(
                    user,
                    enabled=False,
                    oauth=DummyOAuthCalendarIntegration(),
                    calendar_link_function=lambda client_id, client_secret, email, refresh_token, calendar_id: (
                        True,
                        "dummy_calendar_id",
                    ),
                )
                self.message_user(
                    request,
                    f"Recall auto-join calendar integrations deleted for expired users {user_name}",
                    messages.SUCCESS,
                )
            else:
                self.message_user(
                    request, f"No integration found for user {user_name}, skipping deletion", messages.SUCCESS
                )

    @admin.action(description="Delete and re-create Recall auto-join calendar integrations")
    def delete_and_create_autojoin_calendar_integration(self, request: HttpRequest, queryset: QuerySet[User]) -> None:
        self._relink_auto_join_calendar_integration(request, queryset, delete_first=True)

    @admin.action(description="Update Recall auto-join calendar integration OAuth credentials")
    def update_autojoin_calendar_oauth_credentials(self, request: HttpRequest, queryset: QuerySet[User]) -> None:
        self._relink_auto_join_calendar_integration(request, queryset, delete_first=False)

    def _relink_auto_join_calendar_integration(
        self, request: HttpRequest, queryset: QuerySet[User], delete_first: bool
    ) -> None:
        def relink_microsoft(user: User) -> None:
            def update_ms_calendar(enabled: bool) -> None:
                update_recall_auto_join_integration(
                    user,
                    enabled=enabled,
                    oauth=MicrosoftOAuth(),
                    calendar_link_function=RecallBotController().link_microsoft_calendar,
                    force_enable=True,
                )

            if delete_first:
                update_ms_calendar(False)
            update_ms_calendar(True)
            self.message_user(
                request,
                f"Recall Microsoft auto-join calendar integration updated for user {user_name}",
                messages.SUCCESS,
            )

        def relink_google(user: User) -> None:
            def update_google_calendar(enabled: bool) -> None:
                update_recall_auto_join_integration(
                    user,
                    enabled=enabled,
                    oauth=GoogleOAuth(),
                    calendar_link_function=RecallBotController().link_google_calendar,
                    force_enable=True,
                )

            if delete_first:
                update_google_calendar(False)
            update_google_calendar(True)
            self.message_user(
                request,
                f"Recall Google auto-join calendar integration updated for user {user_name}",
                messages.SUCCESS,
            )

        for user in queryset:
            user_name = user.name or f"{user.first_name} {user.last_name}".strip()
            if not user.recall_calendar_id:
                self.message_user(
                    request, f"No Recall calendar ID found for user {user_name}, skipping update", messages.SUCCESS
                )
                continue

            if user.recall_calendar_platform == "microsoft":
                relink_microsoft(user)
            elif user.recall_calendar_platform == "google":
                relink_google(user)
            elif OAuthCredentials.objects.filter(user=user, integration="microsoft").exists():
                relink_microsoft(user)
            elif OAuthCredentials.objects.filter(user=user, integration="google").exists():
                relink_google(user)
            else:
                self.message_user(
                    request, f"No integration found for user {user_name}, skipping update", messages.SUCCESS
                )

    @admin.action(description="Attempt to re-link a disconnected Recall (auto-join) calendar")
    def try_to_relink_disconnected_calendar(self, request: HttpRequest, queryset: QuerySet[User]) -> None:
        relinked: list[str] = []
        not_relinked: list[str] = []
        for user in queryset:
            if not (calendar_id := user.recall_calendar_id):
                not_relinked.append(user.name)
                continue
            RecallBotController.try_to_relink_calendar(calendar_id)
            relinked.append(user.name)

        if not_relinked:
            self.message_user(request, f"No calendars found to relink for users: {not_relinked}", messages.WARNING)

        if relinked:
            self.message_user(
                request,
                f"Attempted to relink disconnected calendars. Check https://go/recallcalendar to confim. Users: {relinked}",
                messages.SUCCESS,
            )

    @admin.action(description="Load demo data for selected users")
    def load_demo_data(self, request: HttpRequest, queryset: QuerySet[User]) -> None:
        success_count = 0
        skipped_count = 0
        failed_count = 0

        for user in queryset:
            if not isinstance(user, User):
                continue

            # Skip users who already have notes
            if Note.objects.filter(note_owner=user.id).exists():
                user_name = user.name or f"{user.first_name} {user.last_name}".strip() or user.username
                self.message_user(
                    request, f"Skipped demo data loading for user {user_name} - user already has notes", messages.INFO
                )
                skipped_count += 1
                continue

            try:
                success = load_demo_data_for_user(user)
                user_name = user.name or f"{user.first_name} {user.last_name}".strip() or user.username

                if success:
                    self.message_user(request, f"Successfully loaded demo data for user {user_name}", messages.SUCCESS)
                    success_count += 1
                else:
                    self.message_user(
                        request, f"Failed to load demo data for user {user_name} - see logs for details", messages.ERROR
                    )
                    failed_count += 1

            except Exception as e:
                user_name = user.name or f"{user.first_name} {user.last_name}".strip() or user.username
                self.message_user(request, f"Error loading demo data for user {user_name}: {str(e)}", messages.ERROR)
                failed_count += 1

        if queryset.count() > 1:
            self.message_user(
                request,
                f"Demo data loading complete: {success_count} successful, {skipped_count} skipped (already had data), {failed_count} failed",
                messages.INFO,
            )

    @admin.display(description="Active Templates")
    def active_templates(self, obj: User) -> SafeString:
        """Display all active templates for this user."""
        return get_user_active_templates_html(obj)

    @admin.display(description="Active Agenda Templates")
    def active_agendas(self, obj: User) -> SafeString:
        """Display all active agenda templates for this user."""
        return get_user_active_agendas_html(obj)


@admin.register(Flag)
class FlagAdmin(admin.ModelAdmin):  # type: ignore[type-arg]
    actions = []
    list_display = ["name", "override_enabled_by_environment", "everyone", "has_org_overrides", "modified"]
    list_filter = ["override_enabled_by_environment", "everyone", "organizations", "disabled_organizations"]
    fields = [
        "name",
        "note",
        "override_enabled_by_environment",
        "everyone",
        "superusers",
        "staff",
        "organizations",
        "disabled_organizations",
        "groups",
        "users",
        "created",
        "modified",
    ]
    readonly_fields = ["name", "note", "override_enabled_by_environment", "created", "modified"]
    filter_horizontal = ["disabled_organizations", "groups", "organizations", "users"]
    ordering = ["-id"]
    search_fields = ["name", "note"]

    @admin.display(description="Has org-level overrides?", boolean=True)
    def has_org_overrides(self, obj: Flag) -> bool:
        return obj.organizations.exists() or obj.disabled_organizations.exists()

    def has_add_permission(self, request, obj=None):  # type: ignore[no-untyped-def]
        return False

    def has_delete_permission(self, request, obj=None):  # type: ignore[no-untyped-def]
        return False


@admin.register(SubscriptionPlan)
class SubscriptionPlanAdmin(admin.ModelAdmin[SubscriptionPlan]):
    list_display = ["organization_name", "title"]
    search_fields = ["organization__name", "title"]

    @admin.display(description="Organization Name")
    def organization_name(self, obj: SubscriptionPlan) -> str:
        return obj.organization.name
