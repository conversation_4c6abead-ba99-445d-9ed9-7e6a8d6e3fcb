import logging
from typing import Any, List, Optional

from django.apps import apps
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.db.models import BooleanField, CharField, ForeignKey, J<PERSON>NField
from django.db.models.functions import Upper
from django.utils.translation import gettext_lazy as _
from model_utils import Choices
from phonenumber_field.phonenumber import PhoneNumber
from pydantic import BaseModel

from deepinsights.core.behaviours import UUIDMixin
from deepinsights.core.preferences.preferences import (
    CrmConfiguration,
    Preferences,
    get_default_crm_configuration,
)
from deepinsights.users.models.subscription_plan import EntitlementType
from deepinsights.utils.choice_array_field import ChoiceArrayField


class UserMetadata(BaseModel):
    recall_calendar_id: str | None = None
    recall_calendar_platform: str | None = None
    calendar_lookahead: str | None = None
    show_events_without_meeting_urls: bool | None = None
    microsoft_id: str | None = None
    google_id: str | None = None
    biasing_words: list[str] | None = None
    user_context: str | None = None
    source: str | None = None
    title: str | None = None

    class Config:
        extra = "allow"


class User(AbstractUser, UUIDMixin):
    STATUS_CHOICES = Choices(
        ("active", "active"), ("waitlisted", "waitlisted"), ("unknown", "unknown"), ("expired", "expired")
    )

    class LicenseType(models.TextChoices):
        advisor = "advisor", "advisor"
        csa = "csa", "csa"
        staff = "staff", "staff"

        def __eq__(self, other: Any) -> bool:
            if isinstance(other, User.LicenseType):
                return self.value.lower() == other.value.lower()
            if isinstance(other, str):
                return self.value.lower() == other.lower()
            return False

    # First and last name do not cover name patterns around the globe
    name = CharField(_("Name of User"), blank=True, max_length=255)
    first_name = CharField(_("First Name"), blank=True, null=True, max_length=100)  # type: ignore[misc]
    middle_name = CharField(_("Middle Name"), blank=True, null=True, max_length=100)
    last_name = CharField(_("Last Name"), blank=True, null=True, max_length=100)  # type: ignore[misc]
    status = CharField(_("Status"), max_length=100, choices=STATUS_CHOICES, null=True, blank=True)
    role = CharField(_("Role"), blank=True, null=True, max_length=100)
    company_size = CharField(_("Company Size"), blank=True, null=True, max_length=50)
    # Deprecated and unused
    is_integrated = BooleanField(default=False)
    organization = ForeignKey("meetingsapp.Organization", on_delete=models.CASCADE, related_name="users", null=True)
    pin = models.CharField(
        null=True,
        blank=True,
        help_text="Numeric PIN for identifying the user in contexts where a password cannot be used (e.g., an inbound phone call).",
    )
    preferences = JSONField(null=True, blank=True, default=dict)
    crm_configuration = JSONField(blank=True, default=get_default_crm_configuration)
    license_type = models.CharField(max_length=100, default=LicenseType.advisor, choices=LicenseType.choices)
    # TODO: Migrate the contents of this dictionary to preferences.
    # contains biasing_words and user_context
    metadata = JSONField(blank=True, default=dict)
    manager = ForeignKey(
        "self",
        on_delete=models.SET_NULL,
        related_name="managed_users",
        null=True,
        blank=True,
        help_text="The user who manages this user, if any.",
    )
    entitlements = ChoiceArrayField(
        CharField(choices=EntitlementType.choices),
        null=False,
        blank=True,
        default=list,
        help_text="Entitlements (features) enabled for this user.",
    )

    @property
    def is_organization_admin(self) -> bool:
        return bool(
            self.organization and self.entitlements and (EntitlementType.organization_admin in self.entitlements)
        )

    def get_role_for_llm(self) -> str:
        """Generate a user role string for LLM use.

        This code uses role or a heuristic based on license_type to generate a role for the user.
        """
        if self.role:
            return self.role
        if self.license_type == User.LicenseType.advisor:
            return "financial advisor"
        return "staff member"

    def get_preferences(self) -> Preferences:
        preferences = Preferences()
        if self.organization:
            preferences.update(data=self.organization.get_preferences())
        preferences.update(data=self.preferences or {})
        return preferences

    def get_crm_configuration(self) -> CrmConfiguration:
        crm_config = self.organization.get_crm_configuration() if self.organization else CrmConfiguration()
        user_configuration = CrmConfiguration.from_dict(self.crm_configuration) or CrmConfiguration()
        # This hotfix specifically addresses the fact that "none" is the default value for the
        # crm_system field, and so a User's "none" overrides and Organization's non-"none". Ideally,
        # this will disappear in a future update to make preference hierarchies work more seamlessly.
        if user_configuration.crm_system == "none" or user_configuration.crm_system is None:
            user_configuration.crm_system = crm_config.crm_system
        crm_config.update(user_configuration)
        return crm_config

    def __str__(self):  # type: ignore[no-untyped-def]
        return self.email

    @property
    def crm_handler(self):  # type: ignore[no-untyped-def]
        from deepinsights.core.integrations.crm.crm_manager import get_crm_interface

        return get_crm_interface(self)

    @property
    def recall_calendar_id(self) -> str | None:
        return UserMetadata.model_validate(self.metadata).recall_calendar_id

    @recall_calendar_id.setter
    def recall_calendar_id(self, value: str | None) -> None:
        model = UserMetadata.model_validate(self.metadata)
        model.recall_calendar_id = value
        self.metadata = model.model_dump(exclude_none=True)

    @property
    def recall_calendar_platform(self) -> str | None:
        return UserMetadata.model_validate(self.metadata).recall_calendar_platform

    @recall_calendar_platform.setter
    def recall_calendar_platform(self, value: str | None) -> None:
        model = UserMetadata.model_validate(self.metadata)
        model.recall_calendar_platform = value
        self.metadata = model.model_dump(exclude_none=True)

    @property
    def calendar_lookahead(self) -> str | None:
        return UserMetadata.model_validate(self.metadata).calendar_lookahead

    @calendar_lookahead.setter
    def calendar_lookahead(self, value: str | None) -> None:
        model = UserMetadata.model_validate(self.metadata)
        model.calendar_lookahead = value
        self.metadata = model.model_dump(exclude_none=True)

    @property
    def show_events_without_meeting_urls(self) -> bool:
        show_events = UserMetadata.model_validate(self.metadata).show_events_without_meeting_urls
        return True if show_events is None else show_events

    @show_events_without_meeting_urls.setter
    def show_events_without_meeting_urls(self, value: bool) -> None:
        model = UserMetadata.model_validate(self.metadata)
        model.show_events_without_meeting_urls = value
        self.metadata = model.model_dump(exclude_none=True)

    @property
    def microsoft_id(self) -> str | None:
        return UserMetadata.model_validate(self.metadata).microsoft_id

    @microsoft_id.setter
    def microsoft_id(self, value: str | None) -> None:
        model = UserMetadata.model_validate(self.metadata)
        model.microsoft_id = value
        self.metadata = model.model_dump(exclude_none=True)

    @property
    def google_id(self) -> str | None:
        return UserMetadata.model_validate(self.metadata).google_id

    @google_id.setter
    def google_id(self, value: str | None) -> None:
        model = UserMetadata.model_validate(self.metadata)
        model.google_id = value
        self.metadata = model.model_dump(exclude_none=True)

    @property
    def biasing_words(self) -> Optional[List[str]]:
        return UserMetadata.model_validate(self.metadata).biasing_words

    @biasing_words.setter
    def biasing_words(self, value: Optional[List[str]]) -> None:
        model = UserMetadata.model_validate(self.metadata)
        model.biasing_words = value
        self.metadata = model.model_dump(exclude_none=True)

    @property
    def user_context(self) -> Optional[str]:
        return UserMetadata.model_validate(self.metadata).user_context

    @user_context.setter
    def user_context(self, value: Optional[str]) -> None:
        model = UserMetadata.model_validate(self.metadata)
        model.user_context = value
        self.metadata = model.model_dump(exclude_none=True)

    @property
    def primary_phone_number(self) -> PhoneNumber | None:
        """Get the primary phone number for the user.

        If the user has a primary phone number, return that. Otherwise, assume that if the user only
        has one phone number that it is the primary number. Otherwise, return None (since the user
        either does not have any phone numbers, or the user has multiple phone numbers and none
        are marked as primary).
        """

        # Avoid an import loop by using the app registry to get the model
        DBPhoneNumber = apps.get_model("meetingsapp", "PhoneNumber")

        try:
            return self.phone_numbers.get(primary=True).number
        except DBPhoneNumber.DoesNotExist:
            try:
                return self.phone_numbers.get().number
            except DBPhoneNumber.MultipleObjectsReturned:
                logging.error(
                    "User %s has multiple phone numbers, but none are marked as primary. Cannot determine primary phone number",
                    self.uuid,
                )
            except DBPhoneNumber.DoesNotExist:
                pass
            return None

    class Meta:
        verbose_name = "User"
        ordering = ["uuid", "email"]
        indexes = [
            models.Index(Upper("email"), name="user_email_upper_index"),
        ]
