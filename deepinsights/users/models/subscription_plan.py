import logging

import pydantic
from django.db.models import (
    CASCADE,
    CharField,
    DateTimeField,
    OneToOneField,
    TextChoices,
)
from django.utils.translation import gettext_lazy as _
from django_json_schema_editor.fields import <PERSON><PERSON><PERSON><PERSON>

from deepinsights.core.behaviours import StatusMixin, UUIDMixin


class EntitlementType(TextChoices):
    """The types of entitlements that can be assigned to a user."""

    meeting_assistant = "meeting_assistant", "Meeting Assistant"
    client_intelligence = "client_intelligence", "Client Intelligence"
    practice_intelligence = "practice_intelligence", "Practice Intelligence"
    meeting_prep = "meeting_prep", "Meeting Prep"
    phone_meetings = "phone_meetings", "Phone Meetings"
    organization_admin = "organization_admin", "Organization Admin"


class MeetingAssistantEntitlementDetails(pydantic.BaseModel):
    """Details specific to the Meeting Assistant entitlement."""

    """The number of meetings the organization is entitled to during each subscription term."""
    meetings_count: int = pydantic.Field(
        ..., description="The number of meetings the organization is entitled to during each subscription term."
    )


class Entitlement(pydantic.BaseModel):
    """A feature of the Zeplyn app which users can be entitled to access"""

    type: EntitlementType = pydantic.Field(
        ...,
        description="The type of entitlement. Each type represents a specific feature set or capability in the app.",
    )
    license_count: int = pydantic.Field(
        ..., description="The number of licenses purchased by the organization for this entitlement."
    )
    details: MeetingAssistantEntitlementDetails | None = pydantic.Field(
        None, description="Optional details specific to the entitlement type."
    )


# A convenience wrapper for a list of Entitlement objects.
Entitlements = pydantic.RootModel[list[Entitlement]]


class SubscriptionTerm(TextChoices):
    """The term of the organization's subscription."""

    monthly = "monthly", "Monthly"
    yearly = "yearly", "Yearly"


class SubscriptionPlan(StatusMixin, UUIDMixin):
    title = CharField(_("Title"), max_length=50)
    term = CharField(
        _("Subscription term"),
        default=SubscriptionTerm.monthly,
        max_length=50,
        choices=SubscriptionTerm.choices,
        null=False,
        blank=False,
        help_text=_("The subscription term for the plan, e.g., monthly or yearly."),
    )
    start_date = DateTimeField(
        _("Start date"),
        null=False,
        blank=False,
        help_text=_(
            "The date when the subscription plan initially started. Note that this is not the "
            "start date of the current term, but rather the first date of the original "
            "subscription (i.e., 'a customer since')."
        ),
    )
    _entitlements = JSONField(schema=Entitlements.model_json_schema(), null=False, blank=False)
    organization = OneToOneField(
        "meetingsapp.Organization",
        on_delete=CASCADE,
        null=False,
        blank=False,
        related_name="plan",
        help_text=(
            "The organization that has this plan. "
            "The plan determines the features and entitlements available to the organization."
        ),
    )

    def __str__(self) -> str:
        return f"{self.organization.name} - {self.title}"

    @property
    def entitlements(self) -> list[Entitlement]:
        try:
            return Entitlements.model_validate(self._entitlements).root
        except pydantic.ValidationError as e:
            logging.error("Invalid entitlements data for SubscriptionPlan %s", self.uuid, exc_info=e)
            return []

    @entitlements.setter
    def entitlements(self, value: list[Entitlement]) -> None:
        try:
            self._entitlements = Entitlements(value).model_dump(mode="json")
        except pydantic.ValidationError as e:
            logging.error("Invalid entitlements data for SubscriptionPlan %s", self.uuid, exc_info=e)

    @property
    def meetings_allowed_per_subscription_term(self) -> int:
        for entitlement in self.entitlements:
            if entitlement.type == EntitlementType.meeting_assistant and entitlement.details:
                return entitlement.details.meetings_count * entitlement.license_count
        return 0
