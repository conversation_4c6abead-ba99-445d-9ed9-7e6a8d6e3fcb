import datetime

import pytest

from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.subscription_plan import (
    Entitlement,
    Entitlements,
    EntitlementType,
    MeetingAssistantEntitlementDetails,
    SubscriptionPlan,
)


@pytest.fixture
def plan() -> SubscriptionPlan:
    return SubscriptionPlan.objects.create(
        title="Test Plan",
        start_date=datetime.datetime(2023, 1, 1),
        _entitlements=[],
        organization=Organization.objects.create(name="Test Organization"),
    )


def test_entitlements_root_model() -> None:
    e = Entitlement(
        type=EntitlementType.meeting_assistant,
        license_count=2,
        details=MeetingAssistantEntitlementDetails(meetings_count=3),
    )
    wrapper = Entitlements([e])
    assert wrapper.root == [e]


def test_plan_entitlements_property(plan: SubscriptionPlan) -> None:
    e1 = Entitlement(
        type=EntitlementType.meeting_assistant,
        license_count=2,
        details=MeetingAssistantEntitlementDetails(meetings_count=3),
    )
    e2 = Entitlement(
        type=EntitlementType.client_intelligence,
        license_count=5,
    )
    plan.entitlements = [e1, e2]
    result = plan.entitlements
    assert result == [e1, e2]


def test_plan_entitlements_invalid_data_returns_empty(plan: SubscriptionPlan, caplog: pytest.LogCaptureFixture) -> None:
    plan._entitlements = {"invalid": "data"}
    plan.save()
    assert plan.entitlements == []
    assert "Invalid entitlements data" in caplog.text


def test_meetings_allowed_per_subscription_term(plan: SubscriptionPlan) -> None:
    e = Entitlement(
        type=EntitlementType.meeting_assistant,
        license_count=3,
        details=MeetingAssistantEntitlementDetails(meetings_count=4),
    )
    plan.entitlements = [e]
    assert plan.meetings_allowed_per_subscription_term == 12
    plan.entitlements = []
    assert plan.meetings_allowed_per_subscription_term == 0


def test_str_representation(plan: SubscriptionPlan) -> None:
    plan.title = "Gold"
    plan.save()
    assert str(plan) == "Test Organization - Gold"


def test_meetings_allowed_with_non_meeting_entitlement() -> None:
    plan = SubscriptionPlan.__new__(SubscriptionPlan)
    e = Entitlement(
        type=EntitlementType.client_intelligence,
        license_count=10,
    )
    plan.entitlements = [e]
    assert plan.meetings_allowed_per_subscription_term == 0
