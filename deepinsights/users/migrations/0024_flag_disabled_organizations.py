# Generated by Django 4.2.18 on 2025-03-04 22:48

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0115_organization_make_uuid_unique_timestamps_nonnull"),
        ("users", "0023_alter_user_crm_configuration_alter_user_preferences"),
    ]

    operations = [
        migrations.AddField(
            model_name="flag",
            name="disabled_organizations",
            field=models.ManyToManyField(
                blank=True,
                help_text="Organizations for which this flag should be inactive, regardless of the values of (most) other settings. If an organization is in this list, it overrides all other settings for users in that organization, except the environment-based override. Note specifically that enabling a flag for users in a disabled organization will not cause the flag to be enabled for those users.",
                related_name="disabledflag_set",
                to="meetingsapp.organization",
            ),
        ),
    ]
