# Generated by Django 4.2.18 on 2025-02-25 20:15

from django.db import migrations, models

import deepinsights.core.preferences.preferences

class Migration(migrations.Migration):

    dependencies = [
        ("users", "0022_alter_user_preferences"),
    ]

    operations = [
        migrations.AlterField(
            model_name="user",
            name="crm_configuration",
            field=models.JSO<PERSON>ield(
                blank=True, default=deepinsights.core.preferences.preferences.get_default_crm_configuration
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="preferences",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
    ]
