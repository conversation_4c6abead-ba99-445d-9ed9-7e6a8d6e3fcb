# Generated by Django 4.2.18 on 2025-04-10 01:59

import phonenumber_field.modelfields
from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0025_alter_flag_crm_systems"),
        ("meetingsapp", "0125_phonenumber_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="user",
            name="phone_number",
            field=phonenumber_field.modelfields.PhoneNumberField(
                blank=True,
                help_text="DEPRECATED: This field is deprecated and will be removed in a future version. \n\nThe user's phone number.\n\nThis field is somewhat intelligent: if you pass in an invalid phone number it will return an error. If you provide a valid US phone number (without country code), it will automatically add the country code. If you provide in a valid phone number with a country code, it will use that country code.\n\nNote that the number is stored and rendered in E.164 format (e.g., +12125551212), regardless of how you enter it.",
                max_length=128,
                null=True,
                region=None,
            ),
        ),
    ]
