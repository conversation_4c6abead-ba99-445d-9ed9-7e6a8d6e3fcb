# Generated by Django 4.2.22 on 2025-06-18 18:18

import deepinsights.utils.choice_array_field
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("meetingsapp", "0148_client_client_email_upper_index"),
        ("users", "0028_user_user_email_upper_index"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="entitlements",
            field=deepinsights.utils.choice_array_field.ChoiceArrayField(
                base_field=models.CharField(
                    choices=[
                        ("meeting_assistant", "Meeting Assistant"),
                        ("client_intelligence", "Client Intelligence"),
                        ("practice_intelligence", "Practice Intelligence"),
                        ("meeting_prep", "Meeting Prep"),
                        ("phone_meetings", "Phone Meetings"),
                        ("organization_admin", "Organization Admin"),
                    ]
                ),
                blank=True,
                default=list,
                help_text="Entitlements (features) enabled for this user.",
                size=None,
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="manager_identifier",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.CreateModel(
            name="SubscriptionPlan",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ("title", models.CharField(max_length=50, verbose_name="Title")),
                (
                    "term",
                    models.CharField(
                        choices=[("monthly", "Monthly"), ("yearly", "Yearly")],
                        default="monthly",
                        help_text="The subscription term for the plan, e.g., monthly or yearly.",
                        max_length=50,
                        verbose_name="Subscription term",
                    ),
                ),
                (
                    "start_date",
                    models.DateTimeField(
                        help_text="The date when the subscription plan initially started. Note that this is not the start date of the current term, but rather the first date of the original subscription (i.e., 'a customer since').",
                        verbose_name="Start date",
                    ),
                ),
                ("_entitlements", models.JSONField()),
                (
                    "organization",
                    models.OneToOneField(
                        help_text="The organization that has this plan. The plan determines the features and entitlements available to the organization.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="plan",
                        to="meetingsapp.organization",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
