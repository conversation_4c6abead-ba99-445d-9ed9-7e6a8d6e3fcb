# Generated by Django 4.2 on 2024-03-02 04:45

import deepinsights.meetingsapp.models.organization
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0006_alter_user_organization"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="crm_configuration",
            field=models.J<PERSON><PERSON>ield(default=deepinsights.meetingsapp.models.organization.get_default_crm_configuration),
        ),
        migrations.AlterField(
            model_name="user",
            name="preferences",
            field=models.JSONField(
                default=deepinsights.meetingsapp.models.organization.get_default_preferences, null=True
            ),
        ),
    ]
