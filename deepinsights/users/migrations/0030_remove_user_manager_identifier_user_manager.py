# Generated by Django 4.2.22 on 2025-06-20 01:04

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0029_user_entitlements_user_manager_identifier_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="user",
            name="manager_identifier",
        ),
        migrations.AddField(
            model_name="user",
            name="manager",
            field=models.ForeignKey(
                blank=True,
                help_text="The user who manages this user, if any.",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="managed_users",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
