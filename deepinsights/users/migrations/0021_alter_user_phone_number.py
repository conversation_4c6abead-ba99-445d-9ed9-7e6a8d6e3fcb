# Generated by Django 5.1.5 on 2025-01-30 23:19

import phonenumber_field.modelfields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0020_user_phone_number"),
    ]

    operations = [
        migrations.AlterField(
            model_name="user",
            name="phone_number",
            field=phonenumber_field.modelfields.PhoneNumberField(
                blank=True,
                help_text="The user's phone number.\n\nThis field is somewhat intelligent: if you pass in an invalid phone number it will return an error. If you provide a valid US phone number (without country code), it will automatically add the country code. If you provide in a valid phone number with a country code, it will use that country code.\n\nNote that the number is stored and rendered in E.164 format (e.g., +12125551212), regardless of how you enter it.",
                max_length=128,
                null=True,
                region=None,
            ),
        ),
    ]
