# Generated by Django 4.2 on 2024-10-12 02:24

from django.db import migrations


def update_user_licenses(apps, schema_editor):
    User = apps.get_model("users", "User")
    db_alias = schema_editor.connection.alias

    users = User.objects.using(db_alias).prefetch_related("license__license_type")
    for user in users:
        try:
            user.license_type = user.license.license_type
        except Exception:
            user.license_type = "advisor"
    User.objects.using(db_alias).bulk_update(users, ["license_type"])


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0016_user_license_type"),
    ]

    operations = [
        migrations.RunPython(update_user_licenses),
    ]
