# Generated by Django 4.2 on 2024-08-06 23:43

from django.conf import settings
from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0052_remove_attendee_unique_note_attendee_name"),
        ("auth", "0012_alter_user_first_name_max_length"),
        ("users", "0012_remove_license_is_active"),
    ]

    operations = [
        migrations.CreateModel(
            name="Sample",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "name",
                    models.Char<PERSON>ield(
                        help_text="The human/computer readable name.", max_length=100, unique=True, verbose_name="Name"
                    ),
                ),
                (
                    "percent",
                    models.DecimalField(
                        decimal_places=1,
                        help_text="A number between 0.0 and 100.0 to indicate a percentage of the time this sample will be active.",
                        max_digits=4,
                        verbose_name="Percent",
                    ),
                ),
                (
                    "note",
                    models.TextField(blank=True, help_text="Note where this Sample is used.", verbose_name="Note"),
                ),
                (
                    "created",
                    models.DateTimeField(
                        db_index=True,
                        default=django.utils.timezone.now,
                        help_text="Date when this Sample was created.",
                        verbose_name="Created",
                    ),
                ),
                (
                    "modified",
                    models.DateTimeField(
                        default=django.utils.timezone.now,
                        help_text="Date when this Sample was last modified.",
                        verbose_name="Modified",
                    ),
                ),
            ],
            options={
                "verbose_name": "Sample",
                "verbose_name_plural": "Samples",
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Switch",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "name",
                    models.CharField(
                        help_text="The human/computer readable name.", max_length=100, unique=True, verbose_name="Name"
                    ),
                ),
                (
                    "active",
                    models.BooleanField(default=False, help_text="Is this switch active?", verbose_name="Active"),
                ),
                (
                    "note",
                    models.TextField(blank=True, help_text="Note where this Switch is used.", verbose_name="Note"),
                ),
                (
                    "created",
                    models.DateTimeField(
                        db_index=True,
                        default=django.utils.timezone.now,
                        help_text="Date when this Switch was created.",
                        verbose_name="Created",
                    ),
                ),
                (
                    "modified",
                    models.DateTimeField(
                        default=django.utils.timezone.now,
                        help_text="Date when this Switch was last modified.",
                        verbose_name="Modified",
                    ),
                ),
            ],
            options={
                "verbose_name": "Switch",
                "verbose_name_plural": "Switches",
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Flag",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "name",
                    models.CharField(
                        help_text="The human/computer readable name.", max_length=100, unique=True, verbose_name="Name"
                    ),
                ),
                (
                    "everyone",
                    models.BooleanField(
                        blank=True,
                        help_text="Flip this flag on (Yes) or off (No) for everyone, overriding all other settings. Leave as Unknown to use normally.",
                        null=True,
                        verbose_name="Everyone",
                    ),
                ),
                (
                    "percent",
                    models.DecimalField(
                        blank=True,
                        decimal_places=1,
                        help_text="A number between 0.0 and 99.9 to indicate a percentage of users for whom this flag will be active.",
                        max_digits=3,
                        null=True,
                        verbose_name="Percent",
                    ),
                ),
                (
                    "testing",
                    models.BooleanField(
                        default=False,
                        help_text="Allow this flag to be set for a session for user testing",
                        verbose_name="Testing",
                    ),
                ),
                (
                    "superusers",
                    models.BooleanField(
                        default=True, help_text="Flag always active for superusers?", verbose_name="Superusers"
                    ),
                ),
                (
                    "staff",
                    models.BooleanField(
                        default=False, help_text="Flag always active for staff?", verbose_name="Staff"
                    ),
                ),
                (
                    "authenticated",
                    models.BooleanField(
                        default=False,
                        help_text="Flag always active for authenticated users?",
                        verbose_name="Authenticated",
                    ),
                ),
                (
                    "languages",
                    models.TextField(
                        blank=True,
                        default="",
                        help_text="Activate this flag for users with one of these languages (comma-separated list)",
                        verbose_name="Languages",
                    ),
                ),
                (
                    "rollout",
                    models.BooleanField(default=False, help_text="Activate roll-out mode?", verbose_name="Rollout"),
                ),
                ("note", models.TextField(blank=True, help_text="Note where this Flag is used.", verbose_name="Note")),
                (
                    "created",
                    models.DateTimeField(
                        db_index=True,
                        default=django.utils.timezone.now,
                        help_text="Date when this Flag was created.",
                        verbose_name="Created",
                    ),
                ),
                (
                    "modified",
                    models.DateTimeField(
                        default=django.utils.timezone.now,
                        help_text="Date when this Flag was last modified.",
                        verbose_name="Modified",
                    ),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Activate this flag for these user groups.",
                        to="auth.group",
                        verbose_name="Groups",
                    ),
                ),
                (
                    "organizations",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Organizations for which this flag should be active.",
                        to="meetingsapp.organization",
                    ),
                ),
                (
                    "users",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Activate this flag for these users.",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Users",
                    ),
                ),
            ],
            options={
                "verbose_name": "Flag",
                "verbose_name_plural": "Flags",
                "abstract": False,
            },
        ),
    ]
