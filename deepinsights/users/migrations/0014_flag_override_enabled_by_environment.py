# Generated by Django 4.2 on 2024-08-13 19:28

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0013_sample_switch_flag"),
    ]

    operations = [
        migrations.AddField(
            model_name="flag",
            name="override_enabled_by_environment",
            field=models.BooleanField(
                blank=True,
                help_text="If on, the flag is forced on in this environment, overriding all other settings. If off, the flag is forced off in this environment, overriding all other settings. Otherwise, the other diversion criteria control the value of this flag. This is controlled by the values in deepinsights/flags/flagdefs.py. Do not adjust this flag by hand or in the shell.",
                null=True,
            ),
        ),
    ]
