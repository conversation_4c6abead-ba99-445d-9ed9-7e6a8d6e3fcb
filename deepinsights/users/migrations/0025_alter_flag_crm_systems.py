# Generated by Django 4.2.18 on 2025-03-20 21:32

import deepinsights.utils.choice_array_field
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0024_flag_disabled_organizations"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="flag",
            name="crm_systems",
            field=deepinsights.utils.choice_array_field.ChoiceArrayField(
                base_field=models.CharField(
                    choices=[
                        ("salesforce_base", "Salesforce (base)"),
                        ("salesforce_financial_cloud", "Salesforce (Financial Cloud)"),
                        ("sequoia_sf", "Salesforce (Sequoia)"),
                        ("wealthbox", "Wealthbox"),
                        ("redtail", "Redtail"),
                        ("sharefile", "ShareFile"),
                        ("sharepoint", "SharePoint"),
                    ]
                ),
                blank=True,
                default=list,
                null=True,
                size=None,
            ),
        ),
    ]
