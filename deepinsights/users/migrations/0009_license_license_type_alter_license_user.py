# Generated by Django 4.2 on 2024-04-07 14:41

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0008_license_license_unique_license"),
    ]

    operations = [
        migrations.AddField(
            model_name="license",
            name="license_type",
            field=models.CharField(
                choices=[("advisor", "advisor"), ("csa", "csa")], default="advisor", max_length=100
            ),
        ),
        migrations.AlterField(
            model_name="license",
            name="user",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="license",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
