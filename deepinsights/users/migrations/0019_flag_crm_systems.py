# Generated by Django 4.2 on 2024-11-12 19:37

import deepinsights.users.models.flags
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0018_delete_license"),
    ]

    operations = [
        migrations.AddField(
            model_name="flag",
            name="crm_systems",
            field=deepinsights.users.models.flags.ChoiceArrayField(
                base_field=models.CharField(
                    choices=[
                        ("salesforce_base", "Salesforce (base)"),
                        ("salesforce_financial_cloud", "Salesforce (Financial Cloud)"),
                        ("sequoia_sf", "Salesforce (Sequoia)"),
                        ("wealthbox", "Wealthbox"),
                        ("redtail", "Redtail"),
                        ("sharefile", "ShareFile"),
                    ]
                ),
                blank=True,
                default=list,
                null=True,
                size=None,
            ),
        ),
    ]
