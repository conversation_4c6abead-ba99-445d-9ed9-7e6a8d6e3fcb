from urllib.parse import urlencode

from django.shortcuts import redirect
from rest_framework.decorators import api_view, permission_classes
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import AllowAny
from rest_framework.request import Request

from deepinsights.core.integrations.meetingbot.bot_controller import ZoomStatus
from deepinsights.core.integrations.meetingbot.recall_ai import <PERSON>callBotController


@api_view(["GET"])
@permission_classes([AllowAny])
def handle_zoom_authorization(request: Request):  # type: ignore[no-untyped-def]
    code = request.GET.get("code")
    if not code:
        raise ValidationError("code is required")
    status = RecallBotController.register_user_oauth_credential(code)

    match status:
        case ZoomStatus.FAILURE:
            integrationStatus = "false"
        case ZoomStatus.SUCCESS:
            integrationStatus = "true"
        case ZoomStatus.RETRY_REQUIRED:
            integrationStatus = "retryRequired"
    params = {"integration": "Zoom", "integrationStatus": integrationStatus}
    # This is a bit simplistic, but ultimately, whatever happens, we don't have
    # a better place to direct the user. Even in the case where the integration
    # fails, the easiest thing to do is return them to settings to try again.
    return redirect(f"/settings?{urlencode(params)}")
