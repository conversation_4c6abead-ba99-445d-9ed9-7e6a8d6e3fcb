from django import forms
from django.contrib.auth import forms as admin_forms
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

User = get_user_model()


class UserAdminChangeForm(admin_forms.UserChangeForm):  # type: ignore[type-arg]
    def clean_pin(self) -> str | None:
        """
        Validate the PIN field to ensure it is numeric
        """
        pin = self.cleaned_data.get("pin")
        if not pin:
            return pin
        if not isinstance(pin, str):
            raise forms.ValidationError(_("PIN must be a string."))
        if not pin.isdigit():
            raise forms.ValidationError(_("PIN must be numeric."))
        return pin

    class Meta(admin_forms.UserChangeForm.Meta):  # type: ignore[misc, name-defined]
        model = User


class UserAdminCreationForm(admin_forms.UserCreationForm):  # type: ignore[type-arg]
    """
    Form for User Creation in the Admin Area.
    To change user signup, see UserSignupForm and UserSocialSignupForm.
    """

    class Meta(admin_forms.UserCreationForm.Meta):  # type: ignore[misc, name-defined]
        model = User
        error_messages = {
            "username": {"unique": _("This username has already been taken.")},
        }
