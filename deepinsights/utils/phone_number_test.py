import phonenumbers
import pydantic
import pytest

from deepinsights.utils.phone_number import PhoneNumberE164OrNone


class Model(pydantic.BaseModel):
    phone: PhoneNumberE164OrNone


@pytest.mark.parametrize(
    "phone_number, expected",
    [
        # Valid
        (phonenumbers.parse("+14155552671", "US"), "+14155552671"),
        (phonenumbers.parse("4155552671", "US"), "+14155552671"),
        (phonenumbers.parse("+442079461111"), "+442079461111"),
        ("+14155552671", "+14155552671"),
        ("4155552671", "+14155552671"),
        ("+442079461111", "+442079461111"),
        # Invalid
        (phonenumbers.parse("123", "US"), None),
        ("123", None),
        ("invalid", None),
        ("", None),
        (None, None),
    ],
)
def test_phone_number_field(phone_number: str | phonenumbers.PhoneNumber | None, expected: str | None) -> None:
    assert Model(phone=phone_number).phone == expected
