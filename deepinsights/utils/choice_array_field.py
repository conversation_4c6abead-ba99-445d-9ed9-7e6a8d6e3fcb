from typing import Any

from django import forms
from django.contrib.postgres.fields import ArrayField


class ChoiceArrayField(ArrayField):  # type: ignore[type-arg]
    """An ArrayField with a predefined list of choices (inferred from the base field).

    Can be used with a field that has a `choices` attribute, such as `<PERSON><PERSON><PERSON><PERSON>` or `Integer<PERSON>ield`.
    The admin console will display a multi selector for the choices.
    """

    def formfield(self, form_class: Any = None, choices_form_class: Any = None, **kwargs: Any) -> Any:
        defaults = {
            "form_class": forms.TypedMultipleChoiceField,
            "choices": self.base_field.choices,
            "coerce": self.base_field.to_python,
            "widget": forms.CheckboxSelectMultiple,
        }
        defaults.update(kwargs)

        return super(<PERSON><PERSON>y<PERSON><PERSON>, self).formfield(**defaults)  # type: ignore[arg-type]
