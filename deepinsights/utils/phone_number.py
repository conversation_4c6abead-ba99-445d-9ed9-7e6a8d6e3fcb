# Wraps the phone number validator to catch exceptions and return None for invalid phone numbers.
#
# Note that this is meant to be used only to wrap another validator (specifically,
# PhoneNumberValidator). It does not validate anything itself, leaving the actual validation work to
# the underying validator(s) that it wraps.
import logging
from typing import Annotated, Callable

import phonenumbers
import pydantic
from pydantic_extra_types.phone_numbers import PhoneNumberValidator


def _validate_phone_number(phone_number: str | None, handler: Callable[[str | None], str | None]) -> str | None:
    if not phone_number:
        return None
    try:
        return handler(phone_number)
    except Exception:
        logging.warning("Phone number validation failed, returning None")
        return None


# A phone number that has been validated and normalized to E.164 format, or None if the phone number
# is missing (or an invalid phone number was provided).
PhoneNumberE164OrNone = Annotated[
    str | None,
    PhoneNumberValidator(
        default_region="US",
        number_format=phonenumbers.PhoneNumberFormat.to_string(phonenumbers.PhoneNumberFormat.E164),
    ),
    pydantic.WrapValidator(_validate_phone_number),
]
