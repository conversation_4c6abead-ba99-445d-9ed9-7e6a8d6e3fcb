import datetime
from typing import Any
from unittest.mock import MagicMock, patch

import pytest

from deepinsights.core.ml.process_transcript import _combine_utts_by_speakers, process_transcript
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


@pytest.fixture
def deepgram_raw_asr_response() -> dict[str, Any]:
    return {
        "metadata": {"duration": 90},
        "results": {
            "utterances": [
                {"speaker": "0", "start": 0, "end": 30, "transcript": "Hello"},
                {"speaker": "1", "start": 31, "end": 60, "transcript": "Hi"},
                {"speaker": "0", "start": 61, "end": 90, "transcript": "How are you?"},
            ]
        },
    }


@pytest.fixture
def recall_raw_asr_response() -> list[dict[str, Any]]:
    return [
        {"speaker": "First speaker", "start": 0, "end": 30, "transcript": "Hello"},
        {"speaker": "Second speaker", "start": 31, "end": 60, "transcript": "Hi"},
        {"speaker": "First speaker", "start": 61, "end": 90, "transcript": "How are you?"},
    ]


@pytest.fixture
def test_user(django_user_model: User) -> User:
    return django_user_model.objects.create(username="test_user")


@pytest.fixture
def note_with_attendees(test_user: User) -> Note:
    note: Note = Note.objects.create(
        note_owner=test_user,
        note_type="voice_memo",
        status="uploaded",
        metadata={
            "meeting_name": "Meeting name",
        },
    )
    Attendee.objects.create(note=note, attendee_name="Attendee 1")
    Attendee.objects.create(note=note, attendee_name="Attendee 2")
    return note


@pytest.fixture
def debrief_note_with_attendees(note_with_attendees: Note) -> Note:
    note_with_attendees.meeting_type = MeetingType.objects.get(key="debrief")
    note_with_attendees.save()
    return note_with_attendees


@patch("deepinsights.core.ml.process_transcript._get_speaker_mapping")
def test_process_transcript_success_deepgram(
    mock_get_speaker_mapping: MagicMock,
    note_with_attendees: Note,
    test_user: User,
    deepgram_raw_asr_response: dict[str, Any],
) -> None:
    mock_get_speaker_mapping.return_value = {"speaker0": "mapping0", "speaker1": "mapping1"}
    result = process_transcript(note_with_attendees, test_user, deepgram_raw_asr_response, "deepgram")

    assert result == {
        "meeting_duration": 90,
        "raw_transcript": [
            {
                "speaker": "speaker0",
                "start": datetime.timedelta(seconds=0),
                "end": datetime.timedelta(seconds=30),
                "utt": "Hello",
            },
            {
                "speaker": "speaker1",
                "start": datetime.timedelta(seconds=31),
                "end": datetime.timedelta(seconds=60),
                "utt": "Hi",
            },
            {
                "speaker": "speaker0",
                "start": datetime.timedelta(seconds=61),
                "end": datetime.timedelta(seconds=90),
                "utt": "How are you?",
            },
        ],
        "transcript": (
            'speaker0\t"0:00:00-->0:00:30\nHello\n\n'
            'speaker1\t"0:00:31-->0:01:00\nHi\n\n'
            'speaker0\t"0:01:01-->0:01:30\nHow are you?'
        ),
        "keywords": [],
        "speaker_percentage": {"speaker0": 67.0, "speaker1": 33.0},
        "speaker_mapping": {"speaker0": "mapping0", "speaker1": "mapping1"},
    }

    assert result["meeting_duration"] == 90
    assert result["transcript"] == (
        'speaker0\t"0:00:00-->0:00:30\nHello\n\n'
        'speaker1\t"0:00:31-->0:01:00\nHi\n\n'
        'speaker0\t"0:01:01-->0:01:30\nHow are you?'
    )
    assert result["keywords"] == []
    assert result["speaker_percentage"] == {"speaker0": 67.0, "speaker1": 33.0}
    assert result["speaker_mapping"] == {"speaker0": "mapping0", "speaker1": "mapping1"}

    mock_get_speaker_mapping.assert_called_once_with(result["transcript"], "Attendee 1, Attendee 2", "", "client")


@patch("deepinsights.core.ml.process_transcript._get_speaker_mapping")
def test_process_transcript_success_deepgram_debrief_meeting(
    mock_get_speaker_mapping: MagicMock,
    debrief_note_with_attendees: Note,
    test_user: User,
    deepgram_raw_asr_response: dict[str, Any],
) -> None:
    mock_get_speaker_mapping.return_value = {"speaker0": "mapping0", "speaker1": "mapping1"}
    result = process_transcript(debrief_note_with_attendees, test_user, deepgram_raw_asr_response, "deepgram")

    assert result == {
        "meeting_duration": 90,
        "raw_transcript": [
            {
                "speaker": "speaker0",
                "start": datetime.timedelta(seconds=0),
                "end": datetime.timedelta(seconds=30),
                "utt": "Hello",
            },
            {
                "speaker": "speaker1",
                "start": datetime.timedelta(seconds=31),
                "end": datetime.timedelta(seconds=60),
                "utt": "Hi",
            },
            {
                "speaker": "speaker0",
                "start": datetime.timedelta(seconds=61),
                "end": datetime.timedelta(seconds=90),
                "utt": "How are you?",
            },
        ],
        "transcript": (
            'speaker0\t"0:00:00-->0:00:30\nHello\n\n'
            'speaker1\t"0:00:31-->0:01:00\nHi\n\n'
            'speaker0\t"0:01:01-->0:01:30\nHow are you?'
        ),
        "keywords": [],
        "speaker_percentage": {},
        "speaker_mapping": None,
    }

    mock_get_speaker_mapping.assert_not_called()


@patch("deepinsights.core.ml.process_transcript._get_speaker_mapping")
def test_process_transcript_no_processed_data(
    mock_get_speaker_mapping: MagicMock,
    note_with_attendees: Note,
    test_user: User,
    deepgram_raw_asr_response: dict[str, Any],
) -> None:
    deepgram_raw_asr_response["results"]["utterances"] = []

    result = process_transcript(note_with_attendees, test_user, deepgram_raw_asr_response, "deepgram")

    assert result == {
        "meeting_duration": 0,
        "transcript": "",
        "keywords": [],
        "speaker_percentage": {},
        "speaker_mapping": None,
    }

    mock_get_speaker_mapping.assert_not_called()


@patch("deepinsights.core.ml.process_transcript._get_speaker_mapping")
def test_process_transcript_success_deepgram_speaker_mapping_failure(
    mock_get_speaker_mapping: MagicMock,
    note_with_attendees: Note,
    test_user: User,
    deepgram_raw_asr_response: dict[str, Any],
) -> None:
    mock_get_speaker_mapping.return_value = None
    result = process_transcript(note_with_attendees, test_user, deepgram_raw_asr_response, "deepgram")

    assert result == {
        "meeting_duration": 90,
        "raw_transcript": [
            {
                "speaker": "speaker0",
                "start": datetime.timedelta(seconds=0),
                "end": datetime.timedelta(seconds=30),
                "utt": "Hello",
            },
            {
                "speaker": "speaker1",
                "start": datetime.timedelta(seconds=31),
                "end": datetime.timedelta(seconds=60),
                "utt": "Hi",
            },
            {
                "speaker": "speaker0",
                "start": datetime.timedelta(seconds=61),
                "end": datetime.timedelta(seconds=90),
                "utt": "How are you?",
            },
        ],
        "transcript": (
            'speaker0\t"0:00:00-->0:00:30\nHello\n\n'
            'speaker1\t"0:00:31-->0:01:00\nHi\n\n'
            'speaker0\t"0:01:01-->0:01:30\nHow are you?'
        ),
        "keywords": [],
        "speaker_percentage": {"speaker0": 67.0, "speaker1": 33.0},
        "speaker_mapping": {"speaker0": "speaker0", "speaker1": "speaker1"},
    }

    mock_get_speaker_mapping.assert_called_once_with(result["transcript"], "Attendee 1, Attendee 2", "", "client")


@patch("deepinsights.core.ml.process_transcript._get_speaker_mapping")
def test_process_transcript_recall_no_processed_data(
    mock_get_speaker_mapping: MagicMock,
    note_with_attendees: Note,
    test_user: User,
) -> None:
    result = process_transcript(note_with_attendees, test_user, [], "recall")

    assert result == {
        "meeting_duration": 0,
        "transcript": "",
        "keywords": [],
        "speaker_percentage": {},
        "speaker_mapping": None,
    }

    mock_get_speaker_mapping.assert_not_called()


@patch("deepinsights.core.ml.process_transcript._get_speaker_mapping")
def test_process_transcript_success_recall(
    mock_get_speaker_mapping: MagicMock,
    note_with_attendees: Note,
    test_user: User,
    recall_raw_asr_response: dict[str, Any],
) -> None:
    result = process_transcript(note_with_attendees, test_user, recall_raw_asr_response, "recall")

    assert result == {
        "meeting_duration": 90,
        "raw_transcript": [
            {
                "speaker": "First speaker",
                "start": datetime.timedelta(seconds=0),
                "end": datetime.timedelta(seconds=30),
                "utt": "Hello",
            },
            {
                "speaker": "Second speaker",
                "start": datetime.timedelta(seconds=31),
                "end": datetime.timedelta(seconds=60),
                "utt": "Hi",
            },
            {
                "speaker": "First speaker",
                "start": datetime.timedelta(seconds=61),
                "end": datetime.timedelta(seconds=90),
                "utt": "How are you?",
            },
        ],
        "transcript": (
            'First speaker\t"0:00:00-->0:00:30\nHello\n\n'
            'Second speaker\t"0:00:31-->0:01:00\nHi\n\n'
            'First speaker\t"0:01:01-->0:01:30\nHow are you?'
        ),
        "keywords": [],
        "speaker_percentage": {"First speaker": 67.0, "Second speaker": 33.0},
        "speaker_mapping": {"First speaker": "First speaker", "Second speaker": "Second speaker"},
    }

    mock_get_speaker_mapping.assert_not_called()


@patch("deepinsights.core.ml.process_transcript._get_speaker_mapping")
def test_process_transcript_success_deepgram_with_recall_asr_response(
    mock_get_speaker_mapping: MagicMock,
    note_with_attendees: Note,
    test_user: User,
    recall_raw_asr_response: dict[str, Any],
) -> None:
    mock_get_speaker_mapping.return_value = {"First speaker": "mapping0", "Second speaker": "mapping1"}
    result = process_transcript(note_with_attendees, test_user, recall_raw_asr_response, "deepgram")

    assert result == {
        "meeting_duration": 90,
        "raw_transcript": [
            {
                "speaker": "First speaker",
                "start": datetime.timedelta(seconds=0),
                "end": datetime.timedelta(seconds=30),
                "utt": "Hello",
            },
            {
                "speaker": "Second speaker",
                "start": datetime.timedelta(seconds=31),
                "end": datetime.timedelta(seconds=60),
                "utt": "Hi",
            },
            {
                "speaker": "First speaker",
                "start": datetime.timedelta(seconds=61),
                "end": datetime.timedelta(seconds=90),
                "utt": "How are you?",
            },
        ],
        "transcript": (
            'First speaker\t"0:00:00-->0:00:30\nHello\n\n'
            'Second speaker\t"0:00:31-->0:01:00\nHi\n\n'
            'First speaker\t"0:01:01-->0:01:30\nHow are you?'
        ),
        "keywords": [],
        "speaker_percentage": {"First speaker": 67.0, "Second speaker": 33.0},
        "speaker_mapping": {"First speaker": "mapping0", "Second speaker": "mapping1"},
    }

    mock_get_speaker_mapping.assert_called_once_with(result["transcript"], "Attendee 1, Attendee 2", "", "client")


def test_empty_utterances() -> None:
    result = _combine_utts_by_speakers([])
    assert result is None


# Test handling of null speakers with forward assignment
def test_null_speakers_forward_assignment() -> None:
    utterances: list[dict[str, Any]] = [
        {"speaker": None, "transcript": "Hi,", "start": 6.29, "end": -1},
        {"speaker": None, "transcript": "Adam.", "start": 6.69, "end": -1},
        {"speaker": "Bob Matt", "transcript": "Thank you", "start": 7.25, "end": 7.57},
    ]

    result = _combine_utts_by_speakers(utterances)
    assert result is not None
    assert len(result) == 1
    assert result[0]["speaker"] == "Bob Matt"
    assert result[0]["utt"] == "Hi, Adam. Thank you"
    assert result[0]["start"] == datetime.timedelta(seconds=6.29)
    assert result[0]["end"] == datetime.timedelta(seconds=7.57)


#  Test handling of invalid end times (-1)
def test_invalid_end_times() -> None:
    utterances = [
        {"speaker": "Speaker1", "transcript": "First", "start": 1.0, "end": -1},
        {"speaker": "Speaker1", "transcript": "Second", "start": 2.0, "end": 3.0},
        {"speaker": "Speaker1", "transcript": "Third", "start": 4.0, "end": -1},
    ]

    result = _combine_utts_by_speakers(utterances)
    assert result is not None
    assert len(result) == 1
    assert result[0]["start"] == datetime.timedelta(seconds=1.0)
    # Last utterance's end time should be start time + 0.5 seconds
    assert result[0]["end"] == datetime.timedelta(seconds=4.5)


# Test handling of mixed speakers with null values.
def test_mixed_speakers_with_nulls() -> None:
    utterances: list[dict[str, Any]] = [
        {"speaker": None, "transcript": "Start", "start": 1.0, "end": 2.0},
        {"speaker": "Speaker1", "transcript": "Middle", "start": 2.0, "end": 3.0},
        {"speaker": None, "transcript": "End", "start": 3.0, "end": 4.0},
    ]

    result = _combine_utts_by_speakers(utterances)
    assert result is not None
    assert len(result) == 1
    assert result[0]["speaker"] == "Speaker1"
    assert result[0]["utt"] == "Start Middle End"


# Test handling of multiple speakers with invalid end times.
def test_multiple_speakers_with_invalid_times() -> None:
    utterances = [
        {"speaker": "Speaker1", "transcript": "Hello", "start": 1.0, "end": -1},
        {"speaker": "Speaker2", "transcript": "Hi", "start": 2.0, "end": -1},
        {"speaker": "Speaker1", "transcript": "Goodbye", "start": 3.0, "end": 4.0},
    ]

    result = _combine_utts_by_speakers(utterances)
    assert result is not None
    assert len(result) == 3
    # First speaker's end time should be start time of next utterance
    assert result[0]["end"] == datetime.timedelta(seconds=2.0)
    # Second speaker's end time should be start time of next utterance
    assert result[1]["end"] == datetime.timedelta(seconds=3.0)


def test_speaker_prefix() -> None:
    """Test speaker prefix functionality."""
    utterances = [{"speaker": "Speaker1", "transcript": "Hello", "start": 1.0, "end": 2.0}]

    result = _combine_utts_by_speakers(utterances, speaker_prefix="TEST_")
    assert result is not None
    assert result[0]["speaker"] == "TEST_Speaker1"


# Test handling of a single utterance with invalid end time.
def test_single_utterance_invalid_end() -> None:
    utterances = [{"speaker": "Speaker1", "transcript": "Hello", "start": 1.0, "end": -1}]

    result = _combine_utts_by_speakers(utterances)
    assert result is not None
    assert len(result) == 1
    assert result[0]["end"] == datetime.timedelta(seconds=1.5)  # start + 0.5
