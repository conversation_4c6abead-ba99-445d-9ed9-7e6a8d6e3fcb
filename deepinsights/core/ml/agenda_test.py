import datetime
from unittest.mock import MagicMock, patch

from deepinsights.core.integrations.crm.crm_models import CRMNote
from deepinsights.core.ml.agenda import generate_filled_agenda
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.prompt import Prompt
from deepinsights.users.models.user import User


@patch("deepinsights.core.ml.agenda.call_model")
@patch("deepinsights.core.ml.agenda.get_crm_interface")
def test_generate_filled_agenda(
    mock_get_crm_interface: MagicMock, mock_call_model: MagicMock, django_user_model: User
) -> None:
    org = Organization.objects.create(name="test_org")
    user = django_user_model.objects.create(username="test_user", organization=org)
    client = Client.objects.create(organization=org, name="test_client")

    Note.objects.create(client={"uuid": "other"})
    client_note_one = Note.objects.create(
        client={"uuid": str(client.uuid)}, summary={"sections": [{"topic": "test", "bullets": ["test"]}]}
    )
    client_note_two = Note.objects.create(
        client={"uuid": str(client.uuid)}, summary={"sections": [{"topic": "test two", "bullets": ["test two"]}]}
    )

    crm_handler = MagicMock()
    crm_handler.fetch_notes_for_client.return_value = [
        CRMNote(
            crm_id="test_id",
            crm_system="test_system",
            created_at=datetime.datetime(2022, 1, 1, 12, 0, 0),
            content="test summary",
        ),
        # Empty date
        CRMNote(
            crm_id="test_id_two",
            crm_system="test_system",
            created_at=None,
            content="test summary two",
        ),
    ]
    crm_handler.get_client_basic_info.return_value = {"test": "info"}
    mock_get_crm_interface.return_value = crm_handler
    mock_call_model.return_value = "response"

    result = generate_filled_agenda(user, client, "agenda")

    prompt_text = Prompt.objects.get(unique_name="meeting_agenda_generator").user_prompt
    assert prompt_text
    first_client_note_history = "\nMeeting Note (from test_system, id: test_id)\nDate: 2022-01-01\n\ntest summary\n\n"
    second_client_note_history = "\nMeeting Note (from test_system, id: test_id_two)\n\ntest summary two\n\n"
    older_zeplyn_note_history = f"\nMeeting Note (from Zeplyn, id: {str(client_note_one.uuid)})\nDate: {client_note_one.created.strftime('%Y-%m-%d')}\n\ntest:\n- test"
    newer_zeplyn_note_history = f"\nMeeting Note (from Zeplyn, id: {str(client_note_two.uuid)})\nDate: {client_note_two.created.strftime('%Y-%m-%d')}\n\ntest two:\n- test two\n\n"
    expected_prompt = prompt_text.format(
        client_history=f"{first_client_note_history}{second_client_note_history}{newer_zeplyn_note_history}{older_zeplyn_note_history}",
        household_info={"test": "info"},
        agenda_template="agenda",
    )

    assert result["content"] == "response"
    assert result["format"] == "markdown"
    mock_call_model.assert_called_once_with(expected_prompt)


@patch("deepinsights.core.ml.agenda.call_model")
@patch("deepinsights.core.ml.agenda.get_crm_interface")
def test_generate_filled_agenda_no_info(
    mock_get_crm_interface: MagicMock, mock_call_model: MagicMock, django_user_model: User
) -> None:
    org = Organization.objects.create(name="test_org")
    user = django_user_model.objects.create(username="test_user", organization=org)
    client = Client.objects.create(organization=org, name="test_client")
    Note.objects.create(client={"uuid": "other"})

    crm_handler = MagicMock()
    crm_handler.fetch_notes_for_client.return_value = []
    crm_handler.get_client_basic_info.return_value = {}
    mock_get_crm_interface.return_value = crm_handler

    result = generate_filled_agenda(user, client, "agenda")

    assert result == {"content": "agenda", "format": "markdown"}

    mock_call_model.assert_not_called()


@patch("deepinsights.core.ml.agenda.call_model")
@patch("deepinsights.core.ml.agenda.get_crm_interface")
def test_generate_filled_agenda_empty_prompt(
    mock_get_crm_interface: MagicMock, mock_call_model: MagicMock, django_user_model: User
) -> None:
    org = Organization.objects.create(name="test_org")
    user = django_user_model.objects.create(username="test_user", organization=org)
    client = Client.objects.create(organization=org, name="test_client")
    Note.objects.create(client={"uuid": "other"})

    crm_handler = MagicMock()
    crm_handler.fetch_notes_for_client.return_value = []
    crm_handler.get_client_basic_info.return_value = {"test": "info"}
    mock_get_crm_interface.return_value = crm_handler

    prompt = Prompt.objects.get(unique_name="meeting_agenda_generator")
    prompt.user_prompt = ""
    prompt.save()

    result = generate_filled_agenda(user, client, "agenda")

    assert result == {"content": "agenda", "format": "markdown"}

    mock_call_model.assert_not_called()
