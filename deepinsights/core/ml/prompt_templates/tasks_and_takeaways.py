tasks_takeaways_client_meeting = """
You are a highly skilled AI trained in language comprehension, summarization, analyzing conversations, and extracting action items.
Please read the meeting transcript and output four sections as specified below.

[Instructions]
1. Key Takeaways from the meeting:
   - Summarize the discussion, capturing all details including names, proper nouns, numbers, takeaways, and financial recommendations or advice.
   - Exclude pleasantries or opening/closing remarks.
   - Provide relevant output even if the meeting is long or deviates from financial topics.
   - Retain the most important points, providing a coherent and readable summary.
   - Include all quantitative data and financial details: savings, salary, expenses, cashflows, investments, sources of income, current portfolio, asset allocation, returns, liabilities, retirement funds, etc.
   - Avoid unnecessary details or tangential points not important to a Financial Advisor.
   - Capture every number, especially asset allocation and returns if mentioned.

2. Action Items:
   - Identify tasks, assignments, or actions agreed upon or mentioned as needing to be done.
   - Mention the assignee if specified in the transcript.
   - Include tasks for both the Advisor and their client, or general actions the group decided to take.
   - Include day/date if mentioned in the transcript.
   - If there are no action items, don't output anything for this section.

3. Advisor Notes:
   - Include notes on the client to help build a good relationship.
   - Cover: relationships (with names if mentioned), family-related discussion, major life events, financial goals, investment style, client type, risk appetite, sensitive topics, motivations, and concerns.
   - Stick to facts without making inferences.

4. Keywords:
   - List the major topics or themes discussed in the meeting.
   - Do not include attendee names.
   - Output no more than 8 keywords.

[Output Format]
Provide output in the following JSON format:
{{
"key_takeaways": [
    "String describing a key takeaway",
    "Another key takeaway"
],
"action_items": [
    {{
    "description": "Description of the action item",
    "assignee": "Person explicitly assigned in the transcript, or 'Unassigned' if not specified",
    "due_date": "Due date if explicitly mentioned, or 'Not specified'"
    }}
],
"advisor_notes": [
    "Note about client relationships, goals, or preferences",
    "Another relevant note for the advisor"
],
"keywords": [
    "keyword1",
    "keyword2"
]
}}

[Examples]
1. Key Takeaways Example:
"key_takeaways": [
    "Client's current portfolio consists of 60% stocks, 30% bonds, and 10% cash",
    "Annual expenses are approximately $80,000, with $30,000 allocated to mortgage payments",
    "Client expressed interest in increasing international exposure, particularly in emerging markets",
    "Retirement goal: $2 million by age 65, currently on track with $800,000 saved at age 45"
]

2. Action Items Example:
"action_items": [
    {{
        "description": "Research ETFs for emerging markets exposure, aiming for 5-10% allocation",
        "assignee": "Advisor",
        "due_date": "Next meeting"
    }},
    {{
        "description": "Provide detailed breakdown of current international investments",
        "assignee": "Advisor",
        "due_date": "End of month"
    }},
    {{
        "description": "Review and potentially increase life insurance coverage to $1.5 million",
        "assignee": "Client",
        "due_date": "Within 3 months"
    }},
    {{
        "description": "Future Consideration: Explore options for long-term care insurance",
        "assignee": "Unassigned",
        "due_date": "Not specified"
    }}
]

3. Advisor Notes Example:
"advisor_notes": [
    "Client recently celebrated 20th wedding anniversary, has two children (ages 15 and 12)",
    "Expressed concern about potential job instability in tech industry",
    "Risk tolerance has decreased since last meeting, now preferring moderate-risk investments",
    "Interested in socially responsible investing, particularly in renewable energy sector",
    "Planning for children's college education is a top priority"
]

4. Keywords Example:
"keywords": [
    "Portfolio rebalancing",
    "International investments",
    "Retirement planning",
    "Risk management",
    "College savings",
    "Life insurance",
    "Socially responsible investing",
    "Tax optimization"
]

{user_context}
Following is a transcript of a meeting between Financial Advisor(s) and their client(s). The meeting attendee(s) were: {attendees}.
The meeting occurred on {date_str}.
<transcript>
{transcript}
</transcript>

Please analyze the transcript and provide the output in the specified JSON format.
"""

tasks_takeaways_client_meeting_detailed = """
You are an AI trained in language comprehension, summarization, conversation analysis, and action item extraction. Provide
the output following instructions, and output in the specified JSON format. Ensure the output is accurate.

[Instructions]
[Action Items]
Include ALL tasks, assignments, or actions mentioned or implied in the transcript, including
long-term planning items and considerations for future projects. Be thorough and err on the side of inclusion.
For each action item:
- Describe the task accurately based on the transcript, including details such as numbers, names, dates and specifics.
- Assign to a person ONLY if explicitly mentioned in the transcript. Use 'Unassigned' if not specified.
- Include due dates ONLY if explicitly stated. Use 'Not specified' if no date is mentioned.
- If the action item includes setting up meetings with, sending emails to, or any other task that requires
    a follow up with someone else, mention the recipient name as well.
- If an action item points to a software, specific document, or any tools or resources, explicitly mention it.
- Include items related to future planning, research, or considerations, even if they're not immediate actions.
- Capture discussions about potential projects or changes, marking them as 'Future Consideration' if appropriate.
- DO NOT infer, assume, or generate any information not directly stated in the transcript.
- If in doubt, use 'Unassigned' for assignee and 'Not specified' for due date.
- Include all action items, even if they are not directly related to financial planning.

[Key Takeaways]
   - Summarize the discussion, capturing all details including names, proper nouns, numbers, takeaways, and financial recommendations or advice.
   - Exclude pleasantries or opening/closing remarks.
   - Provide relevant output even if the meeting is long or deviates from financial topics.
   - Retain the most important points, providing a coherent and readable summary.
   - Include all quantitative data and financial details: savings, salary, expenses, cashflows, investments, sources of income, current portfolio, asset allocation, returns, liabilities, retirement funds, etc.
   - Avoid unnecessary details or tangential points not important to a Financial Advisor.
   - Capture every number, especially asset allocation and returns if mentioned.
   - Do not repeat action items in key takeaways. Key takeaways are about man discussion points.
   - Since we already write tasks and action items, we don't need to repeat them in key takeaways.

[Advisor Notes]
Include notes on client relationships, family, life events, financial goals, investment style,
risk appetite, concerns, and motivations. Include sentiment around client's comments. Stick to facts without inference.

[Keywords]
List up to 8 major topics or themes discussed. Exclude attendee names.

[Output Format]
Provide output in the following JSON format:
{{
"action_items": [
    {{
    "description": "Description of the action item",
    "assignee": "Person explicitly assigned in the transcript, or 'Unassigned' if not specified",
    "due_date": "Due date if explicitly mentioned, or 'Not specified'"
    }}
],
"key_takeaways": [
    "String describing a key takeaway",
    "Another key takeaway"
],
"advisor_notes": [
    "Note about client relationships, goals, or preferences",
    "Another relevant note for the advisor"
],
"keywords": [
    "keyword1",
    "keyword2"
]
}}

[Examples]
Here are examples for each section of the output:
1. Action Items Example:
"action_items": [
    {{
        "description": "Research ETFs for emerging markets exposure, aiming for 5-10% allocation",
        "assignee": "Advisor",
        "due_date": "Next meeting"
    }},
    {{
        "description": "Provide detailed breakdown of current international investments",
        "assignee": "Advisor",
        "due_date": "End of month"
    }},
    {{
        "description": "Review and potentially increase life insurance coverage to $1.5 million",
        "assignee": "Client",
        "due_date": "Within 3 months"
    }},
    {{
        "description": "Future Consideration: Explore options for long-term care insurance",
        "assignee": "Unassigned",
        "due_date": "Not specified"
    }}
]
2. Key Takeaways Example:
"key_takeaways": [
    "Client's current portfolio consists of 60% stocks, 30% bonds, and 10% cash",
    "Annual expenses are approximately $80,000, with $30,000 allocated to mortgage payments",
    "Client expressed interest in increasing international exposure, particularly in emerging markets",
    "Retirement goal: $2 million by age 65, currently on track with $800,000 saved at age 45"
]

3. Advisor Notes Example:
"advisor_notes": [
    "Client recently celebrated 20th wedding anniversary, has two children (ages 15 and 12)",
    "Expressed concern about potential job instability in tech industry",
    "Risk tolerance has decreased since last meeting, now preferring moderate-risk investments",
    "Interested in socially responsible investing, particularly in renewable energy sector",
    "Planning for children's college education is a top priority"
]

4. Keywords Example:
"keywords": [
    "Portfolio rebalancing",
    "International investments",
    "Retirement planning",
    "Risk management",
    "College savings",
    "Life insurance",
    "Socially responsible investing",
    "Tax optimization"
]

5. Missing Action Items Example:
"missing_action_items": [
    "Schedule a follow-up meeting to discuss long-term care insurance options",
    "Provide information on 529 college savings plans for children's education"
]

Ensure your response is in valid JSON format with properly escaped strings. Do not add any information or details
that are not explicitly stated in the transcript. If unsure about any detail, err on the side of less specificity.
After you are done generating the required data, take a final look at it, and if there are any missing points in action items,
output in a section called missing action items as part of the json.

{user_context}
The following is a transcript of a meeting between Financial Advisor(s) and their client(s). {attendees}.
The meeting occurred on {date_str}.

<transcript>{transcript}</transcript>.
"""

task_and_takeaways_internal_meeting = """
You are a highly skilled AI trained in language comprehension, summarization, analyzing conversations,
and extracting action items.
Please read the meeting transcript and output three sections:
1. Key Takeaways from the meeting: This should be a summary of the discussion capturing all details including names,
proper nouns, numbers, takeaways, and decisions. The summary should not contain pleasantries or opening/closing remarks of the meeting.
Please retain the most important points, providing a coherent and readable summary that could help the attendees understand the
main points of the discussion along with all the quantitative data and every financial detail.
Please avoid unnecessary details or tangential points.
2. Action Items: Please review the text and identify any tasks, assignments, or actions that were agreed upon or
mentioned as needing to be done. Please mention the assignee, if they were mentioned in the transcript.
These could be tasks assigned to either an individual, or these can be general actions that the group has decided to take.
The action items should have a day/date if it were mentioned in the transcript.
If there are no action items, don't output anything. Please list these action items clearly and concisely.
3. Keywords: These are the major topics or themes discussed in the meeting and do not include attendee names.
Please output no more than 8 keywords in bullets.

[Output Format]
Provide output in the following JSON format:
{{
"action_items": [
    {{
    "description": "Description of the action item",
    "assignee": "Person explicitly assigned in the transcript, or 'Unassigned' if not specified",
    "due_date": "Due date if explicitly mentioned, or 'Not specified'"
    }}
],
"key_takeaways": [
    "String describing a key takeaway",
    "Another key takeaway"
],

"keywords": [
    "keyword1",
    "keyword2"
]
}}

[Examples]
Here are examples for each section of the output:
1. Action Items Example:
"action_items": [
    {{
        "description": "Research ETFs for emerging markets exposure, aiming for 5-10 allocation",
        "assignee": "Advisor",
        "due_date": "Next meeting"
    }},
    {{
        "description": "Provide detailed breakdown of current international investments",
        "assignee": "Advisor",
        "due_date": "End of month"
    }},
    {{
        "description": "Review and potentially increase life insurance coverage to $1.5 million",
        "assignee": "Client",
        "due_date": "Within 3 months"
    }},
    {{
        "description": "Future Consideration: Explore options for long-term care insurance",
        "assignee": "Unassigned",
        "due_date": "Not specified"
    }}
]
2. Key Takeaways Example:
"key_takeaways": [
    "Annual expenses are approximately $80,000, with $30,000 allocated to mortgage payments",
    "Client expressed interest in increasing international exposure, particularly in emerging markets",
    "Retirement goal: $2 million by age 65, currently on track with $800,000 saved at age 45"
]

3. Keywords Example:
"keywords": [
    "Portfolio rebalancing",
    "International investments",
    "Retirement planning",
    "Risk management",
    "College savings",
    "Life insurance",
    "Socially responsible investing",
    "Tax optimization"
]

{user_context}
The following is a transcript of an internal meeting. The meeting attendee(s) were: {attendees}.
The meeting occurred on {date_str}.

<transcript>{transcript}</transcript>
"""
