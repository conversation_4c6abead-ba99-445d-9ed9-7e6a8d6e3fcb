import datetime
import logging
from typing import Any, Dict, List, Optional, Tuple, cast

import openai
from django.conf import settings

from deepinsights.core.ml.voice_memo_utils import parse_response_into_json
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


def _convert_utterances_to_transcript(full_utterances: List[Dict[str, Any]]) -> str:
    """Convert utterances to a formatted transcript."""
    logging.debug("utterance %s", full_utterances)
    transcript = ""
    for utterance in full_utterances:
        logging.debug("utterance %s", utterance)
        transcript = (
            f"{transcript}"
            f'{str(utterance["speaker"])}\t'
            f'"{str(utterance["start"])}-->{str(utterance["end"])}\n'
            f'{utterance["utt"]}\n\n'
        )
    logging.debug("Converted transcript: %s", transcript)
    return transcript.strip()


def _combine_utts_by_speakers(
    utterances: list[dict[str, Any]], speaker_prefix: str = ""
) -> list[dict[str, str | datetime.timedelta]] | None:
    """Combine utterances by speakers with handling for null speakers and invalid end times."""
    if not utterances:
        return None

    # First pass: Fix null speakers
    fixed_speakers = utterances.copy()

    # Forward pass to assign speakers
    last_valid_speaker = None
    for i in range(len(fixed_speakers)):
        if fixed_speakers[i]["speaker"] is not None:
            last_valid_speaker = fixed_speakers[i]["speaker"]
        elif last_valid_speaker is None:
            # Look ahead for the next valid speaker
            for j in range(i + 1, len(fixed_speakers)):
                if fixed_speakers[j]["speaker"] is not None:
                    fixed_speakers[i]["speaker"] = fixed_speakers[j]["speaker"]
                    break
        else:
            fixed_speakers[i]["speaker"] = last_valid_speaker

    # Backward pass for any remaining null speakers
    last_valid_speaker = None
    for i in range(len(fixed_speakers) - 1, -1, -1):
        if fixed_speakers[i]["speaker"] is not None:
            last_valid_speaker = fixed_speakers[i]["speaker"]
        elif last_valid_speaker is not None:
            fixed_speakers[i]["speaker"] = last_valid_speaker

    # Second pass: Fix end times and combine utterances
    current_speaker = fixed_speakers[0]["speaker"]
    speaker_name = speaker_prefix + str(current_speaker)
    utt = fixed_speakers[0]["transcript"]
    start = datetime.timedelta(seconds=fixed_speakers[0]["start"])

    # Handle end time for first utterance
    if fixed_speakers[0]["end"] == -1:
        if len(fixed_speakers) > 1:
            end = datetime.timedelta(seconds=fixed_speakers[1]["start"])
        else:
            end = datetime.timedelta(seconds=fixed_speakers[0]["start"] + 0.5)  # Add small duration
    else:
        end = datetime.timedelta(seconds=fixed_speakers[0]["end"])

    full_utterances = []

    for i in range(1, len(fixed_speakers)):
        if fixed_speakers[i]["speaker"] == current_speaker:
            utt = utt + " " + fixed_speakers[i]["transcript"]
            # Handle end time
            if fixed_speakers[i]["end"] == -1:
                if i < len(fixed_speakers) - 1:
                    end = datetime.timedelta(seconds=fixed_speakers[i + 1]["start"])
                else:
                    end = datetime.timedelta(seconds=fixed_speakers[i]["start"] + 0.5)
            else:
                end = datetime.timedelta(seconds=fixed_speakers[i]["end"])
        else:
            # Speaker changed, write out the previous speaker's utterance
            full_utterances.append({"speaker": speaker_name, "start": start, "end": end, "utt": utt})

            # Start new utterance
            current_speaker = fixed_speakers[i]["speaker"]
            speaker_name = speaker_prefix + str(current_speaker)
            utt = fixed_speakers[i]["transcript"]
            start = datetime.timedelta(seconds=fixed_speakers[i]["start"])

            # Handle end time for new speaker
            if fixed_speakers[i]["end"] == -1:
                if i < len(fixed_speakers) - 1:
                    end = datetime.timedelta(seconds=fixed_speakers[i + 1]["start"])
                else:
                    end = datetime.timedelta(seconds=fixed_speakers[i]["start"] + 0.5)
            else:
                end = datetime.timedelta(seconds=fixed_speakers[i]["end"])

    # Add the last utterance
    full_utterances.append({"speaker": speaker_name, "start": start, "end": end, "utt": utt})

    return full_utterances


def _get_speaker_mapping(
    transcript: str, attendees: str, user_context: str, meeting_category: str
) -> Optional[Dict[str, str]]:
    """Get speaker mapping by prompting open ai to use context to assign speakers"""
    speaker_mapping = None

    logging.debug("Transcript: %s", transcript)
    openai.organization = settings.OPENAI_ORG_ID
    openai.api_key = settings.OPENAI_API_KEY
    attendees_str = f" with the following attendee(s):  {attendees}."

    meeting_str = ""
    if meeting_category == "client":
        meeting_str = meeting_str + "This was a client meeting between Financial Advisor(s) and their client(s)."
    else:
        meeting_str = meeting_str + "This was an internal meeting."

    prompt = (
        user_context
        + f"\nYou will be provided with a diarized transcript of a meeting {attendees_str}. "
        + f"{meeting_str} Please map the attendee names to the speaker and output them as a valid "
        + 'JSON dictionary of the format {"speaker<i>": "attendee name"}. The mapping can be many to one, '
        + "that is, it's possible that more than 1 speaker maps to the same attendee name. "
        + "Only output the mapping for names you have greater than 95% confidence. "
        + 'For names where confidence is below 95%, simply output {"speaker<i>": "speaker<i>"}.'
    )

    retries_left = settings.LLM_MAX_RETRIES
    while retries_left >= 0:
        try:
            retries_left -= 1
            response = openai.chat.completions.create(
                model="gpt-4o-2024-08-06",
                messages=[{"role": "system", "content": prompt}, {"role": "user", "content": transcript}],
                temperature=0.4,
                max_tokens=4096,
            )
            content = (
                response.choices[0].message.content.strip()
                if (response.choices and response.choices[0].message.content)
                else ""
            )
            logging.info(f"Speaker mapping response: {content}")
            speaker_mapping = parse_response_into_json(content)
            if not speaker_mapping:
                raise Exception("Failed to parse speaker mapping response")
            logging.debug(f"Speaker mapping successful: {speaker_mapping}")
            break
        except Exception as e:
            if retries_left < 0:
                logging.error("Speaker mapping failed: %s", e)
            else:
                logging.info(f"Speaker mapping failed: {e}. Retrying with {retries_left} retries left.")

    return speaker_mapping


def _get_processed_transcript(raw_asr_response: dict[str, Any] | list[dict[str, Any]], asr_process: str) -> dict | None:  # type: ignore[type-arg]
    """
    Process raw ASR response into a structured transcript format.

    This function takes the raw asr response, calculates speaker times, assigns speaker names
    and returns speaker times and the processed transcript.

    Args:
        raw_asr_response (dict): The raw ASR response containing transcript data.
        asr_process (str): The ASR process used (e.g., "deepgram").

    Returns:
        dict: A dictionary containing processed transcript data with the following keys:
            - meeting_duration (int): Duration of the meeting in seconds.
            - raw_transcript (list): List of combined utterances by speakers.
            - transcript (str): Formatted transcript with speakers and timestamps.
        Returns None if no utterances are found in the ASR response.
    """
    combined_utterances: list[dict[str, str | datetime.timedelta]] | None = []
    duration = 0
    if asr_process == "deepgram" and isinstance(raw_asr_response, dict):
        duration = int(raw_asr_response["metadata"]["duration"])
        combined_utterances = _combine_utts_by_speakers(raw_asr_response["results"]["utterances"], "speaker")
    elif isinstance(raw_asr_response, list):
        combined_utterances = _combine_utts_by_speakers(raw_asr_response)
        if not combined_utterances:
            logging.error("Invalid format for ASR response: combined utterances was falsy.")
            return None
        first_start_time = cast(datetime.timedelta, combined_utterances[0]["start"])
        last_end_time = cast(datetime.timedelta, combined_utterances[-1]["end"])
        if not isinstance(first_start_time, datetime.timedelta) or not isinstance(last_end_time, datetime.timedelta):
            logging.error("Invalid format for ASR response: invalid duration values.")
            return None

        duration = last_end_time.seconds - first_start_time.seconds
    else:
        logging.error("Invalid format for ASR response: %s", type(raw_asr_response))
        return None

    if not combined_utterances:
        logging.error("No utterances found in the ASR response.")
        return None

    # Adds speakers and speaker time to the transcript
    trans_with_speakers = _convert_utterances_to_transcript(combined_utterances)

    return {
        "meeting_duration": duration,
        "raw_transcript": combined_utterances or [],
        "transcript": trans_with_speakers,
    }


def calculate_speaker_time(combined_utterances: List[Dict[str, Any]]) -> Tuple[Dict[str, float], float]:
    """Calculate speaking time for each speaker."""
    speaker_time = {}  # type: ignore[var-annotated]
    total_time = 0
    for utterance in combined_utterances:
        speaker_time[utterance["speaker"]] = (
            speaker_time.get(utterance["speaker"], 0) + (utterance["end"] - utterance["start"]).total_seconds()
        )
        total_time += (utterance["end"] - utterance["start"]).total_seconds()

    # Sort dict speaker_time by value in descending order:
    sorted_speakers_by_time = sorted(speaker_time.items(), key=lambda x: x[1], reverse=True)
    speaker_time_sorted_dict = dict(sorted_speakers_by_time)
    return speaker_time_sorted_dict, total_time


def get_speaker_percentage(speaker_time: Dict[str, float], total_time: float) -> Dict[str, float]:
    """Calculate speaking time percentage for each speaker."""
    speaker_percentage = {}
    for speaker, time in speaker_time.items():
        speaker_percentage[speaker] = round((time / total_time) * 100, 1)
    return speaker_percentage


def process_transcript(
    note: Note, user: User, raw_asr_response: dict[str, Any] | list[dict[str, Any]], asr_process: str
) -> Dict[str, Any]:
    """
    Process raw ASR data to generate a structured transcript with metadata.

    This function takes raw Automatic Speech Recognition (ASR) data and processes
    it into a structured format, including speaker identification and time allocation
    for client and internal meetings.

    Args:
        note (Note): Note object containing meeting information and metadata.
        user (User): User object representing the user, containing user context.
        raw_asr_response (dict): The raw ASR response containing transcript data.
        asr_process (str): The ASR process used (e.g., "deepgram" or "recall").

    Returns:
        dict: A dictionary containing processed transcript data with the following keys:
            - meeting_duration (int): Duration of the meeting in seconds.
            - transcript (str): Processed transcript with speaker information.
            - keywords (list): List of keywords (currently empty).
            - speaker_percentage (dict): Mapping of speakers to their speaking time percentage.
            - speaker_mapping (dict): Mapping of speaker identifiers to attendee names
    """

    content_json: dict[str, Any] = {
        "meeting_duration": 0,
        "transcript": "",
        "keywords": [],
        "speaker_percentage": {},
        "speaker_mapping": None,
    }
    # We create two version of the transcript, a dictionary with speaker times and a string that contains the whole transcript.
    # The string is only created to be used in the speaker mapping. We should remove and just use the dictionary in the future.
    processed_data = _get_processed_transcript(raw_asr_response, asr_process)
    if not processed_data:
        logging.error(f"Failed to generate processed data for note id {note.uuid}")
        return content_json
    content_json.update(processed_data)
    meeting_category = note.category
    if meeting_category == "client" or meeting_category == "internal":
        trans_with_speakers = content_json["transcript"]
        speaker_times, total_time = calculate_speaker_time(content_json["raw_transcript"])
        speaker_percentage = get_speaker_percentage(speaker_times, total_time)
        speaker_mapping = None
        if asr_process == "deepgram":
            attendees = ", ".join(note.get_attendees())
            speaker_mapping = _get_speaker_mapping(
                trans_with_speakers, attendees, user.user_context or "", meeting_category
            )
        # If speaker mapping failed, or ASR process is not "deepgram", simply map each speaker to themselves.
        if not speaker_mapping:
            speaker_mapping = {s: s for s in speaker_times.keys()}
        content_json["speaker_mapping"] = speaker_mapping
        content_json["speaker_percentage"] = speaker_percentage

    return content_json
