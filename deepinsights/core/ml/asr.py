import logging

from deepgram import Deepgram
from deepgram._types import PrerecordedTranscriptionResponse
from deepgram.transcription import PrerecordedOptions, TranscriptionSource
from django.conf import settings

from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


def deepgram_options(
    asr_language_code: str, biasing_words: list[str], *, diarize: bool | None = None
) -> PrerecordedOptions:
    options: PrerecordedOptions = {
        "language": asr_language_code,
        "smart_format": True,
        "model": "nova-2",
        "redact": ["pci", "ssn"],
        "numerals": True,
        "utterances": True,
        "keywords": biasing_words,
        "replace": [
            "zeplin:Zeplyn",
            "zeplins:Zeplyns",
            "zeplin's:<PERSON><PERSON>lyn's",
            "zeppelin:Zeplyn",
            "zeppelins:Zeplyns",
            "zeppelin's:<PERSON><PERSON><PERSON>'s",
            "zeplon:<PERSON><PERSON>lyn",
            "zeplons:Z<PERSON>lyn<PERSON>",
            "zeplon's:<PERSON><PERSON><PERSON>'s",
        ],
        "log_data": False,
    }
    if diarize is not None:
        options["diarize"] = diarize

    return options


def _get_deepgram_transcript(
    source: TranscriptionSource, biasing_words: list[str], asr_language_code: str
) -> PrerecordedTranscriptionResponse:
    logging.info("Calling Deepgram for transcript.")

    dg_client = Deepgram(settings.DEEPGRAM_API_KEY)
    raw_response = dg_client.transcription.sync_prerecorded(
        source=source,
        options=deepgram_options(asr_language_code, biasing_words, diarize=True),
        custom_options={"mip_opt_out": True},
    )
    logging.info("Got transcript from DG")
    return raw_response


def fetch_and_save_deepgram_transcript(
    source: TranscriptionSource, note: Note, user: User
) -> PrerecordedTranscriptionResponse | None:
    asr_language_code = user.get_preferences().asr_language_code
    if not asr_language_code:
        logging.warning("ASR language code not set for user: %s, Using default('en') for now", user.uuid)
        asr_language_code = "en"
    raw_asr_response = _get_deepgram_transcript(source, [], asr_language_code)
    logging.debug("saving asr response for note: %s", note.uuid)
    note.raw_asr_response = raw_asr_response
    note.save()
    return raw_asr_response
