import ast
import datetime
import json
import logging
import re
from typing import Any, TypeVar

from pydantic import BaseModel
from pytz import timezone

from deepinsights.meetingsapp.models.note import Note

T = TypeVar("T", bound=BaseModel)


def parse_response_into_json(response: str) -> dict[str, Any]:
    """
    Parses the JSON output from the model, handling cases where the model returns it inside triple backticks
    or if the JSON structure is slightly malformed.

    Args:
    - model_output (str): The raw output from the model, potentially including triple backticks.

    Returns:
    - dict: Parsed JSON content if valid, otherwise an empty dictionary.
    """
    response = response.strip()

    # Remove triple backticks if present
    if response.startswith("```json"):
        response = response.removeprefix("```json").removesuffix("```")

    try:
        # Try parsing the JSON
        parsed_json = json.loads(response)
        return parsed_json  # type: ignore[no-any-return]
    except json.JSONDecodeError:
        logging.warning("Error: Invalid JSON format. Attempting to recover: %s", response)

        # Handle common cases of invalid JSON format (like trailing commas)
        try:
            # Recover from common formatting issues
            model_output_cleaned = response.replace("\n", "").replace(",]", "]").replace(",}", "}")
            parsed_json = json.loads(model_output_cleaned)
            return parsed_json  # type: ignore[no-any-return]
        except json.JSONDecodeError as e:
            logging.error("Error: Unable to parse JSON even after cleaning: %s", e)
            return {}


def parse_llm_response(response: str, model_class: type[T]) -> T | None:
    """
    Parse LLM response into the specified model.

    Args:
        response: Raw response string from LLM
        model_class: Pydantic model class to parse into

    Returns:
        Instance of the specified model class
    """
    try:
        data = parse_response_into_json(response)
        if not data:
            raise ValueError("Empty response from LLM")
        return model_class.model_validate(data)
    except Exception as e:
        logging.error("Failed to parse LLM response: %s", str(e))
        return None


def get_time_string_from_timestamp(timestamp: datetime.datetime, timezone_str: str) -> str:
    """Convert timestamp to formatted string in specified timezone."""
    if not timestamp or not timezone_str:
        return "unknown"
    format = "%Y-%m-%d %H:%M:%S %Z%z"
    actual_time = timestamp.astimezone(timezone(timezone_str))
    actual_time_str = str(actual_time.strftime(format))
    return actual_time_str


def replace_aliases_with_names(note: Note):  # type: ignore[no-untyped-def]
    """
    Returns diarized transcript string with actual speaker names instead of speaker aliases.
    (Only repalces the aliases and returns a string and does not actually modify the saved
    transcript string in the note object.)
    """
    if not note.diarized_trans_with_names:
        return note.diarized_trans_with_names  # Return if no transcript is available

    transcript = note.diarized_trans_with_names
    attendees = note.attendees.all()

    # rereate speaker mapping of speaker aliases to attendee names
    alias_to_name = {attendee.speaker_alias: attendee.get_name() for attendee in attendees if attendee.speaker_alias}

    # Replace aliases in the transcript
    for alias, name in alias_to_name.items():
        transcript = transcript.replace(alias, name)

    return transcript


def clean_and_parse_string(input_string: str) -> list:  # type: ignore[type-arg]
    """
    Clean and parse a raw_transcript string into a Python object using safer parsing methods.
    """

    # Remove starting and ending single quotes
    cleaned_string = input_string.strip("'")

    # Replace datetime.timedelta(...) with a placeholder string
    cleaned_string = re.sub(r"datetime\.timedelta\((.*?)\)", r"'timedelta(\1)'", cleaned_string)

    # Parse the string into a Python object using ast.literal_eval
    parsed_object = ast.literal_eval(cleaned_string)

    # Convert timedelta placeholders back into actual timedelta objects
    def replace_timedelta(obj):  # type: ignore[no-untyped-def]
        if isinstance(obj, list):
            return [replace_timedelta(item) for item in obj]  # type: ignore[no-untyped-call]
        elif isinstance(obj, dict):
            return {key: replace_timedelta(value) for key, value in obj.items()}  # type: ignore[no-untyped-call]
        elif isinstance(obj, str) and obj.startswith("timedelta("):
            # Extract the arguments from the placeholder string like "timedelta(seconds=1, microseconds=840000)"
            args_str = obj[10:-1]  # Strip "timedelta(" and ")"
            args = dict(item.split("=") for item in args_str.split(", "))
            # Convert the arguments into a valid timedelta object
            return datetime.timedelta(**{k: int(v) for k, v in args.items()})
        return obj

    return replace_timedelta(parsed_object)  # type: ignore[no-any-return, no-untyped-call]
