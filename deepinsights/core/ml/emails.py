import hashlib
import json
import logging
import re
import urllib
from datetime import datetime
from typing import Literal

from django.db.models import Q
from zoneinfo import ZoneInfo

from deepinsights.core.email_service import ZeplynEmailService
from deepinsights.core.ml.genai import call_claude_sonnet
from deepinsights.core.ml.prompt_builder import DataSource, PromptContext
from deepinsights.core.ml.voice_memo_utils import replace_aliases_with_names
from deepinsights.meetingsapp.models.emails import Email
from deepinsights.meetingsapp.models.meeting_summary_email_template import MeetingSummaryEmailTemplate
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


def get_time_of_day() -> Literal["morning"] | Literal["afternoon"] | Literal["evening"]:
    current_time = datetime.now(ZoneInfo("America/New_York"))
    hour = current_time.hour

    if 5 <= hour < 12:
        return "morning"
    elif 12 <= hour < 17:
        return "afternoon"
    else:
        return "evening"


def _get_followup_email(
    note: Note, user: User, email_template: str, email_generation_prompt: str, system_prompt: str
) -> str:
    """
    Generate a follow-up email for a given note.
    Args:
        note: The note to generate a follow-up email for.
        user: The user to generate a follow-up email for.
        email_template: The email template to use for the follow-up email.
        email_generation_prompt: The prompt to use for the follow-up email.
        system_prompt: The system prompt to use for the follow-up email.

    Returns:
        str: The generated follow-up email in markdown format.
    """
    prompt_context = PromptContext(note=note)
    context = prompt_context.build_context(
        [DataSource.MEETING_CONTEXT, DataSource.TASKS, DataSource.TAKEAWAYS, DataSource.SUMMARY]
    )
    context += f"\nTime of day to be used in the email: {get_time_of_day()}"
    context += f"\nUser context: the email is sent from the point of view of the user {user.get_full_name()}"

    try:
        prompt = email_generation_prompt.format(
            context=context,
            email_template=email_template,
        )
        markdown_answer = call_claude_sonnet(prompt, system_prompt)
        logging.debug("Markdown answer:\n%s", markdown_answer)

        return markdown_answer

    except Exception as e:
        logging.error("Error during Markdown generation and linting for note %s: %s", note.uuid, e)
        raise e


def _generate_note_checksum(note: Note, template_id: str = ""):  # type: ignore[no-untyped-def]
    # Gather data from note's task set
    task_titles = [task.task_title for task in note.task_set.all()]

    # Extract attendee data
    attendees = ",".join([str(a.uuid) for a in note.attendees.all()])

    # Get key takeaways
    key_takeaways = note.key_takeaways or []

    # Combine all data into a single string
    combined_data = {"tasks": task_titles, "attendees": attendees, "key_takeaways": key_takeaways, "version": "0.2  "}

    # Serialize the combined data into a JSON string
    if template_id:
        combined_data["template_id"] = template_id
    combined_string = json.dumps(combined_data, sort_keys=True)

    # Generate a SHA-256 checksum
    checksum = hashlib.sha256(combined_string.encode("utf-8")).hexdigest()
    logging.info("Checksum for %s is %s", note, checksum)
    return checksum


def generate_followup_email_contents(
    note_id: str,
    user: User,
    template_id: str = "",
) -> str:
    """
    Generate follow-up email contents for a given note.
    This function retrieves a note, generates or retrieves email contents,
    and returns an Email object with the follow-up content.

    Args:
        note_id (str): The UUID of the note to generate a follow-up email for.
        user (User): The user generating the follow-up email.
        use_cache (bool, optional): If True, use the cached email contents stored
                                    in the note object if available. This is not
                                    an external cache like Redis, but rather the
                                    data stored directly on the note object.
                                    Defaults to False.
        template_id (str):          The UUID of the email template to use for the follow-up email.
                                    if not given or invalid UUID the defualt template will be used.

    Returns:
        str: Generates a follow-up mailto link.
    """
    note = Note.objects.get(uuid=note_id)
    if not note:
        raise ValueError("Note with id {note_id} not found")
    if not note.note_owner:
        raise ValueError(f"Note with id {note_id} has no note_owner")
    if not note.metadata or not note.metadata.get("meeting_name"):
        raise ValueError(f"Note with id {note_id} has no metadata")
    meeting_name = note.metadata.get("meeting_name")
    note_owner: User = note.note_owner

    templates = (
        MeetingSummaryEmailTemplate.objects.filter(
            Q(everyone=True)
            | (Q(organizations__isnull=False) & Q(organizations=note_owner.organization))
            | (Q(users__isnull=False) & Q(users=note_owner))
        )
        .order_by("-everyone", "name")
        .distinct()
    )

    # map the exisitng templates for the user.
    template_dict = {str(template.uuid): template for template in templates}

    # Get the appropriate template
    template = None
    if template_id and template_id in template_dict:
        template = template_dict[template_id]
    else:
        logging.info("No template id provided, using default template")
        template = templates.first()
        template_id = str(template.uuid) if template else ""

    if not template:
        raise ValueError("No email template found")

    logging.info(
        "Using template_id: %s, template_name: %s, note_id: %s",
        template_id,
        template.internal_name,
        note_id,
    )

    # Get prompts and settings from the template
    followup_email_format_prompt = template.email_template_content or ""
    gen_prompt = template.generation_prompt
    system_prompt = template.system_prompt
    use_html = template.use_html

    email_checksum = note.follow_up_email_contents.get("email_checksum") if note.follow_up_email_contents else None
    current_checksum = _generate_note_checksum(note, template_id)

    if email_checksum == current_checksum:
        email_contents = Email.from_dict(note.follow_up_email_contents)
    else:
        email_content = _get_followup_email(note, user, followup_email_format_prompt, gen_prompt, system_prompt)
        email_contents = Email()
        email_contents.body = email_content
        email_contents.subject = f"[DRAFT] Follow-up email for {meeting_name}"
        email_contents.email_checksum = current_checksum

        if note.client:
            email_contents.to = note.client.get("email", "")
        else:
            email_contents.to = ""

        email_contents.cc = note_owner.get_preferences().email_settings.ccs
        email_contents.bcc = note_owner.get_preferences().email_settings.bccs

        note.follow_up_email_contents = email_contents.to_dict()
        logging.info("Saving note %s with email contents %s", note.uuid, email_contents.to_dict())
        note.save()

    mailto_link = _generate_mailto_link(email_contents, use_html)

    return mailto_link


def _convert_html_to_plain_text(html: str) -> str:
    """
    Convert HTML content to plain text.

    Args:
        html (str): The HTML content to be converted.

    Returns:
        str: The plain text content.
    """

    plain_text = re.sub(r"<div>", "", html)
    plain_text = re.sub(r"</div>", "\n", plain_text)
    plain_text = re.sub(r"<br>", "\n", plain_text)
    plain_text = re.sub(r"</?[^>]+(>|$)", "", plain_text)
    return plain_text


def _generate_mailto_link(email: Email, use_html: bool = False) -> str:
    """
    Generate a mailto link from an Email object.

    Args:
        email (Email): The Email object containing the email details.

    Returns:
        str: The generated mailto link.
    """

    to = email.to or ""
    subject = urllib.parse.quote(email.subject or "", safe="")
    body = email.body or ""
    if not use_html:
        body = _convert_html_to_plain_text(body)
    body_encoded = urllib.parse.quote(body, safe="")
    cc = urllib.parse.quote(",".join(email.cc) if email.cc else "", safe="")
    bcc = urllib.parse.quote(",".join(email.bcc) if email.bcc else "", safe="")

    mailto_link = f"mailto:{to}?subject={subject}&body={body_encoded}&content_type={'html' if use_html else 'plain'}"
    if cc:
        mailto_link += f"&cc={cc}"
    if bcc:
        mailto_link += f"&bcc={bcc}"

    return mailto_link


def _generate_meeting_notes_email_content(note: Note, summary: str, user: User | None) -> str:
    return note.get_summary_for_email(user, summary)  # type: ignore[arg-type]


def email_meeting_notes(note_id: str, user: User | None):  # type: ignore[no-untyped-def]
    note = Note.objects.get(uuid=note_id)
    if not user:
        user = note.note_owner
        if user is None:
            logging.warning(
                "Cannot send meeting notes email: both user parameter and note.note_owner are None for note %s", note_id
            )
            return
    user_email = str(user.email)
    logging.info("sending meeting notes email to user: %s", user_email)
    subject = "Meeting notes | " + note.metadata["meeting_name"]  # type: ignore[index]
    attachments = None
    if user.get_preferences().attach_transcript_to_follow_up_emails:
        if note.diarized_trans_with_names:
            transcript_with_names = replace_aliases_with_names(note)
            attachments = [{"content": transcript_with_names, "filename": "transcript.txt", "type": "text"}]

    email_service = ZeplynEmailService()
    email_service.send_email(
        sender=f"{user.get_full_name()} via Zeplyn <<EMAIL>>",
        reply_to="<EMAIL>",
        subject=subject,
        recipients=[user_email],
        cc=user.get_preferences().email_settings.ccs,
        bcc=user.get_preferences().email_settings.bccs,
        body=_generate_meeting_notes_email_content(note, subject, user),
        attachments=attachments,
        is_html=True,
    )
