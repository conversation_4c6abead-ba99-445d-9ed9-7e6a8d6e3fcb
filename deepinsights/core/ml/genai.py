import logging
from enum import Enum

import botocore
import openai
from anthropic import AnthropicBedrock
from django.conf import settings

async_openai_client = openai.AsyncOpenAI(
    organization=settings.OPENAI_ORG_ID,
    api_key=settings.OPENAI_API_KEY,
)


openai_client = openai.OpenAI(
    organization=settings.OPENAI_ORG_ID,
    api_key=settings.OPENAI_API_KEY,
)


# pydantic model for the model name
class ModelName(str, Enum):
    GPT_4O = "gpt-4o"
    O3 = "o3"
    O4_MINI = "o4-mini"


def call_model(
    prompt: str,
    temp: float = 0.1,
    max_tokens: int = 16384,
    model: ModelName = ModelName.GPT_4O,
) -> str:
    """
    Call the input model to generate a response to the given prompt.

    Args:
        prompt (str): The input prompt for the input model.
        temp (float, optional): The temperature setting for the model. Note some models do not support temperature.
        max_tokens (int, optional): The maximum number of tokens for the
            response.
        model (ModelName, optional): The model to use for the response.

    Returns:
        str: The generated response text from the model, or an error
            message if no response is received.
    """
    logging.info("calling %s", model)
    response = None
    if model == ModelName.GPT_4O:
        response = openai_client.chat.completions.create(
            model="gpt-4o-2024-08-06",
            messages=[{"role": "user", "content": prompt}],
            temperature=temp,
            max_tokens=max_tokens,
        )
    elif model == ModelName.O3:
        response = openai_client.chat.completions.create(
            model="o3-2025-04-16",
            messages=[{"role": "user", "content": prompt}],
            max_completion_tokens=max_tokens,
        )
    elif model == ModelName.O4_MINI:
        response = openai_client.chat.completions.create(
            model="o4-mini-2025-04-16",
            messages=[{"role": "user", "content": prompt}],
            max_completion_tokens=max_tokens,
        )

    if response and response.choices and response.choices[0].message.content:
        return response.choices[0].message.content
    else:
        raise ValueError("Invalid response object received from the input model.")


async def call_model_async(
    prompt: str, temp: float = 0.1, max_tokens: int = 16384, model: ModelName = ModelName.GPT_4O
) -> str:
    """
    Call the  model to generate a response to the given prompt.

    Args:
        prompt (str): The input prompt for the  model.
        temp (float, optional): The temperature setting for the model. Note some models do not support temperature.
        max_tokens (int, optional): The maximum number of tokens for the response. Note some models do not support max_tokens.
        model (ModelName, optional): The model to use for the response.

    Returns:
        str: The generated response text from the  model, or an error message if no response is received.
    """
    logging.info("calling model %s async", model)

    if model == ModelName.GPT_4O:
        response = await async_openai_client.chat.completions.create(
            model="gpt-4o",
            messages=[{"role": "user", "content": prompt}],
            temperature=temp,
            max_tokens=max_tokens,
        )
    elif model == ModelName.O3:
        response = await async_openai_client.chat.completions.create(
            model="o3-2025-04-16",
            messages=[{"role": "user", "content": prompt}],
            max_completion_tokens=max_tokens,
        )
    elif model == ModelName.O4_MINI:
        response = await async_openai_client.chat.completions.create(
            model="o4-mini-2025-04-16",
            messages=[{"role": "user", "content": prompt}],
            max_completion_tokens=max_tokens,
        )

    if response and response.choices and response.choices[0].message.content:
        return response.choices[0].message.content
    else:
        raise ValueError("Invalid response object received from the input model.")


def call_claude_sonnet(prompt: str, system_prompt: str, max_tokens: int = 8192) -> str:
    """
    Call the Claude Sonnet model to generate a response to the given prompt.

    Args:
        prompt (str): The input prompt for the Claude Sonnet model.
        system_prompt (str): The system prompt to guide the model's behavior.
        max_tokens (int): The maximum number of tokens for the response.

    Returns:
        str: The generated response text from the Claude Sonnet model, or an error message if no response is received.
    """
    logging.info("calling claude sonnet")

    client = AnthropicBedrock(
        aws_access_key=settings.AWS_ACCESS_KEY_ID,
        aws_secret_key=settings.AWS_SECRET_ACCESS_KEY,
        aws_region=settings.AWS_S3_REGION_NAME,
    )

    try:
        message = client.messages.create(
            model="anthropic.claude-3-5-sonnet-20240620-v1:0",
            max_tokens=max_tokens,
            messages=[{"role": "user", "content": prompt}],
            system=system_prompt,
        )

        if message and message.content:
            answer = ""
            for text_block in message.content:
                if hasattr(text_block, "text"):
                    answer += text_block.text
            return answer
        else:
            raise ValueError("Invalid response object received from Claude Sonnet")

    except botocore.exceptions.ClientError as error:
        if error.response["Error"]["Code"] == "AccessDeniedException":
            logging.error(
                "\x1b[41m%s\nTo troubleshoot this issue please refer to the following resources.\n"
                "https://docs.aws.amazon.com/IAM/latest/UserGuide/troubleshoot_access-denied.html\n"
                "https://docs.aws.amazon.com/bedrock/latest/userguide/security-iam.html\x1b[0m\n",
                error.response["Error"]["Message"],
            )
            raise ValueError("Email not available right now. Please try again later.")
        else:
            raise error
