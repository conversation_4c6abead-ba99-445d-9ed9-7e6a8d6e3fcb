import datetime
import uuid
from typing import Any
from unittest import mock

import pytest
from django.utils import timezone

from deepinsights.core.ml.scripts.user_metrics import (
    csv_filename_with_timestamp,
    generate_html_for_org_metrics,
    get_all_metrics,
    get_meeting_type,
    get_monthly_aggregate_metrics,
    get_monthly_users_by_org,
    get_org_metrics,
    get_per_note_information,
    process_metrics,
    write_metrics_to_csv,
    write_monthly_aggregate_metrics_to_csv,
    write_monthly_users_to_csv,
    write_notes_information_to_csv,
)
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.search_query import SearchQuery
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User

pytestmark = [pytest.mark.django_db]


# Common fixtures for all tests
@pytest.fixture
def test_organization() -> Organization:
    """Create a test organization."""
    return Organization.objects.create(name="Test Organization")


@pytest.fixture
def test_user(test_organization: Organization) -> User:
    """Create a test user associated with the test organization."""
    return User.objects.create(
        username="test_user", email="<EMAIL>", organization=test_organization, license_type="advisor"
    )


@pytest.fixture
def test_client(test_organization: Organization, test_user: User) -> Client:
    """Create a test client associated with the test organization."""
    client = Client.objects.create(name="Test Client", crm_id="CRM12345", organization=test_organization)
    client.authorized_users.add(test_user)
    return client


@pytest.fixture
def test_meeting_type() -> MeetingType:
    """Create a test meeting type."""
    return MeetingType.objects.create(name="Test Meeting", category="client")


@pytest.fixture
def test_note(test_user: User, test_meeting_type: MeetingType) -> Note:
    """Create a test note with a user and meeting type."""
    return Note.objects.create(
        note_owner=test_user,
        status=Note.PROCESSING_STATUS.processed,
        created=datetime.datetime(2023, 1, 1, 10, 0, 0, tzinfo=datetime.timezone.utc),
        metadata={
            "meeting_duration": 3600,
            "meeting_name": "Test Meeting",
            "tags": ["important", "follow-up"],
        },
        meeting_type=test_meeting_type,
    )


@pytest.fixture
def test_note_with_client(test_note: Note, test_client: Client) -> Note:
    """Create a test note with a client."""
    test_note.client = {"uuid": str(test_client.uuid), "name": test_client.name}
    test_note.save()
    return test_note


@pytest.fixture
def test_notes_batch(test_organization: Organization) -> list[Note]:
    """Create a batch of test notes with various properties."""
    # Create 5 users
    users = User.objects.bulk_create(
        [
            User(
                username=f"user_{i}",
                email=f"user{i}@example.com",
                organization=test_organization,
                license_type="advisor",
            )
            for i in range(5)
        ]
    )

    # Create 10 notes with different statuses, dates, and metadata
    notes = []
    for i in range(10):
        created_date = timezone.now() - datetime.timedelta(days=i * 30)
        status = Note.PROCESSING_STATUS.processed if i % 2 == 0 else Note.PROCESSING_STATUS.finalized
        meeting_type = "client" if i % 3 == 0 else "internal" if i % 3 == 1 else "debrief"

        note = Note.objects.create(
            note_owner=users[i % 5],
            status=status,
            created=created_date,
            metadata={
                "meeting_duration": i * 600,
                "scheduled_at": created_date.isoformat(),
                "meeting_name": f"Meeting {i}",
                "meeting_type": meeting_type,
                "tags": ["tag1", "tag2"],
            },
        )
        notes.append(note)

    return notes


@pytest.fixture
def test_notes_with_tasks_and_attendees(test_notes_batch: list[Note], test_client: Client) -> list[Note]:
    """Create test notes with associated tasks and attendees."""
    for i, note in enumerate(test_notes_batch):
        # Add tasks
        for j in range(i % 3 + 1):  # 1 to 3 tasks per note
            Task.objects.create(
                note=note,
                task_title=f"Task {j} for Note {i}",
                is_deleted=(j == 0),  # Make first task deleted for testing
            )

        # For note 0, add specific attendee configuration for test
        if i == 0:
            # Add one internal attendee
            user = note.note_owner
            assert user
            Attendee.objects.create(
                note=note,
                user=user,
                attendee_name=f"{user.username}",
            )

            # Add one client attendee (external)
            Attendee.objects.create(
                note=note,
                client=test_client,
                attendee_name=test_client.name,
            )

            # Add one unknown attendee (external)
            Attendee.objects.create(
                note=note,
                attendee_name="Unknown Person",
            )
            continue

        # Handle other notes normally
        if i % 2 == 0:  # Even notes get user attendees (internal)
            user = note.note_owner
            assert user
            Attendee.objects.create(
                note=note,
                user=user,
                attendee_name=f"{user.username}",
            )

        if i % 3 == 0:  # Every third note gets client attendees (external)
            Attendee.objects.create(
                note=note,
                client=test_client,
                attendee_name=test_client.name,
            )

        # Add unknown attendees
        if i % 4 == 0 and i != 0:  # Every fourth note gets unknown attendees, but not note 0 (handled above)
            Attendee.objects.create(
                note=note,
                attendee_name=f"Unknown Person {i}",
            )

        # Add email contents to some notes
        if i % 3 == 0:
            note.follow_up_email_contents = {
                "sent_at": timezone.now().isoformat(),
                "to": "<EMAIL>",
                "subject": "Meeting Notes",
            }
            note.save()

    return test_notes_batch


@pytest.fixture
def test_notes_with_search_queries(test_notes_with_tasks_and_attendees: list[Note], test_user: User) -> list[Note]:
    notes = test_notes_with_tasks_and_attendees

    for i, note in enumerate(notes):
        # Create different number of search queries for each note
        # Note 0: 3 queries, Note 1: 1 query, Note 2: 0 queries, Note 3: 2 queries, etc.
        query_count = 3 if i == 0 else (1 if i == 1 else (0 if i == 2 else (2 if i == 3 else i % 3)))

        for j in range(query_count):
            query = SearchQuery.objects.create(
                requestor=test_user,
                query=f"Test query {j} for note {i}",
                structured_response={"summary": f"Test summary for query {j}, note {i}"},
            )
            query.notes.add(note)

    return notes


@pytest.fixture
def mock_meetingbot_data() -> list[dict[str, Any]]:
    """Create mock meetingbot data for testing."""
    return [
        {"uuid": uuid.uuid4(), "meeting_link": "https://meet.google.com/abc-def-ghi"},
        {"uuid": uuid.uuid4(), "meeting_link": "zoom.us/j/123456789"},
        {"uuid": uuid.uuid4(), "meeting_link": "+14155552671,,123456789#"},
        {"uuid": None, "meeting_link": None},
        {"uuid": uuid.uuid4(), "meeting_link": "teams.microsoft.com/l/meeting/123"},
    ]


# Test for csv_filename_with_timestamp function
def test_csv_filename_with_timestamp() -> None:
    """Test the csv_filename_with_timestamp function."""
    basename = "test_file"
    with mock.patch("django.utils.timezone.now") as mock_now:
        mock_date = datetime.datetime(2023, 1, 1, 12, 30, 45, tzinfo=datetime.timezone.utc)
        mock_now.return_value = mock_date
        filename = csv_filename_with_timestamp(basename)
        assert filename == "test_file_20230101_123045.csv"


# Test for get_meeting_type function
@pytest.mark.parametrize(
    "data_source, meetingbot_data, expected_type",
    [
        ("invalid", {"uuid": uuid.uuid4(), "meeting_link": "https://meet.google.com/abc-def-ghi"}, "Notetaker"),
        (
            Note.DataSource.AUDIO_BUFFERS,
            {"uuid": uuid.uuid4(), "meeting_link": "https://meet.google.com/abc-def-ghi"},
            "Mic",
        ),
        (
            Note.DataSource.AUDIO_FILE,
            {"uuid": uuid.uuid4(), "meeting_link": "https://meet.google.com/abc-def-ghi"},
            "Mic",
        ),
        (Note.DataSource.RECALL, {}, "Notetaker"),
        (Note.DataSource.TWILIO, {}, "Phone call"),
        (None, {"uuid": uuid.uuid4(), "meeting_link": "https://meet.google.com/abc-def-ghi"}, "Notetaker"),
        (None, {"uuid": uuid.uuid4(), "meeting_link": "zoom.us/j/123456789"}, "Notetaker"),
        (None, {"uuid": uuid.uuid4(), "meeting_link": "+14155552671"}, "Phone call"),
        (None, {"uuid": None, "meeting_link": None}, "Mic"),
        (None, {"uuid": uuid.uuid4(), "meeting_link": None}, "Notetaker"),
        (None, {"uuid": uuid.uuid4(), "meeting_link": "teams.microsoft.com/l/meeting/123"}, "Notetaker"),
        (None, {}, "Mic"),
    ],
)
def test_get_meeting_type(data_source: str | None, meetingbot_data: dict[str, Any], expected_type: str) -> None:
    """Test the get_meeting_type function with various meeting link formats."""
    assert get_meeting_type(data_source, meetingbot_data) == expected_type


# Test for get_org_metrics function
def test_get_org_metrics(test_organization: Organization, test_notes_with_tasks_and_attendees: list[Note]) -> None:
    """Test the get_org_metrics function."""
    metrics = get_org_metrics(months=12)  # type: ignore[no-untyped-call]

    # Verify metrics structure
    assert isinstance(metrics, dict)
    assert test_organization.name in metrics

    org_metrics = metrics[test_organization.name]
    assert "monthly_notes" in org_metrics
    assert "weekly_notes" in org_metrics

    # Verify that metrics contain data
    monthly_notes = org_metrics["monthly_notes"]
    assert len(monthly_notes) > 0

    # Verify that a metric entry has the expected structure
    sample_metric = monthly_notes[0]
    assert "month" in sample_metric
    assert "meeting_category" in sample_metric
    assert "note_count" in sample_metric
    assert "total_duration" in sample_metric
    assert "active_users" in sample_metric


# Test for write_metrics_to_csv function
def test_write_metrics_to_csv(test_organization: Organization, test_notes_with_tasks_and_attendees: list[Note]) -> None:
    """Test the write_metrics_to_csv function."""
    org_metrics = get_org_metrics(months=12)  # type: ignore[no-untyped-call]
    processed_data, meeting_categories = process_metrics(org_metrics)  # type: ignore[no-untyped-call]

    mock_open = mock.mock_open()
    with mock.patch("builtins.open", mock_open):
        filename = "test_metrics.csv"
        write_metrics_to_csv(processed_data, meeting_categories, filename)

        # Verify file was opened
        mock_open.assert_called_once_with(filename, "w", newline="")

        # Verify data was written
        handle = mock_open()
        assert handle.write.call_count > 0


# Test for generate_html_for_org_metrics function
def test_generate_html_for_org_metrics(
    test_organization: Organization, test_notes_with_tasks_and_attendees: list[Note]
) -> None:
    """Test the generate_html_for_org_metrics function."""
    org_metrics = get_org_metrics(months=12)  # type: ignore[no-untyped-call]
    processed_data, meeting_categories = process_metrics(org_metrics)  # type: ignore[no-untyped-call]

    # Test with specific org
    html_output = generate_html_for_org_metrics(processed_data, meeting_categories, test_organization.name)  # type: ignore[no-untyped-call]
    assert isinstance(html_output, str)
    assert "<html>" in html_output
    assert test_organization.name in html_output

    # Test with no org filter
    html_output = generate_html_for_org_metrics(processed_data, meeting_categories, None)  # type: ignore[no-untyped-call]
    assert isinstance(html_output, str)
    assert test_organization.name in html_output


# Test for get_monthly_aggregate_metrics function
def test_get_monthly_aggregate_metrics(
    test_organization: Organization, test_notes_with_tasks_and_attendees: list[Note]
) -> None:
    """Test the get_monthly_aggregate_metrics function."""
    monthly_metrics = get_monthly_aggregate_metrics(months=12)  # type: ignore[no-untyped-call]

    assert isinstance(monthly_metrics, dict)
    assert len(monthly_metrics) > 0

    # Check structure of metrics for a month
    sample_month = list(monthly_metrics.keys())[0]
    month_data = monthly_metrics[sample_month]

    assert "total_notes" in month_data
    assert "total_users" in month_data
    assert "meeting_categories" in month_data
    assert isinstance(month_data["meeting_categories"], dict)


# Test for write_monthly_aggregate_metrics_to_csv function
def test_write_monthly_aggregate_metrics_to_csv(
    test_organization: Organization, test_notes_with_tasks_and_attendees: list[Note]
) -> None:
    """Test the write_monthly_aggregate_metrics_to_csv function."""
    monthly_metrics = get_monthly_aggregate_metrics(months=12)  # type: ignore[no-untyped-call]

    mock_open = mock.mock_open()
    with mock.patch("builtins.open", mock_open):
        filename = "test_monthly_metrics.csv"
        write_monthly_aggregate_metrics_to_csv(monthly_metrics, filename)

        # Verify file was opened
        mock_open.assert_called_once_with(filename, "w", newline="")

        # Verify data was written
        handle = mock_open()
        assert handle.write.call_count > 0


# Test for get_monthly_users_by_org function
def test_get_monthly_users_by_org(
    test_organization: Organization, test_notes_with_tasks_and_attendees: list[Note]
) -> None:
    """Test the get_monthly_users_by_org function."""
    user_month_data, all_months = get_monthly_users_by_org(months=12)  # type: ignore[no-untyped-call]

    assert isinstance(user_month_data, dict)
    assert len(user_month_data) > 0
    assert isinstance(all_months, list)
    assert len(all_months) > 0

    # Verify structure of user data
    sample_user = list(user_month_data.keys())[0]
    user_months = user_month_data[sample_user]
    assert isinstance(user_months, set)


# Test for write_monthly_users_to_csv function
def test_write_monthly_users_to_csv(
    test_organization: Organization, test_notes_with_tasks_and_attendees: list[Note]
) -> None:
    """Test the write_monthly_users_to_csv function."""
    user_month_data, all_months = get_monthly_users_by_org(months=12)  # type: ignore[no-untyped-call]

    mock_open = mock.mock_open()
    with mock.patch("builtins.open", mock_open):
        filename = "test_monthly_users.csv"
        write_monthly_users_to_csv(user_month_data, all_months, filename)

        # Verify file was opened
        mock_open.assert_called_once_with(filename, "w", newline="")

        # Verify data was written
        handle = mock_open()
        assert handle.write.call_count > 0


# Test for write_notes_information_to_csv function
def test_write_notes_information_to_csv() -> None:
    """Test the write_notes_information_to_csv function."""
    summary_data = [
        {
            "ID": 1,
            "Organization Name": "Test Org",
            "License Type": "advisor",
            "Meeting Mode": "Mic",
            "Meeting Type": "client",
            "Note Status": "processed",
            "Meeting Duration": 3600,
            "Note Owner Email": "<EMAIL>",
            "Creation Date": "2023-01-01",
            "Salesforce Case ID": "SF12345",
            "CRM ID": "CRM123",
            "Is Deleted": False,
            "Action Items Count": 3,
            "Email to Client Generated": "Yes",
            "Internal Attendees Count": 2,
            "External Attendees Count": 1,
        }
    ]

    mock_open = mock.mock_open()
    with mock.patch("builtins.open", mock_open):
        filename = "test_notes_info.csv"
        write_notes_information_to_csv(summary_data, filename)

        # Verify file was opened
        mock_open.assert_called_once_with(filename, "w", newline="")

        # Verify data was written
        handle = mock_open()
        assert handle.write.call_count > 0


# Test for get_per_note_information function
def test_get_per_note_information(
    test_organization: Organization, test_notes_with_tasks_and_attendees: list[Note]
) -> None:
    """Test the get_per_note_information function."""
    notes_data = get_per_note_information()

    assert isinstance(notes_data, list)
    assert len(notes_data) > 0

    # Verify structure of note data
    sample_note = notes_data[0]
    assert "ID" in sample_note
    assert "Organization Name" in sample_note
    assert "License Type" in sample_note
    assert "Meeting Mode" in sample_note
    assert "Meeting Type" in sample_note
    assert "Note Status" in sample_note
    assert "Meeting Duration" in sample_note
    assert "Note Owner Email" in sample_note
    assert "Creation Date" in sample_note
    assert "Salesforce Case ID" in sample_note
    assert "CRM ID" in sample_note
    assert "Is Deleted" in sample_note
    assert "Action Items Count" in sample_note
    assert "Email to Client Generated" in sample_note
    assert "Internal Attendees Count" in sample_note
    assert "External Attendees Count" in sample_note


# Test for get_all_metrics function
def test_get_all_metrics(test_organization: Organization, test_notes_with_tasks_and_attendees: list[Note]) -> None:
    mock_open = mock.mock_open()
    with mock.patch("builtins.open", mock_open):
        result = get_all_metrics()

        # Verify that all expected files are generated
        assert isinstance(result, list)
        assert len(result) == 4

        # Verify all files were opened for writing
        assert mock_open.call_count == 4


@pytest.mark.parametrize(
    "note_id, expected_count",
    [
        (0, 0),  # First note should have 0 non-deleted tasks (since the only task is marked as deleted)
        (1, 1),  # Second note should have 1 non-deleted task (2 total - 1 deleted)
        (2, 2),  # Third note should have 2 non-deleted tasks (3 total - 1 deleted)
    ],
)
def test_task_count_accuracy(
    test_notes_with_tasks_and_attendees: list[Note], note_id: int, expected_count: int
) -> None:
    """Test the accuracy of task counts in get_per_note_information."""
    note = test_notes_with_tasks_and_attendees[note_id]

    # Get actual non-deleted task count directly from database
    actual_count = Task.objects.filter(note=note, is_deleted=False).count()
    print(f"Note {note_id} direct DB count for non-deleted tasks: {actual_count}")
    assert actual_count == expected_count

    # Verify that get_per_note_information reports the same count
    notes_data = get_per_note_information()
    note_data = next((n for n in notes_data if n["ID"] == note.id), None)

    assert note_data is not None
    print(f"Note {note_id} count from get_per_note_information: {note_data['Action Items Count']}")
    assert note_data["Action Items Count"] == expected_count


# Parametrized test for attendee counts
@pytest.mark.parametrize(
    "note_id, expected_internal, expected_external",
    [
        (0, 1, 2),  # Note 0 has 1 internal, 2 external (1 client + 1 unknown)
        (1, 0, 0),  # Note 1 has no attendees
        (2, 1, 0),  # Note 2 has 1 internal, 0 external
        (3, 0, 1),  # Note 3 has 0 internal, 1 external (client)
    ],
)
def test_attendee_count_accuracy(
    test_notes_with_tasks_and_attendees: list[Note], note_id: int, expected_internal: int, expected_external: int
) -> None:
    """Test the accuracy of attendee counts in get_per_note_information."""
    # Only run the test for notes that should be in processed or finalized state
    note = test_notes_with_tasks_and_attendees[note_id]
    if note.status not in [Note.PROCESSING_STATUS.processed, Note.PROCESSING_STATUS.finalized]:
        pytest.skip(f"Note {note_id} is not in processed or finalized state")

    # Verify database counts first
    internal_count = Attendee.objects.filter(note=note, user__isnull=False).count()
    external_count = Attendee.objects.filter(note=note, user__isnull=True).count()

    # Print debug info
    print(f"Note {note_id} direct DB count: {internal_count} internal, {external_count} external")

    assert internal_count == expected_internal
    assert external_count == expected_external

    # Then verify the function's output
    notes_data = get_per_note_information()
    note_data = next((n for n in notes_data if n["ID"] == note.id), None)

    assert note_data is not None
    print(
        f"Note {note_id} function output: {note_data['Internal Attendees Count']} internal, {note_data['External Attendees Count']} external"
    )

    if expected_internal > 0 or expected_external > 0:
        assert note_data["Internal Attendees Count"] == expected_internal
        assert note_data["External Attendees Count"] == expected_external


@pytest.mark.parametrize(
    "note_id, expected_count",
    [
        (0, 3),  # First note should have 3 search queries
        (1, 1),  # Second note should have 1 search query
        (2, 0),  # Third note should have 0 search queries
        (3, 2),  # Fourth note should have 2 search queries
    ],
)
def test_search_query_count_accuracy(
    test_notes_with_search_queries: list[Note], note_id: int, expected_count: int
) -> None:
    note = test_notes_with_search_queries[note_id]

    actual_count = SearchQuery.objects.filter(notes=note).count()
    assert actual_count == expected_count

    notes_data = get_per_note_information()
    note_data = next((n for n in notes_data if n["ID"] == note.id), None)

    assert note_data is not None
    assert note_data["Ask Anything Usage"] == expected_count
