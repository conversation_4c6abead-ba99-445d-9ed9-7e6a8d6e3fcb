import asyncio
import csv
import logging
from datetime import datetime, timedelta, timezone
from typing import Set
from urllib.parse import urlparse

from asgiref.sync import sync_to_async
from django.conf import settings
from pydantic import HttpUrl, OnErrorOmit

from deepinsights.core.integrations.calendar import google, microsoft
from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.oauth.google import GoogleOAuth
from deepinsights.core.integrations.oauth.microsoft import MicrosoftOAuth
from deepinsights.meetingsapp.models.oauth_credentials import OAuthClientCredentials
from deepinsights.users.models.user import User

logger = logging.getLogger(__name__)


async def export_calendar_events_to_csv(
    filepath: str,
    org_ids: list[str] | None = None,
    user_ids: list[str] | None = None,
    start_date: str | None = None,
    end_date: str | None = None,
) -> None:
    """
    Export calendar events for specified organizations and users to CSV.

    Args:
        filepath: Path to save the CSV file (defaults to root directory with timestamp)
        org_ids: Optional list of organization IDs to filter by
        user_ids: Optional list of user IDs to filter by
        start_date: Optional start date in format "mm-dd-yyyy"
        end_date: Optional end date in format "mm-dd-yyyy"
    """

    if start_date and end_date:
        try:
            start_date_obj = datetime.strptime(start_date, "%m-%d-%Y").replace(tzinfo=timezone.utc)
            end_date_obj = datetime.strptime(end_date, "%m-%d-%Y").replace(
                hour=23, minute=59, second=59, tzinfo=timezone.utc
            )
        except ValueError:
            logger.error("Invalid date format. Use mm-dd-yyyy")
            return
    else:
        end_date_obj = datetime.now(timezone.utc)
        start_date_obj = end_date_obj - timedelta(days=30)

    user_query = {}
    user_query["is_active"] = True

    if org_ids:
        user_query["organization__id__in"] = org_ids  # type: ignore[assignment]

    if user_ids:
        user_query["id__in"] = user_ids  # type: ignore[assignment]

    users: list[User] = [user async for user in User.objects.filter(**user_query).select_related("organization")]

    org_names = {}
    for user in users:
        org_name = user.organization.name if user.organization else "None"
        org_names[str(user.id)] = org_name

    total_users = len(users)
    logger.info(
        "Exporting calendar events for %s users from %s to %s",
        total_users,
        start_date_obj.strftime("%Y-%m-%d"),
        end_date_obj.strftime("%Y-%m-%d"),
    )

    ms_oauth = MicrosoftOAuth()
    google_oauth = GoogleOAuth()

    with open(filepath, "w", newline="") as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(
            [
                "User ID",
                "User Email",
                "Event ID",
                "Provider",
                "Title",
                "Start Time",
                "End Time",
                "All Day",
                "Number of Participants",
                "Meeting Platform",
                "Participants",
                "Organization",
            ]
        )

        batch_size = 50
        for i in range(0, total_users, batch_size):
            batch_users = users[i : i + batch_size]
            logger.info("Processing batch %s/%s", i // batch_size + 1, (total_users + batch_size - 1) // batch_size)

            semaphore = asyncio.Semaphore(10)  # Maximum 10 concurrent API requests

            tasks = [
                process_user_events(user, start_date_obj, end_date_obj, semaphore, ms_oauth, google_oauth)
                for user in batch_users
            ]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)

            for user_events in batch_results:
                if isinstance(user_events, BaseException):
                    logger.error("Error processing user batch: %s", user_events)
                    continue

                user_id, email, events = user_events

                for event in events:
                    try:
                        org_name = org_names.get(user_id, "None")
                        meeting_platform = extract_meeting_platform(event.meeting_urls)

                        participants_str = ""
                        if event.participants:
                            emails = []
                            for p in event.participants:
                                if hasattr(p, "email_address") and p.email_address:
                                    emails.append(p.email_address)
                                elif hasattr(p, "email") and p.email:
                                    emails.append(p.email)
                            participants_str = ",".join(emails)

                        writer.writerow(
                            [
                                user_id,
                                email,
                                event.id,
                                event.provider,
                                event.title,
                                event.start_time.isoformat(),
                                event.end_time.isoformat(),
                                event.all_day,
                                len(event.participants),
                                meeting_platform,
                                participants_str,
                                org_name,
                            ]
                        )
                    except Exception as e:
                        logger.error("Error writing event for user %s: %s", user_id, e)

            # Sleep briefly between batches to avoid overloading APIs
            await asyncio.sleep(1)


def extract_meeting_platform(meeting_urls: list[OnErrorOmit[HttpUrl]]) -> str:
    """
    Extract meeting platform name from meeting URLs.

    Args:
        meeting_urls: List of meeting URLs (can be string or Url objects)

    Returns:
        String containing the meeting platform name(s)
    """
    if not meeting_urls:
        return ""

    platforms = set()

    for url in meeting_urls:
        try:
            url_str = str(url)
            domain = urlparse(url_str).netloc.lower()

            if "zoom" in domain:
                platforms.add("Zoom")
            elif "meet.google" in domain or "hangouts" in domain or "tel.meet" in domain:
                platforms.add("Google Meet")
            elif "teams" in domain:
                platforms.add("Microsoft Teams")
            elif "webex" in domain:
                platforms.add("Webex")
            elif "gotomeeting" in domain or "goto" in domain:
                platforms.add("GoToMeeting")
            elif "bluejeans" in domain:
                platforms.add("BlueJeans")
            elif "skype" in domain:
                platforms.add("Skype")
            elif "chime" in domain:
                platforms.add("Amazon Chime")
            elif "whereby" in domain:
                platforms.add("Whereby")
            elif "jitsi" in domain:
                platforms.add("Jitsi")
            else:
                parsed_domain = domain.split(".")
                if len(parsed_domain) >= 2:
                    platforms.add(parsed_domain[-2].capitalize())
                else:
                    platforms.add(domain)
        except Exception:
            continue

    return ", ".join(sorted(platforms))


async def process_user_events(
    user: User,
    start_date: datetime,
    end_date: datetime,
    semaphore: asyncio.Semaphore,
    ms_oauth: MicrosoftOAuth,
    google_oauth: GoogleOAuth,
) -> tuple[str, str, list[CalendarEvent]]:
    """
    Process calendar events for a single user within the given date range.

    Args:
        user: User to process events for
        start_date: Start date for event retrieval
        end_date: End date for event retrieval
        semaphore: Semaphore to limit concurrent API calls
        ms_oauth: Microsoft OAuth handler
        google_oauth: Google OAuth handler

    Returns:
        Tuple of (user_id, email, events)
    """
    user_id = str(user.id)
    email = user.email
    all_events: list[CalendarEvent] = []
    processed_event_ids: Set[str] = set()

    ms_access_token = await ms_oauth.get_access_token(user)
    google_access_token = await google_oauth.get_access_token(user)
    user_ms_id = user.microsoft_id
    org_client_credentials = await OAuthClientCredentials.objects.filter(
        organization=user.organization, provider=OAuthClientCredentials.Providers.MICROSOFT
    ).afirst()
    ms_tenant_id = org_client_credentials.tenant_id if org_client_credentials else None
    if not ms_access_token and not google_access_token and not user_ms_id and not ms_tenant_id:
        logger.debug("User %s has no OAuth credentials at the user user or org level, skipping", user_id)
        return (user_id, email, [])

    try:
        date_range = end_date - start_date

        logger.debug(
            "Processing events for user %s from %s to %s",
            user_id,
            start_date.strftime("%Y-%m-%d"),
            end_date.strftime("%Y-%m-%d"),
        )

        # Acquire semaphore to limit concurrent API calls
        async with semaphore:
            ms_credentials: microsoft.MicrosoftCredentials | None = None
            if ms_access_token:
                ms_expires_on = int(
                    datetime.timestamp(
                        (await ms_oauth.get_expiry(user)) or datetime.now(timezone.utc) + timedelta(minutes=5)
                    )
                )
                ms_credentials = microsoft.MicrosoftCredentials.from_access_token(
                    access_token=ms_access_token, expires_on=ms_expires_on
                )
            elif (
                (user_ms_id := user.microsoft_id)
                and (
                    org_client_credentials := await OAuthClientCredentials.objects.filter(
                        organization=user.organization, provider=OAuthClientCredentials.Providers.MICROSOFT
                    ).afirst()
                )
                and (tenant_id := org_client_credentials.tenant_id)
            ):
                ms_credentials = microsoft.MicrosoftCredentials.from_client_credentials(
                    client_id=settings.MSAL_CLIENT_ID,
                    client_secret=settings.MSAL_CLIENT_SECRET,
                    tenant_id=tenant_id,
                    user_id=user_ms_id,
                )

            if ms_credentials:
                ms_events = await microsoft.fetch_calendar_events(
                    ms_credentials,
                    interval=date_range,
                    start_time=start_date,
                )

                for event in ms_events:
                    if event.id not in processed_event_ids:
                        all_events.append(event)
                        processed_event_ids.add(event.id)

            if google_access_token:
                google_events = await sync_to_async(google.fetch_calendar_events)(
                    google_access_token, interval=date_range, start_time=start_date
                )

                for event in google_events:
                    if event.id not in processed_event_ids:
                        all_events.append(event)
                        processed_event_ids.add(event.id)

        logger.debug("Completed processing for user %s, found %s events", user_id, len(all_events))
        return (user_id, email, all_events)
    except Exception as e:
        logger.error("Error processing events for user %s: %s", user_id, e)
        return (user_id, email, [])
