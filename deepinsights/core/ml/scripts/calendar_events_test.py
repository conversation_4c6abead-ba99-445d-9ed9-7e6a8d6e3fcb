import asyncio
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Union, cast
from unittest import mock

import pytest
from django.db.models.query import QuerySet
from pydantic import HttpUrl, OnErrorOmit

from deepinsights.core.integrations.calendar.calendar_models import Calendar<PERSON>vent, EventParticipant
from deepinsights.core.integrations.oauth.google import GoogleOAuth
from deepinsights.core.integrations.oauth.microsoft import MicrosoftOAuth
from deepinsights.core.ml.scripts.calendar_events import (
    export_calendar_events_to_csv,
    extract_meeting_platform,
    process_user_events,
)
from deepinsights.meetingsapp.models.oauth_credentials import OAuthClientCredentials
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.user import User

# ====== Test Fixtures ======

pytestmark = [pytest.mark.django_db(transaction=True)]


@pytest.fixture
def test_organization() -> Organization:
    org = Organization.objects.create(name="Test Organization")
    return org


@pytest.fixture
def test_user(test_organization: Organization) -> User:
    user = User.objects.create(
        username="<EMAIL>",
        name="Test User",
        organization=test_organization,
        is_active=True,
        email="<EMAIL>",
    )
    return user


@pytest.fixture
def test_users_with_different_orgs() -> Dict[str, Union[Organization, User]]:
    org1 = Organization.objects.create(name="Organization 1")

    org2 = Organization.objects.create(name="Organization 2")

    user1, _ = User.objects.get_or_create(
        username="<EMAIL>",
        name="User One",
        organization=org1,
        is_active=True,
        email="<EMAIL>",
    )

    user2, _ = User.objects.get_or_create(
        username="<EMAIL>",
        name="User Two",
        organization=org2,
        is_active=True,
        email="<EMAIL>",
    )

    user3, _ = User.objects.get_or_create(
        username="<EMAIL>",
        name="Inactive User",
        organization=org1,
        is_active=False,
        email="<EMAIL>",
    )

    return {"org1": org1, "org2": org2, "user1": user1, "user2": user2, "inactive_user": user3}


@pytest.fixture
def mock_calendar_event() -> CalendarEvent:
    return CalendarEvent(
        provider="microsoft",
        id="event123",
        user_specific_id="uevent123",
        title="Test Meeting",
        body="Test body",
        start_time=datetime.now(timezone.utc),
        end_time=datetime.now(timezone.utc) + timedelta(hours=1),
        all_day=False,
        participants=[
            EventParticipant(name="John Doe", email_address="<EMAIL>"),
            EventParticipant(name="Jane Smith", email_address="<EMAIL>"),
        ],
        meeting_urls=[
            HttpUrl("https://teams.microsoft.com/meeting/123"),
            HttpUrl("https://meet.google.com/abc-def-ghi"),
        ],
    )


@pytest.fixture
def mock_ms_oauth() -> MicrosoftOAuth:
    oauth = mock.MagicMock(spec=MicrosoftOAuth)

    oauth.get_access_token = mock.AsyncMock(return_value="ms_mock_token")
    oauth.get_expiry = mock.AsyncMock(return_value=datetime.now(timezone.utc) + timedelta(hours=1))

    return oauth


@pytest.fixture
def mock_google_oauth() -> GoogleOAuth:
    """Mock GoogleOAuth instance with AsyncMock methods."""
    oauth = mock.MagicMock(spec=GoogleOAuth)

    oauth.get_access_token = mock.AsyncMock(return_value="google_mock_token")

    return oauth


# ====== Test Functions ======


@pytest.mark.parametrize(
    "url,expected_platform",
    [
        (HttpUrl("https://teams.microsoft.com/meeting/123"), "Microsoft Teams"),
        (HttpUrl("https://meet.google.com/abc-def-ghi"), "Google Meet"),
        (HttpUrl("https://tel.meet/abc-def-ghi?pin=*********0"), "Google Meet"),
        (HttpUrl("https://zoom.us/j/*********"), "Zoom"),
        (HttpUrl("https://company.webex.com/meet/12345"), "Webex"),
        (HttpUrl("https://bluejeans.com/*********"), "BlueJeans"),
        (HttpUrl("https://gotomeeting.com/join/*********"), "GoToMeeting"),
        (HttpUrl("https://whereby.com/room1234"), "Whereby"),
        (HttpUrl("https://jitsi.org/room1234"), "Jitsi"),
        (HttpUrl("https://skype.com/join/*********"), "Skype"),
        (HttpUrl("https://chime.aws/meeting/123"), "Amazon Chime"),
        (HttpUrl("https://unknown-platform.com/meeting/123"), "Unknown-platform"),
    ],
)
def test_extract_meeting_platform_individual(url: OnErrorOmit[HttpUrl], expected_platform: str) -> None:
    result = extract_meeting_platform([url])
    assert result == expected_platform


def test_extract_meeting_platform_multiple() -> None:
    urls = [HttpUrl("https://teams.microsoft.com/meeting/123"), HttpUrl("https://meet.google.com/abc-def-ghi")]
    result = extract_meeting_platform(urls)
    assert result == "Google Meet, Microsoft Teams"


def test_extract_meeting_platform_empty() -> None:
    assert extract_meeting_platform([]) == ""


@pytest.mark.asyncio
async def test_process_user_events_no_credentials(test_user: User) -> None:
    mock_ms_oauth = mock.MagicMock(spec=MicrosoftOAuth)
    mock_ms_oauth.get_access_token = mock.AsyncMock(return_value=None)
    mock_ms_oauth.get_expiry = mock.AsyncMock(return_value=None)

    mock_google_oauth = mock.MagicMock(spec=GoogleOAuth)
    mock_google_oauth.get_access_token = mock.AsyncMock(return_value=None)

    semaphore = asyncio.Semaphore(10)
    start_date = datetime.now(timezone.utc) - timedelta(days=7)
    end_date = datetime.now(timezone.utc)

    user_id, email, events = await process_user_events(
        test_user, start_date, end_date, semaphore, mock_ms_oauth, mock_google_oauth
    )

    assert user_id == str(test_user.id)
    assert email == test_user.email
    assert events == []


@pytest.mark.asyncio
@mock.patch("deepinsights.core.integrations.calendar.microsoft.fetch_calendar_events")
async def test_process_user_events_with_credentials(
    mock_fetch_ms: mock.AsyncMock,
    test_user: User,
    mock_ms_oauth: MicrosoftOAuth,
    mock_google_oauth: GoogleOAuth,
    mock_calendar_event: CalendarEvent,
) -> None:
    mock_fetch_ms.return_value = [mock_calendar_event]

    mock_google_events = mock.AsyncMock(return_value=[])

    semaphore = asyncio.Semaphore(10)
    start_date = datetime.now(timezone.utc) - timedelta(days=7)
    end_date = datetime.now(timezone.utc)

    with mock.patch("deepinsights.core.ml.scripts.calendar_events.sync_to_async", return_value=mock_google_events):
        user_id, email, events = await process_user_events(
            test_user, start_date, end_date, semaphore, mock_ms_oauth, mock_google_oauth
        )

    assert user_id == str(test_user.id)
    assert email == test_user.email
    assert len(events) == 1
    assert events[0].id == mock_calendar_event.id

    mock_fetch_ms.assert_called_once()
    assert mock_fetch_ms.call_args[1]["interval"] == (end_date - start_date)
    assert mock_fetch_ms.call_args[1]["start_time"] == start_date


@pytest.mark.asyncio
@mock.patch("deepinsights.core.integrations.calendar.microsoft.fetch_calendar_events")
async def test_process_user_events_with_org_level_credentials(
    mock_fetch_ms: mock.AsyncMock,
    test_user: User,
    test_organization: Organization,
    mock_calendar_event: CalendarEvent,
) -> None:
    mock_ms_oauth = mock.MagicMock(spec=MicrosoftOAuth)
    mock_ms_oauth.get_access_token = mock.AsyncMock(return_value=None)
    mock_ms_oauth.get_expiry = mock.AsyncMock(return_value=None)

    mock_google_oauth = mock.MagicMock(spec=GoogleOAuth)
    mock_google_oauth.get_access_token = mock.AsyncMock(return_value=None)
    mock_fetch_ms.return_value = [mock_calendar_event]

    mock_google_events = mock.AsyncMock(return_value=[])

    test_user.microsoft_id = "test_microsoft_id"
    await test_user.asave()

    await OAuthClientCredentials.objects.acreate(
        organization=test_organization, provider=OAuthClientCredentials.Providers.MICROSOFT, tenant_id="test_client_id"
    )

    semaphore = asyncio.Semaphore(10)
    start_date = datetime.now(timezone.utc) - timedelta(days=7)
    end_date = datetime.now(timezone.utc)

    with mock.patch("deepinsights.core.ml.scripts.calendar_events.sync_to_async", return_value=mock_google_events):
        user_id, email, events = await process_user_events(
            test_user, start_date, end_date, semaphore, mock_ms_oauth, mock_google_oauth
        )

    assert user_id == str(test_user.id)
    assert email == test_user.email
    assert len(events) == 1
    assert events[0].id == mock_calendar_event.id

    mock_fetch_ms.assert_called_once()
    assert mock_fetch_ms.call_args[1]["interval"] == (end_date - start_date)
    assert mock_fetch_ms.call_args[1]["start_time"] == start_date


@pytest.mark.parametrize(
    "use_org1,use_org2,use_user1,use_user2,expected_count",
    [
        (False, False, False, False, 2),  # All active users (user1 and user2)
        (True, False, False, False, 1),  # Only users in org1 (user1)
        (False, False, True, False, 1),  # Only specific user1
        (True, True, True, True, 2),  # Both specific users
    ],
)
@pytest.mark.asyncio
@mock.patch("deepinsights.core.ml.scripts.calendar_events.open", new_callable=mock.mock_open)
@mock.patch("deepinsights.core.ml.scripts.calendar_events.csv.writer")
@mock.patch("deepinsights.core.ml.scripts.calendar_events.process_user_events")
@mock.patch("deepinsights.core.ml.scripts.calendar_events.MicrosoftOAuth")
@mock.patch("deepinsights.core.ml.scripts.calendar_events.GoogleOAuth")
async def test_export_calendar_events_to_csv_with_real_filters(
    mock_google_oauth: mock.MagicMock,
    mock_ms_oauth: mock.MagicMock,
    mock_process_user: mock.AsyncMock,
    mock_csv_writer: mock.MagicMock,
    mock_open: mock.MagicMock,
    test_users_with_different_orgs: Dict[str, Union[Organization, User]],
    use_org1: bool,
    use_org2: bool,
    use_user1: bool,
    use_user2: bool,
    expected_count: int,
) -> None:
    org_ids: list[str] = []
    if use_org1:
        org_ids.append(str(cast(Organization, test_users_with_different_orgs["org1"]).id))
    if use_org2:
        org_ids.append(str(cast(Organization, test_users_with_different_orgs["org2"]).id))

    user_ids: List[str] = []
    if use_user1:
        user_ids.append(str(cast(User, test_users_with_different_orgs["user1"]).id))
    if use_user2:
        user_ids.append(str(cast(User, test_users_with_different_orgs["user2"]).id))

    org_ids_param = org_ids if org_ids else None
    user_ids_param = user_ids if user_ids else None

    mock_process_user.return_value = ("user_id", "email", [])

    mock_writer = mock.MagicMock()
    mock_csv_writer.return_value = mock_writer

    active_users = [test_users_with_different_orgs["user1"], test_users_with_different_orgs["user2"]]

    filtered_users = []
    for user in active_users:
        if org_ids_param and (
            isinstance(user, User) and (user.organization is None or str(user.organization.id) not in org_ids_param)
        ):
            continue
        if user_ids_param and str(user.id) not in user_ids_param:
            continue
        filtered_users.append(user)

    mock_user_iterator = mock.AsyncMock()
    if expected_count == 2:
        mock_user_iterator.__aiter__.return_value = iter(active_users)
    else:
        mock_user_iterator.__aiter__.return_value = iter(filtered_users)

    with mock.patch.object(User.objects, "filter") as mock_filter:
        mock_filter.return_value.select_related.return_value = mock_user_iterator

        filepath = "test_export.csv"
        await export_calendar_events_to_csv(filepath, org_ids=org_ids_param, user_ids=user_ids_param)

        assert mock_process_user.call_count == expected_count


@pytest.mark.asyncio
@mock.patch("deepinsights.core.ml.scripts.calendar_events.open", new_callable=mock.mock_open)
@mock.patch("deepinsights.core.ml.scripts.calendar_events.csv.writer")
@mock.patch("deepinsights.core.ml.scripts.calendar_events.process_user_events")
@mock.patch("deepinsights.core.ml.scripts.calendar_events.User.objects.filter")
async def test_export_calendar_events_to_csv_simplified(
    mock_user_filter: mock.MagicMock,
    mock_process_user: mock.AsyncMock,
    mock_csv_writer: mock.MagicMock,
    mock_open: mock.MagicMock,
) -> None:
    mock_user = mock.MagicMock()
    mock_user.id = "user123"
    mock_user.email = "<EMAIL>"
    mock_user.organization = None

    mock_event = mock.MagicMock()
    mock_event.id = "event123"
    mock_event.provider = "test"
    mock_event.title = "Test Meeting"
    mock_event.start_time = datetime.now(timezone.utc)
    mock_event.end_time = datetime.now(timezone.utc) + timedelta(hours=1)
    mock_event.all_day = False
    mock_event.participants = []
    mock_event.meeting_urls = []

    mock_user_iterator = mock.AsyncMock()
    mock_user_iterator.__aiter__.return_value = iter([mock_user])

    mock_user_filter.return_value.select_related.return_value = mock_user_iterator

    mock_process_user.return_value = ("user123", "<EMAIL>", [mock_event])

    mock_writer = mock.MagicMock()
    mock_csv_writer.return_value = mock_writer

    with mock.patch("deepinsights.core.ml.scripts.calendar_events.MicrosoftOAuth"), mock.patch(
        "deepinsights.core.ml.scripts.calendar_events.GoogleOAuth"
    ):
        filepath = "test_export.csv"
        await export_calendar_events_to_csv(filepath)

    mock_open.assert_called_once()
    mock_process_user.assert_called_once()
    assert mock_writer.writerow.call_count >= 2


@pytest.mark.parametrize(
    "org_ids,user_ids,expected_query",
    [
        (None, None, {"is_active": True}),
        (["org1"], None, {"is_active": True, "organization__id__in": ["org1"]}),
        (None, ["user1"], {"is_active": True, "id__in": ["user1"]}),
        (
            ["org1", "org2"],
            ["user1", "user2"],
            {"is_active": True, "organization__id__in": ["org1", "org2"], "id__in": ["user1", "user2"]},
        ),
    ],
)
@pytest.mark.asyncio
async def test_export_calendar_events_to_csv_filters_simple(
    org_ids: list[str] | None,
    user_ids: list[str] | None,
    expected_query: dict[str, Any],
) -> None:
    original_filter = User.objects.filter
    filter_args_captured: List[Dict[str, Any]] = []

    def capture_filter_args(**kwargs: Any) -> QuerySet[User]:
        filter_args_captured.append(kwargs)
        return QuerySet(model=User)

    async def patched_export(
        filepath: str,
        org_ids: list[str] | None = None,
        user_ids: list[str] | None = None,
        start_date: str | None = None,
        end_date: str | None = None,
    ) -> None:
        user_query: Dict[str, Any] = {}
        user_query["is_active"] = True

        if org_ids and len(org_ids) > 0:
            user_query["organization__id__in"] = org_ids

        if user_ids and len(user_ids) > 0:
            user_query["id__in"] = user_ids

        # Call the filter directly, bypassing sync_to_async
        capture_filter_args(**user_query)

    with mock.patch(
        "deepinsights.core.ml.scripts.calendar_events.User.objects.filter", side_effect=capture_filter_args
    ):
        await patched_export("test.csv", org_ids=org_ids, user_ids=user_ids)

    assert filter_args_captured
    actual_query = filter_args_captured[0]

    for key, value in expected_query.items():
        assert key in actual_query
        assert actual_query[key] == value


@pytest.mark.asyncio
@mock.patch("deepinsights.core.ml.scripts.calendar_events.open", new_callable=mock.mock_open)
@mock.patch("deepinsights.core.ml.scripts.calendar_events.csv.writer")
@mock.patch("deepinsights.core.ml.scripts.calendar_events.sync_to_async")
@mock.patch("deepinsights.core.ml.scripts.calendar_events.User.objects.filter")
@mock.patch("deepinsights.core.ml.scripts.calendar_events.MicrosoftOAuth")
@mock.patch("deepinsights.core.ml.scripts.calendar_events.GoogleOAuth")
async def test_export_calendar_events_to_csv_date_parsing(
    mock_google_oauth: mock.MagicMock,
    mock_ms_oauth: mock.MagicMock,
    mock_user_filter: mock.MagicMock,
    mock_sync_to_async: mock.MagicMock,
    mock_csv_writer: mock.MagicMock,
    mock_open: mock.MagicMock,
) -> None:
    mock_user_queryset = mock.MagicMock()
    mock_user_filter.return_value = mock_user_queryset

    mock_sync_to_async.return_value = mock.AsyncMock(return_value=[])

    mock_writer = mock.MagicMock()
    mock_csv_writer.return_value = mock_writer

    start_date = "03-20-2025"
    end_date = "03-28-2025"

    await export_calendar_events_to_csv("test_export.csv", start_date=start_date, end_date=end_date)

    with mock.patch("deepinsights.core.ml.scripts.calendar_events.logger") as mock_logger:
        await export_calendar_events_to_csv(
            "test_export.csv",
            start_date="2025-03-20",  # Wrong format
            end_date="2025-03-28",  # Wrong format
        )
        mock_logger.error.assert_called_with("Invalid date format. Use mm-dd-yyyy")


@pytest.mark.asyncio
@mock.patch("deepinsights.core.ml.scripts.calendar_events.open", new_callable=mock.mock_open)
@mock.patch("deepinsights.core.ml.scripts.calendar_events.csv.writer")
async def test_export_calendar_events_end_to_end(
    mock_csv_writer: mock.MagicMock, mock_open: mock.MagicMock, test_user: User
) -> None:
    event = CalendarEvent(
        provider="test",
        id="test-event-1",
        user_specific_id="test-user-specific-id",
        title="Test Meeting",
        body="Test body",
        start_time=datetime.now(timezone.utc),
        end_time=datetime.now(timezone.utc) + timedelta(hours=1),
        all_day=False,
        participants=[EventParticipant(name="Test User", email_address=test_user.email)],
        meeting_urls=[HttpUrl("https://meet.google.com/abc-def-ghi")],
    )

    mock_ms_calendar = mock.AsyncMock()
    mock_ms_calendar.return_value = [event]

    mock_google_function = mock.MagicMock()
    mock_google_function.return_value = []

    # Use a real MicrosoftOAuth and GoogleOAuth, but mock their get_access_token methods
    with mock.patch(
        "deepinsights.core.integrations.oauth.microsoft.MicrosoftOAuth.get_access_token",
        new=mock.AsyncMock(return_value="fake-token"),
    ), mock.patch(
        "deepinsights.core.integrations.oauth.microsoft.MicrosoftOAuth.get_expiry",
        new=mock.AsyncMock(return_value=datetime.now(timezone.utc) + timedelta(hours=1)),
    ), mock.patch(
        "deepinsights.core.integrations.oauth.google.GoogleOAuth.get_access_token",
        new=mock.AsyncMock(return_value="fake-token"),
    ), mock.patch(
        "deepinsights.core.integrations.calendar.microsoft.fetch_calendar_events",
        mock_ms_calendar,
    ), mock.patch(
        "deepinsights.core.ml.scripts.calendar_events.sync_to_async",
        return_value=mock.AsyncMock(
            side_effect=[
                [test_user],
                mock_google_function,
            ]
        ),
    ):
        mock_writer = mock.MagicMock()
        mock_csv_writer.return_value = mock_writer

        filepath = "test_export.csv"
        await export_calendar_events_to_csv(filepath)

        mock_open.assert_called_once()
        assert mock_writer.writerow.call_count >= 2

        data_written = False
        for call_args in mock_writer.writerow.call_args_list:
            row = call_args[0][0]
            if "test-event-1" in row:
                data_written = True
                break
        assert data_written, "Expected event data was not written to CSV"
