import csv
from collections import defaultdict
from datetime import datetime, timedelta
from typing import Any

import phonenumbers
from django.db.models import <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>ield, OuterRef, Q, Subquery, Sum, UUIDField, Value, When
from django.db.models.fields.json import KeyTextTransform
from django.db.models.functions import <PERSON>, TruncMonth, TruncWeek
from django.utils import timezone

from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization

excluded_org_names = [
    "Zeplyn Internal",
    "Zeplyn Trial Organization",
    "Zeplyn Trial Org",
    "Trial Org",
    "Salesforce Demo RIA",
    "Demo Redtail RIA",
]


def csv_filename_with_timestamp(basename: str) -> str:
    """Given a basename, returns a filename with a timestamp and .csv extension."""
    return f"{basename}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"


# Add option to take month names.
def get_org_metrics(months=8):  # type: ignore[no-untyped-def]
    end_date = timezone.now()
    start_date = end_date - timedelta(days=months * 30)

    organizations = Organization.objects.exclude(name__in=excluded_org_names).order_by("name")

    notes_query = Note.objects.filter(
        created__gte=start_date,
        created__lte=end_date,
        status__in=[Note.PROCESSING_STATUS.processed, Note.PROCESSING_STATUS.finalized],
    ).annotate(
        month=TruncMonth("created"),
        week=TruncWeek("created"),
        duration=Cast(KeyTextTransform("meeting_duration", "metadata"), output_field=IntegerField()),
        meeting_category=Case(
            When(Q(meeting_type__category__isnull=False), then=F("meeting_type__category")),
            When(Q(metadata__meeting_type__isnull=False), then=KeyTextTransform("meeting_type", "metadata")),
            default=Value("unknown"),
            output_field=CharField(),
        ),
    )

    org_metrics = {}
    for org in organizations:
        org_notes = notes_query.filter(note_owner__organization=org)

        monthly_note_metrics = (
            org_notes.values("month", "meeting_category")
            .annotate(note_count=Count("id"), total_duration=Sum("duration"))
            .order_by("month", "meeting_category")
        )

        monthly_active_users = (
            org_notes.values("month").annotate(active_users=Count("note_owner", distinct=True)).order_by("month")
        )

        weekly_note_metrics = (
            org_notes.values("week", "meeting_category")
            .annotate(note_count=Count("id"), total_duration=Sum("duration"))
            .order_by("week", "meeting_category")
        )

        weekly_active_users = (
            org_notes.values("week").annotate(active_users=Count("note_owner", distinct=True)).order_by("week")
        )

        monthly_metrics = []
        for note_metric in monthly_note_metrics:
            active_user_entry = next((au for au in monthly_active_users if au["month"] == note_metric["month"]), None)
            if active_user_entry:
                note_metric["active_users"] = active_user_entry["active_users"]
            monthly_metrics.append(note_metric)

        weekly_metrics = []
        for note_metric in weekly_note_metrics:
            active_user_entry = next((au for au in weekly_active_users if au["week"] == note_metric["week"]), None)
            if active_user_entry:
                note_metric["active_users"] = active_user_entry["active_users"]
            weekly_metrics.append(note_metric)

        org_metrics[org.name] = {"monthly_notes": monthly_metrics, "weekly_notes": weekly_metrics}

    return org_metrics


def process_metrics(metrics):  # type: ignore[no-untyped-def]
    processed_data: defaultdict[str, defaultdict[str, defaultdict[str, defaultdict[str, int]]]] = defaultdict(
        lambda: defaultdict(lambda: defaultdict(lambda: defaultdict(int)))
    )
    meeting_categories = set()

    for org_name, org_data in metrics.items():
        for period in ["monthly", "weekly"]:
            for note in org_data[f"{period}_notes"]:
                date_key = note[period[:-2]].strftime("%Y-%m-%d")
                meeting_category = note["meeting_category"] or "Unknown"
                meeting_category = meeting_category.lower()
                if meeting_category != "unknown" and meeting_category != "null":
                    meeting_categories.add(meeting_category)
                    processed_data[org_name][period][date_key]["total_notes"] += note["note_count"]
                    processed_data[org_name][period][date_key]["total_duration"] += note["total_duration"] or 0
                    processed_data[org_name][period][date_key][f"{meeting_category}_count"] += note["note_count"]
                    processed_data[org_name][period][date_key][f"{meeting_category}_duration"] += (
                        note["total_duration"] or 0
                    )
                    processed_data[org_name][period][date_key]["active_users"] = note["active_users"]

    return processed_data, meeting_categories


def write_metrics_to_csv(processed_data, meeting_categories, filename: str) -> None:  # type: ignore[no-untyped-def]
    with open(filename, "w", newline="") as csvfile:
        csvwriter = csv.writer(csvfile)

        for org_name, org_data in processed_data.items():
            csvwriter.writerow([org_name])
            csvwriter.writerow([])

            for period in ["monthly", "weekly"]:
                csvwriter.writerow([f"{period.capitalize()} Metrics"])

                header = ["Date", "Total Notes", "Total Duration (min)", "Active Users"]
                for mt in sorted(meeting_categories):
                    header.extend([f"{mt.capitalize()} Count", f"{mt.capitalize()} Duration"])
                csvwriter.writerow(header)

                for date, metrics in sorted(org_data[period].items()):
                    row = [date, metrics["total_notes"], metrics["total_duration"], metrics["active_users"]]
                    for mt in sorted(meeting_categories):
                        row.extend([metrics[f"{mt}_count"], metrics[f"{mt}_duration"]])
                    csvwriter.writerow(row)

                csvwriter.writerow([])

            csvwriter.writerow([])
    print(f"Metrics tables have been written to '{filename}' successfully.")


def generate_html_for_org_metrics(data, meeting_categories, org):  # type: ignore[no-untyped-def]
    html = "<html><head><style>"
    html += """
    body { font-family: Arial, sans-serif; margin: 20px; }
    h1 { color: #333; }
    h2 { color: #555; }
    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
    th, td { border: 1px solid #ccc; padding: 8px; text-align: left; }
    th { background-color: #f4f4f4; }
    tr:nth-child(even) { background-color: #f9f9f9; }
    """
    html += "</style></head><body>"

    for org_name, org_data in data.items():
        if org and org_name != org:
            continue
        html += f"<h1>{org_name}</h1>"

        for period in ["monthly", "weekly"]:
            html += f"<h2>{period.capitalize()} Metrics</h2>"
            html += "<table><thead><tr>"
            headers = (
                ["Date", "Total Notes", "Total Duration (min)", "Active Users"]
                + [f"{mt.capitalize()} Count" for mt in sorted(meeting_categories)]
                + [f"{mt.capitalize()} Duration" for mt in sorted(meeting_categories)]
            )
            for header in headers:
                html += f"<th>{header}</th>"
            html += "</tr></thead><tbody>"

            for date, metrics in sorted(org_data[period].items()):
                html += "<tr>"
                row = (
                    [date, metrics["total_notes"], metrics["total_duration"], metrics["active_users"]]
                    + [metrics[f"{mt}_count"] for mt in sorted(meeting_categories)]
                    + [metrics[f"{mt}_duration"] for mt in sorted(meeting_categories)]
                )
                for item in row:
                    html += f"<td>{item}</td>"
                html += "</tr>"

            html += "</tbody></table>"

    html += "</body></html>"
    return html


def get_monthly_aggregate_metrics(months=8):  # type: ignore[no-untyped-def]
    end_date = timezone.now()
    start_date = end_date - timedelta(days=months * 30)

    organizations = Organization.objects.exclude(name__in=excluded_org_names).order_by("name")

    monthly_metrics = (
        Note.objects.filter(
            created__gte=start_date,
            created__lte=end_date,
            status__in=[Note.PROCESSING_STATUS.processed, Note.PROCESSING_STATUS.finalized],
            note_owner__organization__in=organizations,
        )
        .annotate(
            month=TruncMonth("created"),
            duration=Cast(KeyTextTransform("meeting_duration", "metadata"), output_field=IntegerField()),
            meeting_category=Case(
                When(Q(meeting_type__category__isnull=False), then=F("meeting_type__category")),
                When(Q(metadata__meeting_type__isnull=False), then=KeyTextTransform("meeting_type", "metadata")),
                default=Value("unknown"),
                output_field=CharField(),
            ),
        )
        .values("month", "meeting_category")
        .annotate(note_count=Count("id"), total_duration=Sum("duration"))
        .order_by("month", "meeting_category")
    )

    monthly_active_users = (
        Note.objects.filter(
            created__gte=start_date,
            created__lte=end_date,
            status__in=[Note.PROCESSING_STATUS.processed, Note.PROCESSING_STATUS.finalized],
            note_owner__organization__in=organizations,
        )
        .annotate(month=TruncMonth("created"))
        .values("month")
        .annotate(active_users=Count("note_owner", distinct=True))
        .order_by("month")
    )

    combined_metrics: defaultdict[str, dict[str, Any]] = defaultdict(
        lambda: {
            "total_notes": 0,
            "total_users": 0,
            "meeting_categories": defaultdict(lambda: {"count": 0, "duration": 0}),
        }
    )

    for metric in monthly_metrics:
        month = metric["month"]
        meeting_category = metric["meeting_category"] or "Unknown"
        meeting_category = meeting_category.lower()
        if meeting_category == "unknown" or meeting_category == "null":
            continue
        combined_metrics[month]["total_notes"] += metric["note_count"]
        combined_metrics[month]["meeting_categories"][meeting_category]["count"] += metric["note_count"]
        combined_metrics[month]["meeting_categories"][meeting_category]["duration"] += metric["total_duration"] or 0

    for user_metric in monthly_active_users:
        month = user_metric["month"]
        combined_metrics[month]["total_users"] = user_metric["active_users"]

    return combined_metrics


def write_monthly_aggregate_metrics_to_csv(metrics, filename: str) -> None:  # type: ignore[no-untyped-def]
    with open(filename, "w", newline="") as csvfile:
        csvwriter = csv.writer(csvfile)

        header = ["Month", "Total Users", "Total Notes"]
        meeting_categories = set()
        for month_data in metrics.values():
            meeting_categories.update(month_data["meeting_categories"].keys())
        for mt in sorted(meeting_categories):
            header.extend([f"{mt} Count", f"{mt} Duration"])
        csvwriter.writerow(header)

        for month, data in sorted(metrics.items()):
            row = [month.strftime("%Y-%m"), data["total_users"], data["total_notes"]]
            for mt in sorted(meeting_categories):
                row.extend([data["meeting_categories"][mt]["count"], data["meeting_categories"][mt]["duration"]])
            csvwriter.writerow(row)

    print(f"CSV file '{filename}' has been created successfully.")


def get_monthly_users_by_org(months=8):  # type: ignore[no-untyped-def]
    end_date = timezone.now()
    start_date = end_date - timedelta(days=months * 30)

    organizations = Organization.objects.exclude(name__in=excluded_org_names).order_by("name")

    monthly_users = (
        Note.objects.filter(
            created__gte=start_date,
            created__lte=end_date,
            status__in=[Note.PROCESSING_STATUS.processed, Note.PROCESSING_STATUS.finalized],
            note_owner__organization__in=organizations,
        )
        .annotate(month=TruncMonth("created"))
        .values("month", "note_owner__email")
        .distinct()
        .order_by("month", "note_owner__email")
    )

    user_month_data = defaultdict(set)
    all_months = set()

    for entry in monthly_users:
        month = entry["month"].strftime("%Y-%m")
        email = entry["note_owner__email"]
        user_month_data[email].add(month)
        all_months.add(month)

    return user_month_data, sorted(all_months)


def write_monthly_users_to_csv(user_month_data, all_months, filename: str) -> None:  # type: ignore[no-untyped-def]
    with open(filename, "w", newline="") as csvfile:
        csvwriter = csv.writer(csvfile)

        header = ["User Email"] + all_months
        csvwriter.writerow(header)

        for email, months in user_month_data.items():
            row = [email] + ["X" if month in months else "" for month in all_months]
            csvwriter.writerow(row)

    print(f"CSV file '{filename}' has been created successfully.")


def get_meeting_type(data_source: str | None, meetingbot_data: dict[str, Any]) -> str:
    # Try to use the data source, if it is available.
    match data_source:
        case Note.DataSource.AUDIO_FILE | Note.DataSource.AUDIO_BUFFERS:
            return "Mic"
        case Note.DataSource.RECALL:
            return "Notetaker"
        case Note.DataSource.TWILIO:
            return "Phone call"

    # If not, fall back to intuiting based on the meeting bot data.

    # Mic meeting if no meetingbot UUID
    if not meetingbot_data.get("uuid"):
        return "Mic"

    meeting_link = meetingbot_data.get("meeting_link")
    if not meeting_link:
        return "Notetaker"  # if link is missing but UUID exists

    if len(meeting_link) <= 15 and "/" not in meeting_link:
        try:
            # Try to parse as phone number
            phone_number = phonenumbers.parse(meeting_link, "US")
            if phonenumbers.is_valid_number(phone_number):
                return "Phone call"
        except phonenumbers.phonenumberutil.NumberParseException:
            pass

    # Recall Meeting
    return "Notetaker"


def write_notes_information_to_csv(summary_data: list[dict], filename: str) -> None:  # type: ignore[type-arg]
    try:
        # Open the file and write the CSV data
        with open(filename, "w", newline="") as csvfile:
            fieldnames = [
                "ID",
                "Organization Name",
                "License Type",
                "Meeting Mode",
                "Meeting Type",
                "Note Status",
                "Meeting Duration",
                "Note Owner Email",
                "Creation Date",
                "Salesforce Case ID",
                "CRM ID",
                "Is Deleted",
                "Action Items Count",
                "Completed Action Items Count",
                "Incomplete Action Items Count",
                "Overdue Action Items Count",
                "Email to Client Generated",
                "Internal Attendees Count",
                "External Attendees Count",
                "Ask Anything Usage",
                "Tags",
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            # Write the header and then all rows
            writer.writeheader()
            for row in summary_data:
                writer.writerow(row)

        print(f"CSV file '{filename}' has been generated successfully.")
    except Exception as e:
        print(f"Error while generating CSV file for notes information: {e}")


def get_per_note_information() -> list[dict[str, Any]]:
    """
    Retrieves and processes note data from the database.

    This function queries the Note model, excluding notes from specific organizations,
    and collects relevant information for each note.

    Returns:
        list: A list of dictionaries, each containing data for a single note.
    """
    # First, get all the note IDs we'll need to process
    note_ids = Note.objects_with_deleted.exclude(note_owner__organization__name__in=excluded_org_names).values_list(
        "id", flat=True
    )

    # Get attendee counts in a separate query for each note
    attendee_counts = {}
    for note_id in note_ids:
        internal_count = Attendee.objects.filter(note_id=note_id, user__isnull=False).count()
        external_count = Attendee.objects.filter(note_id=note_id, user__isnull=True).count()
        attendee_counts[note_id] = {"internal": internal_count, "external": external_count}

    # Now get the main note data
    notes = (
        Note.objects_with_deleted.exclude(note_owner__organization__name__in=excluded_org_names)
        .select_related("note_owner__organization")
        .annotate(
            organization_name=F("note_owner__organization__name"),
            license_type=F("note_owner__license_type"),
            note_owner_email=F("note_owner__email"),
            meeting_category=Case(
                When(Q(meeting_type__category__isnull=False), then=F("meeting_type__category")),
                When(Q(metadata__meeting_type__isnull=False), then=KeyTextTransform("meeting_type", "metadata")),
                default=Value("unknown"),
                output_field=CharField(),
            ),
            action_items_count=Count("task", filter=Q(task__is_deleted=False), distinct=True),
            completed_action_items_count=Count(
                "task", filter=Q(task__is_deleted=False, task__completed=True), distinct=True
            ),
            incomplete_action_items_count=Count(
                "task", filter=Q(task__is_deleted=False, task__completed=False), distinct=True
            ),
            overdue_action_items_count=Count(
                "task",
                filter=Q(task__is_deleted=False, task__completed=False, task__due_date__lt=timezone.now()),
                distinct=True,
            ),
            email_generated=Case(
                When(~Q(follow_up_email_contents=None), then=Value(True)),
                default=Value(False),
                output_field=CharField(),
            ),
            client_uuid=Case(
                When(
                    Q(client__uuid__isnull=False)
                    & Q(
                        client__uuid__regex=r"^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"
                    ),
                    Cast(KeyTextTransform("uuid", "client"), output_field=UUIDField()),
                ),
                default=Value(None),
            ),
            crm_id=Case(
                When(
                    Q(client_uuid__isnull=False),
                    then=Subquery(Client.objects.filter(uuid=(OuterRef("client_uuid"))).values("crm_id")[:1]),
                ),
                default=Value("N/A"),
            ),
            meetingbot_link=F("meetingbot__meeting_link"),
            ask_anything_usage=Count("search_queries", distinct=True),
        )
        .order_by("created")
        .values(
            "id",
            "data_source",
            "organization_name",
            "license_type",
            "status",
            "metadata",
            "meetingbot__uuid",
            "meetingbot_link",
            "note_owner_email",
            "created",
            "crm_id",
            "meeting_category",
            "salesforce_case_id",
            "is_deleted",
            "action_items_count",
            "completed_action_items_count",
            "incomplete_action_items_count",
            "overdue_action_items_count",
            "email_generated",
            "ask_anything_usage",
        )
    )

    notes_data = []
    for note in notes:
        # Extract metadata or use an empty dict if None
        metadata = note.get("metadata") or {}

        tags = metadata.get("tags", [])
        tags_string = "| ".join(tags) if tags else ""

        # Extract creation date
        creation_date = note["created"].strftime("%Y-%m-%d")
        internal_attendees = 0
        external_attendees = 0
        meeting_status = note.get("status", "N/A")

        # Attendee count - only for processed/finalized notes
        if meeting_status in [Note.PROCESSING_STATUS.finalized, Note.PROCESSING_STATUS.processed]:
            note_id = note["id"]
            if note_id in attendee_counts:
                internal_attendees = attendee_counts[note_id]["internal"]
                external_attendees = attendee_counts[note_id]["external"]

        if meeting_status in [
            Note.PROCESSING_STATUS.finalized,
            Note.PROCESSING_STATUS.processed,
            Note.PROCESSING_STATUS.uploaded,
            Note.PROCESSING_STATUS.scheduled,
        ]:
            creation_time: datetime | None = note.get("created")
            creation_date = (
                datetime.fromisoformat(creation_time.isoformat().replace(" ", "T")).date() if creation_time else "N/A"
            )

        meetingbot_data = {"uuid": note.get("meetingbot__uuid"), "meeting_link": note.get("meetingbot_link")}
        meeting_capture_type = get_meeting_type(note.get("data_source"), meetingbot_data)

        # Compile data for each note
        notes_data.append(
            {
                "ID": note["id"],
                "Organization Name": note.get("organization_name", "N/A"),
                "License Type": note.get("license_type", "N/A"),
                "Meeting Mode": meeting_capture_type,
                "Meeting Type": note.get("meeting_category", "N/A"),
                "Note Status": meeting_status,
                "Meeting Duration": metadata.get("meeting_duration", "N/A"),
                "Note Owner Email": note.get("note_owner_email", "N/A"),
                "Creation Date": creation_date,
                "Salesforce Case ID": note.get("salesforce_case_id", "N/A"),
                "CRM ID": note.get("crm_id", "N/A"),
                "Is Deleted": note.get("is_deleted", False),
                "Action Items Count": note.get("action_items_count", 0),
                "Completed Action Items Count": note.get("completed_action_items_count", 0),
                "Incomplete Action Items Count": note.get("incomplete_action_items_count", 0),
                "Overdue Action Items Count": note.get("overdue_action_items_count", 0),
                "Email to Client Generated": "Yes" if note.get("email_generated", "No") else "No",
                "Internal Attendees Count": internal_attendees,
                "External Attendees Count": external_attendees,
                "Ask Anything Usage": note.get("ask_anything_usage", 0),
                "Tags": tags_string,
            }
        )

    return notes_data


def get_all_metrics() -> list[str]:
    # Generates monthly and weekly metrics per organization
    metrics = get_org_metrics(6)  # type: ignore[no-untyped-call]
    processed_data, meeting_categories = process_metrics(metrics)  # type: ignore[no-untyped-call]
    metrics_filename = csv_filename_with_timestamp("org_metrics_tables")
    write_metrics_to_csv(processed_data, meeting_categories, metrics_filename)

    # Generates HTML for org
    html_output = generate_html_for_org_metrics(processed_data, meeting_categories, "Wealth Management")  # type: ignore[no-untyped-call]

    # Generates monthly and weekly metrics for all orgs
    user_month_data, all_months = get_monthly_users_by_org()  # type: ignore[no-untyped-call]
    monthly_users_filename = csv_filename_with_timestamp("monthly_users_by_org")
    write_monthly_users_to_csv(user_month_data, all_months, monthly_users_filename)

    # Monthly users total
    monthly_metrics = get_monthly_aggregate_metrics()  # type: ignore[no-untyped-call]
    monthly_aggregate_filename = csv_filename_with_timestamp("monthly_aggregate_metrics_filtered")
    write_monthly_aggregate_metrics_to_csv(monthly_metrics, monthly_aggregate_filename)

    # Generate Notes Information csv
    summary_data = get_per_note_information()
    notes_information_filename = csv_filename_with_timestamp("notes_information")
    write_notes_information_to_csv(summary_data, notes_information_filename)

    return [
        metrics_filename,
        monthly_users_filename,
        monthly_aggregate_filename,
        notes_information_filename,
    ]
