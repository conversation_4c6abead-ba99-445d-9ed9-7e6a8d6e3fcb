import json
import sys
import uuid

from django.core.serializers import deserialize
from django.db import transaction


def load_notes_with_new_ids(json_file, client, user):  # type: ignore[no-untyped-def]
    print("Starting the load_notes_with_new_ids function...")
    try:
        with open(json_file, "r") as file:
            notes_data = file.read()

        print(f"Successfully read data from {json_file}")

        new_notes = []

        with transaction.atomic():
            for deserialized_object in deserialize("json", notes_data):
                new_note = deserialized_object.object

                new_note.pk = None
                new_note.uuid = uuid.uuid4()  # type: ignore[attr-defined]
                new_note.save()
                # clear foreign keys
                for field in new_note._meta.fields:
                    if field.is_relation:
                        setattr(new_note, field.name, None)
                # add new foreign keys
                note_owner = user
                client = client
                new_note.note_owner = note_owner  # type: ignore[attr-defined]
                new_note.metadata["user_name"] = note_owner.name  # type: ignore[attr-defined]
                new_note.metadata["user_uuid"] = str(note_owner.uuid)  # type: ignore[attr-defined]
                new_note.metadata["meeting_name"] = "Client Meeting"  # type: ignore[attr-defined]
                if client:
                    new_client = {"name": client.name, "uuid": str(client.uuid)}
                    new_note.client = new_client  # type: ignore[attr-defined]

                new_notes.append(new_note)
                new_note.authorized_users.set([note_owner])  # type: ignore[attr-defined]
                new_note.save()

                if len(new_notes) % 100 == 0:  # Print progress every 100 notes
                    print(f"Processed {len(new_notes)} notes...")

        print(f"Successfully created {len(new_notes)} new Notes with new PKs and UUIDs")
        return len(new_notes)

    except FileNotFoundError:
        print(f"File not found: {json_file}", file=sys.stderr)
    except json.JSONDecodeError:
        print(f"Invalid JSON in file: {json_file}", file=sys.stderr)
    except Exception as e:
        print(f"An error occurred: {str(e)}", file=sys.stderr)
        import traceback

        traceback.print_exc()

    return 0
