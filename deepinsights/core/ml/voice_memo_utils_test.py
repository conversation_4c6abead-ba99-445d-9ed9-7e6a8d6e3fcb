import pytest
from django.contrib.auth import get_user_model
from django.test import TestCase

from deepinsights.core.ml.voice_memo_utils import parse_response_into_json, replace_aliases_with_names
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.note import Note


class TestReplaceAliasesWithNames(TestCase):
    def setUp(self):  # type: ignore[no-untyped-def]
        User = get_user_model()
        self.user1 = User.objects.create(username="user1", first_name="<PERSON>", last_name="<PERSON><PERSON>")
        self.user2 = User.objects.create(username="user2", first_name="<PERSON>", last_name="<PERSON>")

        self.note = Note.objects.create(
            diarized_trans_with_names="Speaker0 0:00:00.400-->0:00:20.220 Hello. I am testing the new workflow. "
            "Speaker1 0:00:21.800-->0:00:40.854 Training these models is actually more efficient. "
            "Speaker0 0:00:41.475-->0:00:43.655 And that is the conclusion.",
        )

        Attendee.objects.create(note=self.note, user=self.user1, speaker_alias="Speaker0")  # noqa: F821
        Attendee.objects.create(note=self.note, user=self.user2, speaker_alias="Speaker1")

    def test_replace_aliases_with_names(self):  # type: ignore[no-untyped-def]
        result = replace_aliases_with_names(self.note)
        expected_output = (
            "John Doe 0:00:00.400-->0:00:20.220 Hello. I am testing the new workflow. "
            "Jane Smith 0:00:21.800-->0:00:40.854 Training these models is actually more efficient. "
            "John Doe 0:00:41.475-->0:00:43.655 And that is the conclusion."
        )
        self.assertEqual(result, expected_output)

    def test_replace_aliases_with_names_no_transcript(self):  # type: ignore[no-untyped-def]
        note_without_transcript = Note.objects.create()
        result = replace_aliases_with_names(note_without_transcript)
        self.assertIsNone(result)

    def test_replace_aliases_with_names_partial_replacement(self):  # type: ignore[no-untyped-def]
        self.note.diarized_trans_with_names = (
            "Speaker0 0:00:00.400-->0:00:20.220 Hello there. "
            "Speaker1 0:00:21.800-->0:00:40.854 How are you? "
            "Speaker2 0:00:41.475-->0:00:43.655 I'm new here."
        )
        self.note.save()

        result = replace_aliases_with_names(self.note)
        expected_output = (
            "John Doe 0:00:00.400-->0:00:20.220 Hello there. "
            "Jane Smith 0:00:21.800-->0:00:40.854 How are you? "
            "Speaker2 0:00:41.475-->0:00:43.655 I'm new here."
        )
        self.assertEqual(result, expected_output)

    def test_replace_aliases_with_names_no_replacement(self):  # type: ignore[no-untyped-def]
        self.note.diarized_trans_with_names = (
            "Speaker3 0:00:00.400-->0:00:20.220 Hello! " "Speaker4 0:00:21.800-->0:00:40.854 Hi there!"
        )
        self.note.save()

        result = replace_aliases_with_names(self.note)
        self.assertEqual(result, self.note.diarized_trans_with_names)

    def test_replace_aliases_with_names_empty_transcript(self):  # type: ignore[no-untyped-def]
        self.note.diarized_trans_with_names = ""
        self.note.save()

        result = replace_aliases_with_names(self.note)
        self.assertEqual(result, "")


@pytest.mark.parametrize(
    "test_input,expected",
    [
        # Test case 1: Clean JSON
        ('{"key": "value", "numbers": [1, 2, 3]}', {"key": "value", "numbers": [1, 2, 3]}),
        # Test case 2: JSON with code blocks
        ('```json\n{"key": "value"}\n```', {"key": "value"}),
        # Test case 3: JSON with formatting issues
        ('{\n"key": "value",\n"array": [1,2,],\n}', {"key": "value", "array": [1, 2]}),
        # Test case 4: Nested JSON
        (
            """
            {
                "meeting": {
                    "topic": "Financial Review",
                    "points": ["point1", "point2"],
                    "metadata": {"date": "2024-01-01"}
                }
            }
            """,
            {
                "meeting": {
                    "topic": "Financial Review",
                    "points": ["point1", "point2"],
                    "metadata": {"date": "2024-01-01"},
                }
            },
        ),
    ],
)
def test_parse_response_into_json(test_input, expected):  # type: ignore[no-untyped-def]
    result = parse_response_into_json(test_input)
    assert result == expected


def test_parse_response_into_json_invalid_input():  # type: ignore[no-untyped-def]
    # Test completely invalid JSON
    invalid_input = "This is not JSON at all"
    assert parse_response_into_json(invalid_input) == {}

    # Test empty input
    assert parse_response_into_json("") == {}

    # Test malformed JSON
    malformed_input = '{"key": "value" "missing": "comma"}'
    assert parse_response_into_json(malformed_input) == {}
