import json
from unittest.mock import ANY, MagicMock, patch

from django.test import TestCase, override_settings

from deepinsights.core.ml.genai import ModelName
from deepinsights.core.ml.models import Summary, TasksAndTakeaways
from deepinsights.core.ml.voice_memo import (
    _clean_prompt_result_plain_text,
    _get_agenda_for_note,
    _get_llm_prompt_output_with_retries,
    _validate_schema_and_json,
    get_agenda_completion_info,
    get_agenda_follow_up_data,
    get_personalized_summary,
    get_summary,
    get_tasks_and_takeaways,
)
from deepinsights.meetingsapp.models.client_interaction import ClientInteraction
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.prompt import Prompt
from deepinsights.meetingsapp.models.structured_meeting_data import (
    StructuredMeetingData,
    StructuredMeetingDataTemplate,
    StructuredMeetingDataTemplateRule,
)
from deepinsights.meetingsapp.models.structured_meeting_data_schema import StructuredMeetingDataSchema
from deepinsights.ml_agents.models.agent_config import AgentConfig
from deepinsights.users.models.user import User


class TestGetSummary(TestCase):
    def setUp(self) -> None:
        # Create test user
        self.user = User.objects.create(
            username="testuser", first_name="Test", last_name="User", metadata={"user_context": "Test context"}
        )

        # Create meeting types
        self.client_meeting_type = MeetingType.objects.create(
            name="Client Meeting",
            internal_name="Standard Client Meeting",
            category=MeetingType.Category.CLIENT,
            everyone=True,
        )

        self.internal_meeting_type = MeetingType.objects.create(
            name="Internal Meeting",
            internal_name="Standard Internal Meeting",
            category=MeetingType.Category.INTERNAL,
            everyone=True,
        )

        self.debrief_meeting_type = MeetingType.objects.create(
            name="Debrief",
            internal_name="Post-Meeting Debrief",
            category=MeetingType.Category.DEBRIEF,
            everyone=True,
        )

        # Create both regular and detailed prompts to ensure we can verify which one was used
        Prompt.objects.create(
            name="Client Meeting Tasks",
            unique_name="client_meeting_tasks_and_takeaways",
            user_prompt="REGULAR Context: {context}\nTranscript: {transcript}",
            version="test",
        )

        Prompt.objects.create(
            name="Client Meeting Tasks Detailed",
            unique_name="client_meeting_tasks_and_takeaways_detailed",
            user_prompt="DETAILED Context: {context}\nTranscript: {transcript}",
            version="test",
        )

        Prompt.objects.get_or_create(
            name="Client Meeting Summary",
            unique_name="client_meeting_summary",
            defaults={"user_prompt": "Context: {context}\nTranscript: {transcript}"},
            version="test",
        )

        # Create test note with client meeting type
        self.note = Note.objects.create(
            note_owner=self.user, meeting_type=self.client_meeting_type, metadata={"meeting_duration": 3600}
        )

        # Create personalized summary template
        self.template = StructuredMeetingDataTemplate.objects.create(
            title="Personalized Summary Template",
            internal_name="Personalized Summary Template",
            kind="personalized_summary",
            initial_data={
                "review_entries": [
                    {
                        "id": "unique_id_1",
                        "kind": "header",
                        "topic": "PERSONAL NOTES:",
                    },
                    {
                        "id": "unique_id_2",
                        "kind": "header",
                        "topic": "INVESTMENT PLANNING:",
                    },
                ]
            },
        )

        # Create template rule
        self.template_rule = StructuredMeetingDataTemplateRule.objects.create(
            internal_name="Personalized Summary Rule", all_meetings=False, everyone=False
        )
        self.template_rule.follow_up_templates.add(self.template)
        self.template_rule.meeting_types.add(self.client_meeting_type)
        self.template_rule.users.add(self.user)

    @patch("deepinsights.core.ml.voice_memo.call_model")
    def test_successful_summary_generation(self, mock_call_model: MagicMock) -> None:
        mock_response = {
            "sections": [
                {"topic": "Topic 1", "bullets": ["Point 1", "Point 2"]},
                {"topic": "Topic 2", "bullets": ["Point 3"]},
            ]
        }
        mock_call_model.return_value = json.dumps(mock_response)
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        result, has_content = get_summary(self.note, transcript, version="test")
        expected = Summary(
            sections=[
                {"topic": "Topic 1", "bullets": ["Point 1", "Point 2"]},
                {"topic": "Topic 2", "bullets": ["Point 3"]},
            ]
        )
        self.assertEqual(result, expected)
        self.assertTrue(has_content)
        mock_call_model.assert_called_once()

    @patch("deepinsights.core.ml.voice_memo.call_model")
    def test_empty_transcript(self, mock_call_model: MagicMock) -> None:
        result, has_content = get_summary(self.note, "", version="test")
        expected = Summary()
        self.assertEqual(result, expected)
        self.assertFalse(has_content)
        mock_call_model.assert_not_called()

    @patch("deepinsights.core.ml.voice_memo.call_model")
    def test_short_transcript(self, mock_call_model: MagicMock) -> None:
        result, has_content = get_summary(self.note, "Short transcript", version="test")
        expected = Summary()
        self.assertEqual(result, expected)
        self.assertFalse(has_content)
        mock_call_model.assert_not_called()

    @patch("deepinsights.core.ml.voice_memo.call_model")
    @override_settings(LLM_MAX_RETRIES=1)
    def test_retry_on_parse_failure(self, mock_call_model: MagicMock) -> None:
        # Invalid JSON on first try, valid JSON on retry
        valid_response = json.dumps({"sections": []})
        mock_call_model.side_effect = ["invalid json", valid_response]
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        result, has_content = get_summary(self.note, transcript, version="test")
        expected = Summary(sections=[])
        self.assertEqual(result, expected)
        # Even though the LLM didn't return anything useful in this mocked test, we still have
        # content because it did process the transcript and return something. In real use cases, it
        # is not as likely that the LLM will return absolutely nothing.
        self.assertTrue(has_content)
        # Should make 2 calls with LLM_MAX_RETRIES=1
        self.assertEqual(mock_call_model.call_count, 2)

    @patch("deepinsights.core.ml.voice_memo.call_model")
    @override_settings(LLM_MAX_RETRIES=1)
    def test_max_retries_reached(self, mock_call_model: MagicMock) -> None:
        # Using side_effect to provide exactly 2 invalid responses
        mock_call_model.side_effect = ["invalid json", "invalid json"]
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        result, has_content = get_summary(self.note, transcript, version="test")
        expected = Summary()
        self.assertEqual(result, expected)
        # One call plus one retry = 2 calls
        self.assertEqual(mock_call_model.call_count, 2)

    @patch("deepinsights.core.ml.voice_memo.call_model")
    def test_prompt_error(self, mock_call_model: MagicMock) -> None:
        Prompt.objects.all().delete()
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        result, has_content = get_summary(self.note, transcript, version="test")
        expected = Summary()
        self.assertFalse(has_content)
        self.assertEqual(result, expected)
        mock_call_model.assert_not_called()

    @patch("deepinsights.core.ml.voice_memo.get_personalized_summary")
    @patch("deepinsights.core.ml.voice_memo.call_model")
    def test_personalized_summary_used_when_available(
        self, mock_call_model: MagicMock, mock_get_personalized_summary: MagicMock
    ) -> None:
        """Test that personalized summary is used when available."""
        # Mock personalized summary response
        personalized_summary = Summary(
            sections=[
                {
                    "topic": "PERSONAL NOTES",
                    "bullets": [
                        "The client anticipates minimal income next year.",
                        "The client is considering expanding their consulting business.",
                    ],
                },
                {"topic": "INVESTMENT PLANNING", "bullets": ["We discussed the client's investment portfolio."]},
            ]
        )
        mock_get_personalized_summary.return_value = (personalized_summary, True)
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        result, has_content = get_summary(self.note, transcript, version="test")
        self.assertEqual(result, personalized_summary)
        self.assertTrue(has_content)
        mock_get_personalized_summary.assert_called_once_with(self.note, transcript)
        mock_call_model.assert_not_called()

    @patch("deepinsights.core.ml.voice_memo.get_personalized_summary")
    @patch("deepinsights.core.ml.voice_memo.call_model")
    def test_fallback_to_default_summary_when_personalized_fails(
        self, mock_call_model: MagicMock, mock_get_personalized_summary: MagicMock
    ) -> None:
        """Test that falls back to default summary when personalized summary fails."""
        # Mock personalized summary failure
        mock_get_personalized_summary.return_value = (Summary(sections=[]), False)

        # Mock default summary response
        mock_response = {
            "sections": [
                {"topic": "Topic 1", "bullets": ["Point 1", "Point 2"]},
                {"topic": "Topic 2", "bullets": ["Point 3"]},
            ]
        }
        mock_call_model.return_value = json.dumps(mock_response)
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        result, has_content = get_summary(self.note, transcript, version="test")
        expected = Summary(
            sections=[
                {"topic": "Topic 1", "bullets": ["Point 1", "Point 2"]},
                {"topic": "Topic 2", "bullets": ["Point 3"]},
            ]
        )
        self.assertEqual(result, expected)
        self.assertTrue(has_content)
        mock_get_personalized_summary.assert_called_once_with(self.note, transcript)
        mock_call_model.assert_called_once()

    @patch("deepinsights.core.ml.voice_memo.call_model")
    def test_fallback_to_default_summary_when_no_personalized_template(self, mock_call_model: MagicMock) -> None:
        """Test that falls back to default summary when no personalized template exists."""
        # Delete the template rule to simulate no personalized template
        StructuredMeetingDataTemplateRule.objects.all().delete()

        # Mock default summary response
        mock_response = {
            "sections": [
                {"topic": "Topic 1", "bullets": ["Point 1", "Point 2"]},
                {"topic": "Topic 2", "bullets": ["Point 3"]},
            ]
        }
        mock_call_model.return_value = json.dumps(mock_response)
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        result, has_content = get_summary(self.note, transcript, version="test")
        expected = Summary(
            sections=[
                {"topic": "Topic 1", "bullets": ["Point 1", "Point 2"]},
                {"topic": "Topic 2", "bullets": ["Point 3"]},
            ]
        )
        self.assertEqual(result, expected)
        self.assertTrue(has_content)
        mock_call_model.assert_called_once()

    @patch("deepinsights.core.ml.voice_memo.call_model")
    @patch("deepinsights.flags.flagdefs.Flags.EnableReasoningModels.is_active_for_user")
    def test_reasoning_models_flag_active_uses_o4_mini_for_summary(
        self, mock_is_active_for_user: MagicMock, mock_call_model: MagicMock
    ) -> None:
        """Test that O4_MINI model is used for summary when EnableReasoningModels flag is active."""
        # Setup the flag to be active
        mock_is_active_for_user.return_value = True

        # Mock the LLM response
        mock_response = {
            "sections": [
                {"topic": "Topic 1", "bullets": ["Point 1", "Point 2"]},
                {"topic": "Topic 2", "bullets": ["Point 3"]},
            ]
        }
        mock_call_model.return_value = json.dumps(mock_response)
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        # Call the function
        _, has_content = get_summary(self.note, transcript, version="test")

        self.assertTrue(has_content)

        # Check that call_model was called with O4_MINI model
        mock_call_model.assert_called_once()
        call_args = mock_call_model.call_args
        self.assertEqual(call_args[1]["model"], ModelName.O4_MINI)

        # Verify the flag was checked with the correct user
        mock_is_active_for_user.assert_called_with(self.note.note_owner)

    @patch("deepinsights.core.ml.voice_memo.call_model")
    @patch("deepinsights.flags.flagdefs.Flags.EnableReasoningModels.is_active_for_user")
    def test_reasoning_models_flag_inactive_uses_gpt4o_for_summary(
        self, mock_is_active_for_user: MagicMock, mock_call_model: MagicMock
    ) -> None:
        """Test that GPT_4O model is used for summary when EnableReasoningModels flag is inactive."""
        # Setup the flag to be inactive
        mock_is_active_for_user.return_value = False

        # Mock the LLM response
        mock_response = {
            "sections": [
                {"topic": "Topic 1", "bullets": ["Point 1", "Point 2"]},
                {"topic": "Topic 2", "bullets": ["Point 3"]},
            ]
        }
        mock_call_model.return_value = json.dumps(mock_response)
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        # Call the function
        _, has_content = get_summary(self.note, transcript, version="test")
        self.assertTrue(has_content)

        # Check that call_model was called with GPT_4O model
        mock_call_model.assert_called_once()
        call_args = mock_call_model.call_args
        self.assertEqual(call_args[1]["model"], ModelName.GPT_4O)

        # Verify the flag was checked with the correct user
        mock_is_active_for_user.assert_called_with(self.note.note_owner)


class TestGetTasksAndTakeaways(TestCase):
    def setUp(self) -> None:
        # Create test user
        self.user = User.objects.create(
            username="testuser", first_name="Test", last_name="User", metadata={"user_context": "Test context"}
        )

        # Create meeting types
        self.client_meeting_type = MeetingType.objects.create(
            name="Client Meeting",
            internal_name="Standard Client Meeting",
            category=MeetingType.Category.CLIENT,
            everyone=True,
        )

        self.internal_meeting_type = MeetingType.objects.create(
            name="Internal Meeting",
            internal_name="Standard Internal Meeting",
            category=MeetingType.Category.INTERNAL,
            everyone=True,
        )

        self.debrief_meeting_type = MeetingType.objects.create(
            name="Debrief", internal_name="Post-Meeting Debrief", category=MeetingType.Category.DEBRIEF, everyone=True
        )

        # Create test note with client meeting type
        self.note = Note.objects.create(
            note_owner=self.user, meeting_type=self.client_meeting_type, metadata={"meeting_duration": 3600}
        )

    def test_empty_transcript(self) -> None:
        result, has_content = get_tasks_and_takeaways(self.note, "", version="test")

        expected = TasksAndTakeaways(
            key_takeaways=[],
            action_items=[],
            advisor_notes=[],
            keywords=[],
        )
        self.assertEqual(result, expected)
        self.assertFalse(has_content)

    def test_short_transcript(self) -> None:
        result, has_content = get_tasks_and_takeaways(self.note, "too short", version="test")

        expected = TasksAndTakeaways(
            key_takeaways=[],
            action_items=[],
            advisor_notes=[],
            keywords=[],
        )
        self.assertEqual(result, expected)
        self.assertFalse(has_content)

    @patch("deepinsights.core.ml.voice_memo.call_model")
    @override_settings(LLM_MAX_RETRIES=1)
    def test_retry_on_parse_failure(self, mock_call_model: MagicMock) -> None:
        # Invalid JSON on first try, valid JSON on retry
        valid_response = json.dumps({"key_takeaways": [], "action_items": [], "advisor_notes": [], "keywords": []})
        mock_call_model.side_effect = ["invalid json", valid_response]
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        result, has_content = get_tasks_and_takeaways(self.note, transcript, version="test")

        expected = TasksAndTakeaways(
            key_takeaways=[],
            action_items=[],
            advisor_notes=[],
            keywords=[],
        )
        self.assertEqual(result, expected)
        self.assertEqual(mock_call_model.call_count, 2)
        # Even though the LLM didn't return anything useful in this mocked test, we still have
        # content because it did process the transcript and return something. In real use cases, it
        # is not as likely that the LLM will return absolutely nothing.
        self.assertTrue(has_content)

    @patch("deepinsights.core.ml.voice_memo.call_model")
    @override_settings(LLM_MAX_RETRIES=1)
    def test_max_retries_reached(self, mock_call_model: MagicMock) -> None:
        # Using side_effect to provide exactly 2 invalid responses
        mock_call_model.side_effect = ["invalid json", "invalid json"]
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        result, has_content = get_tasks_and_takeaways(self.note, transcript, version="test")
        expected = TasksAndTakeaways()
        self.assertEqual(result, expected)
        self.assertFalse(has_content)
        self.assertEqual(mock_call_model.call_count, 2)

    @patch("deepinsights.core.ml.voice_memo.call_model")
    def test_prompt_error(self, mock_call_model: MagicMock) -> None:
        # Delete the prompt to cause an error
        Prompt.objects.all().delete()
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        result, has_content = get_tasks_and_takeaways(self.note, transcript, version="test")
        expected = TasksAndTakeaways()
        self.assertEqual(result, expected)
        self.assertFalse(has_content)
        mock_call_model.assert_not_called()

    @patch("deepinsights.core.ml.voice_memo.call_model")
    def test_detailed_prompt_used_for_client_meetings(self, mock_call_model: MagicMock) -> None:
        # Mock the LLM response
        mock_response = {
            "key_takeaways": ["Takeaway 1"],
            "action_items": [{"description": "Action 1", "assignee": "John", "due_date": "2023-10-30"}],
            "advisor_notes": ["Note 1"],
            "keywords": ["Keyword1"],
        }
        mock_call_model.return_value = json.dumps(mock_response)
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        # Call the function
        _, has_content = get_tasks_and_takeaways(self.note, transcript, version="test")

        self.assertTrue(has_content)

        # Check that the detailed prompt was passed to call_model
        # We extract the first argument of the first call to call_model
        prompt_text_used = mock_call_model.call_args[0][0]

        # Verify the detailed prompt was used
        self.assertIn("DETAILED Context:", prompt_text_used)
        self.assertNotIn("REGULAR Context:", prompt_text_used)

    @patch("deepinsights.core.ml.voice_memo.call_model")
    def test_action_item_description_formatting(self, mock_call_model: MagicMock) -> None:
        """Test that action item descriptions are properly formatted with assignee names."""
        # Create a valid JSON response that parse_llm_response can handle
        mock_llm_response = json.dumps(
            {
                "key_takeaways": ["Sample takeaway"],
                "action_items": [
                    {"description": "Task 1", "assignee": "John", "due_date": "2023-10-30"},
                    {"description": "Task 2", "assignee": "", "due_date": "2023-11-15"},
                    {"description": "Task 3", "assignee": "", "due_date": "2023-12-01"},
                ],
                "advisor_notes": ["Sample note"],
                "keywords": ["sample", "test"],
            }
        )

        # Configure the mock to return our JSON response
        mock_call_model.return_value = mock_llm_response

        # Call the function with a non-empty transcript
        transcript = (
            "This is a sample transcript with more than ten words to ensure it passes the length check in the function"
        )
        result, has_content = get_tasks_and_takeaways(self.note, transcript, version="test")

        # Verify action items are formatted as strings with assignee prefixes
        expected_action_items = [
            "[Assignee: John] Task 1",
            "[Assignee: Unassigned] Task 2",
            "[Assignee: Unassigned] Task 3",
        ]
        self.assertEqual([a.description for a in result.action_items], expected_action_items)
        self.assertTrue(has_content)

    @patch("deepinsights.core.ml.voice_memo.call_model")
    @patch("deepinsights.flags.flagdefs.Flags.EnableReasoningModels.is_active_for_user")
    def test_reasoning_models_flag_active_uses_o4_mini(
        self, mock_is_active_for_user: MagicMock, mock_call_model: MagicMock
    ) -> None:
        """Test that O4_MINI model is used when EnableReasoningModels flag is active."""
        # Setup the flag to be active
        mock_is_active_for_user.return_value = True

        # Mock the LLM response
        mock_response = {
            "key_takeaways": ["Takeaway 1"],
            "action_items": [{"description": "Action 1", "assignee": "John", "due_date": "2023-10-30"}],
            "advisor_notes": ["Note 1"],
            "keywords": ["Keyword1"],
        }
        mock_call_model.return_value = json.dumps(mock_response)
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        # Call the function
        _, has_content = get_tasks_and_takeaways(self.note, transcript, version="test")

        self.assertTrue(has_content)

        # Check that call_model was called with O4_MINI model
        mock_call_model.assert_called_once()
        call_args = mock_call_model.call_args
        self.assertEqual(call_args[1]["model"], ModelName.O4_MINI)

        # Verify the flag was checked with the correct user
        mock_is_active_for_user.assert_called_with(self.note.note_owner)

    @patch("deepinsights.core.ml.voice_memo.call_model")
    @patch("deepinsights.flags.flagdefs.Flags.EnableReasoningModels.is_active_for_user")
    def test_reasoning_models_flag_inactive_uses_gpt4o(
        self, mock_is_active_for_user: MagicMock, mock_call_model: MagicMock
    ) -> None:
        """Test that GPT_4O model is used when EnableReasoningModels flag is inactive."""
        # Setup the flag to be inactive
        mock_is_active_for_user.return_value = False

        # Mock the LLM response
        mock_response = {
            "key_takeaways": ["Takeaway 1"],
            "action_items": [{"description": "Action 1", "assignee": "John", "due_date": "2024-10-30"}],
            "advisor_notes": ["Note 1"],
            "keywords": ["Keyword1"],
        }
        mock_call_model.return_value = json.dumps(mock_response)
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        # Call the function
        _, has_content = get_tasks_and_takeaways(self.note, transcript, version="test")
        self.assertTrue(has_content)

        # Check that call_model was called with GPT_4O model
        mock_call_model.assert_called_once()
        call_args = mock_call_model.call_args
        self.assertEqual(call_args[1]["model"], ModelName.GPT_4O)

        # Verify the flag was checked with the correct user
        mock_is_active_for_user.assert_called_with(self.note.note_owner)


class TestGetPersonalizedSummary(TestCase):
    def setUp(self) -> None:
        # Create test user
        self.user = User.objects.create(
            username="testuser", first_name="Test", last_name="User", metadata={"user_context": "Test context"}
        )

        # Create meeting type
        self.client_meeting_type = MeetingType.objects.create(
            name="Client Meeting",
            internal_name="Standard Client Meeting",
            category=MeetingType.Category.CLIENT,
            everyone=True,
        )

        # Create test note
        self.note = Note.objects.create(
            note_owner=self.user, meeting_type=self.client_meeting_type, metadata={"meeting_duration": 3600}
        )

        # Create personalized summary template
        self.template = StructuredMeetingDataTemplate.objects.create(
            title="Personalized Summary Template",
            internal_name="Personalized Summary Template",
            kind="personalized_summary",
            initial_data={
                "review_entries": [
                    {
                        "id": "unique_id_1",
                        "kind": "header",
                        "topic": "PERSONAL NOTES:",
                    },
                    {
                        "id": "unique_id_2",
                        "kind": "header",
                        "topic": "INVESTMENT PLANNING:",
                    },
                ]
            },
        )

        # Create template rule
        self.template_rule = StructuredMeetingDataTemplateRule.objects.create(
            internal_name="Personalized Summary Rule", all_meetings=False, everyone=False
        )
        self.template_rule.follow_up_templates.add(self.template)
        self.template_rule.meeting_types.add(self.client_meeting_type)
        self.template_rule.users.add(self.user)

    @patch("deepinsights.core.ml.voice_memo.get_follow_up_structured_data")
    def test_successful_personalized_summary(self, mock_get_follow_up_structured_data: MagicMock) -> None:
        """Test successful generation of personalized summary."""
        mock_response = {
            "review_entries": [
                {
                    "id": "unique_id_1",
                    "kind": "header",
                    "topic": "PERSONAL NOTES:",
                    "subentries": [
                        {
                            "id": "unique_id_1_bullet_1",
                            "kind": "toggle",
                            "topic": "The client anticipates minimal income next year.",
                        },
                        {
                            "id": "unique_id_1_bullet_2",
                            "kind": "toggle",
                            "topic": "The client is considering expanding their consulting business.",
                        },
                    ],
                },
                {
                    "id": "unique_id_2",
                    "kind": "header",
                    "topic": "INVESTMENT PLANNING:",
                    "subentries": [
                        {
                            "id": "unique_id_2_bullet_1",
                            "kind": "bullet",
                            "topic": "We discussed the client's investment portfolio.",
                        }
                    ],
                },
            ]
        }
        mock_get_follow_up_structured_data.return_value = mock_response
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        result, has_content = get_personalized_summary(self.note, transcript)
        expected = Summary(
            sections=[
                {
                    "topic": "PERSONAL NOTES",
                    "bullets": [
                        "The client anticipates minimal income next year.",
                        "The client is considering expanding their consulting business.",
                    ],
                },
                {"topic": "INVESTMENT PLANNING", "bullets": ["We discussed the client's investment portfolio."]},
            ]
        )
        self.assertEqual(result, expected)
        self.assertTrue(has_content)
        mock_get_follow_up_structured_data.assert_called_once()

    @patch("deepinsights.core.ml.voice_memo.get_follow_up_structured_data")
    def test_no_personalized_template(self, mock_get_follow_up_structured_data: MagicMock) -> None:
        """Test behavior when no personalized template exists."""
        # Delete the template rule
        StructuredMeetingDataTemplateRule.objects.all().delete()
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        result, has_content = get_personalized_summary(self.note, transcript)
        expected = Summary(sections=[])
        self.assertEqual(result, expected)
        self.assertFalse(has_content)
        mock_get_follow_up_structured_data.assert_not_called()

    @patch("deepinsights.core.ml.voice_memo.get_follow_up_structured_data")
    def test_empty_template_initial_data(self, mock_get_follow_up_structured_data: MagicMock) -> None:
        """Test behavior when template has no initial data."""
        self.template.initial_data = None
        self.template.save()
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        result, has_content = get_personalized_summary(self.note, transcript)
        expected = Summary(sections=[])
        self.assertEqual(result, expected)
        self.assertFalse(has_content)
        mock_get_follow_up_structured_data.assert_not_called()

    @patch("deepinsights.core.ml.voice_memo.get_follow_up_structured_data")
    def test_llm_error_handling(self, mock_get_follow_up_structured_data: MagicMock) -> None:
        """Test error handling when LLM call fails."""
        mock_get_follow_up_structured_data.side_effect = Exception("LLM error")
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        result, has_content = get_personalized_summary(self.note, transcript)
        expected = Summary(sections=[])
        self.assertEqual(result, expected)
        self.assertFalse(has_content)
        mock_get_follow_up_structured_data.assert_called_once()

    @patch("deepinsights.core.ml.voice_memo.get_follow_up_structured_data")
    def test_invalid_llm_response(self, mock_get_follow_up_structured_data: MagicMock) -> None:
        """Test handling of invalid LLM response."""
        mock_get_follow_up_structured_data.return_value = {}
        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."

        result, has_content = get_personalized_summary(self.note, transcript)
        expected = Summary(sections=[])
        self.assertEqual(result, expected)
        self.assertFalse(has_content)
        mock_get_follow_up_structured_data.assert_called_once()

    @patch("deepinsights.core.ml.voice_memo.get_follow_up_structured_data")
    def test_prompt_context_building(self, mock_get_follow_up_structured_data: MagicMock) -> None:
        """Test that the prompt context is built correctly for personalized summaries."""
        # Mock the follow-up structured data response
        mock_response = {
            "review_entries": [
                {
                    "id": "unique_id_1",
                    "kind": "header",
                    "topic": "PERSONAL NOTES:",
                    "subentries": [
                        {
                            "id": "unique_id_1_bullet_1",
                            "kind": "toggle",
                            "topic": "Test bullet point",
                        }
                    ],
                }
            ]
        }
        mock_get_follow_up_structured_data.return_value = mock_response

        # Add some metadata to the note to verify it's included in context
        self.note.metadata = self.note.metadata or {}
        self.note.metadata.update({"meeting_duration": 3600})
        self.note.save()

        transcript = "This is a valid transcript with more than 10 words to ensure it gets processed."
        result, has_content = get_personalized_summary(self.note, transcript)

        # Verify the follow-up structured data was called with the correct arguments
        mock_get_follow_up_structured_data.assert_called_once()
        call_args = mock_get_follow_up_structured_data.call_args[0]

        # First argument should be the template
        self.assertEqual(call_args[0], self.template)

        # Second argument should be the transcript with context
        transcript_with_context = call_args[1]
        self.assertIn(transcript, transcript_with_context)
        self.assertIn("Test User", transcript_with_context)  # User name from context
        self.assertIn("client meeting", transcript_with_context)  # Meeting type from context

        # Third argument should be the note
        self.assertEqual(call_args[2], self.note)

        # Verify the result
        expected = Summary(
            sections=[
                {
                    "topic": "PERSONAL NOTES",
                    "bullets": ["Test bullet point"],
                }
            ]
        )
        self.assertEqual(result, expected)
        self.assertTrue(has_content)


class TestAgendaCompletion(TestCase):
    def setUp(self) -> None:
        # Create test user
        self.organization = Organization.objects.create(name="test")
        self.user = User.objects.create(
            username="testuser",
            first_name="Test",
            last_name="User",
            organization=self.organization,
            metadata={"user_context": "Test context"},
        )

        # Create meeting type
        self.client_meeting_type = MeetingType.objects.create(
            name="Client Meeting",
            internal_name="Standard Client Meeting",
            category=MeetingType.Category.CLIENT,
            everyone=True,
        )

        # Create test note
        self.note = Note.objects.create(
            note_owner=self.user, meeting_type=self.client_meeting_type, metadata={"meeting_duration": 3600}
        )

        self.prompt = Prompt.objects.get(unique_name="agenda_completion_generator")

    @patch("deepinsights.core.ml.voice_memo._get_llm_prompt_output_with_retries")
    @patch("deepinsights.core.ml.voice_memo._clean_prompt_result_plain_text")
    def test_get_agenda_completion_info_success(self, clean_mock: MagicMock, llm_mock: MagicMock) -> None:
        agenda_value = {"test": "test2"}
        structured_data = StructuredMeetingData.objects.create(data=agenda_value)
        _ = ClientInteraction.objects.create(
            agenda=structured_data, note=self.note, meeting_type=self.client_meeting_type
        )
        llm_mock.return_value = "```agenda one```"
        clean_mock.return_value = "agenda one"
        transcript = "transcript test"
        result = get_agenda_completion_info(self.note, transcript)
        llm_mock.assert_called_once_with(self.prompt, ANY, agenda_value, self.note, transcript, ANY)
        clean_mock.assert_called_once_with(llm_mock.return_value)
        assert result
        assert clean_mock.return_value in result

    @patch("deepinsights.core.ml.voice_memo.call_model")
    def test_get_agenda_completion_info_success_collaborators(self, llm_mock: MagicMock) -> None:
        structured_data = StructuredMeetingData.objects.create(data={"test": "test2"})
        _ = ClientInteraction.objects.create(
            agenda=structured_data, note=self.note, meeting_type=self.client_meeting_type
        )
        llm_mock.return_value = "```agenda one```"
        transcript = "transcript test"
        result = get_agenda_completion_info(self.note, transcript)
        assert result
        assert "agenda one" in result
        llm_mock.assert_called_once()

    @patch("deepinsights.core.ml.voice_memo._get_llm_prompt_output_with_retries")
    def test_get_agenda_completion_info_fails_no_agenda(self, llm_mock: MagicMock) -> None:
        result = get_agenda_completion_info(self.note, "transcript test")
        llm_mock.assert_not_called()
        assert not result

    @patch("deepinsights.core.ml.voice_memo._get_agenda_for_note")
    @patch("deepinsights.core.ml.voice_memo._get_llm_prompt_output_with_retries")
    def test_get_agenda_follow_up_data_success(self, llm_mock: MagicMock, agenda_mock: MagicMock) -> None:
        agenda_mock.return_value = {"agenda one": "sub-points"}
        llm_mock.return_value = json.dumps({"agenda one": {"discussed": True}})

        result = get_agenda_follow_up_data(self.note, "transcript test")
        agenda_mock.assert_called_once()
        llm_mock.assert_called_once()
        assert result
        assert result == {"agenda one": {"discussed": True}}

    @patch("deepinsights.core.ml.voice_memo.call_model")
    def test_get_agenda_follow_up_data_success_collaborators(self, gpt4o_mock: MagicMock) -> None:
        structured_data = StructuredMeetingData.objects.create(data={"test": "test2"})
        _ = ClientInteraction.objects.create(
            agenda=structured_data, note=self.note, meeting_type=self.client_meeting_type
        )

        valid_structured_data = {
            "review_entries": [{"id": 1, "kind": "todo", "topic": "agenda one", "discussed": True}]
        }

        gpt4o_mock.return_value = json.dumps(valid_structured_data)

        result = get_agenda_follow_up_data(self.note, "transcript test")
        gpt4o_mock.assert_called_once()
        assert result
        assert result == valid_structured_data

    @patch("pydantic_ai.Agent.run_sync")
    @patch("deepinsights.core.ml.voice_memo.call_model")
    def test_get_agenda_follow_up_data_success_agentic(self, gpt_old_mock: MagicMock, agent_mock: MagicMock) -> None:
        # agenda setup
        structured_data = StructuredMeetingData.objects.create(data={"test": "test2"})
        _ = ClientInteraction.objects.create(
            agenda=structured_data, note=self.note, meeting_type=self.client_meeting_type
        )

        # agent config setup
        _ = AgentConfig.objects.create(
            name="agenda_followup_generator",
            user_prompt="This is a user prompt",
            version_notes="First version",
            user_prompt_metadata_example={"agenda_template": "test", "transcript": "test"},
            user_prompt_metadata_schema=["agenda_template", "transcript"],
            output_validation_schema="structured_text",
            llm_model="gpt-4o-2024-08-06",
            owner=self.organization,
        )
        # expected output
        valid_structured_data = {
            "review_entries": [{"id": 1, "kind": "header", "topic": "agenda one", "discussed": True}]
        }
        valid_mock = MagicMock()
        valid_mock.output.model_dump.return_value = valid_structured_data
        agent_mock.return_value = valid_mock

        result = get_agenda_follow_up_data(self.note, "transcript test")
        agent_mock.assert_called_once()
        gpt_old_mock.assert_not_called()
        assert result
        assert result == valid_structured_data

    @patch("pydantic_ai.Agent.run_sync")
    @patch("deepinsights.core.ml.voice_memo.call_model")
    def test_get_agenda_follow_up_data_failure_agentic(self, gpt_old_mock: MagicMock, agent_mock: MagicMock) -> None:
        # agenda setup
        structured_data = StructuredMeetingData.objects.create(data={"test": "test2"})
        _ = ClientInteraction.objects.create(
            agenda=structured_data, note=self.note, meeting_type=self.client_meeting_type
        )

        # agent config setup
        _ = AgentConfig.objects.create(
            name="agenda_followup_generator",
            user_prompt="This is a user prompt",
            version_notes="First version",
            user_prompt_metadata_example={"agenda_template": "test", "transcript": "test"},
            user_prompt_metadata_schema=["agenda_template", "transcript"],
            output_validation_schema="structured_text",
            llm_model="gpt-4o-2024-08-06",
            owner=self.organization,
        )
        # expected output
        agent_mock.side_effect = [Exception("didn't work")]

        result = get_agenda_follow_up_data(self.note, "transcript test")
        agent_mock.assert_called_once()
        gpt_old_mock.assert_not_called()
        assert not result

    @patch("deepinsights.core.ml.voice_memo._get_agenda_for_note")
    @patch("deepinsights.core.ml.voice_memo._get_llm_prompt_output_with_retries")
    def test_get_agenda_follow_up_data_fail_not_valid_json(self, llm_mock: MagicMock, agenda_mock: MagicMock) -> None:
        agenda_mock.return_value = {"agenda one": "sub-points"}
        llm_mock.return_value = """{"agenda one": not valid json})"""

        result = get_agenda_follow_up_data(self.note, "transcript test")
        agenda_mock.assert_called_once_with(self.note)
        llm_mock.assert_called_with(ANY, ANY, agenda_mock.return_value, self.note, "transcript test", ANY)
        assert not result

    def test_get_agenda_for_note_client_interaction_success(self) -> None:
        structured_data = StructuredMeetingData.objects.create(
            data={"test": "test2"}, status=StructuredMeetingData.Status.CREATED
        )
        client_interaction = ClientInteraction.objects.create(
            note=self.note, agenda=structured_data, meeting_type=self.client_meeting_type
        )
        result = _get_agenda_for_note(self.note)
        assert result
        assert result == {"test": "test2"}

    def test_get_agenda_for_note_meeting_type_success(self) -> None:
        agenda_template = StructuredMeetingDataTemplate.objects.create(initial_data={"test": "test2"})
        self.client_meeting_type.agenda_templates.set([agenda_template])
        self.client_meeting_type.save()
        result = _get_agenda_for_note(self.note)
        assert result
        assert result == {"test": "test2"}

    def test_get_agenda_for_note_fail(self) -> None:
        result = _get_agenda_for_note(self.note)
        assert not result

    @patch("deepinsights.core.ml.voice_memo.call_model")
    def test_get_llm_prompt_output_with_retries_success(self, call_model: MagicMock) -> None:
        schema = StructuredMeetingDataSchema.objects.get(name="unstructured_text")
        result = _get_llm_prompt_output_with_retries(self.prompt, schema, {}, self.note, "transcript", lambda x: x)
        assert result
        call_model.assert_called_once()

    @patch("deepinsights.core.ml.voice_memo.call_model")
    def test_get_llm_prompt_output_with_retries_failure_no_prompt_text(self, call_model: MagicMock) -> None:
        prompt = Prompt.objects.create(unique_name="agenda_completion_generator", user_prompt="")
        schema = StructuredMeetingDataSchema.objects.get(name="unstructured_text")
        result = _get_llm_prompt_output_with_retries(prompt, schema, {}, self.note, "transcript", lambda x: x)
        assert not result
        call_model.assert_not_called()

    @patch("deepinsights.core.ml.voice_memo.call_model")
    def test_get_llm_prompt_output_with_retries_failure_then_success(self, call_model: MagicMock) -> None:
        schema = StructuredMeetingDataSchema.objects.get(name="structured_data_schema")
        call_model.side_effect = ["invalid JSON{", json.dumps({"test": "valid JSON"})]
        result = _get_llm_prompt_output_with_retries(
            self.prompt, schema, {}, self.note, "transcript", lambda x: _validate_schema_and_json(x, schema, "test")
        )
        assert result
        assert result == json.dumps({"test": "valid JSON"})
        assert call_model.call_count == 2

    def test_clean_prompt_result_plain_text(self) -> None:
        text = "Text in the middle"
        assert _clean_prompt_result_plain_text(f"\n ```\r\n {text} \n```") == text
