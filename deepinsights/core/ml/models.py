import datetime

from pydantic import BaseModel, Field


class ActionItem(BaseModel):
    """
    Represents a single action item extracted from a meeting.
    """

    description: str = Field(description="The detailed description of the action item")
    assignee: str = Field(description="Name of the person assigned to the task")
    due_date: str = Field(description="The date by which the action item is due")


class SummarySection(BaseModel):
    """
    Represents a section in the meeting summary with a topic and bullet points.
    """

    topic: str = Field(description="The topic or heading for this section")
    bullets: list[str] = Field(description="List of bullet points under this topic")


class Summary(BaseModel):
    """
    Represents the complete structured summary of a meeting.
    """

    sections: list[SummarySection] = Field([], description="List of sections in the summary")


class TasksAndTakeaways(BaseModel):
    """
    Represents the structured output of tasks and takeaways from a meeting.
    """

    key_takeaways: list[str] = Field([], description="Main points and conclusions from the meeting")
    action_items: list[ActionItem] = Field([], description="List of tasks and actions to be taken")
    advisor_notes: list[str] = Field([], description="Notes specific to the advisor")
    keywords: list[str] = Field([], description="Key topics discussed")


class MeetingNote(BaseModel):
    """A meeting note with metadata and summary."""

    id: str
    source: str = Field(description="Source system (e.g., 'Zeplyn', 'salesforce')")
    created_at: datetime.datetime | None = None
    summary: Summary | str


class ClientHistory(BaseModel):
    """Collection of meeting notes and summaries for a client."""

    notes: list[MeetingNote]

    def to_prompt_format(self) -> str:
        """Convert the client history to a format suitable for LLM prompts."""
        formatted_notes = []

        for note in self.notes:
            note_text = [f"\nMeeting Note (from {note.source}, id: {note.id})"]

            if note.created_at:
                note_text.append(f"Date: {note.created_at.strftime('%Y-%m-%d')}")

            if isinstance(note.summary, str):
                note_text.append(f"\n{note.summary}")
            else:
                for section in note.summary.sections:
                    note_text.append(f"\n{section.topic}:")
                    for bullet in section.bullets:
                        note_text.append(f"- {bullet}")

            formatted_notes.append("\n".join(note_text))

        return "\n\n".join(formatted_notes)


class Agenda(BaseModel):
    """Data model for meeting agenda."""

    content: str = Field(description="The content of the agenda")
