from unittest.mock import MagicMock, patch

from anthropic.types import Message, TextBlock, Usage
from django.test import TestCase

from deepinsights.core.ml.genai import (
    call_claude_sonnet,
    call_model,
    ModelName,
)


class TestCallClaudeSonnet(TestCase):
    def setUp(self) -> None:
        self.test_prompt = "Test prompt"
        self.test_system_prompt = "Test system prompt"
        self.model = "anthropic.claude-3-5-sonnet-20240620-v1:0"
        self.mock_message = Message(
            id="test_id",
            content=[TextBlock(type="text", text="Test response")],
            model=self.model,
            role="assistant",
            stop_reason="end_turn",
            type="message",
            usage=Usage(input_tokens=0, output_tokens=0),
        )

    @patch("deepinsights.core.ml.genai.AnthropicBedrock")
    def test_successful_call(self, mock_anthropic: MagicMock) -> None:
        """Test successful call to Claude Sonnet with system prompt."""
        mock_anthropic.return_value.messages.create.return_value = self.mock_message

        result = call_claude_sonnet(
            self.test_prompt,
            system_prompt=self.test_system_prompt,
        )

        # Verify the result
        self.assertEqual(result, "Test response")

        # Verify the call to AnthropicBedrock
        mock_anthropic.return_value.messages.create.assert_called_once_with(
            model=self.model,
            max_tokens=8192,
            messages=[{"role": "user", "content": self.test_prompt}],
            system=self.test_system_prompt,
        )

    @patch("deepinsights.core.ml.genai.AnthropicBedrock")
    def test_invalid_response_object(self, mock_anthropic: MagicMock) -> None:
        """Test handling of invalid response object."""
        # Create empty response
        self.mock_message.content = []
        mock_anthropic.return_value.messages.create.return_value = self.mock_message

        with self.assertRaises(ValueError) as context:
            call_claude_sonnet(self.test_prompt, self.test_system_prompt)

        self.assertEqual(str(context.exception), "Invalid response object received from Claude Sonnet")

    @patch("deepinsights.core.ml.genai.AnthropicBedrock")
    def test_multiple_text_blocks(self, mock_anthropic: MagicMock) -> None:
        """Test handling of multiple text blocks in response."""
        # Modify the mock_message content to have multiple text blocks
        self.mock_message.content = [
            TextBlock(type="text", text="First part "),
            TextBlock(type="text", text="second part"),
        ]
        mock_anthropic.return_value.messages.create.return_value = self.mock_message

        result = call_claude_sonnet(self.test_prompt, self.test_system_prompt)

        # Verify the concatenated result
        self.assertEqual(result, "First part second part")


class TestCallModel(TestCase):
    def setUp(self) -> None:
        self.test_prompt = "Test prompt for the input model"

        # Mock OpenAI response structure
        self.mock_choice = MagicMock()
        self.mock_choice.message.content = "Test response"

        self.mock_response = MagicMock()
        self.mock_response.choices = [self.mock_choice]

    @patch("deepinsights.core.ml.genai.openai_client")
    def test_successful_call_gpt4o_default(self, mock_client: MagicMock) -> None:
        """Test successful call to GPT-4o (default model)."""
        mock_client.chat.completions.create.return_value = self.mock_response

        result = call_model(self.test_prompt)

        # Verify the result
        self.assertEqual(result, "Test response")

        # Verify the call to OpenAI client with default parameters
        mock_client.chat.completions.create.assert_called_once_with(
            model="gpt-4o-2024-08-06",
            messages=[{"role": "user", "content": self.test_prompt}],
            temperature=0.1,
            max_tokens=16384,
        )

    @patch("deepinsights.core.ml.genai.openai_client")
    def test_successful_call_gpt4o_custom_params(self, mock_client: MagicMock) -> None:
        """Test successful call to GPT-4o with custom parameters."""
        mock_client.chat.completions.create.return_value = self.mock_response

        result = call_model(self.test_prompt, temp=0.5, max_tokens=1000, model=ModelName.GPT_4O)

        # Verify the result
        self.assertEqual(result, "Test response")

        # Verify the call to OpenAI client with custom parameters
        mock_client.chat.completions.create.assert_called_once_with(
            model="gpt-4o-2024-08-06",
            messages=[{"role": "user", "content": self.test_prompt}],
            temperature=0.5,
            max_tokens=1000,
        )

    @patch("deepinsights.core.ml.genai.openai_client")
    def test_successful_call_o3_model(self, mock_client: MagicMock) -> None:
        """Test successful call to O3 model."""
        mock_client.chat.completions.create.return_value = self.mock_response

        result = call_model(self.test_prompt, model=ModelName.O3)

        # Verify the result
        self.assertEqual(result, "Test response")

        # Verify the call to OpenAI client (O3 doesn't include temperature)
        mock_client.chat.completions.create.assert_called_once_with(
            model="o3-2025-04-16",
            messages=[{"role": "user", "content": self.test_prompt}],
            max_completion_tokens=16384,
        )

    #  test for o4-mini model.
    @patch("deepinsights.core.ml.genai.openai_client")
    def test_successful_call_o4_mini_model(self, mock_client: MagicMock) -> None:
        """Test successful call to O4-mini model."""
        mock_client.chat.completions.create.return_value = self.mock_response

        result = call_model(self.test_prompt, model=ModelName.O4_MINI)

        # Verify the result
        self.assertEqual(result, "Test response")

        # Verify O4-mini doesn't include temperature parameter
        mock_client.chat.completions.create.assert_called_once_with(
            model="o4-mini-2025-04-16",
            messages=[{"role": "user", "content": self.test_prompt}],
            max_completion_tokens=16384,
        )

    @patch("deepinsights.core.ml.genai.openai_client")
    def test_invalid_response_empty_choices(self, mock_client: MagicMock) -> None:
        """Test handling of response with empty choices."""
        self.mock_response.choices = []
        mock_client.chat.completions.create.return_value = self.mock_response

        with self.assertRaises(ValueError) as context:
            call_model(self.test_prompt)

        self.assertEqual(str(context.exception), "Invalid response object received from the input model.")

    @patch("deepinsights.core.ml.genai.openai_client")
    def test_invalid_response_no_content(self, mock_client: MagicMock) -> None:
        """Test handling of response with no message content."""
        self.mock_choice.message.content = None
        mock_client.chat.completions.create.return_value = self.mock_response

        with self.assertRaises(ValueError) as context:
            call_model(self.test_prompt)

        self.assertEqual(str(context.exception), "Invalid response object received from the input model.")

    def test_invalid_model_enum(self) -> None:
        """Test handling of invalid model enum."""
        # This should raise a ValueError before even calling OpenAI
        with self.assertRaises(ValueError):
            # Create an invalid ModelName by casting a string
            invalid_model = "invalid-model"
            call_model(self.test_prompt, model=invalid_model)  # type: ignore
