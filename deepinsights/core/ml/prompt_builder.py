import json
import logging
from enum import StrEnum
from typing import Literal

from deepinsights.core.ml import voice_memo_utils as vmu
from deepinsights.core.ml.prompt_templates import agenda
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.prompt import Prompt
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingDataTemplate
from deepinsights.meetingsapp.models.task import Task


class DataSource(StrEnum):
    MEETING_CONTEXT = "meeting_context"
    SUMMARY = "summary"
    TASKS = "tasks"
    TAKEAWAYS = "takeaways"
    ADVISOR_NOTES = "advisor_notes"
    CLIENT_SUMMARY = "client_summary"


class PromptContext:
    def __init__(self, note: Note):
        self._note = note

    def _get_meeting_context(self) -> str:
        """Builds context string for meeting prompts."""
        user = self._note.note_owner
        user_name = user.get_full_name() if user else ""
        attendees_dict = self._note.get_attendees_by_type()
        client_attendees = attendees_dict["clients"]
        advisor_attendees = attendees_dict["advisors"]
        time_str = vmu.get_time_string_from_timestamp(self._note.created, "America/New_York")

        user_context = user.metadata.get("user_context", "") if user and hasattr(user, "metadata") else ""

        if client_attendees == "None":
            client_attendees = "No clients were present or specified by the user."
        else:
            client_attendees = f"client(s) {client_attendees}"

        context = (
            f"This is a {self._note.category} meeting with advisor {user_name}. "
            f"The meeting attendee(s) were the following:\n"
            f"client(s): {client_attendees}\n advisor(s): {advisor_attendees}. "
            f"Meeting occurred on {time_str}."
        )

        if user and (user_context := user.user_context):
            context += f"\nAdditional context: {user_context}"

        if self._note.metadata:
            if meeting_duration := self._note.metadata.get("meeting_duration"):
                context += f"\nMeeting duration: {meeting_duration} seconds"
        return context

    def _get_tasks(self) -> str:
        """Get formatted string of tasks from the note."""
        tasks = Task.objects.filter(note=self._note, completed=False).order_by("id")
        if not tasks:
            return ""

        action_items = "Action Items:\n"
        for task in tasks:
            action_items += f"- {task.task_title}\n"
        return action_items

    def _get_takeaways(self) -> str:
        """Get formatted string of key takeaways from the note."""
        if not self._note.key_takeaways:
            return ""

        takeaways = "Key Takeaways:\n"
        for tk in self._note.key_takeaways:
            takeaways += f"- {tk}\n"
        return takeaways

    def _get_summary(self) -> str:
        """Get the meeting summary."""
        if not self._note.summary:
            return ""
        return f"Meeting Summary:\n{self._note.get_summary().as_text()}"

    def _get_advisor_notes(self) -> str:
        """Get advisor notes from the note's metadata."""
        if not self._note.advisor_notes:
            return ""
        # advisor notes is a list of strings
        advisor_notes = "Advisor Notes:\n"
        for note in self._note.advisor_notes:
            advisor_notes += f"- {note}\n"
        return advisor_notes

    def _get_client_summary(self) -> str:
        """Get client summary from the note's metadata."""
        if not self._note.client:
            return ""

        try:
            from deepinsights.core.ml.client_recap import (
                build_client_recap_for_client,
                convert_client_recap_to_markdown,
            )

            # Get the note owner as the user
            user = self._note.note_owner
            if not user:
                return ""

            # Get the client recap
            client_recap = build_client_recap_for_client(user, self._note.client, [])

            # Convert to markdown format
            return convert_client_recap_to_markdown(client_recap)
        except Exception as e:
            logging.error("Error getting client summary: %s", str(e), exc_info=True)
            return ""

    def build_context(self, sources: list[DataSource]) -> str:
        """Build context string from multiple data sources.

        Args:
            sources: List of data sources to include in the context

        Returns:
            Combined context string from all requested sources
        """
        context = ""
        for source in sources:
            if source == DataSource.MEETING_CONTEXT:
                if self._get_meeting_context():
                    context += f"[{DataSource.MEETING_CONTEXT}]\n\n{self._get_meeting_context()}\n"
            if source == DataSource.TASKS:
                if self._get_tasks():
                    context += f"[{DataSource.TASKS}]\n\n{self._get_tasks()}\n"
            if source == DataSource.TAKEAWAYS:
                if self._get_takeaways():
                    context += f"[{DataSource.TAKEAWAYS}]\n\n{self._get_takeaways()}\n"
            if source == DataSource.SUMMARY:
                if self._get_summary():
                    context += f"[{DataSource.SUMMARY}]\n\n{self._get_summary()}\n"
            if source == DataSource.ADVISOR_NOTES:
                if self._get_advisor_notes():
                    context += f"[{DataSource.ADVISOR_NOTES}]\n\n{self._get_advisor_notes()}\n"
            if source == DataSource.CLIENT_SUMMARY:
                if self._get_client_summary():
                    context += f"[{DataSource.CLIENT_SUMMARY}]\n\n{self._get_client_summary()}\n"

        return context


class ClientRecapPromptBuilder:
    @staticmethod
    def build_prompt(
        prompt_type: Literal["info_extraction", "agenda_builder"],
        **kwargs: str,
    ) -> str:
        if prompt_type == "info_extraction":
            return ClientRecapPromptBuilder._build_info_extraction_prompt(kwargs["transcript"])
        elif prompt_type == "agenda_builder":
            return ClientRecapPromptBuilder._build_agenda_builder_prompt(
                kwargs["extractions"], kwargs["structured_data"]
            )

    @staticmethod
    def _build_info_extraction_prompt(transcript: str) -> str:
        return agenda.info_extraction.format(transcript=transcript)

    @staticmethod
    def _build_agenda_builder_prompt(extractions: str, structured_data: str) -> str:
        return agenda.agenda_prep.format(extractions=extractions, structured_data=structured_data)


def build_prompt_for_client_recap(
    prompt_type: Literal["info_extraction", "agenda_builder"],
    transcript: str = None,  # type: ignore[assignment]
    extractions: str = None,  # type: ignore[assignment]
    structured_data: str = "Structured Data is not provided.",
) -> str:
    return ClientRecapPromptBuilder.build_prompt(
        prompt_type,
        transcript=transcript,
        extractions=extractions,
        structured_data=structured_data,
    )


class DatabaseBasedPromptBuilder:
    """A prompt builder that uses prompt information stored in the database."""

    class MeetingPromptType(StrEnum):
        SUMMARY = "summary"
        TASKS_AND_TAKEAWAYS = "tasks_and_takeaways"
        TASKS_AND_TAKEAWAYS_DETAILED = "tasks_and_takeaways_detailed"

    @classmethod
    # TODO: [ENG-1119] Remove passing the version here and get the version from a runtime flag
    def build_prompt_for_meeting(
        cls, note: Note, prompt_type: MeetingPromptType, transcript: str, version: str = "1"
    ) -> str:
        """Gets and formats prompt with context and transcript placeholders."""
        # TODO: Consider using a dictionary for look up instead for readability
        unique_name = f"{note.category}_meeting_{prompt_type.value}"
        try:
            prompt = Prompt.objects.filter(unique_name=unique_name, version=version).first()
            if not prompt:
                raise ValueError(f"No prompt found for {unique_name}")
            user_uuid = note.note_owner.uuid if note.note_owner else None
            logging.info(
                "Using prompt {prompt_name} version {version} for user {user_uuid} for note {note_uuid}".format(
                    prompt_name=prompt.name,
                    version=version,
                    user_uuid=user_uuid,
                    note_uuid=note.uuid,
                )
            )
            prompt_context = PromptContext(note)
            context = prompt_context._get_meeting_context()
            return prompt.user_prompt.format(context=context, transcript=transcript) if prompt.user_prompt else ""
        except Exception as e:
            # Catch all exceptions and provide more context in the error message
            raise ValueError(f"Error with prompt {unique_name}: {str(e)}")

    @classmethod
    def build_prompt_for_structured_data(
        cls, structured_data_template: StructuredMeetingDataTemplate, transcript: str
    ) -> str:
        """
        Builds a prompt for a structured meeting data template, with the given transcript.

        Args:
            structured_data_template: the structured meeting data template for which to generate a prompt.
            transcript: the transcript of the meeting

        Returns:
            Formatted prompt string for the LLM

        Raises:
            ValueError: if any required relationship or data is missing
        """
        if not (prompt := structured_data_template.prompt) or not prompt.user_prompt:
            raise ValueError(f"Template {structured_data_template.uuid} has no associated prompt.")

        try:
            if structured_data_template.schema_definition:
                schema = structured_data_template.schema_definition.schema or {}
            else:
                raise ValueError("Template %s has no associated schema definition." % structured_data_template.uuid)

            return prompt.user_prompt.format(
                schema=json.dumps(schema, indent=2),
                data=json.dumps(structured_data_template.initial_data or {}, indent=2),
                example=json.dumps(structured_data_template.context or "", indent=2),
                transcript=transcript,
            )
        except (KeyError, ValueError) as e:
            logging.error("Failed to format prompt %s (%s)", prompt.name, prompt.uuid, exc_info=e)
            raise ValueError(
                f"Failed to format prompt for structured meeting data template {structured_data_template.uuid}: {str(e)}"
            )

    @classmethod
    async def get_search_prompt_async(
        cls, input_data: str, query: str, additional_context: str, prompt_name: str
    ) -> str:
        """
        Retrieves a search prompt, first attempting to get it from the database, falling back to the
        default database search prompt if not found.

        Args:
            input_data: Data to include in the prompt
            query: Query to include in the prompt
            prompt_name: Name of the prompt to retrieve from database

        Returns:
            Formatted prompt ready for LLM processing
        Raises:
            ValueError: If prompt cannot be found, or if transcript or query is empty
        """

        try:
            db_prompt = await Prompt.objects.aget(unique_name=prompt_name)
        except Prompt.DoesNotExist:
            try:
                db_prompt = await Prompt.objects.aget(unique_name="search_prompt_v1")
            except Prompt.DoesNotExist:
                db_prompt = None
        if not db_prompt:
            logging.error(
                "No search prompt found in database, neither default nor custom: custom prompt %s", prompt_name
            )
            raise ValueError(f"No search prompt found in database ({prompt_name})")
        if not db_prompt.user_prompt:
            logging.error("Search prompt has no prompt text (%s)", prompt_name)
            raise ValueError("Search prompt has no prompt text ({prompt_name})")

        try:
            return db_prompt.user_prompt.format(
                query=query, input_data=input_data, additional_context=additional_context
            )
        except Exception as e:
            logging.error("Failed to format prompt", exc_info=e)
            raise ValueError("Failed to format prompt") from e
