from unittest.mock import MagicMock, patch

from django.test import TestCase

from deepinsights.core.ml import emails
from deepinsights.meetingsapp.models.meeting_summary_email_template import MeetingSummaryEmailTemplate
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.user import User


def test_email_template(django_user_model: User) -> None:
    org = Organization.objects.create()
    user = django_user_model.objects.create_user("<EMAIL>", organization=org)
    note = Note.objects.create(metadata={})
    assert emails._generate_meeting_notes_email_content(note, "summary", user)


def test_email_template_user_override(django_user_model: User) -> None:
    org = Organization.objects.create(preferences={"email_settings": {"meeting_notes_email_template": "Hello Org!"}})
    user = django_user_model.objects.create_user(
        "<EMAIL>",
        organization=org,
        preferences={"email_settings": {"meeting_notes_email_template": "Hello User!"}},
    )
    note = Note.objects.create(metadata={})
    assert (
        emails._generate_meeting_notes_email_content(note, "summary", user)
        == f'<p><a href="{note.public_url()}" style="color: #0066cc; text-decoration: none; font-weight: bold;">📝 View Notes in Zeplyn</a></p>\n\n<p>Hello User!</p>'
    )


def test_email_template_org_override(django_user_model: User) -> None:
    org = Organization.objects.create(preferences={"email_settings": {"meeting_notes_email_template": "Hello Org!"}})
    user = django_user_model.objects.create_user("<EMAIL>", organization=org)

    note = Note.objects.create(metadata={})
    assert (
        emails._generate_meeting_notes_email_content(note, "summary", user)
        == f'<p><a href="{note.public_url()}" style="color: #0066cc; text-decoration: none; font-weight: bold;">📝 View Notes in Zeplyn</a></p>\n\n<p>Hello Org!</p>'
    )


@patch("deepinsights.core.ml.emails._get_followup_email")
def test_generate_followup_email_contents(mock_get_followup: MagicMock, django_user_model: User) -> None:
    org = Organization.objects.create()
    user = django_user_model.objects.create_user(username="test user", email="<EMAIL>", organization=org)
    note = Note.objects.create(
        metadata={"meeting_name": "TEST"},
        key_takeaways=["dummy data 1", "dummy data 2"],
        note_owner=user,
    )

    mock_get_followup.return_value = "hello"
    result = emails.generate_followup_email_contents(note_id=str(note.uuid), user=user)
    assert "hello" in result
    mock_get_followup.assert_called_once()


class TestEmailGeneration(TestCase):
    """Test class for email generation functionality."""

    def setUp(self) -> None:
        """Set up test data."""
        super().setUp()
        # Create organization
        self.org = Organization.objects.create()

        # Create user with preferences
        self.user = User.objects.create_user(
            "<EMAIL>",
            organization=self.org,
            preferences={"email_settings": {"meeting_notes_email_template": "Test Template"}},
        )

        # Create note with metadata and takeaways
        self.note = Note.objects.create(
            metadata={"meeting_name": "Test Meeting"},
            note_owner=self.user,
            key_takeaways=["Test takeaway"],
        )

        # Create  non default email templates. Default template is created in conftest.py
        self.org_template = MeetingSummaryEmailTemplate.objects.create(
            name="Org Template", email_template_content="Org content"
        )
        self.org_template.organizations.set([self.org])

        self.user_template = MeetingSummaryEmailTemplate.objects.create(
            name="User Template", email_template_content="User content"
        )
        self.user_template.users.set([self.user])

    @patch("deepinsights.core.ml.emails._get_followup_email")
    def test_template_selection_user(self, mock_get_followup: MagicMock) -> None:
        """Test email template selection logic."""
        mock_get_followup.return_value = "Default content"
        # Test default template selection
        result = emails.generate_followup_email_contents(note_id=str(self.note.uuid), user=self.user)
        self.assertIn("Default", result)
        mock_get_followup.assert_called_once()

    @patch("deepinsights.core.ml.emails._get_followup_email")
    def test_template_selection_org(self, mock_get_followup: MagicMock) -> None:
        mock_get_followup.return_value = "Org content"
        # Test specific template selection
        result = emails.generate_followup_email_contents(
            note_id=str(self.note.uuid), user=self.user, template_id=str(self.org_template.uuid)
        )
        self.assertIn("Org", result)
        mock_get_followup.assert_called_once()

    @patch("deepinsights.core.ml.emails.call_claude_sonnet")
    def test_summary_in_prompt_context(self, mock_call_claude: MagicMock) -> None:
        """Test that the summary is included in the prompt context when generating follow-up emails."""
        # Create a note with a specific summary
        note_with_summary = Note.objects.create(
            metadata={"meeting_name": "Test Meeting"},
            note_owner=self.user,
            key_takeaways=["Test takeaway"],
            summary={"sections": [{"topic": "Test Topic", "bullets": ["This is a test summary for the meeting"]}]},
        )

        context_prompt = "{context} {email_template}"
        mock_call_claude.return_value = "# Test Email\n\nThis is a test email."
        result = emails._get_followup_email(
            note=note_with_summary,
            user=self.user,
            email_template="Test template",
            email_generation_prompt=context_prompt,
            system_prompt="",
        )
        self.assertEqual(result, "# Test Email\n\nThis is a test email.")
        mock_call_claude.assert_called_once()

        # Verify that the summary is included in the prompt context
        prompt_text = mock_call_claude.call_args.args[0]
        self.assertIn("This is a test summary for the meeting", prompt_text)
        self.assertIn("Meeting Summary", prompt_text)
        self.assertIn("Test takeaway", prompt_text)


class TestTemplateSelectionLogic(TestCase):
    """Test class for template selection logic in generate_followup_email_contents."""

    def setUp(self) -> None:
        """Set up test data."""
        super().setUp()
        # Create organization
        self.org = Organization.objects.create()

        # Create user
        self.user = User.objects.create_user(
            "<EMAIL>",
            organization=self.org,
        )

        # Create note with metadata
        self.note = Note.objects.create(
            metadata={"meeting_name": "Test Meeting"},
            note_owner=self.user,
            key_takeaways=["Test takeaway"],
        )

        # Create a default template (available to everyone)
        self.default_template = MeetingSummaryEmailTemplate.objects.create(
            name="Default Template",
            internal_name="default_template",
            email_template_content="Default content",
            everyone=True,
        )

        # Create a specific template for the organization
        self.org_template = MeetingSummaryEmailTemplate.objects.create(
            name="Org Template",
            internal_name="org_template",
            email_template_content="Org content",
            everyone=False,
        )
        self.org_template.organizations.set([self.org])

    @patch("deepinsights.core.ml.emails._get_followup_email")
    def test_template_not_found_raises_error(self, mock_get_followup: MagicMock) -> None:
        """Test that ValueError is raised when no template is found."""
        # Delete all templates to simulate no templates available
        MeetingSummaryEmailTemplate.objects.all().delete()

        with self.assertRaises(ValueError) as context:
            emails.generate_followup_email_contents(note_id=str(self.note.uuid), user=self.user)

        self.assertEqual(str(context.exception), "No email template found")

    @patch("deepinsights.core.ml.emails._get_followup_email")
    def test_template_id_not_found_uses_default(self, mock_get_followup: MagicMock) -> None:
        """Test that when template_id is not found, default template is used."""
        mock_get_followup.return_value = "Default content"

        # Use a non-existent template_id
        non_existent_template_id = "00000000-0000-0000-0000-000000000000"

        result = emails.generate_followup_email_contents(
            note_id=str(self.note.uuid), user=self.user, template_id=non_existent_template_id
        )

        # Should use the default template (first template in the queryset)
        self.assertIn("Default", result)
        mock_get_followup.assert_called_once()

    @patch("deepinsights.core.ml.emails._get_followup_email")
    def test_template_id_not_found_no_default_raises_error(self, mock_get_followup: MagicMock) -> None:
        """Test that ValueError is raised when template_id not found and no default template exists."""
        # Delete all templates to ensure no templates are available
        MeetingSummaryEmailTemplate.objects.all().delete()

        # Use a non-existent template_id
        non_existent_template_id = "00000000-0000-0000-0000-000000000000"

        with self.assertRaises(ValueError) as context:
            emails.generate_followup_email_contents(
                note_id=str(self.note.uuid), user=self.user, template_id=non_existent_template_id
            )

        self.assertEqual(str(context.exception), "No email template found")

    @patch("deepinsights.core.ml.emails._get_followup_email")
    def test_valid_template_id_uses_specified_template(self, mock_get_followup: MagicMock) -> None:
        """Test that when valid template_id is provided, that specific template is used."""
        mock_get_followup.return_value = "Org content"

        result = emails.generate_followup_email_contents(
            note_id=str(self.note.uuid), user=self.user, template_id=str(self.org_template.uuid)
        )

        # Should use the org template
        self.assertIn("Org", result)
        mock_get_followup.assert_called_once()

    @patch("deepinsights.core.ml.emails._get_followup_email")
    def test_none_template_id_uses_default(self, mock_get_followup: MagicMock) -> None:
        """Test that when None template_id is provided, default template is used."""
        mock_get_followup.return_value = "Default content"

        result = emails.generate_followup_email_contents(
            note_id=str(self.note.uuid),
            user=self.user,
            template_id=None,  # type: ignore[arg-type]
        )

        # Should use the default template
        self.assertIn("Default", result)
        mock_get_followup.assert_called_once()

    @patch("deepinsights.core.ml.emails._get_followup_email")
    def test_template_selection_priority_order(self, mock_get_followup: MagicMock) -> None:
        """Test that template selection follows the correct priority order."""
        # Create multiple templates with different access levels
        user_template = MeetingSummaryEmailTemplate.objects.create(
            name="User Template",
            internal_name="user_template",
            email_template_content="User content",
            everyone=False,
        )
        user_template.users.set([self.user])

        # Test that user-specific template is selected first (highest priority)
        mock_get_followup.return_value = "User content"
        result = emails.generate_followup_email_contents(note_id=str(self.note.uuid), user=self.user)

        # Should use the user template (first in the queryset due to ordering)
        self.assertIn("User", result)
        mock_get_followup.assert_called_once()

    @patch("deepinsights.core.ml.emails._get_followup_email")
    def test_template_id_invalid_uuid_format_uses_default(self, mock_get_followup: MagicMock) -> None:
        """Test that when template_id is not a valid UUID format, default template is used."""
        mock_get_followup.return_value = "Default content"

        # Use an invalid UUID format
        invalid_template_id = "not-a-valid-uuid"

        result = emails.generate_followup_email_contents(
            note_id=str(self.note.uuid), user=self.user, template_id=invalid_template_id
        )

        # Should use the default template
        self.assertIn("Default", result)
        mock_get_followup.assert_called_once()
