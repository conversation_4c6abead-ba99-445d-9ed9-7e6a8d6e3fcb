import urllib.error
from unittest.mock import MagicMock, patch

import pytest
from deepgram._types import PrerecordedTranscriptionResponse
from django.conf import settings

from deepinsights.core.ml.asr import (
    _get_deepgram_transcript,
    deepgram_options,
    fetch_and_save_deepgram_transcript,
)
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User

pytestmark = [pytest.mark.django_db(transaction=True)]


@pytest.fixture
def mock_deepgram_response() -> PrerecordedTranscriptionResponse:
    return {
        "metadata": {
            "transaction_key": "test-key",
            "request_id": "test-request",
            "sha256": "test-sha",
            "created": "2023-01-01T00:00:00.000Z",
            "duration": 60.0,
            "channels": 1,
        },
        "results": {
            "channels": [{"alternatives": [{"transcript": "Test transcript", "confidence": 0.95, "words": []}]}]
        },
    }


@pytest.fixture
def test_user(django_user_model: User) -> User:
    user = django_user_model.objects.create(
        username="<EMAIL>",
        first_name="Test",
        last_name="User",
    )
    user.biasing_words = ["test", "words"]
    user.save()
    return user


@pytest.fixture
def test_note(test_user: User) -> Note:
    note: Note = Note.objects.create(note_owner=test_user, note_type="meeting_recording", status="uploaded")
    note.authorized_users.add(test_user)
    return note


@patch("deepinsights.core.ml.asr.Deepgram")
def test_get_deepgram_transcript(
    mock_deepgram: MagicMock, mock_deepgram_response: PrerecordedTranscriptionResponse
) -> None:
    mock_deepgram.return_value.transcription.sync_prerecorded.return_value = mock_deepgram_response

    response = _get_deepgram_transcript(
        source={"url": "https://test.com/audio.mp3"}, biasing_words=["test"], asr_language_code="en"
    )

    mock_deepgram.assert_called_once_with(settings.DEEPGRAM_API_KEY)
    mock_deepgram.return_value.transcription.sync_prerecorded.assert_called_once_with(
        source={"url": "https://test.com/audio.mp3"},
        options={
            "language": "en",
            "smart_format": True,
            "model": "nova-2",
            "redact": ["pci", "ssn"],
            "numerals": True,
            "utterances": True,
            "diarize": True,
            "keywords": ["test"],
            "replace": [
                "zeplin:Zeplyn",
                "zeplins:Zeplyns",
                "zeplin's:Zeplyn's",
                "zeppelin:Zeplyn",
                "zeppelins:Zeplyns",
                "zeppelin's:Zeplyn's",
                "zeplon:Zeplyn",
                "zeplons:Zeplyns",
                "zeplon's:Zeplyn's",
            ],
            "log_data": False,
        },
        custom_options={"mip_opt_out": True},
    )
    assert response == mock_deepgram_response


@patch("deepinsights.core.ml.asr.Deepgram")
def test_get_deepgram_transcript_with_audio_data(
    mock_deepgram: MagicMock, mock_deepgram_response: PrerecordedTranscriptionResponse
) -> None:
    mock_deepgram.return_value.transcription.sync_prerecorded.return_value = mock_deepgram_response

    response = _get_deepgram_transcript(
        source={"buffer": "data", "mimetype": "test"}, biasing_words=["test"], asr_language_code="en"
    )

    mock_deepgram.assert_called_once_with(settings.DEEPGRAM_API_KEY)
    mock_deepgram.return_value.transcription.sync_prerecorded.assert_called_once_with(
        source={"buffer": "data", "mimetype": "test"},
        options={
            "language": "en",
            "smart_format": True,
            "model": "nova-2",
            "redact": ["pci", "ssn"],
            "numerals": True,
            "utterances": True,
            "diarize": True,
            "replace": [
                "zeplin:Zeplyn",
                "zeplins:Zeplyns",
                "zeplin's:Zeplyn's",
                "zeppelin:Zeplyn",
                "zeppelins:Zeplyns",
                "zeppelin's:Zeplyn's",
                "zeplon:Zeplyn",
                "zeplons:Zeplyns",
                "zeplon's:Zeplyn's",
            ],
            "keywords": ["test"],
            "log_data": False,
        },
        custom_options={"mip_opt_out": True},
    )
    assert response == mock_deepgram_response


@patch("deepinsights.core.ml.asr._get_deepgram_transcript")
def test_fetch_and_save_deepgram_transcript_with_language_code(
    mock_get_transcript: MagicMock,
    test_user: User,
    test_note: Note,
    mock_deepgram_response: PrerecordedTranscriptionResponse,
) -> None:
    preferences = test_user.get_preferences()
    preferences.asr_language_code = "es"
    test_user.preferences = preferences.to_dict()
    test_user.save()

    mock_get_transcript.return_value = mock_deepgram_response

    response = fetch_and_save_deepgram_transcript(
        source={"url": "https://test.com/audio.mp3"}, note=test_note, user=test_user
    )

    mock_get_transcript.assert_called_once_with(
        {"url": "https://test.com/audio.mp3"},
        [],
        "es",
    )

    test_note.refresh_from_db()
    assert test_note.raw_asr_response == mock_deepgram_response
    assert response == mock_deepgram_response


@patch("deepinsights.core.ml.asr._get_deepgram_transcript")
@patch("deepinsights.core.ml.asr.logging")
def test_fetch_and_save_deepgram_transcript_default_language_code(
    mock_logging: MagicMock,
    mock_get_transcript: MagicMock,
    test_user: User,
    test_note: Note,
    mock_deepgram_response: PrerecordedTranscriptionResponse,
) -> None:
    # Ensure no language code is set
    preferences = test_user.get_preferences()
    preferences.asr_language_code = ""
    test_user.preferences = preferences.to_dict()
    test_user.save()

    mock_get_transcript.return_value = mock_deepgram_response

    response = fetch_and_save_deepgram_transcript(
        source={"url": "https://test.com/audio.mp3"}, note=test_note, user=test_user
    )

    # Verify warning was logged
    mock_logging.warning.assert_called_once_with(
        "ASR language code not set for user: %s, Using default('en') for now", test_user.uuid
    )

    mock_get_transcript.assert_called_once_with(
        {"url": "https://test.com/audio.mp3"},
        [],
        "en",  # Should use default language code
    )

    test_note.refresh_from_db()
    assert test_note.raw_asr_response == mock_deepgram_response
    assert response == mock_deepgram_response


@patch("deepinsights.core.ml.asr._get_deepgram_transcript")
def test_fetch_and_save_deepgram_transcript_no_biasing_words(
    mock_get_transcript: MagicMock,
    test_user: User,
    test_note: Note,
    mock_deepgram_response: PrerecordedTranscriptionResponse,
) -> None:
    test_user.metadata = {}
    test_user.save()

    mock_get_transcript.return_value = mock_deepgram_response

    response = fetch_and_save_deepgram_transcript(
        source={"url": "https://test.com/audio.mp3"}, note=test_note, user=test_user
    )

    mock_get_transcript.assert_called_once_with({"url": "https://test.com/audio.mp3"}, [], "en")

    test_note.refresh_from_db()
    assert test_note.raw_asr_response == mock_deepgram_response
    assert response == mock_deepgram_response


@patch("deepinsights.core.ml.asr.Deepgram")
def test_get_deepgram_transcript_error(mock_deepgram: MagicMock) -> None:
    mock_client = MagicMock()
    mock_client.transcription.sync_prerecorded.side_effect = urllib.error.HTTPError(
        url="https://test.com",
        code=401,
        msg="Unauthorized",
        hdrs={},  # type: ignore[arg-type]
        fp=None,
    )
    mock_deepgram.return_value = mock_client

    with pytest.raises(Exception, match="HTTP Error 401: Unauthorized"):
        _get_deepgram_transcript(
            source={"url": "https://test.com/audio.mp3"}, biasing_words=["test"], asr_language_code="en"
        )


@pytest.mark.parametrize("diarize", [True, False])
def test_deepgram_options_with_diarize(diarize: bool) -> None:
    assert deepgram_options("en_US", ["test", "words"], diarize=diarize) == {
        "language": "en_US",
        "smart_format": True,
        "model": "nova-2",
        "redact": ["pci", "ssn"],
        "numerals": True,
        "utterances": True,
        "keywords": ["test", "words"],
        "replace": [
            "zeplin:Zeplyn",
            "zeplins:Zeplyns",
            "zeplin's:Zeplyn's",
            "zeppelin:Zeplyn",
            "zeppelins:Zeplyns",
            "zeppelin's:Zeplyn's",
            "zeplon:Zeplyn",
            "zeplons:Zeplyns",
            "zeplon's:Zeplyn's",
        ],
        "log_data": False,
        "diarize": diarize,
    }


def test_deepgram_options_without_diarize() -> None:
    assert deepgram_options("en", ["test", "words"]) == {
        "language": "en",
        "smart_format": True,
        "model": "nova-2",
        "redact": ["pci", "ssn"],
        "numerals": True,
        "utterances": True,
        "keywords": ["test", "words"],
        "replace": [
            "zeplin:Zeplyn",
            "zeplins:Zeplyns",
            "zeplin's:Zeplyn's",
            "zeppelin:Zeplyn",
            "zeppelins:Zeplyns",
            "zeppelin's:Zeplyn's",
            "zeplon:Zeplyn",
            "zeplons:Zeplyns",
            "zeplon's:Zeplyn's",
        ],
        "log_data": False,
    }


def test_deepgram_options_empty_biasing_words() -> None:
    assert deepgram_options("en", []) == {
        "language": "en",
        "smart_format": True,
        "model": "nova-2",
        "redact": ["pci", "ssn"],
        "numerals": True,
        "utterances": True,
        "keywords": [],
        "replace": [
            "zeplin:Zeplyn",
            "zeplins:Zeplyns",
            "zeplin's:Zeplyn's",
            "zeppelin:Zeplyn",
            "zeppelins:Zeplyns",
            "zeppelin's:Zeplyn's",
            "zeplon:Zeplyn",
            "zeplons:Zeplyns",
            "zeplon's:Zeplyn's",
        ],
        "log_data": False,
    }
