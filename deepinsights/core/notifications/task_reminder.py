import logging
from datetime import date, timedelta

from deepinsights.core.email_service import ZeplynEmailService
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User

email_service = ZeplynEmailService()


def get_task_bullet_assigned_today(task: Task) -> str:
    return f"<li><p><a href=https://app.zeplyn.ai/tasks/{task.uuid}><b>{task.task_title}</b></a> - Due on {task.due_date.strftime('%B %d, %Y')}</p></li>"  # type: ignore[union-attr]


def get_task_bullet_due_next_day(task: Task) -> str:
    return f"<li><p><a href=https://app.zeplyn.ai/tasks/{task.uuid}><b>{task.task_title}</b></a></p></li>"


def fetch_tasks(user: User) -> str:
    tasks_reminder = ""
    tasks_assigned_today = Task.objects.filter(assignee=user, created__date=date.today())
    if not len(tasks_assigned_today) == 0:
        tasks_reminder = "<h3>Tasks which were assigned to you today - </h3>"
        tasks_bullets = "\n".join([get_task_bullet_assigned_today(task) for task in tasks_assigned_today])
        tasks_reminder = f"{tasks_reminder} {tasks_bullets}"

    tasks_due_next_day = Task.objects.filter(assignee=user, due_date__date=date.today() + timedelta(days=1))
    if not len(tasks_due_next_day) == 0:
        tasks_reminder = f"{tasks_reminder}\n<h3>Tasks whose due date is tomorrow - </h3>"
        tasks_bullets = "\n".join([get_task_bullet_due_next_day(task) for task in tasks_due_next_day])
        tasks_reminder = f"{tasks_reminder} {tasks_bullets}"

    return tasks_reminder


def send_task_reminders() -> None:
    users = User.objects.all()

    for user in users:
        if not user.get_preferences().send_task_reminder_email:
            logging.info("Task reminders are disabled for %s", user.email)
            continue
        email = user.email
        task_reminder_to_send = fetch_tasks(user)
        logging.info("Task reminder to send for %s is %s", email, task_reminder_to_send)
        if task_reminder_to_send != "":
            email_service.send_email(
                sender=email, recipients=[email], body=task_reminder_to_send, subject="Task Reminder", is_html=True
            )
        else:
            logging.info("No task reminders to send for user %s", user.uuid)
