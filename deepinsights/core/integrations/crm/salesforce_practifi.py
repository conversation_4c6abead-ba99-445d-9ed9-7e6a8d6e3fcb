import datetime
import logging
from datetime import timed<PERSON><PERSON>
from typing import Any, Literal, Mapping

from simple_salesforce.api import Salesforce
from simple_salesforce.format import format_soql

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.crm import salesforce_utils
from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.crm_models import (
    CRMAccount,
    CRMNote,
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTarget,
    CRMSyncTargets,
    CRMUser,
    CRMWorkflow,
)
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


class SalesforcePractifi(CrmBase):
    def __init__(
        self,
        *,
        user: User | None = None,
        username: str | None = None,
        password: str | None = None,
        consumer_key: str | None = None,
        consumer_secret: str | None = None,
        security_token: str = "",
        instance_url: str | None = None,
        sf: Salesforce | None = None,
    ) -> None:
        super().__init__()
        self.sf = salesforce_utils.simple_salesforce_intstance_with_credentials(
            user=user,
            username=username,
            password=password,
            consumer_key=consumer_key,
            consumer_secret=consumer_secret,
            security_token=security_token,
            instance_url=instance_url,
            sf=sf,
        )

    def get_accounts_by_owner_email_and_name(
        self, owner_email: str, account_name_filter: str | None = None
    ) -> list[CRMAccount]:
        return salesforce_utils.get_salesforce_entity_by_owner_email_and_name(
            self.sf, "Account", owner_email, account_name_filter
        )

    def resolve_sync_targets(
        self, note: Note, user: User, selected_sync_targets: CRMSyncTargets | None = None
    ) -> CRMSyncTargets:
        if (
            selected_sync_targets
            and selected_sync_targets.note_targets
            and len(selected_sync_targets.note_targets) == 1
        ):
            return CRMSyncTargets(
                status=CRMSyncTargets.Status.FINAL,
                note_targets=selected_sync_targets.note_targets,
            )

        if note.client and (client_uuid := note.client.get("uuid")):
            try:
                client = Client.objects.get(uuid=client_uuid)
                if client.crm_id:
                    return self._get_activity_options_for_account(client.crm_id, note, user)
            except Client.DoesNotExist:
                pass

        return CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])

    def _get_activity_options_for_account(self, account_id: str, note: Note, user: User) -> CRMSyncTargets:
        try:
            # Get the "most relevant possible" date for the note
            note_date = (
                note.scheduled_event.start_time
                if note.scheduled_event
                else note.metadata.get("scheduled_at")
                if note.metadata and note.metadata.get("scheduled_at") is not None
                else note.created
            )
            query = format_soql(
                (
                    "SELECT Id, Subject, ActivityDateTime, Description, WhatId "
                    "FROM Event "
                    "WHERE WhatId = {} "
                    "ORDER BY ActivityDateTime DESC"
                ),
                account_id,
            )

            result = self.sf.query_all(query)
            events = result.get("records", [])

            targets_with_time_diffs: list[dict[Literal["target", "time_diff"], Any]] = []
            sync_targets: list[CRMSyncTarget] = []

            for event in events:
                try:
                    event_time = salesforce_utils.datetime_from_salesforce_time(event["ActivityDateTime"])
                    time_diff = abs((event_time - note_date).total_seconds())

                    subject = event.get("Subject", "Untitled Event")
                    time_str = event_time.strftime("%H:%M")
                    display_name = f"{subject} ({time_str})"

                    targets_with_time_diffs.append(
                        {
                            "target": CRMSyncTarget(
                                crm_id=event["Id"],
                                type="event",
                                name=display_name,
                                parent=CRMSyncTarget(
                                    crm_id=account_id,
                                    type="account",
                                    name="",
                                ),
                            ),
                            "time_diff": time_diff,
                        }
                    )
                except Exception:
                    logging.warning("Error parsing event %s", event.get("Id"), exc_info=True)

            targets_with_time_diffs.sort(key=lambda x: x["time_diff"])
            sync_targets.extend([t["target"] for t in targets_with_time_diffs])

        except Exception as e:
            logging.error("Error getting activity options for account %s: %s", account_id, e)

        sync_targets.extend(
            [
                CRMSyncTarget(
                    crm_id="new_event",
                    type="event",
                    name="New Event",
                    parent=CRMSyncTarget(
                        crm_id=account_id,
                        type="account",
                        name="",
                    ),
                ),
                CRMSyncTarget(
                    crm_id="new_call",
                    type="call",
                    name="Log a Call",
                    parent=CRMSyncTarget(
                        crm_id=account_id,
                        type="account",
                        name="",
                    ),
                ),
            ],
        )
        return CRMSyncTargets(
            status=CRMSyncTargets.Status.RESOLUTION_REQUIRED,
            note_targets=sync_targets,
        )

    def add_interaction_with_client(
        self,
        note: Note,
        sync_targets: CRMSyncTargets,
        sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = None,
    ) -> None:
        owner_email = note.note_owner.email  # type: ignore[union-attr]
        owner = salesforce_utils.get_user_id_by_email(self.sf, owner_email)
        if not owner:
            error_msg = f"Could not find a Salesforce User with email: {owner_email}"
            logging.error(error_msg)
            raise ValueError(error_msg)

        if not sync_targets.note_targets:
            error_msg = "No sync target provided. Not adding note."
            logging.error(error_msg)
            raise ValueError(error_msg)

        if len(sync_targets.note_targets) > 1:
            error_msg = "Multiple sync targets provided. Please select a single target."
            logging.error(error_msg)
            raise ValueError(error_msg)

        activity_target = sync_targets.note_targets[0]
        account_id = activity_target.parent.crm_id if activity_target.parent else None

        if not account_id:
            error_msg = "No account ID found in sync target"
            logging.error(error_msg)
            raise ValueError(error_msg)

        interaction_id = note.metadata.get("interactionId")  # type: ignore[union-attr]
        if activity_target.crm_id == "new_event":
            interaction_id = self._create_new_event(note, account_id, owner, sync_items)
        elif activity_target.crm_id == "new_call":
            interaction_id = self._create_new_call(note, account_id, owner, sync_items)
        else:
            self._update_existing_event(activity_target.crm_id, note, account_id, sync_items)
        note.metadata["interactionId"] = interaction_id  # type: ignore[index]
        note.save()

        if note.should_include_section(sync_items, CRMSyncSection.TASKS):
            self._create_task_activities(note, account_id, owner, sync_items)

    def _update_existing_event(
        self,
        event_id: str,
        note: Note,
        account_id: str,
        sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = None,
    ) -> None:
        try:
            event = self.sf.Event.get(event_id)  # type: ignore[operator]
            existing_description = event.get("Description", "")

            new_notes = note.get_summary_for_crm(use_html_formatting=False, sync_items=sync_items)

            separator = (
                "\n\n"
                + "=" * 50
                + "\n"
                + f"Updated from Zeplyn - {note.created.strftime('%Y-%m-%d %H:%M')}\n"
                + "=" * 50
                + "\n\n"
            )
            updated_description = (existing_description or "") + separator + new_notes

            if len(updated_description) > 32000:
                # Truncate new content if needed, preserving existing Salesforce content
                available_space = 32000 - len(separator) - len(existing_description or "") - 10
                if available_space > 0:
                    truncated_new_notes = new_notes[:available_space] + "…"
                    updated_description = (existing_description or "") + separator + truncated_new_notes
                else:
                    # If existing content is too long, truncate our new content significantly
                    max_new_content = 32000 - len(separator) - 10
                    truncated_new_notes = new_notes[:max_new_content] + "…"
                    updated_description = separator + truncated_new_notes

            event_update = {
                "Description": updated_description,
                "WhatId": account_id,
                "practifi__Related_entity__c": account_id,  # Practifi custom field
            }

            self.sf.Event.update(event_id, event_update)  # type: ignore[operator]
            logging.info("Updated existing event %s with meeting notes", event_id)

        except Exception as e:
            error_msg = f"Error updating existing event: {e}"
            logging.error(error_msg)
            raise RuntimeError(error_msg) from e

    def _create_new_event(
        self,
        note: Note,
        account_id: str,
        owner_id: str,
        sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = None,
    ) -> str:
        meeting_name = (note.metadata or {}).get("meeting_name") or "Meeting with client"

        event_data = {
            "Subject": meeting_name,
            "WhatId": account_id,
            "practifi__Related_entity__c": account_id,
            "OwnerId": owner_id,
            "ActivityDateTime": salesforce_utils.salesforceTime(note.created),
            # Assume 60 minutes as a fallback; completed meetings should always have a duration.
            "DurationInMinutes": (note.metadata or {}).get("meeting_duration", 3600) // 60,
            "Description": note.get_summary_for_crm(use_html_formatting=False, sync_items=sync_items),
        }

        try:
            result = self.sf.Event.create(event_data)  # type: ignore[operator]
            logging.info("Created new event: %s", result["id"])
            interaction_id: str = result["id"]
            return interaction_id
        except Exception as e:
            logging.error("Error creating new event", exc_info=True)
            raise Exception("Error creating new event") from e

    def _create_new_call(
        self,
        note: Note,
        account_id: str,
        owner_id: str,
        sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = None,
    ) -> str:
        meeting_name = (note.metadata or {}).get("meeting_name") or "Call with client"

        call_data = {
            "Subject": meeting_name,
            "WhatId": account_id,
            "practifi__Related_entity__c": account_id,  # Practifi custom field
            "OwnerId": owner_id,
            "ActivityDate": note.created.date().isoformat(),
            "Status": "Completed",  # Calls are typically logged as completed
            "TaskSubtype": "Call",  # THIS IS THE KEY - sets it as a call log
            "Priority": "Normal",
            "Description": note.get_summary_for_crm(use_html_formatting=False, sync_items=sync_items),
        }

        try:
            result = self.sf.Task.create(call_data)  # type: ignore[operator]
            logging.info("Created new call: %s", result["id"])
            interaction_id: str = result["id"]
            return interaction_id
        except Exception as e:
            logging.error("Error creating new call", exc_info=True)
            raise Exception("Error creating new call") from e

    def _create_task_activities(
        self,
        note: Note,
        account_id: str,
        owner_id: str,
        sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = None,
    ) -> None:
        for task in note.get_tasks_to_include_crm_sync(sync_items=sync_items):
            assignee_email = getattr(task.assignee, "email", None) if task.assignee else None
            assignee_id = salesforce_utils.resolve_task_assignee_id(
                self.sf, assignee_email, fallback_assignee_id=owner_id
            )

            identifier_details = {
                "WhatId": account_id,
                "practifi__Related_entity__c": account_id,  # Practifi custom field
            }

            try:
                salesforce_utils.add_task(self.sf, task, assignee_id, identifier_details)
                logging.info("Created task activity for: %s", task.task_title)
            except Exception as e:
                logging.error("Error creating task activity: %s", e)

    def fetch_notes_for_client(self, user: User, client: Client, lookback_interval: timedelta) -> list[CRMNote]:
        client_crm_id = client.crm_id
        if not client_crm_id:
            logging.error("Client %s does not have a CRM ID", client.uuid)
            return []

        try:
            start_date = datetime.datetime.now(tz=datetime.timezone.utc) - lookback_interval
            crm_notes: list[CRMNote] = []

            # Query Events
            events_query = format_soql(
                "SELECT Id, Subject, ActivityDateTime, Description, CreatedDate "
                "FROM Event "
                "WHERE WhatId = {} "
                "AND ActivityDateTime >= {:literal} "
                "ORDER BY ActivityDateTime DESC",
                client_crm_id,
                salesforce_utils.salesforceTime(start_date),
            )

            try:
                events_result = self.sf.query_all(events_query)

                for event in events_result.get("records", []):
                    try:
                        created_at = salesforce_utils.datetime_from_salesforce_time(event["ActivityDateTime"])
                    except Exception:
                        created_at = None

                    description = event.get("Description", "").strip()
                    if description:
                        crm_notes.append(
                            CRMNote(
                                crm_id=event["Id"],
                                crm_system="salesforce",
                                content=f"Event: {event.get('Subject', 'Untitled')}\n\n{description}",
                                created_at=created_at,
                                web_link=f"https://{self.sf.sf_instance}/{event['Id']}",
                            )
                        )
            except Exception as e:
                logging.error("Error fetching events for client %s: %s", client_crm_id, e)

            # Query Tasks (including Call logs with TaskSubtype = "Call")
            tasks_query = format_soql(
                "SELECT Id, Subject, ActivityDate, Description, CreatedDate, TaskSubtype "
                "FROM Task "
                "WHERE WhatId = {} "
                "AND ActivityDate >= {:literal} "
                "AND TaskSubtype = 'Call' "
                "ORDER BY ActivityDate DESC",
                client_crm_id,
                salesforce_utils.salesforceTime(start_date),
            )

            try:
                tasks_result = self.sf.query_all(tasks_query)

                # Process call tasks
                for task in tasks_result.get("records", []):
                    try:
                        # For tasks, we use ActivityDate (date only), so create a datetime
                        activity_date = datetime.datetime.strptime(task["ActivityDate"], "%Y-%m-%d")
                        created_at = activity_date.replace(tzinfo=datetime.timezone.utc)
                    except Exception:
                        created_at = None

                    description = task.get("Description", "").strip()
                    if description:
                        crm_notes.append(
                            CRMNote(
                                crm_id=task["Id"],
                                crm_system="salesforce",
                                content=f"Call: {task.get('Subject', 'Untitled')}\n\n{description}",
                                created_at=created_at,
                                web_link=f"https://{self.sf.sf_instance}/{task['Id']}",
                            )
                        )
            except Exception as e:
                logging.error("Error fetching call tasks for client %s: %s", client_crm_id, e)

            # Sort by date (newest first)
            crm_notes.sort(
                key=lambda x: x.created_at or datetime.datetime.min.replace(tzinfo=datetime.timezone.utc), reverse=True
            )

            logging.info("Fetched %d notes for client %s", len(crm_notes), client_crm_id)
            return crm_notes

        except Exception as e:
            logging.error("Error fetching notes for client %s: %s", client_crm_id, e, exc_info=True)
            return []

    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        return None

    def fetch_events(self, user: User, interval: timedelta) -> list[CalendarEvent]:
        return []

    def fetch_workflows_for_user(self, user: User) -> list[CRMWorkflow]:
        return []

    def list_users(self, requesting_user: User) -> list[CRMUser]:
        if not requesting_user.organization:
            logging.error("User does not belong to an organization. Cannot list Salesforce users.")
            return []

        return salesforce_utils.list_crm_users(self.sf, requesting_user.organization)
