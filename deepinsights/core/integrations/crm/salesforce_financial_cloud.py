import datetime
import logging
from datetime import timed<PERSON><PERSON>
from typing import Any, Mapping

from simple_salesforce.api import Salesforce

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.crm import salesforce_utils
from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.crm_models import (
    CRMAccount,
    CRMNote,
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTarget,
    CRMSyncTargets,
    CRMUser,
    CRMWorkflow,
)
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


class SalesforceFinancialCloud(CrmBase):
    def __init__(
        self,
        *,
        user: User | None = None,
        username: str | None = None,
        password: str | None = None,
        consumer_key: str | None = None,
        consumer_secret: str | None = None,
        security_token: str = "",
        instance_url: str | None = None,
        sf: Salesforce | None = None,
    ) -> None:
        super().__init__()
        self.sf = salesforce_utils.simple_salesforce_intstance_with_credentials(
            user=user,
            username=username,
            password=password,
            consumer_key=consumer_key,
            consumer_secret=consumer_secret,
            security_token=security_token,
            instance_url=instance_url,
            sf=sf,
        )

    def get_accounts_by_owner_email_and_name(
        self, owner_email: str, account_name_filter: str | None = None
    ) -> list[CRMAccount]:
        return salesforce_utils.get_salesforce_entity_by_owner_email_and_name(
            self.sf, "Account", owner_email, account_name_filter
        )

    def resolve_sync_targets(
        self, note: Note, user: User, selected_sync_targets: CRMSyncTargets | None = None
    ) -> CRMSyncTargets:
        if note.client and (client_uuid := note.client.get("uuid")):
            try:
                client = Client.objects.get(uuid=client_uuid)
                if client.crm_id:
                    return CRMSyncTargets(
                        status=CRMSyncTargets.Status.FINAL,
                        note_targets=[CRMSyncTarget(crm_id=client.crm_id, type="", name=client.name)],
                    )
            except Client.DoesNotExist:
                pass
        return CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])

    def add_interaction_with_client(
        self,
        note: Note,
        sync_targets: CRMSyncTargets,
        sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = None,
    ) -> None:
        owner_email = note.note_owner.email  # type: ignore[union-attr]
        owner = salesforce_utils.get_user_id_by_email(self.sf, owner_email)
        if not owner:
            error_msg = "Could not find a Salesforce User with the given email: %s" % (owner_email,)
            logging.error(error_msg)
            raise ValueError(error_msg)

        if not sync_targets.note_targets:
            error_msg = "No client provided. Not adding interaction."
            logging.error(error_msg)
            raise ValueError(error_msg)

        if len(sync_targets.note_targets) > 1:
            logging.warning(
                "Multiple clients provided for note s %s. Using the first one.",
                [target.crm_id for target in sync_targets.note_targets],
            )

        # Use the first sync target for the interaction
        primary_sync_target = sync_targets.note_targets[0]
        client_crm_id = primary_sync_target.crm_id

        meeting_name = note.metadata.get("meeting_name") or "Meeting with a client"  # type: ignore[union-attr]
        interaction = {
            "OwnerId": owner,
            "Name": meeting_name,
            "AccountId": client_crm_id,
            "StartTime": salesforce_utils.salesforceTime(note.created),
        }
        logging.info("recording interaction: %s", interaction)
        if not note.metadata.get("interactionId"):  # type: ignore[union-attr]
            try:
                interaction_result = self.sf.Interaction.create(interaction)  # type: ignore[operator]
                note.metadata["interactionId"] = interaction_result["id"]  # type: ignore[index]
                note.save()
                logging.info("interaction saved")
            except Exception as e:
                error_msg = "error recording interaction: %s. Not updating note." % (str(e),)
                logging.error(error_msg)
                raise RuntimeError(error_msg) from e
        else:
            logging.info("Already associated with an interaction: %s", note.metadata.get("interactionId"))  # type: ignore[union-attr]

        tasks_to_include = (
            note.get_tasks_to_include_crm_sync(sync_items=sync_items)
            if note.should_include_section(sync_items, CRMSyncSection.TASKS)
            else []
        )

        next_steps = (
            "*" + "\n*".join([task.task_title for task in tasks_to_include])
            if tasks_to_include
            else "No next steps identified"
        )

        interaction_id = note.metadata.get("interactionId")  # type: ignore[union-attr]
        if not interaction_id:
            error_msg = "No interaction ID associated with note. Skipping summary and task generation."
            logging.error(error_msg)
            raise RuntimeError(error_msg)
        interaction_summary = {
            "OwnerId": owner,
            "Name": meeting_name,
            "InteractionId": note.metadata["interactionId"],  # type: ignore[index]
            "MeetingNotes": note.get_summary_for_crm(use_html_formatting=True, sync_items=sync_items),
            "AccountId": client_crm_id,
            "NextSteps": next_steps,
        }
        logging.info("recording interaction summary: %s", interaction_summary)
        if not note.metadata.get("interactionSummaryId"):  # type: ignore[union-attr]
            try:
                summary_result = self.sf.InteractionSummary.create(interaction_summary)  # type: ignore[operator]
                note.metadata["interactionSummaryId"] = summary_result["id"]  # type: ignore[index]
                note.save()
                logging.info("interaction summary saved")
            except Exception as e:
                error_msg = "error recording interaction summary: %s. Not updating note." % (str(e),)
                logging.error(error_msg)
                raise RuntimeError(error_msg) from e
        else:
            logging.info(
                "Already associated with an interaction summary: %s",
                note.metadata.get("interactionSummaryId"),  # type: ignore[union-attr]
            )

        # Task syncing, this will be empty if no tasks should be synced
        for task in tasks_to_include:
            assignee_email = getattr(task.assignee, "email", None)
            assignee_id = salesforce_utils.resolve_task_assignee_id(self.sf, assignee_email, fallback_assignee_id=owner)

            try:
                salesforce_utils.add_task(
                    self.sf,
                    task,
                    assignee_id,
                    {
                        "WhatId": client_crm_id,
                    },
                )
            except Exception as e:
                error_msg = "Error adding task: %s" % (str(e),)
                logging.error(error_msg)
                raise RuntimeError(error_msg)

    def _fetch_interactions_for_client(self, client_crm_id: str, start_date: datetime.datetime) -> list[CRMNote]:
        crm_notes: list[CRMNote] = []

        try:
            interaction_query = (
                f"SELECT Id, Name, StartTime, AccountId "
                f"FROM Interaction "
                f"WHERE AccountId = '{client_crm_id}' "
                f"AND StartTime >= {salesforce_utils.salesforceTime(start_date)} "
            )
            interactions_result = self.sf.query_all(interaction_query)
            interactions = interactions_result.get("records", [])

            if not interactions:
                logging.info("No interactions found for client %s", client_crm_id)
                return crm_notes

            interaction_ids = [interaction["Id"] for interaction in interactions]
            interaction_ids_str = ",".join([f"'{id}'" for id in interaction_ids])
            summary_query = (
                f"SELECT Id, Name, InteractionId, MeetingNotes, NextSteps, AccountId "
                f"FROM InteractionSummary "
                f"WHERE InteractionId IN ({interaction_ids_str})"
            )
            summaries_result = self.sf.query_all(summary_query)
            summaries = summaries_result.get("records", [])

            summary_dict = {s["InteractionId"]: s for s in summaries}

            for interaction in interactions:
                summary = summary_dict.get(interaction["Id"], {})
                meeting_notes = summary.get("MeetingNotes") or "No notes available"
                next_steps = summary.get("NextSteps") or "No next steps available"
                try:
                    created_at = salesforce_utils.datetime_from_salesforce_time(interaction["StartTime"])
                except Exception:
                    created_at = None
                crm_notes.append(
                    CRMNote(
                        crm_id=interaction["Id"],
                        crm_system="salesforce",
                        content=f"{meeting_notes}\n{next_steps}",
                        created_at=created_at,
                        web_link=f"https://{self.sf.sf_instance}/{interaction['Id']}",
                    )
                )

            logging.info("Found %d interactions for client %s", len(interactions), client_crm_id)

        except Exception as e:
            logging.error("Error fetching interactions for client %s: %s", client_crm_id, e)

        return crm_notes

    def fetch_notes_for_client(self, user: User, client: Client, lookback_interval: timedelta) -> list[CRMNote]:
        client_crm_id = client.crm_id
        if not client_crm_id:
            logging.error("Client does not have a CRM ID. Cannot fetch notes %s, for user %s", client.uuid, user.uuid)
            return []

        try:
            start_date = datetime.datetime.now(tz=datetime.timezone.utc) - lookback_interval
            all_crm_notes: list[CRMNote] = []

            all_crm_notes.extend(self._fetch_interactions_for_client(client_crm_id, start_date))
            all_crm_notes.extend(salesforce_utils.fetch_events_for_client(self.sf, client_crm_id, start_date))
            all_crm_notes.extend(salesforce_utils.fetch_call_tasks_for_client(self.sf, client_crm_id, start_date))

            all_crm_notes.sort(
                key=lambda x: x.created_at or datetime.datetime.min.replace(tzinfo=datetime.timezone.utc), reverse=True
            )

            logging.info(
                "Fetched %d total notes/interactions/events/tasks for client %s", len(all_crm_notes), client_crm_id
            )
            return all_crm_notes

        except Exception as e:
            logging.error("Error fetching notes for client UUID %s, %s:", client_crm_id, str(e), exc_info=True)
            return []

    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        logging.warning("get_client_basic_info not implemented for Salesforce")
        return None

    def fetch_events(self, user: User, interval: timedelta) -> list[CalendarEvent]:
        logging.warning("fetch_crm_events not implemented for SalesforceFinancial")
        return []

    def fetch_workflows_for_user(self, user: User) -> list[CRMWorkflow]:
        logging.warning("fetch_workflows_for_user not implemented for SalesforceFinancial")
        return []

    def list_users(self, requesting_user: User) -> list[CRMUser]:
        if not requesting_user.organization:
            error_msg = "User does not belong to an organization. Cannot list Salesforce finacial cloud CRM users."
            logging.error(error_msg)
            return []

        return salesforce_utils.list_crm_users(self.sf, requesting_user.organization)
