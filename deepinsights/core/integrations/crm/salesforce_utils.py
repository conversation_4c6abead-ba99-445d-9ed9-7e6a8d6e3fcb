"""Utility functions for interacting with Salesforce CRM implementations."""

import datetime
import logging
from functools import lru_cache  # For caching Salesforce user IDs
from typing import Any, Literal, Optional

from django.conf import settings
from simple_salesforce.api import Salesforce
from simple_salesforce.format import format_soql

from deepinsights.core.integrations.crm.crm_models import CRMAccount, CRMNote, CRMUser, ZeplynOrganization, ZeplynUser
from deepinsights.core.integrations.oauth.salesforce import SalesforceOAuth
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User


# Returns a configured simple_salesforce.Salesforce instance from the given credentials.
def simple_salesforce_intstance_with_credentials(
    *,
    user: User | None = None,
    username: str | None = None,
    password: str | None = None,
    consumer_key: str | None = None,
    consumer_secret: str | None = None,
    security_token: str = "",
    instance_url: str | None = None,
    sf: Salesforce | None = None,
) -> Salesforce:
    if sf:
        return sf
    try:
        if (
            user
            and Flags.EnableSalesforceOAuthIntegration.is_active_for_user(user)
            and (access_token := SalesforceOAuth().get_access_token(user))
        ):
            return Salesforce(
                session_id=access_token,
                instance_url=instance_url,
            )
    except Exception as e:
        logging.error("Error while trying to get Salesforce OAuth access token", exc_info=e)
    if (
        user
        and Flags.EnableSalesforceClientCredentialsOAuthIntegration.is_active_for_user(user)
        and consumer_key
        and consumer_secret
    ):
        return Salesforce(
            consumer_key=consumer_key,
            consumer_secret=consumer_secret,
            security_token=security_token,
            instance_url=instance_url,
            domain=settings.SALESFORCE_ZEPLYN_APP_DOMAIN,
        )
    return Salesforce(
        username=username or settings.SALESFORCE_USER,
        password=password or settings.SALESFORCE_PASSWORD,
        security_token=security_token,
        instance_url=instance_url or settings.SALESFORCE_INSTANCE,
    )


# Returns the Salesforce ID for the user with the given email address (or None if that user is not
# found, or that email does not uniqley identify a user).
@lru_cache  # Cache up to max size of 128 (default) user IDs
def get_user_id_by_email(simple_salesforce: Salesforce, user_email: str) -> str | None:
    """
    Get the Salesforce User ID by email address.
    :param user_email: Email address of the Salesforce user
    :return: Salesforce User ID or None if not found
    """
    # Query for the user ID based on email
    result = simple_salesforce.query_all(format_soql("SELECT Id FROM User WHERE Email = {} LIMIT 1", user_email))

    # Extract user ID if found
    if result["totalSize"] == 1:
        user_id = result["records"][0]["Id"]
        return user_id  # type: ignore[no-any-return]
    else:
        return None


# Returns a list of `CRMAccount`s for the provided entity, filtered by the entity_filter.
def get_salesforce_entity_by_owner_email_and_name(
    sf: Salesforce,
    entity_name: Literal["Contact", "Account"],
    owner_email: str,
    entity_filter: str | None = None,
    custom_filter: str | None = None,
) -> list[CRMAccount]:
    owner_id = get_user_id_by_email(sf, owner_email)
    if not owner_id:
        logging.warning("Could not find Salesforce user ID for email: %s", owner_email)
        return []

    query = format_soql("SELECT Id, Name, Phone, OwnerId FROM {:literal}", entity_name)
    if entity_filter or custom_filter:
        query += " WHERE"
    if entity_filter:
        query += format_soql(" Name LIKE '%{:like}%'", entity_filter)
    if custom_filter:
        if entity_filter:
            query += " AND"
        query += f" {custom_filter}"

    entities = (sf.query_all(query) or {}).get("records", [])
    logging.info("Got %s accounts for owner %s (Salesforce ID: %s)", len(entities), owner_email, owner_id)

    return [
        CRMAccount(
            crm_id=entity["Id"],
            name=entity["Name"],
            phone_number=entity["Phone"],
            client_type=entity_name.lower(),
            crm_system="salesforce",
            is_owned_by_user=(entity.get("OwnerId") == owner_id),
        )
        for entity in entities
    ]


_SALESFORCE_DATE_FORMAT = "%Y-%m-%dT%H:%M:%S.%f%z"  # Returns a Salesforce-formatted time for the given datetime.


def salesforceTime(date: datetime.datetime) -> str:
    return date.strftime(_SALESFORCE_DATE_FORMAT)


# Returns a datetime from a Salesforce-formatted time.
def datetime_from_salesforce_time(date: str) -> datetime.datetime:
    return datetime.datetime.strptime(date, _SALESFORCE_DATE_FORMAT)


# Adds a given task to the Salesforce CRM as a Salesforce `Task`.
#
# Can throw; if it throws, the task was not added successfully.
def add_task(simple_salesforce: Salesforce, task: Task, owner_id: str, identifier_details: dict[str, Any]):  # type: ignore[no-untyped-def]
    task_details = {
        "Subject": task.task_title,
        "Description": "Follow up from client meeting." + (f"\n{task.task_desc}" if task.task_desc else ""),
        "Status": "Completed" if task.completed else "Not Started",
        "Priority": "Normal",
        "ActivityDate": salesforceTime((datetime.datetime.today().date() + datetime.timedelta(7))),  # type: ignore[arg-type]
        "OwnerId": owner_id,
    }
    task_details.update(identifier_details)
    logging.info("recording task : %s", task_details)

    # Required because the default is to have metadata empty
    if task.metadata is None:
        task.metadata = {}
        task.save()
    if task.metadata.get("taskId"):
        logging.info("Not writing to CRM since this task already exists")
        return True
    # Create a new task
    new_task = simple_salesforce.Task.create(task_details)  # type: ignore[operator]
    task_id = new_task["id"]
    logging.info(f"Task added successfully. Task ID: {task_id}")
    task.metadata["taskId"] = task_id
    task.save()


def resolve_task_assignee_id(sf: Salesforce, assignee_email: str | None, fallback_assignee_id: str) -> str:
    if assignee_email:
        return get_user_id_by_email(sf, assignee_email) or fallback_assignee_id
    return fallback_assignee_id


def get_active_users(sf: Salesforce) -> list[dict[str, Any]] | None:
    try:
        query = """
            SELECT Id, Name, Email
            FROM User
            WHERE IsActive = true
            ORDER BY CreatedDate Desc
        """
        results = sf.query_all(query)
        return list(results.get("records", []))
    except Exception as e:
        logging.warning("Error fetching Salesforce CRM users: %s", e, exc_info=True)
        return None


def list_crm_users(sf: Salesforce, organization: Organization) -> list[CRMUser]:
    try:
        sf_users = get_active_users(sf)
        if sf_users is None:
            logging.warning(f"Failed to fetch active Salesforce users for organization {organization.name}.")
            return []

        if not sf_users:
            logging.info(f"No active Salesforce users found for organization {organization.name}.")
            return []

        crm_org = ZeplynOrganization(
            uuid=str(organization.uuid),
            name=organization.name,
        )
        emails = [u.get("Email") for u in sf_users if u.get("Email")]

        matched_users = {}
        if emails:
            matched_users_query = User.objects.filter(email__in=emails, organization_id=organization.id).only(
                "id", "email", "name"
            )
            matched_users = {u.email.lower(): u for u in matched_users_query}

        crm_users = []
        for sf_user in sf_users:
            sf_user_id = sf_user.get("Id")
            if not sf_user_id:
                logging.debug(f"Salesforce user entry missing 'Id': {sf_user}. Skipping this user.")
                continue

            email = (sf_user.get("Email") or "").lower()
            matched_user = matched_users.get(email)

            zeplyn_user: Optional[ZeplynUser] = None
            if matched_user:
                zeplyn_user = ZeplynUser(
                    uuid=str(matched_user.uuid),
                    email=matched_user.email,
                    name=matched_user.name,
                )

            crm_users.append(
                CRMUser(
                    name=sf_user.get("Name", "Unknown"),
                    crm_id=sf_user_id,
                    organization=crm_org,
                    zeplyn_user=zeplyn_user,
                )
            )
        return crm_users

    except Exception as e:
        logging.error(
            "Unexpected error while listing Salesforce CRM users for organization %s: %s",
            organization.name,
            e,
            exc_info=True,
        )
        return []


def fetch_events_for_client(sf: Salesforce, client_crm_id: str, start_date: datetime.datetime) -> list[CRMNote]:
    parsed_notes: list[CRMNote] = []

    try:
        events_query = format_soql(
            "SELECT Id, Subject, ActivityDateTime, Description, CreatedDate "
            "FROM Event "
            "WHERE (WhatId = {} OR WhoId = {}) "
            "AND ActivityDateTime >= {:literal} "
            "ORDER BY ActivityDateTime DESC",
            client_crm_id,
            client_crm_id,
            salesforceTime(start_date),
        )

        events_result = sf.query_all(events_query)

        if not events_result.get("records"):
            logging.info("No events found for client %s", client_crm_id)
        else:
            for event in events_result.get("records", []):
                try:
                    created_at = datetime_from_salesforce_time(event["ActivityDateTime"])
                except Exception:
                    created_at = None

                description = event.get("Description", "").strip()
                if description:
                    parsed_notes.append(
                        CRMNote(
                            crm_id=event["Id"],
                            crm_system="salesforce",
                            content=f"Event: {event.get('Subject', 'Untitled')}\n\n{description}",
                            created_at=created_at,
                            web_link=f"https://{sf.sf_instance}/{event['Id']}",
                        )
                    )
            logging.info("Fetched %d events for client %s", len(parsed_notes), client_crm_id)
    except Exception as e:
        logging.error("Error fetching events for client %s: %s", client_crm_id, e)

    return parsed_notes


def fetch_call_tasks_for_client(sf: Salesforce, client_crm_id: str, start_date: datetime.datetime) -> list[CRMNote]:
    parsed_notes: list[CRMNote] = []

    try:
        tasks_query = format_soql(
            "SELECT Id, Subject, ActivityDate, Description, CreatedDate, TaskSubtype "
            "FROM Task "
            "WHERE (WhatId = {} OR WhoId = {}) "
            "AND ActivityDate >= {:literal} "
            "AND TaskSubtype = 'Call' "
            "ORDER BY ActivityDate DESC",
            client_crm_id,
            client_crm_id,
            start_date.date().isoformat(),
        )

        tasks_result = sf.query_all(tasks_query)

        if not tasks_result.get("records"):
            logging.info("No Logged Phone Calls found for client %s", client_crm_id)
        else:
            # Process call tasks
            for task in tasks_result.get("records", []):
                try:
                    # For tasks, we use ActivityDate (date only), so create a datetime
                    activity_date = datetime.datetime.strptime(task["ActivityDate"], "%Y-%m-%d")
                    created_at = activity_date.replace(tzinfo=datetime.timezone.utc)
                except Exception:
                    created_at = None

                description = task.get("Description", "").strip()
                if description:
                    parsed_notes.append(
                        CRMNote(
                            crm_id=task["Id"],
                            crm_system="salesforce",
                            content=f"Call: {task.get('Subject', 'Untitled')}\n\n{description}",
                            created_at=created_at,
                            web_link=f"https://{sf.sf_instance}/{task['Id']}",
                        )
                    )
            logging.info("Fetched %d call tasks for client %s", len(parsed_notes), client_crm_id)
    except Exception as e:
        logging.error("Error fetching call tasks for client %s: %s", client_crm_id, e)

    return parsed_notes
