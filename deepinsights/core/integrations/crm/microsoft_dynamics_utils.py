import logging
from typing import Any
from urllib.parse import urljoin

import requests
from pydantic import ValidationError

from deepinsights.core.integrations.crm.crm_models import CRMUser, ZeplynOrganization, ZeplynUser
from deepinsights.core.integrations.oauth.microsoft_dynamics import MicrosoftDynamicsOAuth
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.user import User


def get_api_url(user: User, oauth: MicrosoftDynamicsOAuth, api_version: str = "v9.2") -> tuple[str, str]:
    """Returns the API URL and access token for a Microsoft Dynamics user."""
    access_token = oauth.get_access_token(user)
    if not access_token:
        raise Exception(f"Could not get access token for user {user.uuid}")

    crm_config = user.get_crm_configuration()
    dynamics_resource_url = crm_config.dynamics.dynamics_resource_url
    api_url = urljoin(dynamics_resource_url, f"api/data/{api_version}/")
    return api_url, access_token


def get_headers(access_token: str) -> dict[str, str]:
    """Get complete headers for all Microsoft Dynamics request types."""
    return {
        "Authorization": f"Bearer {access_token}",
        "Accept": "application/json",
        "Content-Type": "application/json",
        "OData-MaxVersion": "4.0",
        "OData-Version": "4.0",
    }


def fetch_crm_users(api_url: str, access_token: str) -> list[dict[str, Any]]:
    """Fetch active CRM system users from Microsoft Dynamics."""
    try:
        headers = get_headers(access_token)
        users_url = urljoin(api_url, "systemusers")
        params = {
            "$select": "fullname,systemuserid,internalemailaddress",
            "$filter": "statecode eq 0",  # Only active users
        }
        response = requests.get(users_url, headers=headers, params=params)
        if not response.ok:
            logging.error(f"Error fetching CRM users from Microsoft Dynamics: {response.status_code}, {response.text}")
            return []

        users_data = response.json().get("value", [])
        logging.info(f"Found {len(users_data)} active Microsoft Dynamics CRM users")
        return list(users_data)
    except requests.RequestException as e:
        logging.error(f"HTTP error during Microsoft Dynamics CRM API call: {e}", exc_info=True)
    except Exception as e:
        logging.error(f"Unexpected error fetching Microsoft Dynamics CRM users: {e}", exc_info=True)
    return []


def map_crm_users_to_zeplyn_users(dynamics_users: list[dict[str, Any]], organization: Organization) -> list[CRMUser]:
    crm_org = ZeplynOrganization(
        uuid=str(organization.uuid),
        name=organization.name,
    )
    emails = [email.lower() for email in (u.get("internalemailaddress") for u in dynamics_users) if email]
    matched_users = {}
    if emails:
        matched_users_query = User.objects.filter(email__in=emails, organization=organization).only(
            "uuid", "email", "name"
        )
        matched_users = {u.email: u for u in matched_users_query}

    crm_users = []
    for dynamics_user in dynamics_users:
        email = dynamics_user.get("internalemailaddress")
        matched_user = matched_users.get(email) if email else None

        zeplyn_user: ZeplynUser | None = None
        if matched_user:
            zeplyn_user = ZeplynUser(
                uuid=str(matched_user.uuid),
                email=matched_user.email,
                name=matched_user.name,
            )
        try:
            crm_users.append(
                CRMUser(
                    crm_id=dynamics_user.get("systemuserid"),
                    name=dynamics_user.get("fullname"),
                    organization=crm_org,
                    zeplyn_user=zeplyn_user,
                )
            )
        except ValidationError as e:
            logging.error(f"Error creating CRMUser object for user: {dynamics_user}", exc_info=e)

    logging.info(f"Successfully mapped {len(crm_users)} Microsoft Dynamics CRM users")
    return crm_users


def list_crm_users(oauth: MicrosoftDynamicsOAuth, user: User) -> list[CRMUser]:
    api_url, access_token = get_api_url(user, oauth)
    dynamics_users = fetch_crm_users(api_url, access_token)

    organization = user.organization
    if not organization:
        logging.warning(f"User {user.uuid} does not have an associated organization.")
        return []

    if not dynamics_users:
        logging.warning(f"Failed to fetch active Microsoft Dynamics users for organization {organization.name}.")
        return []

    return map_crm_users_to_zeplyn_users(dynamics_users, organization)


def prepare_task_subject_and_description(subject: str | None, description: str | None) -> tuple[str, str]:
    description_body = description or ""
    subject = (subject or "").strip() or "No Subject"

    ELLIPSIS = "…"
    if len(subject) > 200:  # since microsoft dynamic has a limit of 200 characters for subject
        safe_subject = subject[: 200 - len(ELLIPSIS)] + ELLIPSIS
        # Full subject first, then task desc
        full_description = f"Subject: {subject}\n\n Description:\n{description_body}"
    else:
        safe_subject = subject
        full_description = description_body

    return safe_subject, full_description
