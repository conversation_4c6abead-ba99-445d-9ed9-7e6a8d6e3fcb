import datetime
import logging
import re
from datetime import timed<PERSON>ta
from typing import Any
from unittest.mock import ANY, MagicMock, call, patch
from uuid import uuid4

import pytest
from django.utils import timezone as django_timezone

from deepinsights.core.integrations.crm.crm_models import (
    CRMSyncTarget,
    CRMSyncTargets,
    CRMUser,
    ZeplynOrganization,
    ZeplynUser,
)
from deepinsights.core.integrations.crm.microsoft_dynamics_utils import get_api_url, get_headers
from deepinsights.core.integrations.crm.tamarac_microsoft_dynamics import TamaracMicrosoftDynamics
from deepinsights.core.preferences.preferences import CrmConfiguration, DynamicsConfiguration
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User

pytestmark = [pytest.mark.django_db]


@pytest.fixture
def test_organization() -> Organization:
    return Organization.objects.create(name="Test Organization")


@pytest.fixture
def test_user(test_organization: Organization) -> User:
    user = User.objects.create(email="<EMAIL>", organization=test_organization)

    dynamics_config = DynamicsConfiguration(dynamics_resource_url="https://tamarac.example.com")
    crm_config = CrmConfiguration(crm_system="microsoft_dynamics", dynamics=dynamics_config)

    def get_crm_configuration() -> CrmConfiguration:
        return crm_config

    user.get_crm_configuration = get_crm_configuration  # type: ignore[method-assign]
    return user


@pytest.fixture
def test_user_without_org() -> User:
    user = User.objects.create(email="<EMAIL>", organization=None)

    dynamics_config = DynamicsConfiguration(dynamics_resource_url="https://org.example.com")

    crm_config = CrmConfiguration(crm_system="microsoft_dynamics", dynamics=dynamics_config)

    # Create a custom get_crm_configuration method
    def get_crm_configuration() -> CrmConfiguration:
        return crm_config

    # Attach it to the user
    setattr(user, "get_crm_configuration", get_crm_configuration)

    return user


@pytest.fixture
def test_client(test_user: User, test_organization: Organization) -> Client:
    client = Client.objects.create(
        name="Test Household Client", organization=test_organization, crm_id="household123", client_type="Account"
    )
    client.authorized_users.add(test_user)
    client.save()
    return client


@pytest.fixture
def test_meeting_type_debrief() -> MeetingType:
    return MeetingType.objects.create(name="Debrief", category="debrief")


@pytest.fixture
def test_note(test_user: User, test_client: Client) -> Note:
    return Note.objects.create(
        note_owner=test_user, client={"uuid": str(test_client.uuid)}, summary="Test Tamarac Note"
    )


@pytest.fixture
def test_note_debrief(
    test_user: User, test_client: Client, test_meeting_type_debrief: MeetingType, test_tasks: list[Task]
) -> Note:
    note = Note.objects.create(
        note_owner=test_user,
        client={"uuid": str(test_client.uuid)},
        summary="Test Debrief Note",
        meeting_type=test_meeting_type_debrief,
    )
    for task in test_tasks:
        task.note = note
        task.save()
    return note


@pytest.fixture
def test_tasks(test_note: Note) -> list[Task]:
    base_date = datetime.datetime(2025, 6, 10, 12, 0, 0, tzinfo=datetime.timezone.utc)
    tasks = [
        Task.objects.create(
            note=test_note,
            task_title="Review portfolio allocation",
            task_desc="Check current allocation vs target",
            due_date=base_date + datetime.timedelta(days=7),
        ),
        Task.objects.create(
            note=test_note,
            task_title="Schedule follow-up meeting",
            task_desc="Book next quarterly review",
            due_date=base_date + datetime.timedelta(days=10),
        ),
        Task.objects.create(
            note=test_note, task_title="Send tax documents", due_date=base_date + datetime.timedelta(days=3)
        ),
    ]
    return tasks


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_accounts_by_owner_email_and_name_household_only(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    account_types_response = {
        "value": [
            {
                "tam_accounttypeid": "0e3c9bf0-54af-e111-b4b4-b8ac6f153db6",
                "tam_name": "Household",
            },
            {
                "tam_accounttypeid": "4256867a-399d-e611-80e3-005056951f51",
                "tam_name": "Household - Courtesy",
            },
        ]
    }

    household_accounts_response = {
        "value": [
            {
                "name": "Smith Household",
                "accountid": "household123",
                "telephone1": "**********",
                "emailaddress1": "<EMAIL>",
                "tam_uploadid": "TAM001",
                "_tam_accounttypeid_value": "0e3c9bf0-54af-e111-b4b4-b8ac6f153db6",
                "statecode": 0,
            },
            {
                "name": "Johnson Household - Courtesy",
                "accountid": "household456",
                "telephone1": "**********",
                "emailaddress1": "<EMAIL>",
                "tam_uploadid": "TAM002",
                "_tam_accounttypeid_value": "4256867a-399d-e611-80e3-005056951f51",
                "statecode": 0,
            },
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: account_types_response, ok=True),
            MagicMock(status_code=200, json=lambda: household_accounts_response, ok=True),
        ]

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        accounts = dynamics.get_accounts_by_owner_email_and_name("<EMAIL>")

        assert len(accounts) == 2
        assert accounts[0].name == "Smith Household"
        assert accounts[0].client_type == "Account"
        assert accounts[0].crm_system == "microsoft_dynamics"
        assert accounts[1].name == "Johnson Household - Courtesy"

        assert mock_requests_get.call_count == 2

        account_types_call = mock_requests_get.call_args_list[0]
        assert "tam_accounttypes" in account_types_call[0][0]
        assert "contains(tam_name, 'Household')" in account_types_call[1]["params"]["$filter"]

        accounts_call = mock_requests_get.call_args_list[1]
        assert "accounts" in accounts_call[0][0]
        filter_param = accounts_call[1]["params"]["$filter"]
        assert "0e3c9bf0-54af-e111-b4b4-b8ac6f153db6" in filter_param
        assert "4256867a-399d-e611-80e3-005056951f51" in filter_param
        assert "statecode eq 0" in filter_param


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_accounts_by_owner_email_and_name_with_filter(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    account_types_response = {
        "value": [
            {
                "tam_accounttypeid": "household-type-123",
                "tam_name": "Household",
            },
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: account_types_response, ok=True),
            MagicMock(status_code=200, json=lambda: {"value": []}, ok=True),
        ]

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        dynamics.get_accounts_by_owner_email_and_name("<EMAIL>", "Smith")

        accounts_call = mock_requests_get.call_args_list[1]
        filter_param = accounts_call[1]["params"]["$filter"]
        assert "contains(name, 'Smith')" in filter_param
        assert "statecode eq 0" in filter_param
        assert "_tam_accounttypeid_value eq 'household-type-123'" in filter_param


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_household_account_type_ids_success(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    account_types_response = {
        "value": [
            {
                "tam_accounttypeid": "household-123",
                "tam_name": "Household",
            },
            {
                "tam_accounttypeid": "household-courtesy-456",
                "tam_name": "Household - Courtesy",
            },
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: account_types_response, ok=True)

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        api_url, access_token = get_api_url(test_user, dynamics.oauth)
        headers = get_headers(access_token)

        household_ids = dynamics._get_household_account_type_ids(api_url, headers)

        assert len(household_ids) == 2
        assert "household-123" in household_ids
        assert "household-courtesy-456" in household_ids

        call_args = mock_requests_get.call_args
        assert "tam_accounttypes" in call_args[0][0]
        params = call_args[1]["params"]
        assert "statecode eq 0" in params["$filter"]
        assert "contains(tam_name, 'Household')" in params["$filter"]


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_household_account_type_ids_empty_response(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: {"value": []}, ok=True)

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        api_url, access_token = get_api_url(test_user, dynamics.oauth)
        headers = get_headers(access_token)

        household_ids = dynamics._get_household_account_type_ids(api_url, headers)

        assert household_ids == []


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_household_account_type_ids_api_error(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.get") as mock_requests_get, patch("logging.error") as mock_error:
        mock_requests_get.return_value = MagicMock(status_code=500, text="Server Error", ok=False)

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        api_url, access_token = get_api_url(test_user, dynamics.oauth)
        headers = get_headers(access_token)

        household_ids = dynamics._get_household_account_type_ids(api_url, headers)

        assert household_ids == []
        mock_error.assert_called()
        error_messages = [str(call) for call in mock_error.call_args_list]
        assert any("Error fetching account types" in msg for msg in error_messages)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_accounts_no_household_types_found(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.get") as mock_requests_get, patch("logging.warning") as mock_warning:
        mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: {"value": []}, ok=True)

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        accounts = dynamics.get_accounts_by_owner_email_and_name("<EMAIL>")

        assert accounts == []
        mock_warning.assert_called()
        warning_messages = [str(call) for call in mock_warning.call_args_list]
        assert any("No household account types found" in msg for msg in warning_messages)

        # Should only call account types endpoint, not accounts endpoint
        assert mock_requests_get.call_count == 1


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.post")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.patch")
def test_add_interaction_with_client_new_appointment(
    mock_requests_patch: MagicMock,
    mock_requests_post: MagicMock,
    mock_oauth: MagicMock,
    test_note: Note,
    test_user: User,
    test_tasks: list[Task],
) -> None:
    """Test adding appointment interaction with consolidated tasks."""

    ScheduledEvent.objects.create(
        start_time=datetime.datetime(2025, 6, 3, 10, 0, 0, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2025, 6, 3, 11, 0, 0, tzinfo=datetime.timezone.utc),
        note=test_note,
        user=test_user,
    )
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    appointment_response = MagicMock(status_code=201, ok=True)
    appointment_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/appointments(appt123)"}

    task_response = MagicMock(status_code=201, ok=True)
    task_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/tasks(task456)"}

    mock_requests_post.side_effect = [appointment_response, task_response]

    mock_requests_patch.return_value = MagicMock(status_code=200, ok=True)

    with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
        mock_summary.return_value = "Meeting summary"
        mock_title.return_value = "Client Meeting"

        dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
        client_target = CRMSyncTarget(crm_id="household123", type="account", name="Test Household Client")
        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(
                    crm_id="new_appointment", type="appointment", name="New appointment", parent=client_target
                )
            ],
        )
        dynamics.add_interaction_with_client(test_note, sync_targets)

    mock_requests_patch.assert_called_once_with(
        "https://tamarac.example.com/api/data/v9.2/accounts(household123)",
        headers={
            "Authorization": "Bearer mock_access_token",
            "Accept": "application/json",
            "Content-Type": "application/json",
            "OData-MaxVersion": "4.0",
            "OData-Version": "4.0",
        },
        json={"tam_lastappointment": "2025-06-03"},
    )

    mock_requests_post.assert_has_calls(
        [
            call(
                "https://tamarac.example.com/api/data/v9.2/appointments",
                headers=ANY,
                json={
                    "subject": "Client Meeting",
                    "description": "Meeting summary",
                    "scheduledstart": "2025-06-03T10:00:00+00:00",
                    "scheduledend": "2025-06-03T11:00:00+00:00",
                    "statecode": 1,
                    "<EMAIL>": "/accounts(household123)",
                },
            ),
            call(
                "https://tamarac.example.com/api/data/v9.2/tasks",
                headers=ANY,
                json={
                    "subject": "Client Meeting - Follow Up",
                    "description": "Follow-up tasks from meeting: Client Meeting\n\n1. Review portfolio allocation: Check current allocation vs target (Due: 2025-06-17)\n2. Schedule follow-up meeting: Book next quarterly review (Due: 2025-06-20)\n3. Send tax documents (Due: 2025-06-13)",
                    "scheduledend": ANY,
                    "statecode": 0,
                    "prioritycode": 1,
                    "<EMAIL>": "/accounts(household123)",
                },
            ),
        ]
    )

    updated_note = Note.objects.get(pk=test_note.pk)
    assert (updated_note.metadata or {}).get("interactionId") == "appt123"

    for task in test_tasks:
        updated_task = Task.objects.get(pk=task.pk)
        assert (updated_task.metadata or {})["consolidatedTaskId"] == "task456"


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.post")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.patch")
def test_add_interaction_with_client_new_phone_call(
    mock_requests_patch: MagicMock,
    mock_requests_post: MagicMock,
    mock_oauth: MagicMock,
    test_note_debrief: Note,
) -> None:
    """Test adding phone call interaction."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    phone_call_response = MagicMock(status_code=201, ok=True)
    phone_call_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/phonecalls(call123)"}

    task_response = MagicMock(status_code=201, ok=True)
    task_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/tasks(task456)"}

    patch_response = MagicMock(status_code=200, ok=True)

    mock_requests_post.side_effect = [phone_call_response, task_response]
    mock_requests_patch.return_value = patch_response

    with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
        mock_summary.return_value = "Debrief notes"
        mock_title.return_value = "Team Debrief"

        dynamics = TamaracMicrosoftDynamics(user=test_note_debrief.note_owner)  # type: ignore[arg-type]
        client_target = CRMSyncTarget(crm_id="household123", type="account", name="Test Household Client")
        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(crm_id="new_phone_call", type="phonecall", name="New phone call", parent=client_target)
            ],
        )
        dynamics.add_interaction_with_client(test_note_debrief, sync_targets)

    mock_requests_patch.assert_called_once_with(
        "https://tamarac.example.com/api/data/v9.2/accounts(household123)",
        headers={
            "Authorization": "Bearer mock_access_token",
            "Accept": "application/json",
            "Content-Type": "application/json",
            "OData-MaxVersion": "4.0",
            "OData-Version": "4.0",
        },
        json={"tam_lastcall": ANY},
    )

    mock_requests_post.assert_has_calls(
        [
            call(
                "https://tamarac.example.com/api/data/v9.2/phonecalls",
                headers=ANY,
                json={
                    "subject": "Team Debrief",
                    "description": "Debrief notes",
                    "scheduledstart": ANY,
                    "scheduledend": ANY,
                    "statecode": 1,
                    "<EMAIL>": "/accounts(household123)",
                },
            ),
            call(
                "https://tamarac.example.com/api/data/v9.2/tasks",
                headers=ANY,
                json={
                    "subject": "Team Debrief - Follow Up",
                    "description": "Follow-up tasks from meeting: Team Debrief\n\n1. Review portfolio allocation: Check current allocation vs target (Due: 2025-06-17)\n2. Schedule follow-up meeting: Book next quarterly review (Due: 2025-06-20)\n3. Send tax documents (Due: 2025-06-13)",
                    "scheduledend": ANY,
                    "statecode": 0,
                    "prioritycode": 1,
                    "<EMAIL>": "/accounts(household123)",
                },
            ),
        ]
    )

    updated_note = Note.objects.get(pk=test_note_debrief.pk)
    assert (updated_note.metadata or {}).get("interactionId") == "call123"


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.post")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.patch")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.get")
def test_add_interaction_with_client_existing_appointment(
    mock_requests_get: MagicMock,
    mock_requests_patch: MagicMock,
    mock_requests_post: MagicMock,
    mock_oauth: MagicMock,
    test_note: Note,
    test_user: User,
    test_tasks: list[Task],
) -> None:
    ScheduledEvent.objects.create(
        start_time=datetime.datetime(2025, 6, 3, 10, 0, 0, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2025, 6, 3, 11, 0, 0, tzinfo=datetime.timezone.utc),
        note=test_note,
        user=test_user,
    )
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    get_response = MagicMock(status_code=200, ok=True)
    get_response.json.return_value = {
        "description": "Existing appointment notes",
        "scheduledstart": "2025-06-01T09:00:00Z",
    }
    mock_requests_get.return_value = get_response

    task_response = MagicMock(status_code=201, ok=True)
    task_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/tasks(task456)"}
    mock_requests_post.return_value = task_response

    mock_requests_patch.return_value = MagicMock(status_code=200, ok=True)

    with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
        mock_summary.return_value = "Meeting summary"
        mock_title.return_value = "Client Meeting"

        appointment_id = str(uuid4())
        dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
        client_target = CRMSyncTarget(crm_id="household123", type="account", name="Test Household Client")
        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(
                    crm_id=appointment_id, type="appointment", name="Existing appointment", parent=client_target
                )
            ],
        )
        dynamics.add_interaction_with_client(test_note, sync_targets)

    # Verify GET request to fetch existing appointment description
    mock_requests_get.assert_called_once_with(
        f"https://tamarac.example.com/api/data/v9.2/appointments({appointment_id})",
        headers={
            "Authorization": "Bearer mock_access_token",
            "Accept": "application/json",
            "Content-Type": "application/json",
            "OData-MaxVersion": "4.0",
            "OData-Version": "4.0",
        },
        params={"$select": "description,scheduledstart"},
    )

    # Verify PATCH calls for updating appointment description and client last appointment time
    mock_requests_patch.assert_has_calls(
        [
            call(
                f"https://tamarac.example.com/api/data/v9.2/appointments({appointment_id})",
                headers={
                    "Authorization": "Bearer mock_access_token",
                    "Accept": "application/json",
                    "Content-Type": "application/json",
                    "OData-MaxVersion": "4.0",
                    "OData-Version": "4.0",
                },
                json={"description": "Existing appointment notes\n\nMeeting summary"},
            ),
            call(
                "https://tamarac.example.com/api/data/v9.2/accounts(household123)",
                headers={
                    "Authorization": "Bearer mock_access_token",
                    "Accept": "application/json",
                    "Content-Type": "application/json",
                    "OData-MaxVersion": "4.0",
                    "OData-Version": "4.0",
                },
                json={"tam_lastappointment": "2025-06-01"},
            ),
        ]
    )

    # Verify task creation
    mock_requests_post.assert_called_once_with(
        "https://tamarac.example.com/api/data/v9.2/tasks",
        headers=ANY,
        json={
            "subject": "Client Meeting - Follow Up",
            "description": "Follow-up tasks from meeting: Client Meeting\n\n1. Review portfolio allocation: Check current allocation vs target (Due: 2025-06-17)\n2. Schedule follow-up meeting: Book next quarterly review (Due: 2025-06-20)\n3. Send tax documents (Due: 2025-06-13)",
            "scheduledend": ANY,
            "statecode": 0,
            "prioritycode": 1,
            "<EMAIL>": "/accounts(household123)",
        },
    )

    updated_note = Note.objects.get(pk=test_note.pk)
    assert (updated_note.metadata or {}).get("interactionId") == appointment_id

    for task in test_tasks:
        updated_task = Task.objects.get(pk=task.pk)
        assert (updated_task.metadata or {})["consolidatedTaskId"] == "task456"


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.post")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.patch")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.get")
def test_add_interaction_with_client_existing_phone_call(
    mock_requests_get: MagicMock,
    mock_requests_patch: MagicMock,
    mock_requests_post: MagicMock,
    mock_oauth: MagicMock,
    test_note_debrief: Note,
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    get_response = MagicMock(status_code=200, ok=True)
    get_response.json.return_value = {
        "description": "Existing phone call notes",
        "scheduledstart": "2025-06-01T09:00:00Z",
    }
    mock_requests_get.return_value = get_response

    task_response = MagicMock(status_code=201, ok=True)
    task_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/tasks(task456)"}
    mock_requests_post.return_value = task_response

    mock_requests_patch.return_value = MagicMock(status_code=200, ok=True)

    phone_call_id = str(uuid4())
    with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
        mock_summary.return_value = "Debrief notes"
        mock_title.return_value = "Team Debrief"

        dynamics = TamaracMicrosoftDynamics(user=test_note_debrief.note_owner)  # type: ignore[arg-type]
        client_target = CRMSyncTarget(crm_id="household123", type="account", name="Test Household Client")
        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(crm_id=phone_call_id, type="phonecall", name="Existing phone call", parent=client_target)
            ],
        )
        dynamics.add_interaction_with_client(test_note_debrief, sync_targets)

    # Verify GET request to fetch existing phone call description
    mock_requests_get.assert_called_once_with(
        f"https://tamarac.example.com/api/data/v9.2/phonecalls({phone_call_id})",
        headers={
            "Authorization": "Bearer mock_access_token",
            "Accept": "application/json",
            "Content-Type": "application/json",
            "OData-MaxVersion": "4.0",
            "OData-Version": "4.0",
        },
        params={"$select": "description,scheduledstart"},
    )

    # Verify PATCH calls for updating phone call description and client last call time
    mock_requests_patch.assert_has_calls(
        [
            call(
                f"https://tamarac.example.com/api/data/v9.2/phonecalls({phone_call_id})",
                headers={
                    "Authorization": "Bearer mock_access_token",
                    "Accept": "application/json",
                    "Content-Type": "application/json",
                    "OData-MaxVersion": "4.0",
                    "OData-Version": "4.0",
                },
                json={"description": "Existing phone call notes\n\nDebrief notes"},
            ),
            call(
                "https://tamarac.example.com/api/data/v9.2/accounts(household123)",
                headers={
                    "Authorization": "Bearer mock_access_token",
                    "Accept": "application/json",
                    "Content-Type": "application/json",
                    "OData-MaxVersion": "4.0",
                    "OData-Version": "4.0",
                },
                json={"tam_lastcall": "2025-06-01"},
            ),
        ]
    )

    # Verify task creation
    mock_requests_post.assert_called_once_with(
        "https://tamarac.example.com/api/data/v9.2/tasks",
        headers=ANY,
        json={
            "subject": "Team Debrief - Follow Up",
            "description": "Follow-up tasks from meeting: Team Debrief\n\n1. Review portfolio allocation: Check current allocation vs target (Due: 2025-06-17)\n2. Schedule follow-up meeting: Book next quarterly review (Due: 2025-06-20)\n3. Send tax documents (Due: 2025-06-13)",
            "scheduledend": ANY,
            "statecode": 0,
            "prioritycode": 1,
            "<EMAIL>": "/accounts(household123)",
        },
    )

    updated_note = Note.objects.get(pk=test_note_debrief.pk)
    assert (updated_note.metadata or {}).get("interactionId") == phone_call_id


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.post")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.patch")
def test_add_interaction_with_client_creation_exception(
    mock_requests_patch: MagicMock,
    mock_requests_post: MagicMock,
    mock_oauth: MagicMock,
    test_note: Note,
    test_user: User,
    test_tasks: list[Task],
) -> None:
    ScheduledEvent.objects.create(
        start_time=datetime.datetime(2025, 6, 3, 10, 0, 0, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2025, 6, 3, 11, 0, 0, tzinfo=datetime.timezone.utc),
        note=test_note,
        user=test_user,
    )
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    mock_requests_post.side_effect = Exception("API creation failed")

    with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
        mock_summary.return_value = "Meeting summary"
        mock_title.return_value = "Client Meeting"

        dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
        client_target = CRMSyncTarget(crm_id="household123", type="account", name="Test Household Client")
        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(
                    crm_id="new_appointment", type="appointment", name="New appointment", parent=client_target
                )
            ],
        )
        with pytest.raises(Exception, match="API creation failed"):
            dynamics.add_interaction_with_client(test_note, sync_targets)

    mock_requests_patch.assert_not_called()

    mock_requests_post.assert_called_once()

    updated_note = Note.objects.get(pk=test_note.pk)
    assert not updated_note.metadata

    for task in test_tasks:
        task.refresh_from_db()
        assert not task.metadata


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.post")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.patch")
def test_add_interaction_with_client_creation_failure(
    mock_requests_patch: MagicMock,
    mock_requests_post: MagicMock,
    mock_oauth: MagicMock,
    test_note: Note,
    test_user: User,
    test_tasks: list[Task],
) -> None:
    ScheduledEvent.objects.create(
        start_time=datetime.datetime(2025, 6, 3, 10, 0, 0, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2025, 6, 3, 11, 0, 0, tzinfo=datetime.timezone.utc),
        note=test_note,
        user=test_user,
    )
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    mock_requests_post.return_value = MagicMock(status_code=500, text="Internal Server Error", ok=False)

    with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
        mock_summary.return_value = "Meeting summary"
        mock_title.return_value = "Client Meeting"

        dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
        client_target = CRMSyncTarget(crm_id="household123", type="account", name="Test Household Client")
        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(
                    crm_id="new_appointment", type="appointment", name="New appointment", parent=client_target
                )
            ],
        )
        with pytest.raises(Exception, match="Failed to create new appointment in Dynamics"):
            dynamics.add_interaction_with_client(test_note, sync_targets)

    mock_requests_patch.assert_not_called()

    mock_requests_post.assert_called_once()

    updated_note = Note.objects.get(pk=test_note.pk)
    assert not updated_note.metadata

    for task in test_tasks:
        task.refresh_from_db()
        assert not task.metadata


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.post")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.patch")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.get")
def test_add_interaction_with_client_fetch_existing_exception(
    mock_requests_get: MagicMock,
    mock_requests_patch: MagicMock,
    mock_requests_post: MagicMock,
    mock_oauth: MagicMock,
    test_note: Note,
    test_user: User,
    test_tasks: list[Task],
) -> None:
    ScheduledEvent.objects.create(
        start_time=datetime.datetime(2025, 6, 3, 10, 0, 0, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2025, 6, 3, 11, 0, 0, tzinfo=datetime.timezone.utc),
        note=test_note,
        user=test_user,
    )
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    mock_requests_get.side_effect = Exception("API get existing failed")

    with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
        mock_summary.return_value = "Meeting summary"
        mock_title.return_value = "Client Meeting"

        appointment_id = str(uuid4())
        dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
        client_target = CRMSyncTarget(crm_id="household123", type="account", name="Test Household Client")
        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(
                    crm_id=appointment_id, type="appointment", name="Existing appointment", parent=client_target
                )
            ],
        )
        with pytest.raises(Exception, match="API get existing failed"):
            dynamics.add_interaction_with_client(test_note, sync_targets)

    mock_requests_patch.assert_not_called()
    mock_requests_post.assert_not_called()

    updated_note = Note.objects.get(pk=test_note.pk)
    assert not updated_note.metadata

    for task in test_tasks:
        task.refresh_from_db()
        assert not task.metadata


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.post")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.patch")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.get")
def test_add_interaction_with_client_fetch_existing_failure(
    mock_requests_get: MagicMock,
    mock_requests_patch: MagicMock,
    mock_requests_post: MagicMock,
    mock_oauth: MagicMock,
    test_note: Note,
    test_user: User,
    test_tasks: list[Task],
) -> None:
    ScheduledEvent.objects.create(
        start_time=datetime.datetime(2025, 6, 3, 10, 0, 0, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2025, 6, 3, 11, 0, 0, tzinfo=datetime.timezone.utc),
        note=test_note,
        user=test_user,
    )
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    mock_requests_get.return_value = MagicMock(status_code=404, text="Not Found", ok=False)

    with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
        mock_summary.return_value = "Meeting summary"
        mock_title.return_value = "Client Meeting"

        appointment_id = str(uuid4())
        dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
        client_target = CRMSyncTarget(crm_id="household123", type="account", name="Test Household Client")
        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(crm_id=appointment_id, type="phonecall", name="Existing phone call", parent=client_target)
            ],
        )
        with pytest.raises(Exception, match="Failed to fetch existing phonecall in Dynamics"):
            dynamics.add_interaction_with_client(test_note, sync_targets)

    mock_requests_patch.assert_not_called()
    mock_requests_post.assert_not_called()

    updated_note = Note.objects.get(pk=test_note.pk)
    assert not updated_note.metadata

    for task in test_tasks:
        task.refresh_from_db()
        assert not task.metadata


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.post")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.patch")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.get")
def test_add_interaction_with_client_update_existing_exception(
    mock_requests_get: MagicMock,
    mock_requests_patch: MagicMock,
    mock_requests_post: MagicMock,
    mock_oauth: MagicMock,
    test_note: Note,
    test_user: User,
    test_tasks: list[Task],
) -> None:
    ScheduledEvent.objects.create(
        start_time=datetime.datetime(2025, 6, 3, 10, 0, 0, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2025, 6, 3, 11, 0, 0, tzinfo=datetime.timezone.utc),
        note=test_note,
        user=test_user,
    )
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    # Mock GET response for fetching existing appointment description
    get_response = MagicMock(status_code=200, ok=True)
    get_response.json.return_value = {"description": "Existing appointment notes"}
    mock_requests_get.return_value = get_response

    mock_requests_patch.side_effect = Exception("API description update failed")

    with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
        mock_summary.return_value = "Meeting summary"
        mock_title.return_value = "Client Meeting"

        appointment_id = str(uuid4())
        dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
        client_target = CRMSyncTarget(crm_id="household123", type="account", name="Test Household Client")
        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(
                    crm_id=appointment_id, type="appointment", name="Existing appointment", parent=client_target
                )
            ],
        )
        with pytest.raises(Exception, match="API description update failed"):
            dynamics.add_interaction_with_client(test_note, sync_targets)

    mock_requests_post.assert_not_called()

    updated_note = Note.objects.get(pk=test_note.pk)
    assert not updated_note.metadata

    for task in test_tasks:
        task.refresh_from_db()
        assert not task.metadata


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.post")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.patch")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.get")
def test_add_interaction_with_client_update_existing_failure(
    mock_requests_get: MagicMock,
    mock_requests_patch: MagicMock,
    mock_requests_post: MagicMock,
    mock_oauth: MagicMock,
    test_note: Note,
    test_user: User,
    test_tasks: list[Task],
) -> None:
    ScheduledEvent.objects.create(
        start_time=datetime.datetime(2025, 6, 3, 10, 0, 0, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2025, 6, 3, 11, 0, 0, tzinfo=datetime.timezone.utc),
        note=test_note,
        user=test_user,
    )
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    # Mock GET response for fetching existing appointment description
    get_response = MagicMock(status_code=200, ok=True)
    get_response.json.return_value = {"description": "Existing appointment notes"}
    mock_requests_get.return_value = get_response

    mock_requests_patch.return_value = MagicMock(status_code=400, text="Bad Request", ok=False)

    with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
        mock_summary.return_value = "Meeting summary"
        mock_title.return_value = "Client Meeting"

        appointment_id = str(uuid4())
        dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
        client_target = CRMSyncTarget(crm_id="household123", type="account", name="Test Household Client")
        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(
                    crm_id=appointment_id, type="appointment", name="Existing appointment", parent=client_target
                )
            ],
        )
        with pytest.raises(Exception, match="Failed to update existing appointment in Dynamics"):
            dynamics.add_interaction_with_client(test_note, sync_targets)

    mock_requests_post.assert_not_called()

    updated_note = Note.objects.get(pk=test_note.pk)
    assert not updated_note.metadata

    for task in test_tasks:
        task.refresh_from_db()
        assert not task.metadata


@pytest.mark.parametrize(
    "get_response_data",
    [
        {"description": "Existing appointment notes"},
        {"description": "Existing appointment notes", "scheduledstart": None},
        {"description": "Existing appointment notes", "scheduledstart": ""},
        {"description": "Existing appointment notes", "scheduledstart": "invalid"},
    ],
)
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.post")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.patch")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.get")
def test_add_interaction_with_client_existing_appointment_invalid_time(
    mock_requests_get: MagicMock,
    mock_requests_patch: MagicMock,
    mock_requests_post: MagicMock,
    mock_oauth: MagicMock,
    test_note: Note,
    test_user: User,
    test_tasks: list[Task],
    get_response_data: dict[str, Any],
) -> None:
    ScheduledEvent.objects.create(
        start_time=datetime.datetime(2025, 6, 3, 10, 0, 0, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2025, 6, 3, 11, 0, 0, tzinfo=datetime.timezone.utc),
        note=test_note,
        user=test_user,
    )
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    get_response = MagicMock(status_code=200, ok=True)
    get_response.json.return_value = get_response_data
    mock_requests_get.return_value = get_response

    task_response = MagicMock(status_code=201, ok=True)
    task_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/tasks(task456)"}
    mock_requests_post.return_value = task_response

    mock_requests_patch.return_value = MagicMock(status_code=200, ok=True)

    with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
        mock_summary.return_value = "Meeting summary"
        mock_title.return_value = "Client Meeting"

        appointment_id = str(uuid4())
        dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
        client_target = CRMSyncTarget(crm_id="household123", type="account", name="Test Household Client")
        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(
                    crm_id=appointment_id, type="appointment", name="Existing appointment", parent=client_target
                )
            ],
        )
        dynamics.add_interaction_with_client(test_note, sync_targets)

    # Verify GET request to fetch existing appointment description
    mock_requests_get.assert_called_once_with(
        f"https://tamarac.example.com/api/data/v9.2/appointments({appointment_id})",
        headers={
            "Authorization": "Bearer mock_access_token",
            "Accept": "application/json",
            "Content-Type": "application/json",
            "OData-MaxVersion": "4.0",
            "OData-Version": "4.0",
        },
        params={"$select": "description,scheduledstart"},
    )

    # Verify PATCH calls for updating appointment description, but not last appointment update
    assert mock_requests_patch.call_count == 1
    mock_requests_patch.assert_has_calls(
        [
            call(
                f"https://tamarac.example.com/api/data/v9.2/appointments({appointment_id})",
                headers={
                    "Authorization": "Bearer mock_access_token",
                    "Accept": "application/json",
                    "Content-Type": "application/json",
                    "OData-MaxVersion": "4.0",
                    "OData-Version": "4.0",
                },
                json={"description": "Existing appointment notes\n\nMeeting summary"},
            ),
        ]
    )


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.post")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.patch")
def test_add_interaction_with_client_last_appointment_time_update_exception(
    mock_requests_patch: MagicMock,
    mock_requests_post: MagicMock,
    mock_oauth: MagicMock,
    test_note: Note,
    test_user: User,
    test_tasks: list[Task],
    caplog: pytest.LogCaptureFixture,
) -> None:
    ScheduledEvent.objects.create(
        start_time=datetime.datetime(2025, 6, 3, 10, 0, 0, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2025, 6, 3, 11, 0, 0, tzinfo=datetime.timezone.utc),
        note=test_note,
        user=test_user,
    )
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    appointment_response = MagicMock(status_code=201, ok=True)
    appointment_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/appointments(appt123)"}

    task_response = MagicMock(status_code=201, ok=True)
    task_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/tasks(task456)"}

    mock_requests_post.side_effect = [appointment_response, task_response]
    mock_requests_patch.side_effect = Exception("API last appointment update failed")

    with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
        mock_summary.return_value = "Meeting summary"
        mock_title.return_value = "Client Meeting"

        dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
        client_target = CRMSyncTarget(crm_id="household123", type="account", name="Test Household Client")
        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(
                    crm_id="new_appointment", type="appointment", name="New appointment", parent=client_target
                )
            ],
        )
        with caplog.at_level(logging.ERROR):
            dynamics.add_interaction_with_client(test_note, sync_targets)

    assert len(caplog.messages) == 1
    assert "Error updating last appointment time for client" in caplog.messages[0]

    mock_requests_patch.assert_called_once()

    assert mock_requests_post.call_count == 2

    updated_note = Note.objects.get(pk=test_note.pk)
    assert (updated_note.metadata or {}).get("interactionId") == "appt123"

    for task in test_tasks:
        updated_task = Task.objects.get(pk=task.pk)
        assert (updated_task.metadata or {})["consolidatedTaskId"] == "task456"


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.post")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.patch")
def test_add_interaction_with_client_last_appointment_time_update_failure(
    mock_requests_patch: MagicMock,
    mock_requests_post: MagicMock,
    mock_oauth: MagicMock,
    test_note: Note,
    test_user: User,
    test_tasks: list[Task],
    caplog: pytest.LogCaptureFixture,
) -> None:
    """Test adding appointment interaction with consolidated tasks."""

    ScheduledEvent.objects.create(
        start_time=datetime.datetime(2025, 6, 3, 10, 0, 0, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2025, 6, 3, 11, 0, 0, tzinfo=datetime.timezone.utc),
        note=test_note,
        user=test_user,
    )
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    appointment_response = MagicMock(status_code=201, ok=True)
    appointment_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/appointments(appt123)"}

    task_response = MagicMock(status_code=201, ok=True)
    task_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/tasks(task456)"}

    mock_requests_post.side_effect = [appointment_response, task_response]
    mock_requests_patch.return_value = MagicMock(status_code=400, text="Bad Request", ok=False)

    with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
        mock_summary.return_value = "Meeting summary"
        mock_title.return_value = "Client Meeting"

        dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
        client_target = CRMSyncTarget(crm_id="household123", type="account", name="Test Household Client")
        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(
                    crm_id="new_appointment", type="appointment", name="New appointment", parent=client_target
                )
            ],
        )
        with caplog.at_level(logging.ERROR):
            dynamics.add_interaction_with_client(test_note, sync_targets)

    assert len(caplog.messages) == 1
    assert "Error updating last appointment time for client" in caplog.messages[0]

    mock_requests_patch.assert_called_once()

    assert mock_requests_post.call_count == 2

    updated_note = Note.objects.get(pk=test_note.pk)
    assert (updated_note.metadata or {}).get("interactionId") == "appt123"

    for task in test_tasks:
        updated_task = Task.objects.get(pk=task.pk)
        assert (updated_task.metadata or {})["consolidatedTaskId"] == "task456"


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_with_client_already_exists(mock_oauth: MagicMock, test_note: Note) -> None:
    """Test that existing interactions are not duplicated."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    test_note.metadata = {"interactionId": "existing123"}
    test_note.save()

    with patch("requests.post") as mock_requests_post:
        dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
        sync_targets = dynamics.resolve_sync_targets(test_note, test_note.note_owner)  # type: ignore[arg-type]
        dynamics.add_interaction_with_client(test_note, sync_targets)

        mock_requests_post.assert_not_called()


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_create_consolidated_follow_up_task_with_assignees(
    mock_oauth: MagicMock, test_note: Note, test_user: User
) -> None:
    """Test consolidated task creation with task assignees."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    assignee_user = User.objects.create(
        email="<EMAIL>",
        username="assignee_user",
        organization=test_user.organization,
    )
    task1 = Task.objects.create(
        note=test_note,
        task_title="Task with assignee",
        task_desc="Description",
        assignee=assignee_user,
        due_date=django_timezone.now() + datetime.timedelta(days=5),
    )
    task2 = Task.objects.create(
        note=test_note, task_title="Task without assignee", due_date=django_timezone.now() + datetime.timedelta(days=10)
    )

    with patch("requests.post") as mock_requests_post:
        mock_response = MagicMock(status_code=201, ok=True)
        mock_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/tasks(consolidated123)"}
        mock_requests_post.return_value = mock_response

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        api_url = "https://tamarac.example.com/api/data/v9.2/"
        headers = get_headers("mock_token")
        client_info = {"id": "client123", "type": "account"}

        with patch.object(Note, "title") as mock_title:
            mock_title.return_value = "Test Meeting"

            dynamics._create_consolidated_follow_up_task(
                api_url, headers, test_note, test_note.task_set.all(), client_info
            )

        call_args = mock_requests_post.call_args[1]
        task_data = call_args["json"]

        assert task_data["subject"] == "Test Meeting - Follow Up"
        assert "Follow-up tasks from meeting: Test Meeting" in task_data["description"]
        assert "1. Task with assignee: Description (Assigned to: <EMAIL>)" in task_data["description"]
        assert "2. Task without assignee" in task_data["description"]
        assert task_data["statecode"] == 0
        assert task_data["prioritycode"] == 1

        updated_task1 = Task.objects.get(pk=task1.pk)
        updated_task2 = Task.objects.get(pk=task2.pk)
        assert updated_task1.metadata["consolidatedTaskId"] == "consolidated123"  # type: ignore[index]
        assert updated_task2.metadata["consolidatedTaskId"] == "consolidated123"  # type: ignore[index]


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_create_consolidated_follow_up_task_earliest_due_date(
    mock_oauth: MagicMock, test_note: Note, test_user: User
) -> None:
    """Test that consolidated task uses earliest due date."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    earliest_date = django_timezone.now() + datetime.timedelta(days=2)
    later_date = django_timezone.now() + datetime.timedelta(days=10)

    Task.objects.create(note=test_note, task_title="Later task", due_date=later_date)
    Task.objects.create(note=test_note, task_title="Earlier task", due_date=earliest_date)

    with patch("requests.post") as mock_requests_post:
        mock_response = MagicMock(status_code=201, ok=True)
        mock_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/tasks(consolidated123)"}
        mock_requests_post.return_value = mock_response

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        api_url = "https://tamarac.example.com/api/data/v9.2/"
        headers = get_headers("mock_token")
        client_info = {"id": "client123", "type": "account"}

        with patch.object(Note, "title") as mock_title:
            mock_title.return_value = "Test Meeting"

            dynamics._create_consolidated_follow_up_task(
                api_url, headers, test_note, test_note.task_set.all(), client_info
            )

        call_args = mock_requests_post.call_args[1]
        task_data = call_args["json"]
        scheduled_end = datetime.datetime.fromisoformat(task_data["scheduledend"].replace("Z", "+00:00"))

        assert abs((scheduled_end - earliest_date).total_seconds()) < 60


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_create_consolidated_follow_up_task_no_due_dates(
    mock_oauth: MagicMock, test_note: Note, test_user: User
) -> None:
    """Test consolidated task creation when no tasks have due dates (should default to 7 days)."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    Task.objects.create(note=test_note, task_title="Task without due date")

    with patch("requests.post") as mock_requests_post:
        mock_response = MagicMock(status_code=201, ok=True)
        mock_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/tasks(consolidated123)"}
        mock_requests_post.return_value = mock_response

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        api_url = "https://tamarac.example.com/api/data/v9.2/"
        headers = get_headers("mock_token")
        client_info = {"id": "client123", "type": "account"}

        with patch.object(Note, "title") as mock_title:
            mock_title.return_value = "Test Meeting"

            with patch(
                "deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.datetime"
            ) as mock_datetime_module:
                fixed_now = datetime.datetime(2023, 1, 1, 12, 0, 0, tzinfo=datetime.timezone.utc)
                mock_datetime_module.datetime.now.return_value = fixed_now
                mock_datetime_module.timedelta = datetime.timedelta  # Keep timedelta working

                dynamics._create_consolidated_follow_up_task(
                    api_url, headers, test_note, test_note.task_set.all(), client_info
                )

        call_args = mock_requests_post.call_args[1]
        task_data = call_args["json"]
        expected_due = fixed_now + datetime.timedelta(days=7)
        assert task_data["scheduledend"] == expected_due.isoformat()


def test_add_interaction_no_note_owner(test_user: User) -> None:
    with pytest.raises(Exception, match="Note owner not found"):
        dynamics = TamaracMicrosoftDynamics(user=test_user)
        dynamics.add_interaction_with_client(
            Note(), CRMSyncTargets(status=CRMSyncTargets.Status.FINAL, note_targets=[])
        )


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_no_client(mock_oauth: MagicMock, test_user: User) -> None:
    """Test error handling when note has no client."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    note = Note.objects.create(note_owner=test_user, summary="No client note")

    dynamics = TamaracMicrosoftDynamics(user=test_user)

    with pytest.raises(Exception, match="Appointment selection for sync is not finalized."):
        sync_targets = CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])
        dynamics.add_interaction_with_client(note, sync_targets)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_no_sync_targets(mock_oauth: MagicMock, test_note: Note) -> None:
    """Test error handling when no sync targets are provided."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
    with pytest.raises(Exception, match=re.escape("No appointments (new or preexisting) provided for sync")):
        dynamics.add_interaction_with_client(
            test_note, CRMSyncTargets(status=CRMSyncTargets.Status.FINAL, note_targets=[])
        )


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_with_client_multiple_clients(mock_oauth: MagicMock, test_note: Note) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
    with pytest.raises(Exception, match="Multiple appointments provided for sync, expected only one"):
        dynamics.add_interaction_with_client(
            test_note,
            CRMSyncTargets(
                status=CRMSyncTargets.Status.FINAL,
                note_targets=[
                    CRMSyncTarget(crm_id="client1", type="client", name="Client One"),
                    CRMSyncTarget(crm_id="client2", type="client", name="Client Two"),
                ],
            ),
        )


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_with_client_no_parent_client(mock_oauth: MagicMock, test_note: Note) -> None:
    """Test error handling when sync target has no parent client."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
    with pytest.raises(Exception, match="Appointment does not have an associated client"):
        dynamics.add_interaction_with_client(
            test_note,
            CRMSyncTargets(
                status=CRMSyncTargets.Status.FINAL,
                note_targets=[CRMSyncTarget(crm_id="new_appointment", type="appointment", name="New appointment")],
            ),
        )


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_oauth_error_handling(mock_oauth: MagicMock, test_user: User, caplog: pytest.LogCaptureFixture) -> None:
    """Test OAuth error handling."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.side_effect = Exception("OAuth failed")

    dynamics = TamaracMicrosoftDynamics(user=test_user)

    accounts = dynamics.get_accounts_by_owner_email_and_name("<EMAIL>")

    assert accounts == []
    assert len(caplog.messages) == 1
    assert "Error getting Tamarac household accounts" in caplog.messages[0]


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_accounts_api_error(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.get") as mock_requests_get, patch("logging.error") as mock_error:
        mock_requests_get.return_value = MagicMock(status_code=401, text="Unauthorized", ok=False)

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        accounts = dynamics.get_accounts_by_owner_email_and_name("<EMAIL>")

        assert accounts == []
        mock_error.assert_called()
        error_messages = [str(call) for call in mock_error.call_args_list]
        assert any("Error fetching account types" in msg for msg in error_messages)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_accounts_malformed_response(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    account_types_response = {
        "value": [
            {
                "tam_accounttypeid": "household-type-123",
                "tam_name": "Household",
            },
        ]
    }

    accounts_response = {
        "value": [
            {
                "name": "Good Household",
                "accountid": "good123",
                "telephone1": "**********",
                "emailaddress1": "<EMAIL>",
                "_tam_accounttypeid_value": "household-type-123",
                "statecode": 0,
            },
            {
                # Missing required fields to cause error
                "name": None,
                "accountid": None,
            },
        ]
    }

    with patch("requests.get") as mock_requests_get, patch("logging.error") as mock_error:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: account_types_response, ok=True),
            MagicMock(status_code=200, json=lambda: accounts_response, ok=True),
        ]

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        accounts = dynamics.get_accounts_by_owner_email_and_name("<EMAIL>")

        # Should get one good account despite the malformed one
        assert len(accounts) == 1
        assert accounts[0].name == "Good Household"

        # Should log error for malformed account
        mock_error.assert_called()
        assert any("Error creating CRMAccount object" in str(call) for call in mock_error.call_args_list)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.post")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.requests.patch")
def test_add_interaction_successful_activity_no_id_header(
    mock_requests_patch: MagicMock,
    mock_requests_post: MagicMock,
    mock_oauth: MagicMock,
    test_note: Note,
    caplog: pytest.LogCaptureFixture,
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    response = MagicMock(status_code=201, ok=True)
    response.headers = {}  # No OData-EntityId header
    mock_requests_post.return_value = response

    mock_requests_patch.return_value = MagicMock(status_code=200, ok=True)

    with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
        mock_summary.return_value = "Test summary"
        mock_title.return_value = "Test Meeting"

        dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
        client_target = CRMSyncTarget(crm_id="household123", type="account", name="Test Household Client")
        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(
                    crm_id="new_appointment", type="appointment", name="New appointment", parent=client_target
                )
            ],
        )
        with pytest.raises(Exception, match="Failed to create new appointment: could not get created activity ID"):
            dynamics.add_interaction_with_client(test_note, sync_targets)

    assert len(caplog.messages) == 1
    assert "Failed to create new appointment: could not get created activity ID" in caplog.messages[0]

    mock_requests_post.assert_called_once()

    updated_note = Note.objects.get(pk=test_note.pk)
    assert not updated_note.metadata


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_consolidated_task_creation_successful_no_id_header(
    mock_oauth: MagicMock, test_note: Note, test_user: User
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    task = Task.objects.create(note=test_note, task_title="Test task")

    with patch("requests.post") as mock_requests_post, patch("logging.warning") as mock_warning:
        response = MagicMock(status_code=201, ok=True)
        response.headers = {}  # No OData-EntityId header
        mock_requests_post.return_value = response

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        api_url = "https://tamarac.example.com/api/data/v9.2/"
        headers = get_headers("mock_token")
        client_info = {"id": "client123", "type": "account"}

        with patch.object(Note, "title") as mock_title:
            mock_title.return_value = "Test Meeting"

            dynamics._create_consolidated_follow_up_task(
                api_url, headers, test_note, test_note.task_set.all(), client_info
            )

        mock_warning.assert_called()
        warning_messages = [str(call) for call in mock_warning.call_args_list]
        assert any("created but couldn't extract ID from response headers" in msg for msg in warning_messages)

        updated_task = Task.objects.get(pk=task.pk)
        assert not updated_task.metadata or "consolidatedTaskId" not in updated_task.metadata


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_complex_task_descriptions_formatting(mock_oauth: MagicMock, test_note: Note, test_user: User) -> None:
    """Test that complex task descriptions are properly formatted in consolidated task."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    assignee = User.objects.create(
        email="<EMAIL>",
        username="advisor_user",  # Add unique username
        organization=test_user.organization,
    )

    tasks = [
        Task.objects.create(
            note=test_note,
            task_title="Review investment strategy",
            task_desc="Analyze current portfolio allocation and recommend rebalancing",
            assignee=assignee,
            due_date=django_timezone.now() + datetime.timedelta(days=5),
        ),
        Task.objects.create(
            note=test_note,
            task_title="Schedule client call",
            assignee=assignee,
            # No description or due date
        ),
        Task.objects.create(
            note=test_note,
            task_title="Send market update",
            task_desc="Quarterly market commentary and outlook",
            due_date=django_timezone.now() + datetime.timedelta(days=2),
            # No assignee
        ),
        Task.objects.create(
            note=test_note,
            task_title="Update client database",
            # Only title
        ),
    ]

    with patch("requests.post") as mock_requests_post:
        mock_response = MagicMock(status_code=201, ok=True)
        mock_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/tasks(consolidated123)"}
        mock_requests_post.return_value = mock_response

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        api_url = "https://tamarac.example.com/api/data/v9.2/"
        headers = get_headers("mock_token")
        client_info = {"id": "client123", "type": "account"}

        with patch.object(Note, "title") as mock_title:
            mock_title.return_value = "Quarterly Review Meeting"

            dynamics._create_consolidated_follow_up_task(
                api_url, headers, test_note, test_note.task_set.all(), client_info
            )

        call_args = mock_requests_post.call_args[1]
        task_data = call_args["json"]
        description = task_data["description"]

        assert "Follow-up tasks from meeting: Quarterly Review Meeting" in description

        assert (
            "1. Review investment strategy: Analyze current portfolio allocation and recommend rebalancing (Assigned to: <EMAIL>)"
            in description
        )
        assert "2. Schedule client call (Assigned to: <EMAIL>)" in description
        assert "3. Send market update: Quarterly market commentary and outlook" in description
        assert "4. Update client database" in description

        assert "(Due:" in description  # Should appear for tasks with due dates


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_success(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    appointments_response = {
        "value": [
            {
                "activityid": "appt123",
                "subject": "Client Meeting",
                "description": "Discussed portfolio strategy",
                "createdon": "2023-12-01T10:00:00Z",
                "scheduledstart": "2023-12-01T09:00:00Z",
                "scheduledend": "2023-12-01T10:00:00Z",
            }
        ]
    }

    phonecalls_response = {
        "value": [
            {
                "activityid": "call456",
                "subject": "Follow-up Call",
                "description": "Discussed action items",
                "createdon": "2023-12-02T14:00:00Z",
                "scheduledstart": "2023-12-02T14:00:00Z",
                "scheduledend": "2023-12-02T14:30:00Z",
            }
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: appointments_response, ok=True),
            MagicMock(status_code=200, json=lambda: phonecalls_response, ok=True),
        ]

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, timedelta(days=30))

        assert len(notes) == 2
        assert notes[0].crm_id == "call456"
        assert notes[0].content == "[Phone Call] Follow-up Call\nDiscussed action items"
        assert notes[0].crm_system == "microsoft_dynamics"
        assert (
            str(notes[0].web_link)
            == "https://tamarac.example.com/main.aspx?pagetype=entityrecord&etn=phonecall&id=call456"
        )

        assert notes[1].crm_id == "appt123"
        assert notes[1].content == "[Appointment] Client Meeting\nDiscussed portfolio strategy"
        assert (
            str(notes[1].web_link)
            == "https://tamarac.example.com/main.aspx?pagetype=entityrecord&etn=appointment&id=appt123"
        )

        assert mock_requests_get.call_count == 2

        appt_call = mock_requests_get.call_args_list[0]
        assert "appointments" in appt_call[0][0]
        appt_params = appt_call[1]["params"]
        assert f"_regardingobjectid_value eq '{test_client.crm_id}'" in appt_params["$filter"]
        assert "createdon ge" in appt_params["$filter"]

        phone_call = mock_requests_get.call_args_list[1]
        assert "phonecalls" in phone_call[0][0]


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_no_crm_id(
    mock_oauth: MagicMock, test_user: User, test_organization: Organization
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    client_no_crm = Client.objects.create(name="Client Without CRM ID", organization=test_organization)

    dynamics = TamaracMicrosoftDynamics(user=test_user)

    with patch("logging.error") as mock_error:
        notes = dynamics.fetch_notes_for_client(test_user, client_no_crm, timedelta(days=30))

        assert notes == []
        mock_error.assert_called()
        error_messages = [str(call) for call in mock_error.call_args_list]
        assert any("does not have a CRM ID" in msg for msg in error_messages)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_empty_results(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    empty_response: dict[str, Any] = {"value": []}

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: empty_response, ok=True)

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, timedelta(days=30))

        assert notes == []
        assert mock_requests_get.call_count == 2


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_date_parsing_error(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    appointments_response = {
        "value": [
            {
                "activityid": "appt123",
                "subject": "Meeting",
                "description": "Description",
                "createdon": "invalid-date-format",
            }
        ]
    }

    with patch("requests.get") as mock_requests_get, patch("logging.error") as mock_error:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: appointments_response, ok=True),
            MagicMock(status_code=200, json=lambda: {"value": []}, ok=True),
        ]

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, timedelta(days=30))

        assert len(notes) == 1
        assert notes[0].created_at is None
        mock_error.assert_called()
        error_messages = [str(call) for call in mock_error.call_args_list]
        assert any("Error parsing date" in msg for msg in error_messages)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_malformed_activity_data(
    mock_oauth: MagicMock, test_user: User, test_client: Client
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    appointments_response = {
        "value": [
            {
                "activityid": "good123",
                "subject": "Good Meeting",
                "description": "Valid description",
                "createdon": "2023-12-01T10:00:00Z",
            },
            {
                # Missing activityid to cause error
                "subject": "Bad Meeting",
                "description": "Missing ID",
            },
        ]
    }

    with patch("requests.get") as mock_requests_get, patch("logging.error") as mock_error:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: appointments_response, ok=True),
            MagicMock(status_code=200, json=lambda: {"value": []}, ok=True),
        ]

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, timedelta(days=30))

        assert len(notes) == 1
        assert notes[0].crm_id == "good123"
        mock_error.assert_called()
        error_messages = [str(call) for call in mock_error.call_args_list]
        assert any("Error creating CRMNote object" in msg for msg in error_messages)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_subject_only(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    appointments_response = {
        "value": [
            {
                "activityid": "appt123",
                "subject": "Meeting Subject Only",
                "description": "",
                "createdon": "2023-12-01T10:00:00Z",
            }
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: appointments_response, ok=True),
            MagicMock(status_code=200, json=lambda: {"value": []}, ok=True),
        ]

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, timedelta(days=30))

        assert len(notes) == 1
        assert notes[0].content == "[Appointment] Meeting Subject Only\n"


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_description_only(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    phonecalls_response = {
        "value": [
            {
                "activityid": "call123",
                "subject": "",
                "description": "Just description content",
                "createdon": "2023-12-01T10:00:00Z",
            }
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: {"value": []}, ok=True),
            MagicMock(status_code=200, json=lambda: phonecalls_response, ok=True),
        ]

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, timedelta(days=30))

        assert len(notes) == 1
        assert notes[0].content == "[Phone Call] Just description content"


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_oauth_error(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.side_effect = Exception("OAuth failed")

    dynamics = TamaracMicrosoftDynamics(user=test_user)

    with patch("logging.error") as mock_error:
        notes = dynamics.fetch_notes_for_client(test_user, test_client, timedelta(days=30))

        assert notes == []
        mock_error.assert_called()
        error_messages = [str(call) for call in mock_error.call_args_list]
        assert any("Error fetching notes from Tamarac Dynamics" in msg for msg in error_messages)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_activities_for_client_direct_call(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    activities_response = {
        "value": [
            {
                "activityid": "test123",
                "subject": "Test Activity",
                "description": "Test Description",
                "createdon": "2023-12-01T10:00:00Z",
            }
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: activities_response, ok=True)

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        api_url = "https://tamarac.example.com/api/data/v9.2/"
        headers = get_headers("mock_access_token")
        formatted_date = "2023-11-01T00:00:00Z"

        notes = dynamics._fetch_activities_for_client(
            api_url=api_url,
            headers=headers,
            client_crm_id=test_client.crm_id,  # type: ignore[arg-type]
            formatted_date=formatted_date,
            user=test_user,
            endpoint="appointments",
            label="Appointment",
            entity="appointment",
        )

        assert len(notes) == 1
        assert notes[0].crm_id == "test123"
        assert notes[0].content == "[Appointment] Test Activity\nTest Description"
        assert (
            str(notes[0].web_link)
            == "https://tamarac.example.com/main.aspx?pagetype=entityrecord&etn=appointment&id=test123"
        )

        call_args = mock_requests_get.call_args
        assert "appointments" in call_args[0][0]
        params = call_args[1]["params"]
        assert f"_regardingobjectid_value eq '{test_client.crm_id}'" in params["$filter"]
        assert "createdon ge 2023-11-01T00:00:00Z" in params["$filter"]


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_sorting_order(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    appointments_response = {
        "value": [
            {
                "activityid": "older_appt",
                "subject": "Older Appointment",
                "description": "",
                "createdon": "2023-11-01T10:00:00Z",
            }
        ]
    }

    phonecalls_response = {
        "value": [
            {
                "activityid": "newer_call",
                "subject": "Newer Phone Call",
                "description": "",
                "createdon": "2023-12-01T10:00:00Z",
            }
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: appointments_response, ok=True),
            MagicMock(status_code=200, json=lambda: phonecalls_response, ok=True),
        ]

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, timedelta(days=30))

        assert len(notes) == 2
        assert notes[0].crm_id == "newer_call"  # Newer one should be first
        assert notes[1].crm_id == "older_appt"


def test_unimplemented_methods_updated(test_user: User, test_client: Client) -> None:
    with (
        patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth") as mock_oauth,
        patch("logging.warning") as mock_warning,
    ):
        mock_oauth_instance = mock_oauth.return_value
        mock_oauth_instance.get_access_token.return_value = "mock_access_token"

        dynamics = TamaracMicrosoftDynamics(user=test_user)

        client_info = dynamics.get_client_basic_info(test_client, test_user, include_household=True)
        assert client_info == {}

        events = dynamics.fetch_events(test_user, datetime.timedelta(days=7))
        assert events == []

        assert mock_warning.call_count == 2
        warning_messages = [call[0][0] for call in mock_warning.call_args_list]
        assert any("fetch_events not implemented" in msg for msg in warning_messages)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_resolve_sync_targets_no_client_in_note(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    note_no_client = Note.objects.create(note_owner=test_user, summary="Note without client")

    dynamics = TamaracMicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(note_no_client, test_user)

    expected = CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])
    assert sync_targets == expected


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_resolve_sync_targets_no_client_uuid(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    note_no_uuid = Note.objects.create(note_owner=test_user, client={}, summary="Note with empty client")

    dynamics = TamaracMicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(note_no_uuid, test_user)

    expected = CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])
    assert sync_targets == expected


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_resolve_sync_targets_client_does_not_exist(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    nonexistent_uuid = "00000000-0000-0000-0000-000000000000"
    note_bad_uuid = Note.objects.create(
        note_owner=test_user, client={"uuid": nonexistent_uuid}, summary="Note with nonexistent client UUID"
    )

    dynamics = TamaracMicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(note_bad_uuid, test_user)

    expected = CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])
    assert sync_targets == expected


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_resolve_sync_targets_client_no_crm_id(
    mock_oauth: MagicMock, test_user: User, test_organization: Organization
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    client_no_crm = Client.objects.create(name="Client Without CRM ID", organization=test_organization, crm_id=None)
    client_no_crm.authorized_users.add(test_user)

    note_no_crm = Note.objects.create(
        note_owner=test_user, client={"uuid": str(client_no_crm.uuid)}, summary="Note with client without CRM ID"
    )

    dynamics = TamaracMicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(note_no_crm, test_user)

    expected = CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])
    assert sync_targets == expected


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_resolve_sync_targets_client_empty_crm_id(
    mock_oauth: MagicMock, test_user: User, test_organization: Organization
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    client_empty_crm = Client.objects.create(name="Client With Empty CRM ID", organization=test_organization, crm_id="")
    client_empty_crm.authorized_users.add(test_user)

    note_empty_crm = Note.objects.create(
        note_owner=test_user, client={"uuid": str(client_empty_crm.uuid)}, summary="Note with client with empty CRM ID"
    )

    dynamics = TamaracMicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(note_empty_crm, test_user)

    expected = CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])
    assert sync_targets == expected


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("requests.get")
def test_resolve_sync_targets_client_name_fallback(
    mock_requests_get: MagicMock, mock_oauth: MagicMock, test_user: User, test_organization: Organization
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    client_no_name = Client.objects.create(name="", organization=test_organization, crm_id="client123")
    client_no_name.authorized_users.add(test_user)

    note_no_name = Note.objects.create(
        note_owner=test_user, client={"uuid": str(client_no_name.uuid)}, summary="Note with nameless client"
    )

    mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: {"value": []}, ok=True)

    dynamics = TamaracMicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(note_no_name, test_user)

    client_target = CRMSyncTarget(crm_id="client123", type="account", name="Unknown Client")
    expected = CRMSyncTargets(
        status=CRMSyncTargets.Status.RESOLUTION_REQUIRED,
        note_targets=[
            CRMSyncTarget(crm_id="new_appointment", type="appointment", name="New appointment", parent=client_target),
            CRMSyncTarget(crm_id="new_phone_call", type="phonecall", name="New phone call", parent=client_target),
        ],
    )
    assert sync_targets == expected


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("requests.get")
def test_resolve_sync_targets_ignores_invalid_selected_sync_targets(
    mock_requests_get: MagicMock, mock_oauth: MagicMock, test_user: User, test_client: Client, test_note: Note
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    different_sync_targets = CRMSyncTargets(
        status=CRMSyncTargets.Status.FINAL,
        note_targets=[CRMSyncTarget(crm_id="different123", type="contact", name="Different Client")],
    )

    mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: {"value": []}, ok=True)

    dynamics = TamaracMicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(test_note, test_user, different_sync_targets)

    client_target = CRMSyncTarget(crm_id=test_client.crm_id, type="account", name=test_client.name)
    expected = CRMSyncTargets(
        status=CRMSyncTargets.Status.RESOLUTION_REQUIRED,
        note_targets=[
            CRMSyncTarget(crm_id="new_appointment", type="appointment", name="New appointment", parent=client_target),
            CRMSyncTarget(crm_id="new_phone_call", type="phonecall", name="New phone call", parent=client_target),
        ],
    )
    assert sync_targets == expected


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("requests.get")
def test_resolve_sync_targets_with_existing_appointments(
    mock_requests_get: MagicMock, mock_oauth: MagicMock, test_user: User, test_client: Client, test_note: Note
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    appointments_response = {
        "value": [
            {
                "activityid": "appt123",
                "subject": "Existing Meeting",
                "scheduledstart": "2023-12-01T10:00:00Z",
            },
            {
                "activityid": "appt456",
                "subject": "Another Meeting",
                "scheduledstart": "2023-12-02T14:00:00Z",
            },
        ]
    }

    mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: appointments_response, ok=True)

    dynamics = TamaracMicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(test_note, test_user)

    client_target = CRMSyncTarget(crm_id=test_client.crm_id, type="account", name=test_client.name)
    expected = CRMSyncTargets(
        status=CRMSyncTargets.Status.RESOLUTION_REQUIRED,
        note_targets=[
            CRMSyncTarget(
                crm_id="appt123", type="appointment", name="Existing Meeting (2023-12-01)", parent=client_target
            ),
            CRMSyncTarget(
                crm_id="appt456", type="appointment", name="Another Meeting (2023-12-02)", parent=client_target
            ),
            CRMSyncTarget(crm_id="new_appointment", type="appointment", name="New appointment", parent=client_target),
            CRMSyncTarget(crm_id="new_phone_call", type="phonecall", name="New phone call", parent=client_target),
        ],
    )
    assert sync_targets == expected
    related_filter = "(regardingobjectid_account/accountid eq 'household123' or appointment_activity_parties/any(o:o/partyid_account/accountid eq 'household123'))"
    state_filter = "(statecode eq 0 or statecode eq 3)"
    expected_filter = f"{related_filter} and {state_filter}"
    mock_requests_get.assert_called_once_with(
        "https://tamarac.example.com/api/data/v9.2/appointments",
        headers={
            "Authorization": "Bearer mock_access_token",
            "Accept": "application/json",
            "Content-Type": "application/json",
            "OData-MaxVersion": "4.0",
            "OData-Version": "4.0",
        },
        params={
            "$filter": expected_filter,
            "$select": "activityid,subject,scheduledstart,scheduledend",
            "$orderby": "scheduledstart desc",
        },
    )


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_resolve_sync_targets_with_selected_appointment(
    mock_oauth: MagicMock, test_user: User, test_client: Client, test_note: Note
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    client_target = CRMSyncTarget(crm_id=test_client.crm_id, type="account", name=test_client.name)
    selected_sync_targets = CRMSyncTargets(
        status=CRMSyncTargets.Status.FINAL,
        note_targets=[
            CRMSyncTarget(crm_id="appt123", type="appointment", name="Selected Appointment", parent=client_target)
        ],
    )

    dynamics = TamaracMicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(test_note, test_user, selected_sync_targets)

    expected = CRMSyncTargets(
        status=CRMSyncTargets.Status.FINAL,
        note_targets=[
            CRMSyncTarget(crm_id="appt123", type="appointment", name="Selected Appointment", parent=client_target)
        ],
    )
    assert sync_targets == expected


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_resolve_sync_targets_with_selected_phone_call(
    mock_oauth: MagicMock, test_user: User, test_client: Client, test_note: Note
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    client_target = CRMSyncTarget(crm_id=test_client.crm_id, type="account", name=test_client.name)
    selected_sync_targets = CRMSyncTargets(
        status=CRMSyncTargets.Status.FINAL,
        note_targets=[
            CRMSyncTarget(crm_id="call123", type="phonecall", name="Selected Phone Call", parent=client_target)
        ],
    )

    dynamics = TamaracMicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(test_note, test_user, selected_sync_targets)

    expected = CRMSyncTargets(
        status=CRMSyncTargets.Status.FINAL,
        note_targets=[
            CRMSyncTarget(crm_id="call123", type="phonecall", name="Selected Phone Call", parent=client_target)
        ],
    )
    assert sync_targets == expected


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("requests.get")
def test_resolve_sync_targets_with_multiple_selected_targets(
    mock_requests_get: MagicMock, mock_oauth: MagicMock, test_user: User, test_client: Client, test_note: Note
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    client_target = CRMSyncTarget(crm_id=test_client.crm_id, type="account", name=test_client.name)
    selected_sync_targets = CRMSyncTargets(
        status=CRMSyncTargets.Status.FINAL,
        note_targets=[
            CRMSyncTarget(crm_id="appt123", type="appointment", name="First", parent=client_target),
            CRMSyncTarget(crm_id="appt456", type="appointment", name="Second", parent=client_target),
        ],
    )

    mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: {"value": []}, ok=True)

    dynamics = TamaracMicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(test_note, test_user, selected_sync_targets)

    expected = CRMSyncTargets(
        status=CRMSyncTargets.Status.RESOLUTION_REQUIRED,
        note_targets=[
            CRMSyncTarget(crm_id="new_appointment", type="appointment", name="New appointment", parent=client_target),
            CRMSyncTarget(crm_id="new_phone_call", type="phonecall", name="New phone call", parent=client_target),
        ],
    )
    assert sync_targets == expected


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("requests.get")
def test_resolve_sync_targets_appointment_fetch_error(
    mock_requests_get: MagicMock,
    mock_oauth: MagicMock,
    test_user: User,
    test_client: Client,
    test_note: Note,
    caplog: pytest.LogCaptureFixture,
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    mock_requests_get.return_value = MagicMock(status_code=500, text="Server Error", ok=False)

    dynamics = TamaracMicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(test_note, test_user)

    client_target = CRMSyncTarget(crm_id=test_client.crm_id, type="account", name=test_client.name)
    expected = CRMSyncTargets(
        status=CRMSyncTargets.Status.RESOLUTION_REQUIRED,
        note_targets=[
            CRMSyncTarget(crm_id="new_appointment", type="appointment", name="New appointment", parent=client_target),
            CRMSyncTarget(crm_id="new_phone_call", type="phonecall", name="New phone call", parent=client_target),
        ],
    )
    assert sync_targets == expected
    assert len(caplog.messages) == 2
    assert "Error fetching open appointments" in caplog.messages[0]


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("requests.get")
def test_resolve_sync_targets_appointment_no_subject(
    mock_requests_get: MagicMock, mock_oauth: MagicMock, test_user: User, test_client: Client, test_note: Note
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    appointments_response = {
        "value": [
            {
                "activityid": "appt123",
                "scheduledstart": "2023-12-01T10:00:00Z",
            }
        ]
    }

    mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: appointments_response, ok=True)

    dynamics = TamaracMicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(test_note, test_user)

    client_target = CRMSyncTarget(crm_id=test_client.crm_id, type="account", name=test_client.name)
    expected = CRMSyncTargets(
        status=CRMSyncTargets.Status.RESOLUTION_REQUIRED,
        note_targets=[
            CRMSyncTarget(crm_id="appt123", type="appointment", name="<No Subject> (2023-12-01)", parent=client_target),
            CRMSyncTarget(crm_id="new_appointment", type="appointment", name="New appointment", parent=client_target),
            CRMSyncTarget(crm_id="new_phone_call", type="phonecall", name="New phone call", parent=client_target),
        ],
    )
    assert sync_targets == expected


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("requests.get")
def test_resolve_sync_targets_appointment_invalid_date(
    mock_requests_get: MagicMock,
    mock_oauth: MagicMock,
    test_user: User,
    test_client: Client,
    test_note: Note,
    caplog: pytest.LogCaptureFixture,
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    appointments_response = {
        "value": [
            {
                "activityid": "appt123",
                "subject": "Meeting with Bad Date",
                "scheduledstart": "invalid-date",
            }
        ]
    }

    mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: appointments_response, ok=True)

    with caplog.at_level(logging.ERROR):
        dynamics = TamaracMicrosoftDynamics(user=test_user)
        sync_targets = dynamics.resolve_sync_targets(test_note, test_user)

    client_target = CRMSyncTarget(crm_id=test_client.crm_id, type="account", name=test_client.name)
    expected = CRMSyncTargets(
        status=CRMSyncTargets.Status.RESOLUTION_REQUIRED,
        note_targets=[
            CRMSyncTarget(crm_id="appt123", type="appointment", name="Meeting with Bad Date", parent=client_target),
            CRMSyncTarget(crm_id="new_appointment", type="appointment", name="New appointment", parent=client_target),
            CRMSyncTarget(crm_id="new_phone_call", type="phonecall", name="New phone call", parent=client_target),
        ],
    )
    assert sync_targets == expected
    assert len(caplog.messages) == 1
    assert "Error parsing scheduled start time" in caplog.messages[0]


@patch("requests.get")
def test_list_users_no_organization(mock_get: MagicMock, test_user_without_org: User) -> None:
    dynamics = TamaracMicrosoftDynamics(user=test_user_without_org)
    result = dynamics.list_users(test_user_without_org)
    assert result == []
    mock_get.assert_not_called()


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("requests.get")
def test_list_users_no_access_token(mock_get: MagicMock, mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = None

    dynamics = TamaracMicrosoftDynamics(user=test_user)

    with pytest.raises(Exception, match=f"Could not get access token for user {test_user.uuid}"):
        dynamics.list_users(test_user)

    mock_get.assert_not_called()


@patch("requests.get")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_list_users_empty_wb_users(mock_oauth: MagicMock, mock_get: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    dynamics = TamaracMicrosoftDynamics(user=test_user)
    mock_users_response = MagicMock()
    mock_users_response.status_code = 200
    mock_users_response.json.return_value = {"users": [], "meta": {"total_pages": 1}}

    mock_get.return_value = mock_users_response
    result = dynamics.list_users(test_user)
    assert result == []
    mock_get.assert_has_calls(
        [
            call(
                "https://tamarac.example.com/api/data/v9.2/systemusers",
                headers=ANY,
                params={"$select": "fullname,systemuserid,internalemailaddress", "$filter": "statecode eq 0"},
            )
        ]
    )


@patch("requests.get")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_list_users_success(
    mock_oauth: MagicMock, mock_requests_get: MagicMock, test_user: User, test_organization: Organization
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"
    dynamics = TamaracMicrosoftDynamics(user=test_user)

    users_response = {
        "value": [
            {
                "systemuserid": "mdid1",
                "fullname": "CRM User 1",
                "internalemailaddress": "<EMAIL>",
            },
            {
                "systemuserid": "mdid2",
                "fullname": "CRM User 2",
                "internalemailaddress": "<EMAIL>",
            },
        ]
    }
    mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: users_response)

    # Create a matching zeplyn user
    matched_user = User.objects.create(
        email="<EMAIL>",
        organization=test_organization,
        name="Matched User",
        username="matched_user",
    )
    result = dynamics.list_users(test_user)

    def expected() -> list[CRMUser]:
        return [
            CRMUser(
                name="CRM User 1",
                crm_id="mdid1",
                organization=ZeplynOrganization(uuid=str(test_organization.uuid), name=test_organization.name),
                zeplyn_user=ZeplynUser(uuid=str(matched_user.uuid), email=matched_user.email, name=matched_user.name),
            ),
            CRMUser(
                name="CRM User 2",
                crm_id="mdid2",
                organization=ZeplynOrganization(uuid=str(test_organization.uuid), name=test_organization.name),
                zeplyn_user=None,
            ),
        ]

    assert len(result) == 2
    assert result == expected()
    mock_requests_get.assert_has_calls(
        [
            call(
                "https://tamarac.example.com/api/data/v9.2/systemusers",
                headers=ANY,
                params={"$select": "fullname,systemuserid,internalemailaddress", "$filter": "statecode eq 0"},
            )
        ]
    )


@patch("requests.get")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_list_users_exception_handling(
    mock_oauth: MagicMock, mock_requests_get: MagicMock, test_user: User, test_organization: Organization
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"
    dynamics = TamaracMicrosoftDynamics(user=test_user)

    users_response: dict[str, list[dict[str, Any]]] = {"value": []}
    mock_requests_get.return_value = MagicMock(
        status_code=200, json=lambda: users_response, side_effect=Exception("API error")
    )

    result = dynamics.list_users(test_user)
    assert result == []
    mock_requests_get.assert_has_calls(
        [
            call(
                "https://tamarac.example.com/api/data/v9.2/systemusers",
                headers=ANY,
                params={"$select": "fullname,systemuserid,internalemailaddress", "$filter": "statecode eq 0"},
            )
        ]
    )


@patch("requests.get")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_list_users_handles_validation_error_in_crm_user(
    mock_oauth: MagicMock,
    mock_requests_get: MagicMock,
    test_user: User,
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"
    dynamics = TamaracMicrosoftDynamics(user=test_user)

    users_response = {
        "value": [
            {
                # systemuserid is required but set to None to cause ValidationError
                "systemuserid": None,
                "fullname": "CRM User 1",
                "internalemailaddress": "<EMAIL>",
            },
        ]
    }
    mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: users_response)

    result = dynamics.list_users(test_user)

    # Expected is empty list because CRMUser creation failed
    assert result == []


@patch("requests.get")
@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_list_users_handles_partial_validation_errors(
    mock_oauth: MagicMock, mock_requests_get: MagicMock, test_user: User, test_organization: Organization
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"
    dynamics = TamaracMicrosoftDynamics(user=test_user)
    # case when one of the users returned from the CRM API is invalid, but others are valid
    # it will return only the valid users and log the error for the invalid one
    users_response = {
        "value": [
            {
                "systemuserid": "valid-id-1",
                "fullname": "CRM User 1",
                "internalemailaddress": "<EMAIL>",
            },
            {
                "systemuserid": None,  # Invalid, triggers ValidationError
                "fullname": "Invalid User",
                "internalemailaddress": "<EMAIL>",
            },
            {
                "systemuserid": "valid-id-2",
                "fullname": "CRM User 2",
                "internalemailaddress": "<EMAIL>",
            },
        ]
    }
    mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: users_response)
    # Create a matching zeplyn user
    matched_user = User.objects.create(
        email="<EMAIL>",
        organization=test_organization,
        name="Matched User",
        username="matched_user",
    )
    result = dynamics.list_users(test_user)

    def expected() -> list[CRMUser]:
        return [
            CRMUser(
                name="CRM User 1",
                crm_id="valid-id-1",
                organization=ZeplynOrganization(uuid=str(test_organization.uuid), name=test_organization.name),
                zeplyn_user=ZeplynUser(uuid=str(matched_user.uuid), email=matched_user.email, name=matched_user.name),
            ),
            CRMUser(
                name="CRM User 2",
                crm_id="valid-id-2",
                organization=ZeplynOrganization(uuid=str(test_organization.uuid), name=test_organization.name),
                zeplyn_user=None,
            ),
        ]

    # Only valid users should be returned (first and third)
    assert len(result) == 2

    assert result == expected()
    mock_requests_get.assert_has_calls(
        [
            call(
                "https://tamarac.example.com/api/data/v9.2/systemusers",
                headers=ANY,
                params={"$select": "fullname,systemuserid,internalemailaddress", "$filter": "statecode eq 0"},
            )
        ]
    )
