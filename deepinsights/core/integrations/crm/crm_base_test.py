import logging

import pytest

from deepinsights.core.integrations.crm.crm_models import CRMAccount


def test_crm_account_valid_data(caplog: pytest.LogCaptureFixture) -> None:
    with caplog.at_level(logging.ERROR):
        account = CRMAccount(
            crm_id="123",
            name="Test Account",
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>",
            email="<EMAIL>",
            phone_number="**********",
            client_type="client",
            crm_system="test_crm",
        )
        assert not caplog.records
    assert account.crm_id == "123"
    assert account.name == "Test Account"
    assert account.first_name == "<PERSON>"
    assert account.last_name == "Doe"
    assert account.email == "<EMAIL>"
    assert account.phone_number == "+***********"
    assert account.client_type == "client"
    assert account.crm_system == "test_crm"


def test_crm_account_missing_optional_fields() -> None:
    account = CRMAccount(crm_id="123", name="Test Account", client_type="client", crm_system="test_crm")
    assert account.crm_id == "123"
    assert account.name == "Test Account"
    assert account.first_name is None
    assert account.last_name is None
    assert account.email is None
    assert account.phone_number is None
    assert account.client_type == "client"
    assert account.crm_system == "test_crm"


@pytest.mark.parametrize("phone_number", ["**********", "invalid", "+**********"])
def test_crm_account_invalid_phone_number(caplog: pytest.LogCaptureFixture, phone_number: str) -> None:
    with caplog.at_level(logging.WARNING):
        account = CRMAccount(
            crm_id="123", name="Test Account", phone_number=phone_number, client_type="client", crm_system="test_crm"
        )
        assert caplog.records
        assert caplog.records[0].message == "Phone number validation failed, returning None"
    assert account.phone_number is None


@pytest.mark.parametrize("phone_number", [None, ""])
def test_crm_account_empty_phone_number(caplog: pytest.LogCaptureFixture, phone_number: str | None) -> None:
    with caplog.at_level(logging.WARNING):
        account = CRMAccount(
            crm_id="123", name="Test Account", phone_number=phone_number, client_type="client", crm_system="test_crm"
        )
        assert not caplog.records
    assert account.phone_number is None
