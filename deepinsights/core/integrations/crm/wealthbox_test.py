import datetime
import json
import uuid
from typing import Any, Callable
from unittest.mock import ANY, <PERSON>Mock, call, patch

import pytest
from django.utils import timezone as django_timezone
from pytest_django.fixtures import SettingsWrapper

from deepinsights.core.integrations.calendar.calendar_models import Calendar<PERSON>vent, EventParticipant
from deepinsights.core.integrations.crm.crm_models import (
    CRMAccount,
    CRMNote,
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTarget,
    CRMSyncTargets,
    CRMUser,
    ZeplynOrganization,
    ZeplynUser,
)
from deepinsights.core.integrations.crm.wealthbox import Wealthbox
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingData, StructuredMeetingDataTemplate
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User

pytestmark = [pytest.mark.django_db]


@pytest.fixture
def test_organization() -> Organization:
    return Organization.objects.create(name="Test Organization")


@pytest.fixture
def test_user(test_organization: Organization) -> User:
    return User.objects.create(email="<EMAIL>", organization=test_organization)


@pytest.fixture
def test_user_without_org() -> User:
    return User.objects.create(email="<EMAIL>")


@pytest.fixture
def oauth_credentials(test_user: User) -> OAuthCredentials:
    credentials: OAuthCredentials = OAuthCredentials.objects.create(
        user=test_user,
        integration="wealthbox",
        access_token="mock_access_token",
        expires_in=django_timezone.now() + datetime.timedelta(days=1),
        refresh_token="mock_refresh_token",
        refresh_token_expires_in=django_timezone.now() + datetime.timedelta(days=1),
    )
    return credentials


@pytest.fixture
def test_client(test_user: User, test_organization: Organization) -> Client:
    client: Client = Client.objects.create(name="Test Client", organization=test_organization, crm_id="123")
    client.authorized_users.add(test_user)
    client.save()
    return client


@pytest.fixture
def note(test_user: User, test_client: Client) -> Note:
    note: Note = Note.objects.create(note_owner=test_user, client={"uuid": str(test_client.uuid)}, summary="Test Note")
    return note


@pytest.fixture
def note_without_client(test_user: User, test_client: Client) -> Note:
    note: Note = Note.objects.create(note_owner=test_user, summary="Test Note")
    return note


# Returns a callable that returns the topic for the given index in the bento_life_data.
@pytest.fixture
def bento_life_data(note: Note) -> Callable[[int], str]:
    # Searching for the specific template created in the conftest.py to ignore the Template created by migrations
    bento_life_template = StructuredMeetingDataTemplate.objects.get(
        internal_name="Bento life events Test", kind=StructuredMeetingDataTemplate.Kind.BENTO_LIFE_EVENTS
    )
    data = bento_life_template.initial_data
    assert data is not None
    data["review_entries"][0]["discussed"] = True
    data["review_entries"][2]["discussed"] = True
    data["review_entries"][5]["discussed"] = True
    bento_life_template.create_structured_meeting_data(note, data)

    return lambda i: data["review_entries"][i]["topic"]


@patch("requests.get")
def test_fetch_notes_for_client(mock_requests_get: MagicMock, test_user: User, test_client: Client) -> None:
    wealthbox = Wealthbox()
    wealthbox.oauth = MagicMock()
    wealthbox.oauth.get_access_token.return_value = "mock_access_token"

    thirty_days_ago = django_timezone.now() - datetime.timedelta(days=30)

    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.side_effect = [
        {"current_user": {"account": "123"}},
        {
            "status_updates": [
                {
                    "id": 1,
                    "content": "This is a note",
                    "created_at": thirty_days_ago.strftime("%Y-%m-%d %I:%M %p %z"),
                    "linked_to": [{"id": 123}],
                },
                {
                    "id": 2,
                    "content": "This is an old note",
                    "created_at": (django_timezone.now() - datetime.timedelta(days=400)).strftime(
                        "%Y-%m-%d %I:%M %p %z"
                    ),
                    "linked_to": [{"id": 123}],
                },
            ],
            "meta": {"total_pages": 1},
        },
    ]

    mock_requests_get.return_value = mock_response

    notes = wealthbox.fetch_notes_for_client(
        user=test_user, client=test_client, lookback_interval=datetime.timedelta(days=365)
    )

    assert notes == [
        CRMNote(
            crm_id="1",
            crm_system="wealthbox",
            content="This is a note",
            created_at=thirty_days_ago.replace(second=0, microsecond=0),
            web_link="https://app.crmworkspace.com/123/status_updates/1",
        )
    ]

    assert mock_requests_get.call_count == 2
    mock_requests_get.assert_has_calls(
        [
            call(
                f"{wealthbox.base_url}/v1/me",
                headers={"Authorization": "Bearer mock_access_token", "Content-Type": "application/json"},
            ),
            call(
                f"{wealthbox.base_url}/v1/notes",
                headers={"Authorization": "Bearer mock_access_token", "Content-Type": "application/json"},
                params={"per_page": 100, "page": 1, "order": "created"},
            ),
        ],
        any_order=True,
    )


@patch("requests.get")
def test_get_accounts_by_owner_email_and_name(
    mock_requests_get: MagicMock, test_user: User, oauth_credentials: OAuthCredentials
) -> None:
    wealthbox = Wealthbox()

    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "contacts": [
            {
                "first_name": "John",
                "last_name": "Doe",
                "email_addresses": [{"address": "<EMAIL>"}],
                "phone_numbers": [{"address": "************"}],
                "id": 1,
                "type": "person",
                "name": "John Doe",
            },
            {
                "first_name": "Jane",
                "last_name": "Doe",
                "email_addresses": [
                    {"address": "<EMAIL>"},
                    {"address": "<EMAIL>", "principal": True},
                ],
                "phone_numbers": [
                    {"address": "************"},
                    {"address": "+************", "principal": True},
                ],
                "id": 2,
                "type": "person",
                "name": "Jane Doe",
            },
        ],
        "meta": {"total_pages": 2},
    }

    second_mock_response = MagicMock()
    second_mock_response.status_code = 200
    second_mock_response.json.return_value = {
        "contacts": [
            {
                "id": "3",
                "email_addresses": [
                    {"address": "<EMAIL>", "principal": True},
                ],
                "type": "person",
                "name": "Client Three",
            },
            {
                "id": "4",
                "email_addresses": [
                    {"address": "<EMAIL>"},
                ],
                "type": "person",
                "name": "Client Four",
            },
        ],
        "meta": {"total_pages": 2},
    }
    mock_requests_get.side_effect = [mock_response, second_mock_response]

    accounts = wealthbox.get_accounts_by_owner_email_and_name(owner_email="<EMAIL>")

    assert accounts == [
        CRMAccount(
            crm_id="1",
            name="John Doe",
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            phone_number="**********",
            client_type="person",
            crm_system="wealthbox",
        ),
        CRMAccount(
            crm_id="2",
            name="Jane Doe",
            first_name="Jane",
            last_name="Doe",
            email="<EMAIL>",
            phone_number="+************",
            client_type="person",
            crm_system="wealthbox",
        ),
        CRMAccount(
            crm_id="3",
            name="Client Three",
            first_name=None,
            last_name=None,
            email="<EMAIL>",
            phone_number=None,
            client_type="person",
            crm_system="wealthbox",
        ),
        CRMAccount(
            crm_id="4",
            name="Client Four",
            first_name=None,
            last_name=None,
            email="<EMAIL>",
            phone_number=None,
            client_type="person",
            crm_system="wealthbox",
        ),
    ]


@patch("requests.patch")
@patch("requests.post")
@patch("requests.get")
@patch("deepinsights.core.integrations.crm.wealthbox.timezone")
def test_add_interaction_with_client(
    mock_timezone: MagicMock,
    mock_requests_get: MagicMock,
    mock_requests_post: MagicMock,
    mock_requests_patch: MagicMock,
    oauth_credentials: OAuthCredentials,
    note: Note,
    test_user: User,
    django_user_model: User,
    test_organization: Organization,
) -> None:
    task_assignee = django_user_model.objects.create(
        username="<EMAIL>", email="<EMAIL>", organization=test_organization
    )
    other_assignee = django_user_model.objects.create(
        username="<EMAIL>", email="<EMAIL>", organization=test_organization
    )
    task = Task.objects.create(
        note=note, task_title="Test Task", due_date=django_timezone.now(), assignee=task_assignee
    )
    task_two = Task.objects.create(note=note, task_title="Test Task 2")
    task_three = Task.objects.create(note=note, task_title="Test Task 3", assignee=other_assignee)

    wealthbox = Wealthbox()
    wealthbox.oauth = MagicMock()
    wealthbox.oauth.get_access_token.return_value = "mock_access_token"

    def request_get_response(*args: Any, **kwargs: Any) -> dict[str, Any]:
        if "v1/me" in args[0]:
            return MagicMock(status_code=200, json=lambda: {"current_user": {"id": "mock_current_user"}})

        return MagicMock(
            status_code=200,
            json=lambda: {
                "teams": [
                    {
                        "members": [
                            {"id": "mock_user_id", "email": "<EMAIL>"},
                            {"id": "mock_assignee_id", "email": "<EMAIL>"},
                        ]
                    }
                ]
            },
        )

    mock_requests_get.side_effect = request_get_response

    mock_response_post = MagicMock()
    mock_response_post.status_code = 200
    mock_requests_post.return_value = mock_response_post

    fake_now = datetime.datetime(2025, 1, 1, 12, 0, 0, tzinfo=datetime.timezone.utc)
    mock_timezone.now.return_value = fake_now

    with patch.object(Note, "get_summary_for_crm") as mock_get_summary_for_crm:
        mock_get_summary_for_crm.return_value = "Test Note Summary"
        sync_targets = wealthbox.resolve_sync_targets(note, test_user)
        wealthbox.add_interaction_with_client(note=note, sync_targets=sync_targets)

    assert mock_requests_post.call_count == 4
    mock_requests_post.assert_has_calls(
        [
            call(
                f"{wealthbox.base_url}/v1/notes",
                headers={"Content-Type": "application/json", "Authorization": "Bearer mock_access_token"},
                data=json.dumps(
                    {
                        "content": "Test Note Summary",
                        "linked_to": [{"id": "123", "type": "Contact", "name": "Test Client"}],
                        "visible_to": "Everyone",
                    }
                ),
            ),
            call(
                f"{wealthbox.base_url}/v1/tasks",
                headers={"Content-Type": "application/json", "Authorization": "Bearer mock_access_token"},
                data=json.dumps(
                    {
                        "name": task.task_title,
                        "due_date": task.due_date.isoformat() if task.due_date else None,
                        "complete": task.completed,
                        "linked_to": [{"id": "123", "type": "Contact", "name": "Test Client"}],
                        "assigned_to": "mock_assignee_id",
                    }
                ),
            ),
            call(
                f"{wealthbox.base_url}/v1/tasks",
                headers={"Content-Type": "application/json", "Authorization": "Bearer mock_access_token"},
                data=json.dumps(
                    {
                        "name": task_two.task_title,
                        "due_date": fake_now.isoformat(),
                        "complete": False,
                        "linked_to": [{"id": "123", "type": "Contact", "name": "Test Client"}],
                        "assigned_to": "mock_user_id",
                    }
                ),
            ),
            call(
                f"{wealthbox.base_url}/v1/tasks",
                headers={"Content-Type": "application/json", "Authorization": "Bearer mock_access_token"},
                data=json.dumps(
                    {
                        "name": task_three.task_title,
                        "due_date": fake_now.isoformat(),
                        "complete": False,
                        "linked_to": [{"id": "123", "type": "Contact", "name": "Test Client"}],
                        "assigned_to": "mock_current_user",
                    }
                ),
            ),
        ],
    )

    mock_requests_patch.assert_not_called()


@patch("requests.patch")
@patch("requests.post")
@patch("requests.get")
def test_add_interaction_with_client_not_found(
    mock_requests_get: MagicMock,
    mock_requests_post: MagicMock,
    mock_requests_patch: MagicMock,
    oauth_credentials: OAuthCredentials,
    note: Note,
    note_without_client: Note,
    django_user_model: User,
    test_organization: Organization,
    test_user: User,
) -> None:
    wealthbox = Wealthbox()
    wealthbox.oauth = MagicMock()
    wealthbox.oauth.get_access_token.return_value = "mock_access_token"

    mock_response_get = MagicMock()
    mock_response_get.status_code = 200
    mock_response_get.json.return_value = {
        "teams": [
            {
                "members": [
                    {"id": "mock_user_id", "email": "<EMAIL>"},
                    {"id": "mock_assignee_id", "email": "<EMAIL>"},
                ]
            }
        ]
    }
    mock_requests_get.return_value = mock_response_get

    mock_response_post = MagicMock()
    mock_response_post.status_code = 200
    mock_requests_post.return_value = mock_response_post
    with patch.object(Note, "get_summary_for_crm") as mock_get_summary_for_crm:
        mock_get_summary_for_crm.return_value = "Test Note Summary"
        with pytest.raises(Exception) as exc_info:
            sync_targets = wealthbox.resolve_sync_targets(note_without_client, test_user)
            wealthbox.add_interaction_with_client(note=note_without_client, sync_targets=sync_targets)
            assert (
                exc_info.value.args[0]
                == "There are no clients associated with the note, please add client to sync with CRM"
            )

    mock_requests_post.assert_not_called()
    mock_requests_patch.assert_not_called()


@patch("requests.patch")
@patch("requests.post")
@patch("requests.get")
def test_add_interaction_with_client_with_bento_tags(
    mock_requests_get: MagicMock,
    mock_requests_post: MagicMock,
    mock_requests_patch: MagicMock,
    oauth_credentials: OAuthCredentials,
    note: Note,
    test_user: User,
    bento_life_data: Callable[[int], str],
) -> None:
    wealthbox = Wealthbox()
    wealthbox.oauth = MagicMock()
    wealthbox.oauth.get_access_token.return_value = "mock_access_token"

    mock_response_get_teams = MagicMock()
    mock_response_get_teams.status_code = 200
    mock_response_get_teams.json.return_value = {
        "teams": [
            {
                "members": [
                    {"id": "mock_user_id", "email": "<EMAIL>"},
                ]
            }
        ]
    }
    mock_response_get_client = MagicMock()
    mock_response_get_client.status_code = 200
    mock_response_get_client.json.return_value = {
        "tags": [],
    }
    mock_requests_get.side_effect = [mock_response_get_teams, mock_response_get_client]

    mock_response_post = MagicMock()
    mock_response_post.status_code = 200
    mock_requests_post.side_effect = mock_response_post
    mock_requests_patch.return_value = MagicMock(status_code=200)

    with patch.object(note, "get_summary_for_crm") as _:
        sync_targets = wealthbox.resolve_sync_targets(note, test_user)
        wealthbox.add_interaction_with_client(note, sync_targets)

    mock_requests_get.assert_any_call(
        f"{wealthbox.base_url}/v1/contacts/123",
        headers={"Content-Type": "application/json", "Authorization": "Bearer mock_access_token"},
    )

    mock_requests_patch.assert_called_once_with(
        f"{wealthbox.base_url}/v1/contacts/123",
        headers=ANY,
        data=json.dumps(
            {
                "tags": [
                    f"Bento - {bento_life_data(0)}",
                    f"Bento - {bento_life_data(2)}",
                    f"Bento - {bento_life_data(5)}",
                ]
            }
        ),
    )


@patch("requests.patch")
@patch("requests.post")
@patch("requests.get")
def test_add_interaction_with_client_with_incomplete_bento_tags(
    mock_requests_get: MagicMock,
    mock_requests_post: MagicMock,
    mock_requests_patch: MagicMock,
    oauth_credentials: OAuthCredentials,
    note: Note,
    test_user: User,
    bento_life_data: Callable[[int], str],
    settings: SettingsWrapper,
) -> None:
    settings.WEALTHBOX_BASE_URL = "https://example.com"
    bento_structured_meeting_data = StructuredMeetingData.objects.first()
    if bento_structured_meeting_data:
        bento_structured_meeting_data.status = StructuredMeetingData.Status.PROCESSING
        bento_structured_meeting_data.save()
    wealthbox = Wealthbox()
    wealthbox.oauth = MagicMock()
    wealthbox.oauth.get_access_token.return_value = "mock_access_token"

    mock_response_get_teams = MagicMock()
    mock_response_get_teams.status_code = 200
    mock_response_get_teams.json.return_value = {
        "teams": [
            {
                "members": [
                    {"id": "mock_user_id", "email": "<EMAIL>"},
                ]
            }
        ]
    }
    mock_requests_get.return_value = mock_response_get_teams

    mock_response_post = MagicMock()
    mock_response_post.status_code = 200
    mock_requests_post.side_effect = mock_response_post

    mock_requests_patch.return_value = MagicMock(status_code=200)

    with patch.object(note, "get_summary_for_crm") as mock_get_summary_for_crm:
        mock_get_summary_for_crm.return_value = {}
        sync_targets = wealthbox.resolve_sync_targets(note, test_user)
        wealthbox.add_interaction_with_client(note=note, sync_targets=sync_targets)

    mock_requests_get.assert_called_once_with(
        f"{wealthbox.base_url}/v1/teams",
        headers=ANY,
    )
    mock_requests_patch.assert_not_called()


@patch("requests.patch")
@patch("requests.post")
@patch("requests.get")
def test_add_interaction_with_client_with_existing_bento_tags(
    mock_requests_get: MagicMock,
    mock_requests_post: MagicMock,
    mock_requests_patch: MagicMock,
    oauth_credentials: OAuthCredentials,
    note: Note,
    test_user: User,
    bento_life_data: Callable[[int], str],
) -> None:
    wealthbox = Wealthbox()
    wealthbox.oauth = MagicMock()
    wealthbox.oauth.get_access_token.return_value = "mock_access_token"

    mock_response_get_teams = MagicMock()
    mock_response_get_teams.status_code = 200
    mock_response_get_teams.json.return_value = {
        "teams": [
            {
                "members": [
                    {"id": "mock_user_id", "email": "<EMAIL>"},
                ]
            }
        ]
    }

    mock_response_get_client = MagicMock()
    mock_response_get_client.status_code = 200
    mock_response_get_client.json.return_value = {
        "tags": [
            {"id": 1, "name": f"Bento - {bento_life_data(0)}"},
            {"id": 2, "name": f"Bento - {bento_life_data(1)}"},
        ],
    }
    mock_requests_get.side_effect = [mock_response_get_teams, mock_response_get_client]

    mock_response_post = MagicMock()
    mock_response_post.status_code = 200
    mock_requests_post.side_effect = mock_response_post

    mock_requests_patch.return_value = MagicMock(status_code=200)

    with patch.object(note, "get_summary_for_crm") as mock_get_summary_for_crm:
        sync_targets = wealthbox.resolve_sync_targets(note, test_user)
        wealthbox.add_interaction_with_client(note=note, sync_targets=sync_targets)

    mock_requests_patch.assert_called_once_with(
        f"{wealthbox.base_url}/v1/contacts/123",
        headers=ANY,
        data=json.dumps(
            {
                "tags": [
                    f"Bento - {bento_life_data(0)}",
                    f"Bento - {bento_life_data(1)}",
                    f"Bento - {bento_life_data(2)}",
                    f"Bento - {bento_life_data(5)}",
                ]
            }
        ),
    )


@pytest.mark.parametrize(
    "sync_items,expected_summary_call,should_sync_tasks",
    [
        # Backward compatibility tests
        (None, {"use_html_formatting": False, "sync_items": None}, True),
        ({}, {"use_html_formatting": False, "sync_items": {}}, True),
        # Functional tests
        (
            {CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True)},
            {
                "use_html_formatting": False,
                "sync_items": {CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True)},
            },
            True,
        ),
        (
            {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True)},
            {
                "use_html_formatting": False,
                "sync_items": {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True)},
            },
            True,
        ),
        (
            {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=False)},
            {
                "use_html_formatting": False,
                "sync_items": {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=False)},
            },
            False,
        ),
        # Mixed boolean sections
        (
            {
                CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True),
                CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True),
                CRMSyncSection.ATTENDEES: CRMSyncItemSelection(include_section=False),
                CRMSyncSection.KEYWORDS: CRMSyncItemSelection(include_section=False),
            },
            {
                "use_html_formatting": False,
                "sync_items": {
                    CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True),
                    CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True),
                    CRMSyncSection.ATTENDEES: CRMSyncItemSelection(include_section=False),
                    CRMSyncSection.KEYWORDS: CRMSyncItemSelection(include_section=False),
                },
            },
            True,
        ),
        (
            {
                CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True),
                CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=False),
                CRMSyncSection.ATTENDEES: CRMSyncItemSelection(include_section=True),
            },
            {
                "use_html_formatting": False,
                "sync_items": {
                    CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True),
                    CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=False),
                    CRMSyncSection.ATTENDEES: CRMSyncItemSelection(include_section=True),
                },
            },
            False,
        ),
    ],
)
@patch("requests.patch")
@patch("requests.post")
@patch("requests.get")
@patch("deepinsights.core.integrations.crm.wealthbox.timezone")
def test_add_interaction_with_client_sync_items_parametrized(
    mock_timezone: MagicMock,
    mock_requests_get: MagicMock,
    mock_requests_post: MagicMock,
    mock_requests_patch: MagicMock,
    sync_items: dict[CRMSyncSection, CRMSyncItemSelection] | None,
    expected_summary_call: dict[str, Any],
    should_sync_tasks: bool,
    oauth_credentials: Any,
    note: Note,
    test_user: User,
    django_user_model: User,
    test_organization: Organization,
) -> None:
    task_assignee = django_user_model.objects.create(
        username="<EMAIL>", email="<EMAIL>", organization=test_organization
    )
    task = Task.objects.create(note=note, task_title="Test Task", assignee=task_assignee)
    task_two = Task.objects.create(note=note, task_title="Test Task 2")

    wealthbox = Wealthbox()
    wealthbox.oauth = MagicMock()
    wealthbox.oauth.get_access_token.return_value = "mock_access_token"

    def request_get_response(*args: Any, **kwargs: Any) -> MagicMock:
        if "v1/me" in args[0]:
            return MagicMock(status_code=200, json=lambda: {"current_user": {"id": "mock_current_user"}})
        return MagicMock(
            status_code=200,
            json=lambda: {
                "teams": [
                    {
                        "members": [
                            {"id": "mock_user_id", "email": "<EMAIL>"},
                            {"id": "mock_assignee_id", "email": "<EMAIL>"},
                        ]
                    }
                ]
            },
        )

    mock_requests_get.side_effect = request_get_response
    mock_requests_post.return_value = MagicMock(status_code=200)

    fake_now = datetime.datetime(2025, 1, 1, 12, 0, 0, tzinfo=datetime.timezone.utc)
    mock_timezone.now.return_value = fake_now

    with patch.object(Note, "get_summary_for_crm") as mock_get_summary_for_crm:
        mock_get_summary_for_crm.return_value = "Test Note Summary"
        sync_targets = wealthbox.resolve_sync_targets(note, test_user)
        wealthbox.add_interaction_with_client(note=note, sync_targets=sync_targets, sync_items=sync_items)

    mock_get_summary_for_crm.assert_called_once_with(**expected_summary_call)

    expected_calls = [
        call(
            f"{wealthbox.base_url}/v1/notes",
            headers={"Content-Type": "application/json", "Authorization": "Bearer mock_access_token"},
            data=json.dumps(
                {
                    "content": "Test Note Summary",
                    "linked_to": [{"id": "123", "type": "Contact", "name": "Test Client"}],
                    "visible_to": "Everyone",
                }
            ),
        )
    ]

    if should_sync_tasks:
        expected_calls.extend(
            [
                call(
                    f"{wealthbox.base_url}/v1/tasks",
                    headers={"Content-Type": "application/json", "Authorization": "Bearer mock_access_token"},
                    data=json.dumps(
                        {
                            "name": task.task_title,
                            "due_date": fake_now.isoformat(),
                            "complete": task.completed,
                            "linked_to": [{"id": "123", "type": "Contact", "name": "Test Client"}],
                            "assigned_to": "mock_assignee_id",
                        }
                    ),
                ),
                call(
                    f"{wealthbox.base_url}/v1/tasks",
                    headers={"Content-Type": "application/json", "Authorization": "Bearer mock_access_token"},
                    data=json.dumps(
                        {
                            "name": task_two.task_title,
                            "due_date": fake_now.isoformat(),
                            "complete": False,
                            "linked_to": [{"id": "123", "type": "Contact", "name": "Test Client"}],
                            "assigned_to": "mock_user_id",
                        }
                    ),
                ),
            ]
        )

    assert mock_requests_post.call_count == len(expected_calls)
    mock_requests_post.assert_has_calls(expected_calls)

    mock_requests_patch.assert_not_called()


def test_add_interaction_backward_compatibility_method_signature(
    oauth_credentials: Any,
    note: Note,
    test_user: User,
) -> None:
    """Test that the method signature maintains backward compatibility."""
    wealthbox = Wealthbox()
    wealthbox.oauth = MagicMock()
    wealthbox.oauth.get_access_token.return_value = "mock_access_token"

    sync_targets = wealthbox.resolve_sync_targets(note, test_user)

    try:
        with patch.object(Note, "get_summary_for_crm", return_value="test_note_summary"):
            with patch("requests.get") as mock_get:
                mock_get.return_value = MagicMock(
                    status_code=200,
                    json=lambda: {"teams": [{"members": [{"id": "mock_user_id", "email": "<EMAIL>"}]}]},
                )
                with patch("requests.post") as mock_post:
                    mock_post.return_value = MagicMock(status_code=200)
                    wealthbox.add_interaction_with_client(note, sync_targets)

        assert True
    except TypeError as e:
        pytest.fail(f"Backward compatibility broken: {e}")


@patch("requests.post")
def test_add_note_sync_items_parameter_direct(mock_requests_post: MagicMock) -> None:
    wealthbox = Wealthbox()
    mock_note = MagicMock(spec=Note)
    mock_note.uuid = "test-uuid"
    mock_note.get_summary_for_crm.return_value = "Test summary content"

    clients = [{"id": "123", "type": "Contact", "name": "Test Client"}]
    sync_items = {
        CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True),
        CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=False),
    }

    mock_requests_post.return_value = MagicMock(status_code=200)

    wealthbox.add_note("mock_access_token", mock_note, clients, sync_items)

    mock_note.get_summary_for_crm.assert_called_with(use_html_formatting=False, sync_items=sync_items)

    mock_requests_post.assert_called_once_with(
        f"{wealthbox.base_url}/v1/notes",
        headers={"Content-Type": "application/json", "Authorization": "Bearer mock_access_token"},
        data=json.dumps(
            {
                "content": "Test summary content",
                "linked_to": clients,
                "visible_to": "Everyone",
            }
        ),
    )

    mock_requests_post.reset_mock()
    mock_note.get_summary_for_crm.reset_mock()

    wealthbox.add_note("mock_access_token", mock_note, clients)

    mock_note.get_summary_for_crm.assert_called_with(use_html_formatting=False, sync_items=None)


@patch("requests.get")
def test_fetch_events_no_access_token(mock_requests_get: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")

    assert Wealthbox().fetch_events(user, datetime.timedelta(days=1)) == []
    mock_requests_get.assert_not_called()


@patch("requests.get")
def test_fetch_events_http_error(
    mock_requests_get: MagicMock, django_user_model: User, settings: SettingsWrapper
) -> None:
    settings.WEALTHBOX_BASE_URL = "https://api.wealthbox.com"
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="wealthbox",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=django_timezone.now() + datetime.timedelta(hours=1),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    mock_users_response = MagicMock()
    mock_users_response.status_code = 200
    mock_users_response.json.return_value = {"users": [], "meta": {"total_pages": 1}}

    mock_events_response = MagicMock()
    mock_events_response.status_code = 500

    mock_requests_get.side_effect = [mock_users_response, mock_events_response]

    assert Wealthbox().fetch_events(user, datetime.timedelta(days=1)) == []
    mock_requests_get.assert_has_calls(
        [
            call(
                "https://api.wealthbox.com/v1/users",
                headers=ANY,
                params={"per_page": 100, "page": 1},
            ),
            call(
                "https://api.wealthbox.com/v1/events",
                headers=ANY,
                params={"per_page": 100, "page": 1, "order": "asc", "start_date_min": ANY, "start_date_max": ANY},
            ),
        ]
    )


@patch("requests.get")
def test_fetch_events_success(mock_requests_get: MagicMock, django_user_model: User, settings: SettingsWrapper) -> None:
    settings.WEALTHBOX_BASE_URL = "https://api.wealthbox.com"
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="wealthbox",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    mock_users_response = MagicMock()
    mock_users_response.status_code = 200
    mock_users_response.json.return_value = {"users": [], "meta": {"total_pages": 1}}

    start_time = django_timezone.now().replace(second=0, microsecond=0)
    interval = datetime.timedelta(hours=1)
    end_time = start_time + interval

    mock_events_response = MagicMock(
        status_code=200,
        json=lambda: {
            "events": [
                {
                    "id": 1,
                    "title": "Event 1",
                    "starts_at": start_time.strftime("%Y-%m-%d %I:%M %p %z"),
                    "ends_at": end_time.strftime("%Y-%m-%d %I:%M %p %z"),
                    "all_day": False,
                    "description": "Description 1",
                    "location": "",
                    "invitees": [],
                }
            ],
            "meta": {"total_pages": 1},
        },
    )

    mock_requests_get.side_effect = [mock_users_response, mock_events_response]

    events = Wealthbox().fetch_events(user, interval)
    assert events == [
        CalendarEvent(
            provider="wealthbox",
            id="1",
            user_specific_id="1",
            title="Event 1",
            start_time=start_time,
            end_time=end_time,
            all_day=False,
            body="Description 1",
            participants=[],
            meeting_urls=[],
        )
    ]

    start_date_min = (start_time - datetime.timedelta(days=1)).date().isoformat()
    start_date_max = (end_time + datetime.timedelta(days=1)).date().isoformat()
    mock_requests_get.assert_has_calls(
        [
            call(
                "https://api.wealthbox.com/v1/events",
                headers=ANY,
                params={
                    "per_page": 100,
                    "page": 1,
                    "start_date_min": start_date_min,
                    "start_date_max": start_date_max,
                    "order": "asc",
                },
            ),
        ]
    )


@patch("requests.get")
def test_fetch_events_time_handling(
    mock_requests_get: MagicMock, django_user_model: User, settings: SettingsWrapper
) -> None:
    settings.WEALTHBOX_BASE_URL = "https://api.wealthbox.com"
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="wealthbox",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    mock_users_response = MagicMock()
    mock_users_response.status_code = 200
    mock_users_response.json.return_value = {"users": [], "meta": {"total_pages": 1}}

    start_time = django_timezone.now().replace(second=0, microsecond=0)
    end_time = start_time + datetime.timedelta(hours=1)

    mock_events_response = MagicMock(
        status_code=200,
        json=lambda: {
            "events": [
                {
                    "id": 1,
                    "title": "Event 1",
                    "starts_at": (start_time + datetime.timedelta(hours=1, minutes=1)).strftime("%Y-%m-%d %I:%M %p %z"),
                    "ends_at": (end_time + datetime.timedelta(hours=1, minutes=1)).strftime("%Y-%m-%d %I:%M %p %z"),
                    "all_day": False,
                    "description": "Description 1",
                    "location": "",
                    "invitees": [],
                },
                {
                    "id": 2,
                    "title": "Event 2",
                    "starts_at": (start_time - datetime.timedelta(hours=1, minutes=1)).strftime("%Y-%m-%d %I:%M %p %z"),
                    "ends_at": (end_time - datetime.timedelta(hours=1, minutes=1)).strftime("%Y-%m-%d %I:%M %p %z"),
                    "all_day": False,
                    "invitees": [],
                },
                {
                    "id": 3,
                    "title": "Event 3",
                    "starts_at": start_time.strftime("%Y-%m-%d %I:%M %p %z"),
                    "ends_at": end_time.strftime("%Y-%m-%d %I:%M %p %z"),
                    "all_day": False,
                },
                {
                    "id": 4,
                    "title": "Event 4",
                    "starts_at": start_time.strftime("%Y-%m-%d %I:%M %p %z"),
                    "ends_at": end_time.strftime("%Y-%m-%d %I:%M %p %z"),
                    "all_day": True,
                },
            ],
            "meta": {"total_pages": 1},
        },
    )

    mock_requests_get.side_effect = [mock_users_response, mock_events_response]

    events = Wealthbox().fetch_events(user, datetime.timedelta(hours=1))
    assert events == [
        CalendarEvent(
            provider="wealthbox",
            id="3",
            user_specific_id="3",
            title="Event 3",
            start_time=start_time,
            end_time=end_time,
            all_day=False,
            body=None,
            participants=[],
            meeting_urls=[],
        ),
        CalendarEvent(
            provider="wealthbox",
            id="4",
            user_specific_id="4",
            title="Event 4",
            start_time=start_time,
            end_time=end_time,
            all_day=True,
            body=None,
            participants=[],
            meeting_urls=[],
        ),
    ]


@pytest.mark.parametrize(
    "location, description, expected_meeting_urls",
    [
        ("", "", []),
        ("", "https://meet.google.com/abc", ["https://meet.google.com/abc"]),
        ("https://meet.google.com/abc", "", ["https://meet.google.com/abc"]),
        (
            "https://meet.google.com/abc",
            "https://meet.google.com/def",
            ["https://meet.google.com/abc", "https://meet.google.com/def"],
        ),
        (
            "https://meet.google.com/def",
            "https://meet.google.com/abc",
            ["https://meet.google.com/def", "https://meet.google.com/abc"],
        ),
    ],
)
@patch("requests.get")
def test_fetch_events_url_handling(
    mock_requests_get: MagicMock,
    django_user_model: User,
    settings: SettingsWrapper,
    description: str,
    location: str,
    expected_meeting_urls: list[str],
) -> None:
    settings.WEALTHBOX_BASE_URL = "https://api.wealthbox.com"
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="wealthbox",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )
    mock_users_response = MagicMock()
    mock_users_response.status_code = 200
    mock_users_response.json.return_value = {"users": [], "meta": {"total_pages": 1}}

    start_time = django_timezone.now().replace(second=0, microsecond=0)
    end_time = start_time + datetime.timedelta(hours=1)

    mock_events_response = MagicMock(
        status_code=200,
        json=lambda: {
            "events": [
                {
                    "id": 1,
                    "title": "Event 1",
                    "starts_at": start_time.strftime("%Y-%m-%d %I:%M %p %z"),
                    "ends_at": end_time.strftime("%Y-%m-%d %I:%M %p %z"),
                    "all_day": False,
                    "description": description,
                    "location": location,
                    "invitees": [],
                }
            ],
            "meta": {"total_pages": 1},
        },
    )

    mock_requests_get.side_effect = [mock_users_response, mock_events_response]

    events = Wealthbox().fetch_events(user, datetime.timedelta(days=1))
    assert events == [
        CalendarEvent(
            provider="wealthbox",
            id="1",
            user_specific_id="1",
            title="Event 1",
            start_time=start_time,
            end_time=end_time,
            all_day=False,
            body=description,
            participants=[],
            meeting_urls=expected_meeting_urls,
        )
    ]


@patch("requests.get")
def test_fetch_events_participants(
    mock_requests_get: MagicMock, django_user_model: User, settings: SettingsWrapper
) -> None:
    settings.WEALTHBOX_BASE_URL = "https://api.wealthbox.com"
    org = Organization.objects.create(name="Test Organization")
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>", organization=org)
    OAuthCredentials.objects.create(
        user=user,
        integration="wealthbox",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    client_one = Client.objects.create(
        name="Client One",
        organization=org,
        crm_id="clientOne",
        email="<EMAIL>",
        crm_system="wealthbox",
    )
    client_one.authorized_users.add(user)
    client_one.save()
    client_two = Client.objects.create(
        name="Client Two",
        organization=org,
        crm_id="clientTwo",
        email="<EMAIL>",
        crm_system="wealthbox",
    )
    client_two.authorized_users.add(user)
    client_two.save()
    client_three = Client.objects.create(
        name="Client Three",
        organization=org,
        crm_id="clientThree",
        email="<EMAIL>",
        crm_system="wealthbox",
    )
    client_three.authorized_users.add(user)
    client_three.save()

    other_crm_client_one = Client.objects.create(
        name="Other Client One",
        organization=org,
        crm_id="clientOne",
        email="<EMAIL>",
        crm_system="other",
    )
    other_crm_client_one.authorized_users.add(user)
    other_crm_client_one.save()
    # Create an unauthorized client
    Client.objects.create(
        name="Unauthorized Client One",
        organization=org,
        crm_id="clientOne",
        email="<EMAIL>",
        crm_system="wealthbox",
    )

    mock_users_response = MagicMock()
    mock_users_response.status_code = 200
    mock_users_response.json.return_value = {
        "users": [{"id": "userOne", "email": "<EMAIL>"}],
        "meta": {"total_pages": 2},
    }

    second_mock_users_response = MagicMock()
    second_mock_users_response.status_code = 200
    second_mock_users_response.json.return_value = {
        "users": [
            {"id": "userTwo", "email": "<EMAIL>"},
            {"id": "userThree", "email": "<EMAIL>"},
        ],
        "meta": {"total_pages": 2},
    }

    start_time = django_timezone.now().replace(second=0, microsecond=0)
    end_time = start_time + datetime.timedelta(hours=1)

    mock_events_response = MagicMock(
        status_code=200,
        json=lambda: {
            "events": [
                {
                    "id": 1,
                    "title": "Event 1",
                    "starts_at": start_time.strftime("%Y-%m-%d %I:%M %p %z"),
                    "ends_at": end_time.strftime("%Y-%m-%d %I:%M %p %z"),
                    "all_day": False,
                    "description": "Description 1",
                    "location": "",
                    "invitees": [
                        {"id": "userOne", "name": "User One", "type": "User"},
                        {"id": "clientOne", "name": "Client One", "type": "Contact"},
                        {"id": "clientTwo", "name": "Client Two", "type": "Contact"},
                        {"id": "userTwo", "name": "User Two", "type": "User"},
                        {"id": "userThree", "name": "User Three", "type": "User"},
                        {"id": "clientThree", "name": "Client Three", "type": "Contact"},
                    ],
                }
            ],
            "meta": {"total_pages": 1},
        },
    )

    mock_requests_get.side_effect = [
        mock_users_response,
        second_mock_users_response,
        mock_events_response,
    ]

    events = Wealthbox().fetch_events(user, datetime.timedelta(days=1))
    assert events == [
        CalendarEvent(
            provider="wealthbox",
            id="1",
            user_specific_id="1",
            title="Event 1",
            start_time=start_time,
            end_time=end_time,
            all_day=False,
            body="Description 1",
            participants=[
                EventParticipant(id="userOne", name="User One", email_address="<EMAIL>"),
                EventParticipant(id="clientOne", name="Client One", email_address="<EMAIL>"),
                EventParticipant(id="clientTwo", name="Client Two", email_address="<EMAIL>"),
                EventParticipant(id="userTwo", name="User Two", email_address="<EMAIL>"),
                EventParticipant(id="userThree", name="User Three", email_address="<EMAIL>"),
                EventParticipant(id="clientThree", name="Client Three", email_address="<EMAIL>"),
            ],
            meeting_urls=[],
        )
    ]

    mock_requests_get.assert_has_calls(
        [
            call("https://api.wealthbox.com/v1/users", headers=ANY, params={"per_page": 100, "page": 1}),
            call("https://api.wealthbox.com/v1/users", headers=ANY, params={"per_page": 100, "page": 2}),
        ]
    )


@patch("requests.get")
def test_fetch_events_pagination(
    mock_requests_get: MagicMock, django_user_model: User, settings: SettingsWrapper
) -> None:
    settings.WEALTHBOX_BASE_URL = "https://api.wealthbox.com"
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="wealthbox",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    mock_users_response = MagicMock()
    mock_users_response.status_code = 200
    mock_users_response.json.return_value = {"users": [], "meta": {"total_pages": 1}}

    start_time = django_timezone.now().replace(second=0, microsecond=0)
    end_time = start_time + datetime.timedelta(hours=1)

    mock_events_response = MagicMock()
    mock_events_response.status_code = 200
    mock_events_response.json.side_effect = [
        {
            "events": [
                {
                    "id": 1,
                    "title": "Event 1",
                    "starts_at": start_time.strftime("%Y-%m-%d %I:%M %p %z"),
                    "ends_at": end_time.strftime("%Y-%m-%d %I:%M %p %z"),
                    "all_day": False,
                    "description": "Description 1",
                    "location": "",
                    "invitees": [],
                }
            ],
            "meta": {"total_pages": 3},
        },
        {
            "events": [
                {
                    "id": 2,
                    "title": "Event 2",
                    "starts_at": start_time.strftime("%Y-%m-%d %I:%M %p %z"),
                    "ends_at": end_time.strftime("%Y-%m-%d %I:%M %p %z"),
                    "all_day": False,
                    "description": "Description 2",
                    "location": "",
                    "invitees": [],
                }
            ],
            "meta": {"total_pages": 3},
        },
        {
            "events": [
                {
                    "id": 3,
                    "title": "Event 3",
                    "starts_at": start_time.strftime("%Y-%m-%d %I:%M %p %z"),
                    "ends_at": end_time.strftime("%Y-%m-%d %I:%M %p %z"),
                    "all_day": False,
                    "description": "Description 3",
                    "location": "",
                    "invitees": [],
                }
            ],
            "meta": {"total_pages": 3},
        },
    ]

    mock_requests_get.side_effect = [
        mock_users_response,
        mock_events_response,
        mock_events_response,
        mock_events_response,
    ]

    events = Wealthbox().fetch_events(user, datetime.timedelta(hours=1))
    assert events == [
        CalendarEvent(
            provider="wealthbox",
            id="1",
            user_specific_id="1",
            title="Event 1",
            start_time=start_time,
            end_time=end_time,
            all_day=False,
            body="Description 1",
            participants=[],
            meeting_urls=[],
        ),
        CalendarEvent(
            provider="wealthbox",
            id="2",
            user_specific_id="2",
            title="Event 2",
            start_time=start_time,
            end_time=end_time,
            all_day=False,
            body="Description 2",
            participants=[],
            meeting_urls=[],
        ),
        CalendarEvent(
            provider="wealthbox",
            id="3",
            user_specific_id="3",
            title="Event 3",
            start_time=start_time,
            end_time=end_time,
            all_day=False,
            body="Description 3",
            participants=[],
            meeting_urls=[],
        ),
    ]

    start_date_min = (start_time - datetime.timedelta(days=1)).date().isoformat()
    start_date_max = (end_time + datetime.timedelta(days=1)).date().isoformat()
    mock_requests_get.assert_has_calls(
        [
            call(
                "https://api.wealthbox.com/v1/events",
                headers=ANY,
                params={
                    "per_page": 100,
                    "page": 1,
                    "start_date_min": start_date_min,
                    "start_date_max": start_date_max,
                    "order": "asc",
                },
            ),
            call(
                "https://api.wealthbox.com/v1/events",
                headers=ANY,
                params={
                    "per_page": 100,
                    "page": 2,
                    "start_date_min": start_date_min,
                    "start_date_max": start_date_max,
                    "order": "asc",
                },
            ),
            call(
                "https://api.wealthbox.com/v1/events",
                headers=ANY,
                params={
                    "per_page": 100,
                    "page": 3,
                    "start_date_min": start_date_min,
                    "start_date_max": start_date_max,
                    "order": "asc",
                },
            ),
        ]
    )


def test_resolve_sync_targets_with_primary_client(test_user: User, test_client: Client) -> None:
    note = Note.objects.create(note_owner=test_user, client={"uuid": str(test_client.uuid)}, summary="Test Note")
    wealthbox = Wealthbox()

    sync_targets = wealthbox.resolve_sync_targets(note, test_user)

    expected_sync_targets = CRMSyncTargets(
        status=CRMSyncTargets.Status.FINAL,
        note_targets=[CRMSyncTarget(crm_id="123", type="Contact", name="Test Client")],
    )
    assert sync_targets == expected_sync_targets


def test_resolve_sync_targets_no_client(test_user: User) -> None:
    note = Note.objects.create(note_owner=test_user, summary="Test Note")
    wealthbox = Wealthbox()

    sync_targets = wealthbox.resolve_sync_targets(note, test_user)

    expected_sync_targets = CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])
    assert sync_targets == expected_sync_targets


def test_resolve_sync_targets_client_without_crm_id(test_user: User, test_organization: Organization) -> None:
    client_without_crm = Client.objects.create(name="Client No CRM", organization=test_organization)
    client_without_crm.authorized_users.add(test_user)
    client_without_crm.save()

    note = Note.objects.create(note_owner=test_user, client={"uuid": str(client_without_crm.uuid)}, summary="Test Note")
    wealthbox = Wealthbox()

    sync_targets = wealthbox.resolve_sync_targets(note, test_user)

    expected_sync_targets = CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])
    assert sync_targets == expected_sync_targets


def test_resolve_sync_targets_client_does_not_exist(test_user: User) -> None:
    fake_uuid = str(uuid.uuid4())
    note = Note.objects.create(note_owner=test_user, client={"uuid": fake_uuid}, summary="Test Note")
    wealthbox = Wealthbox()

    sync_targets = wealthbox.resolve_sync_targets(note, test_user)

    expected_sync_targets = CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])
    assert sync_targets == expected_sync_targets


def test_resolve_sync_targets_with_attendees(
    test_user: User, test_client: Client, test_organization: Organization
) -> None:
    note = Note.objects.create(note_owner=test_user, client={"uuid": str(test_client.uuid)}, summary="Test Note")

    attendee_client = Client.objects.create(name="Attendee Client", organization=test_organization, crm_id="456")
    attendee_client.authorized_users.add(test_user)
    attendee_client.save()
    Attendee.objects.create(note=note, client=attendee_client)

    wealthbox = Wealthbox()
    sync_targets = wealthbox.resolve_sync_targets(note, test_user)

    expected_sync_targets = CRMSyncTargets(
        status=CRMSyncTargets.Status.FINAL,
        note_targets=[
            CRMSyncTarget(crm_id="123", type="Contact", name="Test Client"),
            CRMSyncTarget(crm_id="456", type="Contact", name="Attendee Client"),
        ],
    )
    assert sync_targets == expected_sync_targets


def test_resolve_sync_targets_attendee_without_crm_id(
    test_user: User, test_client: Client, test_organization: Organization
) -> None:
    note = Note.objects.create(note_owner=test_user, client={"uuid": str(test_client.uuid)}, summary="Test Note")

    attendee_client = Client.objects.create(name="Attendee Client No CRM", organization=test_organization)
    attendee_client.authorized_users.add(test_user)
    attendee_client.save()
    Attendee.objects.create(note=note, client=attendee_client)

    wealthbox = Wealthbox()
    sync_targets = wealthbox.resolve_sync_targets(note, test_user)

    expected_sync_targets = CRMSyncTargets(
        status=CRMSyncTargets.Status.FINAL,
        note_targets=[CRMSyncTarget(crm_id="123", type="Contact", name="Test Client")],
    )
    assert sync_targets == expected_sync_targets


def test_resolve_sync_targets_duplicate_clients(test_user: User, test_client: Client) -> None:
    note = Note.objects.create(note_owner=test_user, client={"uuid": str(test_client.uuid)}, summary="Test Note")

    Attendee.objects.create(note=note, client=test_client)

    wealthbox = Wealthbox()
    sync_targets = wealthbox.resolve_sync_targets(note, test_user)

    expected_sync_targets = CRMSyncTargets(
        status=CRMSyncTargets.Status.FINAL,
        note_targets=[CRMSyncTarget(crm_id="123", type="Contact", name="Test Client")],
    )
    assert sync_targets == expected_sync_targets


def test_resolve_sync_targets_attendee_without_client(test_user: User, test_client: Client) -> None:
    note = Note.objects.create(note_owner=test_user, client={"uuid": str(test_client.uuid)}, summary="Test Note")

    # Add attendee without client
    Attendee.objects.create(note=note, client=None)

    wealthbox = Wealthbox()
    sync_targets = wealthbox.resolve_sync_targets(note, test_user)

    expected_sync_targets = CRMSyncTargets(
        status=CRMSyncTargets.Status.FINAL,
        note_targets=[CRMSyncTarget(crm_id="123", type="Contact", name="Test Client")],
    )
    assert sync_targets == expected_sync_targets


def test_resolve_sync_targets_only_attendees(
    test_user: User, test_organization: Organization, test_client: Client
) -> None:
    note = Note.objects.create(note_owner=test_user, summary="Test Note")

    attendee_client = Client.objects.create(name="Attendee Client", organization=test_organization, crm_id="456")
    attendee_client.authorized_users.add(test_user)
    attendee_client.save()
    Attendee.objects.create(note=note, client=attendee_client)
    Attendee.objects.create(note=note, client=test_client)

    wealthbox = Wealthbox()
    sync_targets = wealthbox.resolve_sync_targets(note, test_user)

    expected_sync_targets = CRMSyncTargets(
        status=CRMSyncTargets.Status.FINAL,
        note_targets=[
            CRMSyncTarget(crm_id="456", type="Contact", name="Attendee Client"),
            CRMSyncTarget(crm_id="123", type="Contact", name="Test Client"),
        ],
    )
    assert sync_targets == expected_sync_targets


@patch("requests.get")
def test_workflows_for_user(mock_requests: MagicMock, test_user: User, oauth_credentials: OAuthCredentials) -> None:
    mock_requests.return_value.status_code = 200
    mock_requests.return_value.json.return_value = {
        "workflow_templates": [
            {
                "id": 1,
                "creator": 1,
                "created_at": "2015-05-24 10:00 AM -0400",
                "updated_at": "2015-10-12 11:30 PM -0400",
                "name": "New Client Onboarding Process",
                "sequential": True,
                "description": "Onboarding workflow for our clients...",
                "description_html": "<div>Onboarding workflow for our clients...</div>",
                "shared": True,
                "ready": True,
                "workflow_milestones": [{"id": "abcdef", "name": "Onboarding"}],
                "workflow_steps": [
                    {
                        "id": 1,
                        "creator": 1,
                        "created_at": "2015-05-24 10:00 AM -0400",
                        "updated_at": "2015-10-12 11:30 PM -0400",
                        "name": "Schedule discover meeting",
                        "description": "In this meeting, we will...",
                        "description_html": "<div>In this meeting, we will...</div>",
                        "workflow": 1,
                        "assigned_to": 1,
                        "assigned_to_team": 10,
                        "due_based_on": "default",
                        "due_date_set": True,
                        "due_later": "2 days later at 5:00 PM",
                        "due_date": "2024-03-06 11:30 AM -0500",
                        "priority": "None",
                        "workflow_outcomes": [
                            {
                                "id": 1,
                                "workflow_step_id": 1,
                                "name": "Meeting scheduled with client",
                                "action": "Go to Step",
                                "go_to_step_id": 2,
                                "due_date_set": False,
                                "due_later": "2 days later at 5:00 PM",
                                "created_at": "2015-05-24 10:00 AM -0400",
                                "updated_at": "2015-10-12 11:30 PM -0400",
                            }
                        ],
                        "workflow_milestone_id": 1,
                        "subtasks": [
                            {
                                "id": 1,
                                "creator": 1,
                                "created_at": "2015-05-24 10:00 AM -0400",
                                "updated_at": "2015-10-12 11:30 PM -0400",
                                "due_date": "2024-10-03 12:00 PM -0400",
                                "name": "Call with client",
                                "frame": "specific",
                                "complete": True,
                                "repeats": True,
                                "description": "Review meeting for Kevin...",
                                "description_html": "<p>Review meeting for Kevin...</p>",
                                "priority": "None",
                                "due_later": "2 days later at 5:00 PM",
                                "completer": 1,
                                "category": "null",
                                "linked_to": [{"id": 1, "type": "Contact", "name": "Kevin Anderson"}],
                                "parent": [{"id": 1, "type": "Task"}],
                            }
                        ],
                    }
                ],
                "kind": "Contact",
                "visible_to": "Everyone",
            }
        ]
    }

    wealthbox = Wealthbox()
    result = wealthbox.fetch_workflows_for_user(test_user)
    assert result
    assert len(result) == 1
    assert result[0].crm_id == "1"
    assert result[0].name == "New Client Onboarding Process"


@patch("requests.get")
def test_workflows_for_user_bad_request(
    mock_requests: MagicMock, test_user: User, oauth_credentials: OAuthCredentials
) -> None:
    mock_requests.return_value.status_code = 500

    wealthbox = Wealthbox()
    result = wealthbox.fetch_workflows_for_user(test_user)
    assert not result


@patch("requests.get")
def test_get_workflows_for_user_pagination_success(
    mock_requests: MagicMock, test_user: User, oauth_credentials: OAuthCredentials
) -> None:
    mock_requests.return_value.status_code = 200
    mock_requests.return_value.json.return_value = {
        "workflow_templates": [
            {
                "id": 1,
                "creator": 1,
                "created_at": "2015-05-24 10:00 AM -0400",
                "updated_at": "2015-10-12 11:30 PM -0400",
                "name": "New Client Onboarding Process",
                "sequential": True,
                "description": "Onboarding workflow for our clients...",
                "description_html": "<div>Onboarding workflow for our clients...</div>",
                "shared": True,
                "ready": True,
                "workflow_milestones": [{"id": "abcdef", "name": "Onboarding"}],
                "workflow_steps": [
                    {
                        "id": 1,
                        "creator": 1,
                        "created_at": "2015-05-24 10:00 AM -0400",
                        "updated_at": "2015-10-12 11:30 PM -0400",
                        "name": "Schedule discover meeting",
                        "description": "In this meeting, we will...",
                        "description_html": "<div>In this meeting, we will...</div>",
                        "workflow": 1,
                        "assigned_to": 1,
                        "assigned_to_team": 10,
                        "due_based_on": "default",
                        "due_date_set": True,
                        "due_later": "2 days later at 5:00 PM",
                        "due_date": "2024-03-06 11:30 AM -0500",
                        "priority": "None",
                        "workflow_outcomes": [
                            {
                                "id": 1,
                                "workflow_step_id": 1,
                                "name": "Meeting scheduled with client",
                                "action": "Go to Step",
                                "go_to_step_id": 2,
                                "due_date_set": False,
                                "due_later": "2 days later at 5:00 PM",
                                "created_at": "2015-05-24 10:00 AM -0400",
                                "updated_at": "2015-10-12 11:30 PM -0400",
                            }
                        ],
                        "workflow_milestone_id": 1,
                        "subtasks": [
                            {
                                "id": 1,
                                "creator": 1,
                                "created_at": "2015-05-24 10:00 AM -0400",
                                "updated_at": "2015-10-12 11:30 PM -0400",
                                "due_date": "2024-10-03 12:00 PM -0400",
                                "name": "Call with client",
                                "frame": "specific",
                                "complete": True,
                                "repeats": True,
                                "description": "Review meeting for Kevin...",
                                "description_html": "<p>Review meeting for Kevin...</p>",
                                "priority": "None",
                                "due_later": "2 days later at 5:00 PM",
                                "completer": 1,
                                "category": "null",
                                "linked_to": [{"id": 1, "type": "Contact", "name": "Kevin Anderson"}],
                                "parent": [{"id": 1, "type": "Task"}],
                            }
                        ],
                    }
                ],
                "kind": "Contact",
                "visible_to": "Everyone",
            }
        ],
        "meta": {"total_pages": 2},
    }

    wealthbox = Wealthbox()
    result = wealthbox._get_workflows_for_user("test access token")
    assert result
    assert len(result) == 2
    assert result[0].crm_id == "1"
    assert result[0].name == "New Client Onboarding Process"


@patch("requests.get")
def test_get_workflows_for_user_pagination_partial_failure(
    mock_requests: MagicMock, test_user: User, oauth_credentials: OAuthCredentials
) -> None:
    mock_requests.return_value.status_code = 200
    mock_requests.return_value.json.side_effect = [
        {
            "workflow_templates": [
                {
                    "id": 1,
                    "creator": 1,
                    "created_at": "2015-05-24 10:00 AM -0400",
                    "updated_at": "2015-10-12 11:30 PM -0400",
                    "name": "New Client Onboarding Process",
                    "sequential": True,
                    "description": "Onboarding workflow for our clients...",
                    "description_html": "<div>Onboarding workflow for our clients...</div>",
                    "shared": True,
                    "ready": True,
                    "workflow_milestones": [{"id": "abcdef", "name": "Onboarding"}],
                    "workflow_steps": [
                        {
                            "id": 1,
                            "creator": 1,
                            "created_at": "2015-05-24 10:00 AM -0400",
                            "updated_at": "2015-10-12 11:30 PM -0400",
                            "name": "Schedule discover meeting",
                            "description": "In this meeting, we will...",
                            "description_html": "<div>In this meeting, we will...</div>",
                            "workflow": 1,
                            "assigned_to": 1,
                            "assigned_to_team": 10,
                            "due_based_on": "default",
                            "due_date_set": True,
                            "due_later": "2 days later at 5:00 PM",
                            "due_date": "2024-03-06 11:30 AM -0500",
                            "priority": "None",
                            "workflow_outcomes": [
                                {
                                    "id": 1,
                                    "workflow_step_id": 1,
                                    "name": "Meeting scheduled with client",
                                    "action": "Go to Step",
                                    "go_to_step_id": 2,
                                    "due_date_set": False,
                                    "due_later": "2 days later at 5:00 PM",
                                    "created_at": "2015-05-24 10:00 AM -0400",
                                    "updated_at": "2015-10-12 11:30 PM -0400",
                                }
                            ],
                            "workflow_milestone_id": 1,
                            "subtasks": [
                                {
                                    "id": 1,
                                    "creator": 1,
                                    "created_at": "2015-05-24 10:00 AM -0400",
                                    "updated_at": "2015-10-12 11:30 PM -0400",
                                    "due_date": "2024-10-03 12:00 PM -0400",
                                    "name": "Call with client",
                                    "frame": "specific",
                                    "complete": True,
                                    "repeats": True,
                                    "description": "Review meeting for Kevin...",
                                    "description_html": "<p>Review meeting for Kevin...</p>",
                                    "priority": "None",
                                    "due_later": "2 days later at 5:00 PM",
                                    "completer": 1,
                                    "category": "null",
                                    "linked_to": [{"id": 1, "type": "Contact", "name": "Kevin Anderson"}],
                                    "parent": [{"id": 1, "type": "Task"}],
                                }
                            ],
                        }
                    ],
                    "kind": "Contact",
                    "visible_to": "Everyone",
                }
            ],
            "meta": {"total_pages": 2},
        },
        {},
    ]

    wealthbox = Wealthbox()
    result = wealthbox._get_workflows_for_user("test access token")
    assert result
    assert len(result) == 1
    assert result[0].crm_id == "1"
    assert result[0].name == "New Client Onboarding Process"


@patch("requests.get")
def test_get_workflows_for_user_exception(
    mock_requests: MagicMock, test_user: User, oauth_credentials: OAuthCredentials
) -> None:
    mock_requests.return_value.status_code = 200
    mock_requests.return_value.json.return_value = {"workflow_templates": [{"name": 10, "id": 10}]}

    wealthbox = Wealthbox()
    with pytest.raises(Exception) as e:
        _ = wealthbox._get_workflows_for_user("test access token")


@patch("requests.get")
def test_list_users_no_organization(mock_get: MagicMock, test_user_without_org: User) -> None:
    wealthbox = Wealthbox()
    wealthbox.oauth = MagicMock()
    result = wealthbox.list_users(test_user_without_org)
    assert result == []
    mock_get.assert_not_called()


@patch("requests.get")
def test_list_users_no_access_token(mock_get: MagicMock, test_user: User) -> None:
    wealthbox = Wealthbox()
    wealthbox.oauth = MagicMock()
    wealthbox.oauth.get_access_token.return_value = None
    result = wealthbox.list_users(test_user)
    assert result == []
    mock_get.assert_not_called()


@patch("requests.get")
def test_list_users_empty_wb_users(mock_get: MagicMock, test_user: User) -> None:
    wealthbox = Wealthbox()
    wealthbox.oauth = MagicMock()
    wealthbox.oauth.get_access_token.return_value = "token"
    mock_users_response = MagicMock()
    mock_users_response.status_code = 200
    mock_users_response.json.return_value = {"users": [], "meta": {"total_pages": 1}}

    mock_get.return_value = mock_users_response
    result = wealthbox.list_users(test_user)
    assert result == []
    mock_get.assert_has_calls(
        [
            call(
                "/v1/users",
                headers=ANY,
                params={"per_page": 100, "page": 1},
            )
        ]
    )


@patch("requests.get")
def test_list_users_success(mock_get: MagicMock, test_user: User, test_organization: Organization) -> None:
    wealthbox = Wealthbox()
    wealthbox.oauth = MagicMock()
    wealthbox.oauth.get_access_token.return_value = "token"
    # Wealthbox returns two users, one matches a zeplyn user, one does not
    mock_users_response = MagicMock()

    mock_users_response.status_code = 200
    mock_users_response.json.return_value = {
        "users": [
            {"id": "wb1", "email": "<EMAIL>", "name": "WB User 1"},
            {"id": "wb2", "email": "<EMAIL>", "name": "WB User 2"},
        ],
        "meta": {"total_pages": 1},
    }
    mock_get.return_value = mock_users_response

    # Create a matching zeplyn user
    matched_user = User.objects.create(
        email="<EMAIL>",
        organization=test_organization,
        name="Matched User",
        username="matched_user",
    )
    result = wealthbox.list_users(test_user)

    def expected() -> list[CRMUser]:
        return [
            CRMUser(
                name="WB User 1",
                crm_id="wb1",
                organization=ZeplynOrganization(uuid=str(test_organization.uuid), name=test_organization.name),
                zeplyn_user=ZeplynUser(uuid=str(matched_user.uuid), email=matched_user.email, name=matched_user.name),
            ),
            CRMUser(
                name="WB User 2",
                crm_id="wb2",
                organization=ZeplynOrganization(uuid=str(test_organization.uuid), name=test_organization.name),
                zeplyn_user=None,
            ),
        ]

    assert len(result) == 2
    mock_get.assert_has_calls(
        [
            call(
                "/v1/users",
                headers=ANY,
                params={"per_page": 100, "page": 1},
            )
        ]
    )
    assert result == expected()


@patch("requests.get")
def test_list_users_exception_handling(mock_get: MagicMock, test_user: User) -> None:
    wealthbox = Wealthbox()
    wealthbox.oauth = MagicMock()
    wealthbox.oauth.get_access_token.return_value = "token"

    mock_users_response = MagicMock()
    mock_users_response.status_code = 200
    mock_users_response.json.return_value = {"users": [], "meta": {"total_pages": 1}}
    mock_users_response.side_effect = Exception("API error")

    mock_get.return_value = mock_users_response
    result = wealthbox.list_users(test_user)
    assert result == []
    mock_get.assert_has_calls(
        [
            call(
                "/v1/users",
                headers=ANY,
                params={"per_page": 100, "page": 1},
            )
        ]
    )


@patch("requests.get")
def test_list_users_exception_logs_and_returns_empty(mock_get: MagicMock, test_user: User) -> None:
    wealthbox = Wealthbox()
    wealthbox.oauth = MagicMock()
    wealthbox.oauth.get_access_token.return_value = "token"

    # Simulate an exception during API call
    mock_get.side_effect = Exception("API error")

    with patch("logging.error") as mock_log_error:
        result = wealthbox.list_users(test_user)
        assert result == []
        mock_log_error.assert_called()
        assert "Error listing Wealthbox CRM users for Test Org" in mock_log_error.call_args[0][0]
