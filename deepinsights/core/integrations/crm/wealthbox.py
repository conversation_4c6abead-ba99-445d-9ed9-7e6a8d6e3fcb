import json
import logging
from datetime import datetime, timedelta
from typing import Any, Mapping, Optional

import requests
from django.conf import settings
from django.utils import timezone
from more_itertools import first_true

from deepinsights.core.integrations.calendar.calendar_data_parser import find_urls
from deepinsights.core.integrations.calendar.calendar_models import Calendar<PERSON>vent, EventParticipant
from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.crm_models import (
    CRMAccount,
    CRMNote,
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTarget,
    CRMSyncTargets,
    CRMUser,
    CRMWorkflow,
    ZeplynOrganization,
    ZeplynUser,
)
from deepinsights.core.integrations.oauth.wealthbox import WealthBoxOAuth
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingData, StructuredMeetingDataTemplate
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User


class Wealthbox(CrmBase):
    def __init__(self) -> None:
        self.oauth = WealthBoxOAuth()
        self.base_url = settings.WEALTHBOX_BASE_URL

    def __get_users(self, access_token: str) -> list[dict[str, Any]]:
        try:
            users: list[dict[str, Any]] = []
            page: int = 1
            while True:
                url = f"{self.base_url}/v1/users"
                headers = {
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                }
                params = {
                    "per_page": 100,
                    "page": page,
                }

                response = requests.get(url, headers=headers, params=params)
                if response.status_code == 200:
                    data = response.json()
                    users.extend(data.get("users", []))
                    if data.get("meta", {}).get("total_pages") > page:
                        page += 1
                    else:
                        return users
                else:
                    logging.info("Error getting users from Wealthbox: %s %s", response.json(), response.status_code)
                    return users

        except Exception as e:
            raise Exception(f"Error getting users from Wealthbox: {e}")

    def __get_contacts(self, access_token: str, account_name_filter: str | None = None) -> list[dict[str, Any]]:
        try:
            contacts: list[dict[str, Any]] = []
            page: int = 1
            while True:
                url = f"{self.base_url}/v1/contacts"
                headers = {
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                }
                params = {
                    "per_page": 100,
                    "type": "person",
                    "page": page,
                }
                if account_name_filter:
                    params["name"] = account_name_filter

                response = requests.get(url, headers=headers, params=params)  # type: ignore[arg-type]
                if response.status_code == 200:
                    data = response.json()
                    contacts.extend(data.get("contacts", []))
                    if data.get("meta", {}).get("total_pages") > page:
                        page += 1
                    else:
                        return contacts
                else:
                    logging.info("Error getting contacts from Wealthbox: %s %s", response.json(), response.status_code)
                    return contacts
        except Exception as e:
            raise Exception(f"Error getting contacts from Wealthbox: {e}")

    def __get_email(self, contact: dict[str, Any]) -> str | None:
        if not (emails := contact.get("email_addresses")):
            return None
        email = first_true(emails, emails[0], lambda p: p.get("principal"))
        return email.get("address")  # type: ignore[no-any-return]

    def __get_phone_number(self, contact: dict[str, Any]) -> str | None:
        if not (phone_numbers := contact.get("phone_numbers")):
            return None
        phone_number = first_true(phone_numbers, phone_numbers[0], lambda p: p.get("principal"))
        return phone_number.get("address")  # type: ignore[no-any-return]

    def __get_date(self, datetime_string: str) -> datetime:
        """
        Parses a datetime string and returns a datetime object
        """
        date_format = "%Y-%m-%d %I:%M %p %z"
        return datetime.strptime(datetime_string, date_format)

    def __parse_notes(
        self, notes: list[dict[str, Any]], lookback_interval: timedelta, user_crm_account_id: str | None, client: Client
    ) -> tuple[list[CRMNote], bool]:
        """
        Parses notes from Wealthbox and filters them based on the client's CRM ID and not_before_days
        """
        not_before_days_obj = timezone.now() - lookback_interval
        parsed_notes: list[CRMNote] = []
        for note in notes:
            if self.__get_date(note["created_at"]) < not_before_days_obj:
                logging.debug(
                    "Returning because the note is older than the specified date: %s < %s",
                    self.__get_date(note["created_at"]),
                    not_before_days_obj,
                )
                return parsed_notes, False
            linked_to = note["linked_to"]
            if not linked_to:
                continue
            crm_ids = [linked_client["id"] for linked_client in note["linked_to"]]
            if int(client.crm_id) in crm_ids:  # type: ignore[arg-type]
                parsed_notes.append(
                    CRMNote(
                        crm_system="wealthbox",
                        crm_id=str(note["id"]),
                        content=note["content"],
                        created_at=self.__get_date(note["created_at"]),
                        web_link=f"https://app.crmworkspace.com/{user_crm_account_id}/status_updates/{note['id']}"
                        if user_crm_account_id
                        else None,
                    )
                )
        return parsed_notes, True

    def __get_notes(self, access_token: str, lookback_interval: timedelta, client: Client) -> list[CRMNote]:
        """
        Fetches notes from Wealthbox using pagination and filters them
        """
        try:
            notes: list[CRMNote] = []
            page: int = 1
            continue_fetching: bool = True
            user_crm_account_id = self.__get_user_account_by_self(access_token)
            while continue_fetching:
                url = f"{self.base_url}/v1/notes"
                headers = {
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                }
                params: dict[str, int | str] = {"per_page": 100, "page": page, "order": "created"}

                response = requests.get(url, headers=headers, params=params)
                if response.status_code == 200:
                    data = response.json()
                    logging.info("Successfully fetched notes from Wealthbox: %s", data.get("status_updates"))
                    paged_notes, fetch_status = self.__parse_notes(
                        data.get("status_updates", []), lookback_interval, user_crm_account_id, client
                    )
                    continue_fetching = fetch_status
                    notes.extend(paged_notes)
                    if data.get("meta", {}).get("total_pages") > page:
                        page += 1
                    else:
                        logging.info("Successfully parsed notes from Wealthbox: %s", notes)
                        return notes
                else:
                    logging.info("Error getting notes from Wealthbox: %s %s", data, response.status_code)
                    return notes
            return notes
        except Exception as e:
            raise Exception(f"Error getting notes from Wealthbox: {e}")

    def fetch_notes_for_client(self, user: User, client: Client, lookback_interval: timedelta) -> list[CRMNote]:
        """
        Fetches notes from Wealthbox for a given client
        """
        try:
            access_token = self.oauth.get_access_token(user)
            if not access_token:
                logging.error("Error getting Wealthbox credentials for user: %s", user.uuid)
                return []

            return self.__get_notes(access_token, lookback_interval, client)

        except Exception as e:
            logging.error("Error getting Wealthbox notes for user: %s exception %s", user.uuid, e, exc_info=True)
            return []

    def get_accounts_by_owner_email_and_name(self, owner_email: str, account_name_filter=None) -> list[CRMAccount]:  # type: ignore[no-untyped-def]
        try:
            # Get the user's Wealthbox credentials
            user = User.objects.get(email=owner_email)
            oauth_credentials = self.oauth.get_access_token(user)
            if not oauth_credentials:
                logging.error("Error getting Wealthbox credentials for user: %s", user.uuid)
                return []

            contacts = self.__get_contacts(oauth_credentials, account_name_filter)
            logging.info("Successfully fetched Wealthbox contacts for user: %s", user.uuid)
            parsed_contacts = [
                CRMAccount(
                    first_name=contact.get("first_name"),
                    last_name=contact.get("last_name"),
                    email=self.__get_email(contact),
                    phone_number=self.__get_phone_number(contact),
                    crm_id=str(contact.get("id")),
                    client_type=contact.get("type"),
                    crm_system="wealthbox",
                    name=contact.get("name"),
                )
                for contact in contacts
            ]
            logging.info("Successfully parsed Wealthbox contacts for user: %s", user.uuid)
            return parsed_contacts
        except Exception as e:
            logging.error("Error getting Wealthbox contacts for user: %s, exception %s", user.uuid, e, exc_info=True)
            return []

    def __get_user_id_by_teams(self, user_email: str, access_token: str) -> str | None:
        """
        Getting users of the team and check if the user email is in the team, if yes return the user id
        """
        try:
            url = f"{self.base_url}/v1/teams"
            headers = {"Content-Type": "application/json", "Authorization": f"Bearer {access_token}"}
            response = requests.get(url, headers=headers)
            teams = response.json().get("teams", [])

            wealthbox_user_id = next(
                (
                    member.get("id")
                    for team in teams
                    for member in team.get("members", [])
                    if member.get("email") == user_email
                ),
                None,
            )
            return wealthbox_user_id

        except Exception as e:
            logging.error(
                "Error getting user id by email from Wealthbox for user email: %s, exception %s",
                user_email,
                e,
                exc_info=True,
            )
            raise Exception(f"Error getting user id by email from Wealthbox: {e}")

    def __get_user_id_by_self(self, access_token: str) -> str | None:
        """
        Getting user id by self if the user is not in the team
        """
        try:
            url = f"{self.base_url}/v1/me"
            headers = {"Content-Type": "application/json", "Authorization": f"Bearer {access_token}"}
            response = requests.get(url, headers=headers)
            return response.json().get("current_user").get("id") if response.status_code == 200 else None
        except Exception as e:
            logging.error("Error getting user id by self from Wealthbox: %s", e, exc_info=True)
            raise Exception(f"Error getting user id by self from Wealthbox: {e}")

    def get_user_account_by_self(self, user: User) -> str | None:
        try:
            access_token = self.oauth.get_access_token(user)
            if not access_token:
                logging.error("Error getting Wealthbox credentials for user: %s", user.uuid)
                return None
            return self.__get_user_account_by_self(access_token)
        except Exception as e:
            logging.error("Error getting user account by self from Wealthbox: %s", e, exc_info=True)
            raise Exception(f"Error getting user account by self from Wealthbox: {e}")

    def __get_user_account_by_self(self, access_token: str) -> str | None:
        """
        Getting user id by self if the user is not in the team
        """
        try:
            url = f"{self.base_url}/v1/me"
            headers = {"Content-Type": "application/json", "Authorization": f"Bearer {access_token}"}
            response = requests.get(url, headers=headers)
            return response.json().get("current_user").get("account") if response.status_code == 200 else None
        except Exception as e:
            logging.error("Error getting user id by self from Wealthbox: %s", e, exc_info=True)
            raise Exception(f"Error getting user id by self from Wealthbox: {e}")

    def get_assignee_id_by_email(self, user_email: str, access_token: str) -> str | None:
        try:
            wealthbox_user_id = self.__get_user_id_by_teams(user_email, access_token)
            if wealthbox_user_id:
                logging.info("Successfully fetched user id by email from Wealthbox team for user: %s", user_email)
                return wealthbox_user_id
            else:
                return self.__get_user_id_by_self(access_token)
        except Exception as e:
            raise Exception(f"Error getting user id by email from Wealthbox: {e}")

    def resolve_sync_targets(
        self, note: Note, user: User, selected_sync_targets: CRMSyncTargets | None = None
    ) -> CRMSyncTargets:
        sync_targets: list[CRMSyncTarget] = []

        # Check if note has a primary client
        if note.client and note.client.get("uuid"):
            try:
                client = Client.objects.get(uuid=note.client.get("uuid"))
                if client.crm_id:
                    sync_targets.append(
                        CRMSyncTarget(
                            crm_id=client.crm_id,
                            type="Contact",
                            name=client.name,
                        )
                    )
            except Client.DoesNotExist:
                pass

        # Check attendees for additional clients
        for attendee in note.attendees.filter(client__isnull=False).order_by("created"):
            if not attendee.client or not attendee.client.crm_id:
                continue
            # Avoid duplicates if the primary client is also in attendees
            if any(target.crm_id == attendee.client.crm_id for target in sync_targets):
                continue
            sync_targets.append(CRMSyncTarget(crm_id=attendee.client.crm_id, type="Contact", name=attendee.client.name))

        return CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL if sync_targets else CRMSyncTargets.Status.CLIENTS_REQUIRED,
            note_targets=sync_targets,
        )

    def add_note(
        self,
        access_token: str,
        note: Note,
        clients: list[dict[str, Any]],
        sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = None,
    ) -> None:
        try:
            url = f"{self.base_url}/v1/notes"
            headers = {"Content-Type": "application/json", "Authorization": f"Bearer {access_token}"}
            data = {
                "content": note.get_summary_for_crm(use_html_formatting=False, sync_items=sync_items),
                "linked_to": clients,
                "visible_to": "Everyone",
            }

            response = requests.post(url, headers=headers, data=json.dumps(data))
            if response.status_code == 200:
                logging.info("Successfully added note to Wealthbox: %s", note.uuid)
            else:
                logging.error(
                    "Error adding note %s to Wealthbox: %s with status %s",
                    note.uuid,
                    response.json(),
                    response.status_code,
                    exc_info=True,
                )
        except Exception as e:
            logging.error("Error adding note %s to Wealthbox: %s", note.uuid, e, exc_info=True)

    def add_task(
        self, access_token: str, task: Task, clients: list[dict[str, Any]], asignee_wealthbox_id: str | None
    ) -> None:
        try:
            url = f"{self.base_url}/v1/tasks"
            headers = {"Content-Type": "application/json", "Authorization": f"Bearer {access_token}"}
            data = {
                "name": task.task_title,
                "due_date": (task.due_date or timezone.now()).isoformat(),
                "complete": task.completed,
                "linked_to": clients,
                "assigned_to": asignee_wealthbox_id,
            }

            response = requests.post(url, headers=headers, data=json.dumps(data))
            if response.status_code == 200:
                logging.info("Successfully added task to Wealthbox: %s", task.uuid)
            else:
                logging.error(
                    "Error adding task %s to Wealthbox: %s with status %s",
                    task.uuid,
                    response.json(),
                    response.status_code,
                    exc_info=True,
                )
        except Exception as e:
            logging.error("Error adding task %s to Wealthbox: %s", task.uuid, e, exc_info=True)

    _life_event_map = {
        "birth_or_adoption": "Bento - Birth or Adoption",
        "saving_and_investing": "Bento - Saving and Investing",
        "off_to_college": "Bento - Off to College",
        "age_of_majority": "Bento - Age of Majority",
        "first_job": "Bento - First Job",
        "paying_off_student_debt": "Bento - Paying Off Student Debt",
        "getting_divorced": "Bento - Getting Divorced",
        "getting_married": "Bento - Getting Married",
        "buying_a_home": "Bento - Buying a Home",
        "selling_a_home": "Bento - Selling a Home",
        "receiving_a_windfall": "Bento - Receiving a Windfall",
        "moving_to_a_new_state": "Bento - Moving to a New State",
    }

    # Returns a list of Bento life event tags derived from any life event structured data associated
    # with the note.
    #
    # If no life event structured data is found, returns None.
    def _bento_tags(self, note: Note) -> list[str] | None:
        life_event_datas = StructuredMeetingData.objects.filter(
            note=note,
            kind=StructuredMeetingDataTemplate.Kind.BENTO_LIFE_EVENTS,
            status=StructuredMeetingData.Status.COMPLETED,
        ).order_by("-created")
        if not (life_event_data := life_event_datas.first()):
            logging.info("No life event data found for note %s", note.uuid)
            return None
        if life_event_datas.count() > 1:
            logging.warning("Multiple life event datas found for note %s. Using the newest one.", note.uuid)

        tags = []
        for data in life_event_data.data.get("review_entries", []):
            if data.get("discussed") and (tag := self._life_event_map.get(data.get("id"))):
                tags.append(tag)
        return tags

    # Given a client ID and a list of tags, adds the provided tags to that client.
    def _add_tags_to_client(self, access_token: str, crm_id: str, tags: list[str]) -> None:
        if not tags:
            logging.error("No tags to add to Wealthbox client. Skipping.", exc_info=True)
            return
        try:
            if not (existing_client := self.__get_client_by_id(crm_id, access_token)):
                raise Exception("Client with ID %s not found in Wealthbox", crm_id)
            existing_tags = [t.get("name") for t in existing_client.get("tags", [])]
            url = f"{self.base_url}/v1/contacts/{crm_id}"
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
            }
            updated_tags = list(dict.fromkeys(existing_tags + tags))
            response = requests.patch(url, headers=headers, data=json.dumps({"tags": updated_tags}))
            if response.status_code == 200:
                logging.info("Successfully added tags to Wealthbox client: %s", crm_id)
            else:
                logging.error(
                    "Error adding tags to Wealthbox client %s: %s with status %s",
                    crm_id,
                    response.json(),
                    response.status_code,
                    exc_info=True,
                )
        except Exception as e:
            logging.error("Error adding tags to Wealthbox client %s", crm_id, exc_info=True)

    def add_interaction_with_client(
        self,
        note: Note,
        sync_targets: CRMSyncTargets,
        sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = None,
    ) -> None:
        try:
            if not (user := note.note_owner):
                raise Exception("Note owner not found")
            if not (access_token := self.oauth.get_access_token(user)):
                raise Exception("Could not generate Wealthbox access token")
            wealthbox_note_owner_id = self.get_assignee_id_by_email(user.email, access_token)

            clients = [
                {"id": target.crm_id, "type": target.type, "name": target.name}
                for target in sync_targets.note_targets or []
            ]
            if not clients:
                raise Exception("No clients provided for Wealthbox interaction.")
            self.add_note(access_token, note, clients, sync_items)
            if note.should_include_section(sync_items, CRMSyncSection.TASKS):
                tasks = note.get_tasks_to_include_crm_sync(sync_items=sync_items)
                for task in tasks:
                    assignee_id = (
                        self.get_assignee_id_by_email(task.assignee.email, access_token) if task.assignee else None
                    )
                    self.add_task(access_token, task, clients, assignee_id or wealthbox_note_owner_id)
                    logging.info("Successfully added task %s to Wealthbox for note %s", task.uuid, note.uuid)
            if not (tags := self._bento_tags(note)):
                return
            for id in filter(lambda x: len(x) > 0, [c.get("id", "") for c in clients]):
                self._add_tags_to_client(access_token, id, tags)

        except Exception as e:
            logging.error(
                "Error adding interaction with client: user: %s, note: %s",
                user.uuid if user else "<no user>",
                note.uuid,
                exc_info=True,
            )
            raise

    def __get_client_by_id(self, crm_id: str, access_token: str) -> dict[str, Any] | None:
        try:
            url = f"{self.base_url}/v1/contacts/{crm_id}"
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
            }

            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                logging.info("Successfully fetched Wealthbox client for crm_id: %s %s", crm_id, response.json())
                return response.json()  # type: ignore[no-any-return]
            else:
                logging.info("Error getting contacts from Wealthbox: %s %s", response.json(), response.status_code)
                return None
        except Exception as e:
            raise Exception(f"Error getting contacts from Wealthbox: {e}")

    def __get_household_info(self, household: dict[str, Any], access_token: str) -> dict[str, Any] | None:
        household_info: dict[str, Any] = {}
        try:
            household_info["name"] = household.get("name")
            household_members: list[dict[str, Any]] = []
            for member in household.get("members", []):
                member_basic_info = self.__get_client_by_id(member.get("id"), access_token=access_token)
                if member_basic_info:
                    member_household = member_basic_info.get("household")
                    if member_household:
                        member_title = member_household.get("title")
                    household_members.append(
                        {
                            "gross_annual_income": member_basic_info.get("gross_annual_income"),
                            "assets": member_basic_info.get("assets"),
                            "liabilities": member_basic_info.get("liabilities"),
                            "non_liquid_assets": member_basic_info.get("non_liquid_assets"),
                            "first_name": member_basic_info.get("first_name"),
                            "last_name": member_basic_info.get("last_name"),
                            "title": member_basic_info.get("title"),
                            "type": member_basic_info.get("type"),
                            "birth_date": member_basic_info.get("birth_date"),
                            "title": member_title,
                        }
                    )
            household_info["members"] = household_members
            return household_info

        except Exception as e:
            logging.error("Error getting Wealthbox household", exc_info=True)
            return None

    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        try:
            access_token = self.oauth.get_access_token(user)
            logging.info("Successfully fetched Wealthbox credentials for user: %s %s", user.uuid, client.crm_id)
            client_info = self.__get_client_by_id(
                client.crm_id,  # type: ignore[arg-type]
                access_token,  # type: ignore[arg-type]
            )

            if not client_info:
                return None
            logging.info("Successfully fetched Wealthbox client for user: %s %s", user.uuid, client_info)
            parsed_client_info: dict[str, Any] = {
                "gross_annual_income": client_info.get("gross_annual_income"),
                "assets": client_info.get("assets"),
                "liabilities": client_info.get("liabilities"),
                "non_liquid_assets": client_info.get("non_liquid_assets"),
            }
            if include_household:
                if household_info := client_info.get("household"):
                    logging.info("Fetching Wealthbox household for user: %s", user.uuid)
                    parsed_client_info["household"] = self.__get_household_info(household_info, access_token)  # type: ignore[arg-type]
                else:
                    parsed_client_info["household"] = None

            logging.info("Successfully fetched Wealthbox client for user: %s", user.uuid)
            return parsed_client_info
        except Exception as e:
            logging.error("Error getting Wealthbox client for user: %s, exception %s", user.uuid, e, exc_info=True)
            return None

    def check_integration_status(self, user: User) -> Any:
        return self.oauth.check_integration_status(user)

    def _parse_wealthbox_date(self, date: str) -> datetime:
        return datetime.strptime(date, "%Y-%m-%d %I:%M %p %z")

    def fetch_events(self, user: User, interval: timedelta) -> list[CalendarEvent]:
        logging.info("Fetching events from Wealthbox")
        if not (access_token := self.oauth.get_access_token(user)):
            logging.error("Error fetching events from Wealthbox: Could not get access token")
            return []

        logging.info("Fetching users from Wealthbox for matching invitees")
        users = self.__get_users(access_token)

        events: list[CalendarEvent] = []
        page: int = 1
        now = timezone.now()
        max_time = now + interval

        logging.info("Fetching event data from Wealthbox")
        try:
            while True:
                url = f"{self.base_url}/v1/events"
                headers = {
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                }
                params: dict[str, int | str] = {
                    "per_page": 100,
                    "page": page,
                    "order": "asc",
                    # We do other filtering later, so in order to avoid any issues with time zones,
                    # pad the filtering dates by a day in each direction.
                    "start_date_min": (now - timedelta(days=1)).date().isoformat(),
                    "start_date_max": (max_time + timedelta(days=1)).date().isoformat(),
                }

                response = requests.get(url, headers=headers, params=params)
                if response.status_code != 200:
                    logging.error("Error fetching events from Wealthbox: %s", response.status_code)
                    break

                data = response.json()
                for event in data.get("events", []):
                    start_time = self._parse_wealthbox_date(event.get("starts_at"))
                    end_time = self._parse_wealthbox_date(event.get("ends_at"))
                    # Skip events that are already over or too far in the future
                    if end_time <= now or start_time > max_time:
                        continue

                    # Wealthbox events have a list of invitees which reference other entities in the
                    # Wealthbox data model (`user` or `contact`). The events API doesn't return much
                    # information about the invitiees, and since we want to match these invitees to
                    # users in our own database, we need to fetch all users and contacts from
                    # Wealthbox (done above), match invitees with their corresponding `user`s or
                    # `contact`s and extract their email addresses.
                    participants = []
                    for participant in event.get("invitees", []):
                        id = participant.get("id")
                        if not id:
                            logging.error(
                                "Error while fetching Wealthbox calendar events: no participant ID. Skipping participant."
                            )
                            continue

                        email = None
                        participant_type = participant.get("type")
                        if participant_type == "User":
                            email = first_true(users, {}, lambda u: u.get("id") == id).get("email")
                        elif participant_type == "Contact":
                            try:
                                matching_clients = Client.objects.filter(
                                    crm_id=id, authorized_users__in=[user]
                                ).distinct()
                                email = matching_clients[0].email if matching_clients.exists() else None
                            except Client.DoesNotExist:
                                email = None

                        if not email:
                            continue

                        participants.append(
                            EventParticipant(id=str(id), name=participant.get("name"), email_address=email)
                        )
                    # Annotating this as a loosely-typed list will allow the Pydantic model to accept it
                    # without type errors. The model will then safely convert each URL string to a
                    # URL object, dropping ones that are not relevant.
                    meeting_urls: list[Any] = []
                    if location := event.get("location"):
                        meeting_urls.extend(find_urls(location))
                    if description := event.get("description"):
                        meeting_urls.extend(find_urls(description))

                    calendar_event = CalendarEvent(
                        provider="wealthbox",
                        id=str(event.get("id", "")),
                        user_specific_id=str(event.get("id", "")),
                        title=event.get("title"),
                        start_time=start_time,
                        end_time=end_time,
                        all_day=event.get("all_day", False),
                        body=description,
                        participants=participants,
                        meeting_urls=meeting_urls,
                    )
                    events.append(calendar_event)

                if data.get("meta", {}).get("total_pages") > page:
                    page += 1
                else:
                    break

            logging.info("Fetched event data from Wealthbox")
            return events

        except Exception as e:
            logging.error("Error fetching events from Wealthbox", exc_info=True)
            return []

    def _get_workflows_for_user(self, access_token: str) -> list[CRMWorkflow]:
        # Wealthbox has "workflow templates", "workflows", and "workflow steps"
        # A template is a definition of a series of steps,
        # a workflow is an instance of a series of steps for a client,
        # a step is a specific task to complete for the client which can be completed or reverted within a workflow
        # Despite this "fetch workflows" what we really want here is the workflow templates
        try:
            workflow_templates: list[CRMWorkflow] = []
            page: int = 1
            while True:
                url = f"{self.base_url}/v1/workflow_templates"
                headers = {
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                }
                params = {
                    "per_page": 100,
                    "page": page,
                }

                response = requests.get(url, headers=headers, params=params)
                if response.status_code == 200:
                    data = response.json()
                    workflow_templates += [
                        CRMWorkflow(crm_id=str(x.get("id")), name=x.get("name"))
                        for x in data.get("workflow_templates", {})
                    ]

                    total_pages = data.get("meta", {}).get("total_pages")
                    if total_pages and total_pages > page:
                        page += 1
                    else:
                        return workflow_templates
                else:
                    logging.info(
                        "Error getting workflow templates from Wealthbox: %s %s", response.json(), response.status_code
                    )
                    return workflow_templates

        except Exception as e:
            raise Exception(f"Error getting workflow templates from Wealthbox: {e}")

    def fetch_workflows_for_user(self, user: User) -> list[CRMWorkflow]:
        try:
            access_token = self.oauth.get_access_token(user)
            if not access_token:
                logging.error("Error getting Wealthbox credentials for user: %s", user.uuid)
                return []

            logging.info("Successfully fetched Wealthbox credentials for user: %s", user.uuid)
            workflows = self._get_workflows_for_user(
                access_token,
            )
            logging.info("Successfully fetched Wealthbox workflows for user: %s", user.uuid)
            return workflows
        except Exception as e:
            logging.error("Error getting Wealthbox workflows for user: %s, exception %s", user.uuid, e, exc_info=True)
            return []

    def list_users(self, requesting_user: User) -> list[CRMUser]:
        if not requesting_user.organization:
            error_msg = "User does not belong to an organization. Cannot list Wealthbox CRM users."
            logging.error(error_msg)
            return []

        if not (access_token := self.oauth.get_access_token(requesting_user)):
            logging.error("Error fetching users from Wealthbox: Could not get access token")
            return []

        try:
            wb_users = self.__get_users(access_token)
            if not wb_users:
                return []
            crm_org = ZeplynOrganization(
                uuid=str(requesting_user.organization.uuid),
                name=str(requesting_user.organization.name),
            )
            emails = [u.get("email") for u in wb_users if u.get("email")]
            matched_users = {}
            if emails:
                matched_users_query = User.objects.filter(
                    email__in=emails, organization_id=requesting_user.organization.id
                ).only("id", "email", "name")
                matched_users = {u.email.lower(): u for u in matched_users_query}

            crm_users = []
            for wb_user in wb_users:
                wb_user_id = wb_user.get("id")
                if not wb_user_id:
                    logging.debug(f"Wealthbox user entry missing 'Id': {wb_user}. Skipping this user.")
                    continue

                email = (wb_user.get("email") or "").lower()
                matched_user = matched_users.get(email)

                zeplyn_user: Optional[ZeplynUser] = None
                if matched_user:
                    zeplyn_user = ZeplynUser(
                        uuid=str(matched_user.uuid),
                        email=matched_user.email,
                        name=matched_user.name,
                    )

                crm_users.append(
                    CRMUser(
                        name=wb_user.get("name", "Unknown"),
                        crm_id=wb_user_id,
                        organization=crm_org,
                        zeplyn_user=zeplyn_user,
                    )
                )
            return crm_users
        except Exception as e:
            logging.error(
                f"Error listing Wealthbox CRM users for {requesting_user.organization.name} : {e}", exc_info=True
            )
            return []
