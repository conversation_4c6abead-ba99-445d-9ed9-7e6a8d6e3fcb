import base64
import datetime
import json
import logging
from typing import Any, Mapping

import pydantic
import requests
from django.conf import settings
from more_itertools import first_true
from pydantic import ValidationError

from deepinsights.core.integrations.calendar.calendar_data_parser import find_urls
from deepinsights.core.integrations.calendar.calendar_models import Calendar<PERSON>vent, EventParticipant
from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.crm_models import (
    CRMAccount,
    CRMNote,
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTarget,
    CRMSyncTargets,
    CRMUser,
    CRMWorkflow,
)
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User


class Redtail(CrmBase):
    def _now(self) -> datetime.datetime:
        return datetime.datetime.now(datetime.timezone.utc)

    def _phone_number_from_contact(self, contact: dict[str, Any]) -> str | None:
        phone_numbers = contact.get("phones", [])
        if not phone_numbers:
            return None
        phone_number = first_true(phone_numbers, phone_numbers[0], lambda p: p.get("is_preferred", False))
        country_code = phone_number.get("country_code", "")
        country_code_format = f"+{country_code}" if country_code else ""
        number = phone_number.get("number", "")
        return f"{country_code_format}{number}"

    def _email_from_contact(self, contact: dict[str, Any]) -> str | None:
        if not (emails := contact.get("emails", [])):
            return None
        email = first_true(emails, emails[0], lambda e: e.get("is_preferred", False))
        return email.get("address")  # type: ignore[no-any-return]

    def get_accounts_by_owner_email_and_name(self, owner_email: str, account_name_filter="") -> list[CRMAccount]:  # type: ignore[no-untyped-def]
        current_page_outer_scope: int | None = 1
        page_size = "1000"
        accounts_all: list[CRMAccount] = []

        # returns the list of accounts in this page as first half of 2-tuple
        # returns the index of the next page to pull, or None if there was an error or if it was the last page, second half 2-tuple
        def _get_one_page(current_page: int) -> tuple[list[CRMAccount], int | None]:
            contact_search_url = self.base_url + "contacts/search"
            params = {"page": current_page}
            if account_name_filter:
                params["first_name"] = account_name_filter
            payload = {}  # type: ignore[var-annotated]
            headers = self.headers
            headers.update({"include": "phones, emails, addresses", "pagesize": page_size})
            response = requests.get(contact_search_url, headers=headers, params=params, data=payload)
            # Handle the response as needed
            accounts = []
            if response.status_code == 200:
                # Process the response content
                response_json = response.json()
                response_total_pages = response_json.get("meta").get("total_pages")
                contacts = response_json.get("contacts", [])
                logging.debug("contacts: %s", contacts)
                logging.debug("page: %s of %s", current_page, response_total_pages)
                for contact in contacts:
                    try:
                        accounts.append(
                            CRMAccount(
                                first_name=contact.get("first_name"),
                                last_name=contact.get("last_name"),
                                email=self._email_from_contact(contact),
                                phone_number=self._phone_number_from_contact(contact),
                                crm_id=str(contact.get("id")),
                                client_type=contact.get("type"),
                                crm_system="redtail",
                                name=contact.get("full_name"),
                            )
                        )
                    except (ValueError, pydantic.ValidationError) as e:
                        logging.error("Error parsing Redtail contact: %s", contact, exc_info=e)

                next_page = current_page + 1
                return (accounts, next_page if next_page <= response_total_pages else None)

            else:
                # Handle error response
                logging.error("Error on page %s: %s %s", current_page, response.status_code, response.json())
                return (accounts, None)

        # back in the outer function
        while current_page_outer_scope:
            accounts_current_page, current_page_outer_scope = _get_one_page(current_page_outer_scope)
            accounts_all.extend(accounts_current_page)
        # return accounts for all pages
        return accounts_all

    def generate_user_key_code(self, username, password):  # type: ignore[no-untyped-def]
        authentication_url = self.base_url + "authentication"
        headers = {
            "Authorization": "Basic " + base64.b64encode((f"{self.api_key}:{username}:{password}").encode()).decode()
        }
        logging.debug("headers: %s", headers)
        logging.debug("url: %s", authentication_url)
        response = requests.get(authentication_url, headers=headers)
        if response.status_code == 200:
            logging.debug("response: %s", response.json())
            self.user_key: str = response.json().get("authenticated_user").get("user_key")
            self.generate_headers()
            logging.info("New user_key generated")
            return self.user_key
        else:
            logging.warn("Could not generate new user_key: %s", response.json().get("message"))
            return ""

    def resolve_sync_targets(
        self, note: Note, user: User, selected_sync_targets: CRMSyncTargets | None = None
    ) -> CRMSyncTargets:
        if not note.client or not (client_uuid := note.client.get("uuid")):
            logging.error("Note %s has no client", note.uuid)
            return CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])
        try:
            client = Client.objects.get(uuid=client_uuid)
            if not client.crm_id:
                logging.error("Client %s has no CRM ID", client_uuid)
                return CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])
            return CRMSyncTargets(
                status=CRMSyncTargets.Status.FINAL,
                note_targets=[CRMSyncTarget(crm_id=client.crm_id, type="contact", name=client.name)],
            )
        except Client.DoesNotExist:
            logging.error("Client with UUID %s does not exist", client_uuid)
            return CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])

    def add_interaction_with_client(
        self,
        note: Note,
        sync_targets: CRMSyncTargets,
        sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = None,
    ) -> None:
        note_body = note.get_summary_for_crm(use_html_formatting=False, sync_items=sync_items)
        data = json.dumps({"category_id": 2, "note_type": 1, "pinned": False, "draft": False, "body": note_body})

        if not sync_targets.note_targets:
            logging.error("No client provided for note %s", note.uuid)
            return
        if len(sync_targets.note_targets) > 1:
            logging.warning("Multiple clients provided for note %s, using the first one", note.uuid)
        client_crm_id = sync_targets.note_targets[0].crm_id

        try:
            crm_id = int(client_crm_id)
        except ValueError:
            logging.error("Client with CRM ID %s has an invalid Redtail CRM ID: %s", client_crm_id, client_crm_id)
            return
        url = f"{self.base_url}contacts/{crm_id}/notes"
        headers = self.headers
        headers.update({"Content-Type": "application/json"})
        logging.info("recording interaction : %s", data)
        logging.info("with headers: %s", headers)
        logging.info("with url: %s", url)
        if note.metadata.get("interactionId"):  # type: ignore[union-attr]
            logging.info("Already associated with an interactionId: %s", note.metadata.get("interactionId"))  # type: ignore[union-attr]
        else:
            response = requests.post(url, headers=headers, data=data)
            if response.status_code == 201:
                interactionId = response.json().get("note").get("id")
                note.metadata["interactionId"] = interactionId  # type: ignore[index]
                note.save()
                logging.info("interaction %s saved" % interactionId)
            else:
                logging.error("Error: %s", response.status_code)
                logging.error("Error: %s", response.json())
        if note.should_include_section(sync_items, CRMSyncSection.TASKS):
            for task in note.get_tasks_to_include_crm_sync(sync_items=sync_items):
                self.__add_task(task, crm_id)

    def __add_task(self, task: Task, crm_id: int) -> None:
        due_date = (task.due_date or self._now()).date()
        subject = task.task_title or "No Subject"  # since subject is required field for redtail
        description_body = task.task_desc or ""
        ELLIPSIS = "…"
        if len(subject) > 255:
            safe_subject = subject[: 255 - len(ELLIPSIS)] + ELLIPSIS
            # Full subject first, then task desc
            full_description = f"Subject: {subject}\n\nFollow up from client meeting.\n{description_body}"
        else:
            safe_subject = subject
            full_description = f"Follow up from client meeting.\n{description_body}"
        data = json.dumps(
            {
                "activity_code_id": 1,
                "subject": safe_subject,
                "all_day": True,
                "start_date": due_date.isoformat(),
                "end_date": due_date.isoformat(),
                "description": full_description,
                "importance": 2,
                "repeats": "never",
                "linked_contacts": [{"contact_id": crm_id}],
                # "note_associations": [
                #     {
                #         "actable_id": 9,
                #         "actable_type": "Crm::Note",
                #     }
                # ],
            }
        )
        headers = self.headers
        headers.update(
            {"include": "linked_contacts, attendees, activity_associations", "Content-Type": "application/json"}
        )
        logging.info("recording task : %s", data)
        logging.debug("with headers: %s", headers)
        # if task.metadata.get("taskId"):
        #     logging.info("Already associated with an task: %s", task.metadata.get("taskId"))
        #     return
        response = requests.post(f"{self.base_url}activities", headers=headers, data=data)
        logging.info("response: %s", response.json())
        if response.status_code == 200:
            taskId = response.json().get("activity").get("id")
            task.metadata["taskId"] = taskId  # type: ignore[index]
            task.save()
            logging.info("task %s saved", taskId)

    def __init__(self, user_key=None, api_key=None, base_url=None):  # type: ignore[no-untyped-def]
        self.api_key = api_key or settings.REDTAIL_API_KEY
        self.base_url = base_url or settings.REDTAIL_BASE_URL

        self.user_key = user_key
        self.generate_headers()

    def __get_notes(self, client: Client) -> list[dict]:  # type: ignore[type-arg]
        url = f"{self.base_url}contacts/{client.crm_id}/notes"
        headers = self.headers
        headers.update({"include": "category, note_type, attachments, linked_contacts, linked_deals"})
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            notes = response.json().get("notes", [])
            return notes  # type: ignore[no-any-return]
        else:
            logging.error(
                "Error fetching notes from Redtail for client %s: %s %s",
                client.uuid,
                response.status_code,
                response.json(),
            )
            return []

    def fetch_notes_for_client(
        self, user: User, client: Client, lookback_interval: datetime.timedelta
    ) -> list[CRMNote]:
        notes = self.__get_notes(client)
        logging.info("Successfully fetched Redtail notes for client %s, for user %s", client.uuid, user.uuid)
        parsed_notes: list[CRMNote] = []
        for note in notes:
            try:
                created_at = datetime.datetime.fromisoformat(note.get("created_at", ""))
            except Exception:
                logging.error("Error parsing Redtail note created_at: %s", note["created_at"])
                created_at = None
            parsed_notes.append(
                CRMNote(
                    crm_id=str(note["id"]),
                    crm_system="redtail",
                    content=note["body"],
                    created_at=created_at,
                    web_link=f"https://crm.redtailtechnology.com/contacts/{client.crm_id}/activities/{note['id']}",
                )
            )
        return parsed_notes

    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        logging.warning("get_client_basic_info not implemented for Redtail")
        return None

    def __get_activities(self, start_time: datetime.datetime, end_time: datetime.datetime) -> list[dict]:  # type: ignore[type-arg]
        url = f"{self.base_url}activities"
        headers = self.headers
        headers.update({"include": "category, note_type, attachments, linked_contacts, linked_deals"})

        # Experimentally, it seems that Redtail only uses the date portion of this filter.
        # So, in order to do our time-based filtering, we fetch all possibly-relevant activities
        # on the relevant dates and then filter based on the times in the returned activities.
        params = {
            "start_date": start_time.strftime("%Y-%m-%d"),
            "end_date": end_time.strftime("%Y-%m-%d"),
        }

        response = requests.get(url, headers=headers, params=params)
        if response.status_code == 200:
            activities = response.json().get("activities", [])
            return activities  # type: ignore[no-any-return]
        else:
            logging.error("Error: %s %s", response.status_code, response.json())
            return []

    def __datetime_from_event_time(self, event_time: str, all_day: bool) -> datetime.datetime:
        raw_datetime = datetime.datetime.fromisoformat(event_time)
        return (
            raw_datetime.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc)
            if all_day
            else raw_datetime
        )

    def __calendar_entries_from_events(
        self, activities: list[dict[str, Any]], filter_start_time: datetime.datetime, filter_end_time: datetime.datetime
    ) -> list[CalendarEvent]:
        entries = []
        for activity in activities:
            try:
                if not (event_id := activity.get("id", "")):
                    logging.error("Redtail activity does not have a id, Skipping ")
                    continue

                participants = [
                    EventParticipant(
                        id=attendee.get("id"),
                        name=attendee.get("name"),
                        email_address=attendee.get("email"),
                    )
                    for attendee in (activity.get("attendees", []))
                ]

                all_day = activity.get("all_day", False)
                start_time = self.__datetime_from_event_time(activity["start_date"], all_day)
                end_time = self.__datetime_from_event_time(activity["end_date"], all_day)
                # Experimentally, Redtail's all-day events are represented as two equivalent
                # timestamps. Therefore, if the start and end times are the same, and this is an
                # all-day event, increment the end_time by a day to match the Zeplyn calendar API
                # contract.
                if all_day and start_time == end_time:
                    end_time = end_time + datetime.timedelta(days=1)
                # The activities are already filtered by dates, but we need to filter by times as well.
                # The all-day activities have had their times adjusted above, so this filter will work
                # correctly for them as well.
                if end_time <= filter_start_time or start_time > filter_end_time:
                    continue
                # Annotating this as a loosely-typed list will allow the Pydantic model to accept it
                # without type errors. The model will then safely convert each URL string to a
                # URL object, dropping ones that are not relevant.
                meeting_urls: list[Any] = []
                if location := activity.get("location"):
                    meeting_urls.extend(find_urls(location))
                if description := activity.get("description"):
                    meeting_urls.extend(find_urls(description))
                entries.append(
                    CalendarEvent(
                        provider="redtail",
                        id=str(event_id),
                        user_specific_id=str(event_id),
                        title=activity.get("subject", ""),
                        body=description,
                        start_time=start_time,
                        end_time=end_time,
                        all_day=all_day,
                        participants=participants,
                        meeting_urls=meeting_urls,
                    )
                )
            except KeyError as e:
                logging.error("Missing key in Redtail activity: %s. Skipping" % e)
            except ValidationError as e:
                logging.error("Could not parse Redtail activity: %s. Skipping" % e)
        return entries

    def fetch_events(self, user: User, interval: datetime.timedelta) -> list[CalendarEvent]:
        start_time = self._now()
        end_time = start_time + interval
        activities = self.__get_activities(start_time, end_time)
        activities = list(filter(lambda activity: not activity["activity_code_id"] == 1, activities))

        logging.info("Successfully fetched Redtail %d activities" % (len(activities)))

        return self.__calendar_entries_from_events(activities, start_time, end_time)

    def generate_headers(self) -> None:
        self.headers = {
            "Authorization": "Userkeyauth " + base64.b64encode(f"{self.api_key}:{self.user_key}".encode()).decode()
        }

    def fetch_workflows_for_user(self, user: User) -> list[CRMWorkflow]:
        logging.warning("fetch_workflows_for_user not implemented for Redtail")
        return []

    def list_users(self, requesting_user: User) -> list[CRMUser]:
        logging.warning("list_users not implemented for Redtail")
        return []
