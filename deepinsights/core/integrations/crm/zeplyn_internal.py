import datetime
import logging
from typing import Any, Mapping

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.crm_models import (
    CRMAccount,
    CRMNote,
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTargets,
    CRMUser,
    CRMWorkflow,
)
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


# This is a development CRM that is used to test used for consistent data in demo environments
# and for testing and developing ML features
class ZeplynInternal(CrmBase):
    def resolve_sync_targets(
        self, note: Note, user: User, selected_sync_targets: CRMSyncTargets | None = None
    ) -> CRMSyncTargets:
        logging.info("resolve_sync_targets not implemented for Zeplyn Internal")
        return CRMSyncTargets(status=CRMSyncTargets.Status.FINAL, note_targets=[])

    def add_interaction_with_client(
        self,
        note: Note,
        sync_targets: CRMSyncTargets,
        sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = None,
    ) -> None:
        logging.info("add_interaction_with_client not implemented for Zeplyn Internal")
        return

    def get_accounts_by_owner_email_and_name(
        self, owner_email: str, account_name_filter: str | None = None
    ) -> list[CRMAccount]:
        logging.info("get_accounts_by_owner_email_and_name not implemented for Zeplyn Internal")
        return []

    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        logging.info("get_client_basic_info not implemented for Zeplyn Internal")
        return None

    def fetch_events(self, user: User, interval: datetime.timedelta) -> list[CalendarEvent]:
        """Fetch events from CRM."""
        logging.info("fetch_events not implemented for Zeplyn Internal")
        return []

    def fetch_notes_for_client(
        self, user: User, client: Client, lookback_interval: datetime.timedelta
    ) -> list[CRMNote]:
        """Fetch notes for a client from CRM."""
        if client.crm_system != "zeplyn_internal":
            return []

        notes = Note.objects.filter(client__uuid=str(client.uuid))
        parsed_notes: list[CRMNote] = []
        for note in notes:
            try:
                parsed_notes.append(
                    CRMNote(
                        crm_id=str(note.uuid),
                        crm_system="zeplyn_internal",
                        content=note.get_summary_for_crm(use_html_formatting=False),
                        created_at=note.created,
                    )
                )
            except Exception as e:
                logging.error("Error parsing Zeplyn Dev note: %s", note, exc_info=e)
        return parsed_notes

    def fetch_workflows_for_user(self, user: User) -> list[CRMWorkflow]:
        logging.warning("fetch_workflows_for_user not implemented for Zeplyn Internal")
        return []

    def list_users(self, requesting_user: User) -> list[CRMUser]:
        logging.warning("list_users not implemented for Zeplyn Internal")
        return []
