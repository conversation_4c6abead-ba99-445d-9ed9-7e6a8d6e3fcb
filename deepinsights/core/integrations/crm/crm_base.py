import datetime
from abc import ABC, abstractmethod
from typing import Any, Mapping

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.crm.crm_models import (
    CRMAccount,
    CRMNote,
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTargets,
    CRMUser,
    CRMWorkflow,
)
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


class CrmBase(ABC):
    """A representation of a CRM that can be used to (among other things) sync notes and fetch client information."""

    @abstractmethod
    def resolve_sync_targets(
        self, note: Note, user: User, selected_sync_targets: CRMSyncTargets | None = None
    ) -> CRMSyncTargets:
        """
        Resolve the entity or entities in the CRM to which the note should be synced.

        The intention for this method is that it can be called with different sets of selected sync
        targets, to further resolve from a client to a specific record associated with that client.

        Args:
            note: The Note for which to resolve sync targets.
            user: The User for whom to resolve sync targets.
            selected_sync_targets: Optional list of targets that were selected by the user for resolution.

        Returns:
            A CRMSyncTargets object containing the resolved sync targets.
        """
        pass

    @abstractmethod
    def add_interaction_with_client(
        self,
        note: Note,
        sync_targets: CRMSyncTargets,
        sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = None,
    ) -> None:
        """
        Try to upload note to CRM based on user setup and return status.

        Args:
            note: The Note object to be uploaded.
            sync_targets: The CRMSyncTargets object containing the resolved sync targets.

        Returns:
            None
        """
        pass

    @abstractmethod
    def get_accounts_by_owner_email_and_name(
        self, owner_email: str, account_name_filter: str | None = None
    ) -> list[CRMAccount]:
        """
        Based on user CRM setup, fetch list of all their clients.

        Args:
            user: The User object.
            account_name_filter: Optional filter for account names.

        Returns:
            A list of dictionaries containing account information.
        """
        pass

    @abstractmethod
    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        """
        Fetch basic info of client from CRM.

        Args:
            client: The Client object whose information to fetch.
            user: The User object requesting the information.
            include_household: Whether to include household information.

        Returns:
            A dictionary containing client information or None if not found.
        """
        pass

    @abstractmethod
    def fetch_events(self, user: User, interval: datetime.timedelta) -> list[CalendarEvent]:
        """
        Fetch events from CRM.

        Returns:
            A list of CalendarEvent objects.
        """
        pass

    @abstractmethod
    def fetch_notes_for_client(
        self, user: User, client: Client, lookback_interval: datetime.timedelta
    ) -> list[CRMNote]:
        """
        Fetch notes for a specific client from CRM.

        Args:
            user: The User requesting the notes.
            client: The Client whose notes should be fetched.
            lookback_interval: Lookback time interval for fetching notes. Note that this must be a
            positive datetime.timedelta: it will be subtracted from the current time to get the
            start time for fetching notes.

        Returns:
            A list of CRMNote objects.
        """
        pass

    @abstractmethod
    def fetch_workflows_for_user(self, user: User) -> list[CRMWorkflow]:
        """
        Fetch workflows that a user has access to, to use in a CRM sync for follow up Tasks.

        Args:
            user: the User who owns the Note associated with the follow up Tasks.

        Returns:
            A list of CRMWorkflow items.
        """
        pass

    @abstractmethod
    def list_users(self, requesting_user: User) -> list[CRMUser] | None:
        """
        Retrieves a list of CRM users from Salesforce and maps them to internal Zeplyn users if possible.
        Args:
            requesting_user (User): The user making the request. Must belong to an organization.
        Returns:
            list[CRMUser] | None: A list of CRMUser objects representing Salesforce users, each optionally
            linked to a Zeplyn user if their email matches. Returns an empty list if no Salesforce users are found.
        Raises:
            ValueError: If the provided user does not belong to any organization.
        """
        pass
