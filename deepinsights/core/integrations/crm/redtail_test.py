import json
import logging
import uuid
from datetime import datetime, timedelta, timezone
from unittest.mock import MagicMock, call, patch

import pydantic
from django.test import TestCase, override_settings

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent, EventParticipant
from deepinsights.core.integrations.crm.crm_models import (
    CRMAccount,
    CRMNote,
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTarget,
    CRMSyncTargets,
)
from deepinsights.core.integrations.crm.redtail import Redtail
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User


class TestRedtail(TestCase):
    def setUp(self):  # type: ignore[no-untyped-def]
        self.redtail = Redtail(user_key="test_user_key", api_key="test_api_key")  # type: ignore[no-untyped-call]

    @patch("requests.get")
    def test_get_accounts_by_owner_email_and_name(self, mock_get: MagicMock) -> None:
        self.maxDiff = None
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "contacts": [
                {
                    "id": 123,
                    "first_name": "<PERSON>",
                    "last_name": "Doe",
                    "emails": [{"address": "<EMAIL>"}],
                    "phones": [{"country_code": 44, "number": "***********"}],
                    "full_name": "John Doe",
                    "type": "Client",
                },
                {
                    "id": 124,
                    "first_name": "Jane",
                    "last_name": "Doe",
                    "emails": [
                        {"address": "<EMAIL>"},
                        {"address": "<EMAIL>", "is_preferred": True},
                    ],
                    "phones": [
                        {"country_code": 1, "number": "**********"},
                        {"number": "**********", "is_preferred": True},
                    ],
                    "full_name": "Jane Doe",
                    "type": "Client",
                },
            ],
            "meta": {"total_pages": 1},
        }
        mock_get.return_value = mock_response

        user = User(email="<EMAIL>")
        accounts = self.redtail.get_accounts_by_owner_email_and_name(user.email, "John")
        self.assertEqual(
            accounts,
            [
                CRMAccount(
                    first_name="John",
                    last_name="Doe",
                    email="<EMAIL>",
                    phone_number="+*************",
                    crm_id="123",
                    client_type="Client",
                    crm_system="redtail",
                    name="John Doe",
                ),
                CRMAccount(
                    first_name="Jane",
                    last_name="Doe",
                    email="<EMAIL>",
                    phone_number="**********",
                    crm_id="124",
                    client_type="Client",
                    crm_system="redtail",
                    name="Jane Doe",
                ),
            ],
        )

    @patch("requests.get")
    def test_get_accounts_by_owner_email_and_name_multi_page(self, mock_get: MagicMock) -> None:
        self.maxDiff = None
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "contacts": [
                {
                    "id": 123,
                    "first_name": "John",
                    "last_name": "Doe",
                    "emails": [{"address": "<EMAIL>"}],
                    "phones": [{"country_code": 44, "number": "***********"}],
                    "full_name": "John Doe",
                    "type": "Client",
                },
                {
                    "id": 124,
                    "first_name": "Jane",
                    "last_name": "Doe",
                    "emails": [
                        {"address": "<EMAIL>"},
                        {"address": "<EMAIL>", "is_preferred": True},
                    ],
                    "phones": [
                        {"country_code": 1, "number": "**********"},
                        {"number": "**********", "is_preferred": True},
                    ],
                    "full_name": "Jane Doe",
                    "type": "Client",
                },
            ],
            "meta": {"total_pages": 2},
        }
        mock_get.return_value = mock_response

        accounts = self.redtail.get_accounts_by_owner_email_and_name("<EMAIL>", "John")
        self.assertEqual(4, len(accounts))
        self.assertEqual(2, mock_get.call_count)  # called once per page
        pages_mocked = [call_args.kwargs["params"]["page"] for call_args in mock_get.call_args_list]
        self.assertListEqual(
            [1, 2], pages_mocked
        )  # assert we get each page once and in order (not a guarantee that entries are ordered)
        self.assertEqual(  # since we didn't mock the two calls differently, these two accounts are duplicated into 4 entries
            accounts,
            [
                CRMAccount(
                    first_name="John",
                    last_name="Doe",
                    email="<EMAIL>",
                    phone_number="+*************",
                    crm_id="123",
                    client_type="Client",
                    crm_system="redtail",
                    name="John Doe",
                ),
                CRMAccount(
                    first_name="Jane",
                    last_name="Doe",
                    email="<EMAIL>",
                    phone_number="**********",
                    crm_id="124",
                    client_type="Client",
                    crm_system="redtail",
                    name="Jane Doe",
                ),
                CRMAccount(
                    first_name="John",
                    last_name="Doe",
                    email="<EMAIL>",
                    phone_number="+*************",
                    crm_id="123",
                    client_type="Client",
                    crm_system="redtail",
                    name="John Doe",
                ),
                CRMAccount(
                    first_name="Jane",
                    last_name="Doe",
                    email="<EMAIL>",
                    phone_number="**********",
                    crm_id="124",
                    client_type="Client",
                    crm_system="redtail",
                    name="Jane Doe",
                ),
            ],
        )

    @patch("requests.post")
    @patch("deepinsights.meetingsapp.models.note.Note.get_summary_for_crm", return_value="test_note_summary")
    def test_add_interaction_with_client(self, _: MagicMock, mock_request_post: MagicMock) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        test_client = Client.objects.create(crm_id="123", organization=org)
        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(test_client.uuid)},
            metadata={"interactionId": None},
        )

        Task.objects.create(
            note=note,
            task_title="Test Task",
            task_desc="This is a test task.",
            due_date=datetime(2023, 8, 21, tzinfo=timezone.utc),
        )

        mock_request_post.side_effect = [
            MagicMock(
                status_code=201,
                json=lambda: {"note": {"id": "test_interaction_id"}},
            ),
            MagicMock(
                status_code=200,
                json=lambda: {"activity": {"id": "test_interaction_id"}},
            ),
        ]

        sync_targets = self.redtail.resolve_sync_targets(note, user)
        self.redtail.add_interaction_with_client(note, sync_targets)

        mock_request_post.assert_has_calls(
            [
                call(
                    f"{self.redtail.base_url}contacts/123/notes",
                    headers=self.redtail.headers,
                    data='{"category_id": 2, "note_type": 1, "pinned": false, "draft": false, "body": "test_note_summary"}',
                ),
                call(
                    f"{self.redtail.base_url}activities",
                    headers=self.redtail.headers,
                    data=(
                        '{"activity_code_id": 1, "subject": "Test Task", "all_day": true, '
                        '"start_date": "2023-08-21", "end_date": "2023-08-21", '
                        '"description": "Follow up from client meeting.\\nThis is a test task.", '
                        '"importance": 2, "repeats": "never", "linked_contacts": [{"contact_id": 123}]}'
                    ),
                ),
            ],
            any_order=False,
        )
        self.assertEqual((note.metadata or {})["interactionId"], "test_interaction_id")

    @patch("requests.post")
    @patch("deepinsights.meetingsapp.models.note.Note.get_summary_for_crm", return_value="test_note_summary")
    def test_add_interaction_with_client_subject_truncated(self, _: MagicMock, mock_request_post: MagicMock) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        test_client = Client.objects.create(crm_id="123", organization=org)
        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(test_client.uuid)},
            metadata={"interactionId": None},
        )

        # Create a subject longer than 255 characters
        long_subject = "A" * 300  # 300 'A's, will trigger truncation
        truncated_subject = "A" * 254 + "…"

        Task.objects.create(
            note=note,
            task_title=long_subject,
            task_desc="This is a test task with a very long subject.",
            due_date=datetime(2023, 8, 21, tzinfo=timezone.utc),
        )

        mock_request_post.side_effect = [
            MagicMock(
                status_code=201,
                json=lambda: {"note": {"id": "test_interaction_id"}},
            ),
            MagicMock(
                status_code=200,
                json=lambda: {"activity": {"id": "test_interaction_id"}},
            ),
        ]

        sync_targets = self.redtail.resolve_sync_targets(note, user)
        self.redtail.add_interaction_with_client(note, sync_targets)

        expected_description = (
            f"Subject: {long_subject}\n\nFollow up from client meeting.\n"
            "This is a test task with a very long subject."
        )

        expected_data = json.dumps(
            {
                "activity_code_id": 1,
                "subject": truncated_subject,
                "all_day": True,
                "start_date": "2023-08-21",
                "end_date": "2023-08-21",
                "description": expected_description,
                "importance": 2,
                "repeats": "never",
                "linked_contacts": [{"contact_id": 123}],
            }
        )
        mock_request_post.assert_has_calls(
            [
                call(
                    f"{self.redtail.base_url}contacts/123/notes",
                    headers=self.redtail.headers,
                    data='{"category_id": 2, "note_type": 1, "pinned": false, "draft": false, "body": "test_note_summary"}',
                ),
                call(
                    f"{self.redtail.base_url}activities",
                    headers=self.redtail.headers,
                    data=expected_data,
                ),
            ],
            any_order=False,
        )
        self.assertEqual((note.metadata or {})["interactionId"], "test_interaction_id")

    @patch("requests.post")
    @patch("deepinsights.meetingsapp.models.note.Note.get_summary_for_crm", return_value="test_note_summary")
    def test_add_interaction_with_client_subject_exactly_255_characters(
        self, _: MagicMock, mock_request_post: MagicMock
    ) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        test_client = Client.objects.create(crm_id="123", organization=org)
        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(test_client.uuid)},
            metadata={"interactionId": None},
        )

        # Create subject with exactly 255 characters
        subject_255 = "A" * 255

        Task.objects.create(
            note=note,
            task_title=subject_255,
            task_desc="This is a test task with exactly 255 characters subject.",
            due_date=datetime(2023, 8, 21, tzinfo=timezone.utc),
        )

        mock_request_post.side_effect = [
            MagicMock(
                status_code=201,
                json=lambda: {"note": {"id": "test_interaction_id"}},
            ),
            MagicMock(
                status_code=200,
                json=lambda: {"activity": {"id": "test_interaction_id"}},
            ),
        ]

        sync_targets = self.redtail.resolve_sync_targets(note, user)
        self.redtail.add_interaction_with_client(note, sync_targets)

        expected_description = (
            "Follow up from client meeting.\nThis is a test task with exactly 255 characters subject."
        )

        expected_data = json.dumps(
            {
                "activity_code_id": 1,
                "subject": subject_255,
                "all_day": True,
                "start_date": "2023-08-21",
                "end_date": "2023-08-21",
                "description": expected_description,
                "importance": 2,
                "repeats": "never",
                "linked_contacts": [{"contact_id": 123}],
            }
        )

        mock_request_post.assert_has_calls(
            [
                call(
                    f"{self.redtail.base_url}contacts/123/notes",
                    headers=self.redtail.headers,
                    data=json.dumps(
                        {
                            "category_id": 2,
                            "note_type": 1,
                            "pinned": False,
                            "draft": False,
                            "body": "test_note_summary",
                        }
                    ),
                ),
                call(
                    f"{self.redtail.base_url}activities",
                    headers=self.redtail.headers,
                    data=expected_data,
                ),
            ],
            any_order=False,
        )
        self.assertEqual((note.metadata or {})["interactionId"], "test_interaction_id")

    @patch("requests.post")
    @patch("deepinsights.meetingsapp.models.note.Note.get_summary_for_crm", return_value="test_note_summary")
    def test_add_interaction_with_client_empty_subject_and_description(
        self, _: MagicMock, mock_request_post: MagicMock
    ) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        test_client = Client.objects.create(crm_id="123", organization=org)
        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(test_client.uuid)},
            metadata={"interactionId": None},
        )

        # Empty subject and description
        Task.objects.create(
            note=note,
            task_title="",
            task_desc="",
            due_date=datetime(2023, 8, 21, tzinfo=timezone.utc),
        )

        mock_request_post.side_effect = [
            MagicMock(
                status_code=201,
                json=lambda: {"note": {"id": "test_interaction_id"}},
            ),
            MagicMock(
                status_code=200,
                json=lambda: {"activity": {"id": "test_interaction_id"}},
            ),
        ]

        sync_targets = self.redtail.resolve_sync_targets(note, user)
        self.redtail.add_interaction_with_client(note, sync_targets)

        expected_description = "Follow up from client meeting.\n"

        expected_data = json.dumps(
            {
                "activity_code_id": 1,
                "subject": "No Subject",
                "all_day": True,
                "start_date": "2023-08-21",
                "end_date": "2023-08-21",
                "description": expected_description,
                "importance": 2,
                "repeats": "never",
                "linked_contacts": [{"contact_id": 123}],
            }
        )

        mock_request_post.assert_has_calls(
            [
                call(
                    f"{self.redtail.base_url}contacts/123/notes",
                    headers=self.redtail.headers,
                    data=json.dumps(
                        {
                            "category_id": 2,
                            "note_type": 1,
                            "pinned": False,
                            "draft": False,
                            "body": "test_note_summary",
                        }
                    ),
                ),
                call(
                    f"{self.redtail.base_url}activities",
                    headers=self.redtail.headers,
                    data=expected_data,
                ),
            ],
            any_order=False,
        )
        self.assertEqual((note.metadata or {})["interactionId"], "test_interaction_id")

    @patch("requests.post")
    @patch("deepinsights.meetingsapp.models.note.Note.get_summary_for_crm", return_value="test_note_summary")
    def test_add_interaction_with_client_multiple_clients(self, _: MagicMock, mock_request_post: MagicMock) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        test_client = Client.objects.create(crm_id="123", organization=org)
        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(test_client.uuid)},
            metadata={"interactionId": None},
        )

        Task.objects.create(
            note=note,
            task_title="Test Task",
            task_desc="This is a test task.",
            due_date=datetime(2023, 8, 21, tzinfo=timezone.utc),
        )

        mock_request_post.side_effect = [
            MagicMock(
                status_code=201,
                json=lambda: {"note": {"id": "test_interaction_id"}},
            ),
            MagicMock(
                status_code=200,
                json=lambda: {"activity": {"id": "test_interaction_id"}},
            ),
        ]

        with self.assertLogs(level=logging.WARNING) as cm:
            self.redtail.add_interaction_with_client(
                note,
                CRMSyncTargets(
                    status=CRMSyncTargets.Status.FINAL,
                    note_targets=[
                        CRMSyncTarget(crm_id=test_client.crm_id, type=test_client.client_type, name=test_client.name),
                        CRMSyncTarget(crm_id="456", type="client", name="Another Client"),
                    ],
                ),
            )
            self.assertEqual(len(cm.output), 1)
            self.assertIn("Multiple clients provided for note", cm.output[0])

        mock_request_post.assert_has_calls(
            [
                call(
                    f"{self.redtail.base_url}contacts/123/notes",
                    headers=self.redtail.headers,
                    data='{"category_id": 2, "note_type": 1, "pinned": false, "draft": false, "body": "test_note_summary"}',
                ),
                call(
                    f"{self.redtail.base_url}activities",
                    headers=self.redtail.headers,
                    data=(
                        '{"activity_code_id": 1, "subject": "Test Task", "all_day": true, '
                        '"start_date": "2023-08-21", "end_date": "2023-08-21", '
                        '"description": "Follow up from client meeting.\\nThis is a test task.", '
                        '"importance": 2, "repeats": "never", "linked_contacts": [{"contact_id": 123}]}'
                    ),
                ),
            ],
            any_order=False,
        )
        self.assertEqual((note.metadata or {})["interactionId"], "test_interaction_id")

    @patch("requests.post")
    def test_add_interaction_with_client_missing_note_uuid(self, mock_request_post: MagicMock) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        note = Note.objects.create(
            note_owner=user,
            metadata={"interactionId": None},
        )

        sync_targets = self.redtail.resolve_sync_targets(note, user)
        self.redtail.add_interaction_with_client(note, sync_targets)

        mock_request_post.assert_not_called()
        self.assertIsNone((note.metadata or {})["interactionId"])

    @patch("requests.post")
    def test_add_interaction_with_client_missing_client(self, mock_request_post: MagicMock) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(uuid.uuid4())},
            metadata={"interactionId": None},
        )

        sync_targets = self.redtail.resolve_sync_targets(note, user)
        self.redtail.add_interaction_with_client(note, sync_targets)

        mock_request_post.assert_not_called()
        self.assertIsNone((note.metadata or {})["interactionId"])

    @patch("requests.post")
    def test_add_interaction_with_client_no_crm_id(self, mock_request_post: MagicMock) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        test_client = Client.objects.create(organization=org)
        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(test_client.uuid)},
            metadata={"interactionId": None},
        )

        sync_targets = self.redtail.resolve_sync_targets(note, user)
        self.redtail.add_interaction_with_client(note, sync_targets)

        mock_request_post.assert_not_called()
        self.assertIsNone((note.metadata or {})["interactionId"])

    @patch("requests.post")
    def test_add_interaction_with_client_invalid_crm_id(self, mock_request_post: MagicMock) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        test_client = Client.objects.create(crm_id="notaninteger", organization=org)
        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(test_client.uuid)},
            metadata={"interactionId": None},
        )

        sync_targets = self.redtail.resolve_sync_targets(note, user)
        self.redtail.add_interaction_with_client(note, sync_targets)

        mock_request_post.assert_not_called()
        self.assertIsNone((note.metadata or {})["interactionId"])

    @patch("requests.post")
    @patch("deepinsights.meetingsapp.models.note.Note.get_summary_for_crm", return_value="test_note_summary")
    def test_add_interaction_with_client_error(self, _: MagicMock, mock_request_post: MagicMock) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        test_client = Client.objects.create(crm_id="123", organization=org)
        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(test_client.uuid)},
            metadata={"interactionId": None},
        )

        mock_request_post.return_value = MagicMock(
            status_code=400,
            json=lambda: {"error": "Bad Request"},
        )

        sync_targets = self.redtail.resolve_sync_targets(note, user)
        self.redtail.add_interaction_with_client(note, sync_targets)

        mock_request_post.assert_called_with(
            f"{self.redtail.base_url}contacts/123/notes",
            headers=self.redtail.headers,
            data='{"category_id": 2, "note_type": 1, "pinned": false, "draft": false, "body": "test_note_summary"}',
        )
        self.assertIsNone((note.metadata or {})["interactionId"])

    @patch("requests.post")
    @patch("deepinsights.meetingsapp.models.note.Note.get_summary_for_crm", return_value="test_note_summary")
    def test_add_interaction_sync_items_none_backward_compatibility(
        self, mock_get_summary: MagicMock, mock_request_post: MagicMock
    ) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        test_client = Client.objects.create(crm_id="123", organization=org)
        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(test_client.uuid)},
            metadata={"interactionId": None},
        )

        Task.objects.create(
            note=note,
            task_title="Test Task",
            task_desc="This is a test task.",
            due_date=datetime(2023, 8, 21, tzinfo=timezone.utc),
        )

        mock_request_post.side_effect = [
            MagicMock(status_code=201, json=lambda: {"note": {"id": "test_interaction_id"}}),
            MagicMock(status_code=200, json=lambda: {"activity": {"id": "test_task_id"}}),
        ]

        sync_targets = self.redtail.resolve_sync_targets(note, user)
        self.redtail.add_interaction_with_client(note, sync_targets, None)

        mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items=None)

        self.assertEqual(mock_request_post.call_count, 2)
        self.assertEqual((note.metadata or {})["interactionId"], "test_interaction_id")

    @patch("requests.post")
    @patch("deepinsights.meetingsapp.models.note.Note.get_summary_for_crm", return_value="test_note_summary")
    def test_add_interaction_sync_items_empty_dict_backward_compatibility(
        self, mock_get_summary: MagicMock, mock_request_post: MagicMock
    ) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        test_client = Client.objects.create(crm_id="123", organization=org)
        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(test_client.uuid)},
            metadata={"interactionId": None},
        )

        Task.objects.create(
            note=note,
            task_title="Test Task",
            task_desc="This is a test task.",
            due_date=datetime(2023, 8, 21, tzinfo=timezone.utc),
        )

        mock_request_post.side_effect = [
            MagicMock(status_code=201, json=lambda: {"note": {"id": "test_interaction_id"}}),
            MagicMock(status_code=200, json=lambda: {"activity": {"id": "test_task_id"}}),
        ]

        sync_targets = self.redtail.resolve_sync_targets(note, user)
        self.redtail.add_interaction_with_client(note, sync_targets, {})

        self.assertEqual(mock_request_post.call_count, 2)
        self.assertEqual((note.metadata or {})["interactionId"], "test_interaction_id")

    @patch("requests.post")
    @patch("deepinsights.meetingsapp.models.note.Note.get_summary_for_crm", return_value="test_note_summary")
    def test_add_interaction_sync_items_tasks_enabled_boolean(
        self, mock_get_summary: MagicMock, mock_request_post: MagicMock
    ) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        test_client = Client.objects.create(crm_id="123", organization=org)
        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(test_client.uuid)},
            metadata={"interactionId": None},
        )

        task = Task.objects.create(
            note=note,
            task_title="Test Task",
            task_desc="This is a test task.",
            due_date=datetime(2023, 8, 21, tzinfo=timezone.utc),
        )

        mock_request_post.side_effect = [
            MagicMock(status_code=201, json=lambda: {"note": {"id": "test_interaction_id"}}),
            MagicMock(status_code=200, json=lambda: {"activity": {"id": "test_task_id"}}),
        ]

        sync_items = {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True)}
        sync_targets = self.redtail.resolve_sync_targets(note, user)
        self.redtail.add_interaction_with_client(note, sync_targets, sync_items)

        mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items=sync_items)

        self.assertEqual(mock_request_post.call_count, 2)
        mock_request_post.assert_has_calls(
            [
                call(
                    f"{self.redtail.base_url}contacts/123/notes",
                    headers=self.redtail.headers,
                    data='{"category_id": 2, "note_type": 1, "pinned": false, "draft": false, "body": "test_note_summary"}',
                ),
                call(
                    f"{self.redtail.base_url}activities",
                    headers=self.redtail.headers,
                    data=(
                        '{"activity_code_id": 1, "subject": "Test Task", "all_day": true, '
                        '"start_date": "2023-08-21", "end_date": "2023-08-21", '
                        '"description": "Follow up from client meeting.\\nThis is a test task.", '
                        '"importance": 2, "repeats": "never", "linked_contacts": [{"contact_id": 123}]}'
                    ),
                ),
            ],
            any_order=False,
        )
        self.assertEqual((note.metadata or {})["interactionId"], "test_interaction_id")

    @patch("requests.post")
    @patch("deepinsights.meetingsapp.models.note.Note.get_summary_for_crm", return_value="test_note_summary")
    def test_add_interaction_sync_items_tasks_disabled_boolean(
        self, mock_get_summary: MagicMock, mock_request_post: MagicMock
    ) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        test_client = Client.objects.create(crm_id="123", organization=org)
        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(test_client.uuid)},
            metadata={"interactionId": None},
        )

        Task.objects.create(
            note=note,
            task_title="Test Task Should Not Sync",
            task_desc="This task should not be synced.",
            due_date=datetime(2023, 8, 21, tzinfo=timezone.utc),
        )

        mock_request_post.return_value = MagicMock(
            status_code=201,
            json=lambda: {"note": {"id": "test_interaction_id"}},
        )

        sync_items = {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=False, included_items=[])}
        sync_targets = self.redtail.resolve_sync_targets(note, user)
        self.redtail.add_interaction_with_client(note, sync_targets, sync_items)

        mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items=sync_items)

        self.assertEqual(mock_request_post.call_count, 1)
        mock_request_post.assert_called_with(
            f"{self.redtail.base_url}contacts/123/notes",
            headers=self.redtail.headers,
            data='{"category_id": 2, "note_type": 1, "pinned": false, "draft": false, "body": "test_note_summary"}',
        )
        self.assertEqual((note.metadata or {})["interactionId"], "test_interaction_id")

    @patch("requests.post")
    @patch("deepinsights.meetingsapp.models.note.Note.get_summary_for_crm", return_value="test_note_summary")
    def test_add_interaction_sync_items_mixed_boolean_sections(
        self, mock_get_summary: MagicMock, mock_request_post: MagicMock
    ) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        test_client = Client.objects.create(crm_id="123", organization=org)
        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(test_client.uuid)},
            metadata={"interactionId": None},
        )

        task = Task.objects.create(
            note=note,
            task_title="Test Task",
            task_desc="This is a test task.",
            due_date=datetime(2023, 8, 21, tzinfo=timezone.utc),
        )

        mock_request_post.side_effect = [
            MagicMock(status_code=201, json=lambda: {"note": {"id": "test_interaction_id"}}),
            MagicMock(status_code=200, json=lambda: {"activity": {"id": "test_task_id"}}),
        ]

        sync_items = {
            CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.ATTENDEES: CRMSyncItemSelection(include_section=False),
            CRMSyncSection.KEYWORDS: CRMSyncItemSelection(include_section=False),
        }
        sync_targets = self.redtail.resolve_sync_targets(note, user)
        self.redtail.add_interaction_with_client(note, sync_targets, sync_items)

        mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items=sync_items)

        self.assertEqual(mock_request_post.call_count, 2)

    @patch("requests.post")
    @patch("deepinsights.meetingsapp.models.note.Note.get_summary_for_crm", return_value="test_note_summary")
    def test_add_interaction_sync_items_preserves_existing_error_handling(
        self, mock_get_summary: MagicMock, mock_request_post: MagicMock
    ) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        note = Note.objects.create(
            note_owner=user,
            metadata={"interactionId": None},
        )

        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[],
        )

        sync_items = {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True, included_items=[])}

        with self.assertLogs(level=logging.ERROR) as cm:
            self.redtail.add_interaction_with_client(note, sync_targets, sync_items)
            self.assertEqual(len(cm.output), 1)
            self.assertIn("No client provided for note", cm.output[0])

        mock_request_post.assert_not_called()

    @patch("requests.post")
    @patch("deepinsights.meetingsapp.models.note.Note.get_summary_for_crm", return_value="test_note_summary")
    def test_add_interaction_sync_items_task_processing_independent_of_note_creation_result(
        self, mock_get_summary: MagicMock, mock_request_post: MagicMock
    ) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        test_client = Client.objects.create(crm_id="123", organization=org)
        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(test_client.uuid)},
            metadata={"interactionId": None},
        )

        task = Task.objects.create(
            note=note,
            task_title="Test Task",
            task_desc="This is a test task.",
            due_date=datetime(2023, 8, 21, tzinfo=timezone.utc),
        )

        mock_request_post.side_effect = [
            MagicMock(status_code=400, json=lambda: {"error": "Bad Request"}),  # Note creation fails
            MagicMock(status_code=200, json=lambda: {"activity": {"id": "test_task_id"}}),  # Task creation succeeds
        ]

        sync_items = {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True)}
        sync_targets = self.redtail.resolve_sync_targets(note, user)

        with self.assertLogs(level=logging.ERROR):
            self.redtail.add_interaction_with_client(note, sync_targets, sync_items)

        self.assertEqual(mock_request_post.call_count, 2)

    @patch("requests.post")
    @patch("deepinsights.meetingsapp.models.note.Note.get_summary_for_crm", return_value="test_note_summary")
    def test_add_interaction_sync_items_no_task_calls_when_disabled_even_on_note_error(
        self, mock_get_summary: MagicMock, mock_request_post: MagicMock
    ) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        test_client = Client.objects.create(crm_id="123", organization=org)
        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(test_client.uuid)},
            metadata={"interactionId": None},
        )

        Task.objects.create(
            note=note,
            task_title="Test Task",
            task_desc="This task should not be synced.",
            due_date=datetime(2023, 8, 21, tzinfo=timezone.utc),
        )

        mock_request_post.return_value = MagicMock(status_code=400, json=lambda: {"error": "Bad Request"})

        sync_items = {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=False)}
        sync_targets = self.redtail.resolve_sync_targets(note, user)

        with self.assertLogs(level=logging.ERROR):
            self.redtail.add_interaction_with_client(note, sync_targets, sync_items)

        self.assertEqual(mock_request_post.call_count, 1)

    def test_add_interaction_backward_compatibility_method_signature(self) -> None:
        org = Organization.objects.create(name="Test Org")
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email, organization=org)
        test_client = Client.objects.create(crm_id="123", organization=org)
        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(test_client.uuid)},
            metadata={"interactionId": None},
        )

        sync_targets = self.redtail.resolve_sync_targets(note, user)

        try:
            self.redtail.add_interaction_with_client(note, sync_targets)
            self.assertTrue(True)
        except TypeError as e:
            self.fail(f"Backward compatibility broken: {e}")

    @override_settings(REDTAIL_BASE_URL="https://api.redtailtechnology.com/crm/v1/")
    @patch("requests.get")
    def test_fetch_crm_events(self, mock_get: MagicMock) -> None:
        self.maxDiff = None
        email = "<EMAIL>"
        user = User.objects.create(username=email, email=email)

        # Setup mock response data
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "activities": [
                {
                    "id": "123",
                    "subject": "Test Meeting",
                    "start_date": "2024-11-12T10:00:00Z",
                    "end_date": "2024-11-12T11:00:00Z",
                    "activity_code_id": 2,
                    "attendees": [
                        {
                            "id": "1",
                            "name": "Test",
                            "email": "<EMAIL>",
                            "type": "Crm::Activity::Attendee::User",
                        },
                    ],
                    "location": "https://teams.microsoft.com/l/meetup-join/123",
                    "description": (
                        "Meeting URL: https://meet.google.com/123\nMeeting ID: 123\n"
                        "Alternative URL: https://zoom.us/456"
                    ),
                },
                {
                    "id": "124",
                    "subject": "None description or location",
                    "start_date": "2024-11-12T11:00:00Z",
                    "end_date": "2024-11-12T12:00:00Z",
                    "activity_code_id": 2,
                    "attendees": [],
                    "location": None,
                    "description": None,
                },
                {
                    "id": "125",
                    "subject": "No description or location",
                    "start_date": "2024-11-12T12:00:00Z",
                    "end_date": "2024-11-12T13:00:00Z",
                    "activity_code_id": 2,
                    "attendees": [],
                },
                {
                    "id": "126",
                    "subject": "All-day meeting",
                    "all_day": True,
                    "start_date": "2024-11-12T10:00:00Z",
                    "end_date": "2024-11-12T10:00:00Z",
                    "activity_code_id": 2,
                    "attendees": [],
                },
                {
                    "id": "127",
                    "subject": "Later all-day meeting",
                    "all_day": True,
                    "start_date": "2024-11-13T01:00:00Z",
                    "end_date": "2024-11-13T01:00:00Z",
                    "activity_code_id": 2,
                    "attendees": [],
                },
                {
                    "id": "128",
                    "subject": "Excluded Meeting",
                    "start_date": "2024-11-12T12:00:00",
                    "end_date": "2024-11-12T13:00:00",
                    "activity_code_id": 1,  # This one should be filtered out
                    "attendees": [],
                },
                {
                    "id": "129",
                    "subject": "Later Meeting",
                    "start_date": "2024-11-14T10:00:00Z",
                    "end_date": "2024-11-14T11:00:00Z",
                    "activity_code_id": 2,
                    "attendees": [],
                },
                {
                    "id": "130",
                    "subject": "Even later all-day meeting",
                    "all_day": True,
                    "start_date": "2024-11-14T08:00:00Z",
                    "end_date": "2024-11-14T08:00:00Z",
                    "activity_code_id": 2,
                    "attendees": [],
                },
                {
                    "id": "131",
                    "subject": "Earlier meeting",
                    "start_date": "2024-11-11T10:00:00Z",
                    "end_date": "2024-11-11T11:00:00Z",
                    "activity_code_id": 2,
                    "attendees": [],
                },
                {
                    "id": "131",
                    "subject": "Earlier all-day meeting",
                    "all_day": True,
                    "start_date": "2024-11-11T10:00:00Z",
                    "end_date": "2024-11-11T11:00:00Z",
                    "activity_code_id": 2,
                    "attendees": [],
                },
                {
                    "id": "132",
                    "subject": "End-of-meeting overlap",
                    "start_date": "2024-11-11T23:00:00Z",
                    "end_date": "2024-11-12T00:01:00Z",
                    "activity_code_id": 2,
                    "attendees": [],
                },
                {
                    "id": "133",
                    "subject": "Start-of-meeting overlap",
                    "start_date": "2024-11-12T23:59:00Z",
                    "end_date": "2024-11-13T00:01:00Z",
                    "activity_code_id": 2,
                    "attendees": [],
                },
            ]
        }

        mock_get.return_value = mock_response

        with patch.object(Redtail, "_now") as mock_now:
            mock_now.return_value = datetime(2024, 11, 12, 0, 0, tzinfo=timezone.utc)
            self.assertEqual(
                Redtail().fetch_events(user, timedelta(days=1)),  # type: ignore[no-untyped-call]
                [
                    CalendarEvent(
                        id="123",
                        user_specific_id="123",
                        provider="redtail",
                        title="Test Meeting",
                        body=(
                            "Meeting URL: https://meet.google.com/123\nMeeting ID: 123\n"
                            "Alternative URL: https://zoom.us/456"
                        ),
                        start_time=datetime(2024, 11, 12, 10, 0, tzinfo=timezone.utc),
                        end_time=datetime(2024, 11, 12, 11, 0, tzinfo=timezone.utc),
                        all_day=False,
                        participants=[
                            EventParticipant(id="1", name="Test", email_address="<EMAIL>"),
                        ],
                        meeting_urls=[
                            pydantic.HttpUrl("https://teams.microsoft.com/l/meetup-join/123"),
                            pydantic.HttpUrl("https://meet.google.com/123"),
                            pydantic.HttpUrl("https://zoom.us/456"),
                        ],
                    ),
                    CalendarEvent(
                        id="124",
                        user_specific_id="124",
                        provider="redtail",
                        title="None description or location",
                        body=None,
                        start_time=datetime(2024, 11, 12, 11, 0, tzinfo=timezone.utc),
                        end_time=datetime(2024, 11, 12, 12, 0, tzinfo=timezone.utc),
                        all_day=False,
                        participants=[],
                        meeting_urls=[],
                    ),
                    CalendarEvent(
                        id="125",
                        user_specific_id="125",
                        provider="redtail",
                        title="No description or location",
                        body=None,
                        start_time=datetime(2024, 11, 12, 12, 0, tzinfo=timezone.utc),
                        end_time=datetime(2024, 11, 12, 13, 0, tzinfo=timezone.utc),
                        all_day=False,
                        participants=[],
                        meeting_urls=[],
                    ),
                    CalendarEvent(
                        id="126",
                        user_specific_id="126",
                        provider="redtail",
                        title="All-day meeting",
                        body=None,
                        start_time=datetime(2024, 11, 12, tzinfo=timezone.utc),
                        end_time=datetime(2024, 11, 13, tzinfo=timezone.utc),
                        all_day=True,
                        participants=[],
                        meeting_urls=[],
                    ),
                    CalendarEvent(
                        id="127",
                        user_specific_id="127",
                        provider="redtail",
                        title="Later all-day meeting",
                        body=None,
                        start_time=datetime(2024, 11, 13, 0, 0, tzinfo=timezone.utc),
                        end_time=datetime(2024, 11, 14, 0, 0, tzinfo=timezone.utc),
                        all_day=True,
                        participants=[],
                        meeting_urls=[],
                    ),
                    CalendarEvent(
                        id="132",
                        user_specific_id="132",
                        provider="redtail",
                        title="End-of-meeting overlap",
                        body=None,
                        start_time=datetime(2024, 11, 11, 23, 0, tzinfo=timezone.utc),
                        end_time=datetime(2024, 11, 12, 0, 1, tzinfo=timezone.utc),
                        all_day=False,
                        participants=[],
                        meeting_urls=[],
                    ),
                    CalendarEvent(
                        id="133",
                        user_specific_id="133",
                        provider="redtail",
                        title="Start-of-meeting overlap",
                        body=None,
                        start_time=datetime(2024, 11, 12, 23, 59, tzinfo=timezone.utc),
                        end_time=datetime(2024, 11, 13, 0, 1, tzinfo=timezone.utc),
                        all_day=False,
                        participants=[],
                        meeting_urls=[],
                    ),
                ],
            )

        mock_get.assert_called_once()
        args, kwargs = mock_get.call_args
        assert args == ("https://api.redtailtechnology.com/crm/v1/activities",)
        assert kwargs["headers"]["Authorization"]
        assert kwargs["headers"]["include"] == "category, note_type, attachments, linked_contacts, linked_deals"
        start_date = datetime.fromisoformat(kwargs["params"]["start_date"])
        end_date = datetime.fromisoformat(kwargs["params"]["end_date"])
        assert (end_date - (start_date + timedelta(days=1))) < timedelta(seconds=1)

    @patch("deepinsights.core.integrations.crm.redtail.requests")
    def test_fetch_notes_for_client_success(self, mock_requests: MagicMock) -> None:
        org = Organization.objects.create(name="Test Org")
        client = Client.objects.create(organization=org, crm_id="123")
        user = User.objects.create(username="<EMAIL>", organization=org)

        mock_requests.get.return_value = MagicMock(
            status_code=200,
            json=lambda: {
                "notes": [
                    {"id": 1, "body": "Test note 1", "created_at": "2023-08-01T12:00:00Z"},
                    {"id": 2, "body": "Test note 2", "created_at": "2023-08-02T12:00:00Z"},
                ],
            },
        )

        notes = self.redtail.fetch_notes_for_client(user, client, timedelta(days=365))

        self.assertEqual(
            notes,
            [
                CRMNote(
                    crm_id="1",
                    content="Test note 1",
                    created_at=datetime.fromisoformat("2023-08-01T12:00:00Z"),
                    crm_system="redtail",
                    web_link="https://crm.redtailtechnology.com/contacts/123/activities/1",
                ),
                CRMNote(
                    crm_id="2",
                    crm_system="redtail",
                    content="Test note 2",
                    created_at=datetime.fromisoformat("2023-08-02T12:00:00Z"),
                    web_link="https://crm.redtailtechnology.com/contacts/123/activities/2",
                ),
            ],
        )

    @patch("deepinsights.core.integrations.crm.redtail.requests")
    def test_fetch_notes_for_client_empty_notes(self, mock_requests: MagicMock) -> None:
        org = Organization.objects.create(name="Test Org")
        client = Client.objects.create(organization=org)
        user = User.objects.create(username="<EMAIL>", organization=org)

        mock_requests.get.return_value = MagicMock(status_code=200, json=lambda: {"notes": []})

        notes = self.redtail.fetch_notes_for_client(user, client, timedelta(days=365))

        self.assertEqual(notes, [])

    @patch("deepinsights.core.integrations.crm.redtail.requests")
    def test_fetch_notes_for_client_error(self, mock_requests: MagicMock) -> None:
        org = Organization.objects.create(name="Test Org")
        client = Client.objects.create(organization=org)
        user = User.objects.create(username="<EMAIL>", organization=org)

        mock_requests.get.return_value = MagicMock(status_code=400, json=lambda: {"error": "Bad Request"})

        with self.assertLogs(None, level="ERROR") as log:
            self.assertEqual(self.redtail.fetch_notes_for_client(user, client, timedelta(days=365)), [])
            self.assertIn("Error fetching notes from Redtail for client", log.output[0])

    @patch("deepinsights.core.integrations.crm.redtail.requests")
    def test_fetch_notes_for_invalid_timestamp(self, mock_requests: MagicMock) -> None:
        org = Organization.objects.create(name="Test Org")
        client = Client.objects.create(organization=org, crm_id="123")
        user = User.objects.create(username="<EMAIL>", organization=org)

        mock_requests.get.return_value = MagicMock(
            status_code=200,
            json=lambda: {
                "notes": [{"id": 1, "body": "Test note 1", "created_at": "unparseable"}],
            },
        )

        with self.assertLogs(None, level="ERROR") as log:
            self.assertEqual(
                self.redtail.fetch_notes_for_client(user, client, timedelta(days=365)),
                [
                    CRMNote(
                        crm_id="1",
                        crm_system="redtail",
                        content="Test note 1",
                        created_at=None,
                        web_link="https://crm.redtailtechnology.com/contacts/123/activities/1",
                    ),
                ],
            )
            self.assertIn("Error parsing Redtail note created_at", log.output[0])
