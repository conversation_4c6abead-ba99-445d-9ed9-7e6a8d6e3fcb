import base64
import datetime
import enum
import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Mapping

from simple_salesforce.api import Salesforce as SimpleSalesforce
from simple_salesforce.format import format_soql

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.crm import salesforce_utils
from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.crm_models import (
    CRMAccount,
    CRMNote,
    CRMNoteType,
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTarget,
    CRMSyncTargets,
    CRMUser,
    CRMWorkflow,
)
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


class Salesforce(CrmBase):
    def __init__(
        self,
        *,
        user: User | None = None,
        username: str | None = None,
        password: str | None = None,
        consumer_key: str | None = None,
        consumer_secret: str | None = None,
        security_token: str = "",
        instance_url: str | None = None,
        sf: SimpleSalesforce | None = None,
    ) -> None:
        super().__init__()
        self.sf = salesforce_utils.simple_salesforce_intstance_with_credentials(
            user=user,
            username=username,
            password=password,
            consumer_key=consumer_key,
            consumer_secret=consumer_secret,
            security_token=security_token,
            instance_url=instance_url,
            sf=sf,
        )

    class NoteType(enum.StrEnum):
        STANDARD_NOTE = "standard_note"
        CONTENT_NOTE = "content_note"
        TASK = "task"

    def get_accounts_by_owner_email_and_name(
        self, owner_email: str, account_name_filter: str | None = None
    ) -> list[CRMAccount]:
        entities = salesforce_utils.get_salesforce_entity_by_owner_email_and_name(
            self.sf, "Account", owner_email, account_name_filter
        )
        entities.extend(
            salesforce_utils.get_salesforce_entity_by_owner_email_and_name(
                self.sf, "Contact", owner_email, account_name_filter
            )
        )
        return entities

    def resolve_sync_targets(
        self, note: Note, user: User, selected_sync_targets: CRMSyncTargets | None = None
    ) -> CRMSyncTargets:
        if not note.client:
            return CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])

        client_uuid = note.client.get("uuid", "")
        if not client_uuid:
            return CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])

        try:
            client = Client.objects.get(uuid=client_uuid)
            sync_target = CRMSyncTarget(crm_id=client.crm_id or client_uuid, type="", name=client.name)
            return CRMSyncTargets(status=CRMSyncTargets.Status.FINAL, note_targets=[sync_target])
        # Catch more than just Client.DoesNotExist to handle cases where client_uuid is not a valid UUID.
        except Exception:
            logging.warning(
                "Client with UUID %s not found in database. Assuming the UUID is actually a CRM ID.", client_uuid
            )
            # If client not found in database, assume UUID is the CRM ID
            sync_target = CRMSyncTarget(crm_id=client_uuid, type="", name=note.client.get("name", ""))
            return CRMSyncTargets(status=CRMSyncTargets.Status.FINAL, note_targets=[sync_target])

    # Attempts to add a ContentNote and ContentDocumentLink to Salesforce.
    #
    # This does not catch any errors, but throws them up the stack so that the caller can handle them.
    def __add_content_note(self, owner_id: str, meeting_title: str, client_id: str, content: str) -> str:
        salesforce_note = {
            "OwnerId": owner_id,
            "Title": meeting_title,
            "Content": base64.b64encode(content.encode("utf-8")).decode("utf-8"),
        }
        logging.info("recording ContentNote: %s", salesforce_note)
        note_id = self.sf.ContentNote.create(salesforce_note)["id"]  # type: ignore[operator]
        try:
            self.sf.ContentDocumentLink.create(  # type: ignore[operator]
                {
                    "ContentDocumentId": note_id,
                    "LinkedEntityId": client_id,
                }
            )
        except Exception as e:
            self.sf.ContentNote.delete(note_id)  # type: ignore[operator]
            raise
        return note_id  # type: ignore[no-any-return]

    # Attempts to add a Note to Salesforce.
    #
    # This does not catch any errors, but throws them up the stack so that the caller can handle them.
    def __add_note(self, owner_id: str, meeting_title: str, client_id: str, content: str) -> str:
        salesforce_note = {
            "OwnerId": owner_id,
            "Title": meeting_title,
            "ParentId": client_id,
            "Body": content,
        }
        logging.info("recording note: %s", salesforce_note)
        return self.sf.Note.create(salesforce_note)["id"]  # type: ignore[no-any-return, operator]

    def add_interaction_with_client(
        self,
        note: Note,
        sync_targets: CRMSyncTargets,
        sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = None,
    ) -> None:
        owner_email = note.note_owner.email if note.note_owner else None
        owner = salesforce_utils.get_user_id_by_email(self.sf, owner_email)
        if not owner:
            error_msg = "Could not find a Salesforce User with the given email: %s. Not adding interaction." % (
                owner_email,
            )
            logging.error(error_msg)
            raise ValueError(error_msg)

        if not sync_targets.note_targets:
            error_msg = "Note does not have an associated Salesforce CRM client. Not adding interaction."
            logging.error(error_msg)
            raise ValueError(error_msg)
        if len(sync_targets.note_targets) > 1:
            logging.warning(
                "Multiple clients provided for note %s. Using the first one.",
                [target.crm_id for target in sync_targets.note_targets],
            )

        # Use the first sync target for the interaction
        primary_sync_target = sync_targets.note_targets[0]
        client_crm_id = primary_sync_target.crm_id

        meeting_name = note.metadata.get("meeting_name") or "Meeting with a client"  # type: ignore[union-attr]

        interaction_id = note.metadata.get("interactionId")  # type: ignore[union-attr]
        if not interaction_id:
            try:
                # Try recording the interaction with a ContentNote.
                interaction_id = self.__add_content_note(
                    owner,
                    meeting_name,
                    client_crm_id,
                    note.get_summary_for_crm(use_html_formatting=True, sync_items=sync_items),
                )
            except Exception as e:
                # If the ContentNote can't be created, try creating a Note instead.
                try:
                    interaction_id = self.__add_note(
                        owner,
                        meeting_name,
                        client_crm_id,
                        note.get_summary_for_crm(use_html_formatting=False, sync_items=sync_items),
                    )
                except Exception as e:
                    error_msg = "error recording interaction: %s. Not updating note." % (str(e),)
                    logging.error(error_msg)
                    raise RuntimeError(error_msg) from e
            note.metadata["interactionId"] = interaction_id  # type: ignore[index]
            note.save()
            logging.info("interaction saved")
        else:
            logging.info("Already associated with an interaction: %s", note.metadata.get("interactionId"))  # type: ignore[union-attr]

        if note.should_include_section(sync_items, CRMSyncSection.TASKS):
            for task in note.get_tasks_to_include_crm_sync(sync_items=sync_items):
                assignee_email = getattr(task.assignee, "email", None)
                assignee_id = salesforce_utils.resolve_task_assignee_id(
                    self.sf, assignee_email, fallback_assignee_id=owner
                )
                # First try to add task with a WhoId association (for Contacts)
                try:
                    salesforce_utils.add_task(
                        self.sf,
                        task,
                        assignee_id,
                        {
                            "WhoId": client_crm_id,
                        },
                    )
                except Exception as e:
                    try:
                        # If that fails, add the task with a WhatId association (for Accounts)
                        salesforce_utils.add_task(
                            self.sf,
                            task,
                            assignee_id,
                            {
                                "WhatId": client_crm_id,
                            },
                        )
                    except Exception as e:
                        error_msg = "Error adding task: %s" % (str(e),)
                        logging.error(error_msg)
                        raise RuntimeError(error_msg) from e

    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        logging.warning("get_client_basic_info not implemented for Salesforce")
        return None

    def fetch_events(self, user: User, interval: timedelta) -> list[CalendarEvent]:
        logging.warning("fetch_crm_events not implemented for Salesforce")
        return []

    def _fetch_standard_notes_for_client(self, client_crm_id: str, start_date: datetime.datetime) -> list[CRMNote]:
        parsed_notes: list[CRMNote] = []

        try:
            notes_query = format_soql(
                (
                    "SELECT Id, Title, Body, CreatedDate "
                    "FROM Note "
                    "WHERE ParentId = {} "
                    "AND CreatedDate >= {:literal} "
                    "ORDER BY CreatedDate DESC"
                ),
                client_crm_id,
                salesforce_utils.salesforceTime(start_date),
            )

            note_records = self.sf.query_all(notes_query).get("records", [])
            logging.info("Found %d standard Note records", len(note_records))

            for note in note_records:
                try:
                    created_at = salesforce_utils.datetime_from_salesforce_time(note["CreatedDate"])
                except Exception as e:
                    logging.error("Error parsing CreatedDate for note %s", note["Id"], exc_info=e)
                    created_at = None

                parsed_notes.append(
                    CRMNote(
                        crm_system="salesforce",
                        crm_id=note["Id"],
                        content=f"{note.get('Title', '')}\n{note.get('Body', '')}",
                        created_at=created_at,
                        type=CRMNoteType.SALESFORCE_STANDARD_NOTE,
                        web_link=f"https://{self.sf.sf_instance}/{note['Id']}",
                    )
                )

        except Exception as e:
            logging.error("Error fetching standard Notes: %s", str(e))

        return parsed_notes

    def _fetch_content_notes_for_client(self, client_crm_id: str, start_date: datetime.datetime) -> list[CRMNote]:
        parsed_notes: list[CRMNote] = []

        try:
            content_document_link_query = f"""
                SELECT ContentDocumentId, LinkedEntityId
                FROM ContentDocumentLink
                WHERE LinkedEntityId = '{client_crm_id}'
                ORDER BY ContentDocumentId DESC
            """
            content_document_links = self.sf.query_all(content_document_link_query).get("records", [])
            content_doc_ids = [link["ContentDocumentId"] for link in content_document_links]
            logging.info("Found %d ContentDocumentLinks", len(content_doc_ids))

            if not content_doc_ids:
                logging.info("No ContentDocumentLinks found for client %s", client_crm_id)
                return parsed_notes

            for content_doc_id in content_doc_ids:
                try:
                    content_version_query = format_soql(
                        (
                            "SELECT Id, ContentDocumentId, VersionData, Title, CreatedDate "
                            "FROM ContentVersion "
                            "WHERE ContentDocumentId = {} "
                            "AND CreatedDate >= {:literal} "
                            "ORDER BY CreatedDate DESC"
                        ),
                        content_doc_id,
                        salesforce_utils.salesforceTime(start_date),
                    )
                    content_versions = self.sf.query_all(content_version_query).get("records", [])

                    for version in content_versions:
                        version_data_url = version.get("VersionData")
                        if not version_data_url:
                            continue

                        instance_url = self.sf.sf_instance
                        full_url = f"https://{instance_url}{version_data_url}"
                        try:
                            headers = {"Authorization": f"Bearer {self.sf.session_id}"}
                            response = self.sf.session.get(full_url, headers=headers)
                            response.raise_for_status()
                            content = response.text
                        except Exception as fetch_err:
                            logging.error("Error fetching content from %s: %s", full_url, str(fetch_err))
                            content = "[Error retrieving content]"
                        logging.info(
                            "fetching content for content_doc_id: %s, version_id: %s", content_doc_id, version["Id"]
                        )
                        parsed_notes.append(
                            CRMNote(
                                crm_system="salesforce",
                                crm_id=content_doc_id,
                                content=f"{version.get('Title', '')}\n{content}",
                                created_at=version["CreatedDate"],
                                type=CRMNoteType.SALESFORCE_CONTENT_NOTE,
                                web_link=f"https://{self.sf.sf_instance}/{content_doc_id}",
                            )
                        )

                except Exception as content_note_error:
                    logging.error(
                        "Error fetching ContentVersions for doc ID %s: %s", content_doc_id, str(content_note_error)
                    )

        except Exception as e:
            logging.error("Error fetching ContentNotes: %s", str(e))

        return parsed_notes

    def fetch_notes_for_client(self, user: User, client: Client, lookback_interval: timedelta) -> list[CRMNote]:
        if not client.crm_id:
            logging.error("Client does not have a CRM ID. Cannot fetch notes %s, for user %s", client.uuid, user.uuid)
            return []

        try:
            start_date = datetime.datetime.now(tz=datetime.timezone.utc) - lookback_interval
            all_parsed_notes: list[CRMNote] = []

            all_parsed_notes.extend(self._fetch_standard_notes_for_client(client.crm_id, start_date))
            all_parsed_notes.extend(self._fetch_content_notes_for_client(client.crm_id, start_date))
            all_parsed_notes.extend(salesforce_utils.fetch_events_for_client(self.sf, client.crm_id, start_date))
            all_parsed_notes.extend(salesforce_utils.fetch_call_tasks_for_client(self.sf, client.crm_id, start_date))

            all_parsed_notes.sort(
                key=lambda x: x.created_at or datetime.datetime.min.replace(tzinfo=datetime.timezone.utc), reverse=True
            )

            logging.info("Fetched %s notes/events/tasks for client %s", len(all_parsed_notes), client.crm_id)
            return all_parsed_notes

        except Exception as e:
            logging.error("Error fetching notes for client %s: %s", client.crm_id, str(e), exc_info=True)
            return []

    def fetch_workflows_for_user(self, user: User) -> list[CRMWorkflow]:
        logging.warning("fetch_workflows_for_user not implemented for Salesforce")
        return []

    def list_users(self, requesting_user: User) -> list[CRMUser]:
        if not requesting_user.organization:
            error_msg = "User does not belong to an organization. Cannot list Salesforce CRM users."
            logging.error(error_msg)
            return []

        return salesforce_utils.list_crm_users(self.sf, requesting_user.organization)
