import base64
import logging
from datetime import datetime, timedelta
from datetime import timezone as datetime_timezone
from typing import Any
from unittest import mock
from unittest.mock import ANY, MagicMock, call, patch
from uuid import uuid4

import simple_salesforce
from django.core import cache
from django.test import TestCase, override_settings
from django.utils import timezone
from simple_mockforce import mock_salesforce
from simple_salesforce.api import Salesforce as SimpleSalesforce

from deepinsights.core.integrations.crm.crm_models import (
    CRMAccount,
    CRMNote,
    CRMNoteType,
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTarget,
    CRMSyncTargets,
    CRMUser,
    ZeplynOrganization,
    ZeplynUser,
)
from deepinsights.core.integrations.crm.salesforce import Salesforce
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.flags import Flag
from deepinsights.users.models.user import User


class SalesforceTestCase(TestCase):
    def setUp(self) -> None:
        cache.cache.clear()
        self.simple_salesforce = SimpleSalesforce(instance="test.salesforce.com", session_id="")
        self.salesforce = Salesforce(sf=self.simple_salesforce)
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, False)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, False)

    # Sets the provided flag to the given active state.
    def setFlagActive(self, flag: Flags, is_active: bool) -> None:
        f = Flag.objects.get(name=flag.name)
        f.everyone = is_active
        f.override_enabled_by_environment = None  # To ensure no environment override
        f.save()

    # Populate the required Salesforce types in the mock, so that they can be used in queries even
    # if they have never been created.
    def populateSalesforceTypes(self) -> None:
        for type in [
            self.simple_salesforce.Note,
            self.simple_salesforce.Task,
            self.simple_salesforce.User,
            self.simple_salesforce.ContentNote,
            self.simple_salesforce.ContentDocumentLink,
            self.simple_salesforce.Event,
        ]:
            id = type.create({})["id"]  # type: ignore[operator]
            type.delete(id)  # type: ignore[operator]

    # Populate ContentNote and ContentDocumentType mock, so that they can be used in queries even if
    # they have never been created.
    def populateContentNoteAndContentDocumentLink(self) -> None:
        for type in [
            self.simple_salesforce.ContentNote,
            self.simple_salesforce.ContentDocumentLink,
        ]:
            id = type.create({})["id"]  # type: ignore[operator]
            type.delete(id)  # type: ignore[operator]

    # Creates a new contact with the given name (or a randomized name if the name parameter is None).
    def newContactAndClient(
        self, name: str | None, owner_id: str | None, org: Organization, *, phone: str | None = None
    ) -> tuple[str, Client]:
        contact_name = name.replace("'", "\\'") if name else f"Test Account {uuid4()}"
        data: dict[str, Any] = {
            "Name": contact_name,
            "OwnerId": owner_id,
        }
        if phone:
            data["Phone"] = phone
        contact = self.simple_salesforce.Contact.create(data)  # type: ignore[operator]
        client = Client.objects.create(crm_id=contact["id"], name=contact_name, organization=org)
        self.assertFalse(contact["errors"])
        return (contact["id"], client)

    # Creates a new Account with the given name (or a randomized name if the name parameter is None).
    def newAccountAndClient(
        self, name: str | None, owner_id: str | None, org: Organization, *, phone: str | None = None
    ) -> tuple[str, Client]:
        account_name = name.replace("'", "\\'") if name else f"Test Contact {uuid4()}"
        data: dict[str, Any] = {
            "Name": account_name,
            "OwnerId": owner_id,
        }
        if phone:
            data["Phone"] = phone
        account = self.simple_salesforce.Account.create(data)  # type: ignore[operator]
        self.assertFalse(account["errors"])
        client = Client.objects.create(crm_id=account["id"], name=account_name, organization=org)
        return (account["id"], client)

    # Creates a new arbitrary user, both in the Salesforce and Zeplyn databases.
    #
    # Returns a tuple of (salesforceUserId, userEmail).
    def newUser(self, org: Organization) -> tuple[str, str]:
        email = f"{uuid4()}@example.com"
        user = self.simple_salesforce.User.create({"Email": email})  # type: ignore[operator]
        zeplyn_user = User.objects.create(username=email, email=email, organization=org)
        zeplyn_user.crm_configuration["crm_system"] = "salesforce"
        zeplyn_user.save()
        return (user["id"], email)

    @override_settings(SALESFORCE_USER="user", SALESFORCE_PASSWORD="password", SALESFORCE_INSTANCE="instance")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_from_settings(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        Salesforce(user=user)
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="", instance_url="instance"
        )

    @override_settings(
        SALESFORCE_USER="settings_user",
        SALESFORCE_PASSWORD="settings_password",
        SALESFORCE_INSTANCE="settings_instance",
    )
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_with_params(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        Salesforce(user=user, username="user", password="password", security_token="token", instance_url="instance")
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_for_comsumer_key_missing_values(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        Salesforce(user=user, username="user", password="password", security_token="token", instance_url="instance")
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @override_settings(SALESFORCE_ZEPLYN_APP_DOMAIN="app_domain")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_for_comsumer_key_flag_disabled(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, False)
        Salesforce(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @override_settings(SALESFORCE_ZEPLYN_APP_DOMAIN="app_domain")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_for_comsumer_key(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        Salesforce(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
            security_token="token",
            instance_url="instance",
            domain="app_domain",
        )

    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_for_user_oauth_missing_values(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, True)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        Salesforce(user=user, username="user", password="password", security_token="token", instance_url="instance")
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @override_settings(SALESFORCE_ZEPLYN_APP_DOMAIN="app_domain")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.SalesforceOAuth")
    def test_init_for_user_oauth_flag_disabled(self, sf_oauth: MagicMock, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, False)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, False)
        sf_oauth.return_value.get_access_token.return_value = "access_token"
        Salesforce(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @override_settings(SALESFORCE_ZEPLYN_APP_DOMAIN="app_domain")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.SalesforceOAuth")
    def test_init_for_user_oauth_token_exception(self, sf_oauth: MagicMock, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, True)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        sf_oauth.return_value.get_access_token.side_effect = Exception("Test error")
        Salesforce(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
            security_token="token",
            instance_url="instance",
            domain="app_domain",
        )

    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.SalesforceOAuth")
    def test_init_for_user_oauth(self, sf_oauth: MagicMock, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, True)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        sf_oauth.return_value.get_access_token.return_value = "access_token"
        Salesforce(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(session_id="access_token", instance_url="instance")

    @mock_salesforce
    def test_get_accounts_by_owner_email_and_name(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_one_id, user_one_email = self.newUser(org=org)
        user_two_id, user_two_email = self.newUser(org=org)
        contact_one_id, _ = self.newContactAndClient("one", user_one_id, org)
        contact_two_id, _ = self.newContactAndClient("two", user_one_id, org, phone="**********")
        contact_three_id, _ = self.newContactAndClient("three", user_two_id, org)
        account_one_id, _ = self.newAccountAndClient("one", user_one_id, org, phone="**********")
        account_two_id, _ = self.newAccountAndClient("two", user_one_id, org, phone="invalid")
        account_three_id, _ = self.newAccountAndClient("three", user_two_id, org, phone="+*************")

        def expected(user_one_is_owner: bool) -> list[CRMAccount]:
            return [
                CRMAccount(
                    crm_id=account_one_id,
                    name="one",
                    phone_number="**********",
                    client_type="account",
                    crm_system="salesforce",
                    is_owned_by_user=user_one_is_owner,
                ),
                CRMAccount(
                    crm_id=account_two_id,
                    name="two",
                    client_type="account",
                    crm_system="salesforce",
                    is_owned_by_user=user_one_is_owner,
                ),
                CRMAccount(
                    crm_id=account_three_id,
                    name="three",
                    phone_number="+*************",
                    client_type="account",
                    crm_system="salesforce",
                    is_owned_by_user=not user_one_is_owner,
                ),
                CRMAccount(
                    crm_id=contact_one_id,
                    name="one",
                    client_type="contact",
                    crm_system="salesforce",
                    is_owned_by_user=user_one_is_owner,
                ),
                CRMAccount(
                    crm_id=contact_two_id,
                    name="two",
                    phone_number="**********",
                    client_type="contact",
                    crm_system="salesforce",
                    is_owned_by_user=user_one_is_owner,
                ),
                CRMAccount(
                    crm_id=contact_three_id,
                    name="three",
                    client_type="contact",
                    crm_system="salesforce",
                    is_owned_by_user=not user_one_is_owner,
                ),
            ]

        self.assertEqual(self.salesforce.get_accounts_by_owner_email_and_name(user_one_email), expected(True))
        self.assertEqual(self.salesforce.get_accounts_by_owner_email_and_name(user_two_email), expected(False))
        self.assertEqual(self.salesforce.get_accounts_by_owner_email_and_name("doesnotexist"), [])

    @patch("deepinsights.core.integrations.crm.salesforce_utils.get_user_id_by_email")
    def test_get_accounts_by_owner_email_and_name_filtering(self, mock_get_user_id: MagicMock) -> None:
        mock_salesforce = MagicMock()
        mock_get_user_id.return_value = "001USER1"

        # simple_mockforce does not properly mock LIKE in where clauses, so we need to test this by-hand.
        salesforce = Salesforce(sf=mock_salesforce)
        salesforce.get_accounts_by_owner_email_and_name("test_example.com", "o'ne")

        self.assertEqual(mock_salesforce.query_all.call_count, 2)
        mock_salesforce.query_all.assert_has_calls(
            [
                call("SELECT Id, Name, Phone, OwnerId FROM Account WHERE Name LIKE '%o\\'ne%'"),
                call("SELECT Id, Name, Phone, OwnerId FROM Contact WHERE Name LIKE '%o\\'ne%'"),
            ],
            any_order=True,
        )

    @mock_salesforce
    def test_get_accounts_by_owner_email_and_name_with_quotes(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_one_id, user_one_email = self.newUser(org)
        contact_id, _ = self.newContactAndClient("o'n'e", user_one_id, org)
        account_id, _ = self.newAccountAndClient("t'w'o", user_one_id, org)

        actual_result = self.salesforce.get_accounts_by_owner_email_and_name(user_one_email, "o'")

        expected_accounts = [
            CRMAccount(
                crm_id=account_id,
                name="t\\'w\\'o",
                client_type="account",
                crm_system="salesforce",
                is_owned_by_user=True,
            ),
            CRMAccount(
                crm_id=contact_id,
                name="o\\'n\\'e",
                client_type="contact",
                crm_system="salesforce",
                is_owned_by_user=True,
            ),
        ]

        self.assertEqual(actual_result, expected_accounts)

    @mock_salesforce
    def test_add_interaction_with_client_with_no_note_owner(self) -> None:
        self.populateSalesforceTypes()
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>")
        note = Note(
            status="scheduled",
            client={"uuid": "123"},
            metadata={},
        )

        with self.assertRaisesRegex(
            ValueError,
            "Could not find a Salesforce User with the given email: None. Not adding interaction.",
        ):
            sync_targets = self.salesforce.resolve_sync_targets(note, user)
            self.salesforce.add_interaction_with_client(note, sync_targets)

        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Note")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentDocumentLink")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"])

    @mock_salesforce
    def test_add_interaction_with_client_with_no_contact(self) -> None:
        self.populateSalesforceTypes()
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>")
        note = Note(
            note_owner=user,
            status="scheduled",
            client={"uuid": "123"},
            metadata={},
        )

        with self.assertRaisesRegex(
            ValueError,
            "Could not find a Salesforce User with the given email: <EMAIL>. Not adding interaction.",
        ):
            sync_targets = self.salesforce.resolve_sync_targets(note, user)
            self.salesforce.add_interaction_with_client(note, sync_targets)

        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Note")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentDocumentLink")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"])

    @mock_salesforce
    def test_add_interaction_with_client_with_no_client_id(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
        )

        with self.assertRaisesRegex(
            ValueError, "Note does not have an associated Salesforce CRM client. Not adding interaction."
        ):
            sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
            self.salesforce.add_interaction_with_client(note, sync_targets)

        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Note")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentDocumentLink")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"])

    @mock_salesforce
    def test_add_interaction_creation_with_salesforce_all_error(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        _, client = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        def raiseException(*args, **kwargs):  # type: ignore[no-untyped-def]
            raise AssertionError("Test error")

        raiseException.create = raiseException  # type: ignore[attr-defined]
        self.simple_salesforce.Note = raiseException  # type: ignore[attr-defined]
        self.simple_salesforce.ContentNote = raiseException  # type: ignore[attr-defined]

        with self.assertRaisesRegex(RuntimeError, "error recording interaction: Test error. Not updating note."):
            sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
            self.salesforce.add_interaction_with_client(note, sync_targets)

        note.refresh_from_db()
        # Verify no records were created
        self.assertFalse((note.metadata or {}).get("interactionId"))
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Note")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentDocumentLink")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"])

    @mock_salesforce
    def test_add_interaction_with_client_multiple_clients(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        with patch.object(
            note, "get_summary_for_crm", return_value="Test summary"
        ) as get_summary_for_crm, self.assertLogs(level=logging.WARNING) as cm:
            sync_targets = CRMSyncTargets(
                status=CRMSyncTargets.Status.FINAL,
                note_targets=[
                    CRMSyncTarget(crm_id=account_id, type="", name="Account one"),
                    CRMSyncTarget(crm_id="***************", type="", name=""),
                ],
            )
            self.salesforce.add_interaction_with_client(note, sync_targets)
            get_summary_for_crm.assert_called_once_with(use_html_formatting=True, sync_items=None)

            # simple_mockforce logs a warning, so there will be two log entries.
            self.assertEqual(len(cm.output), 2)
            self.assertIn("Multiple clients provided for note", cm.output[1])

    @mock_salesforce
    def test_add_interaction_with_client_creates_content_note(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        with patch.object(note, "get_summary_for_crm", return_value="Test summary") as get_summary_for_crm:
            sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
            self.salesforce.add_interaction_with_client(note, sync_targets)
            get_summary_for_crm.assert_called_once_with(use_html_formatting=True, sync_items=None)

        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Note")["records"])
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"]), 1)
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM ContentDocumentLink")["records"]), 1)
        salesforce_content_note = self.simple_salesforce.ContentNote.get((note.metadata or {})["interactionId"])  # type: ignore[operator]
        self.assertEqual(salesforce_content_note["OwnerId"], user_id)
        self.assertEqual(salesforce_content_note["Title"], "Meeting with a client")
        self.assertEqual(
            salesforce_content_note["Content"], base64.b64encode("Test summary".encode("utf-8")).decode("utf-8")
        )
        document_link_id = self.simple_salesforce.query_all("SELECT Id FROM ContentDocumentLink")["records"][0]["Id"]
        salesforce_content_document_link = self.simple_salesforce.ContentDocumentLink.get(document_link_id)  # type: ignore[operator]
        self.assertEqual(salesforce_content_document_link["ContentDocumentId"], (note.metadata or {})["interactionId"])
        self.assertEqual(salesforce_content_document_link["LinkedEntityId"], account_id)

    @mock_salesforce
    def test_add_interaction_with_client_uses_uuid_as_crm_id(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, _ = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": account_id},
            created=timezone.now(),
        )

        with patch.object(note, "get_summary_for_crm", return_value="Test summary") as get_summary_for_crm:
            sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
            self.salesforce.add_interaction_with_client(note, sync_targets)

        document_link_id = self.simple_salesforce.query_all("SELECT Id FROM ContentDocumentLink")["records"][0]["Id"]
        salesforce_content_document_link = self.simple_salesforce.ContentDocumentLink.get(document_link_id)  # type: ignore[operator]
        self.assertEqual(salesforce_content_document_link["LinkedEntityId"], account_id)

    @mock_salesforce
    def test_add_interaction_with_client_creates_note_for_cotent_document_failure(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        def raiseException(*args, **kwargs):  # type: ignore[no-untyped-def]
            raise AssertionError

        self.simple_salesforce.ContentDocumentLink = raiseException  # type: ignore[attr-defined]

        with patch.object(note, "get_summary_for_crm", return_value="Test summary"):
            sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
            self.salesforce.add_interaction_with_client(note, sync_targets)
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentDocumentLink")["records"])
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM Note")["records"]), 1)
        salesforce_note = self.simple_salesforce.Note.get((note.metadata or {})["interactionId"])  # type: ignore[operator]
        self.assertEqual(salesforce_note["OwnerId"], user_id)
        self.assertEqual(salesforce_note["Title"], "Meeting with a client")
        self.assertEqual(salesforce_note["ParentId"], account_id)
        self.assertEqual(salesforce_note["Body"], "Test summary")

    @mock_salesforce
    def test_add_interaction_with_client_creates_note(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        def raiseException(*args, **kwargs):  # type: ignore[no-untyped-def]
            raise AssertionError

        self.simple_salesforce.ContentNote = raiseException  # type: ignore[attr-defined]

        with patch.object(note, "get_summary_for_crm", return_value="Test summary"):
            sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
            self.salesforce.add_interaction_with_client(note, sync_targets)
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentDocumentLink")["records"])
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM Note")["records"]), 1)
        salesforce_note = self.simple_salesforce.Note.get((note.metadata or {})["interactionId"])  # type: ignore[operator]
        self.assertEqual(salesforce_note["OwnerId"], user_id)
        self.assertEqual(salesforce_note["Title"], "Meeting with a client")
        self.assertEqual(salesforce_note["ParentId"], account_id)
        self.assertEqual(salesforce_note["Body"], "Test summary")

    @mock_salesforce
    def test_add_interaction_with_client_populates_meeting_title_for_content_note(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        _, client = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={"meeting_name": "Meeting"},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
        self.salesforce.add_interaction_with_client(note, sync_targets)

        salesforce_content_note = self.simple_salesforce.ContentNote.get((note.metadata or {})["interactionId"])  # type: ignore[operator]
        self.assertEqual(salesforce_content_note["Title"], "Meeting")

    @mock_salesforce
    def test_add_interaction_with_client_populates_meeting_title_for_note(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        _, client = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={"meeting_name": "Meeting"},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        def raiseException(*args, **kwargs):  # type: ignore[no-untyped-def]
            raise AssertionError

        self.simple_salesforce.ContentNote = raiseException  # type: ignore[attr-defined]

        sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
        self.salesforce.add_interaction_with_client(note, sync_targets)
        salesforce_note = self.simple_salesforce.Note.get((note.metadata or {})["interactionId"])  # type: ignore[operator]
        self.assertEqual(salesforce_note["Title"], "Meeting")

    @mock_salesforce
    def test_add_interaction_with_client_does_not_update_interaction(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        _, client = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )
        sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
        self.salesforce.add_interaction_with_client(note, sync_targets)

        # Adding a new interaction for the same note should not change anything in the Salesforce database.
        note.metadata = {**(note.metadata or {}), "meeting_name": "New meeting name"}
        sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
        self.salesforce.add_interaction_with_client(note, sync_targets)
        salesforce_note = self.simple_salesforce.ContentNote.get((note.metadata or {})["interactionId"])  # type: ignore[operator]
        self.assertEqual(salesforce_note["Title"], "Meeting with a client")

        # Remove the link between the meeting and the interaction and retry.
        note.metadata = {**(note.metadata or {}), "interactionId": None}
        sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
        self.salesforce.add_interaction_with_client(note, sync_targets)
        salesforce_note = self.simple_salesforce.ContentNote.get((note.metadata or {})["interactionId"])  # type: ignore[operator]
        self.assertEqual(salesforce_note["Title"], "New meeting name")

    @mock_salesforce
    def test_add_interaction_creation_task_creation(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        task = Task.objects.create(task_title="Task One", note=note)
        no_metadata_task = Task.objects.create(
            task_title="Task Two", task_desc="Task two description", note=note, metadata=None
        )
        completed_task = Task.objects.create(
            task_title="Completed Task", task_desc="Completed task description", note=note, completed=True
        )
        already_existing_task = Task.objects.create(
            task_title="Already exsting Task",
            task_desc="Already existing task description",
            note=note,
            metadata={"taskId": "123"},
        )

        note.task_set.add(task)
        note.task_set.add(no_metadata_task)
        note.task_set.add(completed_task)
        note.task_set.add(already_existing_task)

        sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
        self.salesforce.add_interaction_with_client(note, sync_targets)

        self.assertTrue(len(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"]), 3)

        task = Task.objects.get(pk=task.pk)
        salesforce_task = self.simple_salesforce.Task.get(task.metadata["taskId"])  # type: ignore[index, operator]
        self.assertEqual(salesforce_task["Subject"], "Task One")
        self.assertEqual(salesforce_task["Description"], "Follow up from client meeting.")
        self.assertEqual(salesforce_task["Status"], "Not Started")
        self.assertEqual(salesforce_task["Priority"], "Normal")
        self.assertEqual(salesforce_task["WhoId"], account_id)
        self.assertEqual(
            salesforce_task["ActivityDate"],
            (datetime.today().date() + timedelta(7)).strftime("%Y-%m-%dT%H:%M:%S.%f%z"),
        )
        self.assertEqual(salesforce_task["OwnerId"], user_id)

        no_metadata_task = Task.objects.get(pk=no_metadata_task.pk)
        salesforce_no_metadata_task = self.simple_salesforce.Task.get(no_metadata_task.metadata["taskId"])  # type: ignore[index, operator]
        self.assertEqual(salesforce_no_metadata_task["Subject"], "Task Two")
        self.assertEqual(
            salesforce_no_metadata_task["Description"], "Follow up from client meeting.\nTask two description"
        )
        self.assertEqual(salesforce_no_metadata_task["Status"], "Not Started")
        self.assertEqual(salesforce_no_metadata_task["Priority"], "Normal")
        self.assertEqual(salesforce_no_metadata_task["WhoId"], account_id)
        self.assertEqual(
            salesforce_task["ActivityDate"],
            (datetime.today().date() + timedelta(7)).strftime("%Y-%m-%dT%H:%M:%S.%f%z"),
        )
        self.assertEqual(salesforce_task["OwnerId"], user_id)

        completed_task = Task.objects.get(pk=completed_task.pk)
        salesforce_completed_task = self.simple_salesforce.Task.get(completed_task.metadata["taskId"])  # type: ignore[index, operator]
        self.assertEqual(salesforce_completed_task["Subject"], "Completed Task")
        self.assertEqual(
            salesforce_completed_task["Description"], "Follow up from client meeting.\nCompleted task description"
        )
        self.assertEqual(salesforce_completed_task["Status"], "Completed")
        self.assertEqual(salesforce_completed_task["Priority"], "Normal")
        self.assertEqual(salesforce_completed_task["WhoId"], account_id)
        self.assertEqual(
            salesforce_task["ActivityDate"],
            (datetime.today().date() + timedelta(7)).strftime("%Y-%m-%dT%H:%M:%S.%f%z"),
        )
        self.assertEqual(salesforce_task["OwnerId"], user_id)

        already_existing_task = Task.objects.get(pk=already_existing_task.pk)
        with self.assertRaises(simple_salesforce.exceptions.SalesforceResourceNotFound):
            self.simple_salesforce.Task.get(already_existing_task.metadata["taskId"])  # type: ignore[index, operator]

    @mock_salesforce
    def test_sync_fresh_task_creates_salesforce_task(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Org")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newContactAndClient("Client", user_id, org)
        task_assignee_user_id, task_assignee_user_email = self.newUser(org)

        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        task = Task.objects.create(
            task_title="Task One",
            note=note,
            assignee=User.objects.get(email=task_assignee_user_email),
        )
        note.task_set.add(task)

        sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
        self.salesforce.add_interaction_with_client(note, sync_targets)

        task.refresh_from_db()
        assert task.metadata and "taskId" in task.metadata
        salesforce_task = getattr(self.simple_salesforce, "Task").get(task.metadata["taskId"])

        self.assertEqual(salesforce_task["Subject"], "Task One")
        self.assertEqual(salesforce_task["Description"], "Follow up from client meeting.")
        self.assertEqual(salesforce_task["Status"], "Not Started")
        self.assertEqual(salesforce_task["Priority"], "Normal")
        self.assertEqual(salesforce_task["WhoId"], account_id)
        self.assertEqual(salesforce_task["OwnerId"], task_assignee_user_id)

    @mock_salesforce
    def test_sync_task_with_additional_description(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Org")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newContactAndClient("Client", user_id, org)
        task_assignee_user_id, task_assignee_user_email = self.newUser(org)

        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        task = Task.objects.create(
            task_title="Task Two",
            task_desc="Task two description",
            note=note,
            metadata=None,
            assignee=User.objects.get(email=task_assignee_user_email),
        )
        note.task_set.add(task)

        sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
        self.salesforce.add_interaction_with_client(note, sync_targets)

        task.refresh_from_db()
        assert task.metadata and "taskId" in task.metadata
        salesforce_task = getattr(self.simple_salesforce, "Task").get(task.metadata["taskId"])

        self.assertEqual(salesforce_task["Subject"], "Task Two")
        self.assertIn("Task two description", salesforce_task["Description"])
        self.assertEqual(salesforce_task["Status"], "Not Started")
        self.assertEqual(salesforce_task["Priority"], "Normal")
        self.assertEqual(salesforce_task["WhoId"], account_id)
        self.assertEqual(salesforce_task["OwnerId"], task_assignee_user_id)

    @mock_salesforce
    def test_sync_completed_task_sets_status(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Org")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newContactAndClient("Client", user_id, org)
        task_assignee_user_id, task_assignee_user_email = self.newUser(org)

        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        task = Task.objects.create(
            task_title="Completed Task",
            task_desc="Completed task description",
            note=note,
            completed=True,
            assignee=User.objects.get(email=task_assignee_user_email),
        )
        note.task_set.add(task)

        sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
        self.salesforce.add_interaction_with_client(note, sync_targets)

        task.refresh_from_db()
        assert task.metadata and "taskId" in task.metadata
        salesforce_task = getattr(self.simple_salesforce, "Task").get(task.metadata["taskId"])

        self.assertEqual(salesforce_task["Subject"], "Completed Task")
        self.assertEqual(salesforce_task["Status"], "Completed")
        self.assertEqual(salesforce_task["Priority"], "Normal")
        self.assertEqual(salesforce_task["WhoId"], account_id)
        self.assertEqual(salesforce_task["OwnerId"], task_assignee_user_id)

    @mock_salesforce
    def test_already_synced_task_is_not_synced_again(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Org")
        user_id, user_email = self.newUser(org)
        _, client = self.newContactAndClient("Client", user_id, org)
        _, task_assignee_user_email = self.newUser(org)

        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        task = Task.objects.create(
            task_title="Already existing Task",
            task_desc="Already existing task description",
            note=note,
            metadata={"taskId": "123"},
            assignee=User.objects.get(email=task_assignee_user_email),
        )
        note.task_set.add(task)

        sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
        self.salesforce.add_interaction_with_client(note, sync_targets)

        # Salesforce shouldn't overwrite or re-sync this task
        with self.assertRaises(simple_salesforce.exceptions.SalesforceResourceNotFound):
            assert task.metadata and "taskId" in task.metadata
            getattr(self.simple_salesforce, "Task").get(task.metadata["taskId"])

    @mock_salesforce
    def test_add_interaction_task_id_association(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Account one", user_id, org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )
        note.save()

        task = Task(task_title="Task One", note=note)
        task.save()

        note.task_set.add(task)

        with patch("deepinsights.core.integrations.crm.salesforce.salesforce_utils.add_task") as add_task_mock:
            user = User.objects.get(email=user_email)
            sync_targets = self.salesforce.resolve_sync_targets(note, user)
            self.salesforce.add_interaction_with_client(note, sync_targets)
            add_task_mock.assert_called_once_with(
                ANY,
                ANY,
                ANY,
                {"WhoId": account_id},
            )

            # Trigger an add failure to see if the addition is retried with a different ID association.
            add_task_mock.reset()
            add_task_mock.side_effect = [Exception("Test error"), None]

            self.salesforce.add_interaction_with_client(note, sync_targets)

            add_task_mock.assert_has_calls(
                [call(ANY, ANY, ANY, {"WhoId": account_id}), call(ANY, ANY, ANY, {"WhatId": account_id})]
            )

    @mock_salesforce
    def test_add_interaction_sync_items_none_backward_compatibility(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Org")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newContactAndClient("Test Client", user_id, org)

        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(client.uuid)},
            metadata={"interactionId": None},
            created=timezone.now(),
        )

        Task.objects.create(
            note=note,
            task_title="Test Task",
            task_desc="This is a test task.",
            due_date=datetime(2023, 8, 21, tzinfo=datetime_timezone.utc),
        )

        with patch.object(note, "get_summary_for_crm", return_value="test_note_summary") as mock_get_summary:
            sync_targets = self.salesforce.resolve_sync_targets(note, user)
            self.salesforce.add_interaction_with_client(note, sync_targets, None)

            mock_get_summary.assert_called_once_with(use_html_formatting=True, sync_items=None)

        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"]), 1)
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"]), 1)
        self.assertIsNotNone((note.metadata or {})["interactionId"])

    @mock_salesforce
    def test_add_interaction_sync_items_empty_dict_backward_compatibility(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Org")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newContactAndClient("Test Client", user_id, org)

        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(client.uuid)},
            metadata={"interactionId": None},
            created=timezone.now(),
        )

        Task.objects.create(
            note=note,
            task_title="Test Task",
            task_desc="This is a test task.",
            due_date=datetime(2023, 8, 21, tzinfo=datetime_timezone.utc),
        )

        with patch.object(note, "get_summary_for_crm", return_value="test_note_summary") as mock_get_summary:
            sync_targets = self.salesforce.resolve_sync_targets(note, user)
            self.salesforce.add_interaction_with_client(note, sync_targets, {})

            mock_get_summary.assert_called_once_with(use_html_formatting=True, sync_items={})

        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"]), 1)
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"]), 1)

    @mock_salesforce
    def test_add_interaction_sync_items_tasks_enabled_boolean(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Org")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newContactAndClient("Test Client", user_id, org)

        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(client.uuid)},
            metadata={"interactionId": None},
            created=timezone.now(),
        )

        task = Task.objects.create(
            note=note,
            task_title="Test Task",
            task_desc="This is a test task.",
            due_date=datetime(2023, 8, 21, tzinfo=datetime_timezone.utc),
        )

        sync_items = {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True)}

        with patch.object(note, "get_summary_for_crm", return_value="test_note_summary") as mock_get_summary:
            sync_targets = self.salesforce.resolve_sync_targets(note, user)
            self.salesforce.add_interaction_with_client(note, sync_targets, sync_items)

            mock_get_summary.assert_called_once_with(use_html_formatting=True, sync_items=sync_items)

        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"]), 1)
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"]), 1)

        task = Task.objects.get(note=note)
        salesforce_task = self.simple_salesforce.Task.get(task.metadata["taskId"])  # type: ignore[operator, index]
        self.assertEqual(salesforce_task["Subject"], "Test Task")
        self.assertEqual(salesforce_task["Description"], "Follow up from client meeting.\nThis is a test task.")

    @mock_salesforce
    def test_add_interaction_sync_items_tasks_disabled_boolean(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Org")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newContactAndClient("Test Client", user_id, org)

        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(client.uuid)},
            metadata={"interactionId": None},
            created=timezone.now(),
        )

        Task.objects.create(
            note=note,
            task_title="Test Task Should Not Sync",
            task_desc="This task should not be synced.",
            due_date=datetime(2023, 8, 21, tzinfo=datetime_timezone.utc),
        )

        sync_items = {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=False)}

        with patch.object(note, "get_summary_for_crm", return_value="test_note_summary") as mock_get_summary:
            sync_targets = self.salesforce.resolve_sync_targets(note, user)
            self.salesforce.add_interaction_with_client(note, sync_targets, sync_items)

            mock_get_summary.assert_called_once_with(use_html_formatting=True, sync_items=sync_items)

        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"]), 1)
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"]), 0)

        self.assertIsNotNone((note.metadata or {})["interactionId"])

    @mock_salesforce
    def test_add_interaction_sync_items_mixed_boolean_sections(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Org")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newContactAndClient("Test Client", user_id, org)

        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(client.uuid)},
            metadata={"interactionId": None},
            created=timezone.now(),
        )

        task = Task.objects.create(
            note=note,
            task_title="Test Task",
            task_desc="This is a test task.",
            due_date=datetime(2023, 8, 21, tzinfo=datetime_timezone.utc),
        )

        sync_items = {
            CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.ATTENDEES: CRMSyncItemSelection(include_section=False),
            CRMSyncSection.KEYWORDS: CRMSyncItemSelection(include_section=False),
        }

        with patch.object(note, "get_summary_for_crm", return_value="test_note_summary") as mock_get_summary:
            sync_targets = self.salesforce.resolve_sync_targets(note, user)
            self.salesforce.add_interaction_with_client(note, sync_targets, sync_items)

            mock_get_summary.assert_called_once_with(use_html_formatting=True, sync_items=sync_items)

        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"]), 1)
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"]), 1)

    @mock_salesforce
    def test_add_interaction_sync_items_preserves_existing_error_handling(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Org")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)

        note = Note.objects.create(
            note_owner=user,
            metadata={"interactionId": None},
        )

        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[],
        )

        sync_items = {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True, included_items=[])}

        with self.assertLogs(level=logging.ERROR) as cm:
            with self.assertRaisesRegex(
                ValueError, "Note does not have an associated Salesforce CRM client. Not adding interaction."
            ):
                self.salesforce.add_interaction_with_client(note, sync_targets, sync_items)

            self.assertEqual(len(cm.output), 1)
            self.assertIn("Note does not have an associated Salesforce CRM client", cm.output[0])

        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"]), 0)
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"]), 0)

    @mock_salesforce
    def test_add_interaction_sync_items_no_task_calls_when_disabled_even_on_note_error(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Org")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newContactAndClient("Test Client", user_id, org)

        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(client.uuid)},
            metadata={"interactionId": None},
            created=timezone.now(),
        )

        Task.objects.create(
            note=note,
            task_title="Test Task",
            task_desc="This task should not be synced.",
            due_date=datetime(2023, 8, 21, tzinfo=datetime_timezone.utc),
        )

        def raiseException(*args: Any, **kwargs: Any) -> None:
            raise AssertionError("Creation failed")

        self.simple_salesforce.ContentNote = raiseException  # type: ignore[attr-defined]
        self.simple_salesforce.Note = raiseException  # type: ignore[attr-defined]

        sync_items = {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=False)}

        with patch.object(note, "get_summary_for_crm", return_value="test_note_summary"):
            sync_targets = self.salesforce.resolve_sync_targets(note, user)

            with self.assertLogs(level=logging.ERROR):
                with self.assertRaisesRegex(RuntimeError, "error recording interaction"):
                    self.salesforce.add_interaction_with_client(note, sync_targets, sync_items)

        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"]), 0)
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"]), 0)

    @mock_salesforce
    def test_add_interaction_sync_items_uses_should_include_section_correctly(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Org")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newContactAndClient("Test Client", user_id, org)

        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(client.uuid)},
            metadata={"interactionId": None},
            created=timezone.now(),
        )

        task = Task.objects.create(
            note=note,
            task_title="Test Task",
            task_desc="This is a test task.",
            due_date=datetime(2023, 8, 21, tzinfo=datetime_timezone.utc),
        )

        sync_items = {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True)}

        with patch.object(note, "get_summary_for_crm", return_value="test_note_summary"):
            with patch.object(note, "should_include_section", return_value=True) as mock_should_include:
                sync_targets = self.salesforce.resolve_sync_targets(note, user)
                self.salesforce.add_interaction_with_client(note, sync_targets, sync_items)

                mock_should_include.assert_called_with(sync_items, CRMSyncSection.TASKS)

    @mock_salesforce
    def test_add_interaction_backward_compatibility_method_signature(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Org")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newContactAndClient("Test Client", user_id, org)

        note = Note.objects.create(
            note_owner=user,
            client={"uuid": str(client.uuid)},
            metadata={"interactionId": None},
            created=timezone.now(),
        )

        sync_targets = self.salesforce.resolve_sync_targets(note, user)

        try:
            with patch.object(note, "get_summary_for_crm", return_value="test_note_summary"):
                self.salesforce.add_interaction_with_client(note, sync_targets)
            self.assertTrue(True)
        except TypeError as e:
            self.fail(f"Backward compatibility broken: {e}")

    @mock_salesforce
    def test_fetch_notes_for_client_account_successful(self) -> None:
        self.populateContentNoteAndContentDocumentLink()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_id=user_id, org=org)
        contact_id, _ = self.newContactAndClient("Client one", owner_id=user_id, org=org)

        note_creation_time = datetime.now(tz=datetime_timezone.utc)

        note = self.simple_salesforce.Note.create(  # type: ignore[operator]
            {
                "Title": "Test Summary",
                "Body": "Test meeting notes",
                "ParentId": account_id,
            }
        )
        # Update the created date to be a timezone-aware date. There's a bug in simple-mockforce
        # which makes "system" dates (like `CreatedDate`) not timezone-aware.
        self.simple_salesforce.Note.update(  # type: ignore[operator]
            note["id"], {"CreatedDate": note_creation_time.strftime("%Y-%m-%dT%H:%M:%S.%f%z")}
        )

        self.simple_salesforce.Note.create(  # type: ignore[operator]
            {
                "Title": "Test Summary two",
                "Body": "Test meeting notes two",
                "ParentId": contact_id,
            }
        )

        result = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))
        self.assertEqual(
            result,
            [
                CRMNote(
                    crm_id=note["id"],
                    crm_system="salesforce",
                    content="Test Summary\nTest meeting notes",
                    created_at=note_creation_time,
                    type=CRMNoteType.SALESFORCE_STANDARD_NOTE,
                    web_link=f"https://test.salesforce.com/{note['id']}",
                )
            ],
        )

    @mock_salesforce
    def test_fetch_notes_for_client_contact_successful(self) -> None:
        self.populateContentNoteAndContentDocumentLink()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, _ = self.newAccountAndClient("Account one", owner_id=user_id, org=org)
        contact_id, client = self.newContactAndClient("Client one", owner_id=user_id, org=org)

        note_creation_time = datetime.now(tz=datetime_timezone.utc)

        self.simple_salesforce.Note.create(  # type: ignore[operator]
            {
                "Title": "Test Summary",
                "Body": "Test meeting notes",
                "ParentId": account_id,
            }
        )
        # Update the created date to be a timezone-aware date. There's a bug in simple-mockforce
        # which makes "system" dates (like `CreatedDate`) not timezone-aware.

        note = self.simple_salesforce.Note.create(  # type: ignore[operator]
            {
                "Title": "Test Summary two",
                "Body": "Test meeting notes two",
                "ParentId": contact_id,
            }
        )
        self.simple_salesforce.Note.update(  # type: ignore[operator]
            note["id"], {"CreatedDate": note_creation_time.strftime("%Y-%m-%dT%H:%M:%S.%f%z")}
        )

        self.assertEqual(
            self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365)),
            [
                CRMNote(
                    crm_id=note["id"],
                    crm_system="salesforce",
                    content="Test Summary two\nTest meeting notes two",
                    created_at=note_creation_time,
                    type=CRMNoteType.SALESFORCE_STANDARD_NOTE,
                    web_link=f"https://test.salesforce.com/{note['id']}",
                )
            ],
        )

    @mock_salesforce
    def test_fetch_notes_for_client_no_notes(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        _, client = self.newAccountAndClient("Account one", owner_id=user_id, org=org)

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))
        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_notes_for_client_no_crm_id(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        client = Client.objects.create(name="Test Client", organization=org)  # No CRM ID

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))
        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_notes_for_client_date_filter(self) -> None:
        self.populateContentNoteAndContentDocumentLink()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_id=user_id, org=org)

        # Create old note (more than 30 days ago)
        old_date = (datetime.now(tz=datetime_timezone.utc) - timedelta(days=40)).strftime("%Y-%m-%dT%H:%M:%S.%f%z")
        old_note = self.simple_salesforce.Note.create(  # type: ignore[operator]
            {
                "Title": "Test Summary old",
                "Body": "Test meeting notes old",
                "ParentId": account_id,
            }
        )
        self.simple_salesforce.Note.update(  # type: ignore[operator]
            old_note["id"], {"CreatedDate": old_date}
        )

        # Create recent note
        recent_date = datetime.now(datetime_timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f%z")
        recent_note = self.simple_salesforce.Note.create(  # type: ignore[operator]
            {
                "Title": "Test Summary recent",
                "Body": "Test meeting notes recent",
                "ParentId": account_id,
            }
        )
        self.simple_salesforce.Note.update(  # type: ignore[operator]
            recent_note["id"], {"CreatedDate": recent_date}
        )

        # Test with 30 day filter
        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=30))
        self.assertEqual(len(notes), 1)
        self.assertEqual(notes[0].crm_id, recent_note["id"])

    @mock_salesforce
    def test_fetch_notes_for_client_salesforce_error(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        _, client = self.newAccountAndClient("Account one", owner_id=user_id, org=org)

        with patch.object(self.simple_salesforce, "query_all", side_effect=Exception("Salesforce API error")):
            # Fetch notes should handle the exception and return an empty list
            result = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))
            self.assertEqual(result, [])

    @mock_salesforce
    def test_fetch_notes_for_client_multiple_notes(self) -> None:
        self.populateContentNoteAndContentDocumentLink()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_id=user_id, org=org)

        note_ids: list[str] = []
        note_start_time = datetime.now(tz=datetime_timezone.utc)
        for i in range(3):
            note = self.simple_salesforce.Note.create(  # type: ignore[operator]
                {
                    "Title": f"Test Summary {i}",
                    "Body": f"Test meeting notes {i}",
                    "ParentId": account_id,
                }
            )
            self.simple_salesforce.Note.update(  # type: ignore[operator]
                note["id"], {"CreatedDate": note_start_time.strftime("%Y-%m-%dT%H:%M:%S.%f%z")}
            )
            note_ids.append(note["id"])

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))

        self.assertEqual(
            notes,
            [
                CRMNote(
                    crm_id=note_ids[0],
                    crm_system="salesforce",
                    content="Test Summary 0\nTest meeting notes 0",
                    created_at=note_start_time,
                    type=CRMNoteType.SALESFORCE_STANDARD_NOTE,
                    web_link=f"https://test.salesforce.com/{note_ids[0]}",
                ),
                CRMNote(
                    crm_id=note_ids[1],
                    crm_system="salesforce",
                    content="Test Summary 1\nTest meeting notes 1",
                    created_at=note_start_time,
                    type=CRMNoteType.SALESFORCE_STANDARD_NOTE,
                    web_link=f"https://test.salesforce.com/{note_ids[1]}",
                ),
                CRMNote(
                    crm_id=note_ids[2],
                    crm_system="salesforce",
                    content="Test Summary 2\nTest meeting notes 2",
                    created_at=note_start_time,
                    type=CRMNoteType.SALESFORCE_STANDARD_NOTE,
                    web_link=f"https://test.salesforce.com/{note_ids[2]}",
                ),
            ],
        )

    @mock_salesforce
    def test_fetch_notes_for_client_invalid_date(self) -> None:
        # Create empty ContentNote and ContentDocumentLink objects to popualte them in the mock
        # Salesforce instance.
        #
        # ContentDocumentLink is ordered by its ContentDocumentId, so the fake ContentDocumentLink
        # needs to have that key present.
        self.simple_salesforce.ContentNote.create({})  # type: ignore[operator]
        self.simple_salesforce.ContentDocumentLink.create({"ContentDocumentId": ""})  # type: ignore[operator]

        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_id=user_id, org=org)

        note = self.simple_salesforce.Note.create(  # type: ignore[operator]
            {
                "Title": "Test Summary",
                "Body": "Test meeting notes",
                "ParentId": account_id,
            }
        )
        self.simple_salesforce.Note.update(  # type: ignore[operator]
            note["id"], {"CreatedDate": "invalid"}
        )

        self.assertEqual(
            self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365)),
            [
                CRMNote(
                    crm_id=note["id"],
                    crm_system="salesforce",
                    content="Test Summary\nTest meeting notes",
                    created_at=None,
                    type=CRMNoteType.SALESFORCE_STANDARD_NOTE,
                    web_link=f"https://test.salesforce.com/{note['id']}",
                )
            ],
        )

    @mock_salesforce
    def test_fetch_notes_for_client_with_no_crm_id(self) -> None:
        """Test that fetch_notes_for_client returns an empty list when the client has no CRM ID."""
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="test_user", email="<EMAIL>", organization=org)
        client = Client.objects.create(name="Test Client", organization=org)  # No CRM ID

        result = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))

        self.assertEqual(result, [])

    @mock_salesforce
    def test_fetch_notes_for_client_with_content_version_data(self) -> None:
        """Test fetching ContentVersion data from Salesforce API."""
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Test Account", user_id, org)

        content_doc_id = str(uuid4())

        # Mock ContentDocumentLink query results
        content_doc_link_records = [{"ContentDocumentId": content_doc_id, "LinkedEntityId": account_id}]
        content_doc_link_result = {"records": content_doc_link_records}

        # Mock ContentVersion query results
        creation_date = timezone.now()
        version_id = str(uuid4())
        version_data_url = "/services/data/v53.0/sobjects/ContentVersion/123456/VersionData"
        version_records = [
            {
                "Id": version_id,
                "ContentDocumentId": content_doc_id,
                "VersionData": version_data_url,
                "Title": "Content Version Title",
                "CreatedDate": creation_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
            }
        ]
        version_result = {"records": version_records}

        # Create patched query_all that returns our mock data
        def mock_query_all(query: str) -> dict[str, Any]:
            if "ContentDocumentLink" in query:
                return content_doc_link_result
            elif "ContentVersion" in query:
                return version_result
            return {"records": []}

        with patch.object(self.simple_salesforce, "query_all", side_effect=mock_query_all):
            # Mock the session attributes and get method
            self.simple_salesforce.sf_instance = "test.salesforce.com"
            self.simple_salesforce.session_id = "fake_session_id"

            with patch.object(self.simple_salesforce.session, "get") as mock_get:
                mock_response = MagicMock()
                mock_response.text = "This is the ContentVersion data"
                mock_response.raise_for_status = MagicMock()
                mock_get.return_value = mock_response

                # Fetch notes
                user = User.objects.get(email=user_email)
                result = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))

                # Verify session.get was called with correct URL and headers
                expected_url = f"https://test.salesforce.com{version_data_url}"
                expected_headers = {"Authorization": "Bearer fake_session_id"}
                mock_get.assert_called_with(expected_url, headers=expected_headers)

                # Verify content note data was processed correctly
                self.assertEqual(
                    result,
                    [
                        CRMNote(
                            crm_id=content_doc_id,
                            crm_system="salesforce",
                            content="Content Version Title\nThis is the ContentVersion data",
                            created_at=creation_date,
                            type=CRMNoteType.SALESFORCE_CONTENT_NOTE,
                            web_link=f"https://test.salesforce.com/{content_doc_id}",
                        ),
                    ],
                )

    @mock_salesforce
    def test_fetch_notes_for_client_handles_content_version_errors(self) -> None:
        """Test that fetch_notes_for_client handles errors when retrieving ContentVersion data."""
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Test Account", user_id, org)

        # Create ContentNote
        content_doc_id = str(uuid4())

        # Mock ContentDocumentLink query results
        content_doc_link_records = [{"ContentDocumentId": content_doc_id, "LinkedEntityId": account_id}]
        content_doc_link_result = {"records": content_doc_link_records}

        # Mock ContentVersion query results with VersionData URL
        creation_date = timezone.now()
        version_id = str(uuid4())
        version_data_url = "/services/data/v53.0/sobjects/ContentVersion/123456/VersionData"
        version_records = [
            {
                "Id": version_id,
                "ContentDocumentId": content_doc_id,
                "VersionData": version_data_url,
                "Title": "Content Version Title",
                "CreatedDate": creation_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
            }
        ]
        version_result = {"records": version_records}

        # Create patched query_all that returns our mock data
        def mock_query_all(query: str) -> dict[str, Any]:
            if "ContentDocumentLink" in query:
                return content_doc_link_result
            elif "ContentVersion" in query:
                return version_result
            return {"records": []}

        with patch.object(self.simple_salesforce, "query_all", side_effect=mock_query_all):
            # Mock the session attributes
            self.simple_salesforce.sf_instance = "test.salesforce.com"
            self.simple_salesforce.session_id = "fake_session_id"

            with patch.object(self.simple_salesforce.session, "get") as mock_get:
                mock_get.side_effect = Exception("Error fetching content")

                # Fetch notes
                user = User.objects.get(email=user_email)
                result: list[CRMNote] = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))

                # Verify note was still created despite the error fetching content
                self.assertEqual(
                    result,
                    [
                        CRMNote(
                            crm_id=content_doc_id,
                            crm_system="salesforce",
                            content="Content Version Title\n[Error retrieving content]",
                            created_at=creation_date,
                            type=CRMNoteType.SALESFORCE_CONTENT_NOTE,
                            web_link=f"https://test.salesforce.com/{content_doc_id}",
                        )
                    ],
                )

    @mock_salesforce
    def test_fetch_standard_notes_for_client_successful(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Account one", user_id, org)

        note_time = datetime.now(datetime_timezone.utc)
        note = self.simple_salesforce.Note.create(  # type: ignore[operator]
            {
                "Title": "Test Note",
                "Body": "Test note body",
                "ParentId": account_id,
            }
        )
        self.simple_salesforce.Note.update(  # type: ignore[operator]
            note["id"], {"CreatedDate": note_time.strftime("%Y-%m-%dT%H:%M:%S.%f%z")}
        )

        original_query_all = self.simple_salesforce.query_all

        def mock_query_all(query: str, include_deleted: bool = False, **kwargs: Any) -> dict[str, Any]:
            if "FROM Note" in query and "ParentId" in query:
                note_record = self.simple_salesforce.Note.get(note["id"])  # type: ignore[operator]
                if note_record.get("ParentId") == account_id:
                    return {"records": [note_record]}
                return {"records": []}
            else:
                return original_query_all(query, include_deleted, **kwargs)

        with patch.object(self.simple_salesforce, "query_all", side_effect=mock_query_all):
            start_date = datetime.now(datetime_timezone.utc) - timedelta(days=365)
            notes = self.salesforce._fetch_standard_notes_for_client(account_id, start_date)

            self.assertEqual(len(notes), 1)
            self.assertEqual(notes[0].crm_id, note["id"])
            self.assertEqual(notes[0].content, "Test Note\nTest note body")
            self.assertEqual(notes[0].type, CRMNoteType.SALESFORCE_STANDARD_NOTE)
            self.assertEqual(notes[0].created_at, note_time)

    @mock_salesforce
    def test_fetch_standard_notes_for_client_no_notes(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Account one", user_id, org)

        start_date = datetime.now(datetime_timezone.utc) - timedelta(days=365)
        notes = self.salesforce._fetch_standard_notes_for_client(account_id, start_date)
        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_standard_notes_for_client_salesforce_error(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Account one", user_id, org)

        def raise_error(*args, **kwargs):  # type: ignore[no-untyped-def]
            raise Exception("Salesforce API Error")

        original_query = self.simple_salesforce.query_all
        self.simple_salesforce.query_all = raise_error  # type: ignore[method-assign]

        start_date = datetime.now(datetime_timezone.utc) - timedelta(days=365)
        notes = self.salesforce._fetch_standard_notes_for_client(account_id, start_date)

        self.simple_salesforce.query_all = original_query  # type: ignore[method-assign]

        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_content_notes_for_client_successful(self) -> None:
        """Test successful retrieval of content notes"""
        self.populateContentNoteAndContentDocumentLink()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Account one", user_id, org)

        content_doc_id = str(uuid4())

        # Store the original method
        original_query_all = self.simple_salesforce.query_all

        # Mock ContentDocumentLink query results
        def mock_query_all(query: str, include_deleted: bool = False, **kwargs: Any) -> dict[str, Any]:
            if "ContentDocumentLink" in query:
                return {"records": [{"ContentDocumentId": content_doc_id, "LinkedEntityId": account_id}]}
            elif "ContentVersion" in query:
                return {
                    "records": [
                        {
                            "Id": str(uuid4()),
                            "ContentDocumentId": content_doc_id,
                            "VersionData": "/services/data/v53.0/sobjects/ContentVersion/123456/VersionData",
                            "Title": "Content Title",
                            "CreatedDate": datetime.now(datetime_timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                        }
                    ]
                }
            else:
                return original_query_all(query, include_deleted, **kwargs)

        self.simple_salesforce.query_all = mock_query_all  # type: ignore[method-assign]

        # Mock session attributes and response
        self.simple_salesforce.sf_instance = "test.salesforce.com"
        self.simple_salesforce.session_id = "fake_session_id"

        with patch.object(self.simple_salesforce.session, "get") as mock_get:
            mock_response = MagicMock()
            mock_response.text = "Content data"
            mock_response.raise_for_status = MagicMock()
            mock_get.return_value = mock_response

            try:
                start_date = datetime.now(datetime_timezone.utc) - timedelta(days=365)
                notes = self.salesforce._fetch_content_notes_for_client(account_id, start_date)

                self.assertEqual(len(notes), 1)
                self.assertEqual(notes[0].crm_id, content_doc_id)
                self.assertEqual(notes[0].content, "Content Title\nContent data")
                self.assertEqual(notes[0].type, CRMNoteType.SALESFORCE_CONTENT_NOTE)
            finally:
                # Restore original method
                self.simple_salesforce.query_all = original_query_all  # type: ignore[method-assign]

    @mock_salesforce
    def test_fetch_content_notes_for_client_no_links(self) -> None:
        self.populateContentNoteAndContentDocumentLink()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Account one", user_id, org)

        start_date = datetime.now(datetime_timezone.utc) - timedelta(days=365)
        notes = self.salesforce._fetch_content_notes_for_client(account_id, start_date)
        self.assertEqual(notes, [])

    @mock_salesforce
    def test_main_method_combines_all_helpers(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", user_id, org)

        def mock_query_all(*args, **kwargs):  # type: ignore[no-untyped-def]
            query = args[0] if args else ""

            if "Note " in query and "ParentId" in query:
                return {
                    "records": [
                        {
                            "Id": "00P000000000001",
                            "Title": "Test Note",
                            "Body": "Note body",
                            "CreatedDate": "2024-01-15T10:00:00.000Z",
                        }
                    ]
                }
            elif "ContentDocumentLink" in query:
                return {"records": [{"ContentDocumentId": "***************", "LinkedEntityId": account_id}]}
            elif "ContentVersion" in query:
                return {
                    "records": [
                        {
                            "Id": "***************",
                            "ContentDocumentId": "***************",
                            "VersionData": "/services/data/v53.0/sobjects/ContentVersion/123456/VersionData",
                            "Title": "Content Title",
                            "CreatedDate": "2024-01-15T09:30:00.000Z",
                        }
                    ]
                }
            elif "Event" in query:
                return {
                    "records": [
                        {
                            "Id": "00U000000000001",
                            "Subject": "Test Event",
                            "ActivityDateTime": "2024-01-15T09:00:00.000Z",
                            "Description": "Event description",
                        }
                    ]
                }
            elif "Task" in query:
                return {
                    "records": [
                        {
                            "Id": "00T000000000001",
                            "Subject": "Test Call",
                            "ActivityDate": "2024-01-15",
                            "Description": "Call description",
                            "TaskSubtype": "Call",
                        }
                    ]
                }
            return {"records": []}

        original_query = self.simple_salesforce.query_all
        self.simple_salesforce.query_all = mock_query_all  # type: ignore[method-assign]

        # Mock session for content notes
        self.simple_salesforce.sf_instance = "test.salesforce.com"
        self.simple_salesforce.session_id = "fake_session_id"

        with patch.object(self.simple_salesforce.session, "get") as mock_get:
            mock_response = MagicMock()
            mock_response.text = "Content data"
            mock_response.raise_for_status = MagicMock()
            mock_get.return_value = mock_response

            try:
                notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))

                self.assertEqual(len(notes), 4)

                crm_ids = [note.crm_id for note in notes]
                self.assertIn("00P000000000001", crm_ids)  # Standard Note
                self.assertIn("***************", crm_ids)  # Content Note
                self.assertIn("00U000000000001", crm_ids)  # Event
                self.assertIn("00T000000000001", crm_ids)  # Task

            finally:
                self.simple_salesforce.query_all = original_query  # type: ignore[method-assign]

    @mock_salesforce
    def test_fetch_notes_for_client_integration_all_types_with_existing_test_compatibility(self) -> None:
        self.populateContentNoteAndContentDocumentLink()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", user_id, org)

        note_creation_time = datetime.now(tz=datetime_timezone.utc)
        note = self.simple_salesforce.Note.create(  # type: ignore[operator]
            {
                "Title": "Test Summary",
                "Body": "Test meeting notes",
                "ParentId": account_id,
            }
        )
        self.simple_salesforce.Note.update(  # type: ignore[operator]
            note["id"], {"CreatedDate": note_creation_time.strftime("%Y-%m-%dT%H:%M:%S.%f%z")}
        )

        result = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))

        self.assertEqual(len(result), 1)
        self.assertEqual(result[0].crm_id, note["id"])
        self.assertEqual(result[0].content, "Test Summary\nTest meeting notes")
        self.assertEqual(result[0].type, CRMNoteType.SALESFORCE_STANDARD_NOTE)

    @mock_salesforce
    def test_resolve_sync_targets_no_client(self) -> None:
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>")
        note = Note.objects.create(
            note_owner=user,
            status=Note.PROCESSING_STATUS.processed,
            client=None,
        )

        result = self.salesforce.resolve_sync_targets(note, user)

        self.assertEqual(result, CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[]))

    @mock_salesforce
    def test_resolve_sync_targets_empty_client_uuid(self) -> None:
        """Test resolve_sync_targets returns CLIENTS_REQUIRED when client has no uuid."""
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>")
        note = Note.objects.create(
            note_owner=user,
            status="scheduled",
            metadata={},
            client={"name": "Test Client"},  # No uuid field
        )

        result = self.salesforce.resolve_sync_targets(note, user)

        self.assertEqual(result, CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[]))

    @mock_salesforce
    def test_resolve_sync_targets_blank_client_uuid(self) -> None:
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>")
        note = Note.objects.create(
            note_owner=user,
            status=Note.PROCESSING_STATUS.processed,
            client={"uuid": "", "name": "Test Client"},
        )

        result = self.salesforce.resolve_sync_targets(note, user)

        self.assertEqual(result, CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[]))

    @mock_salesforce
    def test_resolve_sync_targets_client_found_with_crm_id(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>", organization=org)
        client = Client.objects.create(
            name="Test Client",
            organization=org,
            crm_id="salesforce_123",
        )
        note = Note.objects.create(
            note_owner=user,
            status=Note.PROCESSING_STATUS.processed,
            client={"uuid": str(client.uuid), "name": "Test Client"},
        )

        result = self.salesforce.resolve_sync_targets(note, user)

        self.assertEqual(
            result,
            CRMSyncTargets(
                status=CRMSyncTargets.Status.FINAL,
                note_targets=[CRMSyncTarget(crm_id="salesforce_123", type="", name="Test Client")],
            ),
        )

    @mock_salesforce
    def test_resolve_sync_targets_client_found_without_crm_id(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>", organization=org)
        client = Client.objects.create(
            name="Test Client",
            organization=org,
            crm_id=None,
        )
        note = Note.objects.create(
            note_owner=user,
            status=Note.PROCESSING_STATUS.processed,
            client={"uuid": str(client.uuid), "name": "Test Client"},
        )

        result = self.salesforce.resolve_sync_targets(note, user)

        self.assertEqual(
            result,
            CRMSyncTargets(
                status=CRMSyncTargets.Status.FINAL,
                note_targets=[CRMSyncTarget(crm_id=str(client.uuid), type="", name="Test Client")],
            ),
        )

    @mock_salesforce
    def test_resolve_sync_targets_client_not_found(self) -> None:
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>")
        fake_uuid = str(uuid4())
        note = Note.objects.create(
            note_owner=user,
            status="scheduled",
            metadata={},
            client={"uuid": fake_uuid, "name": "External Client"},
        )

        result = self.salesforce.resolve_sync_targets(note, user)

        self.assertEqual(
            result,
            CRMSyncTargets(
                status=CRMSyncTargets.Status.FINAL,
                note_targets=[CRMSyncTarget(crm_id=fake_uuid, type="", name="External Client")],
            ),
        )

    @mock_salesforce
    def test_resolve_sync_targets_invalid_uuid_format(self) -> None:
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>")
        invalid_uuid = "not-a-valid-uuid"
        note = Note.objects.create(
            note_owner=user,
            status="scheduled",
            metadata={},
            client={"uuid": invalid_uuid, "name": "Invalid UUID Client"},
        )

        result = self.salesforce.resolve_sync_targets(note, user)

        self.assertEqual(
            result,
            CRMSyncTargets(
                status=CRMSyncTargets.Status.FINAL,
                note_targets=[CRMSyncTarget(crm_id=invalid_uuid, type="", name="Invalid UUID Client")],
            ),
        )

    @mock_salesforce
    def test_resolve_sync_targets_client_no_name_fallback(self) -> None:
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>")
        fake_uuid = str(uuid4())
        note = Note.objects.create(
            note_owner=user,
            status="scheduled",
            metadata={},
            client={"uuid": fake_uuid},  # No name field
        )

        result = self.salesforce.resolve_sync_targets(note, user)

        self.assertEqual(
            result,
            CRMSyncTargets(
                status=CRMSyncTargets.Status.FINAL,
                note_targets=[CRMSyncTarget(crm_id=fake_uuid, type="", name="")],
            ),
        )

    @mock_salesforce
    def test_resolve_sync_targets_database_error(self) -> None:
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>")
        fake_uuid = str(uuid4())
        note = Note.objects.create(
            note_owner=user,
            status="scheduled",
            metadata={},
            client={"uuid": fake_uuid, "name": "Test Client"},
        )

        # Mock Client.objects.get to raise a database error
        with patch.object(Client.objects, "get", side_effect=Exception("Database error")):
            result = self.salesforce.resolve_sync_targets(note, user)

        self.assertEqual(
            result,
            CRMSyncTargets(
                status=CRMSyncTargets.Status.FINAL,
                note_targets=[CRMSyncTarget(crm_id=fake_uuid, type="", name="Test Client")],
            ),
        )

    @mock_salesforce
    def test_resolve_sync_targets_ignores_selected_sync_targets(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>", organization=org)
        client = Client.objects.create(
            name="Test Client",
            organization=org,
            crm_id="salesforce_123",
        )
        note = Note.objects.create(
            note_owner=user,
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid), "name": "Test Client"},
        )

        dummy_target = CRMSyncTarget(crm_id="dummy_id", type="Account", name="Dummy")
        dummy_sync_targets = CRMSyncTargets(status=CRMSyncTargets.Status.FINAL, note_targets=[dummy_target])

        result = self.salesforce.resolve_sync_targets(note, user, dummy_sync_targets)

        self.assertEqual(
            result,
            CRMSyncTargets(
                status=CRMSyncTargets.Status.FINAL,
                note_targets=[CRMSyncTarget(crm_id="salesforce_123", type="", name="Test Client")],
            ),
        )

    @mock_salesforce
    def test_list_users_no_organization(self) -> None:
        user = User.objects.create(username="testuser", email="<EMAIL>")
        user.organization = None
        user.save()
        assert self.salesforce.list_users(user) == []

    @mock_salesforce
    def test_list_users_empty_salesforce_users(self) -> None:
        org = Organization.objects.create(name="Test Org")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)
        assert self.salesforce.list_users(user) == []

    @mock_salesforce
    def test_list_users_with_matching_and_nonmatching_users(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        crm_user_one = self.simple_salesforce.User.create({"Email": "<EMAIL>", "Name": "Abc", "IsActive": True})  # type: ignore[operator]
        crm_user_two = self.simple_salesforce.User.create({"Email": "<EMAIL>", "Name": "Def", "IsActive": True})  # type: ignore[operator]
        self.simple_salesforce.User.create(
            {
                "Email": "<EMAIL>",
                "Name": "Pqr",
            }
        )  # type: ignore[operator]
        user = User.objects.create(username="user_one", email="<EMAIL>", organization=org)

        crm_users = self.salesforce.list_users(user) or []

        def expected() -> list[CRMUser]:
            return [
                CRMUser(
                    name="Def",
                    crm_id=crm_user_two["id"],
                    organization=ZeplynOrganization(uuid=str(org.uuid), name=org.name),
                    zeplyn_user=None,
                ),
                CRMUser(
                    name="Abc",
                    crm_id=crm_user_one["id"],
                    organization=ZeplynOrganization(uuid=str(org.uuid), name=org.name),
                    zeplyn_user=ZeplynUser(uuid=str(user.uuid), email=user.email, name=user.name),
                ),
            ]

        assert len(crm_users) == 2
        assert all(isinstance(u, CRMUser) for u in crm_users)
        assert crm_users == expected()

    @mock_salesforce
    def test_list_users_all_users_unmatched_zeplyn_user(self) -> None:
        org = Organization.objects.create(name="Test Org")
        user = User.objects.create(username="user", email="<EMAIL>", organization=org)

        crm_user = self.simple_salesforce.User.create(
            {"Email": "<EMAIL>", "Name": "NoMatch", "IsActive": True}
        )  # type: ignore[operator]

        def expected() -> list[CRMUser]:
            return [
                CRMUser(
                    name="NoMatch",
                    crm_id=crm_user["id"],
                    organization=ZeplynOrganization(uuid=str(org.uuid), name=org.name),
                    zeplyn_user=None,
                ),
            ]

        crm_users = self.salesforce.list_users(user) or []

        assert len(crm_users) == 1
        assert crm_users == expected()

    @mock_salesforce
    def test_list_users_missing_crm_user_email(self) -> None:
        org = Organization.objects.create(name="Test Org")
        user = User.objects.create(username="user", email="<EMAIL>", organization=org)

        crm_user = self.simple_salesforce.User.create({"Name": "NoEmailUser", "IsActive": True})  # type: ignore[operator]

        def expected() -> list[CRMUser]:
            return [
                CRMUser(
                    name="NoEmailUser",
                    crm_id=crm_user["id"],
                    organization=ZeplynOrganization(uuid=str(org.uuid), name=org.name),
                    zeplyn_user=None,
                ),
            ]

        crm_users = self.salesforce.list_users(user) or []

        assert len(crm_users) == 1
        assert crm_users == expected()

    @mock.patch("deepinsights.core.integrations.crm.salesforce.salesforce_utils.get_active_users")
    def test_list_users_missing_crm_user_id(self, mock_get_active_users: MagicMock) -> None:
        org = Organization.objects.create(name="Test Org")
        user = User.objects.create(username="user", email="<EMAIL>", organization=org)

        # Simulate Salesforce returning a user without "Id"
        mock_get_active_users.return_value = [{"Email": "<EMAIL>", "Name": "NoIdUser"}]
        crm_users = self.salesforce.list_users(user) or []

        assert crm_users == []  # It should skip this user

    @mock.patch("deepinsights.core.integrations.crm.salesforce.salesforce_utils.get_active_users")
    def test_list_users_salesforce_exception(self, mock_get_active_users: MagicMock) -> None:
        org = Organization.objects.create(name="Test Org")
        user = User.objects.create(username="user", email="<EMAIL>", organization=org)
        mock_get_active_users.side_effect = Exception()
        crm_users = self.salesforce.list_users(user) or []

        assert crm_users == []

    @mock.patch("deepinsights.core.integrations.crm.salesforce.salesforce_utils.get_active_users")
    def test_list_users_salesforce_returns_none(self, mock_get_active_users: MagicMock) -> None:
        org = Organization.objects.create(name="Test Org")
        user = User.objects.create(username="user", email="<EMAIL>", organization=org)
        mock_get_active_users.return_value = None
        crm_users = self.salesforce.list_users(user) or []

        assert crm_users == []
