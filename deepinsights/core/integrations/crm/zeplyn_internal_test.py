import datetime
from unittest.mock import patch

import pytest

from deepinsights.core.integrations.crm.crm_models import CRMNote
from deepinsights.core.integrations.crm.zeplyn_internal import ZeplynInternal
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.user import User


@pytest.fixture
def zeplyn_internal() -> ZeplynInternal:
    return ZeplynInternal()


@pytest.fixture
def organization() -> Organization:
    return Organization.objects.create(name="Test Organization")


@pytest.fixture
def test_user(organization: Organization) -> User:
    return User.objects.create(email="<EMAIL>", organization=organization)


@pytest.fixture
def test_client(organization: Organization) -> Client:
    return Client.objects.create(
        crm_system="zeplyn_internal",
        first_name="Test",
        last_name="Client",
        organization=organization,
    )


@pytest.fixture
def test_note(test_client: Client, test_user: User) -> Note:
    return Note.objects.create(
        client={"name": f"{test_client.first_name} {test_client.last_name}", "uuid": str(test_client.uuid)},
        metadata={"meeting_name": "Test Meeting"},
        summary={"sections": [{"topic": "Test Topic", "bullets": ["Test bullet"]}]},
        note_owner=test_user,
    )


def test_fetch_notes_for_client_wrong_crm_system(
    zeplyn_internal: ZeplynInternal, test_user: User, test_client: Client
) -> None:
    test_client.crm_system = "other_crm"
    test_client.save()

    notes = zeplyn_internal.fetch_notes_for_client(
        user=test_user,
        client=test_client,
        lookback_interval=datetime.timedelta(days=7),
    )

    assert notes == []


def test_fetch_notes_for_client_success(
    zeplyn_internal: ZeplynInternal, test_user: User, test_client: Client, test_note: Note
) -> None:
    notes = zeplyn_internal.fetch_notes_for_client(
        user=test_user,
        client=test_client,
        lookback_interval=datetime.timedelta(days=7),
    )

    assert len(notes) == 1
    assert isinstance(notes[0], CRMNote)
    assert notes[0].crm_id == str(test_note.uuid)
    assert notes[0].crm_system == "zeplyn_internal"
    assert notes[0].content == test_note.get_summary_for_crm(use_html_formatting=False)


def test_fetch_notes_for_client_with_exception(
    zeplyn_internal: ZeplynInternal, test_user: User, test_client: Client, test_note: Note
) -> None:
    with patch.object(Note, "get_summary_for_crm", side_effect=Exception("Test error")):
        notes = zeplyn_internal.fetch_notes_for_client(
            user=test_user,
            client=test_client,
            lookback_interval=datetime.timedelta(days=7),
        )

        assert notes == []
