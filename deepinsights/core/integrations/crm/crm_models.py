import datetime
import enum
from typing import Mapping

import pydantic

from deepinsights.utils.phone_number import PhoneNumberE164OrNone


# A standardized representation of a CRM account.
class CRMAccount(pydantic.BaseModel):
    # The identifier provided by the CRM, regularlized to a string.
    crm_id: str

    # The name related to the account.
    name: str

    # The first name of the account holder (if available).
    first_name: str | None = None

    # The last name of the account holder (if available).
    last_name: str | None = None

    # The email address of the account holder (if available).
    email: str | None = None

    # The phone number of the account holder (if available).
    phone_number: PhoneNumberE164OrNone = None

    # The type of client this is; e.g., "account", "contact", "household", "client".
    client_type: str

    # Which CRM this client is from.
    crm_system: str

    # Whether this client is owned by the user who made the request to fetch this client.
    #
    # Note that this will change depending on the user making the request, so there is no
    # guarantee that two users will receive equivalent `CRMAccount` objects when fetching
    # clients from the CRM.
    is_owned_by_user: bool | None = None


class CRMNoteType(enum.StrEnum):
    SALESFORCE_STANDARD_NOTE = "salesforce_standard_note"
    SALESFORCE_CONTENT_NOTE = "salesforce_content_note"


# A standardized representation of data from a CRM note (i.e., a piece of textual information
# associated with a client).
class CRMNote(pydantic.BaseModel):
    # The identifier provided by the CRM, regularlized to a string.
    crm_id: str

    # Which CRM this note is from.
    crm_system: str

    # The contents of the note.
    content: str

    # The creation timestamp associated with this CRM note.
    created_at: datetime.datetime | None

    # Type of note.
    type: CRMNoteType | None = None

    # A link that can be used to access this note in the CRM.
    #
    # This is meant to be a link that can be used to access this note via a browser, not a link in
    # the context of an API.
    web_link: pydantic.HttpUrl | None = None


# A representation of an entity in a CRM that can be used to sync notes or tasks.
class CRMSyncTarget(pydantic.BaseModel):
    # The identifier provided by the CRM, regularlized to a string.
    crm_id: str

    # The type of entity this is, e.g., "client", "appointment", "task".
    type: str

    # A user-friendly name for this target, e.g., "John Doe", "Meeting with Jane".
    name: str

    # The CRM entity that this target is associated with, if any.
    parent: "CRMSyncTarget | None" = None


# Represents the set of CRM entities to which notes should be synced.
class CRMSyncTargets(pydantic.BaseModel):
    class Status(enum.StrEnum):
        # Information about clients is not available, and the user should be prompted to select
        # clients.
        CLIENTS_REQUIRED = "clients_required"

        # Further user input is required to resolve the sync targets to a finalized set of targets.
        RESOLUTION_REQUIRED = "resolution_required"

        # The sync targets have been resolved and are final: the CRM implementation can correctly
        # sync notes and tasks into the CRM.
        FINAL = "final"

    # Whether this set of sync targets is final, meaning that no further resolution is needed.
    status: Status

    # A list of (finalized or unfinalized) targets to which notes should be synced.
    #
    # If status is `RESOLUTION_REQUIRED`, this list will contain targets that require further
    # resolution. The user should be presented with these targets to select the final target(s) for
    # syncing.
    note_targets: list[CRMSyncTarget]


class CRMSyncSection(str, enum.Enum):
    """Main sections that can be synced to CRM"""

    MEETING_DETAILS = "meeting_details"
    ATTENDEES = "attendees"
    TASKS = "tasks"
    KEY_TAKEAWAYS = "key_takeaways"
    ADVISOR_NOTES = "advisor_notes"
    SUMMARY = "summary"
    KEYWORDS = "keywords"


class CRMSyncItemSelection(pydantic.BaseModel):
    """Represents selection state for a sync section"""

    # Whether the entire section is included
    include_section: bool = True
    # Specific items within the section to include/exclude
    included_items: list[str] | None = None


class CRMWorkflow(pydantic.BaseModel):
    name: str
    crm_id: str


class UploadNoteToCRMRequest(pydantic.BaseModel):
    sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = pydantic.Field(
        None, description="Optional configuration for which note sections to sync to CRM"
    )


class ZeplynOrganization(pydantic.BaseModel):
    uuid: str

    name: str


class ZeplynUser(pydantic.BaseModel):
    # The unique identifier (UUID) of the user in the Zeplyn system.
    uuid: str

    # The email address of the Zeplyn user.
    email: str

    # The name of the user, if available.
    name: str | None = None


class CRMUser(pydantic.BaseModel):
    # A human-readable name for this CRM user (used for LLM parsing, display, etc.).
    name: str

    # The unique identifier assigned to the user in the external CRM system.
    crm_id: str

    # The organization that owns this CRM user and CRM configuration.
    organization: ZeplynOrganization

    # The associated Zeplyn user, if one exists. A CRM user may or may not be linked to a Zeplyn user.
    zeplyn_user: ZeplynUser | None = None
