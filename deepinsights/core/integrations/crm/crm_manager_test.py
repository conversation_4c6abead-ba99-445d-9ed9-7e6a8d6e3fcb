from unittest.mock import <PERSON><PERSON><PERSON>, Mo<PERSON>, patch

from django.test import TestCase

from deepinsights.core.integrations.crm.crm_manager import get_crm_interface
from deepinsights.core.integrations.crm.dummy_crm import DummyCrm
from deepinsights.core.integrations.crm.microsoft_dynamics import MicrosoftDynamics
from deepinsights.core.integrations.crm.redtail import Redtail
from deepinsights.core.integrations.crm.salesforce import Salesforce
from deepinsights.core.integrations.crm.salesforce_financial_cloud import SalesforceFinancialCloud
from deepinsights.core.integrations.crm.sequoia_salesforce import SequoiaSalesforce
from deepinsights.core.integrations.crm.sharefile import ShareFile
from deepinsights.core.integrations.crm.sharepoint import SharePoint
from deepinsights.core.integrations.crm.tamarac_microsoft_dynamics import TamaracMicrosoftDynamics
from deepinsights.core.integrations.crm.wealthbox import Wealthbox
from deepinsights.core.integrations.crm.zeplyn_internal import Z<PERSON>lynInternal
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.user import User


class CrmManagerTest(TestCase):
    @patch("simple_salesforce.Salesforce.__init__")
    @patch("deepinsights.core.integrations.crm.sharefile.ShareFile.__init__")
    @patch("deepinsights.core.integrations.crm.sharepoint.SharePoint.__init__")
    @patch("deepinsights.core.integrations.crm.sequoia_salesforce.requests")
    @patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamics.__init__")
    @patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.TamaracMicrosoftDynamics.__init__")
    def test_crm_mapping(
        self,
        mock_tamarac_dynamics_init: MagicMock,
        mock_dynamics_init: MagicMock,
        mock_requests: MagicMock,
        mock_sharepoint_init: MagicMock,
        mock_sharefile_init: MagicMock,
        mock_salesforce_init: MagicMock,
    ) -> None:
        mock_salesforce_init.return_value = None
        mock_sharefile_init.return_value = None
        mock_sharepoint_init.return_value = None
        mock_requests.return_value = None
        mock_dynamics_init.return_value = None
        mock_tamarac_dynamics_init.return_value = None

        user = User.objects.create(username="<EMAIL>", email="<EMAIL>")
        self.assertEqual(type(get_crm_interface(user)), DummyCrm)

        user.crm_configuration["crm_system"] = "salesforce"
        self.assertEqual(type(get_crm_interface(user)), SalesforceFinancialCloud)

        user.crm_configuration["crm_system"] = "salesforce"
        user.crm_configuration["salesforce"] = {"type": "base"}
        self.assertEqual(type(get_crm_interface(user)), Salesforce)

        user.crm_configuration["crm_system"] = "sequoia_sf"
        self.assertEqual(type(get_crm_interface(user)), SequoiaSalesforce)

        user.crm_configuration["crm_system"] = "wealthbox"
        self.assertEqual(type(get_crm_interface(user)), Wealthbox)

        user.crm_configuration["crm_system"] = "redtail"
        self.assertEqual(type(get_crm_interface(user)), Redtail)

        user.crm_configuration["crm_system"] = "sharefile"
        self.assertEqual(type(get_crm_interface(user)), ShareFile)
        mock_sharefile_init.assert_called_once()

        user.crm_configuration["crm_system"] = "sharepoint"
        self.assertEqual(type(get_crm_interface(user)), SharePoint)
        mock_sharepoint_init.assert_called_once()

        user.crm_configuration["crm_system"] = "microsoft_dynamics"
        user.crm_configuration["dynamics"] = {"type": "base"}
        self.assertEqual(type(get_crm_interface(user)), MicrosoftDynamics)
        mock_dynamics_init.assert_called_once_with(user=user)

        user.crm_configuration["crm_system"] = "microsoft_dynamics"
        user.crm_configuration["dynamics"] = {"type": "tamarac"}
        self.assertEqual(type(get_crm_interface(user)), TamaracMicrosoftDynamics)
        mock_tamarac_dynamics_init.assert_called_once_with(user=user)

        user.crm_configuration["crm_system"] = "zeplyn_internal"
        self.assertEqual(type(get_crm_interface(user)), ZeplynInternal)
        mock_sharepoint_init.assert_called_once()

        user.crm_configuration["crm_system"] = "unknown"
        self.assertEqual(type(get_crm_interface(user)), DummyCrm)

    @patch("deepinsights.core.integrations.crm.crm_manager.SalesforceFinancialCloud.__init__")
    def test_user_override_salesforce_configuration(self, mock_salesforce_init: Mock) -> None:
        mock_salesforce_init.return_value = None
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>")
        user.crm_configuration["crm_system"] = "salesforce"
        user.crm_configuration["salesforce"] = {
            "salesforce_endpoint": "http://test",
            "salesforce_username": "username",
            "salesforce_password": "password",
            "salesforce_security_token": "token",
            "salesforce_consumer_key": "consumer_key",
            "salesforce_consumer_secret": "consumer_secret",
        }
        get_crm_interface(user)
        mock_salesforce_init.assert_called_once_with(
            user=user,
            username="username",
            password="password",
            security_token="token",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
            instance_url="http://test",
        )

    @patch("deepinsights.core.integrations.crm.crm_manager.Salesforce.__init__")
    def test_salesforce_base_configuration(self, mock_salesforce_init: Mock) -> None:
        mock_salesforce_init.return_value = None
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>")
        user.crm_configuration["crm_system"] = "salesforce"
        user.crm_configuration["salesforce"] = {
            "type": "base",
            "salesforce_endpoint": "http://test",
            "salesforce_username": "username",
            "salesforce_password": "password",
            "salesforce_security_token": "token",
            "salesforce_consumer_key": "consumer_key",
            "salesforce_consumer_secret": "consumer_secret",
        }
        get_crm_interface(user)
        mock_salesforce_init.assert_called_once_with(
            user=user,
            username="username",
            password="password",
            security_token="token",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
            instance_url="http://test",
        )

    @patch("deepinsights.core.integrations.crm.crm_manager.SalesforceFinancialCloud.__init__")
    def test_org_override_salesforce_configuration(self, mock_salesforce_init: Mock) -> None:
        mock_salesforce_init.return_value = None
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>")
        org = Organization.objects.create()
        org.crm_configuration["crm_system"] = "salesforce"
        org.crm_configuration["salesforce"] = {
            "salesforce_endpoint": "http://test",
            "salesforce_username": "username",
            "salesforce_password": "password",
            "salesforce_security_token": "token",
            "salesforce_consumer_key": "consumer_key",
            "salesforce_consumer_secret": "consumer_secret",
        }
        user.organization = org

        get_crm_interface(user)
        mock_salesforce_init.assert_called_once_with(
            user=user,
            username="username",
            password="password",
            security_token="token",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
            instance_url="http://test",
        )

    @patch("deepinsights.core.integrations.crm.crm_manager.SalesforceFinancialCloud.__init__")
    def test_org_user_override_salesforce_configuration(self, mock_salesforce_init: Mock) -> None:
        mock_salesforce_init.return_value = None
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>")
        user.crm_configuration["crm_system"] = "salesforce"
        user.crm_configuration["salesforce"] = {
            "salesforce_username": "user_username",
            "salesforce_password": "user_password",
            "salesforce_consumer_key": "user_consumer_key",
            "salesforce_consumer_secret": "user_consumer_secret",
        }
        org = Organization.objects.create()
        org.crm_configuration["crm_system"] = "redtail"
        org.crm_configuration["salesforce"] = {
            "salesforce_endpoint": "http://org_test",
            "salesforce_username": "org_username",
            "salesforce_password": "org_password",
            "salesforce_security_token": "org_token",
            "salesforce_consumer_key": "org_consumer_key",
            "salesforce_consumer_secret": "org_consumer_secret",
        }
        user.organization = org

        get_crm_interface(user)
        mock_salesforce_init.assert_called_once_with(
            user=user,
            username="user_username",
            password="user_password",
            security_token="org_token",
            instance_url="http://org_test",
            consumer_key="user_consumer_key",
            consumer_secret="user_consumer_secret",
        )

    @patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamics.__init__")
    def test_microsoft_dynamics_configuration(self, mock_dynamics_init: Mock) -> None:
        mock_dynamics_init.return_value = None
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>")
        user.crm_configuration["crm_system"] = "microsoft_dynamics"
        user.crm_configuration["dynamics"] = {
            "type": "base",
            "dynamics_resource_url": "https://orgname.crm.dynamics.com",
        }

        get_crm_interface(user)
        mock_dynamics_init.assert_called_once_with(user=user)

    @patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.TamaracMicrosoftDynamics.__init__")
    def test_tamarac_icrosoft_dynamics_configuration(self, mock_tamarac_dynamics_init: Mock) -> None:
        mock_tamarac_dynamics_init.return_value = None
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>")
        user.crm_configuration["crm_system"] = "microsoft_dynamics"
        user.crm_configuration["dynamics"] = {
            "type": "tamarac",
            "dynamics_resource_url": "https://orgname.crm.dynamics.com",
        }

        get_crm_interface(user)
        mock_tamarac_dynamics_init.assert_called_once_with(user=user)
