import logging
from datetime import datetime, timedelta, timezone
from typing import Any, <PERSON><PERSON>
from unittest.mock import patch
from uuid import uuid4

import zoneinfo
from django.test import TestCase
from django.utils import timezone as django_timezone
from simple_mockforce import mock_salesforce
from simple_salesforce.api import Salesforce

from deepinsights.core.integrations.crm.crm_models import (
    CRMAccount,
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTarget,
    CRMSyncTargets,
    CRMUser,
    ZeplynOrganization,
    ZeplynUser,
)
from deepinsights.core.integrations.crm.sequoia_salesforce import SequoiaSalesforce
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingData, StructuredMeetingDataTemplate
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User


class SequoiaSalesforceTestCase(TestCase):
    def setUp(self) -> None:
        self.salesforce = Salesforce(instance="test.salesforce.com", session_id="")

    # Creates a new account with the given service team.
    def newAccount(self, service_team: list[str]) -> dict[str, Any]:
        return self.salesforce.Account.create(  # type: ignore[no-any-return, operator]
            {
                "Name": f"Test Account {uuid4()}",
                "Service_Team_User_Ids__c": ",".join(service_team),
            }
        )

    # Creates a new arbitrary user, both in the Salesforce and Zeplyn databases.
    #
    # Returns a tuple of (userId, userEmail).
    def newUser(self, org: Organization) -> tuple[str, str]:
        email = f"{uuid4()}@example.com"
        user = self.salesforce.User.create({"Email": email})  # type: ignore[operator]
        User.objects.create(username=email, email=email, organization=org)
        return (user["id"], email)

    # Creates a new Salesforce case.
    def newCaseAndClient(
        self,
        *,
        account_id: str,
        attendees: str = "",
        zeplyn_id: str | None = None,
        timestamp: datetime = django_timezone.now(),
        closed: bool = False,
        org: Organization,
    ) -> Tuple[dict[str, Any], Client]:
        case = self.salesforce.Case.create(  # type: ignore[operator]
            {
                "AccountId": account_id,
                "Attendees__c": attendees,
                "CaseNumber": str(uuid4()),
                "Client_Review_Start_Time__c": timestamp.strftime("%Y-%m-%dT%H:%M:%S.%f%z"),
                "Subject": "Meeting",
                "Zeplyn_Id__c": zeplyn_id,
                "Client_Review_Location__c": "test",
                "Meeting_Link__c": None,
                "IsClosed": closed,
            }
        )
        client = Client.objects.create(
            crm_id=case["id"], name="Meeting", organization=org, client_type="test", crm_system="test"
        )
        return (case, client)


class SequoiaSalesforceTests(SequoiaSalesforceTestCase):
    # Tests that the Zeplyn note updates a corresponding Salesforce case.
    @mock_salesforce
    def testAddInteractionWithClientWithCaseId(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        note_id = str(uuid4())
        timestamp = django_timezone.now()
        case, _ = self.newCaseAndClient(account_id=account["id"], zeplyn_id=note_id, timestamp=timestamp, org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        note = Note(
            uuid=note_id,
            note_owner=user,
            status="finalized",
            metadata={
                "meeting_name": "Meeting",
                "tags": ["tag1", "tag2"],
                "meeting_duration": 3600,
                "scheduled_at": str(timestamp),
            },
            salesforce_case_id=case["id"],
        )
        note.save()
        Attendee.objects.create(attendee_name="Bob", note=note).save()
        Attendee.objects.create(attendee_name="Paul", note=note).save()
        note.authorized_users.add(user)
        note.save()

        Task.objects.create(task_title="Task one", task_owner=user, note=note, due_date=django_timezone.now())
        Task.objects.create(task_title="Task two", task_owner=user, note=note, due_date=django_timezone.now())

        # Add an interaction
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sequoia_sf.add_interaction_with_client(note, sequoia_sf.resolve_sync_targets(note, user))

        # Confirm that the case was updated (and correctly)
        case = self.salesforce.Case.get(case["id"])  # type: ignore[operator]
        self.assertEqual(case["Subject"], "Meeting")
        self.assertEqual(case["Zeplyn_Meeting_Tags__c"], "tag1;tag2")
        self.assertIn(case["Attendee_Speaker_Allocations_2__c"], ["Bob, Paul", "Paul, Bob"])
        self.assertIsNotNone(case["Cases_To_Create__c"])
        self.assertIsNotNone(case["Meeting_Summary__c"])
        self.assertEqual(case["Meeting_Duration__c"], 1)

    # Tests that adding an interaction for a note with a client works as expected.
    @mock_salesforce
    def testAddInteractionWithClientWithClientMetadataUUIDAsCRMID(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        note_id = str(uuid4())
        timestamp = django_timezone.now()
        case, client = self.newCaseAndClient(account_id=account["id"], org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        note = Note(
            uuid=note_id,
            note_owner=user,
            status="finalized",
            metadata={
                "meeting_name": "Zeplyn meeting",
                "tags": ["tag1", "tag2"],
                "meeting_duration": 3600,
                "scheduled_at": str(timestamp),
            },
            client={"name": "client", "uuid": case["id"]},
            # No salesforce_case_id, because that takes precedence over using the client UUID as a CRM ID.
        )
        note.save()
        note.authorized_users.add(user)
        note.save()

        Task.objects.create(
            task_title="Task one",
            task_owner=user,
            note=note,
            due_date=datetime(2024, 8, 4, 0, 0, 1, tzinfo=timezone.utc),
        )
        Task.objects.create(
            task_title="Task two",
            task_owner=user,
            note=note,
            due_date=datetime(2024, 8, 12, 9, 8, 7, tzinfo=timezone.utc),
        )
        Task.objects.create(
            task_owner=user,
            note=note,
        )

        # Add an interaction
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sequoia_sf.add_interaction_with_client(note, sequoia_sf.resolve_sync_targets(note, user))

        # Confirm that the case was updated
        case = self.salesforce.Case.get(case["id"])  # type: ignore[operator]
        self.assertEqual(case["Subject"], "Meeting")
        self.assertEqual(case["Cases_To_Create__c"], "Task one<br>\nTask two<br>\nNo title")

    # Tests that adding an interaction for a note with a client works as expected.
    @mock_salesforce
    def testAddInteractionWithClientWithClientMetadataUUID(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        note_id = str(uuid4())
        timestamp = django_timezone.now()
        case, client = self.newCaseAndClient(account_id=account["id"], org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        note = Note(
            uuid=note_id,
            note_owner=user,
            status="finalized",
            metadata={
                "meeting_name": "Zeplyn meeting",
                "tags": ["tag1", "tag2"],
                "meeting_duration": 3600,
                "scheduled_at": str(timestamp),
            },
            client={"name": "client", "uuid": str(client.uuid)},
            salesforce_case_id="other_case_id",
        )
        note.save()
        note.authorized_users.add(user)
        note.save()

        Task.objects.create(
            task_title="Task one",
            task_owner=user,
            note=note,
            due_date=datetime(2024, 8, 4, 0, 0, 1, tzinfo=timezone.utc),
        )
        Task.objects.create(
            task_title="Task two",
            task_owner=user,
            note=note,
            due_date=datetime(2024, 8, 12, 9, 8, 7, tzinfo=timezone.utc),
        )
        Task.objects.create(
            task_owner=user,
            note=note,
        )

        # Add an interaction
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sequoia_sf.add_interaction_with_client(note, sequoia_sf.resolve_sync_targets(note, user))

        # Confirm that the case was updated
        case = self.salesforce.Case.get(case["id"])  # type: ignore[operator]
        self.assertEqual(case["Subject"], "Meeting")
        self.assertEqual(case["Cases_To_Create__c"], "Task one<br>\nTask two<br>\nNo title")

    # Tests that adding an interaction for a note with a client but no UUID works as expected.
    @mock_salesforce
    def testAddInteractionWithClientWithoutID(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        note_id = str(uuid4())
        timestamp = django_timezone.now()
        case, _ = self.newCaseAndClient(account_id=account["id"], org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        note = Note(
            uuid=note_id,
            note_owner=user,
            status="finalized",
            metadata={
                "meeting_name": "Meeting",
                "tags": ["tag1", "tag2"],
                "meeting_duration": 3600,
                "scheduled_at": str(timestamp),
            },
            client={"name": "client"},
        )
        note.save()
        note.authorized_users.add(user)
        note.save()

        Task.objects.create(task_title="Task one", task_owner=user, note=note, due_date=django_timezone.now())
        Task.objects.create(task_title="Task two", task_owner=user, note=note, due_date=django_timezone.now())

        expected_case = self.salesforce.Case.get(case["id"])  # type: ignore[operator]
        # Add an interaction
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sequoia_sf.add_interaction_with_client(note, sequoia_sf.resolve_sync_targets(note, user))

        # Confirm that the case was not updated
        self.assertEqual(expected_case, self.salesforce.Case.get(case["id"]))  # type: ignore[operator]

    # Tests that adding an interaction for a note with no client and not case ID works as expected.
    @mock_salesforce
    def testAddInteractionWithoutCaseOrClient(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        note_id = str(uuid4())
        timestamp = django_timezone.now()
        case, _ = self.newCaseAndClient(account_id=account["id"], org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        note = Note(
            uuid=note_id,
            note_owner=user,
            status="finalized",
            metadata={
                "meeting_name": "Meeting",
                "tags": ["tag1", "tag2"],
                "meeting_duration": 3600,
                "scheduled_at": str(timestamp),
            },
        )
        note.save()
        note.authorized_users.add(user)
        note.save()

        Task.objects.create(task_title="Task one", task_owner=user, note=note, due_date=django_timezone.now())
        Task.objects.create(task_title="Task two", task_owner=user, note=note, due_date=django_timezone.now())

        expected_case = self.salesforce.Case.get(case["id"])  # type: ignore[operator]
        # Add an interaction
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sequoia_sf.add_interaction_with_client(note, sequoia_sf.resolve_sync_targets(note, user))

        # Confirm that the case was not updated
        self.assertEqual(expected_case, self.salesforce.Case.get(case["id"]))  # type: ignore[operator]

    # Tests that adding an interaction for a note with multiple clients works as expected.
    @mock_salesforce
    def testAddInteractionWithMultipleClients(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        note_id = str(uuid4())
        timestamp = django_timezone.now()
        case, _ = self.newCaseAndClient(account_id=account["id"], org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        note = Note(
            uuid=note_id,
            note_owner=user,
            status="finalized",
            metadata={
                "meeting_name": "Meeting",
                "tags": ["tag1", "tag2"],
                "meeting_duration": 3600,
                "scheduled_at": str(timestamp),
            },
        )
        note.save()
        note.authorized_users.add(user)
        note.save()

        Task.objects.create(task_title="Task one", task_owner=user, note=note, due_date=django_timezone.now())
        Task.objects.create(task_title="Task two", task_owner=user, note=note, due_date=django_timezone.now())

        case_id = case["id"]
        expected_case = self.salesforce.Case.get(case_id)  # type: ignore[operator]
        # Add an interaction
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(
                    crm_id=case["id"],
                    type="case",
                    name="",
                ),
                CRMSyncTarget(
                    crm_id="client_uuid_1",
                    type="client",
                    name="",
                ),
            ],
        )
        sequoia_sf.add_interaction_with_client(note, sync_targets)

        # Confirm that the case was not updated
        self.assertEqual(expected_case, self.salesforce.Case.get(case_id))  # type: ignore[operator]

    @mock_salesforce
    def testGetAccountsByOwnerEmailAndNameClosed(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        self.newCaseAndClient(account_id=account["id"], closed=True, org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        accounts = sequoia_sf.get_accounts_by_owner_email_and_name(user.email, "")
        self.assertEqual(len(accounts), 0)

    @mock_salesforce
    def testGetAccountsByOwnerEmailAndNameOpen(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        self.newCaseAndClient(account_id=account["id"], closed=False, org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        cases = sequoia_sf.get_accounts_by_owner_email_and_name(user.email, "")
        self.assertEqual(len(cases), 1)

    def create_test_note_with_tasks(
        self, user: User, note_metadata: dict[str, Any] | None = None, task_titles: list[str] | None = None
    ) -> Note:
        note = Note.objects.create(
            uuid=str(uuid4()),
            note_owner=user,
            status=Note.PROCESSING_STATUS.finalized,
            metadata=note_metadata or {},
            salesforce_case_id="some_case_id",
        )

        if task_titles:
            due_date = django_timezone.now() + timedelta(days=7)
            logging.info(f"Calculated due date: {due_date}")
            for title in task_titles:
                Task.objects.create(
                    note=note,
                    task_owner=user,
                    task_title=title,
                    due_date=due_date,
                )

        return note

    @mock_salesforce
    def testGetAccountsByOwnerIDOrEmailProvidesCorrectResults(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        case_one, _ = self.newCaseAndClient(account_id=account["id"], org=org)
        case_two, _ = self.newCaseAndClient(account_id=account["id"], org=org)

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        crm_accounts = sequoia_sf.get_accounts_by_owner_email_and_name(user_email)

        salesforce_case_one = self.salesforce.Case.get(case_one["id"])  # type: ignore[operator]
        salesforce_case_two = self.salesforce.Case.get(case_two["id"])  # type: ignore[operator]
        salesforce_account = self.salesforce.Account.get(account["id"])  # type: ignore[operator]

        self.assertEqual(
            crm_accounts,
            [
                CRMAccount(
                    crm_id=salesforce_case_one["Id"],
                    name=f"Meeting with {salesforce_account['Name']} - {salesforce_case_one['CaseNumber']}",
                    client_type="case",
                    crm_system="sequoia_sf",
                ),
                CRMAccount(
                    crm_id=salesforce_case_two["Id"],
                    name=f"Meeting with {salesforce_account['Name']} - {salesforce_case_two['CaseNumber']}",
                    client_type="case",
                    crm_system="sequoia_sf",
                ),
            ],
        )

    def test_parse_isolike_time(self) -> None:
        """Tests the _parse_isolike_time method with various timestamp formats."""
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)

        # Test with fractional seconds and T separator
        timestamp = "2025-01-28T20:00:00.123+0000"
        parsed = sequoia_sf._parse_isolike_time(timestamp)
        self.assertEqual(parsed.year, 2025)
        self.assertEqual(parsed.month, 1)
        self.assertEqual(parsed.day, 28)
        self.assertEqual(parsed.hour, 20)
        self.assertEqual(parsed.minute, 0)
        self.assertEqual(parsed.second, 0)
        self.assertEqual(parsed.microsecond, 123000)
        self.assertEqual(parsed.utcoffset().total_seconds(), 0)  # type: ignore[union-attr]

        # Test with fractional seconds and space separator
        timestamp = "2025-01-28 20:00:00.123+0000"
        parsed = sequoia_sf._parse_isolike_time(timestamp)
        self.assertEqual(parsed.year, 2025)
        self.assertEqual(parsed.month, 1)
        self.assertEqual(parsed.day, 28)
        self.assertEqual(parsed.hour, 20)
        self.assertEqual(parsed.minute, 0)
        self.assertEqual(parsed.second, 0)
        self.assertEqual(parsed.microsecond, 123000)
        self.assertEqual(parsed.utcoffset().total_seconds(), 0)  # type: ignore[union-attr]

        # Test without fractional seconds
        timestamp = "2025-01-28T20:00:00+0000"
        parsed = sequoia_sf._parse_isolike_time(timestamp)
        self.assertEqual(parsed.year, 2025)
        self.assertEqual(parsed.month, 1)
        self.assertEqual(parsed.day, 28)
        self.assertEqual(parsed.hour, 20)
        self.assertEqual(parsed.minute, 0)
        self.assertEqual(parsed.second, 0)
        self.assertEqual(parsed.microsecond, 0)
        self.assertEqual(parsed.utcoffset().total_seconds(), 0)  # type: ignore[union-attr]

        # space separated without fractional seconds
        timestamp = "2025-01-28 20:00:00+0000"
        parsed = sequoia_sf._parse_isolike_time(timestamp)
        self.assertEqual(parsed.year, 2025)
        self.assertEqual(parsed.month, 1)
        self.assertEqual(parsed.day, 28)
        self.assertEqual(parsed.hour, 20)
        self.assertEqual(parsed.minute, 0)
        self.assertEqual(parsed.second, 0)
        self.assertEqual(parsed.microsecond, 0)
        self.assertEqual(parsed.utcoffset().total_seconds(), 0)  # type: ignore[union-attr]

        # Test with different timezone offset
        timestamp = "2025-01-28T20:00:00.000-0700"
        parsed = sequoia_sf._parse_isolike_time(timestamp)
        self.assertEqual(parsed.year, 2025)
        self.assertEqual(parsed.month, 1)
        self.assertEqual(parsed.day, 28)
        self.assertEqual(parsed.hour, 20)
        self.assertEqual(parsed.minute, 0)
        self.assertEqual(parsed.second, 0)
        self.assertEqual(parsed.microsecond, 0)
        self.assertEqual(parsed.utcoffset().total_seconds(), -7 * 3600)  # type: ignore[union-attr]

    def test_parse_isolike_time_invalid_format(self) -> None:
        """Tests that _parse_isolike_time raises ValueError for invalid formats."""
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)

        invalid_timestamps = [
            "2025-01-28",  # Missing time component
            "2025-01-28T20:00:00",  # Missing timezone
            "2025-01-28T20:00:00:00:00",  # Invalid time format
            "2025-01-28T20:00:00+ABC",  # Invalid timezone format
            "invalid_timestamp",  # Completely invalid format
            "2025-13-45T25:61:61+0000",  # Invalid date/time values
        ]

        for timestamp in invalid_timestamps:
            with self.assertRaises(ValueError) as context:
                sequoia_sf._parse_isolike_time(timestamp)
            self.assertIn("does not match any expected formats", str(context.exception))

    @mock_salesforce
    def test_resolve_sync_targets_with_salesforce_case_id(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)

        note = Note.objects.create(
            note_owner=user,
            status=Note.PROCESSING_STATUS.processed,
            salesforce_case_id="test_case_id_123",
        )

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sync_targets = sequoia_sf.resolve_sync_targets(note, user)

        expected = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[CRMSyncTarget(crm_id="test_case_id_123", type="case", name="")],
        )
        self.assertEqual(sync_targets, expected)

    @mock_salesforce
    def test_resolve_sync_targets_with_client_uuid_as_crm_id(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)

        note = Note.objects.create(
            uuid=str(uuid4()),
            note_owner=user,
            status=Note.PROCESSING_STATUS.processed,
            client={"uuid": "crm_case_id_456", "name": "Test Client"},
            salesforce_case_id=None,
        )

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sync_targets = sequoia_sf.resolve_sync_targets(note, user)

        expected = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[CRMSyncTarget(crm_id="crm_case_id_456", type="case", name="")],
        )
        self.assertEqual(sync_targets, expected)

    @mock_salesforce
    def test_resolve_sync_targets_with_client_uuid_matching_existing_client(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)
        client = Client.objects.create(crm_id="existing_crm_id_789", name="Existing Client", organization=org)

        note = Note.objects.create(
            uuid=str(uuid4()),
            note_owner=user,
            status=Note.PROCESSING_STATUS.processed,
            client={"uuid": str(client.uuid), "name": "Test Client"},
            salesforce_case_id=None,
        )

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sync_targets = sequoia_sf.resolve_sync_targets(note, user)

        expected = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[CRMSyncTarget(crm_id="existing_crm_id_789", type="case", name="")],
        )
        self.assertEqual(sync_targets, expected)

    @mock_salesforce
    def test_resolve_sync_targets_with_client_and_salesforce_case_id_prefers_client(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)
        client = Client.objects.create(crm_id="client_crm_id", name="Client", organization=org)

        note = Note.objects.create(
            uuid=str(uuid4()),
            note_owner=user,
            status=Note.PROCESSING_STATUS.processed,
            client={"uuid": str(client.uuid), "name": "Test Client"},
            salesforce_case_id="salesforce_case_id",
        )

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sync_targets = sequoia_sf.resolve_sync_targets(note, user)

        expected = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[CRMSyncTarget(crm_id="client_crm_id", type="case", name="")],
        )
        self.assertEqual(sync_targets, expected)

    @mock_salesforce
    def test_resolve_sync_targets_with_invalid_client_uuid_falls_back_to_salesforce_case_id(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)

        note = Note.objects.create(
            uuid=str(uuid4()),
            note_owner=user,
            status=Note.PROCESSING_STATUS.processed,
            client={"uuid": "nonexistent_uuid", "name": "Test Client"},
            salesforce_case_id="fallback_case_id",
        )

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sync_targets = sequoia_sf.resolve_sync_targets(note, user)

        expected = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[CRMSyncTarget(crm_id="fallback_case_id", type="case", name="")],
        )
        self.assertEqual(sync_targets, expected)

    @mock_salesforce
    def test_resolve_sync_targets_with_no_case_id_or_client(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)

        note = Note.objects.create(
            uuid=str(uuid4()),
            note_owner=user,
            status=Note.PROCESSING_STATUS.processed,
            salesforce_case_id=None,
            client=None,
        )

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)

        with self.assertLogs(level="ERROR") as log:
            sync_targets = sequoia_sf.resolve_sync_targets(note, user)

            expected = CRMSyncTargets(
                status=CRMSyncTargets.Status.CLIENTS_REQUIRED,
                note_targets=[],
            )
            self.assertEqual(sync_targets, expected)
            self.assertIn(
                "Note has neither case ID nor valid client UUID nor valid client case ID in CRM ID.",
                log.output[0],
            )

    @mock_salesforce
    def test_resolve_sync_targets_with_empty_client_uuid(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)

        note = Note.objects.create(
            uuid=str(uuid4()),
            note_owner=user,
            status=Note.PROCESSING_STATUS.processed,
            client={"uuid": "", "name": "Test Client"},
            salesforce_case_id=None,
        )

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)

        with self.assertLogs(level="ERROR") as log:
            sync_targets = sequoia_sf.resolve_sync_targets(note, user)

            expected = CRMSyncTargets(
                status=CRMSyncTargets.Status.CLIENTS_REQUIRED,
                note_targets=[],
            )
            self.assertEqual(sync_targets, expected)
            self.assertIn(
                "Note has neither case ID nor valid client UUID nor valid client case ID in CRM ID.",
                log.output[0],
            )

    @mock_salesforce
    def test_resolve_sync_targets_ignores_selected_sync_targets_parameter(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)

        note = Note.objects.create(
            uuid=str(uuid4()),
            note_owner=user,
            status=Note.PROCESSING_STATUS.processed,
            salesforce_case_id="actual_case_id",
        )

        selected_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[CRMSyncTarget(crm_id="ignored_case_id", type="case", name="")],
        )

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sync_targets = sequoia_sf.resolve_sync_targets(note, user, selected_targets)

        expected = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[CRMSyncTarget(crm_id="actual_case_id", type="case", name="")],
        )
        self.assertEqual(sync_targets, expected)


class SequoiaSalesforceComplianceTemplatesTests(SequoiaSalesforceTestCase):
    # Ideally we would do this in setUp, but because we use the @mock_salesforce decorator, we need
    # this code to be executed in the contest of the test case.
    def _configure(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        self.user = User.objects.get(email=user_email)
        self.user.crm_configuration["crm_system"] = "sequoia_sf"
        self.user.save()

        self.case, self.test_client = self.newCaseAndClient(account_id=account["id"], org=org)
        self.timestamp = django_timezone.now()
        self.case_date = self.timestamp.astimezone(zoneinfo.ZoneInfo("America/New_York")).strftime("%Y-%m-%d")

        self.note = Note(
            note_owner=self.user,
            status="finalized",
            metadata={
                "meeting_name": "Meeting",
                "tags": ["tag1", "tag2"],
                "meeting_duration": 3600,
                "scheduled_at": str(self.timestamp),
            },
            client={"name": "client", "uuid": str(self.test_client.uuid)},
        )
        self.note.save()
        self.note.authorized_users.add(self.user)
        self.note.save()

        # Create a compliance template and associate it with the note
        self.compliance_template = StructuredMeetingData.objects.create(
            kind=StructuredMeetingDataTemplate.Kind.SEQUOIA_COMPLIANCE_CHECKLIST,
            data={
                "review_entries": [
                    {"id": "financial_status", "discussed": True},
                    {"id": "allocations_holdings", "discussed": True},
                    {"id": "investment_performance", "discussed": False},
                    {"id": "insurance_needs", "discussed": True},
                ]
            },
        )
        self.note.structuredmeetingdata_set.add(self.compliance_template)
        self.note.save()

    @mock_salesforce
    def test_add_interaction_with_compliance_checklist(self) -> None:
        self._configure()

        Task.objects.create(
            task_title="Task one",
            task_owner=self.user,
            note=self.note,
            due_date=datetime(2024, 8, 4, 0, 0, 1, tzinfo=timezone.utc),
        )
        Task.objects.create(
            task_title="Task two",
            task_owner=self.user,
            note=self.note,
            due_date=datetime(2024, 8, 12, 9, 8, 7, tzinfo=timezone.utc),
        )
        Task.objects.create(
            task_owner=self.user,
            note=self.note,
        )

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sequoia_sf.add_interaction_with_client(self.note, sequoia_sf.resolve_sync_targets(self.note, self.user))

        case = self.salesforce.Case.get(self.case["id"])  # type: ignore[operator]
        self.assertEqual(case["Subject"], "Meeting")
        self.assertEqual(case["Zeplyn_Meeting_Tags__c"], "tag1;tag2")
        self.assertEqual(case["Attendee_Speaker_Allocations_2__c"], "")
        self.assertEqual(case["Cases_To_Create__c"], "Task one<br>\nTask two<br>\nNo title")
        self.assertEqual(case["Meeting_Summary__c"], self.note.get_summary_for_crm(use_html_formatting=True))
        self.assertEqual(case["Meeting_Duration__c"], 1)
        self.assertEqual(case["Zep_Current_Fin_Status_Discussed_Date__c"], self.case_date)
        self.assertEqual(case["Zep_Asset_Alloc_Holdings_Discussed_Date__c"], self.case_date)
        self.assertFalse("Zep_Inv_Performance_Discussed_Date__c" in self.case_date)
        self.assertEqual(case["Zep_Insurance_Planning_Discussed_Date__c"], self.case_date)

    @mock_salesforce
    def test_add_interaction_with_compliance_checklist_creation_time(self) -> None:
        self._configure()

        metadata = self.note.metadata or {}
        del metadata["scheduled_at"]
        self.note.created = self.timestamp
        self.note.metadata = metadata
        self.note.save()

        # Add an interaction
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sequoia_sf.add_interaction_with_client(self.note, sequoia_sf.resolve_sync_targets(self.note, self.user))

        # Confirm that the case was updated
        case = self.salesforce.Case.get(self.case["id"])  # type: ignore[operator]
        self.assertEqual(case["Zep_Current_Fin_Status_Discussed_Date__c"], self.case_date)
        self.assertEqual(case["Zep_Asset_Alloc_Holdings_Discussed_Date__c"], self.case_date)
        self.assertFalse("Zep_Inv_Performance_Discussed_Date__c" in case)
        self.assertEqual(case["Zep_Insurance_Planning_Discussed_Date__c"], self.case_date)

    @mock_salesforce
    def test_add_interaction_with_compliance_checklist_all_discussed(self) -> None:
        self._configure()

        self.compliance_template.data = {
            "review_entries": [
                {"id": "financial_status", "discussed": True},
                {"id": "allocations_holdings", "discussed": True},
                {"id": "investment_performance", "discussed": True},
                {"id": "insurance_needs", "discussed": True},
            ],
        }
        self.compliance_template.save()

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sequoia_sf.add_interaction_with_client(self.note, sequoia_sf.resolve_sync_targets(self.note, self.user))

        case = self.salesforce.Case.get(self.case["id"])  # type: ignore[operator]
        self.assertEqual(case["Zep_Current_Fin_Status_Discussed_Date__c"], self.case_date)
        self.assertEqual(case["Zep_Asset_Alloc_Holdings_Discussed_Date__c"], self.case_date)
        self.assertEqual(case["Zep_Inv_Performance_Discussed_Date__c"], self.case_date)
        self.assertEqual(case["Zep_Insurance_Planning_Discussed_Date__c"], self.case_date)

    @mock_salesforce
    def test_add_interaction_with_compliance_checklist_none_discussed(self) -> None:
        self._configure()

        self.compliance_template.data = {
            "review_entries": [
                {"id": "financial_status", "discussed": False},
                {"id": "allocations_holdings", "discussed": False},
                {"id": "investment_performance", "discussed": False},
                {"id": "insurance_needs", "discussed": False},
            ],
        }
        self.compliance_template.save()

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sequoia_sf.add_interaction_with_client(self.note, sequoia_sf.resolve_sync_targets(self.note, self.user))

        case = self.salesforce.Case.get(self.case["id"])  # type: ignore[operator]
        self.assertFalse("Zep_Current_Fin_Status_Discussed_Date__c" in case)
        self.assertFalse("Zep_Asset_Alloc_Holdings_Discussed_Date__c" in case)
        self.assertFalse("Zep_Inv_Performance_Discussed_Date__c" in case)
        self.assertFalse("Zep_Insurance_Planning_Discussed_Date__c" in case)

    @mock_salesforce
    def test_add_interaction_with_compliance_checklist_invalid_template(self) -> None:
        self._configure()

        self.compliance_template.data = {"invalid": "template"}
        self.compliance_template.save()

        # Add an interaction
        with self.assertLogs(level="ERROR") as cm:
            sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
            sequoia_sf.add_interaction_with_client(self.note, sequoia_sf.resolve_sync_targets(self.note, self.user))
            self.assertEqual(len(cm.output), 1)
            self.assertIn("Compliance template has invalid format.", cm.output[0])

        # Confirm that the case was updated
        case = self.salesforce.Case.get(self.case["id"])  # type: ignore[operator]
        self.assertFalse("Zep_Current_Fin_Status_Discussed_Date__c" in case)
        self.assertFalse("Zep_Asset_Alloc_Holdings_Discussed_Date__c" in case)
        self.assertFalse("Zep_Inv_Performance_Discussed_Date__c" in case)
        self.assertFalse("Zep_Insurance_Planning_Discussed_Date__c" in case)

    @mock_salesforce
    def test_add_interaction_with_compliance_checklist_invalid_entries(self) -> None:
        self._configure()

        self.compliance_template.data = {
            "unknown": "entry",
            "review_entries": [
                {"invalid": "entry"},
                {"id": "unknown"},
                # {"id": "financial_status", "discussed": True},
                {"id": "allocations_holdings"},
                {"id": "allocations_holdings", "discussed": None},
                {"id": "investment_performance", "discussed": False},
                {"id": "insurance_needs", "discussed": True},
            ],
        }
        self.compliance_template.save()

        # Add an interaction
        with self.assertLogs(level="INFO") as cm:
            sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
            sequoia_sf.add_interaction_with_client(self.note, sequoia_sf.resolve_sync_targets(self.note, self.user))

            first_log_index = cm.output.index(
                "ERROR:root:Compliance template entry has no topic ID. Skipping compliance field."
            )
            self.assertGreater(first_log_index, 0)
            self.assertIn(
                "Compliance template entry has no mapping for topic ID 'unknown'", cm.output[first_log_index + 1]
            )
            self.assertIn(
                "Compliance template entry for topic ID 'allocations_holdings' has no 'discussed' field or it is None",
                cm.output[first_log_index + 2],
            )
            self.assertIn(
                "Compliance template entry for topic ID 'allocations_holdings' has no 'discussed' field or it is None",
                cm.output[first_log_index + 3],
            )

        # Confirm that the case was updated
        case = self.salesforce.Case.get(self.case["id"])  # type: ignore[operator]
        self.assertFalse("Zep_Current_Fin_Status_Discussed_Date__c" in case)
        self.assertFalse("Zep_Asset_Alloc_Holdings_Discussed_Date__c" in case)
        self.assertFalse("Zep_Inv_Performance_Discussed_Date__c" in case)
        self.assertEqual(case["Zep_Insurance_Planning_Discussed_Date__c"], self.case_date)

    @mock_salesforce
    def test_add_interaction_sync_items_none_backward_compatibility(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        note_id = str(uuid4())
        timestamp = django_timezone.now()
        case, _ = self.newCaseAndClient(account_id=account["id"], zeplyn_id=note_id, timestamp=timestamp, org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        note = Note(
            uuid=note_id,
            note_owner=user,
            status="finalized",
            metadata={
                "meeting_name": "Meeting",
                "tags": ["tag1", "tag2"],
                "meeting_duration": 3600,
                "scheduled_at": str(timestamp),
            },
            salesforce_case_id=case["id"],
        )
        note.save()
        note.authorized_users.add(user)
        note.save()

        Task.objects.create(task_title="Task one", task_owner=user, note=note, due_date=django_timezone.now())
        Task.objects.create(task_title="Task two", task_owner=user, note=note, due_date=django_timezone.now())

        with patch.object(note, "get_summary_for_crm", return_value="Test Summary") as mock_get_summary:
            sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
            sync_targets = sequoia_sf.resolve_sync_targets(note, user)
            sequoia_sf.add_interaction_with_client(note, sync_targets, None)

            mock_get_summary.assert_called_once_with(use_html_formatting=True, sync_items=None)

        updated_case = self.salesforce.Case.get(case["id"])  # type: ignore[operator]
        self.assertEqual(updated_case["Subject"], "Meeting")
        self.assertEqual(updated_case["Meeting_Summary__c"], "Test Summary")
        self.assertEqual(updated_case["Cases_To_Create__c"], "Task one<br>\nTask two")

    @mock_salesforce
    def test_add_interaction_sync_items_empty_dict_backward_compatibility(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        note_id = str(uuid4())
        timestamp = django_timezone.now()
        case, _ = self.newCaseAndClient(account_id=account["id"], zeplyn_id=note_id, timestamp=timestamp, org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        note = Note(
            uuid=note_id,
            note_owner=user,
            status="finalized",
            metadata={
                "meeting_name": "Meeting",
                "tags": ["tag1", "tag2"],
                "meeting_duration": 3600,
                "scheduled_at": str(timestamp),
            },
            salesforce_case_id=case["id"],
        )
        note.save()
        note.authorized_users.add(user)
        note.save()

        Task.objects.create(task_title="Test Task", task_owner=user, note=note, due_date=django_timezone.now())

        with patch.object(note, "get_summary_for_crm", return_value="Test Summary") as mock_get_summary:
            sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
            sync_targets = sequoia_sf.resolve_sync_targets(note, user)
            sequoia_sf.add_interaction_with_client(note, sync_targets, {})

            mock_get_summary.assert_called_once_with(use_html_formatting=True, sync_items={})

        updated_case = self.salesforce.Case.get(case["id"])  # type: ignore[operator]
        self.assertEqual(updated_case["Meeting_Summary__c"], "Test Summary")
        self.assertEqual(updated_case["Cases_To_Create__c"], "Test Task")

    @mock_salesforce
    def test_add_interaction_sync_items_tasks_enabled_boolean(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        case, _ = self.newCaseAndClient(account_id=account["id"], org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        note = Note(
            note_owner=user,
            status="finalized",
            metadata={"meeting_name": "Meeting", "tags": ["tag1"], "meeting_duration": 1800},
            salesforce_case_id=case["id"],
        )
        note.save()
        note.authorized_users.add(user)
        note.save()

        task = Task.objects.create(task_title="Important task", task_owner=user, note=note)

        sync_items = {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True)}

        with patch.object(note, "get_summary_for_crm", return_value="Test Summary") as mock_get_summary:
            sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
            sync_targets = sequoia_sf.resolve_sync_targets(note, user)
            sequoia_sf.add_interaction_with_client(note, sync_targets, sync_items)

            mock_get_summary.assert_called_once_with(use_html_formatting=True, sync_items=sync_items)

        updated_case = self.salesforce.Case.get(case["id"])  # type: ignore[operator]
        self.assertEqual(updated_case["Meeting_Summary__c"], "Test Summary")
        self.assertEqual(updated_case["Cases_To_Create__c"], "Important task")

    @mock_salesforce
    def test_add_interaction_sync_items_tasks_disabled_boolean(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        case, _ = self.newCaseAndClient(account_id=account["id"], org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        note = Note(
            note_owner=user,
            status="finalized",
            metadata={"meeting_name": "Meeting", "tags": ["tag1"], "meeting_duration": 1800},
            salesforce_case_id=case["id"],
        )
        note.save()
        note.authorized_users.add(user)
        note.save()

        Task.objects.create(task_title="Should not sync", task_owner=user, note=note)

        sync_items = {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=False)}

        with patch.object(note, "get_summary_for_crm", return_value="Test Summary") as mock_get_summary:
            sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
            sync_targets = sequoia_sf.resolve_sync_targets(note, user)
            sequoia_sf.add_interaction_with_client(note, sync_targets, sync_items)

            mock_get_summary.assert_called_once_with(use_html_formatting=True, sync_items=sync_items)

        updated_case = self.salesforce.Case.get(case["id"])  # type: ignore[operator]
        self.assertEqual(updated_case["Meeting_Summary__c"], "Test Summary")
        self.assertEqual(updated_case["Cases_To_Create__c"], "<br>\n")

    @mock_salesforce
    def test_add_interaction_sync_items_mixed_boolean_sections(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        case, _ = self.newCaseAndClient(account_id=account["id"], org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        note = Note(
            note_owner=user,
            status="finalized",
            metadata={"meeting_name": "Meeting", "tags": ["tag1", "tag2"], "meeting_duration": 3600},
            salesforce_case_id=case["id"],
        )
        note.save()
        note.authorized_users.add(user)
        note.save()

        task = Task.objects.create(task_title="Mixed test task", task_owner=user, note=note)

        sync_items = {
            CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.ATTENDEES: CRMSyncItemSelection(include_section=False),
            CRMSyncSection.KEYWORDS: CRMSyncItemSelection(include_section=False),
        }

        with patch.object(note, "get_summary_for_crm", return_value="Mixed sections summary") as mock_get_summary:
            sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
            sync_targets = sequoia_sf.resolve_sync_targets(note, user)
            sequoia_sf.add_interaction_with_client(note, sync_targets, sync_items)

            mock_get_summary.assert_called_once_with(use_html_formatting=True, sync_items=sync_items)

        updated_case = self.salesforce.Case.get(case["id"])  # type: ignore[operator]
        self.assertEqual(updated_case["Meeting_Summary__c"], "Mixed sections summary")
        self.assertEqual(updated_case["Cases_To_Create__c"], "Mixed test task")

    @mock_salesforce
    def test_add_interaction_sync_items_no_case_targets_error_handling(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)

        note = Note.objects.create(
            note_owner=user,
            status="finalized",
            metadata={"meeting_name": "Meeting"},
        )
        note.authorized_users.add(user)
        note.save()

        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[],  # No targets
        )

        sync_items = {CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True)}

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)

        with patch.object(note, "get_summary_for_crm", return_value="Test Summary"):
            with self.assertLogs(level="ERROR") as cm:
                sequoia_sf.add_interaction_with_client(note, sync_targets, sync_items)

                self.assertTrue(any("No case provided for note" in log for log in cm.output))

    @mock_salesforce
    def test_add_interaction_sync_items_multiple_case_targets_error_handling(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)

        note = Note.objects.create(
            note_owner=user,
            status="finalized",
            metadata={"meeting_name": "Meeting"},
        )
        note.authorized_users.add(user)
        note.save()

        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(crm_id="case1", type="case", name=""),
                CRMSyncTarget(crm_id="case2", type="case", name=""),
            ],
        )

        sync_items = {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=False)}

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)

        with patch.object(note, "get_summary_for_crm", return_value="Test Summary"):
            with self.assertLogs(level="ERROR") as cm:
                sequoia_sf.add_interaction_with_client(note, sync_targets, sync_items)

                self.assertTrue(any("Multiple cases provided for note" in log for log in cm.output))

    @mock_salesforce
    def test_add_interaction_backward_compatibility_method_signature(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        case, _ = self.newCaseAndClient(account_id=account["id"], org=org)

        note = Note.objects.create(
            note_owner=user,
            status="finalized",
            metadata={"meeting_name": "Meeting"},
            salesforce_case_id=case["id"],
        )
        note.authorized_users.add(user)
        note.save()

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sync_targets = sequoia_sf.resolve_sync_targets(note, user)

        try:
            with patch.object(note, "get_summary_for_crm", return_value="test_summary"):
                sequoia_sf.add_interaction_with_client(note, sync_targets)

            self.assertTrue(True)
        except TypeError as e:
            self.fail(f"Backward compatibility broken: {e}")

    def test_list_users_no_organization(self) -> None:
        user = User(username="no_org", email="<EMAIL>")
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        with self.assertLogs(level="ERROR") as cm:
            result = sequoia_sf.list_users(user)
            self.assertEqual(result, [])
            self.assertIn("User does not belong to an organization", cm.output[0])

    @mock_salesforce
    def test_list_users_no_active_users(self) -> None:
        org = Organization.objects.create(name="Test Org")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)
        # with inactive user
        self.salesforce.User.create({"Email": user.email, "IsActive": False})  # type: ignore[operator]

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        result = sequoia_sf.list_users(user)
        self.assertEqual(result, [])

    @mock_salesforce
    def test_list_users_with_active_users_and_matching_zeplyn_user(self) -> None:
        org = Organization.objects.create(name="Test Org")
        user = User.objects.create(
            username="testuser", email="<EMAIL>", organization=org, name="Test User"
        )
        crm_user = self.salesforce.User.create({"Email": user.email, "Name": "SQ User", "IsActive": True})  # type: ignore[operator]
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        result = sequoia_sf.list_users(user)

        def expected() -> list[CRMUser]:
            return [
                CRMUser(
                    name="SQ User",
                    crm_id=crm_user["id"],
                    organization=ZeplynOrganization(uuid=str(org.uuid), name=org.name),
                    zeplyn_user=ZeplynUser(uuid=str(user.uuid), email=user.email, name=user.name),
                ),
            ]

        self.assertEqual(len(result), 1)
        crm_users = result
        assert crm_users == expected()

    @mock_salesforce
    def test_list_users_with_active_users_no_matching_zeplyn_user(self) -> None:
        org = Organization.objects.create(name="Test Org")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)
        crm_user = self.salesforce.User.create({"Email": "<EMAIL>", "Name": "SQ Only", "IsActive": True})  # type: ignore[operator]
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        result = sequoia_sf.list_users(user)

        def expected() -> list[CRMUser]:
            return [
                CRMUser(
                    name="SQ Only",
                    crm_id=crm_user["id"],
                    organization=ZeplynOrganization(uuid=str(org.uuid), name=org.name),
                    zeplyn_user=None,
                ),
            ]

        self.assertEqual(len(result), 1)
        crm_users = result
        assert crm_users == expected()
