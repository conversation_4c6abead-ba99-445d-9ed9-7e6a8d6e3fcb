import datetime
import logging
from typing import Any
from unittest.mock import ANY, MagicMock, call, patch

import pytest
from django.utils import timezone as django_timezone

from deepinsights.core.integrations.crm.crm_models import (
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTarget,
    CRMSyncTargets,
    CRMUser,
    ZeplynOrganization,
    ZeplynUser,
)
from deepinsights.core.integrations.crm.microsoft_dynamics import MicrosoftDynamics
from deepinsights.core.integrations.crm.microsoft_dynamics_utils import get_headers
from deepinsights.core.preferences.preferences import CrmConfiguration, DynamicsConfiguration
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User

pytestmark = [pytest.mark.django_db]


@pytest.fixture
def test_organization() -> Organization:
    return Organization.objects.create(name="Test Organization")


@pytest.fixture
def test_user(test_organization: Organization) -> User:
    user = User.objects.create(email="<EMAIL>", organization=test_organization)

    dynamics_config = DynamicsConfiguration(dynamics_resource_url="https://org.example.com")

    crm_config = CrmConfiguration(crm_system="microsoft_dynamics", dynamics=dynamics_config)

    # Create a custom get_crm_configuration method
    def get_crm_configuration() -> CrmConfiguration:
        return crm_config

    # Attach it to the user
    user.get_crm_configuration = get_crm_configuration  # type: ignore[method-assign]

    return user


@pytest.fixture
def test_user_without_org() -> User:
    user = User.objects.create(email="<EMAIL>", organization=None)

    dynamics_config = DynamicsConfiguration(dynamics_resource_url="https://org.example.com")

    crm_config = CrmConfiguration(crm_system="microsoft_dynamics", dynamics=dynamics_config)

    # Create a custom get_crm_configuration method
    def get_crm_configuration() -> CrmConfiguration:
        return crm_config

    # Attach it to the user
    setattr(user, "get_crm_configuration", get_crm_configuration)

    return user


@pytest.fixture
def test_client(test_user: User, test_organization: Organization) -> Client:
    client = Client.objects.create(
        name="Test Client", organization=test_organization, crm_id="123", client_type="Contact"
    )
    client.authorized_users.add(test_user)
    client.save()
    return client


@pytest.fixture
def test_note(test_user: User, test_client: Client) -> Note:
    return Note.objects.create(note_owner=test_user, client={"uuid": str(test_client.uuid)}, summary="Test Note")


@pytest.fixture
def test_task(test_note: Note) -> Task:
    return Task.objects.create(note=test_note, task_title="Test Task", due_date=django_timezone.now())


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_accounts_by_owner_email_and_name(mock_oauth: MagicMock, test_user: User) -> None:
    # Setup mock OAuth
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    # Setup mock responses
    accounts_response = {
        "value": [
            {
                "name": "Test Account",
                "accountid": "acc123",
                "telephone1": "**********",
                "emailaddress1": "<EMAIL>",
            }
        ]
    }

    contacts_response = {
        "value": [
            {
                "fullname": "John Doe",
                "firstname": "John",
                "lastname": "Doe",
                "contactid": "con123",
                "telephone1": "**********",
                "emailaddress1": "<EMAIL>",
            }
        ]
    }

    leads_response = {
        "value": [
            {
                "fullname": "Jane Smith",
                "firstname": "Jane",
                "lastname": "Smith",
                "leadid": "lead123",
                "telephone1": "**********",
                "emailaddress1": "<EMAIL>",
                "companyname": "Prospect Corp",
                "subject": "New Lead",
            }
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: accounts_response),
            MagicMock(status_code=200, json=lambda: contacts_response),
            MagicMock(status_code=200, json=lambda: leads_response),
        ]

        dynamics = MicrosoftDynamics(user=test_user)
        accounts = dynamics.get_accounts_by_owner_email_and_name("<EMAIL>")

        assert len(accounts) == 3

        entity_types = [account.client_type for account in accounts]
        assert "Account" in entity_types
        assert "Contact" in entity_types
        assert "Lead" in entity_types

        assert mock_requests_get.call_count == 3
        mock_requests_get.assert_has_calls(
            [
                call(
                    "api/data/v9.2/accounts",
                    headers=ANY,
                    params={"$select": "name,accountid,telephone1,emailaddress1"},
                ),
                call(
                    "api/data/v9.2/contacts",
                    headers=ANY,
                    params={"$select": "fullname,firstname,lastname,contactid,telephone1,emailaddress1"},
                ),
                call(
                    "api/data/v9.2/leads",
                    headers=ANY,
                    params={
                        "$select": "fullname,firstname,lastname,leadid,telephone1,emailaddress1,subject,companyname"
                    },
                ),
            ]
        )


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_accounts_by_owner_email_and_name_with_filter(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: {"value": []}),
            MagicMock(status_code=200, json=lambda: {"value": []}),
        ]

        dynamics = MicrosoftDynamics(user=test_user)
        dynamics.get_accounts_by_owner_email_and_name("<EMAIL>", "Test")

        mock_requests_get.assert_has_calls(
            [
                call(
                    "api/data/v9.2/accounts",
                    headers=ANY,
                    params={"$select": "name,accountid,telephone1,emailaddress1", "$filter": "contains(name, 'Test')"},
                ),
                call(
                    "api/data/v9.2/contacts",
                    headers=ANY,
                    params={
                        "$select": "fullname,firstname,lastname,contactid,telephone1,emailaddress1",
                        "$filter": "contains(fullname, 'Test')",
                    },
                ),
            ]
        )


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_accounts_by_owner_email_and_name_error(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.return_value = MagicMock(status_code=500, text="Server error")

        dynamics = MicrosoftDynamics(user=test_user)
        accounts = dynamics.get_accounts_by_owner_email_and_name("<EMAIL>")

        assert accounts == []


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_with_client(mock_oauth: MagicMock, test_note: Note, test_task: Task) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.post") as mock_requests_post:
        note_response = MagicMock(status_code=204)
        note_response.headers = {"OData-EntityId": "https://org.example.com/api/data/v9.2/annotations(abc123)"}

        task_response = MagicMock(status_code=204)
        task_response.headers = {"OData-EntityId": "https://org.example.com/api/data/v9.2/tasks(def456)"}

        mock_requests_post.side_effect = [note_response, task_response]

        with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
            mock_summary.return_value = "Test summary"
            mock_title.return_value = "Test Title"

            dynamics = MicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
            sync_targets = dynamics.resolve_sync_targets(test_note, test_note.note_owner)  # type: ignore[arg-type]
            dynamics.add_interaction_with_client(test_note, sync_targets)

        assert mock_requests_post.call_count == 2

        updated_note = Note.objects.get(pk=test_note.pk)
        updated_task = Task.objects.get(pk=test_task.pk)

        assert updated_note.metadata["interactionId"] == "abc123"  # type: ignore[index]
        assert "taskId" in updated_task.metadata  # type: ignore[operator]
        assert updated_task.metadata["taskId"] == "def456"  # type: ignore[index]


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_with_client_multiple_clients(
    mock_oauth: MagicMock, test_note: Note, test_task: Task, test_client: Client, caplog: pytest.LogCaptureFixture
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.post") as mock_requests_post:
        note_response = MagicMock(status_code=204)
        note_response.headers = {"OData-EntityId": "https://org.example.com/api/data/v9.2/annotations(abc123)"}

        task_response = MagicMock(status_code=204)
        task_response.headers = {"OData-EntityId": "https://org.example.com/api/data/v9.2/tasks(def456)"}

        mock_requests_post.side_effect = [note_response, task_response]

        with (
            patch.object(Note, "get_summary_for_crm") as mock_summary,
            patch.object(Note, "title") as mock_title,
            caplog.at_level(logging.WARNING),
        ):
            mock_summary.return_value = "Test summary"
            mock_title.return_value = "Test Title"

            dynamics = MicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
            dynamics.add_interaction_with_client(
                test_note,
                CRMSyncTargets(
                    status=CRMSyncTargets.Status.FINAL,
                    note_targets=[
                        CRMSyncTarget(crm_id=test_client.crm_id, type=test_client.client_type, name=test_client.name),
                        CRMSyncTarget(crm_id="client2", type="account", name="Client Two"),
                    ],
                ),
            )

        assert mock_requests_post.call_count == 2

        updated_note = Note.objects.get(pk=test_note.pk)
        updated_task = Task.objects.get(pk=test_task.pk)

        assert updated_note.metadata["interactionId"] == "abc123"  # type: ignore[index]
        assert "taskId" in updated_task.metadata  # type: ignore[operator]
        assert updated_task.metadata["taskId"] == "def456"  # type: ignore[index]
        assert caplog.messages == [f"Multiple clients provided for note {test_note.uuid}, using the first one"]


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_with_client_already_exists(mock_oauth: MagicMock, test_note: Note) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    test_note.metadata = {"interactionId": "existing123"}
    test_note.save()

    with patch("requests.post") as mock_requests_post:
        dynamics = MicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
        sync_targets = dynamics.resolve_sync_targets(test_note, test_note.note_owner)  # type: ignore[arg-type]
        dynamics.add_interaction_with_client(test_note, sync_targets)

        mock_requests_post.assert_not_called()


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_with_client_no_clients(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    note = Note.objects.create(note_owner=test_user, summary="No client note")
    with patch("requests.post") as mock_requests_post:
        dynamics = MicrosoftDynamics(user=test_user)

        with pytest.raises(Exception) as exc_info:
            sync_targets = dynamics.resolve_sync_targets(note, test_user)
            dynamics.add_interaction_with_client(note, sync_targets)

        assert "No primary client specified in the note" in str(exc_info.value)
        mock_requests_post.assert_not_called()


@pytest.mark.parametrize(
    "sync_items,expected_tasks_synced",
    [
        # Backward compatibility
        (None, True),
        ({}, True),
        (
            {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True, included_items=[])},
            True,
        ),
        ({CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=False, included_items=[])}, False),
        (
            {CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True)},
            True,
        ),  # Tasks not specified, defaults to True
        (
            {CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=False)},
            True,
        ),  # Tasks not specified, defaults to True
        # Mixed sections
        (
            {
                CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True, included_items=[]),
                CRMSyncSection.ATTENDEES: CRMSyncItemSelection(include_section=False),
            },
            True,
        ),
        (
            {
                CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=False, included_items=[]),
                CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True),
            },
            False,
        ),
    ],
)
@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_with_client_sync_items_parametrized(
    mock_oauth: MagicMock,
    test_note: Note,
    test_task: Task,
    sync_items: dict[CRMSyncSection, CRMSyncItemSelection] | None,
    expected_tasks_synced: bool,
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    # patch setup here since we don't have test_task available in setup
    if expected_tasks_synced and sync_items:
        sync_items[CRMSyncSection.TASKS] = CRMSyncItemSelection(
            include_section=True, included_items=[str(test_task.uuid)]
        )

    with patch("requests.post") as mock_requests_post:
        note_response = MagicMock(status_code=204)
        note_response.headers = {"OData-EntityId": "https://org.example.com/api/data/v9.2/annotations(abc123)"}

        task_response = MagicMock(status_code=204)
        task_response.headers = {"OData-EntityId": "https://org.example.com/api/data/v9.2/tasks(def456)"}

        if expected_tasks_synced:
            mock_requests_post.side_effect = [note_response, task_response]
        else:
            mock_requests_post.return_value = note_response

        with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
            mock_summary.return_value = "Test summary"
            mock_title.return_value = "Test Title"

            dynamics = MicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
            sync_targets = dynamics.resolve_sync_targets(test_note, test_note.note_owner)  # type: ignore[arg-type]
            dynamics.add_interaction_with_client(test_note, sync_targets, sync_items)

        mock_summary.assert_called_once_with(use_html_formatting=False, sync_items=sync_items)

        if expected_tasks_synced:
            assert mock_requests_post.call_count == 2

            note_call = mock_requests_post.call_args_list[0]
            assert "annotations" in note_call[0][0]
            assert note_call[1]["json"]["subject"] == "Test Title"
            assert note_call[1]["json"]["notetext"] == "Test summary"

            task_call = mock_requests_post.call_args_list[1]
            assert "tasks" in task_call[0][0]
            assert task_call[1]["json"]["subject"] == "Test Task"
        else:
            assert mock_requests_post.call_count == 1

            note_call = mock_requests_post.call_args_list[0]
            assert "annotations" in note_call[0][0]
            assert note_call[1]["json"]["subject"] == "Test Title"

        updated_note = Note.objects.get(pk=test_note.pk)
        assert updated_note.metadata["interactionId"] == "abc123"  # type: ignore[index]


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_create_task_success(mock_oauth: MagicMock, test_user: User, test_task: Task) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.post") as mock_requests_post:
        mock_response = MagicMock(status_code=204)
        mock_response.headers = {"OData-EntityId": "https://org.example.com/api/data/v9.2/tasks(task123)"}
        mock_requests_post.return_value = mock_response

        dynamics = MicrosoftDynamics(user=test_user)
        api_url = "https://org.example.com/api/data/v9.2/"
        headers = get_headers("mock_token")
        client = {"id": "123", "type": "contact"}

        dynamics._create_task(api_url, headers, test_task, client)

        updated_task = Task.objects.get(pk=test_task.pk)

        assert "taskId" in updated_task.metadata  # type: ignore[operator]
        assert updated_task.metadata["taskId"] == "task123"  # type: ignore[index]

        mock_requests_post.assert_called_once_with(
            "https://org.example.com/api/data/v9.2/tasks",
            headers=headers,
            json={
                "subject": "Test Task",
                "description": "",
                "scheduledend": ANY,
                "statecode": 0,
                "prioritycode": 1,
                "<EMAIL>": "/contacts(123)",
            },
        )


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_create_task_success_with_200_char_title(mock_oauth: MagicMock, test_user: User, test_task: Task) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    title = "A" * 200
    test_task.task_title = title
    test_task.task_desc = "test description"
    test_task.save()

    with patch("requests.post") as mock_requests_post:
        mock_response = MagicMock(status_code=204)
        mock_response.headers = {"OData-EntityId": "https://org.example.com/api/data/v9.2/tasks(task123)"}
        mock_requests_post.return_value = mock_response

        dynamics = MicrosoftDynamics(user=test_user)
        api_url = "https://org.example.com/api/data/v9.2/"
        headers = get_headers("mock_token")
        client = {"id": "123", "type": "contact"}

        dynamics._create_task(api_url, headers, test_task, client)

        mock_requests_post.assert_called_once()
        called_json = mock_requests_post.call_args[1]["json"]
        assert called_json["subject"] == title
        assert called_json["description"] == test_task.task_desc


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_create_task_with_title_over_200_chars(mock_oauth: MagicMock, test_user: User, test_task: Task) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    long_title = "B" * 250
    test_task.task_title = long_title
    test_task.task_desc = "test long subject description"
    test_task.save()

    with patch("requests.post") as mock_requests_post:
        mock_response = MagicMock(status_code=204)
        mock_response.headers = {"OData-EntityId": "https://org.example.com/api/data/v9.2/tasks(task123)"}
        mock_requests_post.return_value = mock_response

        dynamics = MicrosoftDynamics(user=test_user)
        api_url = "https://org.example.com/api/data/v9.2/"
        headers = get_headers("mock_token")
        client = {"id": "123", "type": "contact"}

        dynamics._create_task(api_url, headers, test_task, client)

        mock_requests_post.assert_called_once()
        called_json = mock_requests_post.call_args[1]["json"]
        assert len(called_json["subject"]) <= 200
        assert called_json["subject"] == test_task.task_title[: 200 - 1] + "…"
        expected_description = f"Subject: {long_title}\n\n Description:\n{test_task.task_desc}"
        assert called_json["description"] == expected_description


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_create_task_with_empty_title_and_description(mock_oauth: MagicMock, test_user: User, test_task: Task) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"
    test_task.task_title = ""
    test_task.task_desc = None
    test_task.save()

    with patch("requests.post") as mock_requests_post:
        mock_response = MagicMock(status_code=204)
        mock_response.headers = {"OData-EntityId": "https://org.example.com/api/data/v9.2/tasks(task123)"}
        mock_requests_post.return_value = mock_response

        dynamics = MicrosoftDynamics(user=test_user)
        api_url = "https://org.example.com/api/data/v9.2/"
        headers = get_headers("mock_token")
        client = {"id": "123", "type": "contact"}

        dynamics._create_task(api_url, headers, test_task, client)

        mock_requests_post.assert_called_once()
        called_json = mock_requests_post.call_args[1]["json"]
        assert called_json["subject"] == "No Subject"
        assert called_json["description"] == ""


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_create_task_with_subject_with_whitespaces(mock_oauth: MagicMock, test_user: User, test_task: Task) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"
    test_task.task_title = "       "
    test_task.save()

    with patch("requests.post") as mock_requests_post:
        mock_response = MagicMock(status_code=204)
        mock_response.headers = {"OData-EntityId": "https://org.example.com/api/data/v9.2/tasks(task123)"}
        mock_requests_post.return_value = mock_response

        dynamics = MicrosoftDynamics(user=test_user)
        api_url = "https://org.example.com/api/data/v9.2/"
        headers = get_headers("mock_token")
        client = {"id": "123", "type": "contact"}

        dynamics._create_task(api_url, headers, test_task, client)

        mock_requests_post.assert_called_once()
        called_json = mock_requests_post.call_args[1]["json"]
        assert called_json["subject"] == "No Subject"
        assert called_json["description"] == ""


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_create_task_error(mock_oauth: MagicMock, test_user: User, test_task: Task) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.post") as mock_requests_post, patch("logging.error") as mock_error:
        mock_response = MagicMock(status_code=500, text="Server error")
        mock_requests_post.return_value = mock_response

        dynamics = MicrosoftDynamics(user=test_user)
        dynamics._create_task("https://example.com/api/", {}, test_task, {"id": "123", "type": "contact"})

        mock_error.assert_called_once()


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_events_not_implemented(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("logging.warning") as mock_warning:
        dynamics = MicrosoftDynamics(user=test_user)
        events = dynamics.fetch_events(test_user, datetime.timedelta(days=30))

        assert events == []
        mock_warning.assert_called_once()


@pytest.mark.parametrize("completed,expected_state", [(True, 1), (False, 0)])
@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_create_task_state(
    mock_oauth: MagicMock, test_user: User, test_note: Note, completed: bool, expected_state: int
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    task = Task.objects.create(note=test_note, task_title="Test Task", completed=completed)

    with patch("requests.post") as mock_requests_post:
        mock_response = MagicMock(status_code=204)
        mock_response.headers = {"OData-EntityId": "https://org.example.com/api/data/v9.2/tasks(task123)"}
        mock_requests_post.return_value = mock_response

        dynamics = MicrosoftDynamics(user=test_user)
        api_url = "https://org.example.com/api/data/v9.2/"
        headers = get_headers("mock_token")
        client = {"id": "123", "type": "contact"}

        dynamics._create_task(api_url, headers, task, client)

        mock_requests_post.assert_called_once()
        json_data = mock_requests_post.call_args[1]["json"]
        assert json_data["statecode"] == expected_state


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_success(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    notes_response = {
        "value": [
            {
                "annotationid": "note123",
                "subject": "Test Subject",
                "notetext": "Test note content",
                "createdon": "2023-01-01T12:00:00Z",
            },
            {
                "annotationid": "note456",
                "subject": "Another Subject",
                "notetext": "Another test note",
                "createdon": "2023-01-02T14:30:00Z",
            },
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: notes_response)

        dynamics = MicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, datetime.timedelta(days=30))

        assert mock_requests_get.call_count == 1

        call_args = mock_requests_get.call_args[1]
        assert "_objectid_value eq" in call_args["params"]["$filter"]
        assert test_client.crm_id in call_args["params"]["$filter"]
        assert "createdon ge" in call_args["params"]["$filter"]

        assert len(notes) == 2
        assert notes[0].crm_id == "note123"
        assert notes[0].crm_system == "microsoft_dynamics"
        assert notes[0].content == "Test Subject\nTest note content"
        assert isinstance(notes[0].created_at, datetime.datetime)
        assert notes[1].crm_id == "note456"
        assert "entityrecord&etn=annotation&id=" in str(notes[0].web_link)


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_with_trailing_slash_url(
    mock_oauth: MagicMock, test_user: User, test_client: Client
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    notes_response = {
        "value": [
            {
                "annotationid": "note123",
                "subject": "Test Subject",
                "notetext": "Test note content",
                "createdon": "2023-01-01T12:00:00Z",
            }
        ]
    }

    def get_crm_configuration_with_trailing_slash() -> CrmConfiguration:
        dynamics_config = DynamicsConfiguration(dynamics_resource_url="https://org.example.com/")
        return CrmConfiguration(crm_system="microsoft_dynamics", dynamics=dynamics_config)

    original_get_crm_configuration = test_user.get_crm_configuration

    test_user.get_crm_configuration = get_crm_configuration_with_trailing_slash  # type: ignore[method-assign]

    try:
        with patch("requests.get") as mock_requests_get:
            mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: notes_response)

            dynamics = MicrosoftDynamics(user=test_user)
            notes = dynamics.fetch_notes_for_client(test_user, test_client, datetime.timedelta(days=30))

            assert len(notes) == 1

            web_link_str = str(notes[0].web_link)

            assert "https://org.example.com/main.aspx" in web_link_str
            assert "//main.aspx" not in web_link_str

            expected_url_pattern = (
                f"https://org.example.com/main.aspx?pagetype=entityrecord&etn=annotation&id={notes[0].crm_id}"
            )
            assert expected_url_pattern in web_link_str
    finally:
        # Restore the original method
        test_user.get_crm_configuration = original_get_crm_configuration  # type: ignore[method-assign]


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_no_crm_id(
    mock_oauth: MagicMock, test_user: User, test_organization: Organization
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    organization = test_user.organization
    assert organization is not None

    client = Client.objects.create(
        name="Test Client Without CRM ID", organization=test_organization, client_type="Contact"
    )
    client.authorized_users.add(test_user)

    with patch("requests.get") as mock_requests_get, patch("logging.error") as mock_error:
        dynamics = MicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, client, datetime.timedelta(days=30))

        mock_requests_get.assert_not_called()

        assert notes == []
        mock_error.assert_called_once()
        assert "does not have a CRM ID" in mock_error.call_args[0][0]


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_api_error(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.get") as mock_requests_get, patch("logging.error") as mock_error:
        mock_requests_get.return_value = MagicMock(
            status_code=401, text="Unauthorized", json=lambda: {"error": "Unauthorized"}
        )

        dynamics = MicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, datetime.timedelta(days=30))

        assert notes == []
        mock_error.assert_called_once()
        assert "Error fetching notes from Dynamics" in mock_error.call_args[0][0]


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_empty_result(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: {"value": []})

        dynamics = MicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, datetime.timedelta(days=30))

        assert notes == []
        mock_requests_get.assert_called_once()


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_parse_error(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    notes_response = {
        "value": [
            {
                "annotationid": "note123",
                "subject": "Test Subject",
                "notetext": "Test note content",
                "createdon": "Invalid date format",
            }
        ]
    }

    with patch("requests.get") as mock_requests_get, patch("logging.error") as mock_error:
        mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: notes_response)

        dynamics = MicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, datetime.timedelta(days=30))

        assert len(notes) == 1
        assert notes[0].crm_id == "note123"
        assert notes[0].created_at is None

        mock_error.assert_called_once()
        assert "Error parsing date" in mock_error.call_args[0][0]


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_oauth_error(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.side_effect = Exception("OAuth error")

    with patch("logging.error") as mock_error:
        dynamics = MicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, datetime.timedelta(days=30))

        # Verify empty result and error logging
        assert notes == []
        mock_error.assert_called_once()
        assert "Error fetching notes from Dynamics" in mock_error.call_args[0][0]


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_crmnote_creation_error(
    mock_oauth: MagicMock, test_user: User, test_client: Client
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    notes_response = {
        "value": [
            {
                "annotationid": "note123",
                "subject": "Test Subject",
                "notetext": "Test note content",
                "createdon": "2023-01-01T12:00:00Z",
            },
            {
                "subject": "Missing ID",
                "notetext": "This note will cause an error",
                "createdon": "2023-01-02T12:00:00Z",
            },
            {
                "annotationid": "note456",
                "subject": "Valid Note",
                "notetext": "This note should be processed correctly",
                "createdon": "2023-01-03T12:00:00Z",
            },
        ]
    }

    with patch("requests.get") as mock_requests_get, patch("logging.error") as mock_error:
        mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: notes_response)

        with patch("deepinsights.core.integrations.crm.microsoft_dynamics.CRMNote") as mock_crm_note:

            def side_effect(*args, **kwargs):  # type: ignore[no-untyped-def]
                if mock_crm_note.call_count == 2:  # Second call (0-indexed)
                    raise ValueError("Error creating CRMNote")
                return MagicMock()

            mock_crm_note.side_effect = side_effect

            dynamics = MicrosoftDynamics(user=test_user)
            notes = dynamics.fetch_notes_for_client(test_user, test_client, datetime.timedelta(days=30))

            assert len(notes) == 2

            assert mock_error.call_count >= 1

            error_message_found = False
            for call_args in mock_error.call_args_list:
                if "Error creating CRMNote object for note" in call_args[0][0]:
                    error_message_found = True
                    break

            assert error_message_found, "Error message about CRMNote creation not found in logs"


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_resolve_sync_targets_with_client(
    mock_oauth: MagicMock, test_user: User, test_note: Note, test_client: Client
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    dynamics = MicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(test_note, test_user)

    assert sync_targets.status == CRMSyncTargets.Status.FINAL
    assert sync_targets.note_targets == [
        CRMSyncTarget(crm_id=test_client.crm_id, type=test_client.client_type, name=test_client.name)
    ]


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_resolve_sync_targets_no_client(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    note = Note.objects.create(note_owner=test_user, summary="Note without client")

    dynamics = MicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(note, test_user)

    assert sync_targets.status == CRMSyncTargets.Status.CLIENTS_REQUIRED
    assert not sync_targets.note_targets


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_resolve_sync_targets_client_no_uuid(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    note = Note.objects.create(note_owner=test_user, client={}, summary="Note with empty client")

    dynamics = MicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(note, test_user)

    assert sync_targets.status == CRMSyncTargets.Status.CLIENTS_REQUIRED
    assert not sync_targets.note_targets


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_resolve_sync_targets_client_not_found(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    note = Note.objects.create(
        note_owner=test_user,
        client={"uuid": "00000000-0000-0000-0000-000000000000"},
        summary="Note with non-existent client",
    )

    dynamics = MicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(note, test_user)

    assert sync_targets.status == CRMSyncTargets.Status.CLIENTS_REQUIRED
    assert not sync_targets.note_targets


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_resolve_sync_targets_client_no_crm_id(
    mock_oauth: MagicMock, test_user: User, test_organization: Organization
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    client = Client.objects.create(name="Client Without CRM ID", organization=test_organization, client_type="Contact")
    client.authorized_users.add(test_user)

    note = Note.objects.create(
        note_owner=test_user, client={"uuid": str(client.uuid)}, summary="Note with client without CRM ID"
    )

    dynamics = MicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(note, test_user)

    assert sync_targets.status == CRMSyncTargets.Status.CLIENTS_REQUIRED
    assert not sync_targets.note_targets


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_resolve_sync_targets_with_previous_targets(
    mock_oauth: MagicMock, test_user: User, test_note: Note, test_client: Client
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    previous_targets = CRMSyncTargets(
        status=CRMSyncTargets.Status.RESOLUTION_REQUIRED,
        note_targets=[CRMSyncTarget(crm_id="old_id", type="contact", name="Old Client")],
    )

    dynamics = MicrosoftDynamics(user=test_user)
    sync_targets = dynamics.resolve_sync_targets(test_note, test_user, previous_targets)

    assert sync_targets.status == CRMSyncTargets.Status.FINAL
    assert sync_targets.note_targets == [
        CRMSyncTarget(crm_id=test_client.crm_id, type=test_client.client_type, name=test_client.name)
    ]


@patch("requests.get")
def test_list_users_no_organization(mock_get: MagicMock, test_user_without_org: User) -> None:
    dynamics = MicrosoftDynamics(user=test_user_without_org)
    result = dynamics.list_users(test_user_without_org)
    assert result == []
    mock_get.assert_not_called()


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
@patch("requests.get")
def test_list_users_no_access_token(mock_get: MagicMock, mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = None

    dynamics = MicrosoftDynamics(user=test_user)

    with pytest.raises(Exception, match=f"Could not get access token for user {test_user.uuid}"):
        dynamics.list_users(test_user)

    mock_get.assert_not_called()


@patch("requests.get")
@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_list_users_empty_wb_users(mock_oauth: MagicMock, mock_get: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    dynamics = MicrosoftDynamics(user=test_user)
    mock_users_response = MagicMock()
    mock_users_response.status_code = 200
    mock_users_response.json.return_value = {"users": [], "meta": {"total_pages": 1}}

    mock_get.return_value = mock_users_response
    result = dynamics.list_users(test_user)
    assert result == []
    mock_get.assert_has_calls(
        [
            call(
                "https://org.example.com/api/data/v9.2/systemusers",
                headers=ANY,
                params={"$select": "fullname,systemuserid,internalemailaddress", "$filter": "statecode eq 0"},
            )
        ]
    )


@patch("requests.get")
@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_list_users_success(
    mock_oauth: MagicMock, mock_requests_get: MagicMock, test_user: User, test_organization: Organization
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"
    dynamics = MicrosoftDynamics(user=test_user)

    users_response = {
        "value": [
            {
                "systemuserid": "mdid1",
                "fullname": "CRM User 1",
                "internalemailaddress": "<EMAIL>",
            },
            {
                "systemuserid": "mdid2",
                "fullname": "CRM User 2",
                "internalemailaddress": "<EMAIL>",
            },
        ]
    }
    mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: users_response)

    # Create a matching zeplyn user
    matched_user = User.objects.create(
        email="<EMAIL>",
        organization=test_organization,
        name="Matched User",
        username="matched_user",
    )
    result = dynamics.list_users(test_user)

    def expected() -> list[CRMUser]:
        return [
            CRMUser(
                name="CRM User 1",
                crm_id="mdid1",
                organization=ZeplynOrganization(uuid=str(test_organization.uuid), name=test_organization.name),
                zeplyn_user=ZeplynUser(uuid=str(matched_user.uuid), email=matched_user.email, name=matched_user.name),
            ),
            CRMUser(
                name="CRM User 2",
                crm_id="mdid2",
                organization=ZeplynOrganization(uuid=str(test_organization.uuid), name=test_organization.name),
                zeplyn_user=None,
            ),
        ]

    assert len(result) == 2
    assert result == expected()
    mock_requests_get.assert_has_calls(
        [
            call(
                "https://org.example.com/api/data/v9.2/systemusers",
                headers=ANY,
                params={"$select": "fullname,systemuserid,internalemailaddress", "$filter": "statecode eq 0"},
            )
        ]
    )


@patch("requests.get")
@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_list_users_exception_handling(
    mock_oauth: MagicMock, mock_requests_get: MagicMock, test_user: User, test_organization: Organization
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"
    dynamics = MicrosoftDynamics(user=test_user)

    users_response: dict[str, list[dict[str, Any]]] = {"value": []}
    mock_requests_get.return_value = MagicMock(
        status_code=200, json=lambda: users_response, side_effect=Exception("API error")
    )

    result = dynamics.list_users(test_user)
    assert result == []
    mock_requests_get.assert_has_calls(
        [
            call(
                "https://org.example.com/api/data/v9.2/systemusers",
                headers=ANY,
                params={"$select": "fullname,systemuserid,internalemailaddress", "$filter": "statecode eq 0"},
            )
        ]
    )


@patch("requests.get")
@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_list_users_handles_validation_error_in_crm_user(
    mock_oauth: MagicMock,
    mock_requests_get: MagicMock,
    test_user: User,
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"
    dynamics = MicrosoftDynamics(user=test_user)

    users_response = {
        "value": [
            {
                # systemuserid is required but set to None to cause ValidationError
                "systemuserid": None,
                "fullname": "CRM User 1",
                "internalemailaddress": "<EMAIL>",
            },
        ]
    }
    mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: users_response)

    result = dynamics.list_users(test_user)

    # Expected is empty list because CRMUser creation failed
    assert result == []


@patch("requests.get")
@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_list_users_handles_partial_validation_errors(
    mock_oauth: MagicMock, mock_requests_get: MagicMock, test_user: User, test_organization: Organization
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"
    dynamics = MicrosoftDynamics(user=test_user)
    # case when one of the users returned from the CRM API is invalid, but others are valid
    # it will return only the valid users and log the error for the invalid one
    users_response = {
        "value": [
            {
                "systemuserid": "valid-id-1",
                "fullname": "CRM User 1",
                "internalemailaddress": "<EMAIL>",
            },
            {
                "systemuserid": None,  # Invalid, triggers ValidationError
                "fullname": "Invalid User",
                "internalemailaddress": "<EMAIL>",
            },
            {
                "systemuserid": "valid-id-2",
                "fullname": "CRM User 2",
                "internalemailaddress": "<EMAIL>",
            },
        ]
    }
    mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: users_response)
    # Create a matching zeplyn user
    matched_user = User.objects.create(
        email="<EMAIL>",
        organization=test_organization,
        name="Matched User",
        username="matched_user",
    )
    result = dynamics.list_users(test_user)

    def expected() -> list[CRMUser]:
        return [
            CRMUser(
                name="CRM User 1",
                crm_id="valid-id-1",
                organization=ZeplynOrganization(uuid=str(test_organization.uuid), name=test_organization.name),
                zeplyn_user=ZeplynUser(uuid=str(matched_user.uuid), email=matched_user.email, name=matched_user.name),
            ),
            CRMUser(
                name="CRM User 2",
                crm_id="valid-id-2",
                organization=ZeplynOrganization(uuid=str(test_organization.uuid), name=test_organization.name),
                zeplyn_user=None,
            ),
        ]

    # Only valid users should be returned (first and third)
    assert len(result) == 2

    assert result == expected()
    mock_requests_get.assert_has_calls(
        [
            call(
                "https://org.example.com/api/data/v9.2/systemusers",
                headers=ANY,
                params={"$select": "fullname,systemuserid,internalemailaddress", "$filter": "statecode eq 0"},
            )
        ]
    )
