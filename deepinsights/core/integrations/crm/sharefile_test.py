import logging
from datetime import datetime
from unittest.mock import MagicMock, patch

from django.test import TestCase

from deepinsights.core.integrations.crm.crm_models import (
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTarget,
    CRMSyncTargets,
)
from deepinsights.core.integrations.crm.sharefile import ShareFile
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


class TestShareFile(TestCase):
    def setUp(self):  # type: ignore[no-untyped-def]
        self.sharefile = ShareFile(
            client_id="test_client_id",
            client_secret="test_client_secret",
            hostname="test.sharefile.com",
            username="test_user",
            password="test_password",
            sharefile_dir="test_dir",
        )

    @patch("requests.post")
    def test_authenticate(self, mock_post):  # type: ignore[no-untyped-def]
        # Mock the response from requests.post
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"access_token": "test_access_token", "refresh_token": "test_refresh_token"}
        mock_post.return_value = mock_response

        # Call the authenticate method
        self.sharefile.authenticate()  # type: ignore[no-untyped-call]

        mock_post.assert_called_once_with(
            f"https://{self.sharefile.hostname}/oauth/token",
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            data={
                "grant_type": "password",
                "client_id": self.sharefile.client_id,
                "client_secret": self.sharefile.client_secret,
                "username": self.sharefile.username,
                "password": self.sharefile.password,
            },
        )

        # Assert that the access_token and refresh_token were set correctly
        self.assertEqual(self.sharefile.access_token, "test_access_token")
        self.assertEqual(self.sharefile.refresh_token, "test_refresh_token")

    @patch("requests.post")
    def test_refresh_auth_token(self, mock_post):  # type: ignore[no-untyped-def]
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"access_token": "new_access_token", "refresh_token": "new_refresh_token"}
        mock_post.return_value = mock_response

        self.sharefile.refresh_token = "old_refresh_token"  # type: ignore[assignment]
        self.sharefile.refresh_auth_token()  # type: ignore[no-untyped-call]

        self.assertEqual(self.sharefile.access_token, "new_access_token")
        self.assertEqual(self.sharefile.refresh_token, "new_refresh_token")

    @patch("http.client.HTTPSConnection")
    def test_get_folder_id(self, mock_connection):  # type: ignore[no-untyped-def]
        mock_response = MagicMock()
        mock_response.status = 200
        mock_response.read.return_value = b'{"value": [{"Id": "test_folder_id"}]}'
        mock_connection.return_value.getresponse.return_value = mock_response

        folder_id = self.sharefile.get_folder_id("test_folder")  # type: ignore[no-untyped-call]

        self.assertEqual(folder_id, "test_folder_id")

    @patch("http.client.HTTPSConnection")
    def test_create_folder(self, mock_connection):  # type: ignore[no-untyped-def]
        mock_response = MagicMock()
        mock_response.status = 200
        mock_response.read.return_value = b'{"Id": "new_folder_id"}'
        mock_connection.return_value.getresponse.return_value = mock_response

        folder_id = self.sharefile.create_folder("parent_id", "new_folder")  # type: ignore[no-untyped-call]

        self.assertEqual(folder_id, "new_folder_id")

    @patch.object(ShareFile, "get_folder_id")
    @patch.object(ShareFile, "create_folder")
    @patch.object(ShareFile, "upload_file")
    def test_add_interaction_with_client(self, mock_upload_file, mock_create_folder, mock_get_folder_id):  # type: ignore[no-untyped-def]
        mock_get_folder_id.side_effect = ["parent_id", "owner_folder_id"]
        mock_create_folder.return_value = "new_owner_folder_id"
        mock_upload_file.return_value = True

        note = MagicMock(spec=Note)
        note.note_owner.name = "Test Owner"
        note.metadata = {"meeting_name": "Test Meeting"}
        note.title.return_value = "Test Title"
        note.get_summary_for_crm.return_value = "Test Summary"
        note.get_attendees.return_value = ["Client 1", "Client 2"]

        sync_targets = self.sharefile.resolve_sync_targets(note, User.objects.create(username="unused"))
        self.sharefile.add_interaction_with_client(note, sync_targets)

        mock_get_folder_id.assert_called()
        mock_upload_file.assert_called()

    @patch.object(ShareFile, "get_folder_id")
    @patch.object(ShareFile, "create_folder")
    @patch.object(ShareFile, "upload_file")
    @patch.object(ShareFile, "refresh_auth_token")
    def test_add_interaction_sync_items_none_backward_compatibility(
        self,
        mock_refresh_auth: MagicMock,
        mock_upload_file: MagicMock,
        mock_create_folder: MagicMock,
        mock_get_folder_id: MagicMock,
    ) -> None:
        mock_get_folder_id.side_effect = ["parent_id", "owner_folder_id"]
        mock_upload_file.return_value = True

        note = MagicMock(spec=Note)
        note.note_owner.name = "Test Owner"
        note.metadata = {"meeting_name": "Test Meeting"}
        note.created = datetime.now()
        note.title.return_value = "Test Title"
        note.get_attendees.return_value = ["Client 1", "Client 2"]

        with patch.object(note, "get_summary_for_crm", return_value="test_note_summary") as mock_get_summary:
            sync_targets = self.sharefile.resolve_sync_targets(note, User.objects.create(username="unused"))
            self.sharefile.add_interaction_with_client(note, sync_targets, None)

            mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items=None)

        mock_upload_file.assert_called_once()

    @patch.object(ShareFile, "get_folder_id")
    @patch.object(ShareFile, "create_folder")
    @patch.object(ShareFile, "upload_file")
    @patch.object(ShareFile, "refresh_auth_token")
    def test_add_interaction_sync_items_empty_dict_backward_compatibility(
        self,
        mock_refresh_auth: MagicMock,
        mock_upload_file: MagicMock,
        mock_create_folder: MagicMock,
        mock_get_folder_id: MagicMock,
    ) -> None:
        mock_get_folder_id.side_effect = ["parent_id", "owner_folder_id"]
        mock_upload_file.return_value = True

        note = MagicMock(spec=Note)
        note.note_owner.name = "Test Owner"
        note.metadata = {"meeting_name": "Test Meeting"}
        note.created = datetime.now()
        note.title.return_value = "Test Title"
        note.get_attendees.return_value = ["Client 1", "Client 2"]

        with patch.object(note, "get_summary_for_crm", return_value="test_note_summary") as mock_get_summary:
            sync_targets = self.sharefile.resolve_sync_targets(note, User.objects.create(username="unused"))
            self.sharefile.add_interaction_with_client(note, sync_targets, {})

            mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items={})

        mock_upload_file.assert_called_once()

    @patch.object(ShareFile, "get_folder_id")
    @patch.object(ShareFile, "create_folder")
    @patch.object(ShareFile, "upload_file")
    @patch.object(ShareFile, "refresh_auth_token")
    def test_add_interaction_sync_items_summary_enabled_boolean(
        self,
        mock_refresh_auth: MagicMock,
        mock_upload_file: MagicMock,
        mock_create_folder: MagicMock,
        mock_get_folder_id: MagicMock,
    ) -> None:
        mock_get_folder_id.side_effect = ["parent_id", "owner_folder_id"]
        mock_upload_file.return_value = True

        note = MagicMock(spec=Note)
        note.note_owner.name = "Test Owner"
        note.metadata = {"meeting_name": "Test Meeting"}
        note.created = datetime.now()
        note.title.return_value = "Test Title"
        note.get_attendees.return_value = ["Client 1", "Client 2"]

        sync_items = {CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True)}

        with patch.object(note, "get_summary_for_crm", return_value="test_note_summary") as mock_get_summary:
            sync_targets = self.sharefile.resolve_sync_targets(note, User.objects.create(username="unused"))
            self.sharefile.add_interaction_with_client(note, sync_targets, sync_items)

            mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items=sync_items)

        mock_upload_file.assert_called_once()

        upload_call_args = mock_upload_file.call_args
        file_content = upload_call_args[0][2]  # Third argument is the content
        self.assertIn("test_note_summary", file_content)

    @patch.object(ShareFile, "get_folder_id")
    @patch.object(ShareFile, "create_folder")
    @patch.object(ShareFile, "upload_file")
    @patch.object(ShareFile, "refresh_auth_token")
    def test_add_interaction_sync_items_summary_disabled_boolean(
        self,
        mock_refresh_auth: MagicMock,
        mock_upload_file: MagicMock,
        mock_create_folder: MagicMock,
        mock_get_folder_id: MagicMock,
    ) -> None:
        mock_get_folder_id.side_effect = ["parent_id", "owner_folder_id"]
        mock_upload_file.return_value = True

        note = MagicMock(spec=Note)
        note.note_owner.name = "Test Owner"
        note.metadata = {"meeting_name": "Test Meeting"}
        note.created = datetime.now()
        note.title.return_value = "Test Title"
        note.get_attendees.return_value = ["Client 1", "Client 2"]

        sync_items = {CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=False)}

        with patch.object(note, "get_summary_for_crm", return_value="filtered_summary_content") as mock_get_summary:
            sync_targets = self.sharefile.resolve_sync_targets(note, User.objects.create(username="unused"))
            self.sharefile.add_interaction_with_client(note, sync_targets, sync_items)

            mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items=sync_items)

        mock_upload_file.assert_called_once()

        upload_call_args = mock_upload_file.call_args
        file_content = upload_call_args[0][2]  # Third argument is the content
        self.assertIn("filtered_summary_content", file_content)

    @patch.object(ShareFile, "get_folder_id")
    @patch.object(ShareFile, "create_folder")
    @patch.object(ShareFile, "upload_file")
    @patch.object(ShareFile, "refresh_auth_token")
    def test_add_interaction_sync_items_mixed_boolean_sections(
        self,
        mock_refresh_auth: MagicMock,
        mock_upload_file: MagicMock,
        mock_create_folder: MagicMock,
        mock_get_folder_id: MagicMock,
    ) -> None:
        mock_get_folder_id.side_effect = ["parent_id", "owner_folder_id"]
        mock_upload_file.return_value = True

        note = MagicMock(spec=Note)
        note.note_owner.name = "Test Owner"
        note.metadata = {"meeting_name": "Test Meeting"}
        note.created = datetime.now()
        note.title.return_value = "Test Title"
        note.get_attendees.return_value = ["Client 1", "Client 2"]

        sync_items = sync_items = {
            CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.ATTENDEES: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.KEYWORDS: CRMSyncItemSelection(include_section=False),
            CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=False),
        }

        with patch.object(note, "get_summary_for_crm", return_value="mixed_sections_summary") as mock_get_summary:
            sync_targets = self.sharefile.resolve_sync_targets(note, User.objects.create(username="unused"))
            self.sharefile.add_interaction_with_client(note, sync_targets, sync_items)

            mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items=sync_items)

        mock_upload_file.assert_called_once()

    @patch.object(ShareFile, "get_folder_id")
    @patch.object(ShareFile, "create_folder")
    @patch.object(ShareFile, "upload_file")
    @patch.object(ShareFile, "refresh_auth_token")
    def test_add_interaction_sync_items_preserves_existing_error_handling(
        self,
        mock_refresh_auth: MagicMock,
        mock_upload_file: MagicMock,
        mock_create_folder: MagicMock,
        mock_get_folder_id: MagicMock,
    ) -> None:
        mock_get_folder_id.return_value = None  # Simulate parent directory not found

        note = MagicMock(spec=Note)
        note.note_owner.name = "Test Owner"
        note.metadata = {"meeting_name": "Test Meeting"}
        note.created = datetime.now()
        note.title.return_value = "Test Title"
        note.get_attendees.return_value = ["Client 1", "Client 2"]

        sync_items = {CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True)}

        with patch.object(note, "get_summary_for_crm", return_value="test_note_summary"):
            sync_targets = self.sharefile.resolve_sync_targets(note, User.objects.create(username="unused"))

            with self.assertLogs(level=logging.ERROR) as cm:
                with self.assertRaisesRegex(Exception, "Parent directory .* not found"):
                    self.sharefile.add_interaction_with_client(note, sync_targets, sync_items)

            self.assertTrue(any("Parent directory" in log for log in cm.output))

        mock_upload_file.assert_not_called()

    @patch.object(ShareFile, "get_folder_id")
    @patch.object(ShareFile, "create_folder")
    @patch.object(ShareFile, "upload_file")
    @patch.object(ShareFile, "refresh_auth_token")
    def test_add_interaction_sync_items_upload_failure_handling(
        self,
        mock_refresh_auth: MagicMock,
        mock_upload_file: MagicMock,
        mock_create_folder: MagicMock,
        mock_get_folder_id: MagicMock,
    ) -> None:
        mock_get_folder_id.side_effect = ["parent_id", "owner_folder_id"]
        mock_upload_file.return_value = False  # Simulate upload failure

        note = MagicMock(spec=Note)
        note.note_owner.name = "Test Owner"
        note.metadata = {"meeting_name": "Test Meeting"}
        note.created = datetime.now()
        note.title.return_value = "Test Title"
        note.get_attendees.return_value = ["Client 1", "Client 2"]

        sync_items = {CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True)}

        with patch.object(note, "get_summary_for_crm", return_value="test_note_summary") as mock_get_summary:
            sync_targets = self.sharefile.resolve_sync_targets(note, User.objects.create(username="unused"))

            with self.assertLogs(level=logging.ERROR) as cm:
                with self.assertRaisesRegex(Exception, "Failed to upload file .* to ShareFile"):
                    self.sharefile.add_interaction_with_client(note, sync_targets, sync_items)

            mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items=sync_items)

            self.assertTrue(any("Failed to upload file" in log for log in cm.output))

        mock_upload_file.assert_called_once()

    @patch.object(ShareFile, "get_folder_id")
    @patch.object(ShareFile, "create_folder")
    @patch.object(ShareFile, "upload_file")
    @patch.object(ShareFile, "refresh_auth_token")
    def test_add_interaction_sync_items_folder_creation_when_owner_folder_missing(
        self,
        mock_refresh_auth: MagicMock,
        mock_upload_file: MagicMock,
        mock_create_folder: MagicMock,
        mock_get_folder_id: MagicMock,
    ) -> None:
        # First call returns parent_id, second call raises exception (owner folder not found)
        mock_get_folder_id.side_effect = ["parent_id", Exception("Folder 'Test Owner' not found in parent 'parent_id'")]
        mock_create_folder.return_value = "new_owner_folder_id"
        mock_upload_file.return_value = True

        note = MagicMock(spec=Note)
        note.note_owner.name = "Test Owner"
        note.metadata = {"meeting_name": "Test Meeting"}
        note.created = datetime.now()
        note.title.return_value = "Test Title"
        note.get_attendees.return_value = ["Client 1", "Client 2"]

        sync_items = {CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True)}

        with patch.object(note, "get_summary_for_crm", return_value="test_note_summary") as mock_get_summary:
            sync_targets = self.sharefile.resolve_sync_targets(note, User.objects.create(username="unused"))
            self.sharefile.add_interaction_with_client(note, sync_targets, sync_items)

            mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items=sync_items)

        mock_create_folder.assert_called_once_with("parent_id", "Test Owner")

        mock_upload_file.assert_called_once()
        upload_call_args = mock_upload_file.call_args
        self.assertEqual(upload_call_args[0][0], "new_owner_folder_id")  # First argument is owner_folder_id

    def test_add_interaction_backward_compatibility_method_signature(self) -> None:
        note = MagicMock(spec=Note)
        note.note_owner.name = "Test Owner"
        note.metadata = {"meeting_name": "Test Meeting"}
        note.created = datetime.now()
        note.title.return_value = "Test Title"
        note.get_attendees.return_value = ["Client 1", "Client 2"]

        sync_targets = self.sharefile.resolve_sync_targets(note, User.objects.create(username="unused"))

        try:
            with patch.object(note, "get_summary_for_crm", return_value="test_note_summary"):
                with patch.object(self.sharefile, "get_folder_id", side_effect=["parent_id", "owner_folder_id"]):
                    with patch.object(self.sharefile, "upload_file", return_value=True):
                        with patch.object(self.sharefile, "refresh_auth_token"):
                            self.sharefile.add_interaction_with_client(note, sync_targets)
            self.assertTrue(True)  # Test passes if no exception is raised
        except TypeError as e:
            self.fail(f"Backward compatibility broken: {e}")

    @patch.object(ShareFile, "get_folder_id")
    @patch.object(ShareFile, "create_folder")
    @patch.object(ShareFile, "upload_file")
    @patch.object(ShareFile, "refresh_auth_token")
    def test_add_interaction_sync_items_file_content_structure(
        self,
        mock_refresh_auth: MagicMock,
        mock_upload_file: MagicMock,
        mock_create_folder: MagicMock,
        mock_get_folder_id: MagicMock,
    ) -> None:
        mock_get_folder_id.side_effect = ["parent_id", "owner_folder_id"]
        mock_upload_file.return_value = True

        note = MagicMock(spec=Note)
        note.note_owner.name = "Test Owner"
        note.metadata = {"meeting_name": "Test Meeting"}
        note.created = datetime.now()
        note.title.return_value = "Test Title"
        note.get_attendees.return_value = ["Client 1", "Client 2"]

        sync_items = {CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True)}

        with patch.object(note, "get_summary_for_crm", return_value="custom_summary_content") as mock_get_summary:
            sync_targets = self.sharefile.resolve_sync_targets(note, User.objects.create(username="unused"))
            self.sharefile.add_interaction_with_client(note, sync_targets, sync_items)

            mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items=sync_items)

        mock_upload_file.assert_called_once()
        upload_call_args = mock_upload_file.call_args

        file_content = upload_call_args[0][2]
        self.assertIn("Title: Test Title", file_content)
        self.assertIn("Summary: custom_summary_content", file_content)
        self.assertIn("Clients: Client 1, Client 2", file_content)

        filename = upload_call_args[0][1]  # Second argument is the filename
        self.assertTrue(filename.endswith("_Test Meeting.txt"))

    def test_get_accounts_by_owner_email_and_name(self):  # type: ignore[no-untyped-def]
        user = MagicMock(spec=User)
        result = self.sharefile.get_accounts_by_owner_email_and_name(user.email)
        self.assertEqual(result, [])

    @patch.object(ShareFile, "authenticate")
    def test_sharefile_initialization_with_settings(self, mock_authenticate):  # type: ignore[no-untyped-def]
        with self.settings(
            SHAREFILE_CLIENT_ID="settings_client_id",
            SHAREFILE_CLIENT_SECRET="settings_client_secret",
            SHAREFILE_HOSTNAME="settings.sharefile.com",
            SHAREFILE_USERNAME="settings_user",
            SHAREFILE_PASSWORD="settings_password",
            SHAREFILE_PARENTDIR="settings_dir",
        ):
            sharefile = ShareFile()

            self.assertEqual(sharefile.client_id, "settings_client_id")
            self.assertEqual(sharefile.client_secret, "settings_client_secret")
            self.assertEqual(sharefile.hostname, "settings.sharefile.com")
            self.assertEqual(sharefile.username, "settings_user")
            self.assertEqual(sharefile.password, "settings_password")
            self.assertEqual(sharefile.sharefile_parentdir, "settings_dir")

            mock_authenticate.assert_called_once()

    def test_resolve_sync_targets(self) -> None:
        note = MagicMock(spec=Note)
        user = MagicMock(spec=User)

        result = self.sharefile.resolve_sync_targets(note, user)

        self.assertEqual(result, CRMSyncTargets(status=CRMSyncTargets.Status.FINAL, note_targets=[]))

    def test_resolve_sync_targets_with_selected_targets(self) -> None:
        note = MagicMock(spec=Note)
        user = MagicMock(spec=User)

        selected_sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[CRMSyncTarget(crm_id="note1", type="note", name="Note 1")],
        )

        result = self.sharefile.resolve_sync_targets(note, user, selected_sync_targets)

        self.assertEqual(
            result,
            CRMSyncTargets(status=CRMSyncTargets.Status.FINAL, note_targets=[]),
        )
