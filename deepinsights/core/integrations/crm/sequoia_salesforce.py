import datetime
import logging
from typing import Any, Mapping

import pytz
import requests
from django.conf import settings
from simple_salesforce.api import Salesforce

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.crm import salesforce_utils
from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.crm_models import (
    CRMAccount,
    CRMNote,
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTarget,
    CRMSyncTargets,
    CRMUser,
    CRMWorkflow,
)
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingDataTemplate
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User


class SequoiaSalesforce(CrmBase):
    CASE_FIELDS = [
        "Id",
        "AccountId",
        "Account.Name",
        "Account.Service_Team_User_Ids__c",
        "Attendees__c",
        "CaseNumber",
        "Client_Review_Start_Time__c",
        "Subject",
        "Z<PERSON>lyn_Id__c",
        "Client_Review_Location__c",
        "Meeting_Link__c",
        "IsClosed",
    ]
    CASE_SELECT = f"SELECT {', '.join(CASE_FIELDS)} FROM Case"

    def __init__(self, salesforce: Salesforce | None = None) -> None:
        if salesforce:
            self.sf = salesforce
            return
        oauth_url = settings.SEQUOIA_SALESFORCE_OAUTH_URL
        # Salesforce client credentials
        client_id = settings.SEQUOIA_SALESFORCE_CLIENT_ID
        client_secret = settings.SEQUOIA_SALESFORCE_CLIENT_SECRET
        # Define payload for authentication
        payload = {
            "grant_type": "client_credentials",
            "client_id": client_id,
            "client_secret": client_secret,
        }
        # Request OAuth token
        response = requests.post(oauth_url, data=payload)
        auth_data = response.json()
        # Check if authentication is successful
        if "access_token" in auth_data:
            access_token = auth_data["access_token"]

            # Authenticate with Salesforce using access token
            self.sf = Salesforce(instance_url=auth_data["instance_url"], session_id=access_token)
        else:
            logging.error("Authentication failed: %d, %s", response.status_code, auth_data)

    def _get_title_from_case(self, case: dict[str, Any]) -> str:
        return f"Meeting with {case['Account']['Name']} - {case['CaseNumber']}"

    def _get_user_id_by_email(self, user_email: str) -> str | None:
        """
        Get the Salesforce User ID by email address.
        :param user_email: Email address of the Salesforce user
        :return: Salesforce User ID or None if not found
        """
        # Query for the user ID based on email
        query = f"SELECT Id FROM User WHERE Email = '{user_email}' LIMIT 1"
        result = self.sf.query_all(query)

        # Extract user ID if found
        if result["totalSize"] == 1:
            user_id = result["records"][0]["Id"]
            return user_id  # type: ignore[no-any-return]
        else:
            return None

    # Given a time string in the ISO8601 format, or a similar format with the "T" separator,
    # extract a datetime.
    def _parse_isolike_time(self, time_str: str) -> datetime.datetime:
        formats = [
            "%Y-%m-%dT%H:%M:%S.%f%z",  # With fractional seconds
            "%Y-%m-%d %H:%M:%S.%f%z",  # Space separated with fractional seconds
            "%Y-%m-%dT%H:%M:%S%z",  # Without fractional seconds
            "%Y-%m-%d %H:%M:%S%z",  # Space separated without fractional seconds
        ]

        for fmt in formats:
            try:
                return datetime.datetime.strptime(time_str, fmt)
            except ValueError:
                continue
        raise ValueError(f"time data '{time_str}' does not match any expected formats")

    def get_accounts_by_owner_email_and_name(
        self, owner_email: str, account_name_filter: str | None = None
    ) -> list[CRMAccount]:
        sf_user_id = self._get_user_id_by_email(owner_email)
        if not sf_user_id:
            logging.warning("No SF user found with email '%s'", owner_email)
            sf_user_id = "0054P00000Bv3ZzQAJ"
        # Build the query based on the provided parameters
        query = f"{self.CASE_SELECT} WHERE Account.Service_Team_User_Ids__c like '%{sf_user_id}%'"
        logging.info(query)
        # Execute the query using the global Salesforce connection
        result = self.sf.query_all(query)

        # Extracting data from the result
        cases = result["records"]

        # Create a list to store dictionaries with account data
        accounts_list = []

        # Populate the list with dictionaries containing account ID, name, owner ID, and full name
        for case in cases:
            if case["IsClosed"]:
                continue
            account = CRMAccount(
                crm_id=case["Id"],
                name=self._get_title_from_case(case),
                client_type="case",
                crm_system="sequoia_sf",
            )
            accounts_list.append(account)

        return accounts_list

    # Given a note, return the case ID that it corresponds to.
    #
    # This method is complex, to handle older patterns of how cases were associated with Zeplyn
    # notes. Originally, Sequoia cases were associated with notes via `salesforce_case_id`; later,
    # we added support for other CRMs syncing via `note.client`, but did not fully migrate the Sequoia
    # integration to use this until much later.
    def _case_id_for_note(self, note: Note) -> str | None:
        if client_uuid_or_crm_id := (note.client or {}).get("uuid"):
            try:
                return Client.objects.get(uuid=client_uuid_or_crm_id).crm_id
            except Exception:
                # There is no client that matches the UUID. The UUID could be a Salesforce case ID,
                # but we know that Salesforce Case ID is a case ID, so return that if it's present.
                if salesforce_case_id := note.salesforce_case_id:
                    return salesforce_case_id

                # In the absence of a Salesforce Case ID, assume the client UUID is the Salesforce
                # CRM ID, if it's not a valid client ID. The exception caught above is generic
                # because, if client_uuid_or_crm_id is not a valid UUID, the Client.objects.get()
                # call will raise a different exception than "Client.DoesNotExist".
                return client_uuid_or_crm_id  # type: ignore[no-any-return]

        # If there is no client UUID, then the note is not associated with a client, so use the
        # (deprecated) salesforce_case_id.
        return note.salesforce_case_id

    # Adds information from a compliance template (if it exists) to the case data.
    def _update_case_with_compliance_fields(self, note: Note, case_data: dict[str, Any]) -> None:
        if not (
            compliance_template := note.structuredmeetingdata_set.filter(
                kind=StructuredMeetingDataTemplate.Kind.SEQUOIA_COMPLIANCE_CHECKLIST
            ).first()
        ):
            logging.info("Note does not have a compliance template. Skipping compliance fields.")
            return

        metadata = note.metadata or {}
        meeting_timestamp = metadata.get("scheduled_at") or note.created.isoformat()

        # TODO: This should select the timezone based on the caller's timezone.
        meeting_date = (
            self._parse_isolike_time(meeting_timestamp).astimezone(pytz.timezone("US/Eastern")).strftime("%Y-%m-%d")
        )

        topic_id_to_case_field = {
            "financial_status": "Zep_Current_Fin_Status_Discussed_Date__c",
            "allocations_holdings": "Zep_Asset_Alloc_Holdings_Discussed_Date__c",
            "investment_performance": "Zep_Inv_Performance_Discussed_Date__c",
            "insurance_needs": "Zep_Insurance_Planning_Discussed_Date__c",
        }

        if not (entries := compliance_template.data.get("review_entries")):
            logging.error("Compliance template has invalid format. Skipping compliance fields.")
            return

        for entry in entries:
            if not (topic_id := entry.get("id")):
                logging.error("Compliance template entry has no topic ID. Skipping compliance field.")
                continue
            if not (field_name := topic_id_to_case_field.get(topic_id)):
                logging.info(
                    "Compliance template entry has no mapping for topic ID '%s'. Note that not all fields have mappings.",
                    topic_id,
                )
                continue
            discussed = entry.get("discussed")
            if discussed is None:
                logging.error(
                    "Compliance template entry for topic ID '%s' has no 'discussed' field or it is None. Skipping compliance field.",
                    topic_id,
                )
                continue
            if not discussed:
                continue
            case_data[field_name] = meeting_date

    def resolve_sync_targets(
        self, note: Note, user: User, selected_sync_targets: CRMSyncTargets | None = None
    ) -> CRMSyncTargets:
        if not (case_id := self._case_id_for_note(note)):
            logging.error("Note has neither case ID nor valid client UUID nor valid client case ID in CRM ID.")
            return CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])

        return CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[CRMSyncTarget(crm_id=case_id, type="case", name="")],
        )

    def add_interaction_with_client(
        self,
        note: Note,
        sync_targets: CRMSyncTargets,
        sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = None,
    ) -> None:
        """
        This is the mapping between note and case:
        Zeplyn Field	Case Field Label	Case Field API Name	Data Type (Data Format)
        Meeting Date/Time	Meeting Start Date/Time	Client_Review_Start_Time__c	Date/Time
        Key words Tagged	Zeplyn Meeting Tags	Zeplyn_Meeting_Tags__c	Multi-Select Picklist (Semicolon seperated)
        Meeting Attendees with % of time speaking	Attendee Speaker Allocations	Attendee_Speaker_Allocations__c	Text (255 character limit)
        Action Items	Cases to Create	Cases_To_Create__c	Rich Text (32768 character limit)
        Key Takeaways	Meeting Summary	Meeting_Summary__c	Rich Text (32768 character limit)
        Advisor Notes	Meeting Summary	Meeting_Summary__c	Rich Text (32768 character limit)
        Meeting Duration	Meeting Duration	Meeting_Duration__c	Number (1 decimal)
        Meeting Summary	Meeting Summary	Meeting_Summary__c	Rich Text (32768 character limit)
        [Unique Id]	Zeplyn Id	Zeplyn_Id__c	Text (50 character limit; case-sensitive)

        This also maps fields between the compliance follow-up template and the case, setting dates
        on fields in the case if the template indicates that the related topic was discussed.

        """

        def case_to_create(task: Task) -> str:
            return task.task_title or "No title"

        if not sync_targets.note_targets:
            logging.error("No case provided for note '%s'. Cannot update case in Salesforce.", note.uuid)
            return
        if len(sync_targets.note_targets) > 1:
            logging.error("Multiple cases provided for note '%s'. Cannot update case in Salesforce.", note.uuid)
            return

        case_id = sync_targets.note_targets[0].crm_id

        cases_to_create = "<br>\n"
        if note.should_include_section(sync_items, CRMSyncSection.TASKS):
            task_cases = []
            for task in note.get_tasks_to_include_crm_sync(sync_items=sync_items):
                task_case = case_to_create(task)
                task_cases.append(task_case)

            cases_to_create = cases_to_create.join(task_cases)

        metadata = note.metadata or {}
        case_data = {
            "Zeplyn_Meeting_Tags__c": ";".join(metadata.get("tags", [])),
            "Attendee_Speaker_Allocations_2__c": ", ".join(note.get_attendees()),
            "Cases_To_Create__c": cases_to_create,
            "Meeting_Summary__c": note.get_summary_for_crm(use_html_formatting=True, sync_items=sync_items),
            "Meeting_Duration__c": int(metadata.get("meeting_duration", 0)) / 3600,
        }
        self._update_case_with_compliance_fields(note, case_data)
        logging.info("Updating case '%s' with data: %s", case_id, case_data)
        self.sf.Case.update(case_id, case_data)  # type: ignore[operator]

    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        logging.warning("get_client_basic_info not implemented for Seqouia Salesforce")
        return None

    def fetch_events(self, user: User, interval: datetime.timedelta) -> list[CalendarEvent]:
        logging.warning("fetch_events not implemented for Sequoia Salesforce")
        return []

    def fetch_notes_for_client(self, user: User, client: Client, interval: datetime.timedelta) -> list[CRMNote]:
        logging.warning("fetch_notes_for_client not implemented for Sequoia Salesforce")
        return []

    def fetch_workflows_for_user(self, user: User) -> list[CRMWorkflow]:
        logging.warning("fetch_workflows_for_user not implemented for Sequoia Salesforce")
        return []

    def _get_active_users(self) -> list[dict[str, Any]]:
        query = """
            SELECT Id, Name, Email
            FROM User
            WHERE IsActive = true
            ORDER BY CreatedDate Desc
        """
        results = self.sf.query_all(query)
        return list(results.get("records", []))

    def list_users(self, requesting_user: User) -> list[CRMUser]:
        if not requesting_user.organization:
            error_msg = "User does not belong to an organization. Cannot list Sequoia Salesforce CRM users."
            logging.error(error_msg)
            return []

        return salesforce_utils.list_crm_users(self.sf, requesting_user.organization)
