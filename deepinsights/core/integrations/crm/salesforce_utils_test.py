from datetime import datetime, timed<PERSON><PERSON>
from datetime import timezone as datetime_timezone
from typing import Any
from unittest.mock import MagicMock, patch

from django.test import TestCase
from simple_mockforce import mock_salesforce
from simple_salesforce.api import Salesforce

from deepinsights.core.integrations.crm.crm_models import CRMAccount
from deepinsights.core.integrations.crm.salesforce_utils import (
    fetch_call_tasks_for_client,
    fetch_events_for_client,
    get_salesforce_entity_by_owner_email_and_name,
    salesforceTime,
)


@patch("deepinsights.core.integrations.crm.salesforce_utils.get_user_id_by_email")
def test_get_salesforce_entity_by_owner_email_and_name_no_entity_filter(mock_get_user_id: MagicMock) -> None:
    mock_get_user_id.return_value = "001USER1"

    mock_sf = MagicMock(spec=Salesforce)
    mock_sf.query_all.return_value = {
        "totalSize": 2,
        "records": [
            {"Id": "1", "Name": "Test Account 1", "Phone": "***********", "OwnerId": "001USER1"},
            {"Id": "2", "Name": "Test Account 2", "Phone": "***********", "OwnerId": "002USER2"},
        ],
    }

    result = get_salesforce_entity_by_owner_email_and_name(
        mock_sf,
        "Account",
        "<EMAIL>",
    )

    assert result == [
        CRMAccount(
            crm_id="1",
            name="Test Account 1",
            phone_number="***********",
            client_type="account",
            crm_system="salesforce",
            is_owned_by_user=True,
        ),
        CRMAccount(
            crm_id="2",
            name="Test Account 2",
            phone_number="***********",
            client_type="account",
            crm_system="salesforce",
            is_owned_by_user=False,
        ),
    ]

    mock_sf.query_all.assert_called_once_with("SELECT Id, Name, Phone, OwnerId FROM Account")


@patch("deepinsights.core.integrations.crm.salesforce_utils.get_user_id_by_email")
def test_get_salesforce_entity_by_owner_email_and_name_contact(mock_get_user_id: MagicMock) -> None:
    mock_get_user_id.return_value = "001USER1"

    mock_sf = MagicMock(spec=Salesforce)
    mock_sf.query_all.return_value = {"totalSize": 0, "records": []}

    result = get_salesforce_entity_by_owner_email_and_name(
        mock_sf,
        "Contact",
        "<EMAIL>",
    )

    assert not result

    mock_sf.query_all.assert_called_once_with("SELECT Id, Name, Phone, OwnerId FROM Contact")


@patch("deepinsights.core.integrations.crm.salesforce_utils.get_user_id_by_email")
def test_get_salesforce_entity_by_owner_email_and_name_with_entity_filter(mock_get_user_id: MagicMock) -> None:
    mock_get_user_id.return_value = "001USER1"

    mock_sf = MagicMock(spec=Salesforce)
    mock_sf.query_all.return_value = {
        "totalSize": 1,
        "records": [
            {"Id": "3", "Name": "Filtered Account", "Phone": None, "OwnerId": "001USER1"},
        ],
    }

    result = get_salesforce_entity_by_owner_email_and_name(
        sf=mock_sf,
        entity_name="Account",
        owner_email="<EMAIL>",
        entity_filter="Filtered",
    )

    assert result == [
        CRMAccount(
            crm_id="3",
            name="Filtered Account",
            phone_number=None,
            client_type="account",
            crm_system="salesforce",
            is_owned_by_user=True,
        )
    ]

    mock_sf.query_all.assert_called_once_with(
        "SELECT Id, Name, Phone, OwnerId FROM Account WHERE Name LIKE '%Filtered%'"
    )


@patch("deepinsights.core.integrations.crm.salesforce_utils.get_user_id_by_email")
def test_get_salesforce_entity_by_owner_email_and_name_escapes_input(mock_get_user_id: MagicMock) -> None:
    mock_get_user_id.return_value = "001USER1"

    mock_sf = MagicMock(spec=Salesforce)
    mock_sf.query_all.return_value = {"totalSize": 0, "records": []}

    get_salesforce_entity_by_owner_email_and_name(
        sf=mock_sf,
        entity_name="Account",
        owner_email="owner'<EMAIL>",
        entity_filter="Name'Filter",
    )

    mock_sf.query_all.assert_called_once_with(
        "SELECT Id, Name, Phone, OwnerId FROM Account WHERE Name LIKE '%Name\\'Filter%'"
    )


@patch("deepinsights.core.integrations.crm.salesforce_utils.get_user_id_by_email")
def test_get_salesforce_entity_by_owner_email_and_name_with_custom_filter(mock_get_user_id: MagicMock) -> None:
    mock_get_user_id.return_value = "001USER1"

    mock_sf = MagicMock(spec=Salesforce)
    mock_sf.query_all.return_value = {
        "totalSize": 1,
        "records": [
            {"Id": "3", "Name": "Filtered Account", "Phone": None, "OwnerId": "001USER1"},
        ],
    }

    result = get_salesforce_entity_by_owner_email_and_name(
        sf=mock_sf,
        entity_name="Account",
        owner_email="<EMAIL>",
        entity_filter=None,
        custom_filter="Foo = 'Bar'",
    )

    assert result == [
        CRMAccount(
            crm_id="3",
            name="Filtered Account",
            phone_number=None,
            client_type="account",
            crm_system="salesforce",
            is_owned_by_user=True,
        )
    ]

    mock_sf.query_all.assert_called_once_with("SELECT Id, Name, Phone, OwnerId FROM Account WHERE Foo = 'Bar'")


@patch("deepinsights.core.integrations.crm.salesforce_utils.get_user_id_by_email")
def test_get_salesforce_entity_by_owner_email_and_name_with_entity_filter_and_custom_filter(
    mock_get_user_id: MagicMock,
) -> None:
    mock_get_user_id.return_value = "001USER1"

    mock_sf = MagicMock(spec=Salesforce)
    mock_sf.query_all.return_value = {
        "totalSize": 1,
        "records": [
            {"Id": "3", "Name": "Filtered Account", "Phone": None, "OwnerId": "001USER1"},
        ],
    }

    result = get_salesforce_entity_by_owner_email_and_name(
        sf=mock_sf,
        entity_name="Account",
        owner_email="<EMAIL>",
        entity_filter="Filtered",
        custom_filter="Foo = 'Bar'",
    )

    assert result == [
        CRMAccount(
            crm_id="3",
            name="Filtered Account",
            phone_number=None,
            client_type="account",
            crm_system="salesforce",
            is_owned_by_user=True,
        )
    ]

    mock_sf.query_all.assert_called_once_with(
        "SELECT Id, Name, Phone, OwnerId FROM Account WHERE Name LIKE '%Filtered%' AND Foo = 'Bar'"
    )


class SalesforceUtilsTestCase(TestCase):
    def setUp(self) -> None:
        self.simple_salesforce = Salesforce(instance="test.salesforce.com", session_id="")

    @mock_salesforce
    def test_fetch_events_for_client_successful(self) -> None:
        client_crm_id = "***************"

        event_time = datetime.now(datetime_timezone.utc)
        event = self.simple_salesforce.Event.create(
            {  # type: ignore[operator]
                "Subject": "Test Event",
                "WhatId": client_crm_id,
                "ActivityDateTime": event_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "Description": "Test event description",
            }
        )

        start_date = datetime.now(datetime_timezone.utc) - timedelta(days=365)
        notes = fetch_events_for_client(self.simple_salesforce, client_crm_id, start_date)

        self.assertEqual(len(notes), 1)
        self.assertEqual(notes[0].crm_id, event["id"])
        self.assertEqual(notes[0].content, "Event: Test Event\n\nTest event description")
        self.assertEqual(notes[0].created_at, event_time)
        self.assertEqual(str(notes[0].web_link), f"https://test.salesforce.com/{event['id']}")

    @mock_salesforce
    def test_fetch_events_for_client_no_description(self) -> None:
        client_crm_id = "***************"

        event_time = datetime.now(datetime_timezone.utc)
        self.simple_salesforce.Event.create(
            {  # type: ignore[operator]
                "Subject": "Test Event",
                "WhatId": client_crm_id,
                "ActivityDateTime": event_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                # No Description field
            }
        )

        start_date = datetime.now(datetime_timezone.utc) - timedelta(days=365)
        notes = fetch_events_for_client(self.simple_salesforce, client_crm_id, start_date)

        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_events_for_client_empty_description(self) -> None:
        client_crm_id = "***************"

        event_time = datetime.now(datetime_timezone.utc)
        self.simple_salesforce.Event.create(
            {  # type: ignore[operator]
                "Subject": "Test Event",
                "WhatId": client_crm_id,
                "ActivityDateTime": event_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "Description": "   ",  # Empty/whitespace description
            }
        )

        start_date = datetime.now(datetime_timezone.utc) - timedelta(days=365)
        notes = fetch_events_for_client(self.simple_salesforce, client_crm_id, start_date)

        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_events_for_client_date_filter(self) -> None:
        client_crm_id = "***************"

        # Create old event (more than 30 days ago)
        old_date = datetime.now(datetime_timezone.utc) - timedelta(days=40)
        self.simple_salesforce.Event.create(
            {  # type: ignore[operator]
                "Subject": "Old Event",
                "WhatId": client_crm_id,
                "ActivityDateTime": old_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "Description": "Old event description",
            }
        )

        recent_date = datetime.now(datetime_timezone.utc)
        recent_event = self.simple_salesforce.Event.create(
            {  # type: ignore[operator]
                "Subject": "Recent Event",
                "WhatId": client_crm_id,
                "ActivityDateTime": recent_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "Description": "Recent event description",
            }
        )

        start_date = datetime.now(datetime_timezone.utc) - timedelta(days=30)
        notes = fetch_events_for_client(self.simple_salesforce, client_crm_id, start_date)

        self.assertEqual(len(notes), 1)
        self.assertEqual(notes[0].crm_id, recent_event["id"])

    @mock_salesforce
    def test_fetch_events_for_client_salesforce_error(self) -> None:
        client_crm_id = "***************"

        def raise_error(*args, **kwargs):  # type: ignore[no-untyped-def]
            raise Exception("Salesforce API Error")

        original_query = self.simple_salesforce.query_all
        self.simple_salesforce.query_all = raise_error  # type: ignore[method-assign]

        start_date = datetime.now(datetime_timezone.utc) - timedelta(days=365)
        notes = fetch_events_for_client(self.simple_salesforce, client_crm_id, start_date)

        self.simple_salesforce.query_all = original_query  # type: ignore[method-assign]

        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_events_for_client_multiple_events(self) -> None:
        client_crm_id = "***************"

        event_ids = []
        event_start_time = datetime.now(datetime_timezone.utc)
        for i in range(3):
            event = self.simple_salesforce.Event.create(
                {  # type: ignore[operator]
                    "Subject": f"Test Event {i}",
                    "WhatId": client_crm_id,
                    "ActivityDateTime": event_start_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                    "Description": f"Test event description {i}",
                }
            )
            event_ids.append(event["id"])

        start_date = datetime.now(datetime_timezone.utc) - timedelta(days=365)
        notes = fetch_events_for_client(self.simple_salesforce, client_crm_id, start_date)

        self.assertEqual(len(notes), 3)
        for i, note in enumerate(notes):
            self.assertEqual(note.crm_id, event_ids[i])
            self.assertEqual(note.content, f"Event: Test Event {i}\n\nTest event description {i}")
            self.assertEqual(note.created_at, event_start_time)

    @mock_salesforce
    def test_fetch_events_for_client_invalid_date(self) -> None:
        client_crm_id = "***************"

        event = self.simple_salesforce.Event.create(
            {  # type: ignore[operator]
                "Subject": "Test Event",
                "WhatId": client_crm_id,
                "ActivityDateTime": "invalid-date",
                "Description": "Test event description",
            }
        )

        start_date = datetime.now(datetime_timezone.utc) - timedelta(days=365)
        notes = fetch_events_for_client(self.simple_salesforce, client_crm_id, start_date)

        self.assertEqual(len(notes), 1)
        self.assertEqual(notes[0].crm_id, event["id"])
        self.assertEqual(notes[0].content, "Event: Test Event\n\nTest event description")
        self.assertIsNone(notes[0].created_at)

    @mock_salesforce
    def test_fetch_call_tasks_for_client_successful(self) -> None:
        client_crm_id = "***************"

        current_time = datetime.now(datetime_timezone.utc) - timedelta(days=1)
        task_date = (datetime.now(datetime_timezone.utc) - timedelta(days=1)).date()
        task = self.simple_salesforce.Task.create(
            {  # type: ignore[operator]
                "Subject": "Test Call",
                "WhatId": client_crm_id,
                "ActivityDate": task_date.isoformat(),
                "Description": "Test call description",
                "TaskSubtype": "Call",
                "CreatedDate": salesforceTime(current_time),
            }
        )

        original_query_all = self.simple_salesforce.query_all

        def mock_query_all(query: str) -> dict[str, Any]:
            if "FROM Task" in query and "TaskSubtype = 'Call'" in query:
                task_record = self.simple_salesforce.Task.get(task["id"])  # type: ignore[operator]
                if (
                    task_record.get("WhatId") == client_crm_id
                    and task_record.get("TaskSubtype") == "Call"
                    and task_record.get("Description")
                ):
                    return {"records": [task_record]}
                return {"records": []}
            else:
                return original_query_all(query)

        with patch.object(self.simple_salesforce, "query_all", side_effect=mock_query_all):
            start_date = datetime.now(datetime_timezone.utc) - timedelta(days=365)
            notes = fetch_call_tasks_for_client(self.simple_salesforce, client_crm_id, start_date)

            self.assertEqual(len(notes), 1)
            self.assertEqual(notes[0].crm_id, task["id"])
            self.assertEqual(notes[0].content, "Call: Test Call\n\nTest call description")
            expected_date = datetime.combine(task_date, datetime.min.time()).replace(tzinfo=datetime_timezone.utc)
            self.assertEqual(notes[0].created_at, expected_date)
            self.assertEqual(str(notes[0].web_link), f"https://test.salesforce.com/{task['id']}")

    @mock_salesforce
    def test_fetch_call_tasks_for_client_no_description(self) -> None:
        client_crm_id = "***************"

        task_date = datetime.now(datetime_timezone.utc).date()
        self.simple_salesforce.Task.create(
            {  # type: ignore[operator]
                "Subject": "Test Call",
                "WhatId": client_crm_id,
                "ActivityDate": task_date.strftime("%Y-%m-%d"),
                "TaskSubtype": "Call",
                # No Description field
            }
        )

        start_date = datetime.now(datetime_timezone.utc) - timedelta(days=365)
        notes = fetch_call_tasks_for_client(self.simple_salesforce, client_crm_id, start_date)

        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_call_tasks_for_client_empty_description(self) -> None:
        client_crm_id = "***************"

        task_date = datetime.now(datetime_timezone.utc).date()
        self.simple_salesforce.Task.create(
            {  # type: ignore[operator]
                "Subject": "Test Call",
                "WhatId": client_crm_id,
                "ActivityDate": task_date.strftime("%Y-%m-%d"),
                "Description": "   ",  # Empty/whitespace description
                "TaskSubtype": "Call",
            }
        )

        start_date = datetime.now(datetime_timezone.utc) - timedelta(days=365)
        notes = fetch_call_tasks_for_client(self.simple_salesforce, client_crm_id, start_date)

        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_call_tasks_for_client_non_call_task(self) -> None:
        client_crm_id = "***************"

        task_date = datetime.now(datetime_timezone.utc).date()
        self.simple_salesforce.Task.create(
            {  # type: ignore[operator]
                "Subject": "Test Task",
                "WhatId": client_crm_id,
                "ActivityDate": task_date.strftime("%Y-%m-%d"),
                "Description": "Test task description",
                "TaskSubtype": "Email",  # Not a Call
            }
        )

        start_date = datetime.now(datetime_timezone.utc) - timedelta(days=365)
        notes = fetch_call_tasks_for_client(self.simple_salesforce, client_crm_id, start_date)

        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_call_tasks_for_client_date_filter(self) -> None:
        client_crm_id = "***************"

        # Create old task (more than 30 days ago)
        old_date = (datetime.now(datetime_timezone.utc) - timedelta(days=40)).date()
        self.simple_salesforce.Task.create(
            {  # type: ignore[operator]
                "Subject": "Old Call",
                "WhatId": client_crm_id,
                "ActivityDate": old_date.isoformat(),
                "Description": "Old call description",
                "TaskSubtype": "Call",
            }
        )

        recent_date = datetime.now(datetime_timezone.utc).date()
        recent_task = self.simple_salesforce.Task.create(
            {  # type: ignore[operator]
                "Subject": "Recent Call",
                "WhatId": client_crm_id,
                "ActivityDate": recent_date.isoformat(),
                "Description": "Recent call description",
                "TaskSubtype": "Call",
            }
        )

        start_date = datetime.now(datetime_timezone.utc) - timedelta(days=30)

        original_query_all = self.simple_salesforce.query_all

        def mock_query_all(query: str) -> dict[str, Any]:
            if "FROM Task" in query and "TaskSubtype = 'Call'" in query:
                recent_task_record = self.simple_salesforce.Task.get(recent_task["id"])  # type: ignore[operator]
                if (
                    recent_task_record.get("WhatId") == client_crm_id
                    and recent_task_record.get("TaskSubtype") == "Call"
                    and recent_task_record.get("Description")
                ):
                    return {"records": [recent_task_record]}
                return {"records": []}
            else:
                return original_query_all(query)

        with patch.object(self.simple_salesforce, "query_all", side_effect=mock_query_all):
            notes = fetch_call_tasks_for_client(self.simple_salesforce, client_crm_id, start_date)

            self.assertEqual(len(notes), 1)
            self.assertEqual(notes[0].crm_id, recent_task["id"])

        with patch.object(self.simple_salesforce, "query_all", side_effect=mock_query_all):
            notes = fetch_call_tasks_for_client(self.simple_salesforce, client_crm_id, start_date)

            self.assertEqual(len(notes), 1)
            self.assertEqual(notes[0].crm_id, recent_task["id"])

    @mock_salesforce
    def test_fetch_call_tasks_for_client_salesforce_error(self) -> None:
        client_crm_id = "***************"

        def raise_error(*args, **kwargs):  # type: ignore[no-untyped-def]
            raise Exception("Salesforce API Error")

        original_query = self.simple_salesforce.query_all
        self.simple_salesforce.query_all = raise_error  # type: ignore[method-assign]

        start_date = datetime.now(datetime_timezone.utc) - timedelta(days=365)
        notes = fetch_call_tasks_for_client(self.simple_salesforce, client_crm_id, start_date)

        self.simple_salesforce.query_all = original_query  # type: ignore[method-assign]

        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_call_tasks_for_client_multiple_tasks(self) -> None:
        client_crm_id = "***************"

        task_ids = []
        task_date = datetime.now(datetime_timezone.utc).date()
        for i in range(3):
            task = self.simple_salesforce.Task.create(
                {  # type: ignore[operator]
                    "Subject": f"Test Call {i}",
                    "WhatId": client_crm_id,
                    "ActivityDate": task_date.isoformat(),
                    "Description": f"Test call description {i}",
                    "TaskSubtype": "Call",
                }
            )
            task_ids.append(task["id"])

        original_query_all = self.simple_salesforce.query_all

        def mock_query_all(query: str) -> dict[str, Any]:
            if "FROM Task" in query and "TaskSubtype = 'Call'" in query:
                records = []
                for task_id in task_ids:
                    task_record = self.simple_salesforce.Task.get(task_id)  # type: ignore[operator]
                    if (
                        task_record.get("WhatId") == client_crm_id
                        and task_record.get("TaskSubtype") == "Call"
                        and task_record.get("Description")
                    ):
                        records.append(task_record)
                return {"records": records}
            else:
                return original_query_all(query)

        with patch.object(self.simple_salesforce, "query_all", side_effect=mock_query_all):
            start_date = datetime.now(datetime_timezone.utc) - timedelta(days=365)
            notes = fetch_call_tasks_for_client(self.simple_salesforce, client_crm_id, start_date)

            self.assertEqual(len(notes), 3)
            for i, note in enumerate(notes):
                self.assertEqual(note.crm_id, task_ids[i])
                self.assertEqual(note.content, f"Call: Test Call {i}\n\nTest call description {i}")

    @mock_salesforce
    def test_fetch_call_tasks_for_client_invalid_date(self) -> None:
        client_crm_id = "***************"

        task = self.simple_salesforce.Task.create(
            {  # type: ignore[operator]
                "Subject": "Test Call",
                "WhatId": client_crm_id,
                "ActivityDate": "invalid-date",
                "Description": "Test call description",
                "TaskSubtype": "Call",
            }
        )

        original_query_all = self.simple_salesforce.query_all

        def mock_query_all(query: str) -> dict[str, Any]:
            if "FROM Task" in query and "TaskSubtype = 'Call'" in query:
                task_record = self.simple_salesforce.Task.get(task["id"])  # type: ignore[operator]
                if (
                    task_record.get("WhatId") == client_crm_id
                    and task_record.get("TaskSubtype") == "Call"
                    and task_record.get("Description")
                ):
                    return {"records": [task_record]}
                return {"records": []}
            else:
                return original_query_all(query)

        with patch.object(self.simple_salesforce, "query_all", side_effect=mock_query_all):
            start_date = datetime.now(datetime_timezone.utc) - timedelta(days=365)
            notes = fetch_call_tasks_for_client(self.simple_salesforce, client_crm_id, start_date)

            self.assertEqual(len(notes), 1)
            self.assertEqual(notes[0].crm_id, task["id"])
            self.assertEqual(notes[0].content, "Call: Test Call\n\nTest call description")
            self.assertIsNone(notes[0].created_at)
