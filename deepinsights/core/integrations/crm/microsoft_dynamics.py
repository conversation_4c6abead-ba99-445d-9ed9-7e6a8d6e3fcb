import datetime
import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, ClassVar, Mapping
from urllib.parse import urljoin

import requests

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.crm import microsoft_dynamics_utils
from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.crm_models import (
    CRMAccount,
    CRMNote,
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTarget,
    CRMSyncTargets,
    CRMUser,
    CRMWorkflow,
)
from deepinsights.core.integrations.oauth.microsoft_dynamics import MicrosoftDynamicsOAuth
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User


class MicrosoftDynamics(CrmBase):
    """Microsoft Dynamics CRM integration class."""

    # Class constants
    API_VERSION: ClassVar[str] = "v9.2"

    def __init__(self, *, user: User) -> None:
        """Initialize the Microsoft Dynamics CRM integration."""
        resource_url = user.get_crm_configuration().dynamics.dynamics_resource_url
        self.oauth = MicrosoftDynamicsOAuth(resource_url)
        self.user = user

    def get_accounts_by_owner_email_and_name(
        self, owner_email: str, account_name_filter: str | None = None
    ) -> list[CRMAccount]:
        """
        Fetch accounts and contacts from Microsoft Dynamics
        This method fetches both contacts and accounts.
        Args:
            owner_email: Email of the account owner
            account_name_filter: Optional filter for account name
        Returns:
            List of CRMAccount objects
        """
        try:
            user = User.objects.get(email=owner_email)
            api_url, access_token = microsoft_dynamics_utils.get_api_url(user, self.oauth)

            headers = microsoft_dynamics_utils.get_headers(access_token)

            entities = []

            # 1. Fetch accounts
            accounts_url = urljoin(api_url, "accounts")
            params = {"$select": "name,accountid,telephone1,emailaddress1"}

            if account_name_filter:
                params["$filter"] = f"contains(name, '{account_name_filter}')"

            response = requests.get(accounts_url, headers=headers, params=params)
            if response.status_code == 200:
                accounts_data = response.json().get("value", [])
                for account in accounts_data:
                    try:
                        entities.append(
                            CRMAccount(
                                email=account.get("emailaddress1"),
                                phone_number=account.get("telephone1", None),
                                crm_id=account.get("accountid"),
                                client_type="Account",
                                crm_system="microsoft_dynamics",
                                name=account.get("name", "Unknown Account"),
                            )
                        )
                    except Exception as e:
                        logging.error("Error creating CRMAccount object for: %s", account, exc_info=e)
            else:
                logging.error(f"Error fetching accounts: {response.status_code}, {response.text}")

            # 2. Fetch contacts
            contacts_url = urljoin(api_url, "contacts")
            params = {"$select": "fullname,firstname,lastname,contactid,telephone1,emailaddress1"}

            if account_name_filter:
                params["$filter"] = f"contains(fullname, '{account_name_filter}')"

            response = requests.get(contacts_url, headers=headers, params=params)
            if response.status_code == 200:
                contacts_data = response.json().get("value", [])
                for contact in contacts_data:
                    try:
                        entities.append(
                            CRMAccount(
                                first_name=contact.get("firstname", None),
                                last_name=contact.get("lastname", None),
                                email=contact.get("emailaddress1"),
                                phone_number=contact.get("telephone1"),
                                crm_id=contact.get("contactid"),
                                client_type="Contact",
                                crm_system="microsoft_dynamics",
                                name=contact.get("fullname", "Unknown Account"),
                            )
                        )
                    except Exception as e:
                        logging.error("Error creating CRMAccount object for: %s", contact, exc_info=e)
            else:
                logging.error("Error fetching contacts: %s, %s", response.status_code, response.text)

            # 3. Fetch leads
            leads_url = urljoin(api_url, "leads")
            params = {"$select": "fullname,firstname,lastname,leadid,telephone1,emailaddress1,subject,companyname"}

            if account_name_filter:
                params[
                    "$filter"
                ] = f"contains(fullname, '{account_name_filter}') or contains(companyname, '{account_name_filter}')"

            response = requests.get(leads_url, headers=headers, params=params)
            if response.status_code == 200:
                leads_data = response.json().get("value", [])
                for lead in leads_data:
                    try:
                        lead_name = lead.get("fullname", "")

                        entities.append(
                            CRMAccount(
                                first_name=lead.get("firstname", None),
                                last_name=lead.get("lastname", None),
                                email=lead.get("emailaddress1"),
                                phone_number=lead.get("telephone1"),
                                crm_id=lead.get("leadid"),
                                client_type="Lead",
                                crm_system="microsoft_dynamics",
                                name=lead_name or lead.get("subject", "Unknown Lead"),
                            )
                        )
                    except Exception as e:
                        logging.error("Error creating CRMAccount object for lead: %s", lead, exc_info=e)
            else:
                logging.error("Error fetching leads: %s, %s", response.status_code, response.text)

            return entities

        except Exception as e:
            logging.error("Error getting Microsoft Dynamics entities: %s", e, exc_info=True)
            return []

    def resolve_sync_targets(
        self, note: Note, user: User, previous_sync_targets: CRMSyncTargets | None = None
    ) -> CRMSyncTargets:
        if note.client and (client_uuid := note.client.get("uuid")):
            try:
                client = Client.objects.get(uuid=client_uuid)
                if client.crm_id:
                    return CRMSyncTargets(
                        status=CRMSyncTargets.Status.FINAL,
                        note_targets=[CRMSyncTarget(crm_id=client.crm_id, type=client.client_type, name=client.name)],
                    )
            except Client.DoesNotExist:
                pass
        return CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])

    def add_interaction_with_client(
        self,
        note: Note,
        sync_targets: CRMSyncTargets,
        sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = None,
    ) -> None:
        try:
            if not note.note_owner:
                raise Exception("Note owner not found")

            api_url, access_token = microsoft_dynamics_utils.get_api_url(note.note_owner, self.oauth)

            # If we already have an interaction ID, log and return
            if note.metadata and note.metadata.get("interactionId"):
                logging.info("Note %s already has an interaction ID: %s", note.uuid, note.metadata["interactionId"])
                return

            if not sync_targets.note_targets:
                raise Exception("No primary client specified in the note")
            if len(sync_targets.note_targets) > 1:
                logging.warning(
                    "Multiple clients provided for note %s, using the first one",
                    note.uuid,
                )

            primary_sync_target = sync_targets.note_targets[0]
            client_info = {"id": primary_sync_target.crm_id, "type": primary_sync_target.type or "contact"}

            # Prepare headers
            headers = microsoft_dynamics_utils.get_headers(access_token)

            # Create a note activity
            note_data = {
                "subject": note.title(),
                "notetext": note.get_summary_for_crm(use_html_formatting=False, sync_items=sync_items),
                "objecttypecode": client_info["type"],
                f"objectid_{client_info['type']}@odata.bind": f"/{client_info['type']}s({client_info['id']})",
            }

            # Create annotation (note) in Dynamics
            notes_url = urljoin(api_url, "annotations")
            response = requests.post(notes_url, headers=headers, json=note_data)

            if response.status_code in (200, 201, 204):
                # Extract annotation ID from response headers
                annotation_id = None
                if "OData-EntityId" in response.headers:
                    # Header format: "OData-EntityId: [organization uri]/api/data/v9.2/annotations(<guid>)"
                    entity_id = response.headers["OData-EntityId"]
                    annotation_id = entity_id.split("(")[1].split(")")[0]

                if annotation_id:
                    if not note.metadata:
                        note.metadata = {}
                    note.metadata["interactionId"] = annotation_id
                    note.save()
                    logging.info("Successfully created note in Dynamics with ID %s", annotation_id)
                else:
                    logging.warning("Note created but couldn't extract ID from response headers")
            else:
                logging.error("Error creating note in Dynamics: %s, %s", response.status_code, response.text)

            # Create tasks
            if note.should_include_section(sync_items, CRMSyncSection.TASKS):
                for task in note.get_tasks_to_include_crm_sync(sync_items=sync_items):
                    self._create_task(api_url, headers, task, client_info)

        except Exception as e:
            logging.error("Error adding interaction with client for note %s: %s", note.uuid, e, exc_info=True)
            raise

    def _create_task(self, api_url: str, headers: dict[str, str], task: Task, client: dict[str, Any]) -> None:
        """
        Helper method to create a task in Dynamics.

        Args:
            api_url: The API URL
            headers: Request headers
            task: The task to create
            client: Client dictionary with id and type
        """
        try:
            due_date = task.due_date or datetime.datetime.now(datetime.timezone.utc)
            subject, description = microsoft_dynamics_utils.prepare_task_subject_and_description(
                task.task_title, task.task_desc
            )
            # Prepare task data with regarding relationship included
            task_data = {
                "subject": subject,
                "description": description,
                "scheduledend": due_date.isoformat(),
                "statecode": 1 if task.completed else 0,  # 0 = Not Started, 1 = Completed
                "prioritycode": 1,  # Normal priority
                f"regardingobjectid_{client['type']}@odata.bind": f"/{client['type']}s({client['id']})",
            }

            # Assign the task if an assignee is specified
            if task.assignee:
                # Would need to fetch the corresponding systemuser ID from Dynamics
                # For now, we'll skip this part
                pass

            tasks_url = urljoin(api_url, "tasks")
            response = requests.post(tasks_url, headers=headers, json=task_data)

            if response.status_code in (200, 201, 204):
                # For Dynamics, successful creation returns 204 with no content
                # We need to get the task ID from the response headers
                task_id = response.headers.get("OData-EntityId")
                if task_id:
                    # Extract just the GUID from the full URL
                    task_id = task_id.split("(")[1].split(")")[0]
                    task.metadata["taskId"] = task_id  # type: ignore[index]
                    task.save()
                    logging.info("Successfully created task in Dynamics with ID %s", task_id)
                else:
                    logging.warning("Task created but couldn't extract ID from response headers")
            else:
                logging.error("Error creating task in Dynamics: %s, %s", response.status_code, response.text)

        except Exception as e:
            logging.error("Error creating task in Dynamics: %s", e, exc_info=True)
            raise

    def fetch_notes_for_client(self, user: User, client: Client, lookback_interval: timedelta) -> list[CRMNote]:
        """
        Fetch notes for a client from Microsoft Dynamics.

        Args:
            user: The user requesting the notes
            client: The client to fetch notes for
            lookback_interval: How far back to look for notes

        Returns:
            List of CRMNote objects
        """
        try:
            if not client.crm_id:
                logging.error(f"Client {client.uuid} does not have a CRM ID")
                return []

            api_url, access_token = microsoft_dynamics_utils.get_api_url(user, self.oauth)

            client_type = client.client_type.lower() if client.client_type else "account"
            not_before_date = datetime.datetime.now(datetime.timezone.utc) - lookback_interval
            formatted_date = not_before_date.strftime("%Y-%m-%dT%H:%M:%SZ")
            headers = microsoft_dynamics_utils.get_headers(access_token)

            annotations_url = urljoin(api_url, "annotations")

            # Build the filter - Using Dynamics OData syntax
            # The objectid_<type>@odata.bind format is based on how we create notes in add_interaction_with_client
            filter_query = f"_objectid_value eq '{client.crm_id}' and createdon ge {formatted_date}"

            params = {
                "$filter": filter_query,
                "$orderby": "createdon desc",
                "$select": "annotationid,subject,notetext,createdon",
            }

            response = requests.get(annotations_url, headers=headers, params=params)

            if response.status_code != 200:
                logging.error("Error fetching notes from Dynamics: %s, %s", response.status_code, response.text)
                return []

            notes_data = response.json().get("value", [])
            logging.info("Successfully fetched %d notes for client %s from Dynamics", len(notes_data), client.uuid)

            crm_notes = []
            for note in notes_data:
                try:
                    note_id = note.get("annotationid")

                    dynamics_url = user.get_crm_configuration().dynamics.dynamics_resource_url
                    if dynamics_url.endswith("/"):
                        dynamics_url = dynamics_url[:-1]
                    web_link = f"{dynamics_url}/main.aspx?pagetype=entityrecord&etn=annotation&id={note_id}"

                    created_at = None
                    if created_date_str := note.get("createdon"):
                        try:
                            created_at = datetime.datetime.fromisoformat(created_date_str.replace("Z", "+00:00"))
                        except Exception as e:
                            logging.error(f"Error parsing date for note {note_id}: {e}")

                    note_subject = note.get("subject", "")
                    note_text = note.get("notetext", "")
                    content = f"{note_subject}\n{note_text}" if note_subject else note_text

                    crm_notes.append(
                        CRMNote(
                            crm_id=note_id,
                            crm_system="microsoft_dynamics",
                            content=content,
                            created_at=created_at,
                            web_link=web_link,
                        )
                    )
                except Exception as e:
                    logging.error(f"Error creating CRMNote object for note {note.get('annotationid')}: {e}")
                    continue

            return crm_notes

        except Exception as e:
            logging.error(f"Error fetching notes from Dynamics for client {client.uuid}: {e}", exc_info=True)
            return []

    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        logging.warning("fetch_events not implemented for MicrosoftDynamics")
        return {}

    def fetch_events(self, user: User, interval: timedelta) -> list[CalendarEvent]:
        logging.warning("fetch_events not implemented for MicrosoftDynamics")
        return []

    def fetch_workflows_for_user(self, user: User) -> list[CRMWorkflow]:
        logging.warning("fetch_workflows_for_user not implemented for MicrosoftDynamics")
        return []

    def list_users(self, requesting_user: User) -> list[CRMUser]:
        if not requesting_user.organization:
            logging.error("User does not belong to an organization. Cannot list Microsoft Dynamics CRM users.")
            return []

        return microsoft_dynamics_utils.list_crm_users(self.oauth, requesting_user)
