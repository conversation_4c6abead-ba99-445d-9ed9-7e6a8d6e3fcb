import logging
from datetime import datetime, timedelta, timezone
from typing import Any, Mapping

from simple_salesforce.api import Salesforce
from simple_salesforce.format import format_soql

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.crm import salesforce_utils
from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.crm_models import (
    CRMAccount,
    CRMNote,
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTarget,
    CRMSyncTargets,
    CRMUser,
    CRMWorkflow,
)
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User


class SalesforceEventsandActivities(CrmBase):
    def __init__(
        self,
        *,
        user: User | None = None,
        username: str | None = None,
        password: str | None = None,
        consumer_key: str | None = None,
        consumer_secret: str | None = None,
        security_token: str = "",
        instance_url: str | None = None,
        sf: Salesforce | None = None,
    ) -> None:
        super().__init__()
        self.sf = salesforce_utils.simple_salesforce_intstance_with_credentials(
            user=user,
            username=username,
            password=password,
            consumer_key=consumer_key,
            consumer_secret=consumer_secret,
            security_token=security_token,
            instance_url=instance_url,
            sf=sf,
        )

    def get_accounts_by_owner_email_and_name(
        self, owner_email: str, account_name_filter: str | None = None
    ) -> list[CRMAccount]:
        try:
            person_account_query = format_soql(
                "SELECT Id FROM RecordType WHERE SObjectType = 'Account' AND Name = 'Person Account'"
            )
            person_account_result = self.sf.query(person_account_query)
            person_account_records = person_account_result.get("records", [])
            if not person_account_records:
                logging.error("Person Account record type not found in Salesforce")
                raise Exception("Person Account record type not found in Salesforce")
            person_account_record_type_id = person_account_records[0].get("Id")
            logging.info("Found Person Account record type ID: %s", person_account_record_type_id)

            filter_condition = format_soql(
                "RecordTypeId != {} AND Sales_Game_Stage_SubType__c = {}", person_account_record_type_id, "Active"
            )

            return salesforce_utils.get_salesforce_entity_by_owner_email_and_name(
                self.sf,
                "Account",
                owner_email,
                account_name_filter,
                filter_condition,
            )
        except Exception:
            logging.error("Error fetching accounts for owner %s", owner_email, exc_info=True)
            return []

    def resolve_sync_targets(
        self, note: Note, user: User, selected_sync_targets: CRMSyncTargets | None = None
    ) -> CRMSyncTargets:
        if selected_sync_targets:
            if (
                selected_sync_targets.note_targets
                and len(selected_sync_targets.note_targets) == 1
                and (selected_sync_targets.note_targets[0].type == "event")
            ):
                return CRMSyncTargets(
                    status=CRMSyncTargets.Status.FINAL,
                    note_targets=selected_sync_targets.note_targets,
                )

            logging.warning(
                "Selected sync targets are not valid for note %s: %s. Getting new sync targets.",
                note.uuid,
                selected_sync_targets.note_targets,
            )
        if not note.client or not (client_uuid := note.client.get("uuid")):
            return CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])

        try:
            client = Client.objects.get(uuid=client_uuid)
        except Client.DoesNotExist:
            return CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])
        if not client.crm_id:
            return CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])

        client_target = CRMSyncTarget(
            crm_id=client.crm_id,
            type="account",
            name=client.name or "Unknown Client",
        )

        events: list[CRMSyncTarget] = []
        try:
            query = f"""
                SELECT Id, Subject, ActivityDateTime
                FROM Event
                WHERE WhatId = '{client.crm_id}' or WhoId = '{client.crm_id}'
                ORDER BY ActivityDateTime DESC
                LIMIT 10
            """
            result = self.sf.query(query)
            for record in result.get("records", []):
                subject = record.get("Subject", "<No Subject>")
                try:
                    activity_date = salesforce_utils.datetime_from_salesforce_time(
                        record.get("ActivityDateTime")
                    ).date()
                except Exception:
                    activity_date = None
                name = f"{subject} ({activity_date.isoformat()})" if activity_date else subject
                events.append(
                    CRMSyncTarget(
                        crm_id=record.get("Id"),
                        type="event",
                        name=name,
                        parent=client_target,
                    )
                )
        except Exception:
            logging.error("Failed to retrieve Salesforce events for client %s", client.crm_id, exc_info=True)

        events.append(
            CRMSyncTarget(
                crm_id="new_event",
                type="event",
                name="New event",
                parent=client_target,
            )
        )

        return CRMSyncTargets(
            status=CRMSyncTargets.Status.RESOLUTION_REQUIRED,
            note_targets=events,
        )

    def add_interaction_with_client(
        self,
        note: Note,
        sync_targets: CRMSyncTargets,
        sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = None,
    ) -> None:
        try:
            if not note.note_owner:
                raise Exception("Note owner is required for syncing.")

            if note.metadata and note.metadata.get("interactionId"):
                logging.info("Note %s already has an interaction ID: %s", note.uuid, note.metadata["interactionId"])
                return

            if not sync_targets or sync_targets.status != CRMSyncTargets.Status.FINAL:
                raise Exception("Sync targets must be finalized before syncing interaction.")

            if len(sync_targets.note_targets) != 1:
                raise Exception("Exactly one sync target must be selected for syncing.")

            sync_target = sync_targets.note_targets[0]
            parent_target = sync_target.parent
            if not parent_target or not parent_target.crm_id:
                raise Exception("Sync target must have a parent (Account) with CRM ID.")

            if scheduled := note.scheduled_event:
                start = scheduled.start_time
                end = scheduled.end_time
            else:
                start = (note.metadata or {}).get("scheduled_at", note.created)
                end = start + timedelta(hours=1)

            description = note.get_summary_for_crm(use_html_formatting=False, sync_items=sync_items)

            if sync_target.crm_id == "new_event":
                event_data = {
                    "Subject": note.title(),
                    "Description": description,
                    "StartDateTime": salesforce_utils.salesforceTime(start),
                    "EndDateTime": salesforce_utils.salesforceTime(end),
                    "WhatId": parent_target.crm_id,
                    "IsAllDayEvent": False,
                    "ActivityDateTime": salesforce_utils.salesforceTime(start),
                }
                result = self.sf.Event.create(event_data)  # type: ignore[operator]
                if not result.get("success"):
                    raise Exception("Failed to create new Salesforce event.")
                event_id = result.get("id")
                if not note.metadata:
                    note.metadata = {}
                note.metadata["interactionId"] = event_id
                note.save()
                logging.info("Created new Salesforce event for note %s with ID %s", note.uuid, event_id)
            else:
                # Append note content to existing event's description
                existing_event = self.sf.Event.get(sync_target.crm_id)  # type: ignore[operator]
                updated_description = (existing_event.get("Description") or "") + "\n\n" + description
                status = self.sf.Event.update(sync_target.crm_id, {"Description": updated_description})  # type: ignore[operator]
                if not (status >= 200 and status < 300):
                    raise Exception("Failed to update existing Salesforce event.")
                logging.info("Updated existing Salesforce event %s with note %s", sync_target.crm_id, note.uuid)

                if not note.metadata:
                    note.metadata = {}
                note.metadata["interactionId"] = sync_target.crm_id
                note.save()

            # Handle tasks - consolidate into a single follow-up task
            if note.should_include_section(sync_items, CRMSyncSection.TASKS):
                logging.info("Creating consolidated follow-up task for note %s", note.uuid)
                tasks_to_consolidate = []
                for task in note.get_tasks_to_include_crm_sync(sync_items=sync_items):
                    tasks_to_consolidate.append(task)

                if tasks_to_consolidate:
                    self._create_consolidated_follow_up_task(note, tasks_to_consolidate, parent_target.crm_id)

        except Exception:
            logging.error("Failed to sync note %s to Salesforce (events and activites)", note.uuid, exc_info=True)
            raise

    def _create_consolidated_follow_up_task(self, note: Note, tasks: list[Task], client_crm_id: str) -> None:
        try:
            # Create consolidated request name
            meeting_name = note.title()
            consolidated_subject = f"{meeting_name} - Follow Up Tasks"

            # Build consolidated description with all tasks
            task_descriptions = []
            earliest_due_date = None

            for i, task in enumerate(tasks, 1):
                task_desc = f"{i}. {task.task_title}"
                if task.task_desc:
                    task_desc += f": {task.task_desc}"
                if task.assignee:
                    task_desc += f" (Assigned to: {task.assignee.email})"
                if task.due_date:
                    task_desc += f" (Due: {task.due_date.strftime('%Y-%m-%d')})"
                    if not earliest_due_date or task.due_date < earliest_due_date:
                        earliest_due_date = task.due_date

                task_descriptions.append(task_desc)

            consolidated_description = f"Follow-up tasks from meeting: {meeting_name}\n\n" + "\n".join(
                task_descriptions
            )

            # Use earliest due date or default to 7 days from now
            due_date = earliest_due_date or (datetime.now(timezone.utc) + timedelta(days=7))

            # Try to fetch the record ID for the relationship manager. The service team references
            # contacts, but we need to set a user as the owner of the delegation request, so we need to traverse
            # some Salesforce relationships to find the right user.
            try:
                relationship_manager_email_query = format_soql(
                    "SELECT Team_Member__r.Email FROM Oxford_Team__c WHERE Household_Firm__r.Id = {} AND Match_CSM__c=true",
                    client_crm_id,
                )
                relationship_manager_email_result = self.sf.query(relationship_manager_email_query)
                relationship_manager_email = (
                    relationship_manager_email_result.get("records", [{}])[0].get("Team_Member__r").get("Email")
                )
                if not relationship_manager_email:
                    raise Exception("Relationship manager email not found")
                relationship_manager_id = salesforce_utils.get_user_id_by_email(self.sf, relationship_manager_email)
            except Exception:
                logging.error(
                    "Error fetching relationship manager for client %s. Not setting an explicit owner",
                    client_crm_id,
                    exc_info=True,
                )
                relationship_manager_id = None

            # Prepare consolidated delegation request data for Salesforce
            # Using standard Case fields only
            try:
                deleagation_record_type_query = format_soql(
                    "SELECT Id from RecordType Where Name = 'Delegation Request'"
                )
                delegation_record_type_result = self.sf.query(deleagation_record_type_query)
                delgation_record_type_id = delegation_record_type_result.get("records", [{}])[0].get("Id")
            except Exception:
                logging.error(
                    "Error fetching Delegation Request record type in Salesforce. Not setting a record type.",
                    exc_info=True,
                )
                delgation_record_type_id = None

            delegation_request_data = {
                "Subject": consolidated_subject,  # Standard Subject field
                "Due_Date__c": due_date.date().isoformat(),
                "Status": "Requested",  # Default status from the form
                "Description": consolidated_description,
                "Household_Firm__c": client_crm_id,  # Link to the Account (Household Firm)
                "Priority": "Medium",  # Standard Priority field
                "Origin": "Internal",  # Standard Origin field
            }
            if delgation_record_type_id:
                delegation_request_data["RecordTypeId"] = delgation_record_type_id
            if relationship_manager_id:
                delegation_request_data["OwnerId"] = relationship_manager_id

            # Create the consolidated delegation request Case
            result = self.sf.Case.create(delegation_request_data)  # type: ignore[operator]

            if not result.get("success"):
                logging.error("Error creating delegation request in Salesforce: %s", result)
                raise Exception("Failed to create delegation request in Salesforce")
            else:
                delegation_request_id = result.get("id")

                # Update all original tasks with the delegation request ID
                for task in tasks:
                    if not task.metadata:
                        task.metadata = {}
                    task.metadata["delegationRequestCaseId"] = delegation_request_id
                    task.save()

                logging.info(
                    "Successfully created consolidated delegation request in Salesforce with ID %s",
                    delegation_request_id,
                )
                logging.info("Consolidated %d individual tasks into single delegation request", len(tasks))

        except Exception:
            logging.error(
                "Error creating consolidated delegation request from tasks for Salesforce (events and activities)",
                exc_info=True,
            )
            raise

    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        # TODO: Implement client info retrieval
        logging.warning("get_client_basic_info not yet implemented for SalesforceEventsandActivities")
        return None

    def fetch_events(self, user: User, interval: timedelta) -> list[CalendarEvent]:
        # TODO: Implement event fetching
        logging.warning("fetch_events not yet implemented for SalesforceEventsandActivities")
        return []

    def fetch_notes_for_client(self, user: User, client: Client, lookback_interval: timedelta) -> list[CRMNote]:
        try:
            if not client.crm_id:
                logging.error("Client %s does not have a CRM ID", client.uuid)
                return []

            not_before_date = datetime.now(timezone.utc) - lookback_interval
            all_notes: list[CRMNote] = []
            try:
                all_notes.extend(
                    self._fetch_activities_for_client(
                        client_crm_id=client.crm_id,
                        formatted_date=salesforce_utils.salesforceTime(not_before_date),
                        soql_object="Event",
                        label="Event",
                        date_field="CreatedDate",
                    )
                )
            except Exception:
                logging.error("Error fetching activities for client %s", client.uuid, exc_info=True)

            all_notes.sort(key=lambda x: x.created_at or datetime.min.replace(tzinfo=timezone.utc), reverse=True)

            logging.info(
                "Successfully fetched and processed %d total notes for client %s from Salesforce",
                len(all_notes),
                client.uuid,
            )
            return all_notes

        except Exception:
            logging.error(
                "Error fetching notes from Salesforce (events and activities) for client: %s",
                client.uuid,
                exc_info=True,
            )
            return []

    def _fetch_activities_for_client(
        self,
        client_crm_id: str,
        formatted_date: str,
        soql_object: str,
        label: str,
        date_field: str,
    ) -> list[CRMNote]:
        try:
            if soql_object == "Event":
                query = format_soql(
                    (
                        f"SELECT Id, Subject, Description, {date_field}, ActivityDateTime, EndDateTime "
                        f"FROM {soql_object} "
                        "WHERE (WhatId = {} OR WhoId = {}) "
                        f"AND {date_field} >= {{:literal}} "
                        f"ORDER BY {date_field} DESC "
                        "LIMIT 100"
                    ),
                    client_crm_id,
                    client_crm_id,
                    formatted_date,
                )
            else:
                logging.warning("Unsupported SOQL object type: %s", soql_object)
                return []

            result = self.sf.query(query)
            activities_data = result.get("records", [])

            logging.info("Found %d %s records for client %s", len(activities_data), soql_object, client_crm_id)

            notes = []

            instance_url = self.sf.sf_instance
            if not instance_url.startswith("https://"):
                instance_url = f"https://{instance_url}"

            for activity in activities_data:
                try:
                    activity_id = activity.get("Id")

                    web_link = f"{instance_url}/{activity_id}"

                    created_at = None
                    if created_date_str := activity.get(date_field):
                        try:
                            created_at = salesforce_utils.datetime_from_salesforce_time(created_date_str)
                        except Exception as e:
                            logging.error("Error parsing date for %s %s: %s", soql_object, activity_id, e)

                    subject = activity.get("Subject", "")

                    activity_datetime = activity.get("ActivityDateTime", "")
                    content = f"[{label}] {subject}"
                    if activity_datetime:
                        try:
                            event_date = salesforce_utils.datetime_from_salesforce_time(activity_datetime)
                            content += f" ({event_date.strftime('%Y-%m-%d %H:%M')})"
                        except Exception:
                            logging.error("Error parsing ActivityDateTime for %s %s", soql_object, activity_id)
                            pass
                    if description := activity.get("Description"):
                        content += f"\n{description}"

                    notes.append(
                        CRMNote(
                            crm_id=activity_id,
                            crm_system="salesforce",
                            content=content,
                            created_at=created_at,
                            web_link=web_link,
                        )
                    )

                except Exception:
                    logging.error(
                        "Error creating CRMNote object for %s %s", soql_object, activity.get("Id"), exc_info=True
                    )
                    continue

            return notes

        except Exception:
            logging.error("Error fetching %s activities", soql_object, exc_info=True)
            return []

    def fetch_workflows_for_user(self, user: User) -> list[CRMWorkflow]:
        logging.warning("fetch_workflows_for_user not yet implemented for SalesforceEventsandActivities")
        return []

    def list_users(self, requesting_user: User) -> list[CRMUser]:
        if not requesting_user.organization:
            logging.error("User does not belong to an organization. Cannot list Salesforce CRM users.")
            return []

        return salesforce_utils.list_crm_users(self.sf, requesting_user.organization)
