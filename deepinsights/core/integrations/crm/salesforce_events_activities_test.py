import logging
from datetime import datetime, timedelta
from datetime import timezone as datetime_timezone
from typing import Tuple
from unittest.mock import MagicMock, patch
from uuid import uuid4

from django.core import cache
from django.test import TestCase, override_settings
from django.utils import timezone
from simple_mockforce import mock_salesforce
from simple_salesforce.api import Salesforce

from deepinsights.core.integrations.crm import salesforce_utils
from deepinsights.core.integrations.crm.crm_models import (
    CRMAccount,
    CRMNote,
    CRMSyncTarget,
    CRMSyncTargets,
    CRMUser,
    ZeplynOrganization,
    ZeplynUser,
)
from deepinsights.core.integrations.crm.salesforce_events_activities import SalesforceEventsandActivities
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.flags import Flag
from deepinsights.users.models.user import User


class SalesforceEventsActivitiesTestCase(TestCase):
    def setUp(self) -> None:
        cache.cache.clear()
        self.simple_salesforce = Salesforce(instance="test.salesforce.com", session_id="")
        self.salesforce = SalesforceEventsandActivities(sf=self.simple_salesforce)
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, False)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, False)

    def setFlagActive(self, flag: Flags, is_active: bool) -> None:
        f = Flag.objects.get(name=flag.name)
        f.everyone = is_active
        f.override_enabled_by_environment = None
        f.save()

    def populateSalesforceTypes(self) -> None:
        for type in [
            self.simple_salesforce.Interaction,
            self.simple_salesforce.InteractionSummary,
            self.simple_salesforce.Task,
            self.simple_salesforce.User,
            self.simple_salesforce.Account,
            # Add Event type for the event-related tests
            self.simple_salesforce.Event,
            self.simple_salesforce.Case,  # Add Case for delegation requests
            self.simple_salesforce.RecordType,  # Add RecordType for dynamic lookups
        ]:
            id = type.create({})["id"]  # type: ignore[operator]
            type.delete(id)  # type: ignore[operator]

    def newAccountAndClient(
        self, name: str | None, owner_email: str, org: Organization, *, phone: str | None = None
    ) -> Tuple[str, Client]:
        account_name = name.replace("'", "\\'") if name else f"Test Account {uuid4()}"
        account_data = {
            "Name": account_name,
            "Owner.Email": owner_email.replace("'", "\\'"),
        }
        if phone:
            account_data["Phone"] = phone
        account = self.simple_salesforce.Account.create(account_data)  # type: ignore[operator]
        self.assertFalse(account["errors"])
        client = Client.objects.create(crm_id=account["id"], name=account_name, organization=org)
        return account["id"], client

    def newUser(self, org: Organization | None = None) -> Tuple[str, str]:
        email = f"{uuid4()}@example.com"
        user = self.simple_salesforce.User.create({"Email": email})  # type: ignore[operator]
        zeplyn_user = User.objects.create(username=email, email=email, organization=org)
        zeplyn_user.crm_configuration["crm_system"] = "salesforce"
        zeplyn_user.save()
        return (user["id"], email)

    @override_settings(SALESFORCE_USER="user", SALESFORCE_PASSWORD="password", SALESFORCE_INSTANCE="instance")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_from_settings(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        SalesforceEventsandActivities(user=user)
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="", instance_url="instance"
        )

    @mock_salesforce
    def test_get_salesforce_entity_by_owner_email_and_name_missing_record_id(self) -> None:
        self.populateSalesforceTypes()

        assert not self.salesforce.get_accounts_by_owner_email_and_name("<EMAIL>")
        self.assertLogs("Person Account record type not found in Salesforce", level=logging.ERROR)

    @mock_salesforce
    def test_get_accounts_by_owner_email_and_name(self) -> None:
        self.populateSalesforceTypes()

        owner_user = self.simple_salesforce.User.create(
            {  # type: ignore[operator]
                "Email": "<EMAIL>",
                "LastName": "Owner",
            }
        )
        other_user = self.simple_salesforce.User.create(
            {  # type: ignore[operator]
                "Email": "<EMAIL>",
                "LastName": "Other",
            }
        )

        person_account_record = self.simple_salesforce.RecordType.create(  # type: ignore[operator]
            {
                "Name": "Person Account",
                "SObjectType": "Account",
            }
        )

        account1 = self.simple_salesforce.Account.create(
            {  # type: ignore[operator]
                "Name": "Test Account 1",
                "OwnerId": owner_user["id"],
                "Owner.Email": "<EMAIL>",  # Explicit owner email
                "Phone": "**********",
                "RecordTypeId": "012Dn000000mQd4IAE",  # Allowed record type
                "Sales_Game_Stage_SubType__c": "Active",
            }
        )
        account2 = self.simple_salesforce.Account.create(
            {  # type: ignore[operator]
                "Name": "Test Account 2",
                "OwnerId": owner_user["id"],
                "Owner.Email": "<EMAIL>",
                "Phone": "**********",
                "RecordTypeId": person_account_record["id"],
                "Sales_Game_Stage_SubType__c": "Active",
            }
        )
        account3 = self.simple_salesforce.Account.create(
            {  # type: ignore[operator]
                "Name": "Test Account 3",
                "OwnerId": other_user["id"],
                "Owner.Email": "<EMAIL>",  # Different owner
                "RecordTypeId": "012Dn000000mQd4IAE",
                "Sales_Game_Stage_SubType__c": "Active",
            }
        )
        account4 = self.simple_salesforce.Account.create(
            {  # type: ignore[operator]
                "Name": "Test Account 4",
                "OwnerId": owner_user["id"],
                "Owner.Email": "<EMAIL>",
                "Phone": "**********",
                "RecordTypeId": "012Dn000000mQd4IAE",
                "Sales_Game_Stage_SubType__c": "Inactive",  # Inactive status
            }
        )

        # Test filtering by owner email
        # Should return only account1 because:
        # - account2 has Person Account record type (excluded)
        # - account3 has different owner
        # - account4 has Inactive status
        accounts = self.salesforce.get_accounts_by_owner_email_and_name("<EMAIL>")
        self.assertEqual(
            accounts,
            [
                CRMAccount(
                    crm_id=account1["id"],
                    name="Test Account 1",
                    phone_number="**********",
                    client_type="account",
                    crm_system="salesforce",
                    is_owned_by_user=True,
                ),
                CRMAccount(
                    crm_id=account3["id"],
                    name="Test Account 3",
                    client_type="account",
                    crm_system="salesforce",
                    is_owned_by_user=False,
                ),
            ],
        )

    @mock_salesforce
    def test_resolve_sync_targets_with_client_crm_id(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        event = self.simple_salesforce.Event.create(  # type: ignore[operator]
            {
                "Subject": "Test Meeting",
                "WhatId": account_id,
                "ActivityDateTime": datetime.now(datetime_timezone.utc).isoformat(),
            }
        )

        note = Note(
            note_owner=user,
            client={"uuid": str(client.uuid)},
        )

        sync_targets = self.salesforce.resolve_sync_targets(note, user)
        # Should return RESOLUTION_REQUIRED status with events as options
        self.assertEqual(sync_targets.status, CRMSyncTargets.Status.RESOLUTION_REQUIRED)
        # Should have at least 2 targets: the existing event and "New event" option
        self.assertGreaterEqual(len(sync_targets.note_targets), 2)
        # Check that we have a "New event" option
        new_event_targets = [t for t in sync_targets.note_targets if t.name == "New event"]
        self.assertEqual(len(new_event_targets), 1)

    @mock_salesforce
    def test_resolve_sync_targets_no_client(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)

        note = Note(note_owner=user, client=None)
        sync_targets = self.salesforce.resolve_sync_targets(note, user)

        self.assertEqual(sync_targets.status, CRMSyncTargets.Status.CLIENTS_REQUIRED)
        self.assertEqual(len(sync_targets.note_targets), 0)

    @mock_salesforce
    def test_add_interaction_with_client_creates_interaction(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        note = Note(note_owner=user, client={"uuid": str(client.uuid)}, created=timezone.now(), metadata={})
        note.save()

        client_target = CRMSyncTarget(crm_id=account_id, type="account", name="Test Client")
        event_target = CRMSyncTarget(crm_id="new_event", type="event", name="New event", parent=client_target)

        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[event_target],
        )

        self.salesforce.add_interaction_with_client(note, sync_targets)

        events = self.simple_salesforce.query_all("SELECT Id FROM Event")["records"]
        self.assertEqual(len(events), 1)

        note.refresh_from_db()
        if isinstance(note.metadata, dict):
            self.assertIsNotNone(note.metadata.get("interactionId"))

    @mock_salesforce
    def test_add_interaction_with_client_creates_tasks(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        note = Note(note_owner=user, client={"uuid": str(client.uuid)}, created=timezone.now(), metadata={})
        note.save()

        task1 = Task.objects.create(task_title="Task 1", task_desc="Description 1", note=note, assignee=user)
        task2 = Task.objects.create(task_title="Task 2", note=note)

        client_target = CRMSyncTarget(crm_id=account_id, type="account", name="Test Client")
        event_target = CRMSyncTarget(crm_id="new_event", type="event", name="New event", parent=client_target)

        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[event_target],
        )

        from deepinsights.core.integrations.crm.crm_models import CRMSyncItemSelection, CRMSyncSection

        sync_items = {
            CRMSyncSection.TASKS: CRMSyncItemSelection(
                include_section=True,
                included_items=None,  # None means include all items
            )
        }

        self.salesforce.add_interaction_with_client(note, sync_targets, sync_items)

        events = self.simple_salesforce.query_all("SELECT Id FROM Event")["records"]
        self.assertEqual(len(events), 1)

        cases = self.simple_salesforce.query_all("SELECT Id, Subject FROM Case")["records"]
        self.assertEqual(len(cases), 1)
        self.assertIn("Follow Up Tasks", cases[0]["Subject"])

    @mock_salesforce
    def test_add_interaction_with_client_no_sync_targets(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)

        note = Note(note_owner=user, metadata={})
        sync_targets = CRMSyncTargets(status=CRMSyncTargets.Status.FINAL, note_targets=[])

        with self.assertRaisesRegex(Exception, "Exactly one sync target must be selected for syncing."):
            self.salesforce.add_interaction_with_client(note, sync_targets)

    @mock_salesforce
    def test_add_interaction_with_client_multiple_sync_targets(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id1, client1 = self.newAccountAndClient("Client 1", owner_email=user_email, org=org)
        account_id2, client2 = self.newAccountAndClient("Client 2", owner_email=user_email, org=org)

        note = Note(note_owner=user, client={"uuid": str(client1.uuid)}, created=timezone.now(), metadata={})
        note.save()

        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(crm_id=account_id1, type="account", name="Client 1"),
                CRMSyncTarget(crm_id=account_id2, type="account", name="Client 2"),
            ],
        )

        with self.assertRaisesRegex(Exception, "Exactly one sync target must be selected for syncing."):
            self.salesforce.add_interaction_with_client(note, sync_targets)

    @mock_salesforce
    def test_fetch_notes_for_client_success(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        current_time = datetime(2025, 7, 21, 18, 0, tzinfo=datetime_timezone.utc)
        event = self.simple_salesforce.Event.create(
            {  # type: ignore[operator]
                "Subject": "Test Event",
                "WhatId": account_id,
                "ActivityDateTime": salesforce_utils.salesforceTime(current_time),
                "Description": "Test event description",
                "EndDateTime": salesforce_utils.salesforceTime(current_time + timedelta(hours=1)),
            }
        )
        # Update the created date to be a timezone-aware date. There's a bug in simple-mockforce
        # which makes "system" dates (like `CreatedDate`) not timezone-aware.
        self.simple_salesforce.Event.update(  # type: ignore[operator]
            event["id"], {"CreatedDate": salesforce_utils.salesforceTime(current_time)}
        )

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=30))

        self.assertEqual(
            notes,
            [
                CRMNote(
                    crm_id=event["id"],
                    crm_system="salesforce",
                    content="[Event] Test Event (2025-07-21 18:00)\nTest event description",
                    created_at=current_time,
                    web_link=f"https://test.salesforce.com/{event['id']}",
                )
            ],
        )

    @mock_salesforce
    def test_fetch_notes_for_client_no_crm_id(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        client = Client.objects.create(name="Test Client", organization=org)  # No CRM ID

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=30))
        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_notes_for_client_salesforce_error(self) -> None:
        """Test fetch notes when Salesforce query fails"""
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        _, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        # Mock query to raise an exception
        def raise_error(*args, **kwargs):  # type: ignore[no-untyped-def]
            raise Exception("Salesforce query error")

        self.simple_salesforce.query = raise_error  # type: ignore[method-assign]

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=30))
        self.assertEqual(notes, [])

    @mock_salesforce
    def test_list_users_success(self) -> None:
        """Test successful listing of CRM users"""
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)

        # Create some Salesforce users
        sf_user1 = self.simple_salesforce.User.create(
            {  # type: ignore[operator]
                "Email": "<EMAIL>",
                "Name": "User One",
                "IsActive": True,
            }
        )
        sf_user2 = self.simple_salesforce.User.create(
            {  # type: ignore[operator]
                "Email": "<EMAIL>",
                "Name": "User Two",
                "IsActive": True,
            }
        )

        # Create matching Zeplyn user for user1
        User.objects.create(username="user1", email="<EMAIL>", organization=org)

        with patch("deepinsights.core.integrations.crm.salesforce_utils.list_crm_users") as mock_list:
            expected_users = [
                CRMUser(
                    name="User One",
                    crm_id=sf_user1["id"],
                    organization=ZeplynOrganization(uuid=str(org.uuid), name=org.name),
                    zeplyn_user=ZeplynUser(
                        uuid=str(User.objects.get(email="<EMAIL>").uuid),
                        email="<EMAIL>",
                        name="user1",
                    ),
                ),
                CRMUser(
                    name="User Two",
                    crm_id=sf_user2["id"],
                    organization=ZeplynOrganization(uuid=str(org.uuid), name=org.name),
                    zeplyn_user=None,
                ),
            ]
            mock_list.return_value = expected_users

            result = self.salesforce.list_users(user)
            self.assertEqual(result, expected_users)
            mock_list.assert_called_once_with(self.simple_salesforce, org)

    @mock_salesforce
    def test_get_client_basic_info_not_implemented(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)
        client = Client.objects.create(name="Test Client", organization=org)
        self.assertIsNone(self.salesforce.get_client_basic_info(client, user))

    @mock_salesforce
    def test_fetch_events_not_implemented(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)

        result = self.salesforce.fetch_events(user, timedelta(days=30))
        self.assertEqual(result, [])

    @mock_salesforce
    def test_fetch_workflows_for_user_not_implemented(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)

        result = self.salesforce.fetch_workflows_for_user(user)
        self.assertEqual(result, [])
