from datetime import datetime, timed<PERSON><PERSON>
from datetime import timezone as datetime_timezone
from typing import Any, <PERSON><PERSON>
from unittest.mock import MagicMock, patch
from uuid import uuid4

from django.core import cache
from django.test import TestCase, override_settings
from django.utils import timezone
from simple_mockforce import mock_salesforce
from simple_salesforce.api import Salesforce

from deepinsights.core.integrations.crm import salesforce_utils
from deepinsights.core.integrations.crm.crm_models import (
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTarget,
    CRMSyncTargets,
    CRMUser,
    ZeplynOrganization,
    ZeplynUser,
)
from deepinsights.core.integrations.crm.salesforce_practifi import SalesforcePractifi
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.flags import Flag
from deepinsights.users.models.user import User


class SalesforcePractifiTestCase(TestCase):
    def setUp(self) -> None:
        cache.cache.clear()
        self.simple_salesforce = Salesforce(instance="test.salesforce.com", session_id="")
        self.salesforce = SalesforcePractifi(sf=self.simple_salesforce)
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, False)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, False)

    def setFlagActive(self, flag: Flags, is_active: bool) -> None:
        f = Flag.objects.get(name=flag.name)
        f.everyone = is_active
        f.override_enabled_by_environment = None
        f.save()

    def populateSalesforceTypes(self) -> None:
        for type in [
            self.simple_salesforce.Task,
            self.simple_salesforce.User,
            self.simple_salesforce.Account,
            self.simple_salesforce.Event,
        ]:
            id = type.create({})["id"]  # type: ignore[operator]
            type.delete(id)  # type: ignore[operator]

    def newAccountAndClient(
        self, name: str | None, owner_email: str, org: Organization, *, phone: str | None = None
    ) -> Tuple[str, Client]:
        account_name = name.replace("'", "\\'") if name else f"Test Account {uuid4()}"
        account_data = {
            "Name": account_name,
            "Owner.Email": owner_email.replace("'", "\\'"),
        }
        if phone:
            account_data["Phone"] = phone
        account = self.simple_salesforce.Account.create(account_data)  # type: ignore[operator]
        self.assertFalse(account["errors"])
        client = Client.objects.create(crm_id=account["id"], name=account_name, organization=org)
        return account["id"], client

    def newUser(self, org: Organization | None = None) -> Tuple[str, str]:
        email = f"{uuid4()}@example.com"
        user = self.simple_salesforce.User.create({"Email": email})  # type: ignore[operator]
        zeplyn_user = User.objects.create(username=email, email=email, organization=org)
        zeplyn_user.crm_configuration["crm_system"] = "salesforce"
        zeplyn_user.save()
        return (user["id"], email)

    @override_settings(SALESFORCE_USER="user", SALESFORCE_PASSWORD="password", SALESFORCE_INSTANCE="instance")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_from_settings(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        SalesforcePractifi(user=user)
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="", instance_url="instance"
        )

    @mock_salesforce
    def test_get_accounts_by_owner_email_and_name(self) -> None:
        self.populateSalesforceTypes()

        owner_user = self.simple_salesforce.User.create(  # type: ignore[operator]
            {
                "Email": "<EMAIL>",
                "LastName": "Owner",
            }
        )

        account = self.simple_salesforce.Account.create(  # type: ignore[operator]
            {
                "Name": "Test Account",
                "OwnerId": owner_user["id"],
                "Owner.Email": "<EMAIL>",
                "Phone": "***********",
            }
        )

        accounts = self.salesforce.get_accounts_by_owner_email_and_name("<EMAIL>")

        self.assertEqual(len(accounts), 1)
        self.assertEqual(accounts[0].crm_id, account["id"])
        self.assertEqual(accounts[0].name, "Test Account")
        self.assertEqual(accounts[0].phone_number, "+***********")

    @mock_salesforce
    def test_resolve_sync_targets_with_client_crm_id(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        event = self.simple_salesforce.Event.create(  # type: ignore[operator]
            {
                "Subject": "Test Meeting",
                "WhatId": account_id,
                "ActivityDateTime": datetime.now(datetime_timezone.utc).isoformat(),
            }
        )

        note = Note(
            note_owner=user,
            client={"uuid": str(client.uuid)},
        )

        sync_targets = self.salesforce.resolve_sync_targets(note, user)

        self.assertEqual(sync_targets.status, CRMSyncTargets.Status.RESOLUTION_REQUIRED)
        self.assertGreaterEqual(len(sync_targets.note_targets), 3)  # existing event + new event + new call

        new_event_targets = [t for t in sync_targets.note_targets if t.name == "New Event"]
        new_call_targets = [t for t in sync_targets.note_targets if t.name == "Log a Call"]

        self.assertEqual(len(new_event_targets), 1)
        self.assertEqual(len(new_call_targets), 1)

    @mock_salesforce
    def test_resolve_sync_targets_final_status(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        note = Note(note_owner=user, client={"uuid": str(client.uuid)})

        client_target = CRMSyncTarget(crm_id=account_id, type="account", name="Test Client")
        event_target = CRMSyncTarget(crm_id="event123", type="event", name="Test Event", parent=client_target)

        selected_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[event_target],
        )

        sync_targets = self.salesforce.resolve_sync_targets(note, user, selected_targets)

        self.assertEqual(sync_targets.status, CRMSyncTargets.Status.FINAL)
        self.assertEqual(len(sync_targets.note_targets), 1)

    @mock_salesforce
    def test_resolve_sync_targets_no_client(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)

        note = Note(note_owner=user, client=None)
        sync_targets = self.salesforce.resolve_sync_targets(note, user)

        self.assertEqual(sync_targets.status, CRMSyncTargets.Status.CLIENTS_REQUIRED)
        self.assertEqual(len(sync_targets.note_targets), 0)

    @mock_salesforce
    def test_add_interaction_with_client_creates_event(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        note = Note(note_owner=user, client={"uuid": str(client.uuid)}, created=timezone.now(), metadata={})
        note.save()

        client_target = CRMSyncTarget(crm_id=account_id, type="account", name="Test Client")
        event_target = CRMSyncTarget(crm_id="new_event", type="event", name="New Event", parent=client_target)

        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[event_target],
        )

        self.salesforce.add_interaction_with_client(note, sync_targets)

        events = self.simple_salesforce.query_all("SELECT Id FROM Event")["records"]
        self.assertEqual(len(events), 1)

        note.refresh_from_db()
        if isinstance(note.metadata, dict):
            self.assertIsNotNone(note.metadata.get("interactionId"))

    @mock_salesforce
    def test_add_interaction_with_client_creates_call(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        note = Note(note_owner=user, client={"uuid": str(client.uuid)}, created=timezone.now(), metadata={})
        note.save()

        client_target = CRMSyncTarget(crm_id=account_id, type="account", name="Test Client")
        call_target = CRMSyncTarget(crm_id="new_call", type="call", name="Log a Call", parent=client_target)

        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[call_target],
        )

        self.salesforce.add_interaction_with_client(note, sync_targets)

        tasks = self.simple_salesforce.query_all("SELECT Id FROM Task")["records"]
        self.assertEqual(len(tasks), 1)

        note.refresh_from_db()
        if isinstance(note.metadata, dict):
            self.assertIsNotNone(note.metadata.get("interactionId"))

    @mock_salesforce
    def test_add_interaction_with_client_updates_existing_event(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        existing_event = self.simple_salesforce.Event.create(  # type: ignore[operator]
            {
                "Subject": "Existing Meeting",
                "WhatId": account_id,
                "ActivityDateTime": datetime.now(datetime_timezone.utc).isoformat(),
                "Description": "Original description",
            }
        )

        note = Note(note_owner=user, client={"uuid": str(client.uuid)}, created=timezone.now(), metadata={})
        note.save()

        client_target = CRMSyncTarget(crm_id=account_id, type="account", name="Test Client")
        event_target = CRMSyncTarget(
            crm_id=existing_event["id"], type="event", name="Existing Meeting", parent=client_target
        )

        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[event_target],
        )

        self.salesforce.add_interaction_with_client(note, sync_targets)

        updated_event = self.simple_salesforce.Event.get(existing_event["id"])  # type: ignore[operator]
        self.assertIn("Updated from Zeplyn", updated_event["Description"])
        self.assertIn("Original description", updated_event["Description"])

    @mock_salesforce
    def test_add_interaction_with_client_creates_tasks(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        note = Note(note_owner=user, client={"uuid": str(client.uuid)}, created=timezone.now(), metadata={})
        note.save()

        Task.objects.create(task_title="Task 1", task_desc="Description 1", note=note, assignee=user)
        Task.objects.create(task_title="Task 2", note=note)

        client_target = CRMSyncTarget(crm_id=account_id, type="account", name="Test Client")
        event_target = CRMSyncTarget(crm_id="new_event", type="event", name="New Event", parent=client_target)

        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[event_target],
        )

        sync_items = {
            CRMSyncSection.TASKS: CRMSyncItemSelection(
                include_section=True,
                included_items=None,
            )
        }

        with patch("deepinsights.core.integrations.crm.salesforce_utils.add_task") as mock_add_task:
            self.salesforce.add_interaction_with_client(note, sync_targets, sync_items)
            self.assertEqual(mock_add_task.call_count, 2)

    @mock_salesforce
    def test_add_interaction_with_client_no_sync_targets(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)

        note = Note(note_owner=user, metadata={})
        sync_targets = CRMSyncTargets(status=CRMSyncTargets.Status.FINAL, note_targets=[])

        with self.assertRaises(ValueError):
            self.salesforce.add_interaction_with_client(note, sync_targets)

    @mock_salesforce
    def test_add_interaction_with_client_multiple_sync_targets(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id1, client1 = self.newAccountAndClient("Client 1", owner_email=user_email, org=org)
        account_id2, client2 = self.newAccountAndClient("Client 2", owner_email=user_email, org=org)

        note = Note(note_owner=user, client={"uuid": str(client1.uuid)}, created=timezone.now(), metadata={})
        note.save()

        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(crm_id=account_id1, type="account", name="Client 1"),
                CRMSyncTarget(crm_id=account_id2, type="account", name="Client 2"),
            ],
        )

        with self.assertRaises(ValueError):
            self.salesforce.add_interaction_with_client(note, sync_targets)

    @mock_salesforce
    def test_update_existing_event_truncates_long_description(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        existing_description = "Original event description " + "B" * 15000  # ~15,027 chars
        long_summary = "A" * 20000  # 20,000 chars of new content

        note = Note(note_owner=user, client={"uuid": str(client.uuid)}, created=timezone.now(), metadata={})
        note.save()

        with patch.object(note, "get_summary_for_crm", return_value=long_summary):
            existing_event = self.simple_salesforce.Event.create(
                {  # type: ignore[operator]
                    "Subject": "Existing Meeting",
                    "WhatId": account_id,
                    "ActivityDateTime": datetime.now(datetime_timezone.utc).isoformat(),
                    "Description": existing_description,
                }
            )

            client_target = CRMSyncTarget(crm_id=account_id, type="account", name="Test Client")
            event_target = CRMSyncTarget(
                crm_id=existing_event["id"], type="event", name="Existing Meeting", parent=client_target
            )

            sync_targets = CRMSyncTargets(
                status=CRMSyncTargets.Status.FINAL,
                note_targets=[event_target],
            )

            self.salesforce.add_interaction_with_client(note, sync_targets)

            updated_event = self.simple_salesforce.Event.get(existing_event["id"])  # type: ignore[operator]
            updated_description = updated_event["Description"]

            self.assertIn("Original event description", updated_description)
            self.assertIn("Updated from Zeplyn", updated_description)
            self.assertLess(len(updated_description), 32000)
            self.assertTrue(updated_description.endswith("…"))
            self.assertIn("A", updated_description)  # Some of the long content should be present
            self.assertNotIn("A" * 20000, updated_description)

    @mock_salesforce
    def test_update_existing_event_handles_extremely_long_existing_description(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        note = Note(note_owner=user, client={"uuid": str(client.uuid)}, created=timezone.now(), metadata={})
        note.save()

        with patch.object(note, "get_summary_for_crm", return_value="New meeting notes"):
            extremely_long_description = "B" * 31900  # Almost at the limit
            existing_event = self.simple_salesforce.Event.create(  # type: ignore[operator]
                {
                    "Subject": "Existing Meeting",
                    "WhatId": account_id,
                    "ActivityDateTime": datetime.now(datetime_timezone.utc).isoformat(),
                    "Description": extremely_long_description,
                }
            )

            client_target = CRMSyncTarget(crm_id=account_id, type="account", name="Test Client")
            event_target = CRMSyncTarget(
                crm_id=existing_event["id"], type="event", name="Existing Meeting", parent=client_target
            )

            sync_targets = CRMSyncTargets(
                status=CRMSyncTargets.Status.FINAL,
                note_targets=[event_target],
            )

            self.salesforce.add_interaction_with_client(note, sync_targets)

            updated_event = self.simple_salesforce.Event.get(existing_event["id"])  # type: ignore[operator]
            updated_description = updated_event["Description"]

            self.assertLess(len(updated_description), 32000)
            self.assertIn("Updated from Zeplyn", updated_description)
            self.assertTrue(updated_description.endswith("…"))

    @mock_salesforce
    def test_create_new_call_with_correct_fields(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        note = Note(
            note_owner=user,
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
            metadata={"meeting_name": "Test Call"},
        )
        note.save()

        client_target = CRMSyncTarget(crm_id=account_id, type="account", name="Test Client")
        call_target = CRMSyncTarget(crm_id="new_call", type="call", name="Log a Call", parent=client_target)

        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[call_target],
        )

        self.salesforce.add_interaction_with_client(note, sync_targets)

        tasks = self.simple_salesforce.query_all(
            "SELECT Id, Subject, TaskSubtype, Status, WhatId, practifi__Related_entity__c FROM Task"
        )["records"]

        self.assertEqual(len(tasks), 1)
        task = tasks[0]
        self.assertEqual(task["Subject"], "Test Call")
        self.assertEqual(task["TaskSubtype"], "Call")
        self.assertEqual(task["Status"], "Completed")
        self.assertEqual(task["WhatId"], account_id)
        self.assertEqual(task["practifi__Related_entity__c"], account_id)

    @mock_salesforce
    def test_fetch_notes_for_client_success(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        # Use a date that's well within the 30-day lookback period
        current_time = datetime.now(datetime_timezone.utc) - timedelta(days=1)

        # Create an Event
        event = self.simple_salesforce.Event.create(  # type: ignore[operator]
            {
                "Subject": "Test Event",
                "WhatId": account_id,
                "ActivityDateTime": salesforce_utils.salesforceTime(current_time),
                "Description": "Test event description",
            }
        )

        # Create a Call (Task with TaskSubtype = "Call")
        task_date = (datetime.now(datetime_timezone.utc) - timedelta(days=1)).date()
        call_task = self.simple_salesforce.Task.create(  # type: ignore[operator]
            {
                "Subject": "Test Call",
                "WhatId": account_id,
                "ActivityDate": task_date.isoformat(),
                "TaskSubtype": "Call",
                "Description": "Test call description",
                "CreatedDate": salesforce_utils.salesforceTime(current_time),
            }
        )

        # Store the original method
        original_query_all = self.simple_salesforce.query_all

        def mock_query_all(query: str) -> dict[str, Any]:
            if "FROM Event" in query:
                return original_query_all(query)
            elif "FROM Task" in query and "TaskSubtype = 'Call'" in query:
                # Return the call task directly if it matches our criteria
                task_record = self.simple_salesforce.Task.get(call_task["id"])  # type: ignore[operator]
                # Ensure the task matches our filter criteria
                if (
                    task_record.get("WhatId") == account_id
                    and task_record.get("TaskSubtype") == "Call"
                    and task_record.get("Description")
                ):
                    return {"records": [task_record]}
                return {"records": []}
            else:
                return original_query_all(query)

        with patch.object(self.simple_salesforce, "query_all", side_effect=mock_query_all):
            notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=30))

            self.assertEqual(len(notes), 2)

            event_note = next((note for note in notes if note.crm_id == event["id"]), None)
            call_note = next((note for note in notes if note.crm_id == call_task["id"]), None)

            self.assertIsNotNone(event_note, "Event note should be found")
            self.assertIsNotNone(call_note, "Call note should be found")

            assert event_note is not None
            assert call_note is not None

            self.assertIn("Event: Test Event", event_note.content)
            self.assertIn("Test event description", event_note.content)

            self.assertIn("Call: Test Call", call_note.content)
            self.assertIn("Test call description", call_note.content)

    @mock_salesforce
    def test_fetch_notes_for_client_no_crm_id(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        client = Client.objects.create(name="Test Client", organization=org)  # No CRM ID

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=30))
        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_notes_for_client_filters_empty_descriptions(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        current_time = datetime(2025, 7, 21, 18, 0, tzinfo=datetime_timezone.utc)

        # Event with description
        self.simple_salesforce.Event.create(  # type: ignore[operator]
            {
                "Subject": "Event with Description",
                "WhatId": account_id,
                "ActivityDateTime": salesforce_utils.salesforceTime(current_time),
                "Description": "Has description",
            }
        )

        # Event without description
        self.simple_salesforce.Event.create(  # type: ignore[operator]
            {
                "Subject": "Event without Description",
                "WhatId": account_id,
                "ActivityDateTime": salesforce_utils.salesforceTime(current_time),
                "Description": "",
            }
        )

        # Call with whitespace-only description
        self.simple_salesforce.Task.create(  # type: ignore[operator]
            {
                "Subject": "Call with Whitespace",
                "WhatId": account_id,
                "ActivityDate": current_time.strftime("%Y-%m-%d"),
                "TaskSubtype": "Call",
                "Description": "   \n\t   ",
            }
        )

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=30))

        # Should only return the event with actual description
        self.assertEqual(len(notes), 1)
        self.assertIn("Event with Description", notes[0].content)

    @mock_salesforce
    def test_fetch_notes_for_client_error_handling(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        _, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        def raise_error(*args: Any, **kwargs: Any) -> None:
            raise Exception("Salesforce query error")

        with patch.object(self.simple_salesforce, "query_all", side_effect=raise_error):
            notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=30))
            self.assertEqual(notes, [])

    @mock_salesforce
    def test_list_users_success(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)

        sf_user1 = self.simple_salesforce.User.create(  # type: ignore[operator]
            {
                "Email": "<EMAIL>",
                "Name": "User One",
                "IsActive": True,
            }
        )

        User.objects.create(username="user1", email="<EMAIL>", organization=org)

        with patch("deepinsights.core.integrations.crm.salesforce_utils.list_crm_users") as mock_list:
            expected_users = [
                CRMUser(
                    name="User One",
                    crm_id=sf_user1["id"],
                    organization=ZeplynOrganization(uuid=str(org.uuid), name=org.name),
                    zeplyn_user=ZeplynUser(
                        uuid=str(User.objects.get(email="<EMAIL>").uuid),
                        email="<EMAIL>",
                        name="user1",
                    ),
                ),
            ]
            mock_list.return_value = expected_users

            result = self.salesforce.list_users(user)
            self.assertEqual(result, expected_users)
            mock_list.assert_called_once_with(self.simple_salesforce, org)

    @mock_salesforce
    def test_list_users_no_organization(self) -> None:
        user = User.objects.create(username="testuser", email="<EMAIL>")  # No organization

        result = self.salesforce.list_users(user)
        self.assertEqual(result, [])

    def test_get_client_basic_info_not_implemented(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)
        client = Client.objects.create(name="Test Client", organization=org)

        self.assertIsNone(self.salesforce.get_client_basic_info(client, user))

    def test_fetch_events_not_implemented(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)

        result = self.salesforce.fetch_events(user, timedelta(days=30))
        self.assertEqual(result, [])

    def test_fetch_workflows_for_user_not_implemented(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)

        result = self.salesforce.fetch_workflows_for_user(user)
        self.assertEqual(result, [])
