import logging
from datetime import datetime, timedelta
from datetime import timezone as datetime_timezone
from types import MappingProxyType
from typing import Tuple
from unittest import mock
from unittest.mock import MagicMock, patch
from uuid import uuid4

from django.core import cache
from django.test import TestCase, override_settings
from django.utils import timezone
from simple_mockforce import mock_salesforce
from simple_salesforce.api import Salesforce
from simple_salesforce.exceptions import SalesforceResourceNotFound

from deepinsights.core.integrations.crm.crm_models import (
    CRMAccount,
    CRMNote,
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTarget,
    CRMSyncTargets,
    CRMUser,
    ZeplynOrganization,
    ZeplynUser,
)
from deepinsights.core.integrations.crm.salesforce_financial_cloud import SalesforceFinancialCloud
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.flags import Flag
from deepinsights.users.models.user import User


class SalesforceFinancialCloudTestCase(TestCase):
    def setUp(self) -> None:
        cache.cache.clear()
        self.simple_salesforce = Salesforce(instance="test.salesforce.com", session_id="")
        self.salesforce = SalesforceFinancialCloud(sf=self.simple_salesforce)
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, False)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, False)

    # Sets the provided flag to the given active state.
    def setFlagActive(self, flag: Flags, is_active: bool) -> None:
        f = Flag.objects.get(name=flag.name)
        f.everyone = is_active
        f.override_enabled_by_environment = None  # To ensure no environment override
        f.save()

    # Populate the required Salesforce types in the mock, so that they can be used in queries even
    # if they have never been created.
    def populateSalesforceTypes(self) -> None:
        for type in [
            self.simple_salesforce.Interaction,
            self.simple_salesforce.InteractionSummary,
            self.simple_salesforce.Task,
            self.simple_salesforce.User,
        ]:
            id = type.create({})["id"]  # type: ignore[operator]
            type.delete(id)  # type: ignore[operator]

    # Creates a new account with the given name (or a randomized name if the name parameter is None).
    def newAccountAndClient(
        self, name: str | None, owner_email: str, org: Organization, *, phone: str | None = None
    ) -> Tuple[str, Client]:
        account_name = name.replace("'", "\\'") if name else f"Test Account {uuid4()}"
        account_data = {
            "Name": account_name,
            "Owner.Email": owner_email.replace("'", "\\'"),
        }
        if phone:
            account_data["Phone"] = phone
        account = self.simple_salesforce.Account.create(account_data)  # type: ignore[operator]
        self.assertFalse(account["errors"])
        client = Client.objects.create(crm_id=account["id"], name=account_name, organization=org)
        return account["id"], client

    # Creates a new arbitrary user, both in the Salesforce and Zeplyn databases.
    #
    # Returns a tuple of (salesforceUserId, userEmail).
    def newUser(self, org: Organization | None = None) -> Tuple[str, str]:
        email = f"{uuid4()}@example.com"
        user = self.simple_salesforce.User.create({"Email": email})  # type: ignore[operator]
        zeplyn_user = User.objects.create(username=email, email=email, organization=org)
        zeplyn_user.crm_configuration["crm_system"] = "salesforce"
        zeplyn_user.save()
        return (user["id"], email)

    @override_settings(SALESFORCE_USER="user", SALESFORCE_PASSWORD="password", SALESFORCE_INSTANCE="instance")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_from_settings(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        SalesforceFinancialCloud(user=user)
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="", instance_url="instance"
        )

    @override_settings(
        SALESFORCE_USER="settings_user",
        SALESFORCE_PASSWORD="settings_password",
        SALESFORCE_INSTANCE="settings_instance",
    )
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_with_params(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        SalesforceFinancialCloud(
            user=user, username="user", password="password", security_token="token", instance_url="instance"
        )
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_for_comsumer_key_missing_values(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        SalesforceFinancialCloud(
            user=user, username="user", password="password", security_token="token", instance_url="instance"
        )
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @override_settings(SALESFORCE_ZEPLYN_APP_DOMAIN="app_domain")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_for_comsumer_key_flag_disabled(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, False)
        SalesforceFinancialCloud(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @override_settings(SALESFORCE_ZEPLYN_APP_DOMAIN="app_domain")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_for_comsumer_key(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        SalesforceFinancialCloud(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
            security_token="token",
            instance_url="instance",
            domain="app_domain",
        )

    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_for_user_oauth_missing_values(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, True)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        SalesforceFinancialCloud(
            user=user, username="user", password="password", security_token="token", instance_url="instance"
        )
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @override_settings(SALESFORCE_ZEPLYN_APP_DOMAIN="app_domain")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.SalesforceOAuth")
    def test_init_for_user_oauth_flag_disabled(self, sf_oauth: MagicMock, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, False)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, False)
        sf_oauth.return_value.get_access_token.return_value = "access_token"
        SalesforceFinancialCloud(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @override_settings(SALESFORCE_ZEPLYN_APP_DOMAIN="app_domain")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.SalesforceOAuth")
    def test_init_for_user_oauth_token_exception(self, sf_oauth: MagicMock, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, True)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        sf_oauth.return_value.get_access_token.side_effect = Exception("Test error")
        SalesforceFinancialCloud(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
            security_token="token",
            instance_url="instance",
            domain="app_domain",
        )

    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.SalesforceOAuth")
    def test_init_for_user_oauth(self, sf_oauth: MagicMock, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, True)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        sf_oauth.return_value.get_access_token.return_value = "access_token"
        SalesforceFinancialCloud(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(session_id="access_token", instance_url="instance")

    @mock_salesforce
    def test_get_accounts_by_owner_email_and_name(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        owner_one = "<EMAIL>"
        owner_two = "<EMAIL>"

        user1_id = self.simple_salesforce.User.create({"Email": owner_one})["id"]  # type: ignore[operator]
        user2_id = self.simple_salesforce.User.create({"Email": owner_two})["id"]  # type: ignore[operator]

        (account_one_id, _) = self.newAccountAndClient("one", owner_one, org=org, phone="**********")
        (account_two_id, _) = self.newAccountAndClient("two", owner_one, org=org, phone="invalid")
        (account_three_id, _) = self.newAccountAndClient("three", owner_two, org=org, phone="+*************")

        self.simple_salesforce.Account.update(account_one_id, {"OwnerId": user1_id})  # type: ignore[operator]
        self.simple_salesforce.Account.update(account_two_id, {"OwnerId": user1_id})  # type: ignore[operator]
        self.simple_salesforce.Account.update(account_three_id, {"OwnerId": user2_id})  # type: ignore[operator]

        expected_accounts_for_owner_one = [
            CRMAccount(
                crm_id=account_one_id,
                name="one",
                phone_number="**********",
                client_type="account",
                crm_system="salesforce",
                is_owned_by_user=True,
            ),
            CRMAccount(
                crm_id=account_two_id,
                name="two",
                client_type="account",
                crm_system="salesforce",
                is_owned_by_user=True,
            ),
            CRMAccount(
                crm_id=account_three_id,
                name="three",
                phone_number="+*************",
                client_type="account",
                crm_system="salesforce",
                is_owned_by_user=False,
            ),
        ]

        expected_accounts_for_owner_two = [
            CRMAccount(
                crm_id=account_one_id,
                name="one",
                phone_number="**********",
                client_type="account",
                crm_system="salesforce",
                is_owned_by_user=False,
            ),
            CRMAccount(
                crm_id=account_two_id,
                name="two",
                client_type="account",
                crm_system="salesforce",
                is_owned_by_user=False,
            ),
            CRMAccount(
                crm_id=account_three_id,
                name="three",
                phone_number="+*************",
                client_type="account",
                crm_system="salesforce",
                is_owned_by_user=True,
            ),
        ]

        actual_accounts_owner_one = self.salesforce.get_accounts_by_owner_email_and_name(owner_one)
        self.assertEqual(actual_accounts_owner_one, expected_accounts_for_owner_one)

        actual_accounts_owner_two = self.salesforce.get_accounts_by_owner_email_and_name(owner_two)
        self.assertEqual(actual_accounts_owner_two, expected_accounts_for_owner_two)

    @mock_salesforce
    def test_get_accounts_by_owner_email_and_name_with_quotes(self) -> None:
        self.populateSalesforceTypes()

        owner_one = "<EMAIL>"
        org = Organization.objects.create(name="Test Organization")

        user_id = self.simple_salesforce.User.create({"Email": owner_one})["id"]  # type: ignore[operator]

        (account_one_id, _) = self.newAccountAndClient("o'n'e", owner_one, org)
        self.simple_salesforce.Account.update(account_one_id, {"OwnerId": user_id})  # type: ignore[operator]
        self.assertTrue(account_one_id)
        self.assertEqual(
            self.salesforce.get_accounts_by_owner_email_and_name(owner_one, "o'"),
            [
                CRMAccount(
                    crm_id=account_one_id,
                    name="o\\'n\\'e",
                    client_type="account",
                    crm_system="salesforce",
                    is_owned_by_user=True,
                ),
            ],
        )

    @mock_salesforce
    def test_add_interaction_with_client_with_no_account(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>", organization=org)
        client = Client.objects.create(crm_id="123", name="Client", organization=org)
        note = Note(
            note_owner=user,
            status="scheduled",
            client={"uuid": str(client.uuid)},
            metadata={},
        )

        with self.assertRaisesRegex(
            ValueError, "Could not find a Salesforce User with the given email: <EMAIL>"
        ):
            sync_targets = self.salesforce.resolve_sync_targets(note, user)
            self.salesforce.add_interaction_with_client(note, sync_targets)

        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Interaction")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM InteractionSummary")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"])

    @mock_salesforce
    def test_add_interaction_with_client_with_no_client_id(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        (_, user_email) = self.newUser(org=org)
        self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
        )

        with self.assertRaisesRegex(ValueError, "No client provided. Not adding interaction."):
            sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
            self.salesforce.add_interaction_with_client(note, sync_targets)

        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Interaction")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM InteractionSummary")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"])

    @mock_salesforce
    def test_add_interaction_creation_with_salesforce_error(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        (_, user_email) = self.newUser(org)
        _, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )
        note.save()

        class MockInteraction:
            def create(self, *args, **kwargs):  # type: ignore[no-untyped-def]
                raise AssertionError("Test error")

        self.simple_salesforce.Interaction = MockInteraction  # type: ignore[attr-defined]
        with self.assertRaisesRegex(RuntimeError, "error recording interaction: Test error. Not updating note."):
            sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
            self.salesforce.add_interaction_with_client(note, sync_targets)

        note.refresh_from_db()
        self.assertFalse(note.metadata.get("interactionId"))  # type: ignore[union-attr]
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Interaction")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM InteractionSummary")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"])

    @mock_salesforce
    def test_add_interaction_with_client_creates_interaction(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )
        task1 = Task.objects.create(note=note, task_title="Task 1")
        task2 = Task.objects.create(note=note, task_title="Task 2")
        sync_items = MappingProxyType(
            {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True, included_items=[str(task2.uuid)])}
        )

        sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
        self.salesforce.add_interaction_with_client(note, sync_targets, sync_items)
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM Interaction")["records"]), 1)
        interaction = self.simple_salesforce.Interaction.get(note.metadata["interactionId"])  # type: ignore[index, operator]
        self.assertEqual(interaction["AccountId"], account_id)
        summary = self.simple_salesforce.query_all("SELECT NextSteps FROM InteractionSummary")["records"][0]
        self.assertIn(task2.task_title, summary["NextSteps"])
        self.assertNotIn(task1.task_title, summary["NextSteps"])

    @mock_salesforce
    def test_add_interaction_with_client_multiple_clients(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[
                CRMSyncTarget(crm_id=account_id, type="", name="Account one"),
                CRMSyncTarget(crm_id="***************", type="", name=""),
            ],
        )
        with self.assertLogs(level=logging.WARNING) as cm:
            self.salesforce.add_interaction_with_client(note, sync_targets)

            # simple_mockforce logs a warning, and the code that handles the summary logs a warning,
            # so there will be three log entries.
            self.assertEqual(len(cm.output), 3)
            self.assertIn("Multiple clients provided for note", cm.output[1])
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM Interaction")["records"]), 1)
        interaction = self.simple_salesforce.Interaction.get(note.metadata["interactionId"])  # type: ignore[index, operator]
        self.assertEqual(interaction["AccountId"], account_id)

    @mock_salesforce
    def test_add_interaction_uses_uuid_as_crm_id(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
        self.salesforce.add_interaction_with_client(note, sync_targets)
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM Interaction")["records"]), 1)
        interaction = self.simple_salesforce.Interaction.get(note.metadata["interactionId"])  # type: ignore[index, operator]
        self.assertEqual(interaction["AccountId"], account_id)

    @mock_salesforce
    def test_add_interaction_with_client_populates_meeting_title(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        _, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={"meeting_name": "Meeting"},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
        self.salesforce.add_interaction_with_client(note, sync_targets)
        interaction = self.simple_salesforce.Interaction.get(note.metadata["interactionId"])  # type: ignore[index, operator]
        self.assertEqual(interaction["Name"], "Meeting")

    @mock_salesforce
    def test_add_interaction_with_client_does_not_update_interaction(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        _, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )
        sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
        self.salesforce.add_interaction_with_client(note, sync_targets)

        # Adding a new interaction for the same note should not change anything in the Salesforce database.
        note.metadata["meeting_name"] = "New meeting name"  # type: ignore[index]
        self.salesforce.add_interaction_with_client(note, sync_targets)
        interaction = self.simple_salesforce.Interaction.get(note.metadata["interactionId"])  # type: ignore[index, operator]
        self.assertEqual(interaction["Name"], "Meeting with a client")

        # Remove the link between the meeting and the interaction.
        note.metadata["interactionId"] = None  # type: ignore[index]
        self.salesforce.add_interaction_with_client(note, sync_targets)
        interaction = self.simple_salesforce.Interaction.get(note.metadata["interactionId"])  # type: ignore[index, operator]
        self.assertEqual(interaction["Name"], "New meeting name")

    @mock_salesforce
    def test_add_interaction_with_client_generates_interaction_summary(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={"meeting_name": "Meeting"},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
        self.salesforce.add_interaction_with_client(note, sync_targets)
        interaction_summary = self.simple_salesforce.InteractionSummary.get(note.metadata["interactionSummaryId"])  # type: ignore[index, operator]
        self.assertEqual(interaction_summary["OwnerId"], user_id)
        self.assertEqual(interaction_summary["Name"], "Meeting")
        self.assertEqual(interaction_summary["InteractionId"], note.metadata["interactionId"])  # type: ignore[index]
        self.assertIsNotNone(interaction_summary["MeetingNotes"])
        self.assertEqual(interaction_summary["AccountId"], account_id)
        self.assertEqual(interaction_summary["NextSteps"], "No next steps identified")

    @mock_salesforce
    def test_add_interaction_with_client_does_not_update_interaction_summary(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        _, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={"meeting_name": "Meeting"},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
        self.salesforce.add_interaction_with_client(note, sync_targets)

        # Adding a new interaction for the same note should not change the summary in the Salesforce database.
        note.metadata["meeting_name"] = "New meeting name"  # type: ignore[index]
        self.salesforce.add_interaction_with_client(note, sync_targets)
        interaction_summary = self.simple_salesforce.InteractionSummary.get(note.metadata["interactionSummaryId"])  # type: ignore[index, operator]
        self.assertEqual(interaction_summary["Name"], "Meeting")

        # Remove the link between the meeting and the interaction summary.
        note.metadata["interactionSummaryId"] = None  # type: ignore[index]
        self.salesforce.add_interaction_with_client(note, sync_targets)
        interaction_summary = self.simple_salesforce.InteractionSummary.get(note.metadata["interactionSummaryId"])  # type: ignore[index, operator]
        self.assertEqual(interaction_summary["Name"], "New meeting name")

    @mock_salesforce
    def test_add_interaction_with_client_summary_meeting_notes(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        user = User.objects.get(email=user_email)
        note = Note(
            note_owner=user,
            status="scheduled",
            metadata={"meeting_name": "Meeting"},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )
        user.crm_configuration["crm_system"] = "salesforce"

        sync_targets = self.salesforce.resolve_sync_targets(note, user)
        self.salesforce.add_interaction_with_client(note, sync_targets)
        interaction_summary = self.simple_salesforce.InteractionSummary.get(note.metadata["interactionSummaryId"])  # type: ignore[index, operator]
        self.assertEqual(interaction_summary["OwnerId"], user_id)
        self.assertEqual(interaction_summary["Name"], "Meeting")
        self.assertEqual(interaction_summary["InteractionId"], note.metadata["interactionId"])  # type: ignore[index]
        self.assertIsNotNone(interaction_summary["MeetingNotes"])
        self.assertEqual(interaction_summary["AccountId"], account_id)
        self.assertEqual(interaction_summary["NextSteps"], "No next steps identified")

    @mock_salesforce
    def test_add_interaction_creation_with_salesforce_summary_error(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        _, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        def raiseException(*args, **kwargs):  # type: ignore[no-untyped-def]
            raise AssertionError("Test error")

        raiseException.create = raiseException  # type: ignore[attr-defined]
        self.simple_salesforce.InteractionSummary = raiseException  # type: ignore[attr-defined]

        with self.assertRaisesRegex(
            RuntimeError, "error recording interaction summary: Test error. Not updating note."
        ):
            sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
            self.salesforce.add_interaction_with_client(note, sync_targets)

        note.refresh_from_db()
        self.assertTrue(self.simple_salesforce.query_all("SELECT Id FROM Interaction")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM InteractionSummary")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"])
        self.assertTrue(note.metadata.get("interactionId"))  # type: ignore[union-attr]
        self.assertFalse(note.metadata.get("interactionSummaryId"))  # type: ignore[union-attr]

    @mock_salesforce
    def test_add_interaction_creation_task_creation(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )
        note.save()

        task = Task(task_title="Task One", note=note)
        no_metadata_task = Task(task_title="Task Two", task_desc="Task two description", note=note, metadata=None)
        completed_task = Task(
            task_title="Completed Task", task_desc="Completed task description", note=note, completed=True
        )
        already_existing_task = Task(
            task_title="Already exsting Task",
            task_desc="Already existing task description",
            note=note,
            metadata={"taskId": "123"},
        )
        task.save()
        no_metadata_task.save()
        completed_task.save()
        already_existing_task.save()

        note.task_set.add(task)
        note.task_set.add(no_metadata_task)
        note.task_set.add(completed_task)
        note.task_set.add(already_existing_task)

        sync_targets = self.salesforce.resolve_sync_targets(note, User.objects.get(email=user_email))
        self.salesforce.add_interaction_with_client(note, sync_targets)

        self.assertTrue(len(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"]), 3)

        task = Task.objects.get(pk=task.pk)
        salesforce_task = self.simple_salesforce.Task.get(task.metadata["taskId"])  # type: ignore[index, operator]
        self.assertEqual(salesforce_task["Subject"], "Task One")
        self.assertEqual(salesforce_task["Description"], "Follow up from client meeting.")
        self.assertEqual(salesforce_task["Status"], "Not Started")
        self.assertEqual(salesforce_task["Priority"], "Normal")
        self.assertEqual(salesforce_task["WhatId"], account_id)
        self.assertEqual(
            salesforce_task["ActivityDate"],
            (datetime.today().date() + timedelta(7)).strftime("%Y-%m-%dT%H:%M:%S.%f%z"),
        )
        self.assertEqual(salesforce_task["OwnerId"], user_id)

        no_metadata_task = Task.objects.get(pk=no_metadata_task.pk)
        salesforce_no_metadata_task = self.simple_salesforce.Task.get(no_metadata_task.metadata["taskId"])  # type: ignore[index, operator]
        self.assertEqual(salesforce_no_metadata_task["Subject"], "Task Two")
        self.assertEqual(
            salesforce_no_metadata_task["Description"], "Follow up from client meeting.\nTask two description"
        )
        self.assertEqual(salesforce_no_metadata_task["Status"], "Not Started")
        self.assertEqual(salesforce_no_metadata_task["Priority"], "Normal")
        self.assertEqual(salesforce_no_metadata_task["WhatId"], account_id)
        self.assertEqual(
            salesforce_task["ActivityDate"],
            (datetime.today().date() + timedelta(7)).strftime("%Y-%m-%dT%H:%M:%S.%f%z"),
        )
        self.assertEqual(salesforce_task["OwnerId"], user_id)

        completed_task = Task.objects.get(pk=completed_task.pk)
        salesforce_completed_task = self.simple_salesforce.Task.get(completed_task.metadata["taskId"])  # type: ignore[index, operator]
        self.assertEqual(salesforce_completed_task["Subject"], "Completed Task")
        self.assertEqual(
            salesforce_completed_task["Description"], "Follow up from client meeting.\nCompleted task description"
        )
        self.assertEqual(salesforce_completed_task["Status"], "Completed")
        self.assertEqual(salesforce_completed_task["Priority"], "Normal")
        self.assertEqual(salesforce_completed_task["WhatId"], account_id)
        self.assertEqual(
            salesforce_task["ActivityDate"],
            (datetime.today().date() + timedelta(7)).strftime("%Y-%m-%dT%H:%M:%S.%f%z"),
        )
        self.assertEqual(salesforce_task["OwnerId"], user_id)

        already_existing_task = Task.objects.get(pk=already_existing_task.pk)
        with self.assertRaises(SalesforceResourceNotFound):
            self.simple_salesforce.Task.get(already_existing_task.metadata["taskId"])  # type: ignore[index, operator]

    @mock_salesforce
    def test_fetch_notes_for_client_successful(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)

        interaction_start_time = datetime.now(datetime_timezone.utc)

        interaction = self.simple_salesforce.Interaction.create(  # type: ignore[operator]
            {
                "Name": "Test Interaction",
                "AccountId": account_id,
                "StartTime": interaction_start_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
            }
        )

        summary = self.simple_salesforce.InteractionSummary.create(  # type: ignore[operator]
            {
                "Name": "Test Summary",
                "InteractionId": interaction["id"],
                "AccountId": account_id,
                "MeetingNotes": "Test meeting notes",
                "NextSteps": "Test next steps",
            }
        )

        # Fetch notes
        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))

        self.assertEqual(
            notes,
            [
                CRMNote(
                    crm_id=interaction["id"],
                    crm_system="salesforce",
                    content="Test meeting notes\nTest next steps",
                    created_at=interaction_start_time,
                    web_link=f"https://test.salesforce.com/{interaction['id']}",
                )
            ],
        )

    @mock_salesforce
    def test_fetch_notes_for_client_no_interactions(self) -> None:
        """Test when no interactions are found"""
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        _, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))
        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_notes_for_client_no_summaries(self) -> None:
        """Test when interactions exist but no summaries are found"""
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)

        # Create test interaction without summary
        interaction_start_time = datetime.now(datetime_timezone.utc)
        interaction = self.simple_salesforce.Interaction.create(  # type: ignore[operator]
            {
                "Name": "Test Interaction",
                "AccountId": account_id,
                "StartTime": interaction_start_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
            }
        )

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))

        self.assertEqual(
            notes,
            [
                CRMNote(
                    crm_id=interaction["id"],
                    crm_system="salesforce",
                    content="No notes available\nNo next steps available",
                    created_at=interaction_start_time,
                    web_link=f"https://test.salesforce.com/{interaction['id']}",
                )
            ],
        )

    @mock_salesforce
    def test_fetch_notes_for_client_no_crm_id(self) -> None:
        """Test when client has no CRM ID"""
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        client = Client.objects.create(name="Test Client", organization=org)  # No CRM ID

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))
        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_notes_for_client_date_filter(self) -> None:
        """Test that the date filter correctly excludes old interactions"""
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)

        # Create old interaction (more than 30 days ago)
        old_date = (datetime.now(datetime_timezone.utc) - timedelta(days=40)).strftime("%Y-%m-%dT%H:%M:%S.%f%z")
        self.simple_salesforce.Interaction.create(  # type: ignore[operator]
            {"Name": "Old Interaction", "AccountId": account_id, "StartTime": old_date}
        )

        # Create recent interaction
        recent_date = datetime.now(datetime_timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f%z")
        recent_interaction = self.simple_salesforce.Interaction.create(  # type: ignore[operator]
            {"Name": "Recent Interaction", "AccountId": account_id, "StartTime": recent_date}
        )

        # Test with 30 day filter
        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=30))
        self.assertEqual(len(notes), 1)
        self.assertEqual(notes[0].crm_id, recent_interaction["id"])

    @mock_salesforce
    def test_fetch_notes_for_client_salesforce_error(self) -> None:
        """Test handling of Salesforce API errors"""
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        _, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)

        def raise_error(*args, **kwargs):  # type: ignore[no-untyped-def]
            raise Exception("Salesforce API Error")

        # Mock Salesforce query to raise an error
        self.simple_salesforce.query_all = raise_error  # type: ignore[method-assign]

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))
        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_notes_for_client_multiple_interactions(self) -> None:
        """Test fetching multiple interactions and summaries"""
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)

        # Create multiple interactions and summaries
        interaction_ids: list[str] = []
        interaction_start_time = datetime.now(datetime_timezone.utc)
        for i in range(3):
            interaction = self.simple_salesforce.Interaction.create(  # type: ignore[operator]
                {
                    "Name": f"Test Interaction {i}",
                    "AccountId": account_id,
                    "StartTime": interaction_start_time.strftime("%Y-%m-%dT%H:%M:%S.%f%z"),
                }
            )

            self.simple_salesforce.InteractionSummary.create(  # type: ignore[operator]
                {
                    "Name": f"Test Summary {i}",
                    "InteractionId": interaction["id"],
                    "AccountId": account_id,
                    "MeetingNotes": f"Test meeting notes {i}",
                    "NextSteps": f"Test next steps {i}",
                }
            )

            interaction_ids.append(interaction["id"])

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))

        self.assertEqual(
            notes,
            [
                CRMNote(
                    crm_id=interaction_ids[0],
                    crm_system="salesforce",
                    content="Test meeting notes 0\nTest next steps 0",
                    created_at=interaction_start_time,
                    web_link=f"https://test.salesforce.com/{interaction_ids[0]}",
                ),
                CRMNote(
                    crm_id=interaction_ids[1],
                    crm_system="salesforce",
                    content="Test meeting notes 1\nTest next steps 1",
                    created_at=interaction_start_time,
                    web_link=f"https://test.salesforce.com/{interaction_ids[1]}",
                ),
                CRMNote(
                    crm_id=interaction_ids[2],
                    crm_system="salesforce",
                    content="Test meeting notes 2\nTest next steps 2",
                    created_at=interaction_start_time,
                    web_link=f"https://test.salesforce.com/{interaction_ids[2]}",
                ),
            ],
        )

    @mock_salesforce
    def test_fetch_notes_for_client_invalid_date(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)

        interaction = self.simple_salesforce.Interaction.create(  # type: ignore[operator]
            {"Name": "Test Interaction", "AccountId": account_id, "StartTime": "invalid"}
        )
        self.simple_salesforce.InteractionSummary.create(  # type: ignore[operator]
            {
                "Name": "Test Summary",
                "InteractionId": interaction["id"],
                "AccountId": account_id,
                "MeetingNotes": "Test meeting notes",
                "NextSteps": "Test next steps",
            }
        )

        self.assertEqual(
            self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365)),
            [
                CRMNote(
                    crm_id=interaction["id"],
                    crm_system="salesforce",
                    content="Test meeting notes\nTest next steps",
                    created_at=None,
                    web_link=f"https://test.salesforce.com/{interaction['id']}",
                )
            ],
        )

    @mock_salesforce
    def test_main_method_combines_all_helpers(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)

        def mock_query_all(*args, **kwargs):  # type: ignore[no-untyped-def]
            query = args[0] if args else ""

            if "Interaction " in query and "AccountId" in query:
                return {
                    "records": [
                        {
                            "Id": "00I000000000001",
                            "Name": "Test Interaction",
                            "AccountId": account_id,
                            "StartTime": "2024-01-15T10:00:00.000Z",
                        }
                    ]
                }
            elif "InteractionSummary" in query:
                return {
                    "records": [
                        {
                            "Id": "00S000000000001",
                            "Name": "Test Summary",
                            "InteractionId": "00I000000000001",
                            "AccountId": account_id,
                            "MeetingNotes": "Test meeting notes",
                            "NextSteps": "Test next steps",
                        }
                    ]
                }
            elif "Event" in query:
                return {
                    "records": [
                        {
                            "Id": "00U000000000001",
                            "Subject": "Test Event",
                            "ActivityDateTime": "2024-01-15T09:00:00.000Z",
                            "Description": "Event description",
                        }
                    ]
                }
            elif "Task" in query and "TaskSubtype = 'Call'" in query:
                return {
                    "records": [
                        {
                            "Id": "00T000000000001",
                            "Subject": "Test Call",
                            "ActivityDate": "2024-01-15",
                            "Description": "Call description",
                            "TaskSubtype": "Call",
                        }
                    ]
                }
            return {"records": []}

        original_query = self.simple_salesforce.query_all
        self.simple_salesforce.query_all = mock_query_all  # type: ignore[method-assign]

        try:
            notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))

            self.assertEqual(len(notes), 3)

            crm_ids = [note.crm_id for note in notes]
            self.assertIn("00I000000000001", crm_ids)  # Interaction
            self.assertIn("00U000000000001", crm_ids)  # Event
            self.assertIn("00T000000000001", crm_ids)  # Call Task

            # Verify the content of each note type
            interaction_note = next(note for note in notes if note.crm_id == "00I000000000001")
            self.assertEqual(interaction_note.content, "Test meeting notes\nTest next steps")

            event_note = next(note for note in notes if note.crm_id == "00U000000000001")
            self.assertEqual(event_note.content, "Event: Test Event\n\nEvent description")

            call_task_note = next(note for note in notes if note.crm_id == "00T000000000001")
            self.assertEqual(call_task_note.content, "Call: Test Call\n\nCall description")

        finally:
            self.simple_salesforce.query_all = original_query  # type: ignore[method-assign]

    @mock_salesforce
    def test_resolve_sync_targets_with_valid_client(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        _, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        note = Note(
            note_owner=user,
            client={"uuid": str(client.uuid)},
        )

        sync_targets = self.salesforce.resolve_sync_targets(note, user)

        expected = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[CRMSyncTarget(crm_id=client.crm_id, type="", name=client.name)],
        )
        self.assertEqual(sync_targets, expected)

    @mock_salesforce
    def test_resolve_sync_targets_with_client_no_crm_id(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        client = Client.objects.create(name="Test Client", organization=org)

        note = Note(
            note_owner=user,
            client={"uuid": str(client.uuid)},
        )

        sync_targets = self.salesforce.resolve_sync_targets(note, user)

        expected = CRMSyncTargets(
            status=CRMSyncTargets.Status.CLIENTS_REQUIRED,
            note_targets=[],
        )
        self.assertEqual(sync_targets, expected)

    @mock_salesforce
    def test_resolve_sync_targets_with_nonexistent_client(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)

        note = Note(
            note_owner=user,
            client={"uuid": str(uuid4())},
        )

        sync_targets = self.salesforce.resolve_sync_targets(note, user)

        expected = CRMSyncTargets(
            status=CRMSyncTargets.Status.CLIENTS_REQUIRED,
            note_targets=[],
        )
        self.assertEqual(sync_targets, expected)

    @mock_salesforce
    def test_resolve_sync_targets_with_no_client(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)

        note = Note(
            note_owner=user,
            client=None,
        )

        sync_targets = self.salesforce.resolve_sync_targets(note, user)

        expected = CRMSyncTargets(
            status=CRMSyncTargets.Status.CLIENTS_REQUIRED,
            note_targets=[],
        )
        self.assertEqual(sync_targets, expected)

    @mock_salesforce
    def test_resolve_sync_targets_with_empty_client_dict(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)

        note = Note(
            note_owner=user,
            client={},
        )

        sync_targets = self.salesforce.resolve_sync_targets(note, user)

        expected = CRMSyncTargets(
            status=CRMSyncTargets.Status.CLIENTS_REQUIRED,
            note_targets=[],
        )
        self.assertEqual(sync_targets, expected)

    @mock_salesforce
    def test_resolve_sync_targets_with_client_dict_no_uuid(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)

        note = Note(
            note_owner=user,
            client={"name": "Test Client"},
        )

        sync_targets = self.salesforce.resolve_sync_targets(note, user)

        expected = CRMSyncTargets(
            status=CRMSyncTargets.Status.CLIENTS_REQUIRED,
            note_targets=[],
        )
        self.assertEqual(sync_targets, expected)

    @mock_salesforce
    def test_resolve_sync_targets_ignores_selected_sync_targets(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        _, client = self.newAccountAndClient("Test Client", owner_email=user_email, org=org)

        note = Note(
            note_owner=user,
            client={"uuid": str(client.uuid)},
        )

        selected_sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.CLIENTS_REQUIRED,
            note_targets=[CRMSyncTarget(crm_id="different_id", type="", name="Different Client")],
        )

        sync_targets = self.salesforce.resolve_sync_targets(note, user, selected_sync_targets)

        expected = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[CRMSyncTarget(crm_id=client.crm_id, type="", name=client.name)],
        )
        self.assertEqual(sync_targets, expected)

    @mock_salesforce
    def test_list_users_no_organization(self) -> None:
        user = User.objects.create(username="testuser", email="<EMAIL>")
        user.organization = None
        user.save()
        assert self.salesforce.list_users(user) == []

    @mock_salesforce
    def test_list_users_empty_salesforce_users(self) -> None:
        org = Organization.objects.create(name="Test Org")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)
        assert self.salesforce.list_users(user) == []

    @mock_salesforce
    def test_list_users_with_matching_and_nonmatching_users(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        crm_user_one = self.simple_salesforce.User.create({"Email": "<EMAIL>", "Name": "Abc", "IsActive": True})  # type: ignore[operator]
        crm_user_two = self.simple_salesforce.User.create({"Email": "<EMAIL>", "Name": "Def", "IsActive": True})  # type: ignore[operator]
        self.simple_salesforce.User.create(
            {
                "Email": "<EMAIL>",
                "Name": "Pqr",
            }
        )  # type: ignore[operator]
        user = User.objects.create(username="user_one", email="<EMAIL>", organization=org)

        crm_users = self.salesforce.list_users(user) or []

        def expected() -> list[CRMUser]:
            return [
                CRMUser(
                    name="Def",
                    crm_id=crm_user_two["id"],
                    organization=ZeplynOrganization(uuid=str(org.uuid), name=org.name),
                    zeplyn_user=None,
                ),
                CRMUser(
                    name="Abc",
                    crm_id=crm_user_one["id"],
                    organization=ZeplynOrganization(uuid=str(org.uuid), name=org.name),
                    zeplyn_user=ZeplynUser(uuid=str(user.uuid), email=user.email, name=user.name),
                ),
            ]

        assert len(crm_users) == 2
        assert all(isinstance(u, CRMUser) for u in crm_users)
        assert crm_users == expected()

    @mock_salesforce
    def test_list_users_all_users_unmatched_zeplyn_user(self) -> None:
        org = Organization.objects.create(name="Test Org")
        user = User.objects.create(username="user", email="<EMAIL>", organization=org)

        crm_user = self.simple_salesforce.User.create(
            {"Email": "<EMAIL>", "Name": "NoMatch", "IsActive": True}
        )  # type: ignore[operator]

        def expected() -> list[CRMUser]:
            return [
                CRMUser(
                    name="NoMatch",
                    crm_id=crm_user["id"],
                    organization=ZeplynOrganization(uuid=str(org.uuid), name=org.name),
                    zeplyn_user=None,
                ),
            ]

        crm_users = self.salesforce.list_users(user) or []

        assert len(crm_users) == 1
        assert crm_users == expected()

    @mock_salesforce
    def test_list_users_missing_crm_user_email(self) -> None:
        org = Organization.objects.create(name="Test Org")
        user = User.objects.create(username="user", email="<EMAIL>", organization=org)

        crm_user = self.simple_salesforce.User.create({"Name": "NoEmailUser", "IsActive": True})  # type: ignore[operator]

        def expected() -> list[CRMUser]:
            return [
                CRMUser(
                    name="NoEmailUser",
                    crm_id=crm_user["id"],
                    organization=ZeplynOrganization(uuid=str(org.uuid), name=org.name),
                    zeplyn_user=None,
                ),
            ]

        crm_users = self.salesforce.list_users(user) or []

        assert len(crm_users) == 1
        assert crm_users == expected()

    @mock.patch("deepinsights.core.integrations.crm.salesforce.salesforce_utils.get_active_users")
    def test_list_users_missing_crm_user_id(self, mock_get_active_users: MagicMock) -> None:
        org = Organization.objects.create(name="Test Org")
        user = User.objects.create(username="user", email="<EMAIL>", organization=org)

        # Simulate Salesforce returning a user without "Id"
        mock_get_active_users.return_value = [{"Email": "<EMAIL>", "Name": "NoIdUser"}]
        crm_users = self.salesforce.list_users(user) or []

        assert crm_users == []  # It should skip this user

    @mock.patch("deepinsights.core.integrations.crm.salesforce.salesforce_utils.get_active_users")
    def test_list_users_salesforce_exception(self, mock_get_active_users: MagicMock) -> None:
        org = Organization.objects.create(name="Test Org")
        user = User.objects.create(username="user", email="<EMAIL>", organization=org)
        mock_get_active_users.side_effect = Exception()
        crm_users = self.salesforce.list_users(user) or []

        assert crm_users == []

    @mock.patch("deepinsights.core.integrations.crm.salesforce.salesforce_utils.get_active_users")
    def test_list_users_salesforce_returns_none(self, mock_get_active_users: MagicMock) -> None:
        org = Organization.objects.create(name="Test Org")
        user = User.objects.create(username="user", email="<EMAIL>", organization=org)
        mock_get_active_users.return_value = None
        crm_users = self.salesforce.list_users(user) or []

        assert crm_users == []
