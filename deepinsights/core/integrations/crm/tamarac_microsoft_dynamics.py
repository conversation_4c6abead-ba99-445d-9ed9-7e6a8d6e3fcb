import datetime
import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, ClassVar, Iterable, Mapping

import requests

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.crm import microsoft_dynamics_utils
from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.crm_models import (
    CRMAccount,
    CRMNote,
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTarget,
    CRMSyncTargets,
    CRMUser,
    CRMWorkflow,
)
from deepinsights.core.integrations.oauth.microsoft_dynamics import MicrosoftDynamicsOAuth
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User


class TamaracMicrosoftDynamics(CrmBase):
    """Microsoft Dynamics CRM integration class for Tamarac Overlay"""

    # Class constants
    API_VERSION: ClassVar[str] = "v9.2"

    def __init__(self, *, user: User) -> None:
        """Initialize the Microsoft Dynamics CRM integration."""
        resource_url = user.get_crm_configuration().dynamics.dynamics_resource_url
        self.oauth = MicrosoftDynamicsOAuth(resource_url)
        self.user = user

    def _get_household_account_type_ids(self, api_url: str, headers: dict[str, str]) -> list[str]:
        """
        Fetch household account type IDs dynamically from tam_accounttypes lookup table.

        Args:
            api_url: The Dynamics API base URL
            headers: Request headers

        Returns:
            List of household account type IDs
        """
        try:
            account_types_url = f"{api_url}tam_accounttypes"

            # Query for active household account types
            params = {
                "$select": "tam_accounttypeid,tam_name",
                "$filter": "statecode eq 0 and (contains(tam_name, 'Household') or contains(tam_name, 'household'))",
            }

            response = requests.get(account_types_url, headers=headers, params=params)

            if not response.ok:
                logging.error("Error fetching account types: %s, %s", response.status_code, response.text)
                return []

            account_types_data = response.json().get("value", [])
            household_type_ids = [
                account_type.get("tam_accounttypeid")
                for account_type in account_types_data
                if account_type.get("tam_accounttypeid")
            ]

            logging.info(
                "Found %d household account types: %s",
                len(household_type_ids),
                [f"{at.get('tam_name')} ({at.get('tam_accounttypeid')})" for at in account_types_data],
            )

            return household_type_ids

        except Exception as e:
            logging.error("Error fetching household account type IDs: %s", e, exc_info=True)
            return []

    def get_accounts_by_owner_email_and_name(
        self, owner_email: str, account_name_filter: str | None = None
    ) -> list[CRMAccount]:
        """
        Fetch household accounts from Tamarac (Microsoft Dynamics overlay)
        This method fetches only household-type accounts that are active.
        Args:
            owner_email: Email of the account owner
            account_name_filter: Optional filter for account name
        Returns:
            List of CRMAccount objects
        """
        try:
            user = User.objects.get(email=owner_email)
            api_url, access_token = microsoft_dynamics_utils.get_api_url(user, self.oauth)

            headers = microsoft_dynamics_utils.get_headers(access_token)

            # Dynamically fetch household account type IDs
            household_type_ids = self._get_household_account_type_ids(api_url, headers)

            if not household_type_ids:
                logging.warning("No household account types found, returning empty list")
                return []

            entities = []

            # Fetch household accounts using dynamically retrieved account type IDs
            accounts_url = f"{api_url}accounts"

            # Build filter conditions for household accounts
            if len(household_type_ids) == 1:
                account_type_filter = f"_tam_accounttypeid_value eq '{household_type_ids[0]}'"
            else:
                type_conditions = [f"_tam_accounttypeid_value eq '{type_id}'" for type_id in household_type_ids]
                account_type_filter = f"({' or '.join(type_conditions)})"

            filter_conditions = [
                account_type_filter,  # Household account types
                "statecode eq 0",  # Only active accounts
            ]

            # Add account name filter if provided
            if account_name_filter:
                filter_conditions.append(f"contains(name, '{account_name_filter}')")

            params = {
                "$select": "name,accountid,telephone1,emailaddress1,tam_uploadid,_tam_accounttypeid_value,statecode",
                "$filter": " and ".join(filter_conditions),
            }

            response = requests.get(accounts_url, headers=headers, params=params)

            if not response.ok:
                logging.error("Error fetching household accounts: %s, %s", response.status_code, response.text)
            else:
                accounts_data = response.json().get("value", [])
                logging.info("Found %d household accounts for user %s", len(accounts_data), owner_email)

                for account in accounts_data:
                    try:
                        entities.append(
                            CRMAccount(
                                email=account.get("emailaddress1"),
                                phone_number=account.get("telephone1", None),
                                crm_id=account.get("accountid"),
                                client_type="Account",
                                crm_system="microsoft_dynamics",
                                name=account.get("name", "Unknown Account"),
                            )
                        )
                    except Exception as e:
                        logging.error("Error creating CRMAccount object for household account: %s", account, exc_info=e)

                logging.info("Successfully retrieved %d household accounts", len(entities))
            return entities

        except Exception as e:
            logging.error("Error getting Tamarac household accounts: %s", e, exc_info=True)
            return []

    def _fetch_open_appointments(self, user: User, client_crm_id: str) -> list[dict[str, Any]]:
        api_url, access_token = microsoft_dynamics_utils.get_api_url(user, self.oauth)
        headers = microsoft_dynamics_utils.get_headers(access_token)

        appointments_url = f"{api_url}appointments"

        filter_query = (
            "("
            # The client account is the primary regarding object
            f"regardingobjectid_account/accountid eq '{client_crm_id}' "
            "or "
            # The client account is one of the parties (invitees) in the appointment
            f"appointment_activity_parties/any(o:o/partyid_account/accountid eq '{client_crm_id}')"
            ") "
            "and "
            # The meting is "open"
            # Relevant values: https://gist.github.com/alikrc/2871a31b5fc237b730a45998e5eb5cb3
            "(statecode eq 0 or statecode eq 3)"
        )
        params = {
            "$filter": filter_query,
            "$select": "activityid,subject,scheduledstart,scheduledend",
            "$orderby": "scheduledstart desc",
        }

        response = requests.get(appointments_url, headers=headers, params=params)
        if not response.ok:
            logging.error("Error fetching open appointments: %s, %s", response.status_code, response.text)
            raise Exception(
                f"Failed to fetch open appointments for client {client_crm_id}: {response.status_code} - {response.text}"
            )

        return response.json().get("value", [])  # type: ignore[no-any-return]

    def resolve_sync_targets(
        self, note: Note, user: User, selected_sync_targets: CRMSyncTargets | None = None
    ) -> CRMSyncTargets:
        # Short-circuit the selection process if we already have selected valid sync targets.
        if selected_sync_targets:
            if (
                selected_sync_targets.note_targets
                and len(selected_sync_targets.note_targets) == 1
                and (
                    selected_sync_targets.note_targets[0].type == "appointment"
                    or selected_sync_targets.note_targets[0].type == "phonecall"
                )
            ):
                return CRMSyncTargets(
                    status=CRMSyncTargets.Status.FINAL,
                    note_targets=selected_sync_targets.note_targets,
                )
            logging.warning(
                "Selected sync targets are not valid for note %s: %s. Getting new sync targets.",
                note.uuid,
                selected_sync_targets.note_targets,
            )

        # Ensure that we have a client for the note.
        if not note.client or not (client_uuid := note.client.get("uuid")):
            return CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])

        try:
            client = Client.objects.get(uuid=client_uuid)
        except Client.DoesNotExist:
            return CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])
        if not client.crm_id:
            return CRMSyncTargets(status=CRMSyncTargets.Status.CLIENTS_REQUIRED, note_targets=[])

        # Fetch open appointments related to the client
        client_target = CRMSyncTarget(
            crm_id=client.crm_id,
            type="account",
            name=client.name or "Unknown Client",
        )
        appointment_targets: list[CRMSyncTarget] = []
        try:
            for appointment in self._fetch_open_appointments(user, client.crm_id):
                name = appointment.get("subject", "<No Subject>")
                if start_time := appointment.get("scheduledstart"):
                    try:
                        name += f" ({datetime.datetime.fromisoformat(start_time).date()})"
                    except ValueError:
                        logging.error(
                            "Error parsing scheduled start time for appointment %s", appointment.get("activityid")
                        )
                appointment_targets.append(
                    CRMSyncTarget(
                        crm_id=appointment.get("activityid"),
                        type="appointment",
                        name=name,
                        parent=client_target,
                    )
                )
        except Exception as e:
            logging.error("Error fetching open appointments for client %s: %s", client.crm_id, e, exc_info=True)

        return CRMSyncTargets(
            status=CRMSyncTargets.Status.RESOLUTION_REQUIRED,
            note_targets=appointment_targets
            + [
                CRMSyncTarget(
                    crm_id="new_appointment", type="appointment", name="New appointment", parent=client_target
                ),
                CRMSyncTarget(crm_id="new_phone_call", type="phonecall", name="New phone call", parent=client_target),
            ],
        )

    def add_interaction_with_client(
        self,
        note: Note,
        sync_targets: CRMSyncTargets,
        sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = None,
    ) -> None:
        """
        Add a note and associated tasks to Microsoft Dynamics as activities.
        Maps different meeting types to appropriate Dynamics activities:
        - Client meetings -> Appointment activities
        - Debriefs -> Phone Call activities
        - Tasks -> Task activities (consolidated into one follow-up task)

        Args:
            note: The note to add as an interaction
            sync_targets: The CRMSyncTargets object containing the resolved sync targets

        Raises:
            Exception: If the note cannot be added
        """
        try:
            if not note.note_owner:
                raise Exception("Note owner not found")

            api_url, access_token = microsoft_dynamics_utils.get_api_url(note.note_owner, self.oauth)

            # If we already have an interaction ID, log and return
            if note.metadata and note.metadata.get("interactionId"):
                logging.info("Note %s already has an interaction ID: %s", note.uuid, note.metadata["interactionId"])
                return

            # Validate the sync targets info.
            if not sync_targets.status == CRMSyncTargets.Status.FINAL:
                raise Exception("Appointment selection for sync is not finalized.")
            if not sync_targets.note_targets:
                raise Exception("No appointments (new or preexisting) provided for sync")
            if len(sync_targets.note_targets) > 1:
                raise Exception("Multiple appointments provided for sync, expected only one")

            # Use the first sync target as the primary sync entity.
            primary_sync_target = sync_targets.note_targets[0]
            if not primary_sync_target.parent:
                raise Exception("Appointment does not have an associated client")

            if scheduled_event := note.scheduled_event:
                meeting_start_time = scheduled_event.start_time
                meeting_end_time = scheduled_event.end_time
            else:
                # Fallback to creation time if no scheduled event exists
                meeting_start_time = note.created  # Assuming created time is the start time
                meeting_end_time = meeting_start_time + timedelta(hours=1)

            activity_type = primary_sync_target.type
            activity_id = None
            activity_timestamp: datetime.datetime | None = meeting_start_time
            if primary_sync_target.crm_id == "new_phone_call":
                activity_type = "phonecall"
                activity_entity = "phonecalls"
            elif primary_sync_target.crm_id == "new_appointment":
                activity_type = "appointment"
                activity_entity = "appointments"
            else:
                activity_id = primary_sync_target.crm_id
                activity_entity = "appointments" if activity_type == "appointment" else "phonecalls"

            client_type = (primary_sync_target.parent.type or "account").lower()
            client_info = {"id": primary_sync_target.parent.crm_id, "type": client_type}

            headers = microsoft_dynamics_utils.get_headers(access_token)

            # Create a new activity if needed.
            if not activity_id:
                activity_url = f"{api_url}{activity_entity}"
                activity_data = {
                    "subject": note.title(),
                    "description": note.get_summary_for_crm(use_html_formatting=False, sync_items=sync_items),
                    "scheduledstart": meeting_start_time.isoformat(),
                    "scheduledend": meeting_end_time.isoformat(),
                    "statecode": 1,
                    f"regardingobjectid_{client_info['type']}@odata.bind": f"/{client_info['type']}s({client_info['id']})",
                }

                response = requests.post(activity_url, headers=headers, json=activity_data)

                if response.ok:
                    # Extract activity ID from response headers
                    if "OData-EntityId" in response.headers:
                        entity_id = response.headers["OData-EntityId"]
                        activity_id = entity_id.split("(")[1].split(")")[0]
                    else:
                        raise Exception(f"Failed to create new {activity_type}: could not get created activity ID")
                else:
                    logging.error(
                        "Error creating %s in Dynamics: %s, %s", activity_type, response.status_code, response.text
                    )
                    raise Exception(f"Failed to create new {activity_type} in Dynamics")
            else:
                # Fetch the existing activity's description and append our notes content.
                activity_url = f"{api_url}{activity_entity}({activity_id})"
                params = {
                    "$select": "description,scheduledstart",
                }
                response = requests.get(activity_url, headers=headers, params=params)
                if not response.ok:
                    logging.error(
                        "Error fetching existing %s with ID %s: %s, %s",
                        activity_type,
                        activity_id,
                        response.status_code,
                        response.text,
                    )
                    raise Exception(f"Failed to fetch existing {activity_type} in Dynamics")
                data = response.json()
                description = data.get("description", "")
                updated_description = (
                    f"{description}\n\n{note.get_summary_for_crm(use_html_formatting=False, sync_items=sync_items)}"
                )

                # Update the existing activity with the new description.
                update_data = {
                    "description": updated_description,
                }
                if not requests.patch(activity_url, headers=headers, json=update_data).ok:
                    logging.error(
                        "Error updating existing %s with ID %s: %s, %s",
                        activity_type,
                        activity_id,
                        response.status_code,
                        response.text,
                    )
                    raise Exception(f"Failed to update existing {activity_type} in Dynamics")

                # Set the activity timestamp to the scheduled start time (if it's valid).
                scheduledstart = data.get("scheduledstart", "")
                try:
                    activity_timestamp = datetime.datetime.fromisoformat(scheduledstart) if scheduledstart else None
                except ValueError:
                    logging.error(
                        "Invalid scheduled start time format for %s with ID %s: %s.",
                        activity_type,
                        activity_id,
                        scheduledstart,
                    )
                    activity_timestamp = None

            # Link the appointment back to the note.
            if activity_id:
                if not note.metadata:
                    note.metadata = {}
                note.metadata["interactionId"] = activity_id
                note.save()
                logging.info(
                    "Successfully updated %s in Dynamics with ID %s", activity_type, primary_sync_target.crm_id
                )

            # Update the last appointment or call time for the client (if we have a valid activity
            # timestamp).
            if activity_timestamp:
                account_url = f"{api_url}accounts({client_info['id']})"
                update_field = "tam_lastappointment" if activity_type == "appointment" else "tam_lastcall"
                try:
                    response = requests.patch(
                        account_url, headers=headers, json={update_field: activity_timestamp.date().isoformat()}
                    )
                except Exception as e:
                    logging.error(
                        "Error updating last %s time for client %s: %s. Continuing with task creation.",
                        activity_type,
                        client_info["id"],
                        e,
                        exc_info=True,
                    )
                if not response.ok:
                    logging.error(
                        "Error updating last %s time for client %s: %s, %s. Continuing with task creation.",
                        activity_type,
                        client_info["id"],
                        response.status_code,
                        response.text,
                    )

            # Handle tasks - consolidate into a single follow-up task
            if note.should_include_section(sync_items, CRMSyncSection.TASKS):
                logging.info("Creating consolidated follow-up task for note %s", note.uuid)
                tasks_to_consolidate = []
                for task in note.get_tasks_to_include_crm_sync(sync_items=sync_items):
                    tasks_to_consolidate.append(task)

                if tasks_to_consolidate:
                    self._create_consolidated_follow_up_task(api_url, headers, note, tasks_to_consolidate, client_info)

        except Exception as e:
            logging.error("Error adding interaction with client for note %s: %s", note.uuid, e, exc_info=True)
            raise

    def _create_consolidated_follow_up_task(
        self, api_url: str, headers: dict[str, str], note: Note, tasks: Iterable[Task], client: dict[str, Any]
    ) -> None:
        """
        Create a consolidated follow-up task in Dynamics that contains all individual tasks.

        Args:
            api_url: The API URL
            headers: Request headers
            note: The original note
            tasks: List of tasks to consolidate
            client: Client dictionary with id and type
        """
        try:
            # Create consolidated task subject
            meeting_name = note.title()
            consolidated_subject = f"{meeting_name} - Follow Up"

            # Build consolidated description with all tasks
            task_descriptions = []
            earliest_due_date = None

            for i, task in enumerate(tasks, 1):
                task_desc = f"{i}. {task.task_title}"
                if task.task_desc:
                    task_desc += f": {task.task_desc}"
                if task.assignee:
                    task_desc += f" (Assigned to: {task.assignee.email})"
                if task.due_date:
                    task_desc += f" (Due: {task.due_date.strftime('%Y-%m-%d')})"
                    if not earliest_due_date or task.due_date < earliest_due_date:
                        earliest_due_date = task.due_date

                task_descriptions.append(task_desc)

            consolidated_description = f"Follow-up tasks from meeting: {meeting_name}\n\n" + "\n".join(
                task_descriptions
            )

            # Use earliest due date or default to 7 days from now
            due_date = earliest_due_date or datetime.datetime.now(datetime.timezone.utc) + timedelta(days=7)

            # Prepare consolidated task data
            consolidated_task_data = {
                "subject": consolidated_subject,
                "description": consolidated_description,
                "scheduledend": due_date.isoformat(),
                "statecode": 0,  # Open
                "prioritycode": 1,  # Normal priority
                f"regardingobjectid_{client['type']}@odata.bind": f"/{client['type']}s({client['id']})",
            }

            # Create the consolidated task
            tasks_url = f"{api_url}tasks"
            response = requests.post(tasks_url, headers=headers, json=consolidated_task_data)

            if response.ok:
                # Extract task ID from response headers
                consolidated_task_id = None
                if "OData-EntityId" in response.headers:
                    entity_id = response.headers["OData-EntityId"]
                    consolidated_task_id = entity_id.split("(")[1].split(")")[0]

                if consolidated_task_id:
                    # Update all original tasks with the consolidated task ID
                    for task in tasks:
                        if not task.metadata:
                            task.metadata = {}
                        task.metadata["consolidatedTaskId"] = consolidated_task_id
                        task.save()

                    logging.info(
                        "Successfully created consolidated follow-up task in Dynamics with ID %s", consolidated_task_id
                    )
                    logging.info("Consolidated %d individual tasks into single follow-up task", len(list(tasks)))
                else:
                    logging.warning("Consolidated task created but couldn't extract ID from response headers")
            else:
                logging.error(
                    "Error creating consolidated task in Dynamics: %s, %s", response.status_code, response.text
                )

        except Exception as e:
            logging.error("Error creating consolidated follow-up task: %s", e, exc_info=True)
            raise

    def fetch_notes_for_client(self, user: User, client: Client, lookback_interval: timedelta) -> list[CRMNote]:
        """
        Fetch notes for a client from Tamarac (Microsoft Dynamics overlay).
        Notes are stored as activities (appointments or phone calls) rather than annotations.

        Args:
            user: The user requesting the notes
            client: The client to fetch notes for
            lookback_interval: How far back to look for notes

        Returns:
            List of CRMNote objects
        """
        try:
            if not client.crm_id:
                logging.error("Client %s does not have a CRM ID", client.uuid)
                return []

            api_url, access_token = microsoft_dynamics_utils.get_api_url(user, self.oauth)
            headers = microsoft_dynamics_utils.get_headers(access_token)

            not_before_date = datetime.datetime.now(datetime.timezone.utc) - lookback_interval
            formatted_date = not_before_date.strftime("%Y-%m-%dT%H:%M:%SZ")

            activity_types = [
                {"endpoint": "appointments", "label": "Appointment", "entity": "appointment"},
                {"endpoint": "phonecalls", "label": "Phone Call", "entity": "phonecall"},
            ]

            all_notes = []
            for activity_config in activity_types:
                notes = self._fetch_activities_for_client(
                    api_url=api_url,
                    headers=headers,
                    client_crm_id=client.crm_id,
                    formatted_date=formatted_date,
                    user=user,
                    **activity_config,
                )
                all_notes.extend(notes)

            # Sort all notes by creation date (newest first)
            all_notes.sort(
                key=lambda x: x.created_at or datetime.datetime.min.replace(tzinfo=datetime.timezone.utc), reverse=True
            )

            logging.info(
                "Successfully fetched and processed %d total notes for client %s from Tamarac Dynamics",
                len(all_notes),
                client.uuid,
            )
            return all_notes

        except Exception as e:
            logging.error("Error fetching notes from Tamarac Dynamics for client %s: %s", client.uuid, e, exc_info=True)
            return []

    def _fetch_activities_for_client(
        self,
        api_url: str,
        headers: dict[str, str],
        client_crm_id: str,
        formatted_date: str,
        user: User,
        endpoint: str,
        label: str,
        entity: str,
    ) -> list[CRMNote]:
        """
        Helper method to fetch activities of a specific type for a client.

        Args:
            api_url: The Dynamics API base URL
            headers: Request headers
            client_crm_id: The CRM ID of the client
            formatted_date: Formatted date string for filtering
            user: The user making the request
            endpoint: The API endpoint (e.g., "appointments", "phonecalls")
            label: Display label for the activity type (e.g., "Appointment", "Phone Call")
            entity: Entity type name for web links (e.g., "appointment", "phonecall")

        Returns:
            List of CRMNote objects for this activity type
        """
        try:
            activities_url = f"{api_url}{endpoint}"
            filter_query = f"_regardingobjectid_value eq '{client_crm_id}' and createdon ge {formatted_date}"

            params = {
                "$filter": filter_query,
                "$orderby": "createdon desc",
                "$select": "activityid,subject,description,createdon,scheduledstart,scheduledend",
            }

            response = requests.get(activities_url, headers=headers, params=params)

            if not response.ok:
                logging.error(
                    "Error fetching %s from Tamarac Dynamics: %s, %s", endpoint, response.status_code, response.text
                )
                return []

            activities_data = response.json().get("value", [])
            logging.info("Found %d %s for client", len(activities_data), endpoint)

            notes = []
            dynamics_url = user.get_crm_configuration().dynamics.dynamics_resource_url
            if dynamics_url.endswith("/"):
                dynamics_url = dynamics_url[:-1]

            for activity in activities_data:
                try:
                    activity_id = activity.get("activityid")
                    web_link = f"{dynamics_url}/main.aspx?pagetype=entityrecord&etn={entity}&id={activity_id}"

                    created_at = None
                    if created_date_str := activity.get("createdon"):
                        try:
                            created_at = datetime.datetime.fromisoformat(created_date_str.replace("Z", "+00:00"))
                        except Exception as e:
                            logging.error("Error parsing date for %s %s: %s", entity, activity_id, e)

                    subject = activity.get("subject", "")
                    description = activity.get("description", "")
                    content = f"[{label}] {subject}\n{description}" if subject else f"[{label}] {description}"

                    notes.append(
                        CRMNote(
                            crm_id=activity_id,
                            crm_system="microsoft_dynamics",
                            content=content,
                            created_at=created_at,
                            web_link=web_link,
                        )
                    )
                except Exception as e:
                    logging.error("Error creating CRMNote object for %s %s: %s", entity, activity.get("activityid"), e)
                    continue

            return notes

        except Exception as e:
            logging.error("Error fetching %s activities: %s", endpoint, e, exc_info=True)
            return []

    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        logging.warning("fetch_events not implemented for TamaracMicrosoftDynamics")
        return {}

    def fetch_events(self, user: User, interval: timedelta) -> list[CalendarEvent]:
        logging.warning("fetch_events not implemented for TamaracMicrosoftDynamics")
        return []

    def fetch_workflows_for_user(self, user: User) -> list[CRMWorkflow]:
        logging.warning("fetch_workflows_for_user not implemented for TamaracMicrosoftDynamics")
        return []

    def list_users(self, requesting_user: User) -> list[CRMUser]:
        if not requesting_user.organization:
            logging.error("User does not belong to an organization. Cannot list Microsoft Dynamics CRM users.")
            return []

        return microsoft_dynamics_utils.list_crm_users(self.oauth, requesting_user)
