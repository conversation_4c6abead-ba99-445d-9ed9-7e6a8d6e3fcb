import datetime
import logging
from unittest import mock
from unittest.mock import MagicMock, patch

from django.test import TestCase

from deepinsights.core.integrations.crm.crm_models import (
    CRMSyncItemSelection,
    CRMSyncSection,
    CRMSyncTarget,
    CRMSyncTargets,
)
from deepinsights.core.integrations.crm.sharepoint import SharePoint
from deepinsights.core.preferences.preferences import SharePointConfiguration
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


class TestSharePoint(TestCase):
    def setUp(self) -> None:
        self.user = User.objects.create(email="<EMAIL>", name="Test User")

        self.note = Note.objects.create(note_owner=self.user, metadata={"meeting_name": "Test Meeting"})

        self.note.created = datetime.datetime(2023, 1, 15, 10, 30, 0)
        self.note.save()

        self.title_patcher = patch.object(Note, "title", return_value="Test Note Title")
        self.summary_patcher = patch.object(Note, "get_summary_for_crm", return_value="Test Note Summary")
        self.attendees_patcher = patch.object(Note, "get_attendees", return_value=["Client 1", "Client 2"])

        self.mock_title = self.title_patcher.start()
        self.mock_summary = self.summary_patcher.start()
        self.mock_attendees = self.attendees_patcher.start()

        self.config = SharePointConfiguration(
            client_id="test_client_id",
            client_secret="test_client_secret",
            site_url="https://test.sharepoint.com/sites/test",
            parent_folder="test_folder",
        )

        self.client_context_patcher = patch("deepinsights.core.integrations.crm.sharepoint.ClientContext")
        self.client_credential_patcher = patch("deepinsights.core.integrations.crm.sharepoint.ClientCredential")

        self.mock_client_context = self.client_context_patcher.start()
        self.mock_client_credential = self.client_credential_patcher.start()

        self.mock_context_instance = MagicMock()
        self.mock_web = MagicMock()
        self.mock_context_instance.web = self.mock_web
        self.mock_web.properties = {"Title": "Test Site"}
        self.mock_client_context.return_value.with_credentials.return_value = self.mock_context_instance

        self.sharepoint = SharePoint(self.config)

    def tearDown(self) -> None:
        self.title_patcher.stop()
        self.summary_patcher.stop()
        self.attendees_patcher.stop()

        self.client_context_patcher.stop()
        self.client_credential_patcher.stop()

    def test_init_with_valid_config(self) -> None:
        self.assertEqual(self.sharepoint.client_id, "test_client_id")
        self.assertEqual(self.sharepoint.client_secret, "test_client_secret")
        self.assertEqual(self.sharepoint.site_url, "https://test.sharepoint.com/sites/test")
        self.assertEqual(self.sharepoint.sharepoint_parent_folder, "test_folder")
        self.assertEqual(self.sharepoint.client_context, self.mock_context_instance)

    def test_init_with_invalid_config(self) -> None:
        invalid_config = SharePointConfiguration(
            client_id="",
            client_secret="test_secret",
            site_url="https://test.sharepoint.com",
            parent_folder="test_folder",
        )

        with self.assertRaises(ValueError):
            SharePoint(invalid_config)

    def test_authenticate(self) -> None:
        self.mock_client_credential.reset_mock()
        self.mock_client_context.reset_mock()
        self.mock_context_instance.reset_mock()

        self.sharepoint.client_context = None
        self.sharepoint.authenticate()

        self.mock_client_credential.assert_called_once_with("test_client_id", "test_client_secret")
        self.mock_client_context.assert_called_once_with("https://test.sharepoint.com/sites/test")
        self.mock_client_context.return_value.with_credentials.assert_called_once()
        self.assertEqual(self.sharepoint.client_context, self.mock_context_instance)
        self.mock_context_instance.load.assert_called_with(self.mock_web)
        self.mock_context_instance.execute_query.assert_called()

    def test_authenticate_failure(self) -> None:
        self.mock_client_credential.reset_mock()
        self.mock_client_context.reset_mock()
        self.mock_context_instance.reset_mock()

        self.sharepoint.client_context = None
        self.mock_context_instance.execute_query.side_effect = Exception("Authentication failed")

        with self.assertRaises(Exception):
            self.sharepoint.authenticate()

        self.assertIsNone(self.sharepoint.client_context)

    def test_ensure_folder_exists_with_new_folder(self) -> None:
        self.mock_context_instance.reset_mock()
        self.mock_context_instance.web.reset_mock()
        self.mock_context_instance.web.lists.reset_mock()

        mock_doc_lib = MagicMock()
        mock_root_folder = MagicMock()
        mock_root_folder.properties = {"ServerRelativeUrl": "/sites/test/Documents"}
        mock_doc_lib.properties = {"RootFolder": mock_root_folder}

        self.mock_context_instance.web.lists.get_by_title.return_value = mock_doc_lib

        mock_parent_folder = MagicMock()
        mock_new_folder = MagicMock()
        mock_parent_folder.folders.add.return_value = mock_new_folder

        folder_lookup_responses = {
            "/sites/test/Documents/test_folder": mock_parent_folder,
            "/sites/test/Documents/test_folder/new_folder": Exception("Folder not found"),
            "/sites/test/Documents": mock_parent_folder,
        }

        def folder_side_effect(path: str):  # type: ignore[no-untyped-def]
            if path in folder_lookup_responses:
                result = folder_lookup_responses[path]
                if isinstance(result, Exception):
                    raise result
                return result
            raise ValueError(f"Unexpected path: {path}")

        self.mock_context_instance.web.get_folder_by_server_relative_url.side_effect = folder_side_effect

        self.sharepoint.ensure_folder_exists("test_folder/new_folder")

        self.mock_context_instance.web.lists.get_by_title.assert_called_with("Documents")

        load_calls = [
            call
            for call in self.mock_context_instance.load.call_args_list
            if call == mock.call(mock_doc_lib, ["RootFolder"])
        ]
        self.assertTrue(len(load_calls) > 0, "load was not called with the expected arguments")

        mock_parent_folder.folders.add.assert_called_once_with("new_folder")

    @patch("deepinsights.core.integrations.crm.sharepoint.SharePoint.ensure_folder_exists")
    def test_upload_file(self, mock_ensure_folder_exists: MagicMock) -> None:
        mock_doc_lib = MagicMock()
        mock_root_folder = MagicMock()
        mock_root_folder.properties = {"ServerRelativeUrl": "/sites/test/Documents"}
        mock_doc_lib.properties = {"RootFolder": mock_root_folder}

        self.mock_context_instance.web.lists.get_by_title.return_value = mock_doc_lib

        mock_target_folder = MagicMock()
        mock_target_file = MagicMock()
        self.mock_context_instance.web.get_folder_by_server_relative_url.return_value = mock_target_folder
        mock_target_folder.upload_file.return_value = mock_target_file

        result = self.sharepoint.upload_file("test_folder", "test_file.txt", "Test content")

        mock_ensure_folder_exists.assert_called_once_with("test_folder")
        self.mock_context_instance.web.lists.get_by_title.assert_called_once_with("Documents")
        self.mock_context_instance.web.get_folder_by_server_relative_url.assert_called_once()
        mock_target_folder.upload_file.assert_called_once_with("test_file.txt", "Test content".encode("utf-8"))
        self.assertTrue(result)

    @patch("deepinsights.core.integrations.crm.sharepoint.SharePoint.upload_file")
    def test_add_interaction_with_client(self, mock_upload_file: MagicMock) -> None:
        mock_upload_file.return_value = True

        sync_targets = self.sharepoint.resolve_sync_targets(self.note, self.user)
        self.sharepoint.add_interaction_with_client(self.note, sync_targets)

        expected_folder_path = f"test_folder/{self.note.note_owner.name}" if self.note.note_owner else ""
        expected_filename = f"{self.note.created.strftime('%Y-%m-%d-%H-%M')}_Test Meeting.txt"
        expected_content = "Title: Test Note Title\n\nSummary: Test Note Summary\n\nClients: Client 1, Client 2"

        mock_upload_file.assert_called_once_with(expected_folder_path, expected_filename, expected_content)

    @patch("deepinsights.core.integrations.crm.sharepoint.SharePoint.upload_file")
    def test_add_interaction_with_client_failure(self, mock_upload_file: MagicMock) -> None:
        mock_upload_file.return_value = False

        with self.assertRaises(Exception):
            sync_targets = self.sharepoint.resolve_sync_targets(self.note, self.user)
            self.sharepoint.add_interaction_with_client(self.note, sync_targets)

    @patch("deepinsights.core.integrations.crm.sharepoint.SharePoint.upload_file")
    def test_add_interaction_sync_items_none_backward_compatibility(self, mock_upload_file: MagicMock) -> None:
        mock_upload_file.return_value = True

        with patch.object(self.note, "get_summary_for_crm", return_value="test_note_summary") as mock_get_summary:
            sync_targets = self.sharepoint.resolve_sync_targets(self.note, self.user)
            self.sharepoint.add_interaction_with_client(self.note, sync_targets, None)

            mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items=None)

        mock_upload_file.assert_called_once()

    @patch("deepinsights.core.integrations.crm.sharepoint.SharePoint.upload_file")
    def test_add_interaction_sync_items_empty_dict_backward_compatibility(self, mock_upload_file: MagicMock) -> None:
        mock_upload_file.return_value = True

        with patch.object(self.note, "get_summary_for_crm", return_value="test_note_summary") as mock_get_summary:
            sync_targets = self.sharepoint.resolve_sync_targets(self.note, self.user)
            self.sharepoint.add_interaction_with_client(self.note, sync_targets, {})

            mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items={})

        mock_upload_file.assert_called_once()

    @patch("deepinsights.core.integrations.crm.sharepoint.SharePoint.upload_file")
    def test_add_interaction_sync_items_summary_enabled_boolean(self, mock_upload_file: MagicMock) -> None:
        mock_upload_file.return_value = True

        sync_items = {CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True)}

        with patch.object(self.note, "get_summary_for_crm", return_value="test_note_summary") as mock_get_summary:
            sync_targets = self.sharepoint.resolve_sync_targets(self.note, self.user)
            self.sharepoint.add_interaction_with_client(self.note, sync_targets, sync_items)

            mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items=sync_items)

        mock_upload_file.assert_called_once()

        upload_call_args = mock_upload_file.call_args
        file_content = upload_call_args[0][2]  # Third argument is the content
        self.assertIn("test_note_summary", file_content)

    @patch("deepinsights.core.integrations.crm.sharepoint.SharePoint.upload_file")
    def test_add_interaction_sync_items_summary_disabled_boolean(self, mock_upload_file: MagicMock) -> None:
        mock_upload_file.return_value = True

        sync_items = {CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=False)}

        with patch.object(
            self.note, "get_summary_for_crm", return_value="filtered_summary_content"
        ) as mock_get_summary:
            sync_targets = self.sharepoint.resolve_sync_targets(self.note, self.user)
            self.sharepoint.add_interaction_with_client(self.note, sync_targets, sync_items)

            mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items=sync_items)

        mock_upload_file.assert_called_once()

        upload_call_args = mock_upload_file.call_args
        file_content = upload_call_args[0][2]  # Third argument is the content
        self.assertIn("filtered_summary_content", file_content)

    @patch("deepinsights.core.integrations.crm.sharepoint.SharePoint.upload_file")
    def test_add_interaction_sync_items_mixed_boolean_sections(self, mock_upload_file: MagicMock) -> None:
        mock_upload_file.return_value = True

        sync_items = {
            CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.ATTENDEES: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.KEYWORDS: CRMSyncItemSelection(include_section=False),
            CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=False),
        }

        with patch.object(self.note, "get_summary_for_crm", return_value="mixed_sections_summary") as mock_get_summary:
            sync_targets = self.sharepoint.resolve_sync_targets(self.note, self.user)
            self.sharepoint.add_interaction_with_client(self.note, sync_targets, sync_items)

            mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items=sync_items)

        mock_upload_file.assert_called_once()

    @patch("deepinsights.core.integrations.crm.sharepoint.SharePoint.upload_file")
    def test_add_interaction_sync_items_preserves_existing_error_handling(self, mock_upload_file: MagicMock) -> None:
        mock_upload_file.return_value = False  # Simulate upload failure

        sync_items = {CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True)}

        with patch.object(self.note, "get_summary_for_crm", return_value="test_note_summary"):
            sync_targets = self.sharepoint.resolve_sync_targets(self.note, self.user)

            with self.assertLogs(level=logging.ERROR) as cm:
                with self.assertRaisesRegex(Exception, "Failed to upload file .* to SharePoint"):
                    self.sharepoint.add_interaction_with_client(self.note, sync_targets, sync_items)

            self.assertTrue(any("Failed to upload file" in log for log in cm.output))

        mock_upload_file.assert_called_once()

    @patch("deepinsights.core.integrations.crm.sharepoint.SharePoint.upload_file")
    def test_add_interaction_sync_items_authentication_failure_handling(self, mock_upload_file: MagicMock) -> None:
        self.sharepoint.client_context = None

        with patch.object(self.sharepoint, "authenticate") as mock_authenticate:
            mock_authenticate.side_effect = Exception("Authentication failed")

            sync_items = {CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True)}

            with patch.object(self.note, "get_summary_for_crm", return_value="test_note_summary") as mock_get_summary:
                sync_targets = self.sharepoint.resolve_sync_targets(self.note, self.user)

                with self.assertLogs(level=logging.ERROR) as cm:
                    with self.assertRaisesRegex(Exception, "Authentication failed"):
                        self.sharepoint.add_interaction_with_client(self.note, sync_targets, sync_items)

                self.assertTrue(any("Error in add_interaction_with_client" in log for log in cm.output))

            mock_upload_file.assert_not_called()

    @patch("deepinsights.core.integrations.crm.sharepoint.SharePoint.upload_file")
    def test_add_interaction_sync_items_authentication_none_after_auth_failure(
        self, mock_upload_file: MagicMock
    ) -> None:
        self.sharepoint.client_context = None

        with patch.object(self.sharepoint, "authenticate") as mock_authenticate:

            def auth_side_effect() -> None:
                pass

            mock_authenticate.side_effect = auth_side_effect

            sync_items = {CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True)}

            with patch.object(self.note, "get_summary_for_crm", return_value="test_note_summary"):
                sync_targets = self.sharepoint.resolve_sync_targets(self.note, self.user)

                with self.assertLogs(level=logging.ERROR) as cm:
                    with self.assertRaisesRegex(ValueError, "Failed to authenticate with SharePoint"):
                        self.sharepoint.add_interaction_with_client(self.note, sync_targets, sync_items)

                self.assertTrue(any("Error in add_interaction_with_client" in log for log in cm.output))

            mock_upload_file.assert_not_called()

    def test_add_interaction_backward_compatibility_method_signature(self) -> None:
        sync_targets = self.sharepoint.resolve_sync_targets(self.note, self.user)

        try:
            with patch.object(self.note, "get_summary_for_crm", return_value="test_note_summary"):
                with patch.object(self.sharepoint, "upload_file", return_value=True):
                    self.sharepoint.add_interaction_with_client(self.note, sync_targets)
            self.assertTrue(True)  # Test passes if no exception is raised
        except TypeError as e:
            self.fail(f"Backward compatibility broken: {e}")

    @patch("deepinsights.core.integrations.crm.sharepoint.SharePoint.upload_file")
    def test_add_interaction_sync_items_file_content_structure(self, mock_upload_file: MagicMock) -> None:
        """Test that the file content structure is correct when using sync_items."""
        mock_upload_file.return_value = True

        sync_items = {CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True)}

        with patch.object(self.note, "get_summary_for_crm", return_value="custom_summary_content") as mock_get_summary:
            sync_targets = self.sharepoint.resolve_sync_targets(self.note, self.user)
            self.sharepoint.add_interaction_with_client(self.note, sync_targets, sync_items)

            # Verify get_summary_for_crm was called with sync_items
            mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items=sync_items)

        # Verify file upload was called and check content structure
        mock_upload_file.assert_called_once()
        upload_call_args = mock_upload_file.call_args

        # Check the file content structure
        file_content = upload_call_args[0][2]  # Third argument is the content
        self.assertIn("Title: Test Note Title", file_content)
        self.assertIn("Summary: custom_summary_content", file_content)
        self.assertIn("Clients: Client 1, Client 2", file_content)

        # Check folder path and filename
        folder_path = upload_call_args[0][0]  # First argument is the folder path
        filename = upload_call_args[0][1]  # Second argument is the filename

        expected_folder_path = f"test_folder/{self.note.note_owner.name}"  # type: ignore[union-attr]
        self.assertEqual(folder_path, expected_folder_path)
        self.assertTrue(filename.endswith("_Test Meeting.txt"))

    @patch("deepinsights.core.integrations.crm.sharepoint.SharePoint.upload_file")
    def test_add_interaction_sync_items_handles_missing_meeting_name(self, mock_upload_file: MagicMock) -> None:
        """Test that sync_items works correctly when meeting_name is missing from metadata."""
        mock_upload_file.return_value = True

        # Create a note without meeting_name in metadata
        note_without_meeting_name = Note.objects.create(
            note_owner=self.user,
            metadata={},  # No meeting_name
        )
        note_without_meeting_name.created = datetime.datetime(2023, 1, 15, 10, 30, 0)
        note_without_meeting_name.save()

        sync_items = {CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True)}

        with patch.object(note_without_meeting_name, "title", return_value="Test Title"):
            with patch.object(
                note_without_meeting_name, "get_summary_for_crm", return_value="test_summary"
            ) as mock_get_summary:
                with patch.object(note_without_meeting_name, "get_attendees", return_value=["Client 1"]):
                    sync_targets = self.sharepoint.resolve_sync_targets(note_without_meeting_name, self.user)
                    self.sharepoint.add_interaction_with_client(note_without_meeting_name, sync_targets, sync_items)

                    # Verify get_summary_for_crm was called with sync_items
                    mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items=sync_items)

        # Verify file upload was called with default meeting name
        mock_upload_file.assert_called_once()
        upload_call_args = mock_upload_file.call_args
        filename = upload_call_args[0][1]  # Second argument is the filename
        self.assertTrue(filename.endswith("_unnamed_meeting.txt"))

    @patch("deepinsights.core.integrations.crm.sharepoint.SharePoint.upload_file")
    def test_add_interaction_sync_items_uses_correct_html_formatting_parameter(
        self, mock_upload_file: MagicMock
    ) -> None:
        """Test that sync_items parameter ensures use_html_formatting=False is passed."""
        mock_upload_file.return_value = True

        sync_items = {
            CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.ATTENDEES: CRMSyncItemSelection(include_section=False),
        }

        with patch.object(self.note, "get_summary_for_crm", return_value="test_summary") as mock_get_summary:
            sync_targets = self.sharepoint.resolve_sync_targets(self.note, self.user)
            self.sharepoint.add_interaction_with_client(self.note, sync_targets, sync_items)

            # Verify get_summary_for_crm was called with use_html_formatting=False and sync_items
            mock_get_summary.assert_called_once_with(use_html_formatting=False, sync_items=sync_items)

        # Verify file upload was called
        mock_upload_file.assert_called_once()

    def test_get_accounts_by_owner_email_and_name(self) -> None:
        result = self.sharepoint.get_accounts_by_owner_email_and_name("<EMAIL>")
        self.assertEqual(result, [])

    def test_get_client_basic_info(self) -> None:
        mock_client = MagicMock(spec=Client)
        mock_user = MagicMock(spec=User)
        self.assertIsNone(self.sharepoint.get_client_basic_info(mock_client, mock_user))

    def test_fetch_events(self) -> None:
        mock_user = MagicMock(spec=User)

        result = self.sharepoint.fetch_events(mock_user, datetime.timedelta(days=7))
        self.assertEqual(result, [])

    def test_resolve_sync_targets(self) -> None:
        note = MagicMock(spec=Note)
        user = MagicMock(spec=User)

        result = self.sharepoint.resolve_sync_targets(note, user)

        self.assertEqual(result, CRMSyncTargets(status=CRMSyncTargets.Status.FINAL, note_targets=[]))

    def test_resolve_sync_targets_with_selected_targets(self) -> None:
        note = MagicMock(spec=Note)
        user = MagicMock(spec=User)

        selected_sync_targets = CRMSyncTargets(
            status=CRMSyncTargets.Status.FINAL,
            note_targets=[CRMSyncTarget(crm_id="note1", type="note", name="Note 1")],
        )

        result = self.sharepoint.resolve_sync_targets(note, user, selected_sync_targets)

        self.assertEqual(
            result,
            CRMSyncTargets(status=CRMSyncTargets.Status.FINAL, note_targets=[]),
        )
