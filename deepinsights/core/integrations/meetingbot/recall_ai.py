import copy
import datetime
import logging
from enum import Str<PERSON><PERSON>
from hashlib import sha256
from typing import Any, Li<PERSON>, Tu<PERSON>
from uuid import UUID

import aiohttp
import requests
from django.conf import settings
from django.utils import timezone
from phonenumbers import PhoneNumber
from rest_framework import status as http_status

from deepinsights.core.integrations.calendar.calendar_data_parser import should_ignore_event_by_keywords
from deepinsights.core.integrations.meetingbot.bot_controller import (
    BotController,
    BotMeetingType,
    BotResponse,
    BotStatus,
    ZoomStatus,
)
from deepinsights.core.ml import asr
from deepinsights.core.preferences.preferences import BotPreferences
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.bot_processing_task import BotProcessingTask
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent
from deepinsights.users.models.user import User


def _auth_token() -> str:
    return f"Token {settings.RECALL_API_TOKEN}"


def _log_payload(payload_copy: dict[str, Any]) -> None:
    for key in payload_copy.get("automatic_video_output", {}):
        if "b64_data" in payload_copy["automatic_video_output"][key]:
            b64_data = payload_copy["automatic_video_output"][key]["b64_data"]
            payload_copy["automatic_video_output"][key]["b64_data"] = f"{b64_data[:10]}...{b64_data[-10:]}"

    for key in payload_copy.get("automatic_audio_output", {}):
        if (
            "data" in payload_copy["automatic_audio_output"][key]
            and "b64_data" in payload_copy["automatic_audio_output"][key]["data"]
        ):
            b64_data = payload_copy["automatic_audio_output"][key]["data"]["b64_data"]
            payload_copy["automatic_audio_output"][key]["data"]["b64_data"] = f"{b64_data[:10]}...{b64_data[-10:]}"

    logging.info("Bot payload formed: %s", payload_copy)


recall_event_to_status = {
    "ready": BotStatus.SCHEDULED,
    "joining_call": BotStatus.SCHEDULED,
    "in_waiting_room": BotStatus.IN_WAITING_ROOM,
    "in_call_not_recording": BotStatus.IN_CALL_NOT_RECORDING,
    "recording_permission_allowed": BotStatus.IN_CALL_NOT_RECORDING,
    "recording_permission_denied": BotStatus.ERROR,
    "in_call_recording": BotStatus.IN_CALL_RECORDING,
    "call_ended": BotStatus.CALL_ENDED,
    "done": BotStatus.RECORDING_DONE,
    "fatal": BotStatus.ERROR,
    "analysis_done": BotStatus.RECORDING_DONE,
    "analysis_failed": BotStatus.ERROR,
    "media_expired": BotStatus.RECORDING_DONE,
}


class RecallCalendarStatus(StrEnum):
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    UNKNOWN = "unknown"

    @classmethod
    def _missing_(cls, value: object) -> "RecallCalendarStatus":
        if isinstance(value, str):
            lowercased = value.lower()
            if value != lowercased:
                return cls(lowercased)
        return cls.UNKNOWN


class RecallBotController(BotController):
    @classmethod
    def __common_headers(cls) -> dict[str, str]:
        return {
            "accept": "application/json",
            "content-type": "application/json",
            "Authorization": _auth_token(),
        }

    def __init__(self, bot_id: str = ""):
        self._bot_id = bot_id

    @property
    def bot_id(self) -> str:
        return self._bot_id

    @property
    def meeting_type(self) -> BotMeetingType:
        return BotMeetingType.VIDEO_CALL

    def create_bot_and_start_recording(
        self,
        meeting_url: str,
        bot_preferences: BotPreferences,
        enable_live_transcription: bool,
        enable_native_zoom_bot: bool,
        bot_base_name: str,
        asr_language_code: str,
        internal_id: UUID | None = None,
        user_phone_number: PhoneNumber | None = None,
    ) -> BotResponse:
        if self.bot_id:
            logging.warning("Bot ID already set, not creating a new bot")
            return BotResponse(http_status.HTTP_400_BAD_REQUEST, {"error": "Bot ID already set"})
        return self._create_or_update_bot(
            meeting_url,
            None,
            bot_preferences,
            enable_live_transcription=enable_live_transcription,
            enable_native_zoom_bot=enable_native_zoom_bot,
            asr_language_code=asr_language_code,
            bot_base_name=bot_base_name,
            internal_id=internal_id,
        )

    def create_or_update_scheduled_bot(
        self,
        meeting_url: str,
        join_time: datetime.datetime,
        bot_preferences: BotPreferences,
        enable_live_transcription: bool,
        enable_native_zoom_bot: bool,
        bot_base_name: str,
        asr_language_code: str,
        internal_id: UUID | None = None,
        user_phone_number: PhoneNumber | None = None,
    ) -> BotResponse:
        return self._create_or_update_bot(
            meeting_url,
            join_time,
            bot_preferences,
            enable_live_transcription=enable_live_transcription,
            enable_native_zoom_bot=enable_native_zoom_bot,
            asr_language_code=asr_language_code,
            bot_base_name=bot_base_name,
            internal_id=internal_id,
        )

    def _create_or_update_bot(
        self,
        meeting_url: str,
        start_time: datetime.datetime | None,
        bot_preferences: BotPreferences,
        enable_live_transcription: bool,
        enable_native_zoom_bot: bool,
        asr_language_code: str,
        bot_base_name: str,
        internal_id: UUID | None = None,
    ) -> BotResponse:
        url = "https://api.recall.ai/api/v1/bot/"
        payload = self.__bot_payload(
            bot_preferences,
            "call_join",
            enable_live_transcription=enable_live_transcription,
            enable_native_zoom_bot=enable_native_zoom_bot,
            asr_language_code=asr_language_code,
            bot_base_name=bot_base_name,
        )

        payload["meeting_url"] = meeting_url
        if start_time:
            payload["join_at"] = start_time.isoformat()
        if internal_id:
            payload["metadata"] = {"zeplyn_bot_uuid": str(internal_id)}
        headers = self.__common_headers()
        if self.bot_id:
            response = requests.patch(f"{url}{self.bot_id}/", json=payload, headers=headers)
        else:
            response = requests.post(url, json=payload, headers=headers)
        data = response.json()
        logging.info("Recall API response: %s", data)
        if response.status_code > 201:
            logging.error("could not %s bot: %s", "update" if self.bot_id else "create", data, exc_info=True)
            return BotResponse(response.status_code, data)
        if returned_id := data.get("id"):
            if self.bot_id and self.bot_id != returned_id:
                logging.warning(
                    "Bot ID changed from %s to %s after bot update. This is unexpected.",
                    self.bot_id,
                    returned_id,
                )
            self._bot_id = returned_id
        return BotResponse(response.status_code, data)

    # Returns a payload to use when creating or updating a bot.
    #
    # This can be used to create ad-hoc bots or bots for calendar entries.
    @classmethod
    def __bot_payload(
        cls,
        bot_preferences: BotPreferences,
        recording_trigger: str,
        enable_live_transcription: bool,
        enable_native_zoom_bot: bool,
        asr_language_code: str,
        bot_base_name: str = "Zeplyn",
    ) -> dict[str, Any]:
        notetaking_image = bot_preferences.recording_image_b64_or_default()
        not_notetaking_image = bot_preferences.not_recording_image_b64_or_default()
        start_notetaking_audio = bot_preferences.recording_message_b64_or_default()
        notetaker_title = bot_preferences.notetaker_name or f"{bot_base_name}'s notetaker"
        enable_video = bot_preferences.enable_video
        enable_notetaker_start_recording_audio_output = bot_preferences.enable_audio_output

        deepgram_options = asr.deepgram_options(asr_language_code, [])
        # According to the Deepgram docs, this should be provided in a separate place in the
        # Deepgram Python API. However, per our Recall support contacts, this needs to be passed in
        # the `transcription_options.deepgram` dict (which Recall should then handle correctly).
        deepgram_options["mip_opt_out"] = True

        payload = {
            "transcription_options": {
                "provider": "deepgram",
                "deepgram": deepgram_options,
            },
            "recording_mode_options": {"start_recording_on": recording_trigger},
            "recording_mode": "audio_only",
            "bot_name": notetaker_title,
            "metadata": {},
        }
        if not enable_live_transcription:
            del payload["transcription_options"]
        if enable_native_zoom_bot:
            payload["variant"] = {"zoom": "native"}
        if bot_preferences.enable_recording_free_zoom:
            zoom_settings = {}
            if "zoom" in payload and isinstance(payload["zoom"], dict):
                zoom_settings = payload["zoom"].copy()
            zoom_settings["require_recording_permission"] = False
            zoom_settings["request_recording_permission_on_host_join"] = False
            payload["zoom"] = zoom_settings

        if enable_video:
            payload["automatic_video_output"] = {
                "in_call_recording": {"kind": "jpeg", "b64_data": notetaking_image},
                "in_call_not_recording": {"kind": "jpeg", "b64_data": not_notetaking_image},
            }
        if enable_notetaker_start_recording_audio_output:
            payload["automatic_audio_output"] = {
                "in_call_recording": {"data": {"kind": "mp3", "b64_data": start_notetaking_audio}},
            }

        log_payload: dict[str, Any] = copy.deepcopy(payload)
        _log_payload(log_payload)

        return payload

    def get_bot_status(self) -> BotStatus:
        try:
            if not self.bot_id:
                logging.warning("bot_id is not set, returning status NOT_CREATED")
                return BotStatus.NOT_CREATED
            url = f"https://api.recall.ai/api/v1/bot/{self.bot_id}/"
            response = requests.get(url, headers=self.__common_headers())
            response_json = response.json()
            logging.debug("Recall bot status response: status %d, data %s", response.status_code, response_json)
            latest_status = self._get_last_recording_status(response_json["status_changes"])
            logging.debug("Recall Bot %s status : %s", self.bot_id, latest_status)
            return latest_status
        except Exception:
            logging.error("could not get bot %s", self.bot_id, exc_info=True)
            return BotStatus.UNKNOWN

    async def aget_bot_status(self) -> BotStatus:
        try:
            if not self.bot_id:
                logging.warning("bot_id is not set, returning status NOT_CREATED")
                return BotStatus.NOT_CREATED
            url = f"https://api.recall.ai/api/v1/bot/{self.bot_id}/"
            async with aiohttp.ClientSession() as session:
                response = await session.get(url, headers=self.__common_headers())
                response_json = await response.json()
                logging.debug("Recall bot status response: status %d, data %s", response.status, response_json)
                latest_status = self._get_last_recording_status(response_json["status_changes"])
                logging.debug("Recall Bot %s status : %s", self.bot_id, latest_status)
                return latest_status
        except Exception:
            logging.error("could not get bot %s", self.bot_id, exc_info=True)
            return BotStatus.UNKNOWN

    def _get_last_recording_status(self, status_changes: list[dict[str, str]]) -> BotStatus:
        for status_change in reversed(status_changes):
            code = status_change.get("code")
            if code and recall_event_to_status.get(code, BotStatus.UNKNOWN) != BotStatus.UNKNOWN:
                return recall_event_to_status[code]
        return BotStatus.UNKNOWN

    def delete_bot(self) -> BotResponse:
        url = f"https://api.recall.ai/api/v1/bot/{self.bot_id}/"
        response = requests.delete(url, headers=self.__common_headers())
        self._bot_id = ""
        return BotResponse(response.status_code, {})

    @property
    def supports_pause_resume(self) -> bool:
        return True

    def pause_recording(self) -> BotResponse:
        url = f"https://api.recall.ai/api/v1/bot/{self.bot_id}/pause_recording/"
        payload: dict[str, dict[str, str]] = {}
        response = requests.post(url, json=payload, headers=self.__common_headers())
        return BotResponse(response.status_code, response.json())

    def resume_recording(self) -> BotResponse:
        url = f"https://api.recall.ai/api/v1/bot/{self.bot_id}/resume_recording/"
        payload: dict[str, dict[str, str]] = {"transcription_options": {"provider": "deepgram"}}
        response = requests.post(url, json=payload, headers=self.__common_headers())
        return BotResponse(response.status_code, response.json())

    def leave_call(self) -> BotResponse:
        url = f"https://api.recall.ai/api/v1/bot/{self.bot_id}/leave_call/"
        payload: dict[str, dict[str, str]] = {}
        response = requests.post(url, json=payload, headers=self.__common_headers())
        return BotResponse(response.status_code, response.json())

    def get_bot_transcript(self) -> list[dict[str, Any]]:
        url = f"https://api.recall.ai/api/v1/bot/{self.bot_id}/transcript/?enhanced_diarization=true"
        response = requests.get(url, headers=self.__common_headers())
        return self._generate_compatible_utts(response.json())

    def _generate_compatible_utts(self, utterances: list[dict[str, Any]]) -> list[dict[str, Any]]:
        result = []
        for utt in utterances:
            speaker = utt["speaker"]
            words = utt["words"]
            for word in words:
                output_utt = {}
                output_utt["speaker"] = speaker
                output_utt["transcript"] = word["text"]
                output_utt["start"] = word["start_timestamp"]
                output_utt["end"] = word["end_timestamp"]
                result.append(output_utt)
        return result

    def get_media_download_info(self) -> tuple[str, str | None, str | None] | None:
        try:
            if not self.bot_id:
                logging.warning("bot_id is not set, returning status NOT_CREATED")
                return None
            url = f"https://api.recall.ai/api/v1/bot/{self.bot_id}/"
            response = requests.get(url, headers=self.__common_headers()).json()
            if not (video_url := response.get("video_url", "")):
                return None
            return (video_url, None, None)
        except Exception:
            logging.error("could not get bot %s", self.bot_id, exc_info=True)
            return None

    def bot_has_speaker_timeline_data(self) -> bool:
        url = f"https://api.recall.ai/api/v1/bot/{self.bot_id}/speaker_timeline/"
        data = requests.get(url, headers=self.__common_headers()).json()
        return isinstance(data, list) and len(data) > 0

    def delete_media(self) -> bool:
        url = f"https://api.recall.ai/api/v1/bot/{self.bot_id}/delete_media/"
        response = requests.post(url, headers=self.__common_headers())
        return http_status.is_success(response.status_code)

    #
    # Send a chat message with the Recall bot
    #
    @property
    def supports_send_chat_message(self) -> bool:
        return True

    def send_chat_message(self, host_message: str | None, everyone_message: str) -> bool:
        # Send host-only message to host if it exists and there's platform support
        # Otherwise send message to everyone
        # Params should include "to" ("host" is supported for zoom only, use "everyone" otherwise)
        # When "host" is used, we want to send an integer for the host ID
        # and "message" (max 500 chars for Google, 4096 Zoom/Teams)
        # Note also there is no Webex support
        # See: https://docs.recall.ai/docs/sending-chat-messages

        send_to = "everyone"
        message = host_message if host_message else everyone_message

        # try to check if it's a zoom chat supporting host-only
        get_bot_url = f"https://api.recall.ai/api/v1/bot/{self.bot_id}/"
        get_response = requests.get(get_bot_url, headers=self.__common_headers())
        # if we were able to get bot status, use that to determine what we send
        if get_response.status_code == 200:
            get_response_json = get_response.json()
            # if confirmed not zoom, goes to everyone
            if get_response_json.get("meeting_url") and get_response_json.get("meeting_url").get("platform") != "zoom":
                send_to = "everyone"
                message = everyone_message
            else:
                # if zoom and sending to host, try to get the host ID
                meeting_participants = get_response_json.get("meeting_participants") or []
                host_id: int | None = None
                for p in meeting_participants:
                    if p.get("is_host"):
                        host_id = p.get("id")
                if host_message and host_id:
                    send_to = str(host_id)

        else:  # if we don't have confirmation it's zoom, fall back to everyone
            send_to = "everyone"
            message = everyone_message

        # Recall doesn't accept messages containing "localhost", on dev we'll sub in "example.com" so messages go through
        # https://docs.recall.ai/reference/errors#:~:text=Providing%20a%20localhost%20URL%20in%20the%20payload
        if "localhost" in message:
            logging.warning("Replacing localhost in Recall chat message, original was: %s", message)
            message = message.replace("localhost", "example.com")

        # send the message
        send_url = f"https://api.recall.ai/api/v1/bot/{self.bot_id}/send_chat_message/"
        params = {"to": send_to, "message": message}
        response = requests.post(send_url, headers=self.__common_headers(), json=params)
        status_ok = http_status.is_success(response.status_code)
        if not status_ok:
            logging.error(
                "Error sending chat message with recall.ai, status code %s, message %s",
                response.status_code,
                response.text,
            )
        return status_ok

    @property
    def debugging_string(self) -> str:
        if not self.bot_id:
            return "<Recall: no bot ID>"
        return f"Recall bot {self.bot_id} ({self.provider_bot_url})"

    @property
    def provider_bot_url(self) -> str:
        if not self.bot_id:
            return "<Recall: no bot ID>"
        return f"https://go/recallbot/{self.bot_id}"

    @property
    def processing_task(self) -> BotProcessingTask:
        # Importing here to avoid circular imports.
        from deepinsights.meetingsapp.tasks import process_bot_recording

        return process_bot_recording

    ##
    # Methods unique to the RecallBotController
    ##

    #
    # Zoom OAuth credential integration
    #

    @classmethod
    def register_user_oauth_credential(cls, code: str) -> ZoomStatus:
        logging.info("Registering Zoom OAuth credential with Recall")
        url = "https://api.recall.ai/api/v2/zoom-oauth-credentials/"
        headers = cls.__common_headers()
        payload = {
            "oauth_app": settings.ZOOM_RECALL_CLIENT_ID,
            "authorization_code": {
                "code": code,
                "redirect_uri": settings.ZOOM_REDIRECT_URL,
            },
        }
        response = requests.post(url, json=payload, headers=headers)

        # https://docs.recall.ai/docs/recall-managed-oauth#handling-re-authorization
        try:
            data = response.json()
        except Exception:
            logging.error("Could not decode Recall response as JSON.")
            return ZoomStatus.FAILURE
        conflicting_id = data.get("conflicting_zoom_user_id", None) if isinstance(data, dict) else None
        if response.status_code == 400 and conflicting_id:
            logging.info("Conflicting Zoom credentials found, likely due to user unauthorizing and reauthorizing.")

            # Get the UUID of the conflicting credential
            list_query_params = {"user_id": conflicting_id}
            response = requests.get(url, params=list_query_params, headers=headers)
            if not response.ok:
                logging.error("Failed to get credential ID for user with conflicting Recall Zoom user credentials.")
                return ZoomStatus.FAILURE
            credentials = response.json()["results"]
            if not isinstance(credentials, list):
                logging.error("Unexpected response for successful fetch of Recall Zoom user credentials")
                return ZoomStatus.FAILURE
            if len(credentials) == 0:
                logging.error("Unexpected response for successful fetch of Recall Zoom user credentials")
                return ZoomStatus.FAILURE
            credential_id = credentials[0]["id"]

            # Delete the conflicting credential.
            delete_url = f"{url}{credential_id}"
            response = requests.delete(delete_url, headers=headers)
            if not response.ok:
                logging.error("Could not delete Recall Zoom user credentials.")
            return ZoomStatus.RETRY_REQUIRED

        logging.info("Registering OAuth credential response: %s; %s", response.status_code, response.text)
        return ZoomStatus.SUCCESS

    #
    # Bot webhook utilities
    #

    def get_internal_bot_uuid(self, max_tries: int = 3) -> str:
        if not self.bot_id:
            logging.error("Empty bot id passed in for Recall AI get_internal_bot_id")
            return ""
        url = f"https://api.recall.ai/api/v1/bot/{self.bot_id}/"
        for attempt in range(max_tries):
            response = requests.get(url, headers=self.__common_headers())
            if response.ok:
                # The bot UUID will not exist in the output in all cases for joining_call webhook processing
                # Empty zeplyn_bot_uuid is expected for bots created for calendar events
                return_value = response.json().get("metadata", {}).get("zeplyn_bot_uuid", "")
                if return_value:
                    return str(return_value)

                logging.info(
                    "Received OK response from Recall AI with missing Zeplyn ID for Recall AI bot ID %s - attempt %s",
                    self.bot_id,
                    attempt,
                )
                return ""
            else:
                logging.error(
                    "Received non-OK response from Recall AI for bot ID %s - attempt %s - response status %s",
                    self.bot_id,
                    attempt,
                    response.status_code,
                )
        logging.error("Max retries %s met for Recall AI for bot ID %s", max_tries, self.bot_id)
        raise Exception(f"Invalid response from Recall AI - {response.status_code}")

    #
    # Calendar and autojoin support
    #

    # Returns the Zeplyn user ID attached to this bot (if any).
    def zeplyn_user_uuid(self) -> str | None:
        url = f"https://api.recall.ai/api/v1/bot/{self.bot_id}"
        response = requests.get(url, headers=self.__common_headers())
        return response.json().get("metadata", {}).get("zeplyn_user_uuid")  # type: ignore[no-any-return]

    # Returns the calendar event related to this bot (if any).
    def calendar_event(self) -> dict[str, Any] | None:
        url = f"https://api.recall.ai/api/v1/bot/{self.bot_id}"
        response = requests.get(url, headers=self.__common_headers())
        if response.status_code == http_status.HTTP_404_NOT_FOUND:
            logging.info("No calendar event for bot %s", self.bot_id)
            return None
        if not http_status.is_success(response.status_code):
            logging.error("Error getting calendar event for bot %s", self.bot_id)
            return None
        calendar_event_id = response.json().get("metadata", {}).get("calendar_event_id")
        if not calendar_event_id:
            return None
        return self.__calendar_event(calendar_event_id)

    # Links a Microsoft calendar with Recall.
    #
    # Returns a tuple: whether the operation succeeded and the linked calendar ID.
    @classmethod
    def link_microsoft_calendar(
        cls, client_id: str, client_secret: str, email: str, refresh_token: str, existing_calendar_id: str | None
    ) -> Tuple[bool, str]:
        return cls.__link_calendar(
            "microsoft_outlook", client_id, client_secret, email, refresh_token, existing_calendar_id
        )

    # Links a Google calendar with Recall.
    #
    # Returns a tuple: whether the operation succeeded and the linked calendar ID.
    @classmethod
    def link_google_calendar(
        cls, client_id: str, client_secret: str, email: str, refresh_token: str, existing_calendar_id: str | None
    ) -> Tuple[bool, str]:
        return cls.__link_calendar(
            "google_calendar", client_id, client_secret, email, refresh_token, existing_calendar_id
        )

    # Links a user's calendar with Recall.ai.
    #
    # Calling this will trigger a stream of calendar event webhook callbacks to the bot webhook
    # endpoint (if the calendar is properly linked).
    #
    # Returns a tuple: whether the operation succeeded and the linked calendar ID.
    @classmethod
    def __link_calendar(
        cls,
        provider: str,
        client_id: str,
        client_secret: str,
        email: str,
        refresh_token: str,
        existing_calendar_id: str | None,
    ) -> Tuple[bool, str]:
        url = "https://us-east-1.recall.ai/api/v2/calendars/"
        headers = cls.__common_headers()
        payload = {
            "oauth_client_id": client_id,
            "oauth_client_secret": client_secret,
            "oauth_email": email,
            "oauth_refresh_token": refresh_token,
            "platform": provider,
        }
        if existing_calendar_id:
            url = f"{url}{existing_calendar_id}/"
            response = requests.patch(url, json=payload, headers=headers)
        else:
            response = requests.post(url, json=payload, headers=headers)
        if not http_status.is_success(response.status_code):
            logging.error("Could not link calendar: %s", response.text)
            if existing_calendar_id:
                # If the request that failed was a patch, the calendar may have been deleted. Try
                # again without an existing ID.
                return cls.__link_calendar(provider, client_id, client_secret, email, refresh_token, None)
            return (False, "")
        return (True, response.json().get("id"))

    # Given a calendar ID, does an empty PATCH update to the calendar.
    #
    # Experimentally, doing this has fixed disconnected calendars in the past.
    @classmethod
    def try_to_relink_calendar(cls, calendar_id: str) -> bool:
        url = f"https://us-east-1.recall.ai/api/v2/calendars/{calendar_id}"
        response = requests.patch(url, headers=cls.__common_headers())
        if not http_status.is_success(response.status_code):
            logging.error("Could not relink calendar: %s", response.text)
            return False
        return True

    # Unlinks a user's calendar from Recall.ai.
    #
    # Calling this will remove all scheduled bots attached to meetings in the calendar from Recall's systems.
    @classmethod
    def unlink_calendar(cls, calendar_id: str) -> bool:
        url = f"https://us-east-1.recall.ai/api/v2/calendars/{calendar_id}"
        response = requests.delete(url, headers=cls.__common_headers())
        if response.status_code == http_status.HTTP_404_NOT_FOUND:
            logging.info("Recall calendar not found: %s. Assuming stale data and returning success.", calendar_id)
            return True
        if not http_status.is_success(response.status_code):
            logging.error("Could not unlink calendar: %s", response.text)
            return False
        return True

    # Given a calendar ID and the last update time, updates the bots associated with the calendar.
    #
    # This will add and delete bots as necessary to match the events in the calendar (from the
    # last update time, if it is provided).
    #
    # Returns a tuple: whether all events were processed, and a list of event IDs that failed to
    # update. Examples:
    #   (False, []): not all events were processed, but all events that were processed were updated.
    #     In this case, it is best to rerun the processing for all events (i.e., pass None for
    #     event_ids_to_process)
    #   (False, ["id"]): not all events were processed, and the update for event with ID "id" failed
    #   (True, []): all events were processed, and no updates failed
    #   (True, ["id"]): all events were processed, and the update for event with ID "id" failed
    @classmethod
    def update_bots_for_calendar(
        cls,
        calendar_id: str,
        last_updated_ts: str | None,
        user: User,
        enable_live_transcription: bool,
        enable_native_zoom_bot: bool,
        *,
        event_ids_to_process: list[str] | None,
        calendar_platform_ids_to_process: list[str] | None,
        force_update: bool,
    ) -> tuple[bool, list[str]]:
        events_url = "https://us-east-1.recall.ai/api/v2/calendar-events/"
        events_query_params = {
            "calendar_id": calendar_id,
            "start_time__gte": timezone.now().isoformat(),
        }
        if last_updated_ts:
            events_query_params["updated_at__gte"] = last_updated_ts

        # Get user's auto join ignored keywords for filtering
        auto_join_ignored_keywords_list = user.get_preferences().calendar_preferences.auto_join_ignored_keywords or []

        failed_event_ids: list[str] = []
        next_url = events_url
        while next_url:
            logging.info("Handling next page of calendar events")
            # Fetch the next page of results.
            response = requests.get(next_url, params=events_query_params, headers=cls.__common_headers())
            status_code = response.status_code
            if not http_status.is_success(status_code):
                logging.error("Could not update bots for calendar: %s, %s", status_code, response.text)
                return False, failed_event_ids
            data = response.json()
            next_url = data.get("next")

            # Process the events, updating bots as necessary.
            for event in data.get("results"):
                logging.info("Handling event: %s", event)
                event_id = event.get("id")
                event_platform_id = event.get("platform_id")
                event_title = event.get("summary", "")  # Event title for filtering

                if event_ids_to_process and event_id not in event_ids_to_process:
                    logging.info("Skipping event because it is not in event_ids_to_process: %s", event_id)
                    continue
                if calendar_platform_ids_to_process and event_platform_id not in calendar_platform_ids_to_process:
                    logging.info(
                        "Skipping event because its platform ID is not in calendar_event_ids_to_process: %s", event_id
                    )
                    continue

                autojoin_disabled = False
                if should_ignore_event_by_keywords(event_title, auto_join_ignored_keywords_list):
                    logging.info(
                        "Event %s with title '%s' matches ignored keywords, setting autojoin_disabled=True",
                        event_id,
                        event_title,
                    )
                    autojoin_disabled = True
                else:
                    try:
                        scheduled_event = ScheduledEvent.objects.get(
                            user=user, user_specific_source_id=event_platform_id
                        )
                        autojoin_disabled = (
                            scheduled_event.autojoin_behavior == ScheduledEvent.AutoJoinOverride.DISABLED
                        )
                    except ScheduledEvent.DoesNotExist:
                        logging.info("Scheduled event not found for event: %s. Assuming autojoin is enabled.", event_id)

                future_bots = list(
                    filter(
                        lambda b: datetime.datetime.fromisoformat(b.get("start_time", "2020-01-01T00:00:00Z"))
                        > timezone.now(),
                        event.get("bots", []),
                    )
                )

                # Delete bots if the event is deleted, or if autojoin should not be enabled for this meeting.
                if event.get("is_deleted") or autojoin_disabled:
                    if not future_bots:
                        logging.info("No bots to delete for event: %s", event_id)
                        continue
                    logging.info("Deleting bots for event: %s", event_id)
                    if not cls.__delete_bots_for_calendar_event(event_id):
                        failed_event_ids.append(event_id)
                    continue

                if not (meeting_url := event.get("meeting_url")):
                    logging.warning("Event has no meeting URL: %s. Skipping.", event_id)
                    continue
                if not (start_time := event.get("start_time")):
                    logging.error("Event has no start time: %s. Skipping.", event_id)
                    continue

                # Create a new bot if required.
                #
                # We need to create a new bot if there are no bots, or if the meeting previously had been used
                # but has been moved into the future (https://docs.recall.ai/docs/scheduling-guide#perpetual-event),
                # in which case it may only have bots whose start time is in the past.
                if not future_bots:
                    logging.info("Creating bots for event: %s", event_id)
                    if not cls.__create_bot_for_calendar_event(
                        event_id,
                        meeting_url,
                        start_time,
                        user,
                        enable_live_transcription,
                        enable_native_zoom_bot,
                    ):
                        failed_event_ids.append(event_id)
                    continue

                # At this point, at least one of the bots has a start time in the future. If the
                # event has materially changed, we need to recreate that bot.
                bot_update_required = False
                for bot in future_bots:
                    bot_start_time = bot.get("start_time")
                    has_same_start_time = bot_start_time == event.get("start_time")
                    has_same_end_time = bot.get("end_time") == event.get("end_time")
                    has_same_meeting_url = bot.get("meeting_url") == meeting_url
                    if not force_update and (has_same_start_time and has_same_end_time and has_same_meeting_url):
                        logging.info(
                            "Bot has same start/end time and meeting URL as event: %s; ignoring for update", event_id
                        )
                        continue
                    bot_update_required = True

                if not bot_update_required:
                    continue
                updates_succeeded_for_event = True
                logging.info("Updating bots for event: %s", event_id)
                if not cls.__delete_bots_for_calendar_event(event_id):
                    updates_succeeded_for_event = False
                if not cls.__create_bot_for_calendar_event(
                    event_id, meeting_url, start_time, user, enable_live_transcription, enable_native_zoom_bot
                ):
                    updates_succeeded_for_event = False
                if not updates_succeeded_for_event:
                    failed_event_ids.append(event_id)

        # If we're here, we've processed all events.
        return True, failed_event_ids

    # Deletes all scheduled bots associated with a calendar event.
    @classmethod
    def __delete_bots_for_calendar_event(cls, event_id: str) -> bool:
        url = f"https://us-east-1.recall.ai/api/v2/calendar-events/{event_id}/bot/"
        response = requests.delete(url, headers=cls.__common_headers())
        status_code = response.status_code
        if not http_status.is_success(status_code):
            logging.error(f"Could not delete bots for calendar event: {status_code}, {response.text}")
            return False
        return True

    # Creates a Recall bot associated with a calendar event.
    #
    # Note that this is distinct from an ad-hoc bot, even a scheduled ad-hoc bot, although it is
    # still a bot accessible via the bot API: it's created in the context of a calendar event,
    # rather than in isolation.
    @classmethod
    def __create_bot_for_calendar_event(
        cls,
        event_id: str,
        meeting_url: str,
        start_time: str,
        user: User,
        enable_live_transcription: bool,
        enable_native_zoom_bot: bool,
    ) -> bool:
        url = f"https://us-east-1.recall.ai/api/v2/calendar-events/{event_id}/bot/"
        user_preferences = user.get_preferences()
        asr_language_code = user_preferences.asr_language_code
        if not asr_language_code:
            logging.warning("ASR language code not set for user: %s. Using default('en') for now", user.uuid)
            asr_language_code = "en"

        bot_config = cls.__bot_payload(
            user_preferences.bot_preferences,
            "call_join",
            enable_live_transcription=enable_live_transcription,
            enable_native_zoom_bot=enable_native_zoom_bot,
            asr_language_code=asr_language_code,
            bot_base_name=user.first_name or user.name,
        )
        bot_config["metadata"]["zeplyn_user_uuid"] = str(user.uuid)
        bot_config["metadata"]["calendar_event_id"] = event_id
        deduplication_key = f"{event_id}-{meeting_url}-{start_time}"
        if Flags.EnableOrgLevelBotDeduplication.is_active_for_user(user) and (org := user.organization):
            deduplication_key = f"{org.id}-{sha256(org.name.encode()).hexdigest()}-{meeting_url}-{start_time}"
        payload = {
            "deduplication_key": deduplication_key,
            "bot_config": bot_config,
        }
        response = requests.post(url, json=payload, headers=cls.__common_headers())
        status_code = response.status_code
        if not http_status.is_success(status_code):
            logging.error(f"Could not create bot for calendar event: {status_code}, {response.text}")
            return False
        return True

    # Gets a calendar event by its identifier.
    @classmethod
    def __calendar_event(cls, calendar_event_id: str) -> dict[str, Any] | None:
        url = f"https://us-east-1.recall.ai/api/v2/calendar-events/{calendar_event_id}/"
        response = requests.get(url, headers=cls.__common_headers())
        if not http_status.is_success(response.status_code):
            logging.error(f"Could not get calendar event: {response.text}")
            return None
        return response.json()  # type: ignore[no-any-return]

    # Gets the status and platform of a calendar.
    @classmethod
    def info_for_calendar(cls, calendar_id: str) -> dict[Literal["status", "platform"], str] | None:
        url = f"https://us-east-1.recall.ai/api/v2/calendars/{calendar_id}/"
        response = requests.get(url, headers=cls.__common_headers())
        if not http_status.is_success(response.status_code):
            logging.error(f"Could not get status for calendar: {response.text}")
            return None
        data = response.json()
        if not (status := data.get("status")) or not (platform := data.get("platform")):
            logging.error("Could not get info for calendar %s: %s", calendar_id, data)
            return None
        return {"status": status, "platform": platform}

    @classmethod
    def connected_calendar_platform(cls, calendar_id: str | None) -> Literal["microsoft", "google"] | None:
        """Fetches the Zeplyn version of the connected Recall calendar platform for the user.

        Returns None if the calendar is not connected or if the user does not have a calendar.
        Otherwise, returns `microsoft` or `google`
        """

        if not calendar_id:
            return None
        try:
            if (calendar_info := cls.info_for_calendar(calendar_id)) and RecallCalendarStatus(
                calendar_info.get("status", "unknown")
            ) == RecallCalendarStatus.CONNECTED:
                match calendar_info.get("platform"):
                    case "google_calendar":
                        return "google"
                    case "microsoft_outlook":
                        return "microsoft"
            return None
        except Exception as e:
            logging.error("Could not get Recall calendar info for calendar %s.", calendar_id, exc_info=e)
            return None
