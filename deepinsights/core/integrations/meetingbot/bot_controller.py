import datetime
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum, StrEnum
from typing import Any
from uuid import UUID

from dataclasses_json import dataclass_json
from phonenumbers import PhoneNumber

from deepinsights.core.preferences.preferences import BotPreferences
from deepinsights.meetingsapp.bot_processing_task import BotProcessingTask


class BotMeetingType(StrEnum):
    PHONE_CALL = "phone_call"
    VIDEO_CALL = "video_call"


class BotStatus(Enum):
    UNKNOWN = "unknown"
    NOT_CREATED = "not_created"
    SCHEDULED = "scheduled"
    IN_WAITING_ROOM = "in_waiting_room"
    IN_CALL_NOT_RECORDING = "in_call_not_recording"
    IN_CALL_RECORDING = "in_call_recording"
    CALL_ENDED = "call_ended"
    RECORDING_DONE = "recording_done"
    ERROR = "error"


@dataclass_json
@dataclass
class BotResponse:
    status: int
    details: dict[str, Any]


class ZoomStatus(Enum):
    SUCCESS = 0
    FAILURE = 1
    RETRY_REQUIRED = 2


class BotController(ABC):
    """
    A controller for a meeting bot, i.e., bots that join video meeting (e.g., Zoom) calls or audio
    calls and records them.

    This controller is only expected to be responsible for one bot at a time. Create a new
    controller for each bot you want to manage.
    """

    @property
    @abstractmethod
    def bot_id(self) -> str:
        """
        The ID of the bot.

        This varies per integration, and is not expected to be consistent across subclasses.
        """
        pass

    @property
    @abstractmethod
    def meeting_type(self) -> BotMeetingType:
        """
        The type of meeting the bot is in.

        This is used to determine how to interact with the bot.
        """
        pass

    @abstractmethod
    def create_bot_and_start_recording(
        self,
        meeting_url: str,
        bot_preferences: BotPreferences,
        enable_live_transcription: bool,
        enable_native_zoom_bot: bool,
        bot_base_name: str,
        asr_language_code: str,
        internal_id: UUID | None = None,
        user_phone_number: PhoneNumber | None = None,
    ) -> BotResponse:
        """Creates a new bot with the given parameters and starts recording the meeting."""
        pass

    @abstractmethod
    def create_or_update_scheduled_bot(
        self,
        meeting_url: str,
        join_time: datetime.datetime,
        bot_preferences: BotPreferences,
        enable_live_transcription: bool,
        enable_native_zoom_bot: bool,
        bot_base_name: str,
        asr_language_code: str,
        internal_id: UUID | None = None,
        user_phone_number: PhoneNumber | None = None,
    ) -> BotResponse:
        """Creates or updates a scheduled bot with the given parameters."""
        pass

    @abstractmethod
    def get_bot_status(self) -> BotStatus:
        """
        Fetches the status of the bot.

        This may perform HTTP operations.
        """
        pass

    @abstractmethod
    async def aget_bot_status(self) -> BotStatus:
        """
        Fetches the status of the bot in a coroutine.

        This may perform HTTP operations.
        """
        pass

    @abstractmethod
    def delete_bot(self) -> BotResponse:
        """Deletes the bot."""
        pass

    @property
    @abstractmethod
    def supports_pause_resume(self) -> bool:
        """Returns whether the bot supports pausing and resuming recording."""
        pass

    @abstractmethod
    def pause_recording(self) -> BotResponse:
        """Pauses recording with the bot."""
        pass

    @abstractmethod
    def resume_recording(self) -> BotResponse:
        """Resumes recording with the bot, if it was paused."""
        pass

    @abstractmethod
    def leave_call(self) -> BotResponse:
        """Causes the bot to leave the call/meeting."""
        pass

    @abstractmethod
    def get_bot_transcript(self) -> list[dict[str, Any]]:
        """Fetches the transcript of the call recorded by the bot."""
        pass

    @abstractmethod
    def get_media_download_info(self) -> tuple[str, str | None, str | None] | None:
        """
        Fetches a URL to the media recorded by the bot.

        Returns a tuple of the URL, the (optional) HTTP Basic Auth username, and the (optional) HTTP Basic Auth password.
        """
        pass

    @abstractmethod
    def bot_has_speaker_timeline_data(self) -> bool:
        """Returns whether the bot has speaker timeline data."""
        pass

    @abstractmethod
    def delete_media(self) -> bool:
        """Deletes the media recorded by the bot."""
        pass

    @property
    @abstractmethod
    def supports_send_chat_message(self) -> bool:
        """Returns whether the bot supports sending a message within the chat."""
        pass

    @abstractmethod
    def send_chat_message(self, host_message: str | None, everyone_message: str) -> bool:
        """Sends a chat message from the bot to the call's chat system.
        Falls back to everyone_message if host only message is not supported or if there is no host_message passed in"""
        pass

    @property
    @abstractmethod
    def debugging_string(self) -> str:
        """Returns a string that can be printed in logs for debugging purposes."""
        pass

    @property
    @abstractmethod
    def provider_bot_url(self) -> str:
        """Returns a URL that can be used to view information about the bot in the provider's interface."""
        pass

    @property
    @abstractmethod
    def processing_task(self) -> BotProcessingTask:
        """Returns the Celery task that processes the bot's recording."""
        pass
