import datetime
import logging
import uuid
from typing import Any

import phonenumbers
from django.conf import settings
from twilio.base.exceptions import TwilioRestException
from twilio.http import HttpClient
from twilio.http.async_http_client import AsyncTwilioHttpClient
from twilio.rest import Client
from twilio.rest.api.v2010.account.call import CallContext
from twilio.rest.api.v2010.account.conference import ConferenceContext

from deepinsights.core.integrations.meetingbot.bot_controller import (
    BotController,
    BotMeetingType,
    BotResponse,
    BotStatus,
)
from deepinsights.core.preferences.preferences import BotPreferences
from deepinsights.meetingsapp.bot_processing_task import BotProcessingTask


def recording_webhook() -> str:
    from app.fastapi import app

    if not (domain := settings.WEBHOOK_DOMAIN):
        raise ValueError("WEBHOOK_DOMAIN is not set")
    return f"{domain}{app.url_path_for('handle_twilio_recording_event')}"


def conference_webhook() -> str:
    from app.fastapi import app

    if not (domain := settings.WEBHOOK_DOMAIN):
        raise ValueError("WEBHOOK_DOMAIN is not set")
    return f"{domain}{app.url_path_for('handle_twilio_conference_event')}"


def call_webhook() -> str:
    from app.fastapi import app

    if not (domain := settings.WEBHOOK_DOMAIN):
        raise ValueError("WEBHOOK_DOMAIN is not set")
    return f"{domain}{app.url_path_for('handle_twilio_call_event')}"


def on_participant_join_webhook() -> str:
    from app.fastapi import app

    if not (domain := settings.WEBHOOK_DOMAIN):
        raise ValueError("WEBHOOK_DOMAIN is not set")
    return f"{domain}{app.url_path_for('handle_twilio_participant_join_event')}"


def pin_webhook() -> str:
    from app.fastapi import app

    if not (domain := settings.WEBHOOK_DOMAIN):
        raise ValueError("WEBHOOK_DOMAIN is not set")
    return f"{domain}{app.url_path_for('handle_twilio_call_event_with_pin')}"


class TwilioConferenceBotController(BotController):
    conference_sid: str

    def __init__(self, bot_id: str = ""):
        self.conference_sid = bot_id

    @property
    def bot_id(self) -> str:
        return self.conference_sid or ""

    @property
    def meeting_type(self) -> BotMeetingType:
        return BotMeetingType.PHONE_CALL

    def _twilio_client(self, http_client: HttpClient | None = None) -> Client:
        return Client(
            username=settings.TWILIO_AUTH_TOKEN_SID,
            password=settings.TWILIO_AUTH_TOKEN_SECRET,
            account_sid=settings.TWILIO_ACCOUNT_SID,
            http_client=http_client,
        )

    def _conference(self, client: Client, id: str | None = None) -> ConferenceContext:
        return client.conferences.get(id or self.conference_sid)

    def create_bot_and_start_recording(
        self,
        meeting_url: str,
        bot_preferences: BotPreferences,
        enable_live_transcription: bool,
        enable_native_zoom_bot: bool,
        bot_base_name: str,
        asr_language_code: str = "en",
        internal_id: uuid.UUID | None = None,
        user_phone_number: phonenumbers.PhoneNumber | None = None,
    ) -> BotResponse:
        try:
            twilio_phone_number = phonenumbers.parse(settings.TWILIO_PHONE_NUMBER, "US")
            client_phone_number = phonenumbers.parse(meeting_url, "US")
        except phonenumbers.phonenumberutil.NumberParseException as e:
            logging.error("Error parsing phone numbers: %s, %s", settings.TWILIO_PHONE_NUMBER, meeting_url, exc_info=e)
            return BotResponse(status=400, details={"message": "Invalid phone number"})
        if not user_phone_number:
            return BotResponse(status=400, details={"message": "User phone number is required for conference"})
        user_phone_number_string = phonenumbers.format_number(user_phone_number, phonenumbers.PhoneNumberFormat.E164)
        try:
            participant = self._conference(self._twilio_client(), str(uuid.uuid4())).participants.create(
                from_=phonenumbers.format_number(twilio_phone_number, phonenumbers.PhoneNumberFormat.E164),
                to=user_phone_number_string,
                label=user_phone_number_string,
                end_conference_on_exit=True,
                conference_status_callback=conference_webhook(),
                conference_status_callback_event=["start", "end", "join", "leave"],
                conference_record=True,
                conference_recording_status_callback=recording_webhook(),
                conference_recording_status_callback_event=["in-progress", "completed", "failed", "absent"],
            )
            self.conference_sid = participant.conference_sid
            return BotResponse(status=201, details={"message": "Bot created"})
        except TwilioRestException as e:
            logging.error("Error creating Twilio conference", exc_info=e)
            return BotResponse(status=e.status, details={"message": "Error creating conference"})

    def create_or_update_scheduled_bot(
        self,
        meeting_url: str,
        join_time: datetime.datetime,
        bot_preferences: BotPreferences,
        enable_live_transcription: bool,
        enable_native_zoom_bot: bool,
        bot_base_name: str,
        asr_language_code: str = "en",
        internal_id: uuid.UUID | None = None,
        user_phone_number: phonenumbers.PhoneNumber | None = None,
    ) -> BotResponse:
        logging.error("Twilio conference bots do not support scheduling")
        return BotResponse(status=400, details={"message": "Twilio conference bots do not support scheduling"})

    def get_bot_status(self) -> BotStatus:
        if not self.conference_sid:
            return BotStatus.NOT_CREATED
        try:
            client = self._twilio_client()
            conference = self._conference(client)
            status = conference.fetch().status
        except TwilioRestException as e:
            logging.error("Error fetching conference for %s", self.debugging_string, exc_info=e)
            return BotStatus.UNKNOWN
        if status == "init":
            return BotStatus.IN_WAITING_ROOM
        if status == "completed":
            return BotStatus.CALL_ENDED
        if status != "in-progress":
            return BotStatus.UNKNOWN

        try:
            participants = conference.participants.list()
        except TwilioRestException as e:
            logging.error("Error fetching conference participants for %s", self.debugging_string, exc_info=e)
            return BotStatus.UNKNOWN
        particpiant_statuses = [participant.status for participant in participants]
        if any([status == "failed" for status in particpiant_statuses]):
            return BotStatus.ERROR
        if len(participants) < 2:
            return BotStatus.IN_WAITING_ROOM

        if any(
            [status == "queued" or status == "connecting" or status == "ringing" for status in particpiant_statuses]
        ):
            return BotStatus.IN_WAITING_ROOM
        if any([status == "connected" for status in particpiant_statuses]):
            return BotStatus.IN_CALL_RECORDING
        if all([status == "complete" for status in particpiant_statuses]):
            return BotStatus.CALL_ENDED
        logging.error(
            "Unknown Twilio participant statuses '%s' for %s", str(particpiant_statuses), self.debugging_string
        )
        return BotStatus.UNKNOWN

    async def aget_bot_status(self) -> BotStatus:
        if not self.conference_sid:
            return BotStatus.NOT_CREATED
        async with AsyncTwilioHttpClient() as http_client:
            try:
                client = self._twilio_client(http_client=http_client)
                conference = await client.conferences.get(self.conference_sid).fetch_async()
            except TwilioRestException as e:
                logging.error("Error fetching conference for %s", self.debugging_string, exc_info=e)
                return BotStatus.UNKNOWN

            status = conference.status
            if status == "init":
                return BotStatus.IN_WAITING_ROOM
            if status == "completed":
                return BotStatus.CALL_ENDED
            if status != "in-progress":
                return BotStatus.UNKNOWN

            try:
                participants = await client.conferences.get(self.conference_sid).participants.list_async()
            except TwilioRestException as e:
                logging.error("Error fetching conference participants for %s", self.debugging_string, exc_info=e)
                return BotStatus.UNKNOWN
            particpiant_statuses = [participant.status for participant in participants]
            if any([status == "failed" for status in particpiant_statuses]):
                return BotStatus.ERROR
            if len(participants) < 2:
                return BotStatus.IN_WAITING_ROOM

            if any(
                [status == "queued" or status == "connecting" or status == "ringing" for status in particpiant_statuses]
            ):
                return BotStatus.IN_WAITING_ROOM
            if any([status == "connected" for status in particpiant_statuses]):
                return BotStatus.IN_CALL_RECORDING
            if all([status == "complete" for status in particpiant_statuses]):
                return BotStatus.CALL_ENDED
            logging.error("Unknown Twilio conference status '%s' for %s", status, self.debugging_string)
            return BotStatus.UNKNOWN

    def delete_bot(self) -> BotResponse:
        try:
            self._conference(self._twilio_client()).delete()
            self.conference_sid = ""
            return BotResponse(status=204, details={"message": "Bot deleted"})
        except TwilioRestException as e:
            logging.error("Error deleting conference for %s", self.debugging_string, exc_info=e)
            return BotResponse(status=e.status, details={"message": "Error deleting conference"})

    @property
    def supports_pause_resume(self) -> bool:
        return False

    def pause_recording(self) -> BotResponse:
        return BotResponse(status=501, details={"message": "Not implemented"})

    def resume_recording(self) -> BotResponse:
        return BotResponse(status=501, details={"message": "Not implemented"})

    def leave_call(self) -> BotResponse:
        try:
            self._conference(self._twilio_client()).update(status="completed")
            return BotResponse(status=200, details={"message": "Bot left the call"})
        except TwilioRestException as e:
            logging.error("Error leaving conference for %s", self.debugging_string, exc_info=e)
            return BotResponse(status=e.status, details={"message": "Error leaving conference"})

    def get_bot_transcript(self) -> list[dict[str, Any]]:
        return []

    def get_media_download_info(self) -> tuple[str, str | None, str | None] | None:
        try:
            client = self._twilio_client()
            recordings = self._conference(client).recordings.list()
            if not recordings:
                return None
            if len(recordings) > 1:
                logging.warning("Multiple recordings found for %s. Using the first one.", self.debugging_string)
            recording_sid = recordings[0].sid
            recording = client.recordings.get(recording_sid).fetch()
            return (f"{recording.media_url}.mp3", settings.TWILIO_AUTH_TOKEN_SID, settings.TWILIO_AUTH_TOKEN_SECRET)
        except TwilioRestException as e:
            logging.error("Error fetching media download info for %s", self.debugging_string, exc_info=e)
            return None

    def bot_has_speaker_timeline_data(self) -> bool:
        return False

    def delete_media(self) -> bool:
        try:
            for recording in self._conference(self._twilio_client()).recordings.list():
                # There seems to be a bug with the Twilio REST API, or restricted API keys, which causes
                # deletion to fail with a 401 if you try to delete the recording as a child of the conference.
                # Instead, reference the recording directly and delete it.
                self._twilio_client().recordings.get(recording.sid).delete()
            return True
        except TwilioRestException as e:
            logging.error("Error deleting recording(s) for %s", self.debugging_string, exc_info=e)
            return False

    @property
    def supports_send_chat_message(self) -> bool:
        return False

    def send_chat_message(self, host_message: str | None, everyone_message: str) -> bool:
        return False

    @property
    def debugging_string(self) -> str:
        if not self.conference_sid:
            return "<Twilio: no conference ID>"
        return f"Twilio Conference {self.conference_sid} ({self.provider_bot_url})"

    @property
    def provider_bot_url(self) -> str:
        if not self.conference_sid:
            return "<Twilio: no conference ID>"
        return f"https://go/twilioconference/{self.conference_sid}"

    @property
    def processing_task(self) -> BotProcessingTask:
        # Avoid a circular import
        from deepinsights.meetingsapp.tasks import process_phone_call_recording

        return process_phone_call_recording

    # Twilio-specific methods

    async def _is_validated_caller_id_number(
        self, http_client: HttpClient, phone_number: phonenumbers.PhoneNumber
    ) -> bool:
        client = self._twilio_client(http_client=http_client)
        ids = await client.outgoing_caller_ids.list_async()
        return any([phonenumbers.parse(id.phone_number, "US") == phone_number for id in ids])

    # Add a participant to a conference.
    #
    # Returns True if the participant was added, False if they were already in the call, or raises an exception.
    async def add_participant_if_not_in_call(
        self,
        participant_number: phonenumbers.PhoneNumber,
        caller_id_number: phonenumbers.PhoneNumber | None,
    ) -> bool:
        async with AsyncTwilioHttpClient() as http_client:
            client = self._twilio_client(http_client=http_client)

            # Make sure this participant isn't already in the call. If they are, make the
            # "recording in progress" announcement.
            participants = client.conferences.get(self.conference_sid).participants
            for participant in await participants.list_async():
                if phonenumbers.parse(participant.label, "US") == participant_number:
                    return False

            from_number = settings.TWILIO_PHONE_NUMBER
            if caller_id_number and await self._is_validated_caller_id_number(http_client, caller_id_number):
                from_number = phonenumbers.format_number(caller_id_number, phonenumbers.PhoneNumberFormat.E164)
            participant_number_string = phonenumbers.format_number(
                participant_number, phonenumbers.PhoneNumberFormat.E164
            )
            await participants.create_async(
                from_=from_number,
                to=participant_number_string,
                label=participant_number_string,
            )
            return True

    async def make_recording_announcement(self, participant_number: phonenumbers.PhoneNumber) -> None:
        try:
            async with AsyncTwilioHttpClient() as http_client:
                client = self._twilio_client(http_client=http_client)
                participants = client.conferences.get(self.conference_sid).participants
                for participant in await participants.list_async():
                    if phonenumbers.parse(participant.label, "US") == participant_number:
                        await participant.update_async(announce_url=on_participant_join_webhook())
        except TwilioRestException as e:
            logging.error("Error making recording announcement for %s", self.debugging_string, exc_info=e)


class TwilioCallBotController(BotController):
    call_sid: str

    def __init__(self, bot_id: str = ""):
        self.call_sid = bot_id

    @property
    def bot_id(self) -> str:
        return self.call_sid or ""

    @property
    def meeting_type(self) -> BotMeetingType:
        return BotMeetingType.PHONE_CALL

    @classmethod
    def _twilio_client(self, http_client: HttpClient | None = None) -> Client:
        return Client(
            username=settings.TWILIO_AUTH_TOKEN_SID,
            password=settings.TWILIO_AUTH_TOKEN_SECRET,
            account_sid=settings.TWILIO_ACCOUNT_SID,
            http_client=http_client,
        )

    def _call(self, client: Client, id: str | None = None) -> CallContext:
        return client.calls.get(id or self.call_sid)

    def create_bot_and_start_recording(
        self,
        meeting_url: str,
        bot_preferences: BotPreferences,
        enable_live_transcription: bool,
        enable_native_zoom_bot: bool,
        bot_base_name: str,
        asr_language_code: str = "en",
        internal_id: uuid.UUID | None = None,
        user_phone_number: phonenumbers.PhoneNumber | None = None,
    ) -> BotResponse:
        try:
            cleaned_meeting_url_phone_number = phonenumbers.parse(meeting_url, "US")
            cleaned_twilio_phone_number = phonenumbers.parse(settings.TWILIO_PHONE_NUMBER, "US")
        except phonenumbers.phonenumberutil.NumberParseException as e:
            logging.error("Error parsing phone numbers for call", exc_info=e)
            return BotResponse(status=400, details={"message": "Invalid phone number"})
        twiml = (
            "<Response>"
            "<Pause length='8'/>"
            "<Say>This meeting is being transcribed.</Say>"
            f"<Record maxLength='14400' timeout='0' recordingStatusCallback='{recording_webhook()}' recordingStatusCallbackEvent='in-progress completed failed absent'/>"
            "</Response>"
        )
        try:
            call = self._twilio_client().calls.create(
                to=phonenumbers.format_number(cleaned_meeting_url_phone_number, phonenumbers.PhoneNumberFormat.E164),
                from_=phonenumbers.format_number(cleaned_twilio_phone_number, phonenumbers.PhoneNumberFormat.E164),
                twiml=twiml,
                status_callback=call_webhook(),
                status_callback_event=["completed"],
            )
            self.call_sid = call.sid
            return BotResponse(status=201, details={"message": "Bot created"})
        except TwilioRestException as e:
            logging.error("Error creating call", exc_info=e)
            return BotResponse(status=e.status, details={"message": "Error creating call"})

    def create_or_update_scheduled_bot(
        self,
        meeting_url: str,
        join_time: datetime.datetime,
        bot_preferences: BotPreferences,
        enable_live_transcription: bool,
        enable_native_zoom_bot: bool,
        bot_base_name: str,
        asr_language_code: str = "en",
        internal_id: uuid.UUID | None = None,
        user_phone_number: phonenumbers.PhoneNumber | None = None,
    ) -> BotResponse:
        logging.error("Twilio call bots do not support scheduling")
        return BotResponse(status=400, details={"message": "Twilio call bots do not support scheduling"})

    def get_bot_status(self) -> BotStatus:
        if not self.call_sid:
            return BotStatus.NOT_CREATED

        try:
            call = self._call(self._twilio_client())
            status = call.fetch().status
        except TwilioRestException as e:
            logging.error("Error fetching call for %s", self.debugging_string, exc_info=e)
            return BotStatus.UNKNOWN
        if status == "queued" or status == "ringing":
            return BotStatus.IN_WAITING_ROOM
        if status == "in-progress":
            try:
                recordings = call.recordings.list()
            except TwilioRestException as e:
                logging.error("Error fetching call recordings for %s", self.debugging_string, exc_info=e)
                return BotStatus.UNKNOWN
            if not recordings:
                return BotStatus.IN_WAITING_ROOM
            if all([recording.status == "in-progress" or recording.status == "processing" for recording in recordings]):
                return BotStatus.IN_CALL_RECORDING
            else:
                return BotStatus.IN_CALL_NOT_RECORDING
        if status == "completed":
            return BotStatus.CALL_ENDED
        if status == "busy" or status == "failed" or status == "no-answer" or status == "canceled":
            return BotStatus.ERROR
        logging.error("Unknown Twilio call status '%s' for %s", status, self.debugging_string)
        return BotStatus.UNKNOWN

    async def aget_bot_status(self) -> BotStatus:
        if not self.call_sid:
            return BotStatus.NOT_CREATED

        async with AsyncTwilioHttpClient() as http_client:
            try:
                client = self._twilio_client(http_client=http_client)
                call = client.calls.get(self.call_sid)
                status = (await call.fetch_async()).status
            except TwilioRestException as e:
                logging.error("Error fetching call for %s", self.debugging_string, exc_info=e)
                return BotStatus.UNKNOWN
            if status == "queued" or status == "ringing":
                return BotStatus.IN_WAITING_ROOM
            if status == "in-progress":
                try:
                    recordings = await call.recordings.list_async()
                except TwilioRestException as e:
                    logging.error("Error fetching call recordings for %s", self.debugging_string, exc_info=e)
                    return BotStatus.UNKNOWN
                if not recordings:
                    return BotStatus.IN_WAITING_ROOM
                if all(
                    [recording.status == "in-progress" or recording.status == "processing" for recording in recordings]
                ):
                    return BotStatus.IN_CALL_RECORDING
                else:
                    return BotStatus.IN_CALL_NOT_RECORDING
            if status == "completed":
                return BotStatus.CALL_ENDED
            if status == "busy" or status == "failed" or status == "no-answer" or status == "canceled":
                return BotStatus.ERROR
            logging.error("Unknown Twilio call status '%s' for %s", status, self.debugging_string)
            return BotStatus.UNKNOWN

    def delete_bot(self) -> BotResponse:
        try:
            self._call(self._twilio_client()).delete()
            self.call_sid = ""
            return BotResponse(status=204, details={"message": "Bot deleted"})
        except TwilioRestException as e:
            logging.error("Error deleting call for %s", self.debugging_string, exc_info=e)
            return BotResponse(status=e.status, details={"message": "Error deleting call"})

    @property
    def supports_pause_resume(self) -> bool:
        return False

    def pause_recording(self) -> BotResponse:
        return BotResponse(status=501, details={"message": "Not implemented"})

    def resume_recording(self) -> BotResponse:
        return BotResponse(status=501, details={"message": "Not implemented"})

    def leave_call(self) -> BotResponse:
        try:
            self._call(self._twilio_client()).update(status="completed")
            return BotResponse(status=200, details={"message": "Bot left the call"})
        except TwilioRestException as e:
            logging.error("Error leaving call for %s", self.debugging_string, exc_info=e)
            return BotResponse(status=e.status, details={"message": "Error leaving call"})

    def get_bot_transcript(self) -> list[dict[str, Any]]:
        return []

    def get_media_download_info(self) -> tuple[str, str | None, str | None] | None:
        try:
            client = self._twilio_client()
            recordings = self._call(client).recordings.list()
            if not recordings:
                return None
            if len(recordings) > 1:
                logging.warning("Multiple recordings found for %s. Using the first one.", self.debugging_string)
            recording_sid = recordings[0].sid
            recording = client.recordings.get(recording_sid).fetch()
            return (f"{recording.media_url}.mp3", settings.TWILIO_AUTH_TOKEN_SID, settings.TWILIO_AUTH_TOKEN_SECRET)
        except TwilioRestException as e:
            logging.error("Error fetching media download info for %s", self.debugging_string, exc_info=e)
            return None

    def bot_has_speaker_timeline_data(self) -> bool:
        return False

    def delete_media(self) -> bool:
        try:
            for recording in self._call(self._twilio_client()).recordings.list():
                recording.delete()
            return True
        except TwilioRestException as e:
            logging.error("Error deleting recording(s) for %s", self.debugging_string, exc_info=e)
            return False

    @property
    def supports_send_chat_message(self) -> bool:
        return False

    def send_chat_message(self, host_message: str | None, everyone_message: str) -> bool:
        return False

    @property
    def debugging_string(self) -> str:
        if not self.call_sid:
            return "<Twilio: no call ID>"
        return f"Twilio Call {self.call_sid} ({self.provider_bot_url})"

    @property
    def provider_bot_url(self) -> str:
        if not self.call_sid:
            return "<Twilio: no call ID>"
        return f"https://go/twiliocall/{self.call_sid}"

    @property
    def processing_task(self) -> BotProcessingTask:
        # Avoid a circular import
        from deepinsights.meetingsapp.tasks import process_phone_call_recording

        return process_phone_call_recording
