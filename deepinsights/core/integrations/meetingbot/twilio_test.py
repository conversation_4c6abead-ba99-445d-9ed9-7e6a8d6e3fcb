import datetime
from unittest.mock import <PERSON><PERSON><PERSON><PERSON>, MagicMock, patch

import phonenumbers
import pytest
from pytest_django.fixtures import SettingsWrapper
from twilio.base.exceptions import TwilioRestException

from deepinsights.core.integrations.meetingbot.bot_controller import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BotResponse
from deepinsights.core.integrations.meetingbot.twilio import TwilioCallBotController, TwilioConferenceBotController


@pytest.mark.parametrize(
    "bot_controller_cls",
    [
        TwilioConferenceBotController,
        TwilioCallBotController,
    ],
)
def test_twilio_bot_controller_get_bot_transcript(bot_controller_cls: type[BotController]) -> None:
    assert bot_controller_cls().get_bot_transcript() == []


@pytest.mark.parametrize(
    "bot_controller_cls",
    [
        TwilioConferenceBotController,
        TwilioCallBotController,
    ],
)
def test_twilio_bot_controller_bot_has_speaker_timeline_data(bot_controller_cls: type[<PERSON><PERSON><PERSON><PERSON>roll<PERSON>]) -> None:
    assert not bot_controller_cls().bot_has_speaker_timeline_data()


@pytest.mark.parametrize(
    "bot_controller_cls",
    [
        TwilioConferenceBotController,
        TwilioCallBotController,
    ],
)
def test_twilio_bot_controller_pause_recording(bot_controller_cls: type[BotController]) -> None:
    response = bot_controller_cls().pause_recording()
    assert response == BotResponse(status=501, details={"message": "Not implemented"})


@pytest.mark.parametrize(
    "bot_controller_cls",
    [
        TwilioConferenceBotController,
        TwilioCallBotController,
    ],
)
def test_twilio_bot_controller_resume_recording(bot_controller_cls: type[BotController]) -> None:
    response = bot_controller_cls().resume_recording()
    assert response == BotResponse(status=501, details={"message": "Not implemented"})


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_twilio_call_bot_controller_delete_bot_success(mock_client: MagicMock) -> None:
    mock_delete = mock_client.return_value.calls.get.return_value.delete
    mock_delete.return_value = None
    controller = TwilioCallBotController(bot_id="test_call_sid")
    response = controller.delete_bot()
    assert response == BotResponse(status=204, details={"message": "Bot deleted"})
    mock_delete.assert_called_once()
    assert not controller.bot_id


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_twilio_call_bot_controller_delete_bot_failure(mock_client: MagicMock) -> None:
    mock_delete = mock_client.return_value.calls.get.return_value.delete
    mock_delete.side_effect = TwilioRestException(status=500, uri="")
    controller = TwilioCallBotController(bot_id="test_call_sid")
    response = controller.delete_bot()
    assert response == BotResponse(status=500, details={"message": "Error deleting call"})
    mock_delete.assert_called_once()
    assert controller.bot_id == "test_call_sid"


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_twilio_conference_bot_controller_delete_bot_success(mock_client: MagicMock) -> None:
    mock_delete = mock_client.return_value.conferences.get.return_value.delete
    mock_delete.return_value = None
    controller = TwilioConferenceBotController(bot_id="test_conference_sid")
    response = controller.delete_bot()
    assert response == BotResponse(status=204, details={"message": "Bot deleted"})
    mock_delete.assert_called_once()
    assert not controller.bot_id


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_twilio_conference_bot_controller_delete_bot_failure(mock_client: MagicMock) -> None:
    mock_delete = mock_client.return_value.conferences.get.return_value.delete
    mock_delete.side_effect = TwilioRestException(status=500, uri="")
    controller = TwilioConferenceBotController(bot_id="test_conference_sid")
    response = controller.delete_bot()
    assert response == BotResponse(status=500, details={"message": "Error deleting conference"})
    mock_delete.assert_called_once()
    assert controller.bot_id == "test_conference_sid"


@pytest.mark.parametrize(
    "bot_controller_cls",
    [
        TwilioConferenceBotController,
        TwilioCallBotController,
    ],
)
def test_twilio_bot_controller_send_chat_message(bot_controller_cls: type[BotController]) -> None:
    response = bot_controller_cls().send_chat_message(None, "test")
    assert not response


@pytest.mark.asyncio
@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
async def test_add_participant_if_not_in_call_already_in_call(
    mock_client: MagicMock, settings: SettingsWrapper
) -> None:
    settings.TWILO_PHONE_NUMBER = "+12125551211"
    mock_participant_list = AsyncMock()
    mock_participant_list.list_async.return_value = [MagicMock(label="+12125551212"), MagicMock(label="+12125551214")]
    mock_conference = mock_client.return_value.conferences.get.return_value
    mock_conference.participants = mock_participant_list

    controller = TwilioConferenceBotController(bot_id="test_conference_sid")
    participant_number = phonenumbers.parse("2125551212", "US")

    result = await controller.add_participant_if_not_in_call(participant_number, None)

    assert not result
    mock_participant_list.list_async.assert_called_once()
    mock_conference.participants.create_async.assert_not_called()


@pytest.mark.asyncio
@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
async def test_add_participant_if_not_in_call_success(mock_client: MagicMock, settings: SettingsWrapper) -> None:
    settings.TWILIO_PHONE_NUMBER = "+12125551211"
    mock_participant_list = AsyncMock(list_async=AsyncMock())
    mock_participant_list.list_async.return_value = []
    mock_conference = mock_client.return_value.conferences.get.return_value
    mock_conference.participants = mock_participant_list
    mock_create_async = mock_conference.participants.create_async
    mock_create_async.return_value = None
    mock_outgoing_caller_ids = mock_client.return_value.outgoing_caller_ids
    mock_outgoing_caller_ids.list_async = AsyncMock(return_value=[])

    controller = TwilioConferenceBotController(bot_id="test_conference_sid")
    participant_number = phonenumbers.parse("2125551212", "US")

    result = await controller.add_participant_if_not_in_call(participant_number, None)

    assert result
    mock_participant_list.list_async.assert_called_once()
    mock_create_async.assert_called_once_with(from_="+12125551211", to="+12125551212", label="+12125551212")


@pytest.mark.asyncio
@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
async def test_add_participant_if_not_in_call_success_using_user_number(
    mock_client: MagicMock, settings: SettingsWrapper
) -> None:
    settings.TWILIO_PHONE_NUMBER = "+12125551211"
    mock_participant_list = AsyncMock(list_async=AsyncMock())
    mock_participant_list.list_async.return_value = []
    mock_conference = mock_client.return_value.conferences.get.return_value
    mock_conference.participants = mock_participant_list
    mock_create_async = mock_conference.participants.create_async
    mock_create_async.return_value = None
    mock_outgoing_caller_ids = mock_client.return_value.outgoing_caller_ids
    mock_outgoing_caller_ids.list_async = AsyncMock(
        return_value=[MagicMock(phone_number="+12125551111"), MagicMock(phone_number="+12125551213")]
    )

    controller = TwilioConferenceBotController(bot_id="test_conference_sid")
    participant_number = phonenumbers.parse("2125551212", "US")
    user_number = phonenumbers.parse("2125551213", "US")

    result = await controller.add_participant_if_not_in_call(participant_number, user_number)

    assert result
    mock_participant_list.list_async.assert_called_once()
    mock_create_async.assert_called_once_with(from_="+12125551213", to="+12125551212", label="+12125551212")


@pytest.mark.asyncio
@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
async def test_add_participant_if_not_in_call_success_user_number_not_verified(
    mock_client: MagicMock, settings: SettingsWrapper
) -> None:
    settings.TWILIO_PHONE_NUMBER = "+12125551211"
    mock_participant_list = AsyncMock(list_async=AsyncMock())
    mock_participant_list.list_async.return_value = []
    mock_conference = mock_client.return_value.conferences.get.return_value
    mock_conference.participants = mock_participant_list
    mock_create_async = mock_conference.participants.create_async
    mock_create_async.return_value = None
    mock_outgoing_caller_ids = mock_client.return_value.outgoing_caller_ids
    mock_outgoing_caller_ids.list_async = AsyncMock(
        return_value=[MagicMock(phone_number="+12125551111"), MagicMock(phone_number="+12125551212")]
    )

    controller = TwilioConferenceBotController(bot_id="test_conference_sid")
    participant_number = phonenumbers.parse("2125551212", "US")
    user_number = phonenumbers.parse("2125551213", "US")

    result = await controller.add_participant_if_not_in_call(participant_number, user_number)

    assert result
    mock_participant_list.list_async.assert_called_once()
    mock_create_async.assert_called_once_with(from_="+12125551211", to="+12125551212", label="+12125551212")


@pytest.mark.asyncio
@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
async def test_add_participant_if_not_in_call_twilio_get_exception(mock_client: MagicMock) -> None:
    mock_client.return_value.conferences.get.side_effect = TwilioRestException(status=500, uri="")

    controller = TwilioConferenceBotController(bot_id="test_conference_sid")
    participant_number = phonenumbers.parse("2125551212", "US")

    with pytest.raises(TwilioRestException):
        result = await controller.add_participant_if_not_in_call(participant_number, None)
        assert result is None
    mock_client.return_value.conferences.get.return_value.participants.list_async.assert_not_called()


@pytest.mark.asyncio
@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
async def test_add_participant_if_not_in_call_twilio_participants_exception(mock_client: MagicMock) -> None:
    mock_client.return_value.conferences.get.return_value.participants.list_async.side_effect = TwilioRestException(
        status=500, uri=""
    )

    controller = TwilioConferenceBotController(bot_id="test_conference_sid")
    participant_number = phonenumbers.parse("2125551212", "US")

    with pytest.raises(TwilioRestException):
        result = await controller.add_participant_if_not_in_call(participant_number, None)
        assert result is None
    mock_client.return_value.conferences.get.assert_called_once()
    mock_client.return_value.conferences.get.return_value.participants.list_async.assert_called_once()


def test_twilio_conference_bot_controller_create_or_update_scheduled_bot(settings: SettingsWrapper) -> None:
    settings.TWILIO_PHONE_NUMBER = "+12125551212"

    response = TwilioConferenceBotController().create_or_update_scheduled_bot(
        meeting_url="+12125551212",
        join_time=datetime.datetime.now(datetime.timezone.utc),
        bot_preferences=MagicMock(),
        enable_live_transcription=False,
        enable_native_zoom_bot=False,
        bot_base_name="Test Bot",
        user_phone_number=phonenumbers.parse("+12125551214", "US"),
    )

    assert response.status == 400
    assert response.details["message"] == "Twilio conference bots do not support scheduling"


def test_twilio_conference_bot_controller_create_bot_invalid_twilio_phone_number(settings: SettingsWrapper) -> None:
    settings.TWILIO_PHONE_NUMBER = "invalid"

    response = TwilioConferenceBotController().create_bot_and_start_recording(
        meeting_url="+12125551212",
        bot_preferences=MagicMock(),
        enable_live_transcription=False,
        enable_native_zoom_bot=False,
        bot_base_name="Test Bot",
        user_phone_number=phonenumbers.parse("+12125551214", "US"),
    )

    assert response.status == 400
    assert response.details["message"] == "Invalid phone number"


def test_twilio_conference_bot_controller_create_bot_invalid_client_phone_number(settings: SettingsWrapper) -> None:
    settings.TWILIO_PHONE_NUMBER = "+12125551212"

    response = TwilioConferenceBotController().create_bot_and_start_recording(
        meeting_url="invalid",
        bot_preferences=MagicMock(),
        enable_live_transcription=False,
        enable_native_zoom_bot=False,
        bot_base_name="Test Bot",
        user_phone_number=phonenumbers.parse("+12125551214", "US"),
    )

    assert response.status == 400
    assert response.details["message"] == "Invalid phone number"


def test_twilio_conference_bot_controller_missing_user_phone_number(settings: SettingsWrapper) -> None:
    settings.TWILIO_PHONE_NUMBER = "+12125551212"

    response = TwilioConferenceBotController().create_bot_and_start_recording(
        meeting_url="+12125551213",
        bot_preferences=MagicMock(),
        enable_live_transcription=False,
        enable_native_zoom_bot=False,
        bot_base_name="Test Bot",
    )

    assert response.status == 400
    assert response.details["message"] == "User phone number is required for conference"


def test_twilio_call_bot_controller_create_or_update_scheduled_bot(settings: SettingsWrapper) -> None:
    settings.TWILIO_PHONE_NUMBER = "+12125551212"
    controller = TwilioCallBotController()

    response = controller.create_or_update_scheduled_bot(
        meeting_url="+12125551213",
        join_time=datetime.datetime.now(datetime.timezone.utc),
        bot_preferences=MagicMock(),
        enable_live_transcription=False,
        enable_native_zoom_bot=False,
        bot_base_name="Test Bot",
        user_phone_number=phonenumbers.parse("+12125551214", "US"),
    )

    assert response.status == 400
    assert response.details["message"] == "Twilio call bots do not support scheduling"


def test_twilio_call_bot_controller_create_bot_invalid_twilio_phone_number(settings: SettingsWrapper) -> None:
    settings.TWILIO_PHONE_NUMBER = "invalid"
    controller = TwilioCallBotController()
    bot_preferences = MagicMock()

    response = controller.create_bot_and_start_recording(
        meeting_url="+12125551213",
        bot_preferences=bot_preferences,
        enable_live_transcription=False,
        enable_native_zoom_bot=False,
        bot_base_name="Test Bot",
        user_phone_number=phonenumbers.parse("+12125551214", "US"),
    )

    assert response.status == 400
    assert response.details["message"] == "Invalid phone number"


def test_twilio_call_bot_controller_create_bot_invalid_client_phone_number(settings: SettingsWrapper) -> None:
    settings.TWILIO_PHONE_NUMBER = "+12125551212"
    controller = TwilioCallBotController()
    bot_preferences = MagicMock()

    response = controller.create_bot_and_start_recording(
        meeting_url="invalid",
        bot_preferences=bot_preferences,
        enable_live_transcription=False,
        enable_native_zoom_bot=False,
        bot_base_name="Test Bot",
        user_phone_number=phonenumbers.parse("+12125551214", "US"),
    )

    assert response.status == 400
    assert response.details["message"] == "Invalid phone number"


@pytest.mark.asyncio
@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
async def test_make_recording_announcement_success(mock_client: MagicMock, settings: SettingsWrapper) -> None:
    settings.WEBHOOK_DOMAIN = "https://example.com"
    matching_mock_participant = MagicMock(label="+12125551212")
    mock_participant_list = AsyncMock(list_async=AsyncMock())
    mock_participant_list.list_async.return_value = [
        MagicMock(label="+12125551213"),
        matching_mock_participant,
        MagicMock(label="+12125551214"),
    ]
    mock_conference = mock_client.return_value.conferences.get.return_value
    mock_conference.participants = mock_participant_list

    matching_mock_participant.update_async = AsyncMock()

    controller = TwilioConferenceBotController(bot_id="test_conference_sid")
    participant_number = phonenumbers.parse("+12125551212", "US")

    await controller.make_recording_announcement(participant_number)

    mock_participant_list.list_async.assert_called_once()
    matching_mock_participant.update_async.assert_called_once_with(
        announce_url="https://example.com/api/v2/bot/twilio/on_participant_join"
    )


@pytest.mark.asyncio
@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
async def test_make_recording_announcement_no_matching_participant(mock_client: MagicMock) -> None:
    participants = [
        MagicMock(label="+12125551213"),
        MagicMock(label="+12125551214"),
    ]
    mock_participant_list = AsyncMock()
    mock_participant_list.list_async.return_value = participants
    mock_conference = mock_client.return_value.conferences.get.return_value
    mock_conference.participants = mock_participant_list

    controller = TwilioConferenceBotController(bot_id="test_conference_sid")
    participant_number = phonenumbers.parse("+12125551212", "US")

    await controller.make_recording_announcement(participant_number)

    mock_participant_list.list_async.assert_called_once()
    for participant in participants:
        participant.update_async.assert_not_called()


@pytest.mark.asyncio
@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
async def test_make_recording_announcement_twilio_exception(
    mock_client: MagicMock, caplog: pytest.LogCaptureFixture
) -> None:
    mock_participant_list = AsyncMock()
    mock_participant_list.list_async.side_effect = TwilioRestException(status=500, uri="")
    mock_conference = mock_client.return_value.conferences.get.return_value
    mock_conference.participants = mock_participant_list

    controller = TwilioConferenceBotController(bot_id="test_conference_sid")
    participant_number = phonenumbers.parse("+12125551212", "US")

    with caplog.at_level("ERROR"):
        await controller.make_recording_announcement(participant_number)
        assert len(caplog.records) == 1
        assert (
            caplog.messages[0]
            == "Error making recording announcement for Twilio Conference test_conference_sid (https://go/twilioconference/test_conference_sid)"
        )

    mock_participant_list.list_async.assert_called_once()
