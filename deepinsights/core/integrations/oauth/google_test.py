import datetime
import logging
from datetime import timezone
from typing import Any
from unittest.mock import AN<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ock, <PERSON>Mock, patch

import google.oauth2
import google.oauth2.credentials
import pytest
from googleapiclient.errors import HttpError
from pytest_django.fixtures import SettingsWrapper

from deepinsights.core.integrations.oauth.google import GoogleOAuth
from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.users.models.user import User


def test_get_client_info(settings: SettingsWrapper) -> None:
    settings.GOOGLE_CLIENT_ID = "client_id"
    settings.GOOGLE_CLIENT_SECRET = "client_secret"
    assert GoogleOAuth.client_id() == "client_id"
    assert GoogleOAuth.client_secret() == "client_secret"


@patch("deepinsights.core.integrations.oauth.google.Flow")
@patch("deepinsights.core.integrations.oauth.google.settings")
def test_exchange_tokens_error(mock_flow: <PERSON><PERSON><PERSON>, django_user_model: User, settings: SettingsWrapper) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    settings.GOOGLE_CLIENT_ID = "client_id"
    settings.GOOGLE_CLIENT_SECRET = "client_secret"
    mock_flow.from_client_config.return_value.fetch_token.side_effect = Exception("Error")

    with pytest.raises(Exception, match="Error exchanging tokens"):
        GoogleOAuth().exchange_tokens("auth_code", user, "http://localhost/redirect")


@patch("deepinsights.core.integrations.oauth.google.Flow")
def test_exchange_tokens(mock_flow: MagicMock, django_user_model: User, settings: SettingsWrapper) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    settings.GOOGLE_CLIENT_ID = "client_id"
    settings.GOOGLE_CLIENT_SECRET = "client_secret"
    expiry = datetime.datetime.now(timezone.utc)
    mock_from_client_config = mock_flow.from_client_config
    mock_fetch_token = mock_flow.from_client_config.return_value.fetch_token
    mock_from_client_config.return_value.credentials = google.oauth2.credentials.Credentials(  # type: ignore[no-untyped-call]
        "access_token",
        refresh_token="refresh_token",
        expiry=expiry,
        scopes=[
            "https://www.googleapis.com/auth/calendar.readonly",
            "https://www.googleapis.com/auth/calendar.readwrite",
        ],
    )
    mock_fetch_token.return_value = "token"

    GoogleOAuth().exchange_tokens("auth_code", user, "http://localhost/redirect")

    mock_from_client_config.assert_called_once_with(
        {
            "web": {
                "client_id": "client_id",
                "client_secret": "client_secret",
                "redirect_uris": ["http://localhost/redirect"],
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://accounts.google.com/o/oauth2/token",
            }
        },
        scopes=["https://www.googleapis.com/auth/calendar.readonly"],
    )
    mock_fetch_token.assert_called_once_with(code="auth_code")

    credentials = OAuthCredentials.objects.get(user=user, integration="google")
    assert credentials.access_token == "access_token"
    assert credentials.refresh_token == "refresh_token"
    assert (
        credentials.scope
        == "https://www.googleapis.com/auth/calendar.readonly https://www.googleapis.com/auth/calendar.readwrite"
    )
    assert credentials.expires_in == expiry
    assert credentials.refresh_token_expires_in > expiry


@patch("deepinsights.core.integrations.oauth.google.Flow")
def test_exchange_tokens_updating_existing_token(
    mock_flow: MagicMock, django_user_model: User, settings: SettingsWrapper
) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="google",
        access_token="old_access_token",
        refresh_token="old_refresh_token",
        scope="",
        expires_in=datetime.datetime.now(timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(timezone.utc),
    )

    settings.GOOGLE_CLIENT_ID = "client_id"
    settings.GOOGLE_CLIENT_SECRET = "client_secret"
    expiry = datetime.datetime.now(timezone.utc)
    mock_from_client_config = mock_flow.from_client_config
    mock_fetch_token = mock_flow.from_client_config.return_value.fetch_token
    mock_from_client_config.return_value.credentials = google.oauth2.credentials.Credentials(  # type: ignore[no-untyped-call]
        "access_token",
        refresh_token="refresh_token",
        expiry=expiry,
        scopes=[
            "https://www.googleapis.com/auth/calendar.readonly",
            "https://www.googleapis.com/auth/calendar.readwrite",
        ],
    )
    mock_fetch_token.return_value = "token"

    GoogleOAuth().exchange_tokens("auth_code", user, "http://localhost/redirect")

    mock_from_client_config.assert_called_once_with(
        {
            "web": {
                "client_id": "client_id",
                "client_secret": "client_secret",
                "redirect_uris": ["http://localhost/redirect"],
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://accounts.google.com/o/oauth2/token",
            }
        },
        scopes=["https://www.googleapis.com/auth/calendar.readonly"],
    )
    mock_fetch_token.assert_called_once_with(code="auth_code")

    credentials = OAuthCredentials.objects.get(user=user, integration="google")
    assert credentials.access_token == "access_token"
    assert credentials.refresh_token == "refresh_token"
    assert (
        credentials.scope
        == "https://www.googleapis.com/auth/calendar.readonly https://www.googleapis.com/auth/calendar.readwrite"
    )
    assert credentials.expires_in == expiry
    assert credentials.refresh_token_expires_in > expiry


@patch("deepinsights.core.integrations.oauth.google.Flow")
def test_exchange_tokens_updating_existing_token_without_refresh_token(
    mock_flow: MagicMock, django_user_model: User, settings: SettingsWrapper
) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="google",
        access_token="old_access_token",
        refresh_token="old_refresh_token",
        scope="",
        expires_in=datetime.datetime.now(timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(timezone.utc),
    )

    settings.GOOGLE_CLIENT_ID = "client_id"
    settings.GOOGLE_CLIENT_SECRET = "client_secret"
    expiry = datetime.datetime.now(timezone.utc)
    mock_from_client_config = mock_flow.from_client_config
    mock_fetch_token = mock_flow.from_client_config.return_value.fetch_token
    mock_from_client_config.return_value.credentials = google.oauth2.credentials.Credentials(  # type: ignore[no-untyped-call]
        "access_token",
        expiry=expiry,
        scopes=[
            "https://www.googleapis.com/auth/calendar.readonly",
            "https://www.googleapis.com/auth/calendar.readwrite",
        ],
    )
    mock_fetch_token.return_value = "token"

    GoogleOAuth().exchange_tokens("auth_code", user, "http://localhost/redirect")

    mock_from_client_config.assert_called_once_with(
        {
            "web": {
                "client_id": "client_id",
                "client_secret": "client_secret",
                "redirect_uris": ["http://localhost/redirect"],
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://accounts.google.com/o/oauth2/token",
            }
        },
        scopes=["https://www.googleapis.com/auth/calendar.readonly"],
    )
    mock_fetch_token.assert_called_once_with(code="auth_code")

    credentials = OAuthCredentials.objects.get(user=user, integration="google")
    assert credentials.refresh_token == "old_refresh_token"


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
async def test_get_access_token_does_not_exist(django_user_model: User, caplog: pytest.LogCaptureFixture) -> None:
    user = await django_user_model.objects.acreate(username="<EMAIL>", email="<EMAIL>")
    caplog.set_level(logging.ERROR)
    assert not await GoogleOAuth().get_access_token(user)
    assert not caplog.records


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch("deepinsights.core.integrations.oauth.google.OAuthCredentials")
async def test_get_access_token_error(
    mock_oauth_credentials: MagicMock, django_user_model: User, caplog: pytest.LogCaptureFixture
) -> None:
    user = await django_user_model.objects.acreate(username="<EMAIL>", email="<EMAIL>")
    mock_oauth_credentials.objects.aget = AsyncMock()
    p = PropertyMock(side_effect=Exception("Error"), return_value=None)
    type(mock_oauth_credentials.objects.aget.return_value).expires_in = p

    caplog.set_level(logging.ERROR)
    with pytest.raises(Exception):
        assert not await GoogleOAuth().get_access_token(user)
    assert len(caplog.records) == 1
    assert "Error getting access token" in caplog.messages[0]


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
async def test_get_access_token(django_user_model: User) -> None:
    user = await django_user_model.objects.acreate(username="<EMAIL>", email="<EMAIL>")
    await OAuthCredentials.objects.acreate(
        user=user,
        integration="google",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(timezone.utc) + datetime.timedelta(hours=1),
        refresh_token_expires_in=datetime.datetime.now(timezone.utc),
    )

    assert await GoogleOAuth().get_access_token(user) == "access_token"


@pytest.mark.django_db(transaction=True)
def test_get_refresh_token_does_not_exist(django_user_model: User, caplog: pytest.LogCaptureFixture) -> None:
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    caplog.set_level(logging.ERROR)
    assert not GoogleOAuth().get_refresh_token(user)
    assert not caplog.records


@pytest.mark.django_db(transaction=True)
@patch("deepinsights.core.integrations.oauth.google.OAuthCredentials")
def test_get_refresh_token_error(
    mock_oauth_credentials: MagicMock, django_user_model: User, caplog: pytest.LogCaptureFixture
) -> None:
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    p = PropertyMock(side_effect=Exception("Error"), return_value=None)
    type(mock_oauth_credentials.objects.get.return_value).expires_in = p

    caplog.set_level(logging.ERROR)
    with pytest.raises(Exception):
        assert not GoogleOAuth().get_refresh_token(user)
    assert not caplog.records


@pytest.mark.django_db(transaction=True)
def test_get_refresh_token(django_user_model: User) -> None:
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="google",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(timezone.utc) + datetime.timedelta(hours=1),
        refresh_token_expires_in=datetime.datetime.now(timezone.utc),
    )

    assert GoogleOAuth().get_refresh_token(user) == "refresh_token"


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch("deepinsights.core.integrations.oauth.google.google.oauth2.credentials.Credentials")
async def test_get_access_token_refresh_error(
    mock_credentials: MagicMock, django_user_model: User, settings: SettingsWrapper
) -> None:
    user = await django_user_model.objects.acreate(username="<EMAIL>", email="<EMAIL>")
    await OAuthCredentials.objects.acreate(
        user=user,
        integration="google",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(timezone.utc),
    )
    settings.GOOGLE_CLIENT_ID = "client_id"
    settings.GOOGLE_CLIENT_SECRET = "client_secret"

    mock_credentials.return_value.refresh.side_effect = Exception("Error")

    with pytest.raises(Exception):
        assert not await GoogleOAuth().get_access_token(user)


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch("deepinsights.core.integrations.oauth.google.google.oauth2.credentials.Credentials")
async def test_get_access_token_refresh(
    mock_credentials: MagicMock, django_user_model: User, settings: SettingsWrapper
) -> None:
    user = await django_user_model.objects.acreate(username="<EMAIL>", email="<EMAIL>")
    await OAuthCredentials.objects.acreate(
        user=user,
        integration="google",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(timezone.utc),
    )
    settings.GOOGLE_CLIENT_ID = "client_id"
    settings.GOOGLE_CLIENT_SECRET = "client_secret"

    new_expiry = datetime.datetime.now(tz=timezone.utc) + datetime.timedelta(hours=1)

    def update_credentials(_: Any) -> None:
        mock_credentials.return_value.token = "new_access_token"
        mock_credentials.return_value.expiry = new_expiry

    mock_credentials.return_value.refresh.side_effect = update_credentials

    assert await GoogleOAuth().get_access_token(user) == "new_access_token"
    credentials = await OAuthCredentials.objects.aget(user=user, integration="google")
    assert credentials.access_token == "new_access_token"
    assert credentials.expires_in == new_expiry


@patch("deepinsights.core.integrations.oauth.google.build")
def test_get_user_info_success(mock_build: MagicMock) -> None:
    user_info = {"id": "123", "email": "<EMAIL>"}
    mock_service = MagicMock()
    mock_service.userinfo.return_value.get.return_value.execute.return_value = user_info
    mock_build.return_value = mock_service

    result = GoogleOAuth.get_user_info("access_token")

    assert result == user_info
    mock_build.assert_called_once_with("oauth2", "v2", credentials=ANY)
    mock_service.userinfo.return_value.get.return_value.execute.assert_called_once()


@patch("deepinsights.core.integrations.oauth.google.build")
def test_get_user_info_http_error(mock_build: MagicMock) -> None:
    mock_service = MagicMock()
    mock_service.userinfo.return_value.get.return_value.execute.side_effect = HttpError(MagicMock(), b"Error")
    mock_build.return_value = mock_service

    result = GoogleOAuth.get_user_info("access_token")

    assert not result
    mock_build.assert_called_once_with("oauth2", "v2", credentials=ANY)
    mock_service.userinfo.return_value.get.return_value.execute.assert_called_once()


@patch("deepinsights.core.integrations.oauth.google.build")
def test_get_user_info_other_exception(mock_build: MagicMock) -> None:
    mock_service = MagicMock()
    mock_service.userinfo.return_value.get.return_value.execute.side_effect = Exception("Some other error")
    mock_build.return_value = mock_service

    result = GoogleOAuth.get_user_info("access_token")

    assert not result
    mock_build.assert_called_once_with("oauth2", "v2", credentials=ANY)
    mock_service.userinfo.assert_called_once()
    mock_service.userinfo.return_value.get.assert_called_once()
    mock_service.userinfo.return_value.get.return_value.execute.assert_called_once()
