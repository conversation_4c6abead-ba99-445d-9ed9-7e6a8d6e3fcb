from abc import abstractmethod
from typing import Protocol

from deepinsights.users.models.user import User

class OAuthCalendarIntegrationProtocol(Protocol):
    """A protocol for OAuth integrations that are used for calendar integration."""

    @classmethod
    @abstractmethod
    def client_id(cls) -> str:
        pass

    @classmethod
    @abstractmethod
    def client_secret(cls) -> str:
        pass

    @abstractmethod
    def exchange_tokens(self, authorization_code: str, user: User, redirect_uri: str) -> None:
        pass

    @abstractmethod
    def get_refresh_token(self, user: User) -> str | None:
        pass
