import datetime
import logging
from typing import Annotated, Optional

import msal
import pydantic
from django.conf import settings
from django.utils import timezone

from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.users.models.user import User


# A model representing a Microsoft Dynamics token exchange response
class _TokenResponse(pydantic.BaseModel):
    access_token: Annotated[str, pydantic.StringConstraints(min_length=1)]
    refresh_token: str | None = None
    expires_in: pydantic.PositiveInt
    scope: str | None = None
    resource: str | None = None


class MicrosoftDynamicsOAuth:
    def __init__(self, resource_url: str) -> None:
        self.app = msal.ConfidentialClientApplication(
            client_id=self.client_id(), client_credential=self.client_secret()
        )

        self.resource_url = resource_url
        # Dynamics CRM requires these scopes
        self.scopes = [f"{resource_url}/user_impersonation"]

    @classmethod
    def client_id(cls) -> str:
        return settings.MICROSOFT_DYNAMICS_CLIENT_ID  # type: ignore[no-any-return]

    @classmethod
    def client_secret(cls) -> str:
        return settings.MICROSOFT_DYNAMICS_CLIENT_SECRET  # type: ignore[no-any-return]

    def exchange_tokens(self, authorization_code: str, user: User, redirect_uri: str) -> None:
        try:
            response = self.app.acquire_token_by_authorization_code(
                authorization_code,
                scopes=self.scopes,
                redirect_uri=redirect_uri,
            )

            if response.get("error"):
                raise Exception(f"Error exchanging tokens: {response}")

            parsed_response = _TokenResponse.model_validate(response, strict=True)

            if not parsed_response.refresh_token:
                raise Exception("Missing refresh token")

            expiry_date = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(
                seconds=parsed_response.expires_in
            )

            # Save the OAuth credentials
            OAuthCredentials.objects.update_or_create(
                user=user,
                integration="microsoft_dynamics",
                defaults={
                    "access_token": parsed_response.access_token,
                    "refresh_token": parsed_response.refresh_token,
                    "expires_in": expiry_date,
                    "refresh_token_expires_in": datetime.datetime.now(datetime.timezone.utc)
                    + datetime.timedelta(days=90),  # Typical Dynamics refresh token lifetime
                    "scope": parsed_response.scope or " ".join(self.scopes),
                },
            )

        except Exception as e:
            logging.error("Error exchanging tokens for Microsoft Dynamics: %s for user %s", e, user)
            raise Exception("Error exchanging tokens for Microsoft Dynamics") from e

    def _refresh_access_token(self, oauth_credentials: OAuthCredentials) -> str:
        try:
            response = self.app.acquire_token_by_refresh_token(oauth_credentials.refresh_token, scopes=self.scopes)

            if response.get("error"):
                raise Exception("Error refreshing token: %s ", response)

            parsed_response = _TokenResponse.model_validate(response)

            oauth_credentials.access_token = parsed_response.access_token
            if parsed_response.refresh_token:
                oauth_credentials.refresh_token = parsed_response.refresh_token
            oauth_credentials.expires_in = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(
                seconds=parsed_response.expires_in
            )
            if parsed_response.scope:
                oauth_credentials.scope = parsed_response.scope

            oauth_credentials.save()
            logging.info("Refreshed Microsoft Dynamics OAuth credentials: %s", oauth_credentials.uuid)

            return oauth_credentials.access_token
        except Exception as e:
            logging.error("Error refreshing Microsoft Dynamics access token: %s", e)
            raise Exception("Error refreshing access token") from e

    def get_access_token(self, user: User) -> Optional[str]:
        try:
            oauth_credentials = OAuthCredentials.objects.get(user=user, integration="microsoft_dynamics")
            expiry = oauth_credentials.expires_in

            if expiry < timezone.now():
                logging.info("Microsoft Dynamics access token expired, refreshing for user: %s", user.uuid)
                access_token = self._refresh_access_token(oauth_credentials)
                return access_token
            return oauth_credentials.access_token
        except OAuthCredentials.DoesNotExist:
            logging.info("No Microsoft Dynamics credentials found for user: %s", user.uuid)
            return None
        except Exception as e:
            logging.error("Error getting Microsoft Dynamics access token: %s", e)
            raise Exception("Error getting access token") from e

    def check_integration_status(self, user: User) -> bool:
        try:
            oauth_credentials = OAuthCredentials.objects.get(user=user, integration="microsoft_dynamics")
            refresh_token_expiry = oauth_credentials.refresh_token_expires_in
            return refresh_token_expiry > timezone.now()
        except OAuthCredentials.DoesNotExist:
            return False
