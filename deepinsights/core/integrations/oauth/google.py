import datetime
import logging
from typing import Any, Mapping

import google.oauth2.credentials
from django.conf import settings
from django.utils import timezone
from google.auth.transport.requests import Request as GoogleTransportRequest
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build

from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.users.models.user import User


# Performs OAuth code and token exchanges for the Google OAuth2 integration.
class GoogleOAuth:
    def __init__(self) -> None:
        self.scopes = ["https://www.googleapis.com/auth/calendar.readonly"]

    # The client ID used for Google OAuth token exchanges.
    @classmethod
    def client_id(cls) -> str:
        return settings.GOOGLE_CLIENT_ID  # type: ignore[no-any-return]

    # The client secret used for Google OAuth token exchanges.
    @classmethod
    def client_secret(cls) -> str:
        return settings.GOOGLE_CLIENT_SECRET  # type: ignore[no-any-return]

    # Validates a Google access token, returning the user information if valid.
    @classmethod
    def get_user_info(cls, access_token: str) -> Mapping[str, Any] | None:
        try:
            creds = google.oauth2.credentials.Credentials(access_token)  # type: ignore[no-untyped-call]
            return build("oauth2", "v2", credentials=creds).userinfo().get().execute()
        except Exception as e:
            logging.error("Error validating Google access token", exc_info=e)
            return None

    # Exchanges an authorization code for an access token and (potentially) refresh token.
    #
    # Not all Google OAuth2 token exchanges will return a refresh token, so it is possible that
    # there will not be a refresh token and that refreshes will not be possible. See
    # https://stackoverflow.com/questions/10827920/not-receiving-google-oauth-refresh-token for
    # some details about when a refresh token will not be returned in the token exchange.
    def exchange_tokens(self, authorization_code: str, user: User, redirect_uri: str) -> None:
        try:
            flow = Flow.from_client_config(
                {
                    "web": {
                        "client_id": self.client_id(),
                        "client_secret": self.client_secret(),
                        "redirect_uris": [redirect_uri],
                        "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                        "token_uri": "https://accounts.google.com/o/oauth2/token",
                    }
                },
                scopes=self.scopes,
            )
            flow.redirect_uri = redirect_uri

            flow.fetch_token(code=authorization_code)
            credentials = flow.credentials
            defaults = {
                "access_token": credentials.token,
                "expires_in": credentials.expiry,
                "scope": " ".join(credentials.scopes),
                # This isn't used by this integration, but it must be set to a non-nil value.
                "refresh_token_expires_in": datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=90),
            }
            if flow.credentials.refresh_token:
                defaults["refresh_token"] = credentials.refresh_token

            OAuthCredentials.objects.update_or_create(
                user=user,
                integration="google",
                defaults=defaults,
            )
        except Exception as e:
            logging.error(f"Error exchanging tokens: {e} for user {user}")
            raise Exception("Error exchanging tokens") from e

    # Attemps to refresh access tokens using a refresh token.
    #
    # Returns the updated access token if successful, and stores it in the database.
    async def _refresh_access_token(self, oauth_credentials: OAuthCredentials) -> str | None:
        try:
            credentials = google.oauth2.credentials.Credentials(  # type: ignore[no-untyped-call]
                oauth_credentials.access_token,
                refresh_token=oauth_credentials.refresh_token,
                token_uri="https://accounts.google.com/o/oauth2/token",
                client_id=settings.GOOGLE_CLIENT_ID,
                client_secret=settings.GOOGLE_CLIENT_SECRET,
            )
            credentials.refresh(GoogleTransportRequest())  # type: ignore[no-untyped-call]
            oauth_credentials.access_token = credentials.token  # type:ignore[assignment]
            oauth_credentials.expires_in = credentials.expiry  # type:ignore[assignment]

            logging.info("Saving refresh OAuth credentials: %s", oauth_credentials.uuid)
            await oauth_credentials.asave()
            return oauth_credentials.access_token
        except Exception as e:
            raise Exception("Error refreshing access token") from e

    # Gets an access token for a user.
    #
    # This will attempt to refresh the token if the token expiration date has passed.
    async def get_access_token(self, user: User) -> str | None:
        try:
            oauth_credentials = await OAuthCredentials.objects.aget(user=user, integration="google")
        except OAuthCredentials.DoesNotExist:
            logging.info("No credentials found for user: %s", user.uuid)
            return None

        try:
            expiry = oauth_credentials.expires_in
            if expiry < timezone.now():
                logging.info("Access token expired, refreshing token for user: %s", user.uuid)
                return await self._refresh_access_token(oauth_credentials)
            else:
                return oauth_credentials.access_token
        except Exception as e:
            logging.error("Error getting access token: %s", e)
            raise Exception("Error getting access token") from e

    # Gets the refresh token for a user.
    #
    # This will only return a saved refresh token; it will not attempt to get a refresh token if one does not exist.
    def get_refresh_token(self, user: User) -> str | None:
        try:
            return OAuthCredentials.objects.get(user=user, integration="google").refresh_token
        except OAuthCredentials.DoesNotExist:
            logging.info("No credentials found for user: %s", user.uuid)
            return None
