import datetime
from datetime import timezone
from typing import Any, Callable
from unittest.mock import ANY, <PERSON>Mock, PropertyMock, patch

import httpx
import pytest
from pytest_django.fixtures import SettingsWrapper

from deepinsights.core.integrations.oauth.salesforce import SalesforceOAuth
from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.users.models.user import User


# Whether or not the given dates are "close enough" to be considered equal for the purposes of this
# test.
def _dates_are_close(date1: datetime.datetime, date2: datetime.datetime) -> bool:
    return abs(date1 - date2) < datetime.timedelta(seconds=1)


def test_initialization():  # type: ignore[no-untyped-def]
    sf_oauth = SalesforceOAuth()
    assert isinstance(sf_oauth.client, type(httpx.Client()))

    mock_client = httpx.Client(transport=httpx.MockTransport(handler=lambda _: httpx.Response(200)))
    sf_oauth_mocked_client = SalesforceOAuth(client=mock_client)
    assert sf_oauth_mocked_client.client == mock_client


def test_exchange_tokens_error(django_user_model: User):  # type: ignore[no-untyped-def]
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    sf_oauth = SalesforceOAuth(
        client=httpx.Client(transport=httpx.MockTransport(handler=lambda _: httpx.Response(400)))
    )

    with pytest.raises(Exception, match="Error exchanging tokens"):
        sf_oauth.exchange_tokens("auth_code", user, "http://localhost/redirect")


def test_exchange_tokens_response_not_json(django_user_model: User):  # type: ignore[no-untyped-def]
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    sf_oauth = SalesforceOAuth(
        client=httpx.Client(transport=httpx.MockTransport(handler=lambda _: httpx.Response(200, text="Not JSON")))
    )

    with pytest.raises(Exception, match="Error exchanging tokens"):
        sf_oauth.exchange_tokens("auth_code", user, "http://localhost/redirect")


@pytest.mark.parametrize(
    ("response"),
    [
        {"error": "Error"},
        {
            "access_token": "access_token",
            "instance_url": "http://instance",
        },
        {
            "access_token": "access_token",
            "refresh_token": "refresh_token",
        },
        {
            "refresh_token": "refresh_token",
            "instance_url": "http://instance",
        },
    ],
)
def test_exchange_tokens_invalid_responses(response: dict[str, str], django_user_model: User):  # type: ignore[no-untyped-def]
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    sf_oauth = SalesforceOAuth(
        client=httpx.Client(transport=httpx.MockTransport(handler=lambda _: httpx.Response(200, json=response)))
    )

    with pytest.raises(Exception):
        sf_oauth.exchange_tokens("auth_code", user, "http://localhost/redirect")


def test_exchange_tokens_request_body(django_user_model: User, settings: SettingsWrapper) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    mock_httpx_client = MagicMock()
    settings.SALESFORCE_CONSUMER_KEY = "client_id"
    settings.SALESFORCE_CONSUMER_SECRET = "client_secret"
    sf_oauth = SalesforceOAuth(client=mock_httpx_client)

    with pytest.raises(Exception, match="Error exchanging tokens"):
        sf_oauth.exchange_tokens("auth_code", user, "http://localhost/redirect")
    mock_httpx_client.post.assert_called_once_with(
        ANY,
        data={
            "client_id": "client_id",
            "client_secret": "client_secret",
            "code": "auth_code",
            "grant_type": "authorization_code",
            "redirect_uri": "http://localhost/redirect",
        },
        headers=ANY,
    )


def test_access_token_introspection_errors(django_user_model: User):  # type: ignore[no-untyped-def]
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    def handler(request):  # type: ignore[no-untyped-def]
        if request.url.path == "/services/oauth2/token":
            return httpx.Response(
                200,
                json={
                    "access_token": "access_token",
                    "refresh_token": "refresh_token",
                    "instance_url": "http://instance",
                },
            )
        return httpx.Response(400)

    sf_oauth = SalesforceOAuth(client=httpx.Client(transport=httpx.MockTransport(handler=handler)))
    sf_oauth.exchange_tokens("auth_code", user, "http://localhost/redirect")

    credentials = OAuthCredentials.objects.get(user=user, integration="salesforce")
    assert credentials.access_token == "access_token"
    assert credentials.refresh_token == "refresh_token"


def test_refresh_token_introspection_error(django_user_model: User):  # type: ignore[no-untyped-def]
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    def handler(request: httpx.Request):  # type: ignore[no-untyped-def]
        if request.url.path == "/services/oauth2/token":
            return httpx.Response(
                200,
                json={
                    "access_token": "access_token",
                    "refresh_token": "refresh_token",
                    "instance_url": "http://instance",
                },
            )
        if "access_token" in str(request.read()):
            return httpx.Response(200, json={"exp": 123})

        return httpx.Response(400)

    sf_oauth = SalesforceOAuth(client=httpx.Client(transport=httpx.MockTransport(handler=handler)))
    sf_oauth.exchange_tokens("auth_code", user, "http://localhost/redirect")

    assert (credentials := OAuthCredentials.objects.get(user=user, integration="salesforce"))
    assert credentials.expires_in == datetime.datetime.fromtimestamp(123, tz=timezone.utc)
    assert credentials.refresh_token_expires_in > credentials.expires_in


@pytest.mark.parametrize(
    ("access_token_response", "refresh_token_response", "access_token_expires_in", "refresh_token_expires_in"),
    [
        ({"error": "Error"}, {"error": "Error"}, None, None),
        ({"exp": "123"}, {"exp": "123"}, None, None),
        ({"exp": 123}, {}, 123, None),
        ({}, {"exp": 456}, None, 456),
    ],
)
def test_token_introspection_invalid_responses(  # type: ignore[no-untyped-def]
    access_token_response: dict[str, Any],
    refresh_token_response: dict[str, Any],
    access_token_expires_in: int | None,
    refresh_token_expires_in: int | None,
    django_user_model: User,
):
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    def handler(request):  # type: ignore[no-untyped-def]
        if request.url.path == "/services/oauth2/token":
            return httpx.Response(
                200,
                json={
                    "access_token": "access_token",
                    "refresh_token": "refresh_token",
                    "instance_url": "http://instance",
                },
            )
        if "access_token" in str(request.read()):
            return httpx.Response(200, json=access_token_response)
        if "refresh_token" in str(request.read()):
            return httpx.Response(200, json=refresh_token_response)
        return httpx.Response(400)

    sf_oauth = SalesforceOAuth(client=httpx.Client(transport=httpx.MockTransport(handler=handler)))
    sf_oauth.exchange_tokens("auth_code", user, "http://localhost/redirect")

    assert (credentials := OAuthCredentials.objects.get(user=user, integration="salesforce"))
    if access_token_expires_in:
        assert credentials.expires_in == datetime.datetime.fromtimestamp(access_token_expires_in, tz=timezone.utc)
    else:
        assert _dates_are_close(
            credentials.expires_in, datetime.datetime.now(tz=timezone.utc) + datetime.timedelta(hours=2)
        )

    if refresh_token_expires_in:
        assert credentials.refresh_token_expires_in == datetime.datetime.fromtimestamp(
            refresh_token_expires_in, tz=timezone.utc
        )
    else:
        assert credentials.refresh_token_expires_in > credentials.expires_in


def test_successful_token_exchange(django_user_model: User):  # type: ignore[no-untyped-def]
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    def handler(request):  # type: ignore[no-untyped-def]
        if request.url.path == "/services/oauth2/token":
            return httpx.Response(
                200,
                json={
                    "access_token": "access_token",
                    "refresh_token": "refresh_token",
                    "instance_url": "http://instance",
                },
            )
        if "access_token" in str(request.read()):
            return httpx.Response(200, json={"exp": 123})
        if "refresh_token" in str(request.read()):
            return httpx.Response(200, json={"exp": 456})
        return httpx.Response(400)

    sf_oauth = SalesforceOAuth(client=httpx.Client(transport=httpx.MockTransport(handler=handler)))
    sf_oauth.exchange_tokens("auth_code", user, "http://localhost/redirect")

    assert (credentials := OAuthCredentials.objects.get(user=user, integration="salesforce"))
    assert credentials.access_token == "access_token"
    assert credentials.refresh_token == "refresh_token"
    assert credentials.scope == ""
    assert credentials.expires_in == datetime.datetime.fromtimestamp(123, tz=timezone.utc)
    assert credentials.refresh_token_expires_in == datetime.datetime.fromtimestamp(456, tz=timezone.utc)

    user.refresh_from_db()
    assert user.get_crm_configuration().salesforce.salesforce_endpoint == "http://instance"


def test_successful_token_exchange_with_scope(django_user_model: User):  # type: ignore[no-untyped-def]
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    def handler(request):  # type: ignore[no-untyped-def]
        if request.url.path == "/services/oauth2/token":
            return httpx.Response(
                200,
                json={
                    "access_token": "access_token",
                    "refresh_token": "refresh_token",
                    "instance_url": "http://instance",
                    "scope": "test scopes",
                },
            )
        return httpx.Response(400)

    sf_oauth = SalesforceOAuth(client=httpx.Client(transport=httpx.MockTransport(handler=handler)))
    sf_oauth.exchange_tokens("auth_code", user, "http://localhost/redirect")

    assert (credentials := OAuthCredentials.objects.get(user=user, integration="salesforce"))
    assert credentials.scope == "test scopes"


def test_exchange_tokens_updating_existing_data(django_user_model: User):  # type: ignore[no-untyped-def]
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    crm_config = user.get_crm_configuration()
    crm_config.salesforce.salesforce_endpoint = "http://old_instance"
    user.crm_configuration = crm_config.to_dict()
    user.save()

    OAuthCredentials.objects.create(
        user=user,
        integration="salesforce",
        access_token="old_access_token",
        refresh_token="old_refresh_token",
        scope="old scope",
        expires_in=datetime.datetime.now(timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(timezone.utc),
    )

    def handler(request):  # type: ignore[no-untyped-def]
        if request.url.path == "/services/oauth2/token":
            return httpx.Response(
                200,
                json={
                    "access_token": "access_token",
                    "refresh_token": "refresh_token",
                    "instance_url": "http://instance",
                    "scope": "test scopes",
                },
            )
        if "access_token" in str(request.read()):
            return httpx.Response(200, json={"exp": 123})
        if "refresh_token" in str(request.read()):
            return httpx.Response(200, json={"exp": 456})
        return httpx.Response(400)

    sf_oauth = SalesforceOAuth(client=httpx.Client(transport=httpx.MockTransport(handler=handler)))
    sf_oauth.exchange_tokens("auth_code", user, "http://localhost/redirect")

    assert (credentials := OAuthCredentials.objects.get(user=user, integration="salesforce"))
    assert credentials.access_token == "access_token"
    assert credentials.refresh_token == "refresh_token"
    assert credentials.scope == "test scopes"
    assert credentials.expires_in == datetime.datetime.fromtimestamp(123, tz=timezone.utc)
    assert credentials.refresh_token_expires_in == datetime.datetime.fromtimestamp(456, tz=timezone.utc)

    user.refresh_from_db()
    assert user.get_crm_configuration().salesforce.salesforce_endpoint == "http://instance"


def test_get_access_token_does_not_exist(django_user_model: User):  # type: ignore[no-untyped-def]
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    assert not SalesforceOAuth().get_access_token(user)


@patch("deepinsights.core.integrations.oauth.salesforce.OAuthCredentials")
def test_get_access_token_error(mock_oauth_credentials, django_user_model: User):  # type: ignore[no-untyped-def]
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    mock_oauth_credentials.objects.get = MagicMock()
    p = PropertyMock(side_effect=Exception("Error"), return_value=None)
    type(mock_oauth_credentials.objects.get.return_value).expires_in = p

    with pytest.raises(Exception):
        assert not SalesforceOAuth().get_access_token(user)


def test_get_access_token(django_user_model: User):  # type: ignore[no-untyped-def]
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="salesforce",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(timezone.utc) + datetime.timedelta(hours=1),
        refresh_token_expires_in=datetime.datetime.now(timezone.utc),
    )

    assert SalesforceOAuth().get_access_token(user) == "access_token"


@pytest.mark.parametrize(
    ("response_handler"),
    [
        lambda _: httpx.Response(400),
        lambda _: httpx.Response(200, text="Not JSON"),
        lambda _: httpx.Response(200, json={}),
        lambda _: httpx.Response(200, json={"Error": "Error"}),
    ],
)
def test_get_access_token_refresh_http_error(  # type: ignore[no-untyped-def]
    response_handler: Callable[[httpx.Request], httpx.Response], django_user_model: User
):
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="salesforce",
        access_token="old_access_token",
        refresh_token="old_refresh_token",
        scope="",
        expires_in=datetime.datetime.now(timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(timezone.utc),
    )

    sf_oauth = SalesforceOAuth(client=httpx.Client(transport=httpx.MockTransport(handler=response_handler)))
    with pytest.raises(Exception):
        assert not sf_oauth.get_access_token(user)

    # Make sure that the credentials were not modified.
    credentials = OAuthCredentials.objects.get(user=user, integration="salesforce")
    assert credentials.access_token == "old_access_token"
    assert credentials.refresh_token == "old_refresh_token"
    assert not user.get_crm_configuration().salesforce.salesforce_endpoint
    assert _dates_are_close(credentials.expires_in, datetime.datetime.now(timezone.utc))
    assert _dates_are_close(credentials.refresh_token_expires_in, datetime.datetime.now(timezone.utc))


def test_refresh_with_no_new_refresh_token(django_user_model: User):  # type: ignore[no-untyped-def]
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="salesforce",
        access_token="old_access_token",
        refresh_token="old_refresh_token",
        scope="old scope",
        expires_in=datetime.datetime.now(timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(timezone.utc),
    )

    def handler(request):  # type: ignore[no-untyped-def]
        if request.url.path == "/services/oauth2/token":
            return httpx.Response(200, json={"access_token": "new_access_token"})
        return httpx.Response(200, json={"exp": 123})

    sf_oauth = SalesforceOAuth(client=httpx.Client(transport=httpx.MockTransport(handler=handler)))
    assert sf_oauth.get_access_token(user) == "new_access_token"

    credentials = OAuthCredentials.objects.get(user=user, integration="salesforce")
    assert credentials.access_token == "new_access_token"
    assert credentials.refresh_token == "old_refresh_token"
    assert credentials.expires_in == datetime.datetime.fromtimestamp(123, timezone.utc)
    assert _dates_are_close(credentials.refresh_token_expires_in, datetime.datetime.now(timezone.utc))


def test_full_refresh_response(django_user_model: User):  # type: ignore[no-untyped-def]
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="salesforce",
        access_token="old_access_token",
        refresh_token="old_refresh_token",
        scope="old scope",
        expires_in=datetime.datetime.now(timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(timezone.utc),
    )

    def handler(request):  # type: ignore[no-untyped-def]
        if request.url.path == "/services/oauth2/token":
            return httpx.Response(
                200,
                json={
                    "access_token": "new_access_token",
                    "refresh_token": "new_refresh_token",
                    "instance_url": "http://newinstance",
                    "scope": "new scope",
                },
            )
        if "access_token" in str(request.read()):
            return httpx.Response(200, json={"exp": 123})
        if "refresh_token" in str(request.read()):
            return httpx.Response(200, json={"exp": 456})
        return httpx.Response(400)

    sf_oauth = SalesforceOAuth(client=httpx.Client(transport=httpx.MockTransport(handler=handler)))
    assert sf_oauth.get_access_token(user) == "new_access_token"

    credentials = OAuthCredentials.objects.get(user=user, integration="salesforce")
    assert credentials.access_token == "new_access_token"
    assert credentials.refresh_token == "new_refresh_token"
    assert credentials.scope == "new scope"
    assert user.get_crm_configuration().salesforce.salesforce_endpoint == "http://newinstance"
    assert credentials.expires_in == datetime.datetime.fromtimestamp(123, timezone.utc)
    assert credentials.refresh_token_expires_in == datetime.datetime.fromtimestamp(456, timezone.utc)


@pytest.mark.parametrize(
    ("access_expiry_response", "refresh_expiry_response"),
    [
        (
            httpx.Response(400, json={}),
            httpx.Response(400, json={}),
        ),
        (
            httpx.Response(200, json={"exp": 123}),
            httpx.Response(200, json={"exp": 456}),
        ),
        (
            httpx.Response(400, json={}),
            httpx.Response(200, json={"exp": 456}),
        ),
        (
            httpx.Response(200, json={"exp": 123}),
            httpx.Response(400, json={}),
        ),
    ],
)
def test_get_access_token_refresh_responses(  # type: ignore[no-untyped-def]
    access_expiry_response: httpx.Response,
    refresh_expiry_response: httpx.Response,
    django_user_model: User,
):
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="salesforce",
        access_token="old_access_token",
        refresh_token="old_refresh_token",
        scope="old scope",
        expires_in=datetime.datetime.now(timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(timezone.utc),
    )

    def handler(request):  # type: ignore[no-untyped-def]
        if request.url.path == "/services/oauth2/token":
            return httpx.Response(200, json={"access_token": "new_access_token", "refresh_token": "new_refresh_token"})
        if "access_token" in str(request.read()):
            return access_expiry_response
        if "refresh_token" in str(request.read()):
            return refresh_expiry_response
        return httpx.Response(400)

    sf_oauth = SalesforceOAuth(client=httpx.Client(transport=httpx.MockTransport(handler=handler)))
    assert sf_oauth.get_access_token(user) == "new_access_token"

    expected_expiry = (
        datetime.datetime.fromtimestamp(access_expiry_response.json().get("exp"), timezone.utc)
        if access_expiry_response.json().get("exp")
        else datetime.datetime.now(timezone.utc)
    )
    expected_refresh_expiry = (
        datetime.datetime.fromtimestamp(refresh_expiry_response.json().get("exp"), timezone.utc)
        if refresh_expiry_response.json().get("exp")
        else datetime.datetime.now(timezone.utc)
    )

    credentials = OAuthCredentials.objects.get(user=user, integration="salesforce")
    assert credentials.access_token == "new_access_token"
    assert credentials.refresh_token == "new_refresh_token"
    assert _dates_are_close(credentials.expires_in, expected_expiry)
    assert _dates_are_close(credentials.refresh_token_expires_in, expected_refresh_expiry)
