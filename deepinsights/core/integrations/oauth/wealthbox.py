import datetime
import logging

import requests
from django.conf import settings
from django.utils import timezone

from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.users.models.user import User


class WealthBoxOAuth:
    def __init__(self) -> None:
        self.base_url = settings.WEALTHBOX_OAUTH_BASE_URL
        self.client_id = settings.WEALTHBOX_CLIENT_ID
        self.client_secret = settings.WEALTHBOX_CLIENT_SECRET

    def exchange_tokens(self, authorization_code: str, user: User, redirect_uri: str) -> None:
        try:
            authentication_url = f"{self.base_url}/oauth/token"
            headers = {"Content-Type": "multipart/form-data"}
            payload = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "code": authorization_code,
                "grant_type": "authorization_code",
                "redirect_uri": redirect_uri,
            }

            data = requests.post(authentication_url, data=payload, headers=headers).json()
            logging.info("Data from token exchange: %s", data)

            oauth_credentials, created = OAuthCredentials.objects.update_or_create(
                user=user,
                integration="wealthbox",
                defaults={
                    "access_token": data["access_token"],
                    "refresh_token": data["refresh_token"],
                    "created_at": datetime.datetime.fromtimestamp(data["created_at"], tz=datetime.timezone.utc),
                    "expires_in": datetime.datetime.fromtimestamp(
                        data["created_at"] + data["expires_in"], tz=datetime.timezone.utc
                    ),
                    "refresh_token_expires_in": datetime.datetime.fromtimestamp(
                        data["created_at"], tz=datetime.timezone.utc
                    )
                    + datetime.timedelta(days=90),
                    "scope": data["scope"],
                },
            )
            logging.info("%s OAuth credentials: %s", "Created" if created else "Updated", oauth_credentials.uuid)
        except Exception as e:
            logging.error("Error exchanging tokens for user %s", user.uuid, exc_info=e)
            raise

    def _refresh_access_token(self, oauth_credentials: OAuthCredentials) -> str:
        try:
            authentication_url = f"{self.base_url}/oauth/token"
            headers = {"Content-Type": "multipart/form-data"}

            payload = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "grant_type": "refresh_token",
                "refresh_token": oauth_credentials.refresh_token,
            }

            data = requests.post(authentication_url, data=payload, headers=headers).json()
            logging.info("Data from token refresh: %s", data)

            oauth_credentials.access_token = data["access_token"]
            oauth_credentials.refresh_token = data["refresh_token"]
            oauth_credentials.created_at = datetime.datetime.fromtimestamp(data["created_at"], tz=datetime.timezone.utc)
            oauth_credentials.expires_in = datetime.datetime.fromtimestamp(
                data["created_at"] + data["expires_in"], tz=datetime.timezone.utc
            )
            oauth_credentials.refresh_token_expires_in = datetime.datetime.fromtimestamp(
                data["created_at"], tz=datetime.timezone.utc
            ) + datetime.timedelta(days=30)
            oauth_credentials.scope = data["scope"]

            oauth_credentials.save()
            logging.info("Saving refresh oauth credentials: %s", oauth_credentials.uuid)

            return data["access_token"]  # type: ignore[no-any-return]
        except Exception as e:
            logging.error("Error refreshing access token", exc_info=e)
            raise

    def get_access_token(self, user: User) -> str | None:
        try:
            oauth_credentials = OAuthCredentials.objects.get(user=user, integration="wealthbox")
            expiry = oauth_credentials.expires_in
            if expiry < timezone.now():
                logging.info("Access token expired, refreshing token for user: %s", user.uuid)
                access_token = self._refresh_access_token(oauth_credentials)
                return access_token
            else:
                return oauth_credentials.access_token
        except OAuthCredentials.DoesNotExist:
            logging.info("No credentials found for user: %s", user.uuid)
            return None
        except Exception as e:
            logging.error("Error getting access token", exc_info=e)
            return None

    def check_integration_status(self, user: User) -> bool:
        try:
            oauth_credentials = OAuthCredentials.objects.get(user=user, integration="wealthbox")
            refresh_token_expiry = oauth_credentials.refresh_token_expires_in
            if refresh_token_expiry < timezone.now():
                return False
            else:
                return True
        except OAuthCredentials.DoesNotExist:
            return False
