from deepinsights.users.models.user import User


class DummyOAuthCalendarIntegration:
    """A dummy implementation of OAuthCalendarIntegrationProtocol"""

    @classmethod
    def client_id(cls) -> str:
        return "dummy_client_id"

    @classmethod
    def client_secret(cls) -> str:
        return "dummy_client_secret"

    def exchange_tokens(self, authorization_code: str, user: User, redirect_uri: str) -> None:
        pass

    def get_refresh_token(self, user: User) -> str | None:
        return None
