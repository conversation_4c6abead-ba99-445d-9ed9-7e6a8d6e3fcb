import datetime
import logging
from typing import Any
from unittest.mock import MagicMock, patch

import pydantic
import pytest
import requests
from pytest_django.fixtures import SettingsWrapper

from deepinsights.core.integrations.oauth.advisor_engine import AdvisorEngineOAuth
from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.users.models.user import User


@pytest.fixture
def advisor_engine_oauth(settings: SettingsWrapper) -> AdvisorEngineOAuth:
    settings.ADVISORENGINE_OAUTH_BASE_URL = "https://example.com/test"
    settings.ADVISORENGINE_CLIENT_ID = "test_client_id"
    settings.ADVISORENGINE_CLIENT_SECRET = "test_client_secret"
    return AdvisorEngineOAuth()


def test_initialization(settings: SettingsWrapper) -> None:
    settings.ADVISORENGINE_OAUTH_BASE_URL = "https://example.com/test"
    settings.ADVISORENGINE_CLIENT_ID = "zeplyn"
    settings.ADVISORENGINE_CLIENT_SECRET = "secret123"

    oauth = AdvisorEngineOAuth()

    assert oauth.client_id == "zeplyn"
    assert oauth.client_secret == "secret123"
    assert oauth.scopes == ["openid", "offline_access", "ae_crm.ext_partner"]


@pytest.mark.parametrize(
    "status_code,response_text",
    [
        (204, "No Content"),
        (400, "Bad Request"),
        (401, "Unauthorized"),
        (500, "Internal Server Error"),
    ],
)
@patch("deepinsights.core.integrations.oauth.advisor_engine.requests.post")
def test_exchange_tokens_http_errors(
    mock_post: MagicMock,
    advisor_engine_oauth: AdvisorEngineOAuth,
    django_user_model: User,
    status_code: int,
    response_text: str,
) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    mock_response = MagicMock()
    mock_response.status_code = status_code
    mock_response.text = response_text
    mock_post.return_value = mock_response

    with pytest.raises(Exception, match=f"Token exchange failed with status {status_code}: {response_text}"):
        advisor_engine_oauth.exchange_tokens("auth_code", "code_verifier", user, "http://localhost/redirect")


@patch("deepinsights.core.integrations.oauth.advisor_engine.requests.post")
def test_exchange_tokens_request_timeout(
    mock_post: MagicMock,
    advisor_engine_oauth: AdvisorEngineOAuth,
    django_user_model: User,
) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    mock_post.side_effect = requests.exceptions.Timeout("Request timeout")

    with pytest.raises(requests.exceptions.Timeout, match="Request timeout"):
        advisor_engine_oauth.exchange_tokens("auth_code", "code_verifier", user, "http://localhost/redirect")


@pytest.mark.parametrize(
    "response_data,expected_error",
    [
        (
            {"refresh_token": "refresh_token", "expires_in": 3600},
            "Field required",
        ),  # Missing access_token
        ({"access_token": "access_token"}, "Field required"),  # Missing expires_in
        (
            {"access_token": "access_token", "expires_in": "invalid"},
            "Input should be a valid integer",
        ),  # Invalid expires_in type
        ({"access_token": "", "expires_in": 3600}, "String should have at least 1 character"),  # Empty access_token
    ],
)
@patch("deepinsights.core.integrations.oauth.advisor_engine.requests.post")
def test_exchange_tokens_invalid_responses(
    mock_post: MagicMock,
    advisor_engine_oauth: AdvisorEngineOAuth,
    django_user_model: User,
    response_data: dict[str, Any],
    expected_error: str,
) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = response_data
    mock_post.return_value = mock_response

    with pytest.raises(pydantic.ValidationError, match=expected_error):
        advisor_engine_oauth.exchange_tokens("auth_code", "code_verifier", user, "http://localhost/redirect")


@patch("deepinsights.core.integrations.oauth.advisor_engine.requests.post")
def test_exchange_tokens_request_payload(
    mock_post: MagicMock,
    advisor_engine_oauth: AdvisorEngineOAuth,
    django_user_model: User,
) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "access_token": "access_token",
        "refresh_token": "refresh_token",
        "expires_in": 3600,
    }
    mock_post.return_value = mock_response

    advisor_engine_oauth.exchange_tokens("auth_code", "code_verifier", user, "http://localhost/redirect")

    mock_post.assert_called_once_with(
        "https://example.com/test/connect/token",
        data={
            "grant_type": "authorization_code",
            "code": "auth_code",
            "redirect_uri": "http://localhost/redirect",
            "client_id": "test_client_id",
            "code_verifier": "code_verifier",
            "client_secret": "test_client_secret",
        },
        headers={"Content-Type": "application/x-www-form-urlencoded"},
        timeout=30,
    )


@patch("deepinsights.core.integrations.oauth.advisor_engine.requests.post")
def test_exchange_tokens_without_client_secret(
    mock_post: MagicMock,
    django_user_model: User,
    settings: SettingsWrapper,
) -> None:
    settings.ADVISORENGINE_OAUTH_BASE_URL = "https://example.com/test"
    settings.ADVISORENGINE_CLIENT_ID = "test_client_id"
    settings.ADVISORENGINE_CLIENT_SECRET = None

    oauth = AdvisorEngineOAuth()
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "access_token": "access_token",
        "refresh_token": "refresh_token",
        "expires_in": 3600,
    }
    mock_post.return_value = mock_response

    oauth.exchange_tokens("auth_code", "code_verifier", user, "http://localhost/redirect")

    # Verify client_secret is not included when None
    call_data = mock_post.call_args[1]["data"]
    assert "client_secret" not in call_data


@patch("deepinsights.core.integrations.oauth.advisor_engine.requests.post")
def test_exchange_tokens_successful(
    mock_post: MagicMock,
    advisor_engine_oauth: AdvisorEngineOAuth,
    django_user_model: User,
) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    current_time = datetime.datetime.now(datetime.timezone.utc)

    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "access_token": "access_token_123",
        "refresh_token": "refresh_token_456",
        "expires_in": 3600,
        "scope": "openid offline_access ae_crm.ext_partner",
        "token_type": "Bearer",
    }
    mock_post.return_value = mock_response

    advisor_engine_oauth.exchange_tokens("auth_code", "code_verifier", user, "http://localhost/redirect")

    credentials = OAuthCredentials.objects.get(user=user, integration="advisor_engine")
    assert credentials.access_token == "access_token_123"
    assert credentials.refresh_token == "refresh_token_456"
    assert credentials.scope == "openid offline_access ae_crm.ext_partner"
    assert abs(credentials.expires_in - (current_time + datetime.timedelta(seconds=3600))) < datetime.timedelta(
        seconds=2
    )
    assert credentials.refresh_token_expires_in > current_time


@patch("deepinsights.core.integrations.oauth.advisor_engine.requests.post")
def test_exchange_tokens_updating_existing_credentials(
    mock_post: MagicMock,
    advisor_engine_oauth: AdvisorEngineOAuth,
    django_user_model: User,
) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    OAuthCredentials.objects.create(
        user=user,
        integration="advisor_engine",
        access_token="old_access_token",
        refresh_token="old_refresh_token",
        scope="old_scope",
        expires_in=datetime.datetime.now(datetime.timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "access_token": "new_access_token",
        "refresh_token": "new_refresh_token",
        "expires_in": 7200,
        "scope": "new_scope",
    }
    mock_post.return_value = mock_response

    advisor_engine_oauth.exchange_tokens("auth_code", "code_verifier", user, "http://localhost/redirect")

    credentials = OAuthCredentials.objects.get(user=user, integration="advisor_engine")
    assert credentials.access_token == "new_access_token"
    assert credentials.refresh_token == "new_refresh_token"
    assert credentials.scope == "new_scope"


def test_get_access_token_does_not_exist(
    advisor_engine_oauth: AdvisorEngineOAuth,
    django_user_model: User,
    caplog: pytest.LogCaptureFixture,
) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    caplog.set_level(logging.INFO)
    result = advisor_engine_oauth.get_access_token(user)

    assert result is None
    assert "No AdvisorEngine credentials found for user" in caplog.text


def test_get_access_token_valid_token(
    advisor_engine_oauth: AdvisorEngineOAuth,
    django_user_model: User,
) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    OAuthCredentials.objects.create(
        user=user,
        integration="advisor_engine",
        access_token="valid_access_token",
        refresh_token="refresh_token",
        scope="openid",
        expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=30),
    )

    result = advisor_engine_oauth.get_access_token(user)
    assert result == "valid_access_token"


@patch("deepinsights.core.integrations.oauth.advisor_engine.requests.post")
def test_refresh_access_token_successful(
    mock_post: MagicMock,
    advisor_engine_oauth: AdvisorEngineOAuth,
    django_user_model: User,
    caplog: pytest.LogCaptureFixture,
) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    # Create expired credentials
    credentials = OAuthCredentials.objects.create(
        user=user,
        integration="advisor_engine",
        access_token="expired_token",
        refresh_token="refresh_token_456",
        scope="openid",
        expires_in=datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(minutes=1),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=30),
    )

    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "access_token": "new_access_token",
        "refresh_token": "new_refresh_token",
        "expires_in": 3600,
        "scope": "updated_scope",
    }
    mock_post.return_value = mock_response

    caplog.set_level(logging.INFO)
    result = advisor_engine_oauth.get_access_token(user)

    assert result == "new_access_token"
    assert "AdvisorEngine access token expired, refreshing" in caplog.text
    assert "Refreshed AdvisorEngine OAuth credentials" in caplog.text

    credentials.refresh_from_db()
    assert credentials.access_token == "new_access_token"
    assert credentials.refresh_token == "new_refresh_token"
    assert credentials.scope == "updated_scope"


@pytest.mark.parametrize(
    "status_code,response_text",
    [
        (204, "No Content"),
        (400, "Invalid refresh token"),
        (401, "Unauthorized"),
        (500, "Server error"),
    ],
)
@patch("deepinsights.core.integrations.oauth.advisor_engine.requests.post")
def test_refresh_access_token_http_errors(
    mock_post: MagicMock,
    advisor_engine_oauth: AdvisorEngineOAuth,
    django_user_model: User,
    status_code: int,
    response_text: str,
) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    OAuthCredentials.objects.create(
        user=user,
        integration="advisor_engine",
        access_token="expired_token",
        refresh_token="refresh_token_456",
        scope="openid",
        expires_in=datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(minutes=1),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=30),
    )

    mock_response = MagicMock()
    mock_response.status_code = status_code
    mock_response.text = response_text
    mock_post.return_value = mock_response

    with pytest.raises(Exception, match=f"Token refresh failed with status {status_code}: {response_text}"):
        advisor_engine_oauth.get_access_token(user)


@patch("deepinsights.core.integrations.oauth.advisor_engine.requests.post")
def test_refresh_access_token_partial_response(
    mock_post: MagicMock,
    advisor_engine_oauth: AdvisorEngineOAuth,
    django_user_model: User,
) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    credentials = OAuthCredentials.objects.create(
        user=user,
        integration="advisor_engine",
        access_token="expired_token",
        refresh_token="refresh_token_456",
        scope="original_scope",
        expires_in=datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(minutes=1),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=30),
    )

    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "access_token": "new_access_token",
        "expires_in": 3600,
        # No new refresh_token or scope
    }
    mock_post.return_value = mock_response

    result = advisor_engine_oauth.get_access_token(user)

    assert result == "new_access_token"
    credentials.refresh_from_db()
    assert credentials.access_token == "new_access_token"
    assert credentials.refresh_token == "refresh_token_456"  # Unchanged
    assert credentials.scope == "original_scope"  # Unchanged


@patch("deepinsights.core.integrations.oauth.advisor_engine.OAuthCredentials")
def test_get_access_token_database_error(
    mock_oauth_credentials: MagicMock,
    advisor_engine_oauth: AdvisorEngineOAuth,
    django_user_model: User,
    caplog: pytest.LogCaptureFixture,
) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    mock_oauth_credentials.objects.get.side_effect = Exception("Database connection failed")
    mock_oauth_credentials.DoesNotExist = OAuthCredentials.DoesNotExist

    caplog.set_level(logging.ERROR)
    with pytest.raises(Exception, match="Database connection failed"):
        advisor_engine_oauth.get_access_token(user)
