import datetime
from typing import Any
from unittest.mock import AN<PERSON>, MagicMock, PropertyMock, patch

import pytest
from pytest_django.fixtures import SettingsWrapper

from deepinsights.core.integrations.oauth.wealthbox import WealthBoxOAuth
from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.users.models.user import User


# Whether or not the given dates are "close enough" to be considered equal for the purposes of this
# test.
def _dates_are_close(date1: datetime.datetime, date2: datetime.datetime) -> bool:
    return abs(date1 - date2) < datetime.timedelta(seconds=1)


def test_initialization(settings: SettingsWrapper) -> None:
    settings.WEALTHBOX_CLIENT_ID = "client_id"
    settings.WEALTHBOX_CLIENT_SECRET = "client_secret"
    settings.WEALTHBOX_OAUTH_BASE_URL = "http://localhost"

    oauth = WealthBoxOAuth()
    assert oauth.client_id == "client_id"
    assert oauth.client_secret == "client_secret"
    assert oauth.base_url == "http://localhost"


@patch("deepinsights.core.integrations.oauth.wealthbox.requests")
def test_exchange_tokens_error(mock_requests: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    mock_requests.post.side_effect = Exception("Error")

    with pytest.raises(Exception):
        WealthBoxOAuth().exchange_tokens("auth_code", user, "http://localhost/redirect")


@patch("deepinsights.core.integrations.oauth.wealthbox.requests")
def test_exchange_tokens_response_not_json(mock_requests: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    mock_requests.post.return_value.json.return_value = "Not JSON"

    with pytest.raises(Exception):
        WealthBoxOAuth().exchange_tokens("auth_code", user, "http://localhost/redirect")


@pytest.mark.parametrize(
    ("response"),
    [
        {"error": "Error"},
        {
            "access_token": "access_token",
            "instance_url": "http://instance",
        },
        {
            "access_token": "access_token",
            "refresh_token": "refresh_token",
        },
        {
            "refresh_token": "refresh_token",
            "instance_url": "http://instance",
        },
    ],
)
@patch("deepinsights.core.integrations.oauth.wealthbox.requests")
def test_exchange_tokens_invalid_responses(
    mock_requests: MagicMock, response: dict[str, str], django_user_model: User
) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    mock_requests.post.return_value.json.return_value = response

    with pytest.raises(Exception):
        WealthBoxOAuth().exchange_tokens("auth_code", user, "http://localhost/redirect")


@patch("deepinsights.core.integrations.oauth.wealthbox.requests")
def test_exchange_tokens_request(mock_requests: MagicMock, django_user_model: User, settings: SettingsWrapper) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    settings.WEALTHBOX_OAUTH_BASE_URL = "http://example.com"
    settings.WEALTHBOX_CLIENT_ID = "client_id"
    settings.WEALTHBOX_CLIENT_SECRET = "client_secret"

    with pytest.raises(Exception):
        WealthBoxOAuth().exchange_tokens("auth_code", user, "http://localhost/redirect")
    mock_requests.post.assert_called_once_with(
        "http://example.com/oauth/token",
        data={
            "client_id": "client_id",
            "client_secret": "client_secret",
            "code": "auth_code",
            "grant_type": "authorization_code",
            "redirect_uri": "http://localhost/redirect",
        },
        headers=ANY,
    )


@patch("deepinsights.core.integrations.oauth.wealthbox.requests")
def test_exchange_tokens_success(mock_requests: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    mock_requests.post.return_value.json.return_value = {
        "access_token": "access_token",
        "refresh_token": "refresh_token",
        "created_at": 123,
        "expires_in": 456,
        "scope": "scope",
    }

    WealthBoxOAuth().exchange_tokens("auth_code", user, "http://localhost/redirect")

    assert (credentials := OAuthCredentials.objects.get(user=user, integration="wealthbox"))
    assert credentials.access_token == "access_token"
    assert credentials.refresh_token == "refresh_token"
    assert credentials.scope == "scope"
    assert credentials.expires_in == datetime.datetime.fromtimestamp(123 + 456, tz=datetime.timezone.utc)
    assert credentials.refresh_token_expires_in == datetime.datetime.fromtimestamp(
        123 + 90 * 60 * 60 * 24, tz=datetime.timezone.utc
    )


@patch("deepinsights.core.integrations.oauth.wealthbox.requests")
def test_exchange_tokens_updating_existing_data(mock_requests: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    OAuthCredentials.objects.create(
        user=user,
        integration="wealthbox",
        access_token="old_access_token",
        refresh_token="old_refresh_token",
        scope="old scope",
        expires_in=datetime.datetime.now(datetime.timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    mock_requests.post.return_value.json.return_value = {
        "access_token": "access_token",
        "refresh_token": "refresh_token",
        "created_at": 123,
        "expires_in": 456,
        "scope": "scope",
    }

    WealthBoxOAuth().exchange_tokens("auth_code", user, "http://localhost/redirect")

    assert (credentials := OAuthCredentials.objects.get(user=user, integration="wealthbox"))
    assert credentials.access_token == "access_token"
    assert credentials.refresh_token == "refresh_token"
    assert credentials.scope == "scope"
    assert credentials.expires_in == datetime.datetime.fromtimestamp(123 + 456, tz=datetime.timezone.utc)
    assert credentials.refresh_token_expires_in == datetime.datetime.fromtimestamp(
        123 + 90 * 60 * 60 * 24, tz=datetime.timezone.utc
    )


def test_get_access_token_does_not_exist(django_user_model: User) -> None:
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    assert not WealthBoxOAuth().get_access_token(user)


@patch("deepinsights.core.integrations.oauth.wealthbox.OAuthCredentials")
def test_get_access_token_error(mock_oauth_credentials: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    mock_oauth_credentials.objects.get = MagicMock()
    p = PropertyMock(side_effect=Exception("Error"), return_value=None)
    type(mock_oauth_credentials.objects.get.return_value).expires_in = p

    with pytest.raises(Exception):
        assert not WealthBoxOAuth().get_access_token(user)


def test_get_access_token(django_user_model: User) -> None:
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="wealthbox",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    assert WealthBoxOAuth().get_access_token(user) == "access_token"


@pytest.mark.parametrize(
    ("response"),
    [
        "Not JSON",
        {},
        {"Error": "Error"},
        {
            "refresh_token": "new_refresh_token",
            "created_at": 123,
            "expires_in": 456,
            "scope": "new scope",
        },
        {
            "access_token": "new_access_token",
            "created_at": 123,
            "expires_in": 456,
            "scope": "new scope",
        },
        {
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
            "expires_in": 456,
            "scope": "new scope",
        },
        {
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
            "created_at": 123,
            "scope": "new scope",
        },
        {
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
            "created_at": 123,
            "expires_in": 456,
        },
    ],
)
@patch("deepinsights.core.integrations.oauth.wealthbox.requests")
def test_get_access_token_refresh_http_error(mock_requests: MagicMock, response: Any, django_user_model: User) -> None:
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="wealthbox",
        access_token="old_access_token",
        refresh_token="old_refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    mock_requests.post.return_value.json.return_value = response

    assert not WealthBoxOAuth().get_access_token(user)

    # Make sure that the credentials were not modified.
    credentials = OAuthCredentials.objects.get(user=user, integration="wealthbox")
    assert credentials.access_token == "old_access_token"
    assert credentials.refresh_token == "old_refresh_token"
    assert _dates_are_close(credentials.expires_in, datetime.datetime.now(datetime.timezone.utc))
    assert _dates_are_close(credentials.refresh_token_expires_in, datetime.datetime.now(datetime.timezone.utc))


@patch("deepinsights.core.integrations.oauth.wealthbox.requests")
def test_full_refresh_response(mock_requests: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="wealthbox",
        access_token="old_access_token",
        refresh_token="old_refresh_token",
        scope="old scope",
        expires_in=datetime.datetime.now(datetime.timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    mock_requests.post.return_value.json.return_value = {
        "access_token": "new_access_token",
        "refresh_token": "new_refresh_token",
        "created_at": 123,
        "expires_in": 456,
        "scope": "new scope",
    }

    assert WealthBoxOAuth().get_access_token(user) == "new_access_token"

    credentials = OAuthCredentials.objects.get(user=user, integration="wealthbox")
    assert credentials.access_token == "new_access_token"
    assert credentials.refresh_token == "new_refresh_token"
    assert credentials.scope == "new scope"
    assert credentials.expires_in == datetime.datetime.fromtimestamp(123 + 456, datetime.timezone.utc)
    assert credentials.refresh_token_expires_in == datetime.datetime.fromtimestamp(
        123 + 30 * 24 * 60 * 60, datetime.timezone.utc
    )


def test_check_integration_status_no_credentials(django_user_model: User) -> None:
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    assert not WealthBoxOAuth().check_integration_status(user)


def test_check_integration_status_expired_refresh_token(django_user_model: User) -> None:
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="wealthbox",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(days=1),
    )
    assert not WealthBoxOAuth().check_integration_status(user)
    assert OAuthCredentials.objects.get(user=user, integration="wealthbox")


def test_check_integration_status_valid_refresh_token(django_user_model: User) -> None:
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="wealthbox",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=1),
    )
    assert WealthBoxOAuth().check_integration_status(user)
