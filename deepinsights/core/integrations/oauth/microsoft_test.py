import datetime
import logging
from unittest.mock import Async<PERSON>ock, <PERSON><PERSON>ock, PropertyMock, patch

import pytest
import requests
from pytest_django.fixtures import SettingsWrapper

from deepinsights.core.integrations.oauth.microsoft import MicrosoftOAuth
from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.users.models.user import User


@patch("deepinsights.core.integrations.oauth.microsoft.msal.ConfidentialClientApplication")
def test_initialization(mock_app: MagicMock, settings: SettingsWrapper) -> None:
    settings.MSAL_CLIENT_ID = "client_id"
    settings.MSAL_CLIENT_SECRET = "client_secret"
    MicrosoftOAuth()
    mock_app.assert_called_with(client_id="client_id", client_credential="client_secret")


def test_client_settings(settings: SettingsWrapper) -> None:
    settings.MSAL_CLIENT_ID = "client_id"
    settings.MSAL_CLIENT_SECRET = "client_secret"
    assert MicrosoftOAuth.client_id() == "client_id"
    assert MicrosoftOAuth.client_secret() == "client_secret"


@patch("deepinsights.core.integrations.oauth.microsoft.msal.ConfidentialClientApplication")
def test_exchange_tokens_error(mock_app: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    mock_app.return_value.acquire_token_by_authorization_code.side_effect = Exception("Error")

    with pytest.raises(Exception, match="Error exchanging tokens"):
        MicrosoftOAuth().exchange_tokens("auth_code", user, "http://localhost/redirect")


@patch("deepinsights.core.integrations.oauth.microsoft.msal.ConfidentialClientApplication")
def test_exchange_tokens_response_error(mock_app: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    mock_app.return_value.acquire_token_by_authorization_code.return_value = {"error": "Error"}

    with pytest.raises(Exception, match="Error exchanging tokens"):
        MicrosoftOAuth().exchange_tokens("auth_code", user, "http://localhost/redirect")


@patch("deepinsights.core.integrations.oauth.microsoft.msal.ConfidentialClientApplication")
def test_exchange_tokens_missing_access_token(mock_app: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    mock_app.return_value.acquire_token_by_authorization_code.return_value = {
        "refresh_token": "refresh_token",
        "expires_in": 1,
    }

    with pytest.raises(Exception):
        MicrosoftOAuth().exchange_tokens("auth_code", user, "http://localhost/redirect")


@patch("deepinsights.core.integrations.oauth.microsoft.msal.ConfidentialClientApplication")
def test_exchange_tokens_missing_refresh_token(mock_app: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    mock_app.return_value.acquire_token_by_authorization_code.return_value = {
        "access_token": "access_token",
        "expires_in": 1,
    }

    with pytest.raises(Exception):
        MicrosoftOAuth().exchange_tokens("auth_code", user, "http://localhost/redirect")


@patch("deepinsights.core.integrations.oauth.microsoft.msal.ConfidentialClientApplication")
def test_exchange_tokens_missing_expiration(mock_app: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    mock_app.return_value.acquire_token_by_authorization_code.return_value = {
        "access_token": "access_token",
        "refresh_token": "refresh_token",
    }

    with pytest.raises(Exception):
        MicrosoftOAuth().exchange_tokens("auth_code", user, "http://localhost/redirect")


@patch("deepinsights.core.integrations.oauth.microsoft.msal.ConfidentialClientApplication")
def test_exchange_tokens(mock_app: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    expiry = datetime.datetime.now(datetime.timezone.utc)

    mock_acquire = mock_app.return_value.acquire_token_by_authorization_code
    mock_acquire.return_value = {
        "access_token": "access_token",
        "refresh_token": "refresh_token",
        "expires_in": 100,
        "foo": "foo",
    }

    MicrosoftOAuth().exchange_tokens("auth_code", user, "http://localhost/redirect")

    mock_acquire.assert_called_once_with(
        "auth_code", scopes=["User.Read", "Calendars.Read"], redirect_uri="http://localhost/redirect"
    )
    credentials = OAuthCredentials.objects.get(user=user, integration="microsoft")
    assert credentials.access_token == "access_token"
    assert credentials.refresh_token == "refresh_token"
    assert credentials.scope == "User.Read Calendars.Read"
    assert abs(credentials.expires_in - (expiry + datetime.timedelta(seconds=100))) < datetime.timedelta(seconds=2)
    assert credentials.refresh_token_expires_in > expiry


@patch("deepinsights.core.integrations.oauth.microsoft.msal.ConfidentialClientApplication")
def test_exchange_tokens_with_scopes(mock_app: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")

    mock_acquire = mock_app.return_value.acquire_token_by_authorization_code
    mock_acquire.return_value = {
        "access_token": "access_token",
        "refresh_token": "refresh_token",
        "expires_in": 100,
        "scope": "new scopes",
    }

    MicrosoftOAuth().exchange_tokens("auth_code", user, "http://localhost/redirect")

    mock_acquire.assert_called_once_with(
        "auth_code", scopes=["User.Read", "Calendars.Read"], redirect_uri="http://localhost/redirect"
    )
    credentials = OAuthCredentials.objects.get(user=user, integration="microsoft")
    assert credentials.scope == "new scopes"


@patch("deepinsights.core.integrations.oauth.microsoft.msal.ConfidentialClientApplication")
def test_exchange_tokens_updating_existing_token(mock_app: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="microsoft",
        access_token="old_access_token",
        refresh_token="old_refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    expiry = datetime.datetime.now(datetime.timezone.utc)

    mock_acquire = mock_app.return_value.acquire_token_by_authorization_code
    mock_acquire.return_value = {
        "access_token": "access_token",
        "refresh_token": "refresh_token",
        "expires_in": 100,
        "scope": "new scopes",
    }

    MicrosoftOAuth().exchange_tokens("auth_code", user, "http://localhost/redirect")

    credentials = OAuthCredentials.objects.get(user=user, integration="microsoft")
    assert credentials.access_token == "access_token"
    assert credentials.refresh_token == "refresh_token"
    assert credentials.scope == "new scopes"
    assert abs(credentials.expires_in - (expiry + datetime.timedelta(seconds=100))) < datetime.timedelta(seconds=2)
    assert credentials.refresh_token_expires_in > expiry


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
async def test_get_access_token_does_not_exist(django_user_model: User, caplog: pytest.LogCaptureFixture) -> None:
    user = await django_user_model.objects.acreate(username="<EMAIL>", email="<EMAIL>")
    caplog.set_level(logging.ERROR)
    assert not await MicrosoftOAuth().get_access_token(user)
    assert not caplog.records


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch("deepinsights.core.integrations.oauth.microsoft.OAuthCredentials")
async def test_get_access_token_error(
    mock_oauth_credentials: MagicMock, django_user_model: User, caplog: pytest.LogCaptureFixture
) -> None:
    user = await django_user_model.objects.acreate(username="<EMAIL>", email="<EMAIL>")
    mock_oauth_credentials.objects.aget = AsyncMock()
    p = PropertyMock(side_effect=Exception("Error"), return_value=None)
    type(mock_oauth_credentials.objects.aget.return_value).expires_in = p

    caplog.set_level(logging.ERROR)
    with pytest.raises(Exception):
        assert not await MicrosoftOAuth().get_access_token(user)
    assert len(caplog.records) == 1
    assert "Error getting access token" in caplog.messages[0]


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
async def test_get_access_token(django_user_model: User) -> None:
    user = await django_user_model.objects.acreate(username="<EMAIL>", email="<EMAIL>")
    await OAuthCredentials.objects.acreate(
        user=user,
        integration="microsoft",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    assert await MicrosoftOAuth().get_access_token(user) == "access_token"


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch("deepinsights.core.integrations.oauth.microsoft.msal.ConfidentialClientApplication")
async def test_get_access_token_refresh_exception(mock_app: MagicMock, django_user_model: User) -> None:
    user = await django_user_model.objects.acreate(username="<EMAIL>", email="<EMAIL>")
    await OAuthCredentials.objects.acreate(
        user=user,
        integration="microsoft",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    mock_app.return_value.acquire_token_by_refresh_token.side_effect = Exception("Error")

    with pytest.raises(Exception):
        assert not await MicrosoftOAuth().get_access_token(user)


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch("deepinsights.core.integrations.oauth.microsoft.msal.ConfidentialClientApplication")
async def test_get_access_token_refresh_response_error(mock_app: MagicMock, django_user_model: User) -> None:
    user = await django_user_model.objects.acreate(username="<EMAIL>", email="<EMAIL>")
    await OAuthCredentials.objects.acreate(
        user=user,
        integration="microsoft",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    mock_app.return_value.acquire_token_by_refresh_token.return_value = {"error": "Error"}

    with pytest.raises(Exception):
        assert not await MicrosoftOAuth().get_access_token(user)


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch("deepinsights.core.integrations.oauth.microsoft.msal.ConfidentialClientApplication")
async def test_get_access_token_refresh(mock_app: MagicMock, django_user_model: User) -> None:
    user = await django_user_model.objects.acreate(username="<EMAIL>", email="<EMAIL>")
    await OAuthCredentials.objects.acreate(
        user=user,
        integration="microsoft",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    mock_app.return_value.acquire_token_by_refresh_token.return_value = {
        "access_token": "new_access_token",
        "expires_in": 100,
    }

    assert await MicrosoftOAuth().get_access_token(user) == "new_access_token"
    credentials = await OAuthCredentials.objects.aget(user=user, integration="microsoft")
    assert credentials.access_token == "new_access_token"
    assert credentials.refresh_token == "refresh_token"
    assert credentials.scope == ""
    assert abs(
        credentials.expires_in - (datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(seconds=100))
    ) < datetime.timedelta(seconds=2)


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
@patch("deepinsights.core.integrations.oauth.microsoft.msal.ConfidentialClientApplication")
async def test_get_access_token_refresh_update_refresh_and_scopes(mock_app: MagicMock, django_user_model: User) -> None:
    user = await django_user_model.objects.acreate(username="<EMAIL>", email="<EMAIL>")
    await OAuthCredentials.objects.acreate(
        user=user,
        integration="microsoft",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    mock_app.return_value.acquire_token_by_refresh_token.return_value = {
        "access_token": "new_access_token",
        "expires_in": 100,
        "refresh_token": "new_refresh_token",
        "scope": "new scopes",
    }

    assert await MicrosoftOAuth().get_access_token(user) == "new_access_token"
    credentials = await OAuthCredentials.objects.aget(user=user, integration="microsoft")
    assert credentials.refresh_token == "new_refresh_token"
    assert credentials.scope == "new scopes"


@pytest.mark.django_db(transaction=True)
def test_get_refresh_token_does_not_exist(django_user_model: User, caplog: pytest.LogCaptureFixture) -> None:
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    caplog.set_level(logging.ERROR)
    assert not MicrosoftOAuth().get_refresh_token(user)
    assert not caplog.records


@pytest.mark.django_db(transaction=True)
@patch("deepinsights.core.integrations.oauth.microsoft.OAuthCredentials")
def test_get_refresh_token_error(
    mock_oauth_credentials: MagicMock, django_user_model: User, caplog: pytest.LogCaptureFixture
) -> None:
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    mock_oauth_credentials.objects.aget = AsyncMock()
    p = PropertyMock(side_effect=Exception("Error"), return_value=None)
    type(mock_oauth_credentials.objects.aget.return_value).expires_in = p

    caplog.set_level(logging.ERROR)
    with pytest.raises(Exception):
        assert not MicrosoftOAuth().get_refresh_token(user)
    assert not caplog.records


@pytest.mark.django_db(transaction=True)
def test_get_refresh_token(django_user_model: User) -> None:
    user = django_user_model.objects.create(username="<EMAIL>", email="<EMAIL>")
    OAuthCredentials.objects.create(
        user=user,
        integration="microsoft",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    assert MicrosoftOAuth().get_refresh_token(user) == "refresh_token"


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
async def test_get_expiry_does_not_exist(django_user_model: User) -> None:
    user = await django_user_model.objects.acreate(username="<EMAIL>", email="<EMAIL>")
    assert not await MicrosoftOAuth().get_expiry(user)


@pytest.mark.django_db(transaction=True)
@pytest.mark.asyncio
async def test_get_expiry(django_user_model: User) -> None:
    user = await django_user_model.objects.acreate(username="<EMAIL>", email="<EMAIL>")
    expiry = datetime.datetime.now(datetime.timezone.utc)
    await OAuthCredentials.objects.acreate(
        user=user,
        integration="microsoft",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=expiry,
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    assert await MicrosoftOAuth().get_expiry(user) == expiry


def test_get_user_info_success(monkeypatch: pytest.MonkeyPatch) -> None:
    mock_get = MagicMock(
        return_value=MagicMock(
            status_code=200,
            json=MagicMock(return_value={"user": "info"}),
        )
    )
    monkeypatch.setattr(requests, "get", mock_get)

    user_info = MicrosoftOAuth.get_user_info("test_token")

    assert user_info == {"user": "info"}
    mock_get.assert_called_once_with(
        "https://graph.microsoft.com/v1.0/me", headers={"Authorization": "Bearer test_token"}
    )


def test_get_user_info_failure(monkeypatch: pytest.MonkeyPatch, caplog: pytest.LogCaptureFixture) -> None:
    mock_get = MagicMock(return_value=MagicMock(status_code=401))
    monkeypatch.setattr(requests, "get", mock_get)

    user_info = MicrosoftOAuth.get_user_info("test_token")

    assert not user_info
    assert "Error validating Microsoft access token: status 401" in caplog.text
    mock_get.assert_called_once_with(
        "https://graph.microsoft.com/v1.0/me", headers={"Authorization": "Bearer test_token"}
    )


def test_get_user_info_exception(monkeypatch: pytest.MonkeyPatch, caplog: pytest.LogCaptureFixture) -> None:
    mock_get = MagicMock(side_effect=Exception("Test exception"))
    monkeypatch.setattr(requests, "get", mock_get)

    user_info = MicrosoftOAuth.get_user_info("test_token")

    assert not user_info
    assert "Error validating Microsoft access token" in caplog.text
    assert "Test exception" in caplog.text
    mock_get.assert_called_once_with(
        "https://graph.microsoft.com/v1.0/me", headers={"Authorization": "Bearer test_token"}
    )
