import datetime
import logging
from typing import Annotated

import pydantic
import requests
from django.conf import settings
from django.utils import timezone

from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.users.models.user import User


class _TokenResponse(pydantic.BaseModel):
    access_token: Annotated[str, pydantic.StringConstraints(min_length=1)]
    refresh_token: str | None = None
    expires_in: pydantic.PositiveInt
    scope: str | None = None
    token_type: str | None = None


class AdvisorEngineOAuth:
    def __init__(self) -> None:
        self.oauth_url = settings.ADVISORENGINE_OAUTH_BASE_URL
        self.client_id = settings.ADVISORENGINE_CLIENT_ID
        self.client_secret = settings.ADVISORENGINE_CLIENT_SECRET
        self.scopes = ["openid", "offline_access", "ae_crm.ext_partner"]

    def exchange_tokens(self, authorization_code: str, code_verifier: str, user: User, redirect_uri: str) -> None:
        token_url = f"{self.oauth_url}/connect/token"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}

        payload = {
            "grant_type": "authorization_code",
            "code": authorization_code,
            "redirect_uri": redirect_uri,
            "client_id": self.client_id,
            "code_verifier": code_verifier,
        }

        # Adding client_secret if available as some PKCE implementations still use it
        if self.client_secret:
            payload["client_secret"] = self.client_secret

        response = requests.post(token_url, data=payload, headers=headers, timeout=30)

        if response.status_code != 200:
            logging.error(
                "Error response from AdvisorEngine token exchange: %s - %s", response.status_code, response.text
            )
            raise Exception(f"Token exchange failed with status {response.status_code}: {response.text}")

        data = response.json()

        parsed_response = _TokenResponse.model_validate(data, strict=True)

        if not parsed_response.refresh_token:
            logging.warning("No refresh token received from AdvisorEngine for user %s", user.uuid)

        expiry_date = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(
            seconds=parsed_response.expires_in
        )

        oauth_credentials, created = OAuthCredentials.objects.update_or_create(
            user=user,
            integration="advisor_engine",
            defaults={
                "access_token": parsed_response.access_token,
                "refresh_token": parsed_response.refresh_token or "",
                "expires_in": expiry_date,
                "refresh_token_expires_in": datetime.datetime.now(datetime.timezone.utc)
                + datetime.timedelta(days=90),  # Typical refresh token lifetime
                "scope": parsed_response.scope or " ".join(self.scopes),
            },
        )

        logging.info(
            "%s AdvisorEngine OAuth credentials: %s", "Created" if created else "Updated", oauth_credentials.uuid
        )

    def _refresh_access_token(self, oauth_credentials: OAuthCredentials) -> str:
        token_url = f"{self.oauth_url}/connect/token"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}

        payload = {
            "client_id": self.client_id,
            "grant_type": "refresh_token",
            "refresh_token": oauth_credentials.refresh_token,
        }

        if self.client_secret:
            payload["client_secret"] = self.client_secret

        response = requests.post(token_url, data=payload, headers=headers)

        if response.status_code != 200:
            logging.error(
                "Error response from AdvisorEngine token refresh: %s - %s", response.status_code, response.text
            )
            raise Exception(f"Token refresh failed with status {response.status_code}: {response.text}")

        data = response.json()

        parsed_response = _TokenResponse.model_validate(data)

        oauth_credentials.access_token = parsed_response.access_token
        if parsed_response.refresh_token:
            oauth_credentials.refresh_token = parsed_response.refresh_token

        oauth_credentials.expires_in = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(
            seconds=parsed_response.expires_in
        )

        if parsed_response.scope:
            oauth_credentials.scope = parsed_response.scope

        oauth_credentials.save()
        logging.info("Refreshed AdvisorEngine OAuth credentials: %s", oauth_credentials.uuid)

        return oauth_credentials.access_token

    def get_access_token(self, user: User) -> str | None:
        try:
            oauth_credentials = OAuthCredentials.objects.get(user=user, integration="advisor_engine")
            expiry = oauth_credentials.expires_in
        except OAuthCredentials.DoesNotExist:
            logging.info("No AdvisorEngine credentials found for user: %s", user.uuid)
            return None
        if expiry < timezone.now():
            logging.info("AdvisorEngine access token expired, refreshing for user: %s", user.uuid)
            access_token = self._refresh_access_token(oauth_credentials)
            return access_token
        return oauth_credentials.access_token
