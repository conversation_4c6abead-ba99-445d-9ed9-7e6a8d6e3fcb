import datetime
import logging
from typing import Annotated, Any, Mapping

import msal
import pydantic
import requests
from django.conf import settings
from django.utils import timezone

from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.users.models.user import User


# A model representing a Microsoft token exchange response.
class _TokenResponse(pydantic.BaseModel):
    access_token: Annotated[str, pydantic.StringConstraints(min_length=1)]
    refresh_token: str | None = None
    expires_in: pydantic.PositiveInt
    scope: str | None = None


# Performs OAuth code and token exchanges for the Microsoft OAuth2 integration.
class MicrosoftOAuth:
    def __init__(self) -> None:
        self.app = msal.ConfidentialClientApplication(
            client_id=self.client_id(), client_credential=self.client_secret()
        )
        self.scopes = ["User.Read", "Calendars.Read"]

    # The client ID used for Microsoft OAuth token exchanges.
    @classmethod
    def client_id(cls) -> str:
        return settings.MSAL_CLIENT_ID  # type: ignore[no-any-return]

    # The client secret used for Microsoft OAuth token exchanges.
    @classmethod
    def client_secret(cls) -> str:
        return settings.MSAL_CLIENT_SECRET  # type: ignore[no-any-return]

    @classmethod
    # Validates a Microsoft access token, returning the user information if valid.
    def get_user_info(cls, access_token: str) -> Mapping[str, Any] | None:
        url = "https://graph.microsoft.com/v1.0/me"
        headers = {"Authorization": f"Bearer {access_token}"}
        try:
            response = requests.get(url, headers=headers)
            if response.status_code != 200:
                logging.error("Error validating Microsoft access token: status %d", response.status_code)
                return None
            return response.json()  # type: ignore[no-any-return]
        except Exception as e:
            logging.error("Error validating Microsoft access token", exc_info=e)
            return None

    # Exchanges an authorization code for an access token and (potentially) refresh token.
    def exchange_tokens(self, authorization_code: str, user: User, redirect_uri: str) -> None:
        try:
            response = self.app.acquire_token_by_authorization_code(
                authorization_code,
                scopes=self.scopes,
                redirect_uri=redirect_uri,
            )
            if response.get("error"):
                raise Exception(f"Error exchanging tokens: {response}")

            parsed_response = _TokenResponse.model_validate(response, strict=True)
            if not parsed_response.refresh_token:
                raise Exception("Missing refresh token")

            expiry_date = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(
                seconds=parsed_response.expires_in
            )
            OAuthCredentials.objects.update_or_create(
                user=user,
                integration="microsoft",
                defaults={
                    "access_token": parsed_response.access_token,
                    "refresh_token": parsed_response.refresh_token,
                    "expires_in": expiry_date,
                    # We don't have a way to determine this value from the response. Based on
                    # https://learn.microsoft.com/en-us/answers/questions/1181800/how-to-change-expiry-date-of-refresh-token,
                    # 90 days seems like a reasonable guess.
                    "refresh_token_expires_in": datetime.datetime.now(datetime.timezone.utc)
                    + datetime.timedelta(days=90),
                    "scope": parsed_response.scope or " ".join(self.scopes),
                },
            )
        except Exception as e:
            logging.error(f"Error exchanging tokens: {e} for user {user}")
            raise Exception("Error exchanging tokens") from e

    # Attemps to refresh access tokens using a refresh token.
    #
    # Returns the updated access token if successful, and stores it in the database.
    async def _refresh_access_token(self, oauth_credentials: OAuthCredentials) -> str:
        try:
            response = self.app.acquire_token_by_refresh_token(oauth_credentials.refresh_token, self.scopes)
            if response.get("error"):
                raise Exception(f"Error exchanging tokens: {response}")

            parsed_response = _TokenResponse.model_validate(response)

            oauth_credentials.access_token = parsed_response.access_token
            if parsed_response.refresh_token:
                oauth_credentials.refresh_token = parsed_response.refresh_token
            oauth_credentials.expires_in = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(
                seconds=parsed_response.expires_in
            )
            if parsed_response.scope:
                oauth_credentials.scope = parsed_response.scope

            logging.info("Saving refresh OAuth credentials: %s", oauth_credentials.uuid)
            await oauth_credentials.asave()
            return oauth_credentials.access_token
        except Exception as e:
            logging.error("Error refreshing access token: %s", e)
            raise Exception("Error refreshing access token") from e

    # Gets an access token for a user.
    #
    # This will attempt to refresh the token if the token expiration date has passed.
    async def get_access_token(self, user: User) -> str | None:
        try:
            oauth_credentials = await OAuthCredentials.objects.aget(user=user, integration="microsoft")
        except OAuthCredentials.DoesNotExist:
            logging.info("No credentials found for user: %s", user.uuid)
            return None
        try:
            expiry = oauth_credentials.expires_in
            if expiry < timezone.now():
                logging.info("Access token expired, refreshing token for user: %s", user.uuid)
                access_token = await self._refresh_access_token(oauth_credentials)
                return access_token
            else:
                return oauth_credentials.access_token
        except Exception as e:
            logging.error("Error getting access token: %s", e)
            raise Exception("Error getting access token") from e

    # Gets the refresh token for a user.
    #
    # This will only return a saved refresh token; it will not attempt to get a refresh token if one does not exist.
    def get_refresh_token(self, user: User) -> str | None:
        try:
            return OAuthCredentials.objects.get(user=user, integration="microsoft").refresh_token
        except OAuthCredentials.DoesNotExist:
            logging.info("No credentials found for user: %s", user.uuid)
            return None

    # Gets the access token expiration date for a user.
    async def get_expiry(self, user: User) -> datetime.datetime | None:
        try:
            oauth_credentials = await OAuthCredentials.objects.aget(user=user, integration="microsoft")
            return oauth_credentials.expires_in
        except OAuthCredentials.DoesNotExist:
            logging.info("No credentials found for user: %s", user.uuid)
            return None
