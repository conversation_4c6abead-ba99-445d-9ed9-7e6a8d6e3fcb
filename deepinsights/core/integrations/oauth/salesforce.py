import datetime
import logging
from typing import Annotated

import httpx
import pydantic
from django.conf import settings
from django.utils import timezone

from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.users.models.user import User


# A response from the token exchange endpoint.
class _TokenResponse(pydantic.BaseModel):
    access_token: Annotated[str, pydantic.StringConstraints(min_length=1)]
    refresh_token: str | None = None
    scope: str | None = None
    instance_url: str | None = None


# A response from the token introspection endpoint.
# https://help.salesforce.com/s/articleView?id=sf.remoteaccess_oidc_token_introspection_endpoint.htm&type=5
class _IntrospectionResponse(pydantic.BaseModel):
    # The expiration time of the token (if there is an expiration time).
    exp: pydantic.PositiveInt | None = None


class SalesforceOAuth:
    def __init__(self, client: httpx.Client | None = None):
        self.client = client or httpx.Client()
        self.client_id = settings.SALESFORCE_CONSUMER_KEY
        self.client_secret = settings.SALESFORCE_CONSUMER_SECRET

    # Makes a response to a Salesforce token endpoint.
    def _make_salesforce_token_request(self, url: str, parameters: dict[str, str]) -> dict:  # type: ignore[type-arg]
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        payload = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
        }
        payload.update(parameters)
        response = self.client.post(url, data=payload, headers=headers)
        if response.status_code != 200:
            raise Exception(f"HTTP error {response.status_code}")
        return response.json()  # type: ignore[no-any-return]

    # Given a payload for a token exchange request (which can be an initial exchange or a refresh),
    # returns the token exchange response, the (optional) access token expiry date and the
    # (optional) refresh token expiry date.
    def _token_response_with_expiration_times(
        self, token_payload: dict[str, str]
    ) -> tuple[_TokenResponse, datetime.datetime | None, datetime.datetime | None]:
        # Exchange the authorization code for an access token and (optionally) refresh token.
        data = self._make_salesforce_token_request("https://login.salesforce.com/services/oauth2/token", token_payload)

        parsed_response = _TokenResponse.model_validate(data, strict=True)

        parsed_introspection_response = None
        try:
            # Determine the expiration date of the access token. This should generally succeed, but
            # this method can return None in case of transient failures.
            introspection_data = self._make_salesforce_token_request(
                "https://login.salesforce.com/services/oauth2/introspect",
                {
                    "token": parsed_response.access_token,
                    "token_type_hint": "access_token",
                },
            )
            parsed_introspection_response = _IntrospectionResponse.model_validate(introspection_data, strict=True)
        except Exception as e:
            logging.error(f"Error getting expiration time for access token; using default values: {e}")

        parsed_refresh_introspection_response = None
        try:
            # Determine the expiration date of the refresh token (if any).
            if parsed_response.refresh_token:
                refresh_introspection_data = self._make_salesforce_token_request(
                    "https://login.salesforce.com/services/oauth2/introspect",
                    {
                        "token": parsed_response.refresh_token,
                        "token_type_hint": "refresh_token",
                    },
                )
                parsed_refresh_introspection_response = _IntrospectionResponse.model_validate(
                    refresh_introspection_data, strict=True
                )
        except Exception as e:
            logging.error(f"Error getting expiration times for tokens; using default values: {e}")

        # Every response should have an expiration time for the access token. If not,
        # based on a quick search it appears that two hours is a common default.
        access_token_expiry = (
            datetime.datetime.fromtimestamp(parsed_introspection_response.exp, tz=datetime.timezone.utc)
            if parsed_introspection_response and parsed_introspection_response.exp
            else None
        )
        # If the refresh token has an expiration time, use it. Otherwise, set it to "forever".
        refresh_token_expiry = (
            datetime.datetime.fromtimestamp(parsed_refresh_introspection_response.exp, tz=datetime.timezone.utc)
            if parsed_refresh_introspection_response and parsed_refresh_introspection_response.exp
            else None
        )
        return (parsed_response, access_token_expiry, refresh_token_expiry)

    # Update the user's CRM configuration with the Salesforce endpoint, which is returned in the
    # token exchange response.
    def _update_salesforce_instance_url(self, user: User, instance_url: str | None):  # type: ignore[no-untyped-def]
        if not instance_url:
            return
        crm_configuration = user.get_crm_configuration()
        crm_configuration.salesforce.salesforce_endpoint = instance_url
        user.crm_configuration = crm_configuration.to_dict()
        user.save()

    # Exchanges an authorization code for an access token and (potentially) refresh token.
    def exchange_tokens(self, authorization_code: str, user: User, redirect_uri: str):  # type: ignore[no-untyped-def]
        try:
            token_response, access_token_expiry, refresh_token_expiry = self._token_response_with_expiration_times(
                {
                    "code": authorization_code,
                    "grant_type": "authorization_code",
                    "redirect_uri": redirect_uri,
                }
            )
            if not token_response.refresh_token:
                raise Exception("Missing refresh token")
            if not token_response.instance_url:
                raise Exception("Missing instance URL")

            # Update the stored OAuth credentials.
            OAuthCredentials.objects.update_or_create(
                user=user,
                integration="salesforce",
                defaults={
                    "access_token": token_response.access_token,
                    "refresh_token": token_response.refresh_token,
                    "expires_in": access_token_expiry or timezone.now() + datetime.timedelta(hours=2),
                    "refresh_token_expires_in": refresh_token_expiry or timezone.now() + datetime.timedelta(weeks=5200),
                    "scope": token_response.scope or "",
                },
            )

            self._update_salesforce_instance_url(user, token_response.instance_url)
        except Exception as e:
            logging.error(f"Error exchanging tokens: {e} for user {user}")
            raise Exception("Error exchanging tokens") from e

    # Attemps to refresh access tokens using a refresh token.
    #
    # Returns the updated access token if successful, and stores it in the database.
    def _refresh_access_token(self, user: User, oauth_credentials: OAuthCredentials) -> str:
        try:
            token_response, access_token_expiry, refresh_token_expiry = self._token_response_with_expiration_times(
                {
                    "grant_type": "refresh_token",
                    "refresh_token": oauth_credentials.refresh_token,
                }
            )

            oauth_credentials.access_token = token_response.access_token
            if token_response.refresh_token:
                oauth_credentials.refresh_token = token_response.refresh_token
            if access_token_expiry:
                oauth_credentials.expires_in = access_token_expiry
            if refresh_token_expiry:
                oauth_credentials.refresh_token_expires_in = refresh_token_expiry
            if token_response.scope:
                oauth_credentials.scope = token_response.scope
            logging.info("Saving refresh OAuth credentials: %s", oauth_credentials.uuid)
            oauth_credentials.save()

            self._update_salesforce_instance_url(user, token_response.instance_url)

            return oauth_credentials.access_token
        except Exception as e:
            logging.error("Error refreshing access token: %s", e)
            raise Exception("Error refreshing access token") from e

    # Gets an access token for a user.
    #
    # This will attempt to refresh the token if the token expiration date has passed.
    #
    # Raises an exception if there is an error retrieving a refresh token.
    def get_access_token(self, user: User) -> str | None:
        try:
            oauth_credentials = OAuthCredentials.objects.get(user=user, integration="salesforce")
        except OAuthCredentials.DoesNotExist:
            logging.info("No credentials found for user: %s", user.uuid)
            return None
        try:
            expiry = oauth_credentials.expires_in
            if expiry < timezone.now():
                logging.info("Access token expired, refreshing token for user: %s", user.uuid)
                access_token = self._refresh_access_token(user, oauth_credentials)
                return access_token
            else:
                return oauth_credentials.access_token
        except Exception as e:
            logging.error("Error getting access token: %s", e)
            raise Exception("Error getting access token") from e
