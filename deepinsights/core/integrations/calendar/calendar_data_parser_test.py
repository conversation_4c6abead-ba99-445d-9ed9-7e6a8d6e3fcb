import pytest

from deepinsights.core.integrations.calendar.calendar_data_parser import (
    extract_crm_linked_entity_info,
    find_urls,
    should_ignore_event_by_keywords,
)


def test_find_urls_with_valid_meeting_urls() -> None:
    text = """
    Here are some meeting links:
    https://meet.google.com/abc-defg-hij
    https://teams.microsoft.com/l/meetup-join/19%3ameeting_NmE2YzY1YzgtYzY2Zi00YzY2LWE2YzYtYzY2YzY2YzY2YzY2%40thread.v2/0?context=%7b%22Tid%22%3a%22abc%22%2c%22Oid%22%3a%22def%22%7d
    https://zoom.us/j/1234567890
    https://foo.zoom.us/j/123-4567890
    https://foo.webex.com/abc-defg-hij
    """
    expected_urls = [
        "https://meet.google.com/abc-defg-hij",
        "https://teams.microsoft.com/l/meetup-join/19%3ameeting_NmE2YzY1YzgtYzY2Zi00YzY2LWE2YzYtYzY2YzY2YzY2YzY2%40thread.v2/0?context=%7b%22Tid%22%3a%22abc%22%2c%22Oid%22%3a%22def%22%7d",
        "https://zoom.us/j/1234567890",
        "https://foo.zoom.us/j/123-4567890",
        "https://foo.webex.com/abc-defg-hij",
    ]
    assert find_urls(text) == expected_urls


def test_handling_of_whitespace() -> None:
    text = "\thttps://meet.google.com/abc-defg-hij\r\nhttps://teams.microsoft.com/test https://zoom.us/foo\r"
    expected_urls = [
        "https://meet.google.com/abc-defg-hij",
        "https://teams.microsoft.com/test",
        "https://zoom.us/foo",
    ]
    assert find_urls(text) == expected_urls


def test_find_urls_with_no_meeting_urls() -> None:
    text = """
    Here are some random links:
    https://example.com
    https://anotherexample.com
    """
    assert find_urls(text) == []


def test_find_urls_with_mixed_content() -> None:
    text = """
    Here are some links:
    https://example.com
    https://meet.google.com/abc-defg-hij
    Some text in between
    https://zoom.us/j/1234567890
    """
    expected_urls = ["https://meet.google.com/abc-defg-hij", "https://zoom.us/j/1234567890"]
    assert find_urls(text) == expected_urls


def test_find_urls_with_invalid_urls() -> None:
    text = """
    Here are some invalid meeting links:
    meet.google.com/abc-defg-hij
    teams.microsoft.com/l/meetup-join/19%3ameeting_NmE2YzY1YzgtYzY2Zi00YzY2LWE2YzYtYzY2YzY2YzY2YzY2%40thread.v2/0?context=%7b%22Tid%22%3a%22abc%22%2c%22Oid%22%3a%22def%22%7d
    zoom.us/j/1234567890
    """
    assert find_urls(text) == []


def test_find_urls_without_meeting_info() -> None:
    text = """
    Edge cases:
    https://meet.google.com
    https://zoom.us
    """
    assert find_urls(text) == []


def test_html() -> None:
    text = """
    <table><tbody><tr><td><br>
    <a href="https://foo.zoom.us/j/123-4567890" target="_blank">URL</a>
    <a href="https://meet.google.com/test/meeting" target="_blank">URL</a>
    <a href='https://test.webex.com/test/meeting' target="_blank">URL</a>
    >https://meet.google.com/test/meeting<https://meet.google.com/test/meeting2<
    <https://meet.google.com/test/meeting3>
    <https://teams.microsoft.com/l/meetup-join/19%3ameeting_ZGI2NzY2MWQtM2E0Zi00MzU1LTliN2EtZGVhMTYzYjMzNzlh%40thread.v2/0?context=%7b%22Tid%22%3a%2209fd564e-bf42-4321-8f2d-b8e482f8635c%22%2c%22Oid%22%3a%22e8d89c93-338e-4191-a6eb-daae2bce18b6%22%7d>
    </td></tr></tbody></table>
    """
    assert find_urls(text) == [
        "https://foo.zoom.us/j/123-4567890",
        "https://meet.google.com/test/meeting",
        "https://test.webex.com/test/meeting",
        "https://meet.google.com/test/meeting",
        "https://meet.google.com/test/meeting2",
        "https://meet.google.com/test/meeting3",
        "https://teams.microsoft.com/l/meetup-join/19%3ameeting_ZGI2NzY2MWQtM2E0Zi00MzU1LTliN2EtZGVhMTYzYjMzNzlh%40thread.v2/0?context=%7b%22Tid%22%3a%2209fd564e-bf42-4321-8f2d-b8e482f8635c%22%2c%22Oid%22%3a%22e8d89c93-338e-4191-a6eb-daae2bce18b6%22%7d",
    ]


@pytest.mark.parametrize(
    "input_text,expected_result",
    [
        # Valid Salesforce case ID
        (
            "This text contains a [ZDATA]<salesforce_case_id>SF12345</salesforce_case_id>[/ZDATA] reference",
            {"id": "SF12345", "name": ""},
        ),
        # Valid Salesforce case ID with surrounding text
        (
            "Before [ZDATA]<salesforce_case_id>SF67890</salesforce_case_id>[/ZDATA] After",
            {"id": "SF67890", "name": ""},
        ),
        # Valid Salesforce case ID with special characters
        (
            "[ZDATA]<salesforce_case_id>SF-123_456.789</salesforce_case_id>[/ZDATA]",
            {"id": "SF-123_456.789", "name": ""},
        ),
        # No Salesforce case ID
        (
            "This text contains no CRM reference",
            None,
        ),
        # Invalid format - missing opening tag
        (
            "[ZDATA]salesforce_case_id>ABC123</salesforce_case_id>[/ZDATA]",
            None,
        ),
        # Invalid format - missing closing tag
        (
            "[ZDATA]<salesforce_case_id>ABC123[/ZDATA]",
            None,
        ),
        # Empty text
        (
            "",
            None,
        ),
        # Empty case ID
        (
            "[ZDATA]<salesforce_case_id></salesforce_case_id>[/ZDATA]",
            {"id": "", "name": ""},
        ),
    ],
)
def test_extract_crm_linked_entity_info(input_text: str, expected_result: dict[str, str] | None) -> None:
    result = extract_crm_linked_entity_info(input_text)
    assert result == expected_result


def test_empty_ignored_keywords() -> None:
    assert not should_ignore_event_by_keywords("Team Meeting", [])


def test_no_matching_keywords() -> None:
    assert not should_ignore_event_by_keywords("Team Meeting", ["vacation", "holiday"])
    assert not should_ignore_event_by_keywords("Project Review", ["lunch", "break"])


def test_exact_keyword_match() -> None:
    assert should_ignore_event_by_keywords("Vacation Day", ["vacation"])
    assert should_ignore_event_by_keywords("Holiday Party", ["holiday"])


def test_partial_keyword_match() -> None:
    assert should_ignore_event_by_keywords("Summer Vacation Planning", ["vacation"])
    assert should_ignore_event_by_keywords("Holiday Shopping", ["holiday"])
    assert should_ignore_event_by_keywords("Lunch and Learn", ["lunch"])


def test_case_insensitive_matching() -> None:
    assert should_ignore_event_by_keywords("VACATION DAY", ["vacation"])
    assert should_ignore_event_by_keywords("vacation day", ["VACATION"])
    assert should_ignore_event_by_keywords("Holiday Party", ["HOLIDAY"])
    assert should_ignore_event_by_keywords("TEAM LUNCH", ["lunch"])


def test_whitespace_handling() -> None:
    assert should_ignore_event_by_keywords("  Team Meeting  ", ["meeting"])
    assert not should_ignore_event_by_keywords("Daily Standup", ["  meeting  "])
    assert should_ignore_event_by_keywords("Daily Standup", ["  standup  "])
    assert should_ignore_event_by_keywords("  Vacation Day  ", ["  vacation  "])


def test_multiple_keywords() -> None:
    keywords = ["vacation", "holiday", "lunch", "break"]
    assert not should_ignore_event_by_keywords("Team Meeting", keywords)
    assert should_ignore_event_by_keywords("Vacation Planning", keywords)
    assert should_ignore_event_by_keywords("Holiday Party", keywords)
    assert should_ignore_event_by_keywords("Lunch Meeting", keywords)
    assert should_ignore_event_by_keywords("Coffee Break", keywords)


def test_empty_and_whitespace_keywords() -> None:
    keywords = ["", "  ", "vacation", "holiday"]
    assert not should_ignore_event_by_keywords("Team Meeting", keywords)
    assert should_ignore_event_by_keywords("Vacation Day", keywords)
    assert should_ignore_event_by_keywords("Holiday Party", keywords)


def test_special_characters() -> None:
    assert should_ignore_event_by_keywords("Café Meeting", ["café"])
    assert should_ignore_event_by_keywords("Team-Building Event", ["team-building"])
    assert should_ignore_event_by_keywords("Q&A Session", ["q&a"])


def test_unicode_case_folding() -> None:
    # German ß should match SS when case-folded
    assert should_ignore_event_by_keywords("Straße Meeting", ["strasse"])
    assert should_ignore_event_by_keywords("STRASSE Planning", ["straße"])


def test_edge_cases() -> None:
    assert should_ignore_event_by_keywords("A Team Meeting", ["a"])

    # Very long event title
    long_title = "This is a very long event title that goes on and on with vacation planning"
    assert should_ignore_event_by_keywords(long_title, ["vacation"])

    assert should_ignore_event_by_keywords("Vacation Day", ["vacation"])
    assert should_ignore_event_by_keywords("Summer Vacation", ["vacation"])
    assert should_ignore_event_by_keywords("Plan Vacation Time", ["vacation"])


def test_no_false_positives() -> None:
    assert not should_ignore_event_by_keywords("Vocation Training", ["vacation"])
    assert not should_ignore_event_by_keywords("Holistic Review", ["holiday"])
    assert not should_ignore_event_by_keywords("Launch Event", ["lunch"])
