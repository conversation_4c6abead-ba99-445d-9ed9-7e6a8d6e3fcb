import datetime
import logging
from typing import Literal
from unittest.mock import ANY, Async<PERSON>ock, MagicMock, patch

import pydantic
import pytest
from asgiref.sync import async_to_sync
from django.utils import timezone
from pytest_django.fixtures import SettingsWrapper

from deepinsights.core.integrations.calendar.calendar_models import Calendar<PERSON>vent, EventParticipant
from deepinsights.core.integrations.calendar.events_fetcher import fetch_calendar_events
from deepinsights.flags import flag_loader
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.oauth_credentials import OAuthClientCredentials, OAuthCredentials
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.flags import Flag
from deepinsights.users.models.user import User

pytestmark = pytest.mark.django_db


@pytest.fixture
def test_user(django_user_model: User) -> User:
    return django_user_model.objects.create(username="<EMAIL>")


def _enable_crm_events_flag() -> None:
    flag_loader.load_flags()
    f = Flag.objects.get(name=Flags.EnableCalendarEventsFromCRM.name)
    f.everyone = True
    f.override_enabled_by_environment = None  # To ensure no environment override
    f.save()


@patch("deepinsights.core.integrations.calendar.events_fetcher.MicrosoftOAuth")
@patch("deepinsights.core.integrations.calendar.events_fetcher.GoogleOAuth")
def test_full_failure(google_oauth: MagicMock, ms_oauth: MagicMock, test_user: User) -> None:
    google_oauth.return_value.get_access_token.side_effect = Exception("Test error")
    ms_oauth.return_value.get_access_token.side_effect = Exception("Test error")

    with pytest.raises(Exception):
        assert not async_to_sync(fetch_calendar_events)(test_user, datetime.timedelta(days=1))


@patch("deepinsights.core.integrations.calendar.events_fetcher.MicrosoftOAuth")
@patch("deepinsights.core.integrations.calendar.events_fetcher.GoogleOAuth")
@patch("deepinsights.core.integrations.crm.redtail.Redtail.fetch_events")
def test_full_failure_with_crm_events(
    mock_fetch_events: MagicMock,
    google_oauth: MagicMock,
    ms_oauth: MagicMock,
    test_user: User,
) -> None:
    _enable_crm_events_flag()
    google_oauth.return_value.get_access_token.side_effect = Exception("Test error")
    ms_oauth.return_value.get_access_token.side_effect = Exception("Test error")
    crm_config = test_user.get_crm_configuration()
    crm_config.crm_system = "redtail"
    test_user.crm_configuration = crm_config.to_dict()
    test_user.save()
    mock_fetch_events.side_effect = MagicMock(side_effect=Exception("Test error"))

    with pytest.raises(Exception):
        assert not async_to_sync(fetch_calendar_events)(test_user, datetime.timedelta(days=1))


@patch("deepinsights.core.integrations.calendar.events_fetcher.google.fetch_calendar_events")
@patch("deepinsights.core.integrations.calendar.events_fetcher.MicrosoftOAuth")
@patch("deepinsights.core.integrations.calendar.events_fetcher.GoogleOAuth")
def test_microsoft_failure(
    google_oauth: MagicMock, ms_oauth: MagicMock, google_fetch: MagicMock, test_user: User
) -> None:
    ms_oauth.return_value.get_access_token.side_effect = Exception("Test error")
    google_oauth.return_value.get_access_token = AsyncMock(return_value="123")
    google_fetch.return_value = [
        CalendarEvent(
            provider="google",
            id="123",
            user_specific_id="123",
            title="Test meeting",
            start_time=timezone.now(),
            end_time=timezone.now() + datetime.timedelta(hours=1),
            all_day=False,
            participants=[],
            meeting_urls=[],
            body="Test Body",
        ),
    ]

    events, all_succeeded = async_to_sync(fetch_calendar_events)(test_user, datetime.timedelta(days=1))
    assert not all_succeeded
    assert events == google_fetch.return_value


@patch("deepinsights.core.integrations.calendar.events_fetcher.microsoft.fetch_calendar_events")
@patch("deepinsights.core.integrations.calendar.events_fetcher.MicrosoftOAuth")
@patch("deepinsights.core.integrations.calendar.events_fetcher.GoogleOAuth")
def test_google_failure(google_oauth: MagicMock, ms_oauth: MagicMock, ms_fetch: MagicMock, test_user: User) -> None:
    google_oauth.return_value.get_access_token.side_effect = Exception("Test error")
    ms_oauth.return_value.get_access_token = AsyncMock(return_value="123")
    ms_oauth.return_value.get_expiry = AsyncMock(return_value=timezone.now())
    ms_fetch.return_value = [
        CalendarEvent(
            provider="microsoft",
            id="123",
            user_specific_id="u123",
            title="Test meeting",
            start_time=timezone.now(),
            end_time=timezone.now() + datetime.timedelta(hours=1),
            all_day=False,
            participants=[],
            meeting_urls=[],
            body="Test Body",
        ),
    ]

    events, all_succeeded = async_to_sync(fetch_calendar_events)(test_user, datetime.timedelta(days=1))

    assert not all_succeeded
    assert events == ms_fetch.return_value


@patch("deepinsights.core.integrations.calendar.events_fetcher.microsoft.fetch_calendar_events")
@patch("deepinsights.core.integrations.calendar.events_fetcher.google.fetch_calendar_events")
@patch("deepinsights.core.integrations.calendar.events_fetcher.MicrosoftOAuth")
@patch("deepinsights.core.integrations.calendar.events_fetcher.GoogleOAuth")
def test_no_calendar_events(
    google_oauth: MagicMock, ms_oauth: MagicMock, google_fetch: MagicMock, ms_fetch: MagicMock, test_user: User
) -> None:
    google_oauth.return_value.get_access_token = AsyncMock(return_value="123")
    ms_oauth.return_value.get_access_token = AsyncMock(return_value="123")
    ms_oauth.return_value.get_expiry = AsyncMock(return_value=timezone.now())

    google_fetch.return_value = []
    ms_fetch.return_value = []

    events, all_succeeded = async_to_sync(fetch_calendar_events)(test_user, datetime.timedelta(days=1))

    assert all_succeeded
    assert not events


@patch("deepinsights.core.integrations.calendar.events_fetcher.microsoft.fetch_calendar_events")
@patch("deepinsights.core.integrations.calendar.events_fetcher.MicrosoftOAuth")
def test_microsoft_calendar_events(ms_oauth: MagicMock, mock_fetch_calendar_events: MagicMock, test_user: User) -> None:
    ms_oauth.return_value.get_access_token = AsyncMock(return_value="123")
    ms_oauth.return_value.get_expiry = AsyncMock(return_value=timezone.now())

    date = timezone.now()
    events = [
        CalendarEvent(
            provider="microsoft",
            id="123",
            user_specific_id="u123",
            title="Test meeting",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
            body="Test Body",
        ),
        CalendarEvent(
            provider="microsoft",
            id="234",
            user_specific_id="u234",
            title="Test meeting 2",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[EventParticipant(id="2", email_address="<EMAIL>", name="Test Example 2")],
            meeting_urls=[pydantic.HttpUrl("https://example2.com")],
            body="Test Body",
        ),
        CalendarEvent(
            provider="microsoft",
            id="345",
            user_specific_id="u345",
            title="Test meeting 3",
            start_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc),
            end_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc)
            + datetime.timedelta(days=1),
            all_day=True,
            participants=[],
            meeting_urls=[],
            body="Test Body",
        ),
    ]
    mock_fetch_calendar_events.return_value = events

    actual_events, all_succeeded = async_to_sync(fetch_calendar_events)(test_user, datetime.timedelta(days=1))

    assert all_succeeded
    assert actual_events == events


@patch("deepinsights.core.integrations.calendar.events_fetcher.microsoft.fetch_calendar_events")
@patch("deepinsights.core.integrations.calendar.events_fetcher.microsoft.MicrosoftCredentials")
@patch(
    "deepinsights.core.integrations.calendar.events_fetcher.Flags.EnableOrgLevelMicrosoftCalendarIntegration.is_active_for_user",
    return_value=False,
)
def test_microsoft_calendar_events_with_client_credentials_flag_disabled(
    mock_enable_org_level_integration_active: MagicMock,
    mock_microsoft_credentials: MagicMock,
    mock_fetch_calendar_events: MagicMock,
    test_user: User,
    settings: SettingsWrapper,
) -> None:
    settings.MSAL_CLIENT_ID = "test_client_id"
    settings.MSAL_CLIENT_SECRET = "test_client_secret"
    org = Organization.objects.create(name="Test organization")
    test_user.organization = org
    test_user.microsoft_id = "user_id"
    test_user.save()

    OAuthClientCredentials.objects.create(
        organization=org, provider=OAuthClientCredentials.Providers.MICROSOFT, tenant_id="tenant_id"
    )

    date = timezone.now()
    events = [
        CalendarEvent(
            provider="microsoft",
            id="123",
            user_specific_id="u123",
            title="Test meeting",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
            body="Test Body",
        ),
        CalendarEvent(
            provider="microsoft",
            id="234",
            user_specific_id="u234",
            title="Test meeting 2",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[EventParticipant(id="2", email_address="<EMAIL>", name="Test Example 2")],
            meeting_urls=[pydantic.HttpUrl("https://example2.com")],
            body="Test Body",
        ),
        CalendarEvent(
            provider="microsoft",
            id="345",
            user_specific_id="u345",
            title="Test meeting 3",
            start_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc),
            end_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc)
            + datetime.timedelta(days=1),
            all_day=True,
            participants=[],
            meeting_urls=[],
            body="Test Body",
        ),
    ]
    mock_fetch_calendar_events.return_value = events
    mock_microsoft_credentials.from_client_credentials.return_value = MagicMock()

    actual_events, all_succeeded = async_to_sync(fetch_calendar_events)(test_user, datetime.timedelta(days=1))

    assert all_succeeded
    assert not actual_events
    mock_microsoft_credentials.from_client_credentials.assert_not_called()


@patch("deepinsights.core.integrations.calendar.events_fetcher.microsoft.fetch_calendar_events")
@patch("deepinsights.core.integrations.calendar.events_fetcher.microsoft.MicrosoftCredentials")
@patch(
    "deepinsights.core.integrations.calendar.events_fetcher.Flags.EnableOrgLevelMicrosoftCalendarIntegration.is_active_for_user",
    return_value=True,
)
def test_microsoft_calendar_events_with_client_credentials(
    mock_enable_org_level_integration_active: MagicMock,
    mock_microsoft_credentials: MagicMock,
    mock_fetch_calendar_events: MagicMock,
    test_user: User,
    settings: SettingsWrapper,
) -> None:
    settings.MSAL_CLIENT_ID = "test_client_id"
    settings.MSAL_CLIENT_SECRET = "test_client_secret"
    org = Organization.objects.create(name="Test organization")
    test_user.organization = org
    test_user.microsoft_id = "user_id"
    test_user.save()

    OAuthClientCredentials.objects.create(
        organization=org, provider=OAuthClientCredentials.Providers.MICROSOFT, tenant_id="tenant_id"
    )

    date = timezone.now()
    events = [
        CalendarEvent(
            provider="microsoft",
            id="123",
            user_specific_id="u123",
            title="Test meeting",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
            body="Test Body",
        ),
        CalendarEvent(
            provider="microsoft",
            id="234",
            user_specific_id="u234",
            title="Test meeting 2",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[EventParticipant(id="2", email_address="<EMAIL>", name="Test Example 2")],
            meeting_urls=[pydantic.HttpUrl("https://example2.com")],
            body="Test Body",
        ),
        CalendarEvent(
            provider="microsoft",
            id="345",
            user_specific_id="u345",
            title="Test meeting 3",
            start_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc),
            end_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc)
            + datetime.timedelta(days=1),
            all_day=True,
            participants=[],
            meeting_urls=[],
            body="Test Body",
        ),
    ]
    mock_fetch_calendar_events.return_value = events
    mock_microsoft_credentials.from_client_credentials.return_value = MagicMock()

    actual_events, all_succeeded = async_to_sync(fetch_calendar_events)(test_user, datetime.timedelta(days=1))

    assert all_succeeded
    assert actual_events == events
    mock_microsoft_credentials.from_client_credentials.assert_called_once_with(
        client_id="test_client_id",
        client_secret="test_client_secret",
        tenant_id="tenant_id",
        user_id="user_id",
    )


@patch("deepinsights.core.integrations.calendar.events_fetcher.google.fetch_calendar_events")
@patch("deepinsights.core.integrations.calendar.events_fetcher.GoogleOAuth")
@patch("deepinsights.core.integrations.calendar.events_fetcher.MicrosoftOAuth")
def test_google_calendar_events(
    microsoft_oauth: MagicMock, google_oauth: MagicMock, mock_fetch_calendar_events: MagicMock, test_user: User
) -> None:
    microsoft_oauth.return_value.get_access_token = AsyncMock(return_value=None)
    microsoft_oauth.return_value.get_expiry = AsyncMock(return_value=timezone.now())
    google_oauth.return_value.get_access_token = AsyncMock(return_value="123")

    date = timezone.now()
    events = [
        CalendarEvent(
            provider="google",
            id="123",
            user_specific_id="u123",
            title="Test meeting",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
            body="Test Body",
        ),
        CalendarEvent(
            provider="google",
            id="234",
            user_specific_id="u234",
            title="Test meeting 2",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[EventParticipant(id="2", email_address="<EMAIL>", name="Test Example 2")],
            meeting_urls=[pydantic.HttpUrl("https://example2.com")],
            body="Test Body",
        ),
        CalendarEvent(
            provider="google",
            id="345",
            user_specific_id="u345",
            title="Test meeting 3",
            start_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc),
            end_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc)
            + datetime.timedelta(days=1),
            all_day=True,
            participants=[],
            meeting_urls=[],
            body="Test Body",
        ),
    ]
    mock_fetch_calendar_events.return_value = events

    actual_events, all_succeeded = async_to_sync(fetch_calendar_events)(test_user, datetime.timedelta(days=1))

    assert all_succeeded
    assert actual_events == events


@patch("deepinsights.core.integrations.crm.redtail.Redtail.fetch_events")
def test_crm_calendar_events_flag_disabled(mock_fetch_events: MagicMock, test_user: User) -> None:
    crm_config = test_user.get_crm_configuration()
    crm_config.crm_system = "redtail"
    test_user.crm_configuration = crm_config.to_dict()
    test_user.save()
    date = timezone.now()

    redtail_events = [
        CalendarEvent(
            provider="redtail",
            id="345",
            user_specific_id="u345",
            title="Test meeting",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
            body="Test Body",
        ),
        CalendarEvent(
            provider="redtail",
            id="456",
            user_specific_id="u456",
            title="Test meeting 2",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[EventParticipant(id="2", email_address="<EMAIL>", name="Test Example 2")],
            meeting_urls=[pydantic.HttpUrl("https://example2.com")],
            body="Test Body",
        ),
    ]

    mock_fetch_events.return_value = redtail_events

    events, all_succeeded = async_to_sync(fetch_calendar_events)(test_user, datetime.timedelta(days=1))
    assert all_succeeded
    assert not events


@patch("deepinsights.core.integrations.crm.redtail.Redtail.fetch_events")
def test_crm_calendar_events(mock_fetch_events: MagicMock, test_user: User) -> None:
    _enable_crm_events_flag()

    crm_config = test_user.get_crm_configuration()
    crm_config.crm_system = "redtail"
    test_user.crm_configuration = crm_config.to_dict()
    test_user.save()
    date = timezone.now()

    redtail_events = [
        CalendarEvent(
            provider="redtail",
            id="345",
            user_specific_id="u345",
            title="Test meeting",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
            body="Test Body",
        ),
        CalendarEvent(
            provider="redtail",
            id="456",
            user_specific_id="u456",
            title="Test meeting 2",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[EventParticipant(id="2", email_address="<EMAIL>", name="Test Example 2")],
            meeting_urls=[pydantic.HttpUrl("https://example2.com")],
            body="Test Body",
        ),
        CalendarEvent(
            provider="redtail",
            id="567",
            user_specific_id="u567",
            title="Test meeting 3",
            start_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc),
            end_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc)
            + datetime.timedelta(days=1),
            all_day=True,
            participants=[],
            meeting_urls=[],
            body="Test Body",
        ),
    ]

    mock_fetch_events.return_value = redtail_events

    events, all_succeeded = async_to_sync(fetch_calendar_events)(test_user, datetime.timedelta(days=1))
    assert all_succeeded
    assert events == redtail_events


# This isn't a comprehensive test. It's mainly to ensure that ENG-635 stays fixed.
@pytest.mark.parametrize("crm_system", ["salesforce", "none", "dummy"])
@patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
@patch("deepinsights.core.integrations.calendar.events_fetcher.Flags")
def test_crm_calendar_events_other_crms(
    mock_flags: MagicMock,
    mock_salesforce: MagicMock,
    test_user: User,
    crm_system: Literal["wealthbox"] | Literal["salesforce"] | Literal["none"] | Literal["dummy"],
    caplog: pytest.LogCaptureFixture,
) -> None:
    mock_flags.EnableCalendarEventsFromCRM.is_active_for_user.return_value = True

    crm_config = test_user.get_crm_configuration()
    crm_config.crm_system = crm_system
    test_user.crm_configuration = crm_config.to_dict()
    test_user.save()

    with caplog.at_level(logging.ERROR):
        events, all_succeeded = async_to_sync(fetch_calendar_events)(test_user, datetime.timedelta(days=1))
        assert all_succeeded
        assert not events
        assert not caplog.messages


@patch("requests.get")
def test_crm_calendar_events_wealthbox(
    mock_requests_get: MagicMock,
    test_user: User,
    caplog: pytest.LogCaptureFixture,
    settings: SettingsWrapper,
) -> None:
    settings.WEALTHBOX_API_URL = "https://api.wealthbox.com"
    _enable_crm_events_flag()

    crm_config = test_user.get_crm_configuration()
    crm_config.crm_system = "wealthbox"
    test_user.crm_configuration = crm_config.to_dict()
    test_user.save()

    OAuthCredentials.objects.create(
        user=test_user,
        integration="wealthbox",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    events, all_succeeded = async_to_sync(fetch_calendar_events)(test_user, datetime.timedelta(days=1))
    assert all_succeeded
    assert not events


@patch("deepinsights.core.integrations.calendar.events_fetcher.microsoft.fetch_calendar_events")
@patch("deepinsights.core.integrations.calendar.events_fetcher.google.fetch_calendar_events")
@patch("deepinsights.core.integrations.calendar.events_fetcher.MicrosoftOAuth")
@patch("deepinsights.core.integrations.calendar.events_fetcher.GoogleOAuth")
@patch("deepinsights.core.integrations.calendar.events_fetcher.User.crm_handler")
def test_all_calendar_events(
    crm_handler: MagicMock,
    google_oauth: MagicMock,
    ms_oauth: MagicMock,
    google_fetch: MagicMock,
    ms_fetch: MagicMock,
    test_user: User,
) -> None:
    _enable_crm_events_flag()

    google_oauth.return_value.get_access_token = AsyncMock(return_value="123")
    ms_oauth.return_value.get_access_token = AsyncMock(return_value="123")
    ms_oauth.return_value.get_expiry = AsyncMock(return_value=timezone.now())

    test_user.crm_configuration = {"crm_system": "redtail"}
    test_user.save()

    date = timezone.now()
    google_events = [
        CalendarEvent(
            provider="google",
            id="345",
            user_specific_id="u345",
            title="Test meeting",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
            body="Test Body",
        ),
        CalendarEvent(
            provider="google",
            id="456",
            user_specific_id="u456",
            title="Test meeting 2",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[EventParticipant(id="2", email_address="<EMAIL>", name="Test Example 2")],
            meeting_urls=[pydantic.HttpUrl("https://example2.com")],
            body="Test Body",
        ),
        CalendarEvent(
            provider="google",
            id="567",
            user_specific_id="u567",
            title="Test meeting 3",
            start_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc),
            end_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc)
            + datetime.timedelta(days=1),
            all_day=True,
            participants=[],
            meeting_urls=[],
            body="Test Body",
        ),
    ]
    google_fetch.return_value = google_events

    ms_events = [
        CalendarEvent(
            provider="microsoft",
            id="123",
            user_specific_id="u123",
            title="Test meeting",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
            body="Test Body",
        ),
        CalendarEvent(
            provider="microsoft",
            id="234",
            user_specific_id="u234",
            title="Test meeting 2",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[EventParticipant(id="2", email_address="<EMAIL>", name="Test Example 2")],
            meeting_urls=[pydantic.HttpUrl("https://example2.com")],
            body="Test Body",
        ),
        CalendarEvent(
            provider="microsoft",
            id="345",
            user_specific_id="u345",
            title="Test meeting 3",
            start_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc),
            end_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc)
            + datetime.timedelta(days=1),
            all_day=True,
            participants=[],
            meeting_urls=[],
            body="Test Body",
        ),
    ]
    ms_fetch.return_value = ms_events

    redtail_events = [
        CalendarEvent(
            provider="redtail",
            id="345",
            user_specific_id="u345",
            title="Test meeting",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
            body="Test Body",
        ),
        CalendarEvent(
            provider="redtail",
            id="456",
            user_specific_id="u456",
            title="Test meeting 2",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[EventParticipant(id="2", email_address="<EMAIL>", name="Test Example 2")],
            meeting_urls=[pydantic.HttpUrl("https://example2.com")],
            body="Test Body",
        ),
        CalendarEvent(
            provider="redtail",
            id="567",
            user_specific_id="u567",
            title="Test meeting 3",
            start_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc),
            end_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc)
            + datetime.timedelta(days=1),
            all_day=True,
            participants=[],
            meeting_urls=[],
            body="Test Body",
        ),
    ]

    crm_handler.fetch_events.return_value = redtail_events
    events, all_succeeded = async_to_sync(fetch_calendar_events)(test_user, datetime.timedelta(days=1))

    assert all_succeeded
    assert events == ms_events + google_events + redtail_events


@patch("deepinsights.core.integrations.crm.redtail.Redtail.fetch_events")
def test_no_crm_available(redtail: MagicMock, test_user: User) -> None:
    _enable_crm_events_flag()

    events, all_succeeded = async_to_sync(fetch_calendar_events)(test_user, datetime.timedelta(days=1))
    assert all_succeeded


@patch("deepinsights.core.integrations.crm.redtail.Redtail.fetch_events")
def test_different_crm_available(redtail: MagicMock, test_user: User) -> None:
    _enable_crm_events_flag()

    crm_config = test_user.get_crm_configuration()
    crm_config.crm_system = "wealthbox"
    test_user.crm_configuration = crm_config.to_dict()
    test_user.save()

    events, all_succeeded = async_to_sync(fetch_calendar_events)(test_user, datetime.timedelta(days=1))
    assert all_succeeded


@patch("api.routers.calendar._now")
@patch("deepinsights.core.integrations.calendar.events_fetcher.microsoft.fetch_calendar_events")
@patch("deepinsights.core.integrations.calendar.events_fetcher.google.fetch_calendar_events")
@patch("deepinsights.core.integrations.calendar.events_fetcher.MicrosoftOAuth")
@patch("deepinsights.core.integrations.calendar.events_fetcher.GoogleOAuth")
def test_lookahead(
    google_oauth: MagicMock,
    ms_oauth: MagicMock,
    google_fetch: MagicMock,
    ms_fetch: MagicMock,
    mock_now: MagicMock,
    test_user: User,
) -> None:
    google_oauth.return_value.get_access_token = AsyncMock(return_value="123")
    ms_oauth.return_value.get_access_token = AsyncMock(return_value="123")
    ms_oauth.return_value.get_expiry = AsyncMock(return_value=timezone.now())

    google_fetch.return_value = []
    ms_fetch.return_value = []

    _, __ = async_to_sync(fetch_calendar_events)(test_user, datetime.timedelta(days=1, hours=1))

    google_fetch.assert_called_once_with(ANY, datetime.timedelta(days=1, hours=1))
    ms_fetch.assert_called_once_with(ANY, datetime.timedelta(days=1, hours=1))
