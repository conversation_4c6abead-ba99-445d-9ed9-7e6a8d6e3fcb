import datetime

import pydantic
import pytest
from django.utils import timezone

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent, EventParticipant
from deepinsights.core.integrations.calendar.participant_utils import events_with_zeplyn_attendees
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.user import User


@pytest.fixture
def date() -> datetime.datetime:
    return timezone.now()


@pytest.fixture
def events(date: datetime.datetime) -> list[CalendarEvent]:
    return [
        CalendarEvent(
            provider="google",
            id="123",
            user_specific_id="123",
            title="Test meeting",
            body="Test meeting body",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[
                EventParticipant(id="1", email_address="<EMAIL>", name="One"),
                EventParticipant(id="2", email_address="<EMAIL>", name="Two"),
                EventParticipant(id="3", email_address="<EMAIL>", name="Other"),
            ],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
        ),
        CalendarEvent(
            provider="google",
            id="234",
            user_specific_id="234",
            title="Test meeting 2",
            body="Test meeting 2 body",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[
                EventParticipant(id="1", email_address="<EMAIL>", name="One"),
                EventParticipant(id="2", email_address="<EMAIL>", name="Two"),
                EventParticipant(id="3", email_address="<EMAIL>", name="Other Two"),
            ],
            meeting_urls=[pydantic.HttpUrl("https://example2.com")],
        ),
    ]


def test_participant_mapping_no_mappings(django_user_model: User, events: list[CalendarEvent]) -> None:
    user = django_user_model.objects.create(username="<EMAIL>")
    assert events == events_with_zeplyn_attendees(events, user)


def test_participant_mapping(django_user_model: User, events: list[CalendarEvent], date: datetime.datetime) -> None:
    org = Organization.objects.create(name="Test org")
    user = django_user_model.objects.create(username="<EMAIL>", organization=org)
    user.crm_configuration["crm_system"] = "wealthbox"
    user.save()
    client_one = Client.objects.create(
        name="Test client", email="<EMAIL>", organization=org, crm_system="wealthbox"
    )
    client_one.authorized_users.add(user)
    client_one.save()
    client_two = Client.objects.create(
        name="Test client two",
        email="<EMAIL>",
        organization=org,
        crm_system="wealthbox",
    )
    client_two.authorized_users.add(user)
    client_two.save()
    # Confirm that a client with a different CRM system is not returned.
    different_crm_client = Client.objects.create(
        name="Test client two",
        email="<EMAIL>",
        organization=org,
        crm_system="redtail",
    )
    different_crm_client.authorized_users.add(user)
    different_crm_client.save()
    user_one = django_user_model.objects.create(
        username="<EMAIL>", email="<EMAIL>", name="Test user", organization=org
    )
    user_two = django_user_model.objects.create(
        username="<EMAIL>", email="<EMAIL>", name="Test user two", organization=org
    )

    expected_events = [
        CalendarEvent(
            provider="google",
            id="123",
            user_specific_id="123",
            title="Test meeting",
            body="Test meeting body",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[
                EventParticipant(
                    id="1",
                    zeplyn_uuid=client_one.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                    email_address="<EMAIL>",
                    name="Test client",
                ),
                EventParticipant(
                    id="2",
                    zeplyn_uuid=user_one.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.USER,
                    email_address="<EMAIL>",
                    name="Test user",
                ),
                EventParticipant(id="3", email_address="<EMAIL>", name="Other"),
            ],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
        ),
        CalendarEvent(
            provider="google",
            id="234",
            user_specific_id="234",
            title="Test meeting 2",
            body="Test meeting 2 body",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[
                EventParticipant(
                    id="1",
                    zeplyn_uuid=client_two.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                    email_address="<EMAIL>",
                    name="Test client two",
                ),
                EventParticipant(
                    id="2",
                    zeplyn_uuid=user_two.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.USER,
                    email_address="<EMAIL>",
                    name="Test user two",
                ),
                EventParticipant(id="3", email_address="<EMAIL>", name="Other Two"),
            ],
            meeting_urls=[pydantic.HttpUrl("https://example2.com")],
        ),
    ]

    assert expected_events == events_with_zeplyn_attendees(events, user)


def test_participant_mapping_different_users(
    django_user_model: User, events: list[CalendarEvent], date: datetime.datetime
) -> None:
    org = Organization.objects.create(name="Test org")
    org_two = Organization.objects.create(name="Test org two")
    user = django_user_model.objects.create(username="<EMAIL>", organization=org)
    user.crm_configuration["crm_system"] = "wealthbox"
    user.save()
    other_user = django_user_model.objects.create(username="<EMAIL>", organization=org_two)
    client_one = Client.objects.create(
        name="Test client", email="<EMAIL>", organization=org, crm_system="wealthbox"
    )
    client_one.authorized_users.add(user)
    client_one.save()
    client_two = Client.objects.create(
        name="Test client two",
        email="<EMAIL>",
        organization=org,
        crm_system="wealthbox",
    )
    client_two.authorized_users.add(other_user)
    client_two.save()
    # Create a copy of client_two with no authorized users.
    Client.objects.create(
        name="Test client two",
        email="<EMAIL>",
        organization=org,
        crm_system="wealthbox",
    )
    user_one = django_user_model.objects.create(
        username="<EMAIL>", email="<EMAIL>", name="Test user", organization=org
    )
    django_user_model.objects.create(
        username="<EMAIL>", email="<EMAIL>", name="Test user two", organization=org_two
    )

    expected_events = [
        CalendarEvent(
            provider="google",
            id="123",
            user_specific_id="123",
            title="Test meeting",
            body="Test meeting body",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[
                EventParticipant(
                    id="1",
                    zeplyn_uuid=client_one.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                    email_address="<EMAIL>",
                    name="Test client",
                ),
                EventParticipant(
                    id="2",
                    zeplyn_uuid=user_one.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.USER,
                    email_address="<EMAIL>",
                    name="Test user",
                ),
                EventParticipant(id="3", email_address="<EMAIL>", name="Other"),
            ],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
        ),
        CalendarEvent(
            provider="google",
            id="234",
            user_specific_id="234",
            title="Test meeting 2",
            body="Test meeting 2 body",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[
                EventParticipant(id="1", email_address="<EMAIL>", name="One"),
                EventParticipant(id="2", email_address="<EMAIL>", name="Two"),
                EventParticipant(id="3", email_address="<EMAIL>", name="Other Two"),
            ],
            meeting_urls=[pydantic.HttpUrl("https://example2.com")],
        ),
    ]

    assert expected_events == events_with_zeplyn_attendees(events, user)


def test_participant_mapping_name_overrides(
    django_user_model: User, events: list[CalendarEvent], date: datetime.datetime
) -> None:
    org = Organization.objects.create(name="Test org")
    user = django_user_model.objects.create(username="<EMAIL>", organization=org)
    user.crm_configuration["crm_system"] = "wealthbox"
    user.save()
    client_one = Client.objects.create(email="<EMAIL>", organization=org, crm_system="wealthbox")
    client_one.authorized_users.add(user)
    client_one.save()
    client_two = Client.objects.create(
        email="<EMAIL>",
        organization=org,
        crm_system="wealthbox",
    )
    client_two.authorized_users.add(user)
    client_two.save()
    # Confirm that a client with a different CRM system is not returned.
    different_crm_client = Client.objects.create(
        name="Test client two",
        email="<EMAIL>",
        organization=org,
        crm_system="redtail",
    )
    different_crm_client.authorized_users.add(user)
    different_crm_client.save()
    user_one = django_user_model.objects.create(
        username="<EMAIL>", email="<EMAIL>", organization=org
    )
    user_two = django_user_model.objects.create(
        username="<EMAIL>", email="<EMAIL>", organization=org
    )

    expected_events = [
        CalendarEvent(
            provider="google",
            id="123",
            user_specific_id="123",
            title="Test meeting",
            body="Test meeting body",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[
                EventParticipant(
                    id="1",
                    zeplyn_uuid=client_one.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                    email_address="<EMAIL>",
                    name="One",
                ),
                EventParticipant(
                    id="2",
                    zeplyn_uuid=user_one.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.USER,
                    email_address="<EMAIL>",
                    name="Two",
                ),
                EventParticipant(id="3", email_address="<EMAIL>", name="Other"),
            ],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
        ),
        CalendarEvent(
            provider="google",
            id="234",
            user_specific_id="234",
            title="Test meeting 2",
            body="Test meeting 2 body",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[
                EventParticipant(
                    id="1",
                    zeplyn_uuid=client_two.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                    email_address="<EMAIL>",
                    name="One",
                ),
                EventParticipant(
                    id="2",
                    zeplyn_uuid=user_two.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.USER,
                    email_address="<EMAIL>",
                    name="Two",
                ),
                EventParticipant(id="3", email_address="<EMAIL>", name="Other Two"),
            ],
            meeting_urls=[pydantic.HttpUrl("https://example2.com")],
        ),
    ]

    assert expected_events == events_with_zeplyn_attendees(events, user)


def test_participant_mapping_client_user_collision(
    django_user_model: User, events: list[CalendarEvent], date: datetime.datetime
) -> None:
    org = Organization.objects.create(name="Test org")
    user = django_user_model.objects.create(username="<EMAIL>", organization=org)
    user.crm_configuration["crm_system"] = "wealthbox"
    user.save()
    client_one = Client.objects.create(email="<EMAIL>", organization=org, crm_system="wealthbox")
    client_one.authorized_users.add(user)
    client_one.save()

    user_one = django_user_model.objects.create(
        username="<EMAIL>", email="<EMAIL>", organization=org
    )

    events = [
        CalendarEvent(
            provider="google",
            id="123",
            user_specific_id="123",
            title="Test meeting",
            body="Test meeting body",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[
                EventParticipant(id="1", email_address="<EMAIL>", name="One"),
            ],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
        ),
    ]
    expected_events = [
        CalendarEvent(
            provider="google",
            id="123",
            user_specific_id="123",
            title="Test meeting",
            body="Test meeting body",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[
                EventParticipant(
                    id="1",
                    zeplyn_uuid=user_one.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.USER,
                    email_address="<EMAIL>",
                    name="One",
                ),
            ],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
        ),
    ]

    assert expected_events == events_with_zeplyn_attendees(events, user)


def test_participant_mapping_subtype(
    django_user_model: User, events: list[CalendarEvent], date: datetime.datetime
) -> None:
    class CalendarEventWithTest(CalendarEvent):
        test: str

    events_with_test = [
        CalendarEventWithTest(**events[0].model_dump(), test="test"),
        CalendarEventWithTest(**events[1].model_dump(), test="test2"),
    ]

    org = Organization.objects.create(name="Test org")
    user = django_user_model.objects.create(username="<EMAIL>", organization=org)
    user.crm_configuration["crm_system"] = "wealthbox"
    user.save()
    client_one = Client.objects.create(
        name="Test client", email="<EMAIL>", organization=org, crm_system="wealthbox"
    )
    client_one.authorized_users.add(user)
    client_one.save()
    client_two = Client.objects.create(
        name="Test client two",
        email="<EMAIL>",
        organization=org,
        crm_system="wealthbox",
    )
    client_two.authorized_users.add(user)
    client_two.save()
    # Confirm that a client with a different CRM system is not returned.
    different_crm_client = Client.objects.create(
        name="Test client two",
        email="<EMAIL>",
        organization=org,
        crm_system="redtail",
    )
    different_crm_client.authorized_users.add(user)
    different_crm_client.save()
    user_one = django_user_model.objects.create(
        username="<EMAIL>", email="<EMAIL>", name="Test user", organization=org
    )
    user_two = django_user_model.objects.create(
        username="<EMAIL>", email="<EMAIL>", name="Test user two", organization=org
    )

    expected_events = [
        CalendarEventWithTest(
            test="test",
            provider="google",
            id="123",
            user_specific_id="123",
            title="Test meeting",
            body="Test meeting body",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[
                EventParticipant(
                    id="1",
                    zeplyn_uuid=client_one.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                    email_address="<EMAIL>",
                    name="Test client",
                ),
                EventParticipant(
                    id="2",
                    zeplyn_uuid=user_one.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.USER,
                    email_address="<EMAIL>",
                    name="Test user",
                ),
                EventParticipant(id="3", email_address="<EMAIL>", name="Other"),
            ],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
        ),
        CalendarEventWithTest(
            test="test2",
            provider="google",
            id="234",
            user_specific_id="234",
            title="Test meeting 2",
            body="Test meeting 2 body",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[
                EventParticipant(
                    id="1",
                    zeplyn_uuid=client_two.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                    email_address="<EMAIL>",
                    name="Test client two",
                ),
                EventParticipant(
                    id="2",
                    zeplyn_uuid=user_two.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.USER,
                    email_address="<EMAIL>",
                    name="Test user two",
                ),
                EventParticipant(id="3", email_address="<EMAIL>", name="Other Two"),
            ],
            meeting_urls=[pydantic.HttpUrl("https://example2.com")],
        ),
    ]

    assert expected_events == events_with_zeplyn_attendees(events_with_test, user)


def test_participant_mapping_custom_tyope(
    django_user_model: User, events: list[CalendarEvent], date: datetime.datetime
) -> None:
    class OtherModel(pydantic.BaseModel):
        participants: list[EventParticipant]

    other_models = [
        OtherModel(participants=events[0].participants),
        OtherModel(participants=events[1].participants),
    ]

    org = Organization.objects.create(name="Test org")
    user = django_user_model.objects.create(username="<EMAIL>", organization=org)
    user.crm_configuration["crm_system"] = "wealthbox"
    user.save()
    client_one = Client.objects.create(
        name="Test client", email="<EMAIL>", organization=org, crm_system="wealthbox"
    )
    client_one.authorized_users.add(user)
    client_one.save()
    client_two = Client.objects.create(
        name="Test client two",
        email="<EMAIL>",
        organization=org,
        crm_system="wealthbox",
    )
    client_two.authorized_users.add(user)
    client_two.save()
    # Confirm that a client with a different CRM system is not returned.
    different_crm_client = Client.objects.create(
        name="Test client two",
        email="<EMAIL>",
        organization=org,
        crm_system="redtail",
    )
    different_crm_client.authorized_users.add(user)
    different_crm_client.save()
    user_one = django_user_model.objects.create(
        username="<EMAIL>", email="<EMAIL>", name="Test user", organization=org
    )
    user_two = django_user_model.objects.create(
        username="<EMAIL>", email="<EMAIL>", name="Test user two", organization=org
    )

    expected_models = [
        OtherModel(
            participants=[
                EventParticipant(
                    id="1",
                    zeplyn_uuid=client_one.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                    email_address="<EMAIL>",
                    name="Test client",
                ),
                EventParticipant(
                    id="2",
                    zeplyn_uuid=user_one.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.USER,
                    email_address="<EMAIL>",
                    name="Test user",
                ),
                EventParticipant(id="3", email_address="<EMAIL>", name="Other"),
            ],
        ),
        OtherModel(
            test="test2",
            provider="google",
            id="234",
            user_specific_id="234",
            title="Test meeting 2",
            body="Test meeting 2 body",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[
                EventParticipant(
                    id="1",
                    zeplyn_uuid=client_two.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                    email_address="<EMAIL>",
                    name="Test client two",
                ),
                EventParticipant(
                    id="2",
                    zeplyn_uuid=user_two.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.USER,
                    email_address="<EMAIL>",
                    name="Test user two",
                ),
                EventParticipant(id="3", email_address="<EMAIL>", name="Other Two"),
            ],
        ),
    ]

    assert expected_models == events_with_zeplyn_attendees(other_models, user)


def test_participant_mapping_client_email_overlap(
    django_user_model: User, events: list[CalendarEvent], date: datetime.datetime
) -> None:
    org = Organization.objects.create(name="Test org")
    user = django_user_model.objects.create(username="<EMAIL>", organization=org)
    user.crm_configuration["crm_system"] = "wealthbox"
    user.save()

    client_one = Client.objects.create(
        name="Test client", email="<EMAIL>", organization=org, crm_system="wealthbox"
    )
    client_one.authorized_users.add(user)
    client_one.save()
    client_one_duplicate = Client.objects.create(
        name="Test client one duplicate",
        email="<EMAIL>",
        organization=org,
        crm_system="wealthbox",
    )
    client_one_duplicate.authorized_users.add(user)
    client_one_duplicate.save()
    client_two = Client.objects.create(
        name="Test client two",
        email="<EMAIL>",
        organization=org,
        crm_system="wealthbox",
    )
    client_two.authorized_users.add(user)
    client_two.save()

    expected_participants = [
        [
            EventParticipant(
                id="1",
                zeplyn_uuid=client_one.uuid,
                zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                email_address="<EMAIL>",
                name="Test client",
            ),
            EventParticipant(
                id="2",
                email_address="<EMAIL>",
                name="Two",
            ),
            EventParticipant(id="3", email_address="<EMAIL>", name="Other"),
        ],
        [
            EventParticipant(
                id="1",
                zeplyn_uuid=client_two.uuid,
                zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                email_address="<EMAIL>",
                name="Test client two",
            ),
            EventParticipant(
                id="2",
                email_address="<EMAIL>",
                name="Two",
            ),
            EventParticipant(id="3", email_address="<EMAIL>", name="Other Two"),
        ],
    ]

    assert expected_participants == [e.participants for e in events_with_zeplyn_attendees(events, user)]
