"""Fetches calendar information from the Google calendar API."""

from __future__ import annotations

import datetime
import logging
from typing import TYPE_CHECKING
from urllib.parse import urlparse

from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from pydantic import ValidationError
from zoneinfo import ZoneInfo

from deepinsights.core.integrations.calendar.calendar_data_parser import find_urls
from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent, EventParticipant

if TYPE_CHECKING:
    # See https://pypi.org/project/google-api-python-client-stubs/ for details about importing types
    # for Google API clients.
    from googleapiclient._apis.calendar.v3 import (  # pyright: ignore
        CalendarResource,
        Event,
        EventDateTime,
        EventsHttpRequest,
    )


# Extracts a Python datetime from a Google calendar event time object.
# cf https://developers.google.com/calendar/api/v3/reference/events.
def __datetime_from_event_time(event_time: EventDateTime, all_day: bool) -> datetime.datetime:
    event_date = event_time.get("date")
    event_datetime = event_time.get("dateTime")
    event_timezone = event_time.get("timeZone")

    # Extract the date or datetime. If both are present, prefer datetime.
    raw_datetime = None
    if event_date:
        raw_datetime = datetime.datetime.fromisoformat(event_date)
    if event_datetime:
        raw_datetime = datetime.datetime.fromisoformat(event_datetime)
    if not raw_datetime:
        raise ValidationError("Google calendar event time is missing both date and dateTime fields")

    # If there is a timezone, adjust the datetime to that timezone.
    if event_timezone:
        # If the datetime already has a timezone, convert it to the event timezone.
        if raw_datetime.tzinfo:
            raw_datetime = raw_datetime.astimezone(ZoneInfo(event_timezone))
        else:
            # Otherwise, set the timezone without adjusting the time.
            raw_datetime = raw_datetime.replace(tzinfo=ZoneInfo(event_timezone))

    # If the event is all-day, set the time to midnight and the timezone to UTC.
    if all_day:
        raw_datetime = raw_datetime.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc)

    return raw_datetime


def _events_service(access_token: str) -> CalendarResource.EventsResource:
    return build("calendar", "v3", credentials=Credentials(access_token)).events()  # type: ignore[no-any-return, attr-defined, no-untyped-call]


def fetch_calendar_event(access_token: str, event_id: str) -> CalendarEvent | None:
    logging.debug(f"Fetching calendar event from Google: {event_id}")
    try:
        event_result = _events_service(access_token).get(calendarId="primary", eventId=event_id).execute()
    except HttpError as error:
        logging.error(f"Error fetching calendar entries: {error}")
        raise Exception("Error fetching calendar entries") from error
    entries = calendar_entries_from_events([event_result])
    return entries[0] if entries else None


def fetch_calendar_events(
    access_token: str,
    interval: datetime.timedelta = datetime.timedelta(days=1),
    start_time: datetime.datetime | None = None,
) -> list[CalendarEvent]:
    logging.info("Fetching calendar events from Google")

    # start_time = datetime.datetime.now(datetime.timezone.utc)
    if start_time is None:
        start_time = datetime.datetime.now(datetime.timezone.utc)

    end_time = start_time + interval

    events: list[Event] = []
    try:
        events_service = _events_service(access_token)
        request: EventsHttpRequest | None = events_service.list(
            calendarId="primary",
            timeMin=start_time.isoformat(),
            timeMax=end_time.isoformat(),
            singleEvents=True,
            orderBy="startTime",
            maxResults=250,
        )
        while request is not None:
            response = request.execute()
            events.extend(response.get("items", []))
            request = events_service.list_next(request, response)
    except HttpError as error:
        logging.error(f"Error fetching calendar entries: {error}")
        raise Exception("Error fetching calendar entries") from error
    return calendar_entries_from_events(events)


# Given a list of Google API calendar events, returns a list of Zeplyn `CalendarEvent`s.
def calendar_entries_from_events(events: list[Event]) -> list[CalendarEvent]:
    entries = []
    for event in events:
        try:
            attendees = event.get("attendees", []) or ((organizer := event.get("organizer")) and [organizer]) or []
            participants = [
                EventParticipant(
                    id=attendee.get("id"), name=attendee.get("displayName"), email_address=attendee.get("email")
                )
                for attendee in attendees
            ]
            start = event.get("start", {})
            end = event.get("end", {})
            all_day = "date" in start and "date" in end and "dateTime" not in start and "dateTime" not in end
            start_time = __datetime_from_event_time(event["start"], all_day)
            end_time = __datetime_from_event_time(event["end"], all_day)

            meeting_urls = []
            for entry_point in (event.get("conferenceData") or {}).get("entryPoints", []):
                try:
                    entry_point_type = entry_point["entryPointType"]
                    if entry_point_type == "video" or entry_point_type == "more":
                        meeting_urls.append(entry_point["uri"])
                except KeyError as e:
                    logging.error(f"Google calendar event entrypoint has no URL: {e}. Skipping")
            # See if the location is a meeting URL
            try:
                parsed_location = urlparse(event.get("location", ""))
                if parsed_location.scheme and parsed_location.netloc and parsed_location.path:
                    meeting_urls.append(event["location"])
            except Exception as e:
                logging.debug(
                    "Could not parse location as URL. This is not surprising; location is often a physical location."
                )
                pass

            description = event.get("description", "")

            # See if there are any meeting URLs in the body.
            try:
                meeting_urls.extend(find_urls(description))
            except Exception as e:
                logging.warning(f"Parsing meeting URLs from meeting description failed: {e}")
                pass
            if not (event_id := event.get("id", "")):
                logging.error("Google calendar event has no ID. Skipping")
                continue
            entries.append(
                CalendarEvent(
                    provider="google",
                    id=event_id,
                    user_specific_id=event_id,
                    title=event.get("summary", ""),
                    body=description,
                    start_time=start_time,
                    end_time=end_time,
                    participants=participants,
                    meeting_urls=meeting_urls,
                    all_day=all_day,
                )
            )
        except KeyError as e:
            logging.error(f"Missing key in Google calendar event: {e}. Skipping")
        except ValidationError as e:
            logging.error(f"Could not parse Google calendar event: {e}. Skipping")
    return entries
