"""Fetches calendar information from the Microsoft Graph API."""

import logging
from datetime import datetime, timedelta, timezone
from typing import Any

from azure.core.credentials import AccessToken
from azure.identity import ClientSecretCredential
from kiota_abstractions.base_request_configuration import RequestConfiguration
from msgraph.generated.models.attendee_type import AttendeeType
from msgraph.generated.models.date_time_time_zone import DateTimeTimeZone
from msgraph.generated.models.event import Event
from msgraph.generated.users.item.calendar.calendar_view.calendar_view_request_builder import CalendarViewRequestBuilder
from msgraph.generated.users.item.calendar.events.item.event_item_request_builder import (
    EventItemRequestBuilder as CalendarEventItemRequestBuilder,
)
from msgraph.generated.users.item.events.item.event_item_request_builder import EventItemRequestBuilder
from msgraph.generated.users.item.user_item_request_builder import UserItemRequestBuilder
from msgraph.graph_service_client import GraphServiceClient
from msgraph_core import PageIterator
from pydantic import BaseModel, ValidationError
from zoneinfo import ZoneInfo

from deepinsights.core.integrations.calendar.calendar_data_parser import find_urls
from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent, EventParticipant


class MicrosoftCredentials(BaseModel):
    """Credentials (and other related information) to use for accessing the Microsoft calendar APIs."""

    class _ClientCredentials(BaseModel):
        """Credentials for a client credentials access flow."""

        """The client ID of the accessing application."""
        client_id: str

        """The client secret of the accessing application."""
        client_secret: str

        """The ID of the user for whom to fetch calendar events."""
        user_id: str

        """The tenant ID of the application in the organization where it is being accessed."""
        tenant_id: str

    class _UserAccessToken(BaseModel):
        """Credentials for a user access token based flow."""

        """The access token to use for accessing the Microsoft Graph APIs."""
        token: str

        """The expiration time of the access token, in seconds since the epoch."""
        expires_on: int

    access_token: _UserAccessToken | None = None
    client_credentials: _ClientCredentials | None = None

    @classmethod
    def from_access_token(cls, access_token: str, expires_on: int) -> "MicrosoftCredentials":
        """Creates a MicrosoftCredentials object from an access token and expiration time."""

        return cls(
            access_token=cls._UserAccessToken(token=access_token, expires_on=expires_on),
        )

    @classmethod
    def from_client_credentials(
        cls, *, client_id: str, client_secret: str, tenant_id: str, user_id: str
    ) -> "MicrosoftCredentials":
        """Creates a MicrosoftCredentials object from client credentials information."""

        return cls(
            client_credentials=cls._ClientCredentials(
                client_id=client_id, client_secret=client_secret, tenant_id=tenant_id, user_id=user_id
            )
        )


class RawAccessTokenProvider:
    """
    A simple credential provider that returns a raw access token for use with Azure SDK clients.
    """

    def __init__(self, access_token: str, expires_on: int) -> None:
        self._access_token = access_token
        self._expires_on = expires_on

    def get_token(
        self,
        *scopes: str,
        claims: str | None = None,
        tenant_id: str | None = None,
        enable_cae: bool = False,
        **kwargs: Any,
    ) -> AccessToken:
        return AccessToken(self._access_token, self._expires_on)


def _selected_fields() -> list[str]:
    return [
        "subject",
        "body",
        "start",
        "end",
        "attendees",
        "onlineMeeting",
        "isAllDay",
        "iCalUId",
        "originalStart",
        "seriesMasterId",
    ]


# Returns the iCalUId for a series master event ID (if it exists).
async def __ical_uid_for_series_master_id(
    user_request_builder: UserItemRequestBuilder, series_master_id: str | None
) -> str | None:
    if not series_master_id:
        return None
    series_event = await user_request_builder.events.by_event_id(series_master_id).get(
        request_configuration=RequestConfiguration(
            query_parameters=EventItemRequestBuilder.EventItemRequestBuilderGetQueryParameters(select=["iCalUId"]),
        )
    )
    return series_event.i_cal_u_id if series_event else None


def __user_client_for_credentials(credentials: MicrosoftCredentials) -> UserItemRequestBuilder:
    if access_token := credentials.access_token:
        return GraphServiceClient(
            credentials=RawAccessTokenProvider(access_token.token, access_token.expires_on),
            scopes=["User.Read", "Calendars.Read"],
        ).me

    if client_creds := credentials.client_credentials:
        creds = ClientSecretCredential(
            client_id=client_creds.client_id,
            client_secret=client_creds.client_secret,
            tenant_id=client_creds.tenant_id,
        )
        return GraphServiceClient(credentials=creds, scopes=["https://graph.microsoft.com/.default"]).users.by_user_id(
            client_creds.user_id
        )
    raise ValueError("No valid credentials provided")


async def fetch_calendar_event(credentials: MicrosoftCredentials, calendar_event_id: str) -> CalendarEvent | None:
    try:
        client = __user_client_for_credentials(credentials)
    except Exception as e:
        logging.error("Error configuring user credentials for Microsoft API access: %s", e)
        raise Exception("Error configuring user credentials for Microsoft API access") from e

    request_configuration = RequestConfiguration(
        query_parameters=CalendarEventItemRequestBuilder.EventItemRequestBuilderGetQueryParameters(
            select=_selected_fields()
        )
    )

    try:
        event = await client.calendar.events.by_event_id(event_id=calendar_event_id).get(
            request_configuration=request_configuration
        )
    except Exception as e:
        logging.error(f"Error fetching calendar entries: {e}")
        raise Exception("Error fetching calendar entries") from e

    if not event:
        return None

    zeplyn_entries = await __calendar_entries_from_events([event], client)
    return zeplyn_entries[0] if zeplyn_entries else None


async def fetch_calendar_events(
    credentials: MicrosoftCredentials,
    interval: timedelta = timedelta(days=1),
    start_time: datetime | None = None,
) -> list[CalendarEvent]:
    """
    Fetch calendar events for a specified time interval.
    Args:
        credentials: Credentials and other info requred to access the Microsoft Graph APIs.
        interval: Time interval to fetch events for
        start_time: Optional start time; if None, current time is used
    Returns:
        List of CalendarEvent objects
    """
    try:
        client = __user_client_for_credentials(credentials)
    except Exception as e:
        logging.error("Error configuring user credentials for Microsoft API access: %s", e)
        raise Exception("Error configuring user credentials for Microsoft API access") from e

    if start_time is None:
        start_time = datetime.now(tz=timezone.utc)

    end_time = start_time + interval

    request_configuration = RequestConfiguration(
        query_parameters=CalendarViewRequestBuilder.CalendarViewRequestBuilderGetQueryParameters(
            start_date_time=start_time.isoformat(),
            end_date_time=end_time.isoformat(),
            orderby=["start/dateTime"],
            select=_selected_fields(),
            top=250,
        )
    )

    try:
        initial_response = await client.calendar.calendar_view.get(request_configuration=request_configuration)
        if not initial_response or not initial_response.value:
            return []

        all_events: list[Event] = []

        def process_page(event: Event) -> bool:
            all_events.append(event)
            return True

        page_iterator = PageIterator(initial_response, client.request_adapter)

        await page_iterator.iterate(process_page)

    except Exception as e:
        logging.error("Error fetching calendar entries: %s", e)
        raise Exception("Error fetching calendar entries") from e

    if not all_events:
        return []

    return await __calendar_entries_from_events(all_events, client)


# Returns a list of Zeplyn `CalendarEvent`s from a list of Microsoft Graph API calendar events.
#
# This constructs event IDs using either the iCalUId, or the series master's iCalUId plus a
# timestamp (if the events are part of a recurring series).
async def __calendar_entries_from_events(
    calendar_events: list[Event],
    user_request_builder: UserItemRequestBuilder,
) -> list[CalendarEvent]:
    entries = []
    for event in calendar_events:
        try:
            participants = [
                EventParticipant(name=attendee.email_address.name, email_address=attendee.email_address.address)
                for attendee in (event.attendees or [])
                if (
                    attendee.type
                    and attendee.type != AttendeeType.Resource
                    and attendee.email_address
                    and attendee.email_address.address
                )
            ]

            meeting_urls = []
            if event.online_meeting and event.online_meeting.join_url:
                meeting_urls.append(event.online_meeting.join_url)
            # See if there are any meeting URLs in the body.
            try:
                if event.body and (content := event.body.content):
                    meeting_urls.extend(find_urls(content))
            except Exception as e:
                logging.warning(f"Parsing meeting URLs from meeting body failed: {e}")
                pass

            def build_datetime(date_time_time_zone: DateTimeTimeZone, all_day: bool) -> datetime:
                if not date_time_time_zone.date_time:
                    raise ValueError("Missing date_time in DateTimeTimeZone")
                if not date_time_time_zone.time_zone:
                    raise ValueError("Missing time_zone in DateTimeTimeZone")
                raw_datetime = datetime.fromisoformat(date_time_time_zone.date_time)
                if all_day:
                    return raw_datetime.replace(hour=0, minute=0, second=0, tzinfo=timezone.utc)
                return raw_datetime.replace(tzinfo=ZoneInfo(date_time_time_zone.time_zone))

            if not (start := event.start):
                raise ValueError("Missing start time in event")
            if not (end := event.end):
                raise ValueError("Missing end time in event")
            if (all_day := event.is_all_day) is None:
                raise ValueError("Missing isAllDay in event")

            start_time = build_datetime(start, all_day)
            end_time = build_datetime(end, all_day)

            event_id = event.i_cal_u_id

            # Get the iCalUId for the series master event, if it exists. We need to do this because
            # of the following observed behaviors:
            #
            # - Microsoft calendar events (nominally) do not have a global-across-calendars identifier, only
            #   unique-within-a-calendar (`id`) or (supposedly)
            #   unique-across-calendars-but-not-unique-across-recurring-event-instances (`iCalUId`) identifiers
            # - iCalUIds for occurrences of recurring events *should* be the same as the iCalUIds of the
            #   recurring event masters, but experimentally this is not the case: there is some extra cruft in the
            #   iCalUIds of occurrences that is not present in the iCalUIds of the series master (but not always)
            # - iCalUIds are inconsistent for occurrences of a recurring event between Recall's representation
            #   of Microsoft calendar events and the events that we fetch from the Graph API directly
            #
            # Therefore, the only consistent way to get stable iCalUIds that don't change for each instance
            # of a recurring event is to fetch the iCalUIds of the series master events, append the original
            # start time of the event and use that as the identifier. We assume that events that aren't part of
            # a recurrence have correct iCalUIds.
            try:
                if series_master_ical_uid := await __ical_uid_for_series_master_id(
                    user_request_builder, event.series_master_id
                ):
                    event_id = f"{series_master_ical_uid}{f'_{int(event.original_start.timestamp())}' if event.original_start else ''}"
            except Exception as e:
                logging.error("Error fetching series master event %s. Skipping.", event.series_master_id, exc_info=e)
                continue

            entries.append(
                CalendarEvent(
                    provider="microsoft",
                    id=event_id,
                    user_specific_id=event.id,
                    title=event.subject,
                    body=event.body.content if event.body else None,
                    start_time=start_time,
                    end_time=end_time,
                    all_day=all_day,
                    participants=participants,
                    meeting_urls=meeting_urls,
                )
            )
        except (ValueError, ValidationError) as e:
            logging.error(f"Could not parse Microsoft calendar event: {e}. Skipping")
    return entries
