import logging
import urllib
from datetime import datetime, timedelta, timezone
from typing import Any
from unittest.mock import MagicMock, patch

import httpx
import pydantic
import pytest
from pytest_httpx import HTTPXMock
from zoneinfo import ZoneInfo

from deepinsights.core.integrations.calendar import microsoft
from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent, EventParticipant

pytestmark = [pytest.mark.asyncio]


async def test_raw_token_provider() -> None:
    token = microsoft.RawAccessTokenProvider("token", 1).get_token()
    assert token.token == "token"
    assert token.expires_on == 1


async def test_fetch_calendar_event_invalid_credentials(caplog: pytest.LogCaptureFixture) -> None:
    with caplog.at_level(logging.ERROR):
        with pytest.raises(Exception):
            assert not await microsoft.fetch_calendar_event(
                microsoft.MicrosoftCredentials(access_token=None, client_credentials=None), "eventID"
            )
        assert len(caplog.messages) == 1
        assert "No valid credentials" in caplog.messages[0]


async def test_fetch_calendar_event_error(httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture) -> None:
    httpx_mock.add_exception(Exception("Test exception"))
    with caplog.at_level(logging.ERROR):
        with pytest.raises(Exception):
            assert not await microsoft.fetch_calendar_event(
                microsoft.MicrosoftCredentials.from_access_token("token", 1), "eventID"
            )
        assert len(caplog.messages) == 1
        assert "Test exception" in caplog.messages[0]


async def test_fetch_empty_calendar_event(httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture) -> None:
    httpx_mock.add_response(status_code=httpx.codes.NOT_FOUND)
    with caplog.at_level(logging.ERROR):
        with pytest.raises(Exception):
            assert not await microsoft.fetch_calendar_event(
                microsoft.MicrosoftCredentials.from_access_token("token", 1), "eventID"
            )
        assert len(caplog.messages) == 1
        assert "Error fetching calendar entries" in caplog.messages[0]
        assert (request := httpx_mock.get_request()) and "eventID" in request.url.path


async def test_fetch_calendar_event(httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture) -> None:
    httpx_mock.add_response(
        json={
            "id": "event_id",
            "iCalUId": "ical_event_id",
            "subject": "Event Summary",
            "body": {"content": "Event body", "contentType": "text"},
            "start": {"dateTime": "2024-01-01T10:00:00", "timeZone": "UTC"},
            "end": {"dateTime": "2024-01-01T12:00:00", "timeZone": "UTC"},
            "isAllDay": False,
            "attendees": [
                {
                    "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                    "type": "required",
                },
            ],
            "onlineMeeting": {"joinUrl": "https://example.com/video"},
        }
    )
    with caplog.at_level(logging.ERROR):
        assert await microsoft.fetch_calendar_event(
            microsoft.MicrosoftCredentials.from_access_token("token", 1), "eventID"
        ) == CalendarEvent(
            provider="microsoft",
            id="ical_event_id",
            user_specific_id="event_id",
            title="Event Summary",
            body="Event body",
            start_time=datetime(2024, 1, 1, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
            end_time=datetime(2024, 1, 1, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
            all_day=False,
            participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
            meeting_urls=[pydantic.HttpUrl("https://example.com/video")],
        )
        assert not caplog.messages


@patch("deepinsights.core.integrations.calendar.microsoft.ClientSecretCredential")
async def test_fetch_calendar_event_with_client_credentials_error(
    mock_client_secret_credential: MagicMock,
    httpx_mock: HTTPXMock,
    caplog: pytest.LogCaptureFixture,
) -> None:
    mock_client_secret_credential.side_effect = Exception("Test error")
    with caplog.at_level(logging.ERROR):
        with pytest.raises(Exception):
            assert await microsoft.fetch_calendar_event(
                microsoft.MicrosoftCredentials.from_client_credentials(
                    client_id="client_id", client_secret="client_secret", tenant_id="tenant_id", user_id="user_id"
                ),
                "eventID",
            ) == CalendarEvent(
                provider="microsoft",
                id="ical_event_id",
                user_specific_id="event_id",
                title="Event Summary",
                body="Event body",
                start_time=datetime(2024, 1, 1, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
                end_time=datetime(2024, 1, 1, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
                all_day=False,
                participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
                meeting_urls=[pydantic.HttpUrl("https://example.com/video")],
            )
            assert len(caplog.messages) == 1
            assert "Test error" in caplog.messages[0]

    assert not httpx_mock.get_request()


@patch("deepinsights.core.integrations.calendar.microsoft.ClientSecretCredential")
async def test_fetch_calendar_event_with_client_credentials(
    mock_client_secret_credential: MagicMock,
    httpx_mock: HTTPXMock,
    caplog: pytest.LogCaptureFixture,
) -> None:
    httpx_mock.add_response(
        json={
            "id": "event_id",
            "iCalUId": "ical_event_id",
            "subject": "Event Summary",
            "body": {"content": "Event body", "contentType": "text"},
            "start": {"dateTime": "2024-01-01T10:00:00", "timeZone": "UTC"},
            "end": {"dateTime": "2024-01-01T12:00:00", "timeZone": "UTC"},
            "isAllDay": False,
            "attendees": [
                {
                    "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                    "type": "required",
                },
            ],
            "onlineMeeting": {"joinUrl": "https://example.com/video"},
        }
    )
    with caplog.at_level(logging.ERROR):
        assert await microsoft.fetch_calendar_event(
            microsoft.MicrosoftCredentials.from_client_credentials(
                client_id="client_id", client_secret="client_secret", tenant_id="tenant_id", user_id="user_id"
            ),
            "eventID",
        ) == CalendarEvent(
            provider="microsoft",
            id="ical_event_id",
            user_specific_id="event_id",
            title="Event Summary",
            body="Event body",
            start_time=datetime(2024, 1, 1, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
            end_time=datetime(2024, 1, 1, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
            all_day=False,
            participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
            meeting_urls=[pydantic.HttpUrl("https://example.com/video")],
        )
        assert not caplog.messages

    mock_client_secret_credential.assert_called_once_with(
        client_id="client_id",
        client_secret="client_secret",
        tenant_id="tenant_id",
    )

    request = httpx_mock.get_request()
    assert request
    assert request.url.path == "/v1.0//users/user_id/calendar/events/eventID"


async def test_fetch_calendar_event_recurring_fetch_error(
    httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture
) -> None:
    httpx_mock.add_response(
        json={
            "id": "event_id",
            "iCalUId": "ical_event_id",
            "subject": "Event Summary",
            "body": {"content": "Event body", "contentType": "text"},
            "start": {"dateTime": "2024-01-01T10:00:00", "timeZone": "UTC"},
            "end": {"dateTime": "2024-01-01T12:00:00", "timeZone": "UTC"},
            "isAllDay": False,
            "attendees": [
                {
                    "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                    "type": "required",
                },
            ],
            "onlineMeeting": {"joinUrl": "https://example.com/video"},
            "seriesMasterId": "series_master_id",
        }
    )
    httpx_mock.add_exception(Exception("Test exception"))
    with caplog.at_level(logging.ERROR):
        assert not await microsoft.fetch_calendar_event(
            microsoft.MicrosoftCredentials.from_access_token("token", 1), "eventID"
        )
        assert len(caplog.messages) == 1
        assert "Error fetching series master event series_master_id" in caplog.messages[0]


async def test_fetch_calendar_event_recurring_with_original_start(
    httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture
) -> None:
    httpx_mock.add_response(
        json={
            "id": "event_id",
            "iCalUId": "ical_event_id",
            "subject": "Event Summary",
            "body": {"content": "Event body", "contentType": "text"},
            "start": {"dateTime": "2024-01-01T10:00:00", "timeZone": "UTC"},
            "end": {"dateTime": "2024-01-01T12:00:00", "timeZone": "UTC"},
            "isAllDay": False,
            "attendees": [
                {
                    "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                    "type": "required",
                },
            ],
            "onlineMeeting": {"joinUrl": "https://example.com/video"},
            "seriesMasterId": "series_master_id",
            "originalStart": "2024-01-01T10:00:00",
        }
    )
    httpx_mock.add_response(json={"iCalUId": "series_master_ical_uid"})
    with caplog.at_level(logging.ERROR):
        assert await microsoft.fetch_calendar_event(
            microsoft.MicrosoftCredentials.from_access_token("token", 1), "eventID"
        ) == CalendarEvent(
            provider="microsoft",
            id=f"series_master_ical_uid_{int(datetime(2024, 1, 1, 10, 0, 0, tzinfo=ZoneInfo('UTC')).timestamp())}",
            user_specific_id="event_id",
            title="Event Summary",
            body="Event body",
            start_time=datetime(2024, 1, 1, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
            end_time=datetime(2024, 1, 1, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
            all_day=False,
            participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
            meeting_urls=[pydantic.HttpUrl("https://example.com/video")],
        )
        assert not caplog.messages


async def test_fetch_calendar_event_recurring_no_original_start(
    httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture
) -> None:
    httpx_mock.add_response(
        json={
            "id": "event_id",
            "iCalUId": "ical_event_id",
            "subject": "Event Summary",
            "body": {"content": "Event body", "contentType": "text"},
            "start": {"dateTime": "2024-01-01T10:00:00", "timeZone": "UTC"},
            "end": {"dateTime": "2024-01-01T12:00:00", "timeZone": "UTC"},
            "isAllDay": False,
            "attendees": [
                {
                    "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                    "type": "required",
                },
            ],
            "onlineMeeting": {"joinUrl": "https://example.com/video"},
            "seriesMasterId": "series_master_id",
        }
    )
    httpx_mock.add_response(
        json={
            "iCalUId": "series_master_ical_uid",
        }
    )
    with caplog.at_level(logging.ERROR):
        assert await microsoft.fetch_calendar_event(
            microsoft.MicrosoftCredentials.from_access_token("token", 1), "eventID"
        ) == CalendarEvent(
            provider="microsoft",
            id="series_master_ical_uid",
            user_specific_id="event_id",
            title="Event Summary",
            body="Event body",
            start_time=datetime(2024, 1, 1, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
            end_time=datetime(2024, 1, 1, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
            all_day=False,
            participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
            meeting_urls=[pydantic.HttpUrl("https://example.com/video")],
        )
        assert not caplog.messages


async def test_fetch_calendar_event_meeting_url_in_body(
    httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture
) -> None:
    httpx_mock.add_response(
        json={
            "id": "event_id_two",
            "iCalUId": "ical_event_id_two",
            "subject": "Event Summary Two",
            "body": {
                "content": '<html><body><a href="https://zoom.us/video">Join Meeting</a></body></html>',
                "contentType": "html",
            },
            "start": {"dateTime": "2024-01-02T10:00:00", "timeZone": "UTC"},
            "end": {"dateTime": "2024-01-02T12:00:00", "timeZone": "UTC"},
            "isAllDay": False,
            "attendees": [
                {
                    "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                    "type": "required",
                },
            ],
        }
    )
    with caplog.at_level(logging.ERROR):
        assert await microsoft.fetch_calendar_event(
            microsoft.MicrosoftCredentials.from_access_token("token", 1), "eventID"
        ) == CalendarEvent(
            provider="microsoft",
            id="ical_event_id_two",
            user_specific_id="event_id_two",
            title="Event Summary Two",
            body='<html><body><a href="https://zoom.us/video">Join Meeting</a></body></html>',
            start_time=datetime(2024, 1, 2, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
            end_time=datetime(2024, 1, 2, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
            all_day=False,
            participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
            meeting_urls=[pydantic.HttpUrl("https://zoom.us/video")],
        )
        assert not caplog.messages


async def test_time_zone_handling_for_fetch_event(httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture) -> None:
    httpx_mock.add_response(
        json={
            "id": "event_id_two",
            "iCalUId": "ical_event_id_two",
            "subject": "Summary Two",
            "body": {"content": "Event body", "contentType": "text"},
            "start": {"dateTime": "2024-01-02T10:00:00", "timeZone": "Australia/Sydney"},
            "end": {"dateTime": "2024-01-02T12:00:00", "timeZone": "America/New_York"},
            "isAllDay": False,
            "attendees": [
                {
                    "emailAddress": {"address": "<EMAIL>", "name": "Test User 2"},
                    "type": "required",
                },
            ],
            "onlineMeeting": {"joinUrl": "https://example.com/video2"},
        },
    )
    with caplog.at_level(logging.ERROR):
        assert await microsoft.fetch_calendar_event(
            microsoft.MicrosoftCredentials.from_access_token("token", 1), "eventID"
        ) == CalendarEvent(
            provider="microsoft",
            id="ical_event_id_two",
            user_specific_id="event_id_two",
            title="Summary Two",
            body="Event body",
            start_time=datetime(2024, 1, 2, 10, 0, 0, tzinfo=ZoneInfo("Australia/Sydney")),
            end_time=datetime(2024, 1, 2, 12, 0, 0, tzinfo=ZoneInfo("America/New_York")),
            all_day=False,
            participants=[EventParticipant(name="Test User 2", email_address="<EMAIL>")],
            meeting_urls=[pydantic.HttpUrl("https://example.com/video2")],
        )
        assert not caplog.messages


async def test_fetch_calendar_events_error(httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture) -> None:
    httpx_mock.add_exception(Exception("Test exception"))
    with caplog.at_level(logging.ERROR):
        with pytest.raises(Exception):
            assert not await microsoft.fetch_calendar_events(
                microsoft.MicrosoftCredentials.from_access_token("token", 1)
            )
        assert len(caplog.messages) == 1
        assert "Test exception" in caplog.messages[0]


async def test_fetch_calendar_events_invalid_credentials(caplog: pytest.LogCaptureFixture) -> None:
    with caplog.at_level(logging.ERROR):
        with pytest.raises(Exception):
            assert not await microsoft.fetch_calendar_events(
                microsoft.MicrosoftCredentials(access_token=None, client_credentials=None)
            )
        assert len(caplog.messages) == 1
        assert "No valid credentials" in caplog.messages[0]


@patch("deepinsights.core.integrations.calendar.microsoft.ClientSecretCredential")
async def test_fetch_calendar_events_client_credentials_error(
    mock_client_secret_credential: MagicMock, caplog: pytest.LogCaptureFixture
) -> None:
    mock_client_secret_credential.side_effect = Exception("Test error")
    with caplog.at_level(logging.ERROR):
        with pytest.raises(Exception):
            assert not await microsoft.fetch_calendar_events(
                microsoft.MicrosoftCredentials.from_client_credentials(
                    client_id="client_id", client_secret="client_secret", tenant_id="tenant_id", user_id="user_id"
                )
            )
        assert len(caplog.messages) == 1
        assert "Test error" in caplog.messages[0]


async def test_fetch_empty_calendar_events(httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture) -> None:
    httpx_mock.add_response(json={"value": []})
    with caplog.at_level(logging.ERROR):
        assert not await microsoft.fetch_calendar_events(microsoft.MicrosoftCredentials.from_access_token("token", 1))
        assert not caplog.messages


async def test_start_end_dates(httpx_mock: HTTPXMock) -> None:
    httpx_mock.add_response(json={"value": []})
    assert not await microsoft.fetch_calendar_events(
        microsoft.MicrosoftCredentials.from_access_token("token", 1), timedelta(hours=1)
    )
    request = httpx_mock.get_request()
    assert request
    start_date_string = urllib.parse.parse_qs(request.url.query)[b"startDateTime"][0].decode("utf-8")
    end_date_string = urllib.parse.parse_qs(request.url.query)[b"endDateTime"][0].decode("utf-8")
    start_date = datetime.fromisoformat(start_date_string)
    end_date = datetime.fromisoformat(end_date_string)
    assert end_date == start_date + timedelta(hours=1)


async def test_fetch_calendar_events(httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture) -> None:
    httpx_mock.add_response(
        json={
            "value": [
                {
                    "id": "event_id",
                    "iCalUId": "ical_event_id",
                    "subject": "Event Summary",
                    "body": {"content": "Event body", "contentType": "text"},
                    "start": {"dateTime": "2024-01-01T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-01T12:00:00", "timeZone": "UTC"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                            "type": "required",
                        },
                    ],
                    "onlineMeeting": {"joinUrl": "https://example.com/video"},
                },
                {
                    "id": "event_id_two",
                    "iCalUId": "ical_event_id_two",
                    "subject": "Event Summary Two",
                    "body": {"content": "Event body two", "contentType": "text"},
                    "start": {"dateTime": "2024-01-02T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-02T12:00:00", "timeZone": "UTC"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                            "type": "required",
                        },
                    ],
                    "onlineMeeting": {"joinUrl": "https://example.com/video2"},
                },
            ]
        }
    )
    with caplog.at_level(logging.ERROR):
        assert await microsoft.fetch_calendar_events(microsoft.MicrosoftCredentials.from_access_token("token", 1)) == [
            CalendarEvent(
                provider="microsoft",
                id="ical_event_id",
                user_specific_id="event_id",
                title="Event Summary",
                body="Event body",
                start_time=datetime(2024, 1, 1, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
                end_time=datetime(2024, 1, 1, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
                all_day=False,
                participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
                meeting_urls=[pydantic.HttpUrl("https://example.com/video")],
            ),
            CalendarEvent(
                provider="microsoft",
                id="ical_event_id_two",
                user_specific_id="event_id_two",
                title="Event Summary Two",
                body="Event body two",
                start_time=datetime(2024, 1, 2, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
                end_time=datetime(2024, 1, 2, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
                all_day=False,
                participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
                meeting_urls=[pydantic.HttpUrl("https://example.com/video2")],
            ),
        ]
        assert not caplog.messages


async def test_fetch_calendar_events_paged(httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture) -> None:
    httpx_mock.add_response(
        json={
            "value": [
                {
                    "id": "event_id",
                    "iCalUId": "ical_event_id",
                    "subject": "Event Summary",
                    "body": {"content": "Event body", "contentType": "text"},
                    "start": {"dateTime": "2024-01-01T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-01T12:00:00", "timeZone": "UTC"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                            "type": "required",
                        },
                    ],
                    "onlineMeeting": {"joinUrl": "https://example.com/video"},
                },
            ],
            "@odata.nextLink": "https://example.com/next_page",
        }
    )

    httpx_mock.add_response(
        json={
            "value": [
                {
                    "id": "event_id_two",
                    "iCalUId": "ical_event_id_two",
                    "subject": "Event Summary Two",
                    "body": {"content": "Event body two", "contentType": "text"},
                    "start": {"dateTime": "2024-01-02T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-02T12:00:00", "timeZone": "UTC"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                            "type": "required",
                        },
                    ],
                    "onlineMeeting": {"joinUrl": "https://example.com/video2"},
                },
            ]
        }
    )
    with caplog.at_level(logging.ERROR):
        assert await microsoft.fetch_calendar_events(microsoft.MicrosoftCredentials.from_access_token("token", 1)) == [
            CalendarEvent(
                provider="microsoft",
                id="ical_event_id",
                user_specific_id="event_id",
                title="Event Summary",
                body="Event body",
                start_time=datetime(2024, 1, 1, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
                end_time=datetime(2024, 1, 1, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
                all_day=False,
                participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
                meeting_urls=[pydantic.HttpUrl("https://example.com/video")],
            ),
            CalendarEvent(
                provider="microsoft",
                id="ical_event_id_two",
                user_specific_id="event_id_two",
                title="Event Summary Two",
                body="Event body two",
                start_time=datetime(2024, 1, 2, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
                end_time=datetime(2024, 1, 2, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
                all_day=False,
                participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
                meeting_urls=[pydantic.HttpUrl("https://example.com/video2")],
            ),
        ]
        assert not caplog.messages


async def test_fetch_calendar_events_paged_error(httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture) -> None:
    httpx_mock.add_response(
        json={
            "value": [
                {
                    "id": "event_id",
                    "iCalUId": "ical_event_id",
                    "subject": "Event Summary",
                    "body": {"content": "Event body", "contentType": "text"},
                    "start": {"dateTime": "2024-01-01T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-01T12:00:00", "timeZone": "UTC"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                            "type": "required",
                        },
                    ],
                    "onlineMeeting": {"joinUrl": "https://example.com/video"},
                },
            ],
            "@odata.nextLink": "https://example.com/next_page",
        }
    )

    httpx_mock.add_response(status_code=httpx.codes.INTERNAL_SERVER_ERROR)
    with caplog.at_level(logging.ERROR):
        with pytest.raises(Exception):
            assert not await microsoft.fetch_calendar_events(
                microsoft.MicrosoftCredentials.from_access_token("token", 1)
            )
        assert len(caplog.messages) == 1


async def test_microsoft_fetch_calendar_events_with_custom_start_time(httpx_mock: HTTPXMock) -> None:
    httpx_mock.add_response(
        json={
            "value": [
                {
                    "id": "event_id",
                    "iCalUId": "ical_event_id",
                    "subject": "Event Summary",
                    "body": {"content": "Event body", "contentType": "text"},
                    "start": {"dateTime": "2024-01-01T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-01T12:00:00", "timeZone": "UTC"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                            "type": "required",
                        },
                    ],
                    "onlineMeeting": {"joinUrl": "https://example.com/video"},
                }
            ]
        }
    )
    expires_on = int(datetime.now(timezone.utc).timestamp() + 3600)
    interval = timedelta(days=30)
    start_time = datetime.now(timezone.utc)

    assert await microsoft.fetch_calendar_events(
        microsoft.MicrosoftCredentials.from_access_token("token", 1), interval=interval, start_time=start_time
    ) == [
        CalendarEvent(
            provider="microsoft",
            id="ical_event_id",
            user_specific_id="event_id",
            title="Event Summary",
            body="Event body",
            start_time=datetime(2024, 1, 1, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
            end_time=datetime(2024, 1, 1, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
            all_day=False,
            participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
            meeting_urls=[pydantic.HttpUrl("https://example.com/video")],
        ),
    ]

    request = httpx_mock.get_request()
    assert request
    assert request.url.params.get("startDateTime") == start_time.isoformat()
    assert request.url.params.get("endDateTime") == (start_time + interval).isoformat()


@patch("deepinsights.core.integrations.calendar.microsoft.ClientSecretCredential")
async def test_fetch_calendar_events_with_client_credentials(
    mock_client_secret_credential: MagicMock, httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture
) -> None:
    httpx_mock.add_response(
        json={
            "value": [
                {
                    "id": "event_id",
                    "iCalUId": "ical_event_id",
                    "subject": "Event Summary",
                    "body": {"content": "Event body", "contentType": "text"},
                    "start": {"dateTime": "2024-01-01T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-01T12:00:00", "timeZone": "UTC"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                            "type": "required",
                        },
                    ],
                    "onlineMeeting": {"joinUrl": "https://example.com/video"},
                },
                {
                    "id": "event_id_two",
                    "iCalUId": "ical_event_id_two",
                    "subject": "Event Summary Two",
                    "body": {"content": "Event body two", "contentType": "text"},
                    "start": {"dateTime": "2024-01-02T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-02T12:00:00", "timeZone": "UTC"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                            "type": "required",
                        },
                    ],
                    "onlineMeeting": {"joinUrl": "https://example.com/video2"},
                },
            ]
        }
    )
    with caplog.at_level(logging.ERROR):
        assert await microsoft.fetch_calendar_events(
            microsoft.MicrosoftCredentials.from_client_credentials(
                client_id="client_id", client_secret="client_secret", tenant_id="tenant_id", user_id="user_id"
            )
        ) == [
            CalendarEvent(
                provider="microsoft",
                id="ical_event_id",
                user_specific_id="event_id",
                title="Event Summary",
                body="Event body",
                start_time=datetime(2024, 1, 1, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
                end_time=datetime(2024, 1, 1, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
                all_day=False,
                participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
                meeting_urls=[pydantic.HttpUrl("https://example.com/video")],
            ),
            CalendarEvent(
                provider="microsoft",
                id="ical_event_id_two",
                user_specific_id="event_id_two",
                title="Event Summary Two",
                body="Event body two",
                start_time=datetime(2024, 1, 2, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
                end_time=datetime(2024, 1, 2, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
                all_day=False,
                participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
                meeting_urls=[pydantic.HttpUrl("https://example.com/video2")],
            ),
        ]
        assert not caplog.messages

    mock_client_secret_credential.assert_called_once_with(
        client_id="client_id",
        client_secret="client_secret",
        tenant_id="tenant_id",
    )

    request = httpx_mock.get_request()
    assert request
    assert request.url.path == "/v1.0//users/user_id/calendar/calendarView"


async def test_fetch_calendar_events_recurring_fetch_errors(
    httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture
) -> None:
    httpx_mock.add_response(
        json={
            "value": [
                {
                    "id": "event_id",
                    "iCalUId": "ical_event_id",
                    "subject": "Event Summary",
                    "body": {"content": "Event body", "contentType": "text"},
                    "start": {"dateTime": "2024-01-01T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-01T12:00:00", "timeZone": "UTC"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                            "type": "required",
                        },
                    ],
                    "onlineMeeting": {"joinUrl": "https://example.com/video"},
                    "seriesMasterId": "series_master_id",
                },
                {
                    "id": "event_id_two",
                    "iCalUId": "ical_event_id_two",
                    "subject": "Event Summary Two",
                    "body": {"content": "Event body two", "contentType": "text"},
                    "start": {"dateTime": "2024-01-02T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-02T12:00:00", "timeZone": "UTC"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                            "type": "required",
                        },
                    ],
                    "onlineMeeting": {"joinUrl": "https://example.com/video2"},
                    "seriesMasterId": "series_master_id_two",
                },
            ]
        }
    )
    httpx_mock.add_exception(Exception("Test exception"))
    httpx_mock.add_exception(Exception("Test exception"))

    with caplog.at_level(logging.ERROR):
        assert not await microsoft.fetch_calendar_events(microsoft.MicrosoftCredentials.from_access_token("token", 1))
        assert len(caplog.messages) == 2
        assert "Error fetching series master event series_master_id. Skipping." in caplog.messages[0]
        assert "Error fetching series master event series_master_id_two. Skipping." in caplog.messages[1]


async def test_fetch_calendar_events_recurring_partial_fetch_errors(
    httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture
) -> None:
    httpx_mock.add_response(
        json={
            "value": [
                {
                    "id": "event_id",
                    "iCalUId": "ical_event_id",
                    "subject": "Event Summary",
                    "body": {"content": "Event body", "contentType": "text"},
                    "start": {"dateTime": "2024-01-01T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-01T12:00:00", "timeZone": "UTC"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                            "type": "required",
                        },
                    ],
                    "onlineMeeting": {"joinUrl": "https://example.com/video"},
                    "seriesMasterId": "series_master_id",
                },
                {
                    "id": "event_id_two",
                    "iCalUId": "ical_event_id_two",
                    "subject": "Event Summary Two",
                    "body": {"content": "Event body two", "contentType": "text"},
                    "start": {"dateTime": "2024-01-02T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-02T12:00:00", "timeZone": "UTC"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                            "type": "required",
                        },
                    ],
                    "onlineMeeting": {"joinUrl": "https://example.com/video2"},
                    "seriesMasterId": "series_master_id_two",
                },
            ]
        }
    )
    httpx_mock.add_exception(Exception("Test exception"))
    httpx_mock.add_response(json={"iCalUId": "series_master_two_ical_uid"})

    with caplog.at_level(logging.ERROR):
        assert await microsoft.fetch_calendar_events(microsoft.MicrosoftCredentials.from_access_token("token", 1)) == [
            CalendarEvent(
                provider="microsoft",
                id="series_master_two_ical_uid",
                user_specific_id="event_id_two",
                title="Event Summary Two",
                body="Event body two",
                start_time=datetime(2024, 1, 2, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
                end_time=datetime(2024, 1, 2, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
                all_day=False,
                participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
                meeting_urls=[pydantic.HttpUrl("https://example.com/video2")],
            ),
        ]
        assert len(caplog.messages) == 1
        assert "Error fetching series master event series_master_id. Skipping." in caplog.messages[0]


async def test_fetch_calendar_events_recurring_with_original_start(
    httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture
) -> None:
    httpx_mock.add_response(
        json={
            "value": [
                {
                    "id": "event_id",
                    "iCalUId": "ical_event_id",
                    "subject": "Event Summary",
                    "body": {"content": "Event body", "contentType": "text"},
                    "start": {"dateTime": "2024-01-01T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-01T12:00:00", "timeZone": "UTC"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                            "type": "required",
                        },
                    ],
                    "onlineMeeting": {"joinUrl": "https://example.com/video"},
                    "seriesMasterId": "series_master_id",
                    "originalStart": "2024-01-01T10:00:00",
                }
            ],
        }
    )
    httpx_mock.add_response(json={"iCalUId": "series_master_ical_uid"})
    with caplog.at_level(logging.ERROR):
        assert await microsoft.fetch_calendar_events(microsoft.MicrosoftCredentials.from_access_token("token", 1)) == [
            CalendarEvent(
                provider="microsoft",
                id=f"series_master_ical_uid_{int(datetime(2024, 1, 1, 10, 0, 0, tzinfo=ZoneInfo('UTC')).timestamp())}",
                user_specific_id="event_id",
                title="Event Summary",
                body="Event body",
                start_time=datetime(2024, 1, 1, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
                end_time=datetime(2024, 1, 1, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
                all_day=False,
                participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
                meeting_urls=[pydantic.HttpUrl("https://example.com/video")],
            )
        ]
        assert not caplog.messages


async def test_fetch_calendar_events_recurring_no_original_start(
    httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture
) -> None:
    httpx_mock.add_response(
        json={
            "value": [
                {
                    "id": "event_id",
                    "iCalUId": "ical_event_id",
                    "subject": "Event Summary",
                    "body": {"content": "Event body", "contentType": "text"},
                    "start": {"dateTime": "2024-01-01T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-01T12:00:00", "timeZone": "UTC"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                            "type": "required",
                        },
                    ],
                    "onlineMeeting": {"joinUrl": "https://example.com/video"},
                    "seriesMasterId": "series_master_id",
                }
            ],
        }
    )
    httpx_mock.add_response(
        json={
            "iCalUId": "series_master_ical_uid",
        }
    )
    with caplog.at_level(logging.ERROR):
        assert await microsoft.fetch_calendar_events(microsoft.MicrosoftCredentials.from_access_token("token", 1)) == [
            CalendarEvent(
                provider="microsoft",
                id="series_master_ical_uid",
                user_specific_id="event_id",
                title="Event Summary",
                body="Event body",
                start_time=datetime(2024, 1, 1, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
                end_time=datetime(2024, 1, 1, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
                all_day=False,
                participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
                meeting_urls=[pydantic.HttpUrl("https://example.com/video")],
            )
        ]
        assert not caplog.messages


async def test_fetch_calendar_events_meeting_url_in_body(
    httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture
) -> None:
    httpx_mock.add_response(
        json={
            "value": [
                {
                    "id": "event_id",
                    "iCalUId": "ical_event_id",
                    "subject": "Event Summary",
                    "body": {
                        "content": "https://teams.microsoft.com/video",
                        "contentType": "text",
                    },
                    "start": {"dateTime": "2024-01-01T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-01T12:00:00", "timeZone": "UTC"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                            "type": "required",
                        },
                    ],
                },
                {
                    "id": "event_id_two",
                    "iCalUId": "ical_event_id_two",
                    "subject": "Event Summary Two",
                    "body": {
                        "content": '<html><body><a href="https://zoom.us/video">Join Meeting</a></body></html>',
                        "contentType": "html",
                    },
                    "start": {"dateTime": "2024-01-02T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-02T12:00:00", "timeZone": "UTC"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                            "type": "required",
                        },
                    ],
                },
            ]
        }
    )
    with caplog.at_level(logging.ERROR):
        assert await microsoft.fetch_calendar_events(microsoft.MicrosoftCredentials.from_access_token("token", 1)) == [
            CalendarEvent(
                provider="microsoft",
                id="ical_event_id",
                user_specific_id="event_id",
                title="Event Summary",
                body="https://teams.microsoft.com/video",
                start_time=datetime(2024, 1, 1, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
                end_time=datetime(2024, 1, 1, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
                all_day=False,
                participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
                meeting_urls=[pydantic.HttpUrl("https://teams.microsoft.com/video")],
            ),
            CalendarEvent(
                provider="microsoft",
                id="ical_event_id_two",
                user_specific_id="event_id_two",
                title="Event Summary Two",
                body='<html><body><a href="https://zoom.us/video">Join Meeting</a></body></html>',
                start_time=datetime(2024, 1, 2, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
                end_time=datetime(2024, 1, 2, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
                all_day=False,
                participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
                meeting_urls=[pydantic.HttpUrl("https://zoom.us/video")],
            ),
        ]
        assert not caplog.messages


@pytest.mark.parametrize(
    "invalid_event",
    [
        {},
        {
            "id": "event_id_three",
        },
        {
            "iCalUId": "ical_event_id_three",
        },
        {
            "id": "event_id_three",
            "iCalUId": "ical_event_id_three",
        },
        {
            "id": "event_id_three",
            "subject": "Invalid",
        },
        {
            "iCalUId": "ical_event_id_three",
            "subject": "Invalid",
        },
        {
            "id": "event_id_three",
            "subject": "Invalid",
            "start": {"dateTime": "2024-01-03T10:00:00"},
        },
        {
            "id": "event_id_three",
            "subject": "Invalid",
            "start": {"timeZone": "UTC"},
        },
        {
            "id": "event_id_three",
            "subject": "Invalid",
            "start": {"dateTime": "2024-01-03T10:00:00", "timeZone": "UTC"},
            "end": {"dateTime": "2024-01-03T10:00:00"},
        },
        {
            "id": "event_id_three",
            "subject": "Invalid",
            "start": {"dateTime": "2024-01-03T10:00:00", "timeZone": "UTC"},
            "end": {"timeZone": "UTC"},
        },
        {
            "start": {"dateTime": "2024-01-02T10:00:00", "timeZone": "UTC"},
            "end": {"dateTime": "2024-01-02T12:00:00", "timeZone": "UTC"},
            "attendees": [
                {
                    "emailAddress": {"address": "<EMAIL>", "name": "Test User2"},
                    "type": "required",
                },
            ],
            "onlineMeeting": {"joinUrl": "https://example.com/video2"},
        },
        {
            "iCalUId": "ical_event_id_three",
            "isAllDay": False,
            "start": {"dateTime": "2024-01-02T10:00:00", "timeZone": "UTC"},
            "end": {"dateTime": "2024-01-02T12:00:00", "timeZone": "UTC"},
            "attendees": [
                {
                    "emailAddress": {"address": "<EMAIL>", "name": "Test User2"},
                    "type": "required",
                },
            ],
            "onlineMeeting": {"joinUrl": "https://example.com/video2"},
        },
        {
            "id": "event_id_three",
            "isAllDay": False,
            "start": {"dateTime": "2024-01-02T10:00:00", "timeZone": "UTC"},
            "end": {"dateTime": "2024-01-02T12:00:00", "timeZone": "UTC"},
            "attendees": [
                {
                    "emailAddress": {"address": "<EMAIL>", "name": "Test User2"},
                    "type": "required",
                },
            ],
            "onlineMeeting": {"joinUrl": "https://example.com/video2"},
        },
        {
            "id": "event_id_three",
            "iCalUId": "ical_event_id_three",
            "start": {"dateTime": "2024-01-02T10:00:00", "timeZone": "UTC"},
            "end": {"dateTime": "2024-01-02T12:00:00", "timeZone": "UTC"},
            "attendees": [
                {
                    "emailAddress": {"address": "<EMAIL>", "name": "Test User2"},
                    "type": "required",
                },
            ],
            "onlineMeeting": {"joinUrl": "https://example.com/video2"},
        },
    ],
)
async def test_fetch_calendar_events_partial_failure(
    httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture, invalid_event: dict[str, Any]
) -> None:
    httpx_mock.add_response(
        json={
            "value": [
                {
                    "id": "event_id",
                    "iCalUId": "ical_event_id",
                    "subject": "Summary One",
                    "start": {"dateTime": "2024-01-01T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-01T12:00:00", "timeZone": "UTC"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                            "type": "required",
                        },
                    ],
                    "onlineMeeting": {"joinUrl": "https://example.com/video"},
                },
                invalid_event,
                {
                    "id": "event_id_three",
                    "iCalUId": "ical_event_id_three",
                    "subject": "Summary Three",
                    "start": {"dateTime": "2024-01-03T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-03T12:00:00", "timeZone": "UTC"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User 3"},
                            "type": "required",
                        },
                    ],
                    "onlineMeeting": {"joinUrl": "https://example.com/video3"},
                },
            ]
        }
    )
    with caplog.at_level(logging.ERROR):
        assert await microsoft.fetch_calendar_events(microsoft.MicrosoftCredentials.from_access_token("token", 1)) == [
            CalendarEvent(
                provider="microsoft",
                id="ical_event_id",
                user_specific_id="event_id",
                title="Summary One",
                body=None,
                start_time=datetime(2024, 1, 1, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
                end_time=datetime(2024, 1, 1, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
                all_day=False,
                participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
                meeting_urls=[pydantic.HttpUrl("https://example.com/video")],
            ),
            CalendarEvent(
                provider="microsoft",
                id="ical_event_id_three",
                user_specific_id="event_id_three",
                title="Summary Three",
                body=None,
                start_time=datetime(2024, 1, 3, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
                end_time=datetime(2024, 1, 3, 12, 0, 0, tzinfo=ZoneInfo("UTC")),
                all_day=False,
                participants=[EventParticipant(name="Test User 3", email_address="<EMAIL>")],
                meeting_urls=[pydantic.HttpUrl("https://example.com/video3")],
            ),
        ]
        assert len(caplog.messages) == 1


async def test_time_zone_handling_for_fetch_events(httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture) -> None:
    httpx_mock.add_response(
        json={
            "value": [
                {
                    "id": "event_id",
                    "iCalUId": "ical_event_id",
                    "subject": "Summary One",
                    "start": {"dateTime": "2024-01-01T10:00:00", "timeZone": "UTC"},
                    "end": {"dateTime": "2024-01-01T12:00:00", "timeZone": "America/Los_Angeles"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User"},
                            "type": "required",
                        },
                    ],
                    "onlineMeeting": {"joinUrl": "https://example.com/video"},
                },
                {
                    "id": "event_id_two",
                    "iCalUId": "ical_event_id_two",
                    "subject": "Summary Two",
                    "start": {"dateTime": "2024-01-02T10:00:00", "timeZone": "Australia/Sydney"},
                    "end": {"dateTime": "2024-01-02T12:00:00", "timeZone": "America/New_York"},
                    "isAllDay": False,
                    "attendees": [
                        {
                            "emailAddress": {"address": "<EMAIL>", "name": "Test User 2"},
                            "type": "required",
                        },
                    ],
                    "onlineMeeting": {"joinUrl": "https://example.com/video2"},
                },
            ]
        }
    )
    with caplog.at_level(logging.ERROR):
        assert await microsoft.fetch_calendar_events(microsoft.MicrosoftCredentials.from_access_token("token", 1)) == [
            CalendarEvent(
                provider="microsoft",
                id="ical_event_id",
                user_specific_id="event_id",
                title="Summary One",
                body=None,
                start_time=datetime(2024, 1, 1, 10, 0, 0, tzinfo=ZoneInfo("UTC")),
                end_time=datetime(2024, 1, 1, 12, 0, 0, tzinfo=ZoneInfo("America/Los_Angeles")),
                all_day=False,
                participants=[EventParticipant(name="Test User", email_address="<EMAIL>")],
                meeting_urls=[pydantic.HttpUrl("https://example.com/video")],
            ),
            CalendarEvent(
                provider="microsoft",
                id="ical_event_id_two",
                user_specific_id="event_id_two",
                title="Summary Two",
                body=None,
                start_time=datetime(2024, 1, 2, 10, 0, 0, tzinfo=ZoneInfo("Australia/Sydney")),
                end_time=datetime(2024, 1, 2, 12, 0, 0, tzinfo=ZoneInfo("America/New_York")),
                all_day=False,
                participants=[EventParticipant(name="Test User 2", email_address="<EMAIL>")],
                meeting_urls=[pydantic.HttpUrl("https://example.com/video2")],
            ),
        ]
        assert not caplog.messages


async def test_all_day_events(httpx_mock: HTTPXMock, caplog: pytest.LogCaptureFixture) -> None:
    httpx_mock.add_response(
        json={
            "value": [
                {
                    "id": "event_id",
                    "iCalUId": "ical_event_id",
                    "subject": "All Day Event",
                    "body": {"content": "Event body", "contentType": "text"},
                    "start": {"dateTime": "2024-01-01T00:00:00", "timeZone": "America/Los_Angeles"},
                    "end": {"dateTime": "2024-01-02T00:00:00", "timeZone": "America/Los_Angeles"},
                    "isAllDay": True,
                    "attendees": [],
                },
                {
                    "id": "event_id_two",
                    "iCalUId": "ical_event_id_two",
                    "subject": "Summary Two",
                    "body": {"content": "Event body", "contentType": "text"},
                    "start": {"dateTime": "2024-01-03T10:00:00", "timeZone": "America/New_York"},
                    "end": {"dateTime": "2024-01-04T10:00:00", "timeZone": "America/New_York"},
                    "isAllDay": True,
                    "attendees": [],
                },
            ]
        }
    )
    with caplog.at_level(logging.ERROR):
        assert await microsoft.fetch_calendar_events(microsoft.MicrosoftCredentials.from_access_token("token", 1)) == [
            CalendarEvent(
                provider="microsoft",
                id="ical_event_id",
                user_specific_id="event_id",
                title="All Day Event",
                body="Event body",
                start_time=datetime(2024, 1, 1, 0, 0, 0, tzinfo=ZoneInfo("UTC")),
                end_time=datetime(2024, 1, 2, 0, 0, 0, tzinfo=ZoneInfo("UTC")),
                all_day=True,
                participants=[],
                meeting_urls=[],
            ),
            CalendarEvent(
                provider="microsoft",
                id="ical_event_id_two",
                user_specific_id="event_id_two",
                title="Summary Two",
                body="Event body",
                start_time=datetime(2024, 1, 3, 0, 0, 0, tzinfo=ZoneInfo("UTC")),
                end_time=datetime(2024, 1, 4, 0, 0, 0, tzinfo=ZoneInfo("UTC")),
                all_day=True,
                participants=[],
                meeting_urls=[],
            ),
        ]
        assert not caplog.messages
