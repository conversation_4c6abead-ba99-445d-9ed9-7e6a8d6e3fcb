import datetime
import logging

from asgiref.sync import sync_to_async
from django.utils import timezone
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from starlette import status

from deepinsights.core.integrations.calendar import google, microsoft
from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.oauth.google import GoogleOAuth
from deepinsights.core.integrations.oauth.microsoft import MicrosoftOAuth
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.oauth_credentials import OAuthClientCredentials
from deepinsights.users.models.user import User


async def fetch_calendar_events(user: User, lookahead: datetime.timedelta) -> tuple[list[CalendarEvent], bool]:
    events: list[CalendarEvent] = []
    any_succeeded = False
    any_failed = False

    try:
        ms_credentials: microsoft.MicrosoftCredentials | None = None

        # Check if the user has a user-level integration.
        ms_oauth = MicrosoftOAuth()
        ms_access_token = await ms_oauth.get_access_token(user)
        expires_on = int(
            datetime.datetime.timestamp(
                (await ms_oauth.get_expiry(user)) or timezone.now() + datetime.timedelta(minutes=5)
            )
        )
        if ms_access_token and expires_on:
            ms_credentials = microsoft.MicrosoftCredentials.from_access_token(
                access_token=ms_access_token, expires_on=expires_on
            )

        # If there is no user-level integration, check if the user has access via an
        # organization-level integration.
        elif (
            (user_microsoft_id := user.microsoft_id)
            and await sync_to_async(Flags.EnableOrgLevelMicrosoftCalendarIntegration.is_active_for_user)(user)
            and (
                client_credentials := await OAuthClientCredentials.objects.filter(
                    organization=user.organization, provider="microsoft"
                ).afirst()
            )
            and (tenant_id := client_credentials.tenant_id)
        ):
            ms_credentials = microsoft.MicrosoftCredentials.from_client_credentials(
                client_id=MicrosoftOAuth.client_id(),
                client_secret=MicrosoftOAuth.client_secret(),
                tenant_id=tenant_id,
                user_id=user_microsoft_id,
            )
        else:
            logging.info("No Microsoft credentials found for user.")

        # If there are any valid credentials, make the calendar API request.
        if ms_credentials:
            events.extend(await microsoft.fetch_calendar_events(ms_credentials, lookahead))
    except Exception as e:
        logging.error("Error fetching Microsoft calendar events", exc_info=e)
        any_failed = True
    else:
        any_succeeded = True

    try:
        google_oauth = GoogleOAuth()
        google_access_token = await google_oauth.get_access_token(user)
        if google_access_token:
            events.extend(google.fetch_calendar_events(google_access_token, lookahead))
    except Exception as e:
        logging.error("Error fetching Google calendar events", exc_info=e)
        any_failed = True
    else:
        any_succeeded = True

    if await sync_to_async(Flags.EnableCalendarEventsFromCRM.is_active_for_user)(user):
        try:
            # Some CRM handler initializer methods are not async-safe.
            if crm_handler := await sync_to_async(lambda u: u.crm_handler)(user):
                events.extend(await sync_to_async(crm_handler.fetch_events)(user, lookahead))
        except Exception as e:
            logging.error(f"Error fetching CRM calendar events: {e}")
            any_failed = True
        else:
            any_succeeded = True

    if not any_succeeded:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)

    return events, not any_failed
