# Handles OAuth integration for Google and Microsoft calendar integrations.
from typing import Callable, <PERSON><PERSON>

from deepinsights.core.integrations.meetingbot.recall_ai import RecallBotController
from deepinsights.core.integrations.oauth import OAuthCalendarIntegrationProtocol
from deepinsights.users.models.user import User


def update_recall_auto_join_integration(
    user: User,
    enabled: bool,
    oauth: OAuthCalendarIntegrationProtocol,
    calendar_link_function: Callable[[str, str, str, str, str | None], Tuple[bool, str]],
    *,
    force_enable: bool = False,
) -> None:
    """Updates the Recall.ai based bot auto-join integration for a user."""

    # Calendar disabled; disconnect from Recall and remove the calendar ID from the user.
    if not enabled:
        if not (calendar_id := user.recall_calendar_id):
            return
        if not RecallBotController().unlink_calendar(calendar_id):
            raise Exception("Failed to unlink calendar from Recall")
        user.recall_calendar_id = None
        user.recall_calendar_platform = None
        user.save()
        return

    # Calendar connected and Recall calendar ID already set; nothing to do (unless forced).
    if user.recall_calendar_id and not force_enable:
        return

    # Calendar connected; link to Recall
    if not (refresh_token := oauth.get_refresh_token(user)):
        raise Exception("No refresh token found for user")
    recall_status, id = calendar_link_function(
        oauth.client_id(),
        oauth.client_secret(),
        user.email,
        refresh_token,
        user.recall_calendar_id,
    )
    if not recall_status:
        raise Exception("Failed to link calendar to Recall")

    user.recall_calendar_id = id
    user.recall_calendar_platform = RecallBotController.connected_calendar_platform(id)
    user.save()
