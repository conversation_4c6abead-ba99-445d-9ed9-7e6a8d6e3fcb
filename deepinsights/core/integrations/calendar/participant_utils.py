from typing import Any, Protocol, Self, Sequence, TypeVar

from django.db.models.functions import Upper

from deepinsights.core.integrations.calendar.calendar_models import EventParticipant
from deepinsights.meetingsapp.models.client import Client
from deepinsights.users.models.user import User


class _HasParticipants(Protocol):
    participants: list[EventParticipant]

    # This is the model_copy method implemented on BaseModel.
    def model_copy(self, *, update: dict[str, Any] | None = None, deep: bool = False) -> Self:
        ...


T = TypeVar("T", bound=_HasParticipants)


# Attemps to map attendees in the provided calendar events to Zeplyn clients and users.
#
# Returns a list with the same events as the input, but with attendees mapped to Zeplyn clients and
# users (when possible). The base case for this (i.e., no users and clients) is to return a
# list equivalent to the input.
#
# Note that this code is performance sensitive and has the potential to be slow: it runs during
# calendar event fetching, and can do a signifant number of database queries. Make sure to profile
# this code if you change it.
def events_with_zeplyn_attendees(events: Sequence[T], user: User) -> list[T]:
    crm_system = user.get_crm_configuration().crm_system
    events_with_zeplyn_attendees: list[T] = []
    for event in events:
        zeplyn_event = event.model_copy(deep=True)
        for participant in zeplyn_event.participants:
            try:
                user_for_participant = User.objects.alias(email_upper=Upper("email")).get(
                    organization=user.organization, email_upper=participant.email_address.upper()
                )
                if user_name := user_for_participant.name:
                    participant.name = user_name
                participant.zeplyn_uuid = user_for_participant.uuid
                participant.zeplyn_kind = EventParticipant.ZeplynKind.USER
                continue
            except User.DoesNotExist:
                try:
                    client = Client.objects.alias(email_upper=Upper("email")).filter(
                        authorized_users=user, email_upper=participant.email_address.upper(), crm_system=crm_system
                    )[0]
                    if client_name := client.name:
                        participant.name = client_name
                    participant.zeplyn_uuid = client.uuid
                    participant.zeplyn_kind = EventParticipant.ZeplynKind.CLIENT
                except IndexError:
                    pass
        events_with_zeplyn_attendees.append(zeplyn_event)

    return events_with_zeplyn_attendees
