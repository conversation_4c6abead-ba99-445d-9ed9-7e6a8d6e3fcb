from __future__ import annotations

import json
from datetime import datetime, timedelta, timezone
from typing import TYPE_CHECKING, Any
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch

import pydantic
import pytest
from googleapiclient.discovery import build
from googleapiclient.http import HttpMockSequence
from zoneinfo import ZoneInfo

from deepinsights.core.integrations.calendar import google
from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent, EventParticipant

if TYPE_CHECKING:
    # See https://pypi.org/project/google-api-python-client-stubs/ for details about importing types
    # for Google API clients.
    from googleapiclient._apis.calendar.v3 import (  # pyright: ignore
        Event,
    )


def _set_up_events_service_mock(
    mock_events_service: MagicMock, responses: list[tuple[dict[str, Any], str]]
) -> MagicMock:
    service = build("calendar", "v3", http=HttpMockSequence(responses)).events()  # type: ignore[call-overload]
    service_mock = Mock(wraps=service)
    mock_events_service.return_value = service_mock
    return service_mock


def test_fetch_calendar_event_invalid_token() -> None:
    with pytest.raises(Exception):
        assert not google.fetch_calendar_event("token", "event_id")


@patch("deepinsights.core.integrations.calendar.google._events_service")
def test_fetch_calendar_event_error(mock_events_service: MagicMock) -> None:
    _set_up_events_service_mock(mock_events_service, [({"status": 401}, "")])
    with pytest.raises(Exception):
        assert not google.fetch_calendar_event("token", "event_id")


@patch("deepinsights.core.integrations.calendar.google._events_service")
def test_fetch_calendar_event(mock_events_service: MagicMock) -> None:
    description = """
      This is a description with a video link: https://zoom.us/abc-123-def
      http://teams.microsoft.com/abc-123-def
      It will have some other meeting URLs as well
      zoom.us/meeting
      http:zoom.us/meeting
      http://foo.webex.com/meeting2
      And some other URLs
      not-a-meeting.com
    """
    event_result = {
        "id": "event_id",
        "summary": "Event Summary",
        "description": description,
        "start": {"dateTime": "2022-01-03T16:00:00Z"},
        "end": {"dateTime": "2022-01-03T18:00:00Z"},
        "attendees": [
            {"id": "attendee_id_1", "displayName": "Attendee Name 1", "email": "<EMAIL>"},
            {"id": "attendee_id_2", "displayName": "Attendee Name 2", "email": "<EMAIL>"},
        ],
        "organizer": {"id": "organizer_id", "displayName": "Organizer Name", "email": "<EMAIL>"},
        "conferenceData": {
            "entryPoints": [
                {"entryPointType": "video", "uri": "https://example.com/video"},
                {"entryPointType": "more", "uri": "https://example.com/more"},
            ]
        },
    }
    service_spy = _set_up_events_service_mock(mock_events_service, [({"status": 200}, json.dumps(event_result))])

    expected_result = CalendarEvent(
        provider="google",
        id="event_id",
        user_specific_id="event_id",
        title="Event Summary",
        body=description,
        start_time=datetime(2022, 1, 3, 16, 0, 0, tzinfo=timezone.utc),
        end_time=datetime(2022, 1, 3, 18, 0, 0, tzinfo=timezone.utc),
        all_day=False,
        participants=[
            EventParticipant(id="attendee_id_1", name="Attendee Name 1", email_address="<EMAIL>"),
            EventParticipant(id="attendee_id_2", name="Attendee Name 2", email_address="<EMAIL>"),
        ],
        meeting_urls=[
            pydantic.HttpUrl("https://example.com/video"),
            pydantic.HttpUrl("https://example.com/more"),
            pydantic.HttpUrl("https://zoom.us/abc-123-def"),
            pydantic.HttpUrl("http://teams.microsoft.com/abc-123-def"),
            pydantic.HttpUrl("http://foo.webex.com/meeting2"),
        ],
    )

    result = google.fetch_calendar_event("token", "event_id")

    assert result == expected_result
    service_spy.get.assert_called_once_with(calendarId="primary", eventId="event_id")


@patch("deepinsights.core.integrations.calendar.google.build")
def test_fetch_minimal_calendar_event(build: MagicMock) -> None:
    event_result = {
        "id": "event_id",
        "summary": "Event Summary",
        "start": {"dateTime": "2022-01-01T10:00:00Z"},
        "end": {"dateTime": "2022-01-01T12:00:00Z"},
    }
    build.return_value.events.return_value.get.return_value.execute.return_value = event_result

    expected_result = CalendarEvent(
        provider="google",
        id="event_id",
        user_specific_id="event_id",
        title="Event Summary",
        body="",
        start_time=datetime(2022, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
        end_time=datetime(2022, 1, 1, 12, 0, 0, tzinfo=timezone.utc),
        all_day=False,
        participants=[],
        meeting_urls=[],
    )

    result = google.fetch_calendar_event("token", "event_id")
    assert result == expected_result


def test_fetch_calendar_events_no_oauth_token() -> None:
    with pytest.raises(Exception):
        assert not google.fetch_calendar_events("token")


@patch("deepinsights.core.integrations.calendar.google._events_service")
def test_fetch_calendar_events_error(build: MagicMock) -> None:
    _set_up_events_service_mock(build, [({"status": 401}, "")])

    with pytest.raises(Exception):
        assert not google.fetch_calendar_events("token")


@patch("deepinsights.core.integrations.calendar.google._events_service")
def test_default_interval(mock_events_service: MagicMock) -> None:
    service_spy = _set_up_events_service_mock(mock_events_service, [({"status": 200}, json.dumps({"items": []}))])
    assert not google.fetch_calendar_events("token")
    call_args = service_spy.list.call_args.kwargs
    assert datetime.fromisoformat(call_args["timeMax"]) == datetime.fromisoformat(call_args["timeMin"]) + timedelta(
        days=1
    )


@patch("deepinsights.core.integrations.calendar.google._events_service")
def test_interval(mock_events_service: MagicMock) -> None:
    service_spy = _set_up_events_service_mock(mock_events_service, [({"status": 200}, json.dumps({"items": []}))])
    assert not google.fetch_calendar_events("token", timedelta(hours=1))
    call_args = service_spy.list.call_args.kwargs
    assert datetime.fromisoformat(call_args["timeMax"]) == datetime.fromisoformat(call_args["timeMin"]) + timedelta(
        hours=1
    )


@patch("deepinsights.core.integrations.calendar.google._events_service")
def test_fetch_calendar_events(mock_events_service: MagicMock) -> None:
    description = """
      This is a description with a video link: https://zoom.us/abc-123-def
      http://teams.microsoft.com/abc-123-def
      It will have some other meeting URLs as well
      zoom.us/meeting
      http:zoom.us/meeting
      http://foo.webex.com/meeting2
      And some other URLs
      not-a-meeting.com
    """
    events_result = {
        "items": [
            {
                "id": "event_id",
                "location": "https://example.com/location",
                "summary": "Event Summary",
                "start": {"dateTime": "2022-01-01T10:00:00Z"},
                "end": {"dateTime": "2022-01-01T12:00:00Z"},
                "attendees": [
                    {"id": "attendee_id", "displayName": "Attendee Name", "email": "<EMAIL>"},
                ],
                "conferenceData": {
                    "entryPoints": [
                        {"entryPointType": "video", "uri": "https://example.com/video"},
                        {"entryPointType": "more", "uri": "https://example.com/more"},
                    ]
                },
            },
            {
                "id": "event_id_2",
                "summary": "Event Summary 2",
                "start": {"dateTime": "2022-01-02T14:00:00Z"},
                "end": {"dateTime": "2022-01-02T16:00:00Z"},
                "attendees": [
                    {"id": "attendee_id_2", "displayName": "Attendee Name 2", "email": "<EMAIL>"},
                    {"id": "attendee_id_3", "displayName": "Attendee Name 3", "email": "<EMAIL>"},
                    {"id": "attendee_id_4", "displayName": "Attendee Name 4", "email": "<EMAIL>"},
                    {"id": "attendee_id_5", "displayName": "Attendee Name 5", "email": "<EMAIL>"},
                ],
                "conferenceData": {
                    "entryPoints": [
                        {"entryPointType": "video", "uri": "https://example.com/video2"},
                        {"entryPointType": "more", "uri": "https://example.com/more2"},
                    ]
                },
            },
            {
                "id": "event_id_3",
                "summary": "Event Summary 3",
                "description": description,
                "start": {"dateTime": "2022-01-03T16:00:00Z"},
                "end": {"dateTime": "2022-01-03T18:00:00Z"},
                "attendees": [
                    {"id": "attendee_id_3", "displayName": "Attendee Name 3", "email": "<EMAIL>"},
                    {"id": "attendee_id_4", "displayName": "Attendee Name 4", "email": "<EMAIL>"},
                    {"id": "attendee_id_5", "displayName": "Attendee Name 5", "email": "<EMAIL>"},
                    {"id": "attendee_id_6", "displayName": "Attendee Name 6", "email": "<EMAIL>"},
                ],
                "conferenceData": {
                    "entryPoints": [
                        {"entryPointType": "video", "uri": "https://example.com/video3"},
                        {"entryPointType": "more", "uri": "https://example.com/more3"},
                    ]
                },
            },
        ]
    }
    _set_up_events_service_mock(mock_events_service, [({"status": 200}, json.dumps(events_result))])

    expected_result = [
        CalendarEvent(
            provider="google",
            id="event_id",
            user_specific_id="event_id",
            title="Event Summary",
            body="",
            start_time=datetime(2022, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 1, 12, 0, 0, tzinfo=timezone.utc),
            all_day=False,
            participants=[
                EventParticipant(id="attendee_id", name="Attendee Name", email_address="<EMAIL>")
            ],
            meeting_urls=[
                pydantic.HttpUrl("https://example.com/video"),
                pydantic.HttpUrl("https://example.com/more"),
                pydantic.HttpUrl("https://example.com/location"),
            ],
        ),
        CalendarEvent(
            provider="google",
            id="event_id_2",
            user_specific_id="event_id_2",
            title="Event Summary 2",
            body="",
            start_time=datetime(2022, 1, 2, 14, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 2, 16, 0, 0, tzinfo=timezone.utc),
            all_day=False,
            participants=[
                EventParticipant(id="attendee_id_2", name="Attendee Name 2", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_3", name="Attendee Name 3", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_4", name="Attendee Name 4", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_5", name="Attendee Name 5", email_address="<EMAIL>"),
            ],
            meeting_urls=[
                pydantic.HttpUrl("https://example.com/video2"),
                pydantic.HttpUrl("https://example.com/more2"),
            ],
        ),
        CalendarEvent(
            provider="google",
            id="event_id_3",
            user_specific_id="event_id_3",
            title="Event Summary 3",
            body=description,
            start_time=datetime(2022, 1, 3, 16, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 3, 18, 0, 0, tzinfo=timezone.utc),
            all_day=False,
            participants=[
                EventParticipant(id="attendee_id_3", name="Attendee Name 3", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_4", name="Attendee Name 4", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_5", name="Attendee Name 5", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_6", name="Attendee Name 6", email_address="<EMAIL>"),
            ],
            meeting_urls=[
                pydantic.HttpUrl("https://example.com/video3"),
                pydantic.HttpUrl("https://example.com/more3"),
                pydantic.HttpUrl("https://zoom.us/abc-123-def"),
                pydantic.HttpUrl("http://teams.microsoft.com/abc-123-def"),
                pydantic.HttpUrl("http://foo.webex.com/meeting2"),
            ],
        ),
    ]

    result = google.fetch_calendar_events("token")

    assert result == expected_result


@patch("deepinsights.core.integrations.calendar.google._events_service")
def test_fetch_calendar_events_paged(mock_events_service: MagicMock) -> None:
    description = """
      This is a description with a video link: https://zoom.us/abc-123-def
      http://teams.microsoft.com/abc-123-def
      It will have some other meeting URLs as well
      zoom.us/meeting
      http:zoom.us/meeting
      http://foo.webex.com/meeting2
      And some other URLs
      not-a-meeting.com
    """
    first_events_result = {
        "items": [
            {
                "id": "event_id",
                "location": "https://example.com/location",
                "summary": "Event Summary",
                "start": {"dateTime": "2022-01-01T10:00:00Z"},
                "end": {"dateTime": "2022-01-01T12:00:00Z"},
                "attendees": [
                    {"id": "attendee_id", "displayName": "Attendee Name", "email": "<EMAIL>"},
                ],
                "conferenceData": {
                    "entryPoints": [
                        {"entryPointType": "video", "uri": "https://example.com/video"},
                        {"entryPointType": "more", "uri": "https://example.com/more"},
                    ]
                },
            },
        ],
        "nextPageToken": "next_page_token",
    }
    second_events_result = {
        "items": [
            {
                "id": "event_id_2",
                "summary": "Event Summary 2",
                "start": {"dateTime": "2022-01-02T14:00:00Z"},
                "end": {"dateTime": "2022-01-02T16:00:00Z"},
                "attendees": [
                    {"id": "attendee_id_2", "displayName": "Attendee Name 2", "email": "<EMAIL>"},
                    {"id": "attendee_id_3", "displayName": "Attendee Name 3", "email": "<EMAIL>"},
                    {"id": "attendee_id_4", "displayName": "Attendee Name 4", "email": "<EMAIL>"},
                    {"id": "attendee_id_5", "displayName": "Attendee Name 5", "email": "<EMAIL>"},
                ],
                "conferenceData": {
                    "entryPoints": [
                        {"entryPointType": "video", "uri": "https://example.com/video2"},
                        {"entryPointType": "more", "uri": "https://example.com/more2"},
                    ]
                },
            },
        ],
        "nextPageToken": "next_page_token_two",
    }
    third_events_result = {
        "items": [
            {
                "id": "event_id_3",
                "summary": "Event Summary 3",
                "description": description,
                "start": {"dateTime": "2022-01-03T16:00:00Z"},
                "end": {"dateTime": "2022-01-03T18:00:00Z"},
                "attendees": [
                    {"id": "attendee_id_3", "displayName": "Attendee Name 3", "email": "<EMAIL>"},
                    {"id": "attendee_id_4", "displayName": "Attendee Name 4", "email": "<EMAIL>"},
                    {"id": "attendee_id_5", "displayName": "Attendee Name 5", "email": "<EMAIL>"},
                    {"id": "attendee_id_6", "displayName": "Attendee Name 6", "email": "<EMAIL>"},
                ],
                "conferenceData": {
                    "entryPoints": [
                        {"entryPointType": "video", "uri": "https://example.com/video3"},
                        {"entryPointType": "more", "uri": "https://example.com/more3"},
                    ]
                },
            },
        ],
        "nextPageToken": None,
    }
    _set_up_events_service_mock(
        mock_events_service,
        [
            ({"status": 200}, json.dumps(first_events_result)),
            ({"status": 200}, json.dumps(second_events_result)),
            ({"status": 200}, json.dumps(third_events_result)),
        ],
    )

    expected_result = [
        CalendarEvent(
            provider="google",
            id="event_id",
            user_specific_id="event_id",
            title="Event Summary",
            body="",
            start_time=datetime(2022, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 1, 12, 0, 0, tzinfo=timezone.utc),
            all_day=False,
            participants=[
                EventParticipant(id="attendee_id", name="Attendee Name", email_address="<EMAIL>")
            ],
            meeting_urls=[
                pydantic.HttpUrl("https://example.com/video"),
                pydantic.HttpUrl("https://example.com/more"),
                pydantic.HttpUrl("https://example.com/location"),
            ],
        ),
        CalendarEvent(
            provider="google",
            id="event_id_2",
            user_specific_id="event_id_2",
            title="Event Summary 2",
            body="",
            start_time=datetime(2022, 1, 2, 14, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 2, 16, 0, 0, tzinfo=timezone.utc),
            all_day=False,
            participants=[
                EventParticipant(id="attendee_id_2", name="Attendee Name 2", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_3", name="Attendee Name 3", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_4", name="Attendee Name 4", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_5", name="Attendee Name 5", email_address="<EMAIL>"),
            ],
            meeting_urls=[
                pydantic.HttpUrl("https://example.com/video2"),
                pydantic.HttpUrl("https://example.com/more2"),
            ],
        ),
        CalendarEvent(
            provider="google",
            id="event_id_3",
            user_specific_id="event_id_3",
            title="Event Summary 3",
            body=description,
            start_time=datetime(2022, 1, 3, 16, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 3, 18, 0, 0, tzinfo=timezone.utc),
            all_day=False,
            participants=[
                EventParticipant(id="attendee_id_3", name="Attendee Name 3", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_4", name="Attendee Name 4", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_5", name="Attendee Name 5", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_6", name="Attendee Name 6", email_address="<EMAIL>"),
            ],
            meeting_urls=[
                pydantic.HttpUrl("https://example.com/video3"),
                pydantic.HttpUrl("https://example.com/more3"),
                pydantic.HttpUrl("https://zoom.us/abc-123-def"),
                pydantic.HttpUrl("http://teams.microsoft.com/abc-123-def"),
                pydantic.HttpUrl("http://foo.webex.com/meeting2"),
            ],
        ),
    ]

    result = google.fetch_calendar_events("token")

    assert result == expected_result


@patch("deepinsights.core.integrations.calendar.google._events_service")
def test_fetch_calendar_events_paging_failure(mock_events_service: MagicMock) -> None:
    first_events_result = {
        "items": [
            {
                "id": "event_id",
                "location": "https://example.com/location",
                "summary": "Event Summary",
                "start": {"dateTime": "2022-01-01T10:00:00Z"},
                "end": {"dateTime": "2022-01-01T12:00:00Z"},
                "attendees": [
                    {"id": "attendee_id", "displayName": "Attendee Name", "email": "<EMAIL>"},
                ],
                "conferenceData": {
                    "entryPoints": [
                        {"entryPointType": "video", "uri": "https://example.com/video"},
                        {"entryPointType": "more", "uri": "https://example.com/more"},
                    ]
                },
            },
        ],
        "nextPageToken": "next_page_token",
    }
    second_events_result = {
        "items": [
            {
                "id": "event_id_2",
                "summary": "Event Summary 2",
                "start": {"dateTime": "2022-01-02T14:00:00Z"},
                "end": {"dateTime": "2022-01-02T16:00:00Z"},
                "attendees": [
                    {"id": "attendee_id_2", "displayName": "Attendee Name 2", "email": "<EMAIL>"},
                    {"id": "attendee_id_3", "displayName": "Attendee Name 3", "email": "<EMAIL>"},
                    {"id": "attendee_id_4", "displayName": "Attendee Name 4", "email": "<EMAIL>"},
                    {"id": "attendee_id_5", "displayName": "Attendee Name 5", "email": "<EMAIL>"},
                ],
                "conferenceData": {
                    "entryPoints": [
                        {"entryPointType": "video", "uri": "https://example.com/video2"},
                        {"entryPointType": "more", "uri": "https://example.com/more2"},
                    ]
                },
            },
        ],
        "nextPageToken": "next_page_token_two",
    }
    _set_up_events_service_mock(
        mock_events_service,
        [
            ({"status": 200}, json.dumps(first_events_result)),
            ({"status": 200}, json.dumps(second_events_result)),
            ({"status": 400}, ""),
        ],
    )

    with pytest.raises(Exception):
        assert not google.fetch_calendar_events("token")


@patch("deepinsights.core.integrations.calendar.google._events_service")
def test_fetch_calendar_events_with_custom_start_time(mock_events_service: MagicMock) -> None:
    events_result = {
        "items": [
            {
                "id": "event_id",
                "location": "https://example.com/location",
                "summary": "Event Summary",
                "start": {"dateTime": "2022-01-01T10:00:00Z"},
                "end": {"dateTime": "2022-01-01T12:00:00Z"},
                "attendees": [
                    {"id": "attendee_id", "displayName": "Attendee Name", "email": "<EMAIL>"},
                ],
                "conferenceData": {
                    "entryPoints": [
                        {"entryPointType": "video", "uri": "https://example.com/video"},
                        {"entryPointType": "more", "uri": "https://example.com/more"},
                    ]
                },
            },
        ]
    }
    service_spy = _set_up_events_service_mock(mock_events_service, [({"status": 200}, json.dumps(events_result))])

    expected_result = [
        CalendarEvent(
            provider="google",
            id="event_id",
            user_specific_id="event_id",
            title="Event Summary",
            body="",
            start_time=datetime(2022, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 1, 12, 0, 0, tzinfo=timezone.utc),
            all_day=False,
            participants=[
                EventParticipant(id="attendee_id", name="Attendee Name", email_address="<EMAIL>")
            ],
            meeting_urls=[
                pydantic.HttpUrl("https://example.com/video"),
                pydantic.HttpUrl("https://example.com/more"),
                pydantic.HttpUrl("https://example.com/location"),
            ],
        ),
    ]

    start_time = datetime(2025, 1, 1, 0, 0, 0, tzinfo=timezone.utc)
    result = google.fetch_calendar_events("token", interval=timedelta(days=30), start_time=start_time)

    assert result == expected_result

    service_spy.list.assert_called_once_with(
        calendarId="primary",
        timeMin=start_time.isoformat(),
        timeMax=(start_time + timedelta(days=30)).isoformat(),
        singleEvents=True,
        orderBy="startTime",
        maxResults=250,
    )


@patch("deepinsights.core.integrations.calendar.google._events_service")
def test_fetch_calendar_events_partial_errors(mock_events_service: MagicMock) -> None:
    events_result = {
        "items": [
            {
                "id": "event_id",
                "summary": "Event Summary",
                "start": {"dateTime": "2022-01-01T10:00:00Z"},
                "end": {"dateTime": "2022-01-01T12:00:00Z"},
                "attendees": [
                    {"id": "attendee_id", "displayName": "Attendee Name", "email": "<EMAIL>"},
                ],
                "conferenceData": {
                    "entryPoints": [
                        {"entryPointType": "video", "uri": "https://example.com/video"},
                        {"entryPointType": "more", "uri": "https://example.com/more"},
                    ]
                },
            },
            {
                "summary": "Event Summary 2",
                "start": {"dateTime": "2022-01-02T14:00:00Z"},
                "end": {"dateTime": "2022-01-02T16:00:00Z"},
                "attendees": [
                    {"id": "attendee_id_2", "displayName": "Attendee Name 2", "email": "<EMAIL>"},
                    {"id": "attendee_id_3", "displayName": "Attendee Name 3", "email": "<EMAIL>"},
                    {"id": "attendee_id_4", "displayName": "Attendee Name 4", "email": "<EMAIL>"},
                    {"id": "attendee_id_5", "displayName": "Attendee Name 5", "email": "<EMAIL>"},
                ],
                "conferenceData": {
                    "entryPoints": [
                        {"entryPointType": "video", "uri": "https://example.com/video2"},
                        {"entryPointType": "more", "uri": "https://example.com/more2"},
                    ]
                },
            },
            {
                "id": "event_id_3",
                "summary": "Event Summary 3",
                "start": {"dateTime": "2022-01-03T16:00:00Z"},
                "end": {"dateTime": "2022-01-03T18:00:00Z"},
                "attendees": [
                    {"id": "attendee_id_3", "displayName": "Attendee Name 3", "email": "<EMAIL>"},
                    {"id": "attendee_id_4", "displayName": "Attendee Name 4", "email": "<EMAIL>"},
                    {"id": "attendee_id_5", "displayName": "Attendee Name 5", "email": "<EMAIL>"},
                    {"id": "attendee_id_6", "displayName": "Attendee Name 6", "email": "<EMAIL>"},
                ],
                "conferenceData": {
                    "entryPoints": [
                        {"entryPointType": "video", "uri": "https://example.com/video3"},
                        {"entryPointType": "more", "uri": "https://example.com/more3"},
                    ]
                },
            },
        ]
    }
    _set_up_events_service_mock(mock_events_service, [({"status": 200}, json.dumps(events_result))])

    expected_result = [
        CalendarEvent(
            provider="google",
            id="event_id",
            user_specific_id="event_id",
            title="Event Summary",
            body="",
            start_time=datetime(2022, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 1, 12, 0, 0, tzinfo=timezone.utc),
            all_day=False,
            participants=[
                EventParticipant(id="attendee_id", name="Attendee Name", email_address="<EMAIL>")
            ],
            meeting_urls=[pydantic.HttpUrl("https://example.com/video"), pydantic.HttpUrl("https://example.com/more")],
        ),
        CalendarEvent(
            provider="google",
            id="event_id_3",
            user_specific_id="event_id_3",
            title="Event Summary 3",
            body="",
            start_time=datetime(2022, 1, 3, 16, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 3, 18, 0, 0, tzinfo=timezone.utc),
            all_day=False,
            participants=[
                EventParticipant(id="attendee_id_3", name="Attendee Name 3", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_4", name="Attendee Name 4", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_5", name="Attendee Name 5", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_6", name="Attendee Name 6", email_address="<EMAIL>"),
            ],
            meeting_urls=[
                pydantic.HttpUrl("https://example.com/video3"),
                pydantic.HttpUrl("https://example.com/more3"),
            ],
        ),
    ]

    result = google.fetch_calendar_events("token")

    assert result == expected_result


@patch("deepinsights.core.integrations.calendar.google._events_service")
def test_fetch_minimal_calendar_events(mock_events_service: MagicMock) -> None:
    events_result = {
        "items": [
            {
                "id": "event_id",
                "summary": "Event Summary",
                "start": {"dateTime": "2022-01-01T10:00:00Z"},
                "end": {"dateTime": "2022-01-01T12:00:00Z"},
            },
            {
                "id": "event_id_2",
                "summary": "Event Summary 2",
                "start": {"dateTime": "2022-01-02T14:00:00Z"},
                "end": {"dateTime": "2022-01-02T16:00:00Z"},
                "attendees": [],
                "conferenceData": {},
            },
            {
                "id": "event_id_3",
                "start": {"dateTime": "2022-01-03T18:00:00Z"},
                "end": {"dateTime": "2022-01-03T20:00:00Z"},
            },
        ]
    }
    _set_up_events_service_mock(mock_events_service, [({"status": 200}, json.dumps(events_result))])

    expected_result = [
        CalendarEvent(
            provider="google",
            id="event_id",
            user_specific_id="event_id",
            title="Event Summary",
            body="",
            start_time=datetime(2022, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 1, 12, 0, 0, tzinfo=timezone.utc),
            all_day=False,
            participants=[],
            meeting_urls=[],
        ),
        CalendarEvent(
            provider="google",
            id="event_id_2",
            user_specific_id="event_id_2",
            title="Event Summary 2",
            body="",
            start_time=datetime(2022, 1, 2, 14, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 2, 16, 0, 0, tzinfo=timezone.utc),
            all_day=False,
            participants=[],
            meeting_urls=[],
        ),
        CalendarEvent(
            provider="google",
            id="event_id_3",
            user_specific_id="event_id_3",
            title="",
            body="",
            start_time=datetime(2022, 1, 3, 18, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 3, 20, 0, 0, tzinfo=timezone.utc),
            all_day=False,
            participants=[],
            meeting_urls=[],
        ),
    ]

    result = google.fetch_calendar_events("token")

    assert result == expected_result


@patch("deepinsights.core.integrations.calendar.google._events_service")
def test_fetch_all_day_calendar_events(mock_events_service: MagicMock) -> None:
    events_result = {
        "items": [
            {
                "id": "event_id",
                "summary": "Event Summary",
                "start": {"date": "2022-01-01"},
                "end": {"date": "2022-01-02"},
            },
            {
                "id": "event_id_2",
                "summary": "Event Summary 2",
                "start": {"date": "2022-01-02"},
                "end": {"date": "2022-01-04"},
            },
        ]
    }
    _set_up_events_service_mock(mock_events_service, [({"status": 200}, json.dumps(events_result))])

    expected_result = [
        CalendarEvent(
            provider="google",
            id="event_id",
            user_specific_id="event_id",
            title="Event Summary",
            body="",
            start_time=datetime(2022, 1, 1, 0, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 2, 0, 0, 0, tzinfo=timezone.utc),
            all_day=True,
            participants=[],
            meeting_urls=[],
        ),
        CalendarEvent(
            provider="google",
            id="event_id_2",
            user_specific_id="event_id_2",
            title="Event Summary 2",
            body="",
            start_time=datetime(2022, 1, 2, 0, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 4, 0, 0, 0, tzinfo=timezone.utc),
            all_day=True,
            participants=[],
            meeting_urls=[],
        ),
    ]

    result = google.fetch_calendar_events("token")

    assert result == expected_result


def test_calendar_entries_from_events() -> None:
    description = """
      This is a description with a video link: https://zoom.us/abc-123-def
      http://teams.microsoft.com/abc-123-def
      It will have some other meeting URLs as well
      zoom.us/meeting
      http:zoom.us/meeting
      http://foo.webex.com/meeting2
      And some other URLs
      not-a-meeting.com
    """
    events: list[Event] = [
        {
            "id": "event_id",
            "location": "https://example.com/location",
            "summary": "Event Summary",
            "start": {"dateTime": "2022-01-01T10:00:00Z"},
            "end": {"dateTime": "2022-01-01T12:00:00Z"},
            "attendees": [
                {"id": "attendee_id", "displayName": "Attendee Name", "email": "<EMAIL>"},
            ],
            "conferenceData": {
                "entryPoints": [
                    {"entryPointType": "video", "uri": "https://example.com/video"},
                    {"entryPointType": "more", "uri": "https://example.com/more"},
                ]
            },
        },
        {
            "id": "event_id_2",
            "summary": "Event Summary 2",
            "start": {"dateTime": "2022-01-02T14:00:00Z"},
            "end": {"dateTime": "2022-01-02T16:00:00Z"},
            "attendees": [
                {"id": "attendee_id_2", "displayName": "Attendee Name 2", "email": "<EMAIL>"},
                {"id": "attendee_id_3", "displayName": "Attendee Name 3", "email": "<EMAIL>"},
                {"id": "attendee_id_4", "displayName": "Attendee Name 4", "email": "<EMAIL>"},
                {"id": "attendee_id_5", "displayName": "Attendee Name 5", "email": "<EMAIL>"},
            ],
            "conferenceData": {
                "entryPoints": [
                    {"entryPointType": "video", "uri": "https://example.com/video2"},
                    {"entryPointType": "more", "uri": "https://example.com/more2"},
                ]
            },
        },
        {
            "id": "event_id_3",
            "summary": "Event Summary 3",
            "description": description,
            "start": {"dateTime": "2022-01-03T16:00:00Z"},
            "end": {"dateTime": "2022-01-03T18:00:00Z"},
            "attendees": [
                {"id": "attendee_id_3", "displayName": "Attendee Name 3", "email": "<EMAIL>"},
                {"id": "attendee_id_4", "displayName": "Attendee Name 4", "email": "<EMAIL>"},
                {"id": "attendee_id_5", "displayName": "Attendee Name 5", "email": "<EMAIL>"},
                {"id": "attendee_id_6", "displayName": "Attendee Name 6", "email": "<EMAIL>"},
            ],
            "conferenceData": {
                "entryPoints": [
                    {"entryPointType": "video", "uri": "https://example.com/video3"},
                    {"entryPointType": "more", "uri": "https://example.com/more3"},
                ]
            },
        },
    ]

    expected_result = [
        CalendarEvent(
            provider="google",
            id="event_id",
            user_specific_id="event_id",
            title="Event Summary",
            body="",
            start_time=datetime(2022, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 1, 12, 0, 0, tzinfo=timezone.utc),
            all_day=False,
            participants=[
                EventParticipant(id="attendee_id", name="Attendee Name", email_address="<EMAIL>")
            ],
            meeting_urls=[
                pydantic.HttpUrl("https://example.com/video"),
                pydantic.HttpUrl("https://example.com/more"),
                pydantic.HttpUrl("https://example.com/location"),
            ],
        ),
        CalendarEvent(
            provider="google",
            id="event_id_2",
            user_specific_id="event_id_2",
            title="Event Summary 2",
            body="",
            start_time=datetime(2022, 1, 2, 14, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 2, 16, 0, 0, tzinfo=timezone.utc),
            all_day=False,
            participants=[
                EventParticipant(id="attendee_id_2", name="Attendee Name 2", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_3", name="Attendee Name 3", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_4", name="Attendee Name 4", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_5", name="Attendee Name 5", email_address="<EMAIL>"),
            ],
            meeting_urls=[
                pydantic.HttpUrl("https://example.com/video2"),
                pydantic.HttpUrl("https://example.com/more2"),
            ],
        ),
        CalendarEvent(
            provider="google",
            id="event_id_3",
            user_specific_id="event_id_3",
            title="Event Summary 3",
            body=description,
            start_time=datetime(2022, 1, 3, 16, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 3, 18, 0, 0, tzinfo=timezone.utc),
            all_day=False,
            participants=[
                EventParticipant(id="attendee_id_3", name="Attendee Name 3", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_4", name="Attendee Name 4", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_5", name="Attendee Name 5", email_address="<EMAIL>"),
                EventParticipant(id="attendee_id_6", name="Attendee Name 6", email_address="<EMAIL>"),
            ],
            meeting_urls=[
                pydantic.HttpUrl("https://example.com/video3"),
                pydantic.HttpUrl("https://example.com/more3"),
                pydantic.HttpUrl("https://zoom.us/abc-123-def"),
                pydantic.HttpUrl("http://teams.microsoft.com/abc-123-def"),
                pydantic.HttpUrl("http://foo.webex.com/meeting2"),
            ],
        ),
    ]

    result = google.calendar_entries_from_events(events)

    assert result == expected_result


@pytest.mark.parametrize(
    ("event_time", "expected_datetime", "expected_all_day"),
    [
        # Simple dates and datetimes.
        (
            {"date": "2022-01-01"},
            datetime(2022, 1, 1, 0, 0, 0, tzinfo=timezone.utc),
            True,
        ),
        (
            {"dateTime": "2022-01-01T10:00:00"},
            datetime(2022, 1, 1, 10, 0, 0),
            False,
        ),
        (
            {"dateTime": "2022-01-01T10:00:00Z"},
            datetime(2022, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
            False,
        ),
        (
            {"dateTime": "2022-01-01T10:00:00-05:00"},
            datetime(2022, 1, 1, 10, 0, 0, tzinfo=timezone(offset=timedelta(hours=-5))),
            False,
        ),
        # Time zone handling.
        (
            {"date": "2022-01-01", "timeZone": "America/New_York"},
            datetime(2022, 1, 1, 0, 0, 0, tzinfo=timezone.utc),
            True,
        ),
        (
            {"date": "2022-01-01", "timeZone": "America/New_York"},
            datetime(2022, 1, 1, 0, 0, 0, tzinfo=timezone.utc),
            True,
        ),
        (
            {"dateTime": "2022-01-01T10:00:00", "timeZone": "America/New_York"},
            datetime(2022, 1, 1, 10, 0, 0, tzinfo=ZoneInfo("America/New_York")),
            False,
        ),
        (
            {"dateTime": "2022-01-01T10:00:00Z", "timeZone": "America/New_York"},
            datetime(2022, 1, 1, 5, 0, 0, tzinfo=ZoneInfo("America/New_York")),
            False,
        ),
        # datetime overrides date
        (
            {"date": "2022-02-02", "dateTime": "2022-01-01T10:00:00"},
            datetime(2022, 1, 1, 10, 0, 0),
            False,
        ),
    ],
)
@patch("deepinsights.core.integrations.calendar.google._events_service")
def test_datetime_handling(
    mock_events_service: MagicMock, event_time: dict[str, str], expected_datetime: datetime, expected_all_day: bool
) -> None:
    events_result = {
        "items": [
            {
                "id": "event_id",
                "summary": "Event Summary",
                "start": event_time,
                "end": event_time,
            },
        ]
    }

    _set_up_events_service_mock(mock_events_service, [({"status": 200}, json.dumps(events_result))])

    event = google.fetch_calendar_events("token")[0]
    assert event.start_time == expected_datetime
    assert event.end_time == expected_datetime
    assert event.all_day == expected_all_day


@patch("deepinsights.core.integrations.calendar.google._events_service")
def test_calendar_event_invalid_meeting_url_handling(mock_events_service: MagicMock) -> None:
    description = """
      foohttp://teams.microsoft.com/meeting1
      zoom.us/meeting
      http://foo.webex.com/meeting2
    """
    events_result = {
        "items": [
            {
                "id": "event_id",
                "location": "meet.google.com/video",
                "summary": "Event Summary",
                "description": description,
                "start": {"dateTime": "2022-01-01T10:00:00Z"},
                "end": {"dateTime": "2022-01-01T12:00:00Z"},
                "attendees": [],
                "conferenceData": {
                    "entryPoints": [
                        {"entryPointType": "video", "uri": "meet.google.com/video2"},
                        {"entryPointType": "more", "uri": "https://meet.google.com/video3"},
                    ]
                },
            },
        ]
    }

    _set_up_events_service_mock(mock_events_service, [({"status": 200}, json.dumps(events_result))])

    expected_result = [
        CalendarEvent(
            provider="google",
            id="event_id",
            user_specific_id="event_id",
            title="Event Summary",
            body=description,
            start_time=datetime(2022, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 1, 12, 0, 0, tzinfo=timezone.utc),
            all_day=False,
            participants=[],
            meeting_urls=[
                pydantic.HttpUrl("https://meet.google.com/video3"),
                pydantic.HttpUrl("http://foo.webex.com/meeting2"),
            ],
        ),
    ]

    result = google.fetch_calendar_events("token")

    assert result == expected_result


@patch("deepinsights.core.integrations.calendar.google._events_service")
def test_organizer_as_event_participant(mock_events_service: MagicMock) -> None:
    events_result = {
        "items": [
            {
                "id": "event_id",
                "summary": "Event Summary",
                "start": {"dateTime": "2022-01-01T10:00:00Z"},
                "end": {"dateTime": "2022-01-01T12:00:00Z"},
                "organizer": {"id": "organizer_id", "displayName": "Organizer Name", "email": "<EMAIL>"},
            },
        ]
    }
    _set_up_events_service_mock(mock_events_service, [({"status": 200}, json.dumps(events_result))])

    expected_result = [
        CalendarEvent(
            provider="google",
            id="event_id",
            user_specific_id="event_id",
            title="Event Summary",
            body="",
            start_time=datetime(2022, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
            end_time=datetime(2022, 1, 1, 12, 0, 0, tzinfo=timezone.utc),
            all_day=False,
            participants=[
                EventParticipant(id="organizer_id", name="Organizer Name", email_address="<EMAIL>"),
            ],
            meeting_urls=[],
        ),
    ]

    result = google.fetch_calendar_events("token")

    assert result == expected_result
