import io
from unittest import mock

from pytest_django.fixtures import SettingsWrapper

from deepinsights.core.aws import AWS


@mock.patch("deepinsights.core.aws.boto3.Session")
def test_upload_file(mock_session: mock.MagicMock, settings: SettingsWrapper) -> None:
    settings.AWS_MEETING_BUCKET = "test-bucket"
    settings.AWS_S3_REGION_NAME = "us-west-2"
    settings.AWS_ACCESS_KEY_ID = "fake-access-key-id"
    settings.AWS_SECRET_ACCESS_KEY = "fake-secret-access-key"

    # Call the method
    contents = io.BytesIO(b"file content")
    AWS().upload_file("file/path", contents)

    mock_session.assert_called_once_with(
        region_name="us-west-2",
        aws_access_key_id="fake-access-key-id",
        aws_secret_access_key="fake-secret-access-key",
    )
    mock_session.return_value.client.return_value.upload_fileobj.assert_called_once_with(
        contents, "test-bucket", "file/path"
    )


@mock.patch("deepinsights.core.aws.boto3.Session")
def test_get_temp_url(mock_session: mock.MagicMock, settings: SettingsWrapper) -> None:
    settings.AWS_MEETING_BUCKET = "test-bucket"
    settings.AWS_S3_REGION_NAME = "us-west-2"
    settings.AWS_ACCESS_KEY_ID = "fake-access-key-id"
    settings.AWS_SECRET_ACCESS_KEY = "fake-secret-access-key"

    # Mock the generate_presigned_url method
    mock_presigned_url = "https://example.com/presigned-url"
    mock_session.return_value.client.return_value.generate_presigned_url.return_value = mock_presigned_url

    # Call the method
    url = AWS().get_temp_url("file/path")

    mock_session.assert_called_once_with(
        region_name="us-west-2",
        aws_access_key_id="fake-access-key-id",
        aws_secret_access_key="fake-secret-access-key",
    )
    mock_session.return_value.client.return_value.generate_presigned_url.assert_called_once_with(
        ClientMethod="get_object",
        Params={"Bucket": "test-bucket", "Key": "file/path"},
        ExpiresIn=600,
    )
    assert url == mock_presigned_url


@mock.patch("deepinsights.core.aws.boto3.Session")
def test_delete_s3_file(mock_session: mock.MagicMock, settings: SettingsWrapper) -> None:
    settings.AWS_MEETING_BUCKET = "test-bucket"
    settings.AWS_S3_REGION_NAME = "us-west-2"
    settings.AWS_ACCESS_KEY_ID = "fake-access-key-id"
    settings.AWS_SECRET_ACCESS_KEY = "fake-secret-access-key"

    # Call the method
    AWS().delete_s3_file("file/path")

    mock_session.assert_called_once_with(
        region_name="us-west-2",
        aws_access_key_id="fake-access-key-id",
        aws_secret_access_key="fake-secret-access-key",
    )
    mock_session.return_value.client.return_value.delete_object.assert_called_once_with(
        Bucket="test-bucket", Key="file/path"
    )
