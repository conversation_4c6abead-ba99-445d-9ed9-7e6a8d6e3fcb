import logging
from typing import BinaryIO

import boto3
from django.conf import settings


class AWS:
    def create_session(self) -> None:
        self.s3_bucket_name = settings.AWS_MEETING_BUCKET
        self.session = boto3.Session(
            region_name=settings.AWS_S3_REGION_NAME,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        )

    def _storage_client(self):  # type: ignore[no-untyped-def]
        return self.session.client("s3")

    def upload_file(self, file_path: str, content: BinaryIO) -> None:
        self.create_session()
        self._storage_client().upload_fileobj(content, self.s3_bucket_name, file_path)  # type: ignore[no-untyped-call]
        logging.info(f"Successfully uploaded {file_path}.")

    def get_temp_url(self, file_path: str) -> str:
        self.create_session()
        return self._storage_client().generate_presigned_url(  # type: ignore[no-untyped-call, no-any-return]
            ClientMethod="get_object",
            Params={"Bucket": self.s3_bucket_name, "Key": file_path},
            ExpiresIn=600,
        )

    def delete_s3_file(self, file_path: str) -> None:
        self.create_session()
        logging.info(
            self._storage_client().delete_object(Bucket=self.s3_bucket_name, Key=file_path)  # type: ignore[no-untyped-call]
        )
