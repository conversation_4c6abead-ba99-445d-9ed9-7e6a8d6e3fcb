from typing import Self
from uuid import uuid4

from django.db import models
from django_stubs_ext.db.models import TypedModelMeta
from model_utils.models import TimeStampedModel

from deepinsights.core.managers import StatusMixinManager


# A mixin for models that support soft deletion.
class StatusMixin(models.Model):
    # Indicates that this object has been soft deleted. In other words, it should not be shown to
    # users or considered in user-facing queries.
    is_deleted = models.BooleanField(default=False, blank=False, null=False)

    # Overrides the default manager to not show objects that are soft deleted.
    objects = StatusMixinManager[Self]()

    # A manager that shows all objects, even soft-deleted objects.
    objects_with_deleted = models.Manager[Self]()

    class Meta(TypedModelMeta):
        abstract = True


class UUIDMixin(TimeStampedModel):
    uuid = models.UUIDField(default=uuid4, editable=False, unique=True)

    class Meta(TypedModelMeta):
        abstract = True
