import logging
from typing import Any, Literal, Union

from django.conf import settings
from django.core.mail import EmailMessage, EmailMultiAlternatives
from pydantic import BaseModel


class EmailAttachment(BaseModel):
    content: Union[str, bytes]
    filename: str
    type: Literal["text", "binary"]


class EmailResponse(BaseModel):
    message_id: str
    status: str
    mails_sent: int


class ZeplynEmailService:
    """Service for handling email operations using Django's built-in email functionality."""

    def send_email(
        self,
        *,
        subject: str,
        body: str,
        recipients: str | list[str],
        sender: str | None = None,
        reply_to: str | None = None,
        cc: str | list[str] | None = None,
        bcc: str | list[str] | None = None,
        attachments: list[dict[str, Any]] | None = None,
        is_html: bool = False,
    ) -> dict[str, Any]:
        """
        Send emails through Django's email backend with support for multiple recipients,
        attachments and HTML content.

        Args:
            subject: Email subject line
            body: Email body content
            recipients: Single recipient email or list of recipient emails
            sender: Sender email address (defaults to settings.DEFAULT_FROM_EMAIL)
            reply_to: Reply-to email address
            cc: Single CC email or list of CC emails
            bcc: Single BCC email or list of BCC emails
            attachments: List of attachment dictionaries with structure:
                [
                    {
                        'content': str | bytes - The content of the attachment
                        'filename': str - The filename for the attachment
                        'type': str - Type of attachment ('text' or 'binary')
                    }
                ]
            is_html: Whether the body content is HTML

        Returns:
            Dict containing message ID and status

        Raises:
            Exception: If email sending fails
        """
        try:
            recipient_list = [recipients] if isinstance(recipients, str) else recipients
            cc_list = [] if cc is None else ([cc] if isinstance(cc, str) else cc)
            bcc_list = [] if bcc is None else ([bcc] if isinstance(bcc, str) else bcc)
            from_email = sender or settings.DEFAULT_FROM_EMAIL
            reply_to_list = [reply_to] if reply_to else None

            email: EmailMessage | EmailMultiAlternatives
            if is_html:
                email = EmailMultiAlternatives(
                    subject=subject,
                    body="",
                    from_email=from_email,
                    to=recipient_list,
                    cc=cc_list,
                    bcc=bcc_list,
                    reply_to=reply_to_list,
                )
                email.attach_alternative(body, "text/html")
            else:
                email = EmailMessage(
                    subject=subject,
                    body=body,
                    from_email=from_email,
                    to=recipient_list,
                    cc=cc_list,
                    bcc=bcc_list,
                    reply_to=reply_to_list,
                )

            if attachments:
                for attachment in attachments:
                    content = attachment["content"]
                    filename = attachment["filename"]

                    if attachment["type"] == "text" and isinstance(content, str):
                        content = content.encode("utf-8")

                    email.attach(filename=filename, content=content)

            num_sent = email.send()

            logging.info(
                "Email sent successfully. Subject: %s, Recipients: %s, CC: %s",
                subject,
                recipient_list,
                cc_list,
            )

            return EmailResponse(
                message_id=f"django-email-{id(email)}", status="Sent" if num_sent > 0 else "Failed", mails_sent=num_sent
            ).model_dump()

        except Exception as e:
            error_msg = f"Failed to send email. Subject: {subject}, Recipients: {recipients}, Error: {str(e)}"
            logging.error(error_msg, exc_info=True)
            raise Exception(error_msg) from e
