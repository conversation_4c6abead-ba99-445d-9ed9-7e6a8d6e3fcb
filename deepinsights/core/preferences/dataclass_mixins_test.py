from dataclasses import dataclass, field
from typing import Any

from deepinsights.core.preferences.dataclass_mixins import ZeplynDataclassJsonMixin


@dataclass
class TestChild(ZeplynDataclassJsonMixin):
    name: str = field(default="")
    value: int = field(default=0)
    mapping: dict[Any, Any] = field(default_factory=dict)
    listing: list[Any] = field(default_factory=list)


@dataclass
class TestParent(ZeplynDataclassJsonMixin):
    child: TestChild = field(default_factory=TestChild)
    data: str = field(default="")


class TestDataclassMixins:
    def test_from_dict_basic(self) -> None:
        data = {"name": "test", "value": 42, "mapping": {"key": "value"}, "listing": [1, 2, 3]}
        obj = TestChild.from_dict(data)
        assert obj.name == "test"
        assert obj.value == 42
        assert obj.mapping == {"key": "value"}
        assert obj.listing == [1, 2, 3]

    def test_from_dict_nested(self) -> None:
        data = {"child": {"name": "child", "value": 1, "mapping": {"a": 1}, "listing": ["x"]}, "data": "parent"}
        obj = TestParent.from_dict(data)
        assert isinstance(obj.child, TestChild)
        assert obj.child == TestChild(name="child", value=1, mapping={"a": 1}, listing=["x"])
        assert obj.data == "parent"

    def test_update_dict(self) -> None:
        obj = TestChild(name="original", value=0)
        obj.update({"name": "updated", "value": 99})
        assert obj.name == "updated"
        assert obj.value == 99

    def test_update_nested_dict(self) -> None:
        child = TestChild(name="child", value=1)
        parent = TestParent(child=child, data="original")
        parent.update(
            {
                "child": {"name": "updatedchild", "value": 2, "mapping": {"new": "map"}, "listing": [4, 5, 6]},
                "data": "updatedparent",
            }
        )
        assert parent.data == "updatedparent"
        assert parent.child.name == "updatedchild"
        assert parent.child.value == 2
        assert parent.child.mapping == {"new": "map"}
        assert parent.child.listing == [4, 5, 6]

    def test_update_object(self) -> None:
        obj1 = TestChild(name="original", value=0)
        obj2 = TestChild(name="new", value=42)
        obj1.update(obj2)
        assert obj1.name == "new"
        assert obj1.value == 42

    def test_update_nested_object(self) -> None:
        child1 = TestChild(name="child1", value=1)
        child2 = TestChild(name="child2", value=2)
        parent1 = TestParent(child=child1, data="parent1")
        parent2 = TestParent(child=child2, data="parent2")
        parent1.update(parent2)
        assert parent1.data == "parent2"
        assert parent1.child.name == "child2"
        assert parent1.child.value == 2

    def test_none_values_are_updated(self) -> None:
        obj = TestChild(name="original", value=42)
        obj.update({"name": None, "value": None})
        assert obj.name is None
        assert obj.value is None

    def test_false_values_are_updated(self) -> None:
        obj = TestChild(name="original", value=42)
        obj.update({"name": "", "value": 0})
        assert obj.name == ""
        assert obj.value == 0

    def test_to_dict_simple(self) -> None:
        obj = TestChild(name="test", value=42, mapping={"x": 1}, listing=[1])
        assert obj.to_dict() == {"name": "test", "value": 42, "mapping": {"x": 1}, "listing": [1]}

    def test_to_dict_nested(self) -> None:
        obj = TestParent(child=TestChild(name="child", value=1, mapping={"key": "val"}, listing=[1]), data="parent")
        assert obj.to_dict() == {
            "child": {"name": "child", "value": 1, "mapping": {"key": "val"}, "listing": [1]},
            "data": "parent",
        }

    def test_to_dict_exclude_empty(self) -> None:
        obj = TestParent(child=TestChild(name="", value=0), data="")
        assert obj.to_dict() == {}

    def test_to_dict_partial(self) -> None:
        obj = TestParent(child=TestChild(name="", value=1, mapping={"x": 1}, listing=[]), data="parent")
        assert obj.to_dict() == {"child": {"value": 1, "mapping": {"x": 1}}, "data": "parent"}

    def test_to_dict_nested_partial(self) -> None:
        obj = TestParent(child=TestChild(name="", value=1), data="")
        assert obj.to_dict() == {"child": {"value": 1}}

    def test_schema_caching(self) -> None:
        child1 = TestChild(name="child1", value=1)
        child2 = TestChild(name="child2", value=2)
        parent1 = TestParent(child=child1, data="parent1")
        parent2 = TestParent(child=child2, data="parent2")
        parent1.update(parent2)
        assert parent1.data == "parent2"
        assert parent1.child.name == "child2"
        assert parent1.child.value == 2
        assert child1.name == "child2"
        assert child1.value == 2

        child3 = TestChild(name="child3", value=3)
        child4 = TestChild(name="child4", value=4)
        parent3 = TestParent(child3, data="parent3")
        parent4 = TestParent(child4, data="parent4")
        parent4.update(parent3)
        assert parent4.data == "parent3"
        assert parent4.child.name == "child3"
        assert parent4.child.value == 3
