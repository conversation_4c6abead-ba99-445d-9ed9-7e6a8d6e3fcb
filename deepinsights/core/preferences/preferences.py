from dataclasses import dataclass, field
from enum import Str<PERSON>num

from dataclasses_json.core import <PERSON><PERSON>

from deepinsights.core.integrations.meetingbot import recall_b64data
from deepinsights.core.preferences.dataclass_mixins import ZeplynDataclassJsonMixin


class PreferencesSchemaPopulators(StrEnum):
    """Enum that defines the types of populators that can be used to populate preferences schemas at runtime.

    This is a workaround to allow dynamic population of certain fields in the preferences schema.
    There are certain preferences fields that require values based on the user's context, but we
    don't have that context statically when defining the schema. The populators are a contract between
    the schema definition and the code that generates the schema for display in the UI.
    """

    # Populate the schema with a list of meeting types that the user has access to.
    MEETING_TYPES = "meeting_types"


@dataclass
class SalesForceConfiguration(ZeplynDataclassJsonMixin):
    type: str = field(default="")
    salesforce_username: str = field(default="")
    salesforce_password: str = field(default="")
    salesforce_consumer_key: str = field(default="")
    salesforce_consumer_secret: str = field(default="")
    salesforce_security_token: str = field(default="")
    salesforce_endpoint: str = field(default="")


@dataclass
class RedtailConfiguration(ZeplynDataclassJsonMixin):
    user_key: str = field(default="")


@dataclass
class SharePointConfiguration(ZeplynDataclassJsonMixin):
    client_id: str = field(default="")
    client_secret: str = field(default="")
    site_url: str = field(default="")
    parent_folder: str = field(default="")


@dataclass
class DynamicsConfiguration(ZeplynDataclassJsonMixin):
    type: str = field(default="")
    dynamics_resource_url: str = field(default="")


@dataclass
class SyncPreferences(ZeplynDataclassJsonMixin):
    advisor_certification_required: bool = field(
        default=False,
        metadata={
            "title": "Advisor Certification Required for CRM sync",
            "description": "Whether or not to require advisor certification when syncing interactions to CRM.",
        },
    )
    advisor_certification_synced_text: str = field(
        default="Reviewed and certified by {user_email}, at {timestamp_with_tz}",
        metadata={
            "title": "Certification sync text",
            "description": (
                "The text to display in the CRM record when an advisor syncs an interaction that requires "
                "certification. You can use '{user_email}' and '{timestamp_with_tz}' (exactly as shown, "
                "without quotes) to insert the user's email and the timestamp of the sync."
            ),
        },
    )
    advisor_certification_display_text: str = field(
        default="I certify that the summary of notes has been reviewed and verified, and is accurate to the best of my knowledge.",
        metadata={
            "title": "Certification display text",
            "description": "The text to display in the Zeplyn app before syncing to CRM when requesting certification.",
        },
    )


class DefaultClientFilter(StrEnum):
    ALL = "all"
    OWNED = "owned"

    @classmethod
    def _missing_(cls, _: object) -> "DefaultClientFilter":
        return cls.ALL


@dataclass
class CrmConfiguration(ZeplynDataclassJsonMixin):
    crm_system: str = field(default="")
    default_client_filter: DefaultClientFilter = field(default=DefaultClientFilter.ALL)
    salesforce: SalesForceConfiguration = field(default_factory=SalesForceConfiguration)
    redtail: RedtailConfiguration = field(default_factory=RedtailConfiguration)
    sharepoint: SharePointConfiguration = field(default_factory=SharePointConfiguration)
    dynamics: DynamicsConfiguration = field(default_factory=DynamicsConfiguration)
    summary_template: str = field(default="")


@dataclass
class EmailConfiguration(ZeplynDataclassJsonMixin):
    ccs: list[str] = field(
        default_factory=list,
        metadata={
            "title": "CCed email addresses",
            "description": "Email addresses to CC on follow-up emails",
            "json_schema_extra": {"items": {"type": "string", "format": "email"}},
        },
    )
    bccs: list[str] = field(
        default_factory=list,
        metadata={
            "title": "BCCed email addresses",
            "description": "Email addresses to BCC on follow-up emails",
            "json_schema_extra": {"items": {"type": "string", "format": "email"}},
        },
    )
    followup_email_format_prompt: str = field(
        default="", metadata={"title": "Followup Email Format Prompt", "ui_shown": False}
    )
    meeting_notes_email_template: str = field(
        default="", metadata={"title": "Meeting Notes Email Template", "ui_shown": False}
    )


@dataclass
class BotPreferences(ZeplynDataclassJsonMixin):
    not_recording_image_b64: str = field(
        default="",
        metadata={
            "title": "Notetaker image: not recording",
            "description": "The image to display in notetaker meetings when the bot is not recording.",
            # This is not a standard JSON schema format, but it's used for the frontend to display
            # the image. It would be more correc to use contentencoding and contentmediatype, but they
            # are not supported by the current version of the code that renders this on the frontend.
            "json_schema_extra": {"format": "base64jpeg"},
        },
    )
    recording_image_b64: str = field(
        default="",
        metadata={
            "title": "Notetaker image: recording",
            "description": "The image to display in notetaker meetings when the bot is recording.",
            # This is not a standard JSON schema format, but it's used for the frontend to display
            # the image. It would be more correc to use contentencoding and contentmediatype, but they
            # are not supported by the current version of the code that renders this on the frontend.
            "json_schema_extra": {"format": "base64jpeg"},
        },
    )
    recording_message_b64: str = field(
        default="",
        metadata={
            "title": "Notetaker message: recording",
            "description": "The message to display in notetaker meetings when the bot is recording.",
            "ui_shown": False,
        },
    )
    notetaker_name: str = field(
        default="",
        metadata={
            "title": "Notetaker name",
            "description": "The name of the notetaker to display in notetaker meetings.",
        },
    )
    enable_video: bool = field(
        default=False,
        metadata={
            "title": "Show Notetaker background image",
            "description": "Whether or not to show the background image for the notetaker in meetings.",
        },
    )
    enable_audio_output: bool = field(
        default=False,
        metadata={
            "title": "Play notetaking audio disclaimer",
            "description": "Whether or not the notetaker should speak recording indicator disclaimer messages in video calls.",
        },
    )
    enable_recording_free_zoom: bool = field(
        default=False,
        metadata={
            "title": "Enable recording free zoom",
            "description": "Whether or not to enable recording-free Zoom.",
            "ui_shown": False,
        },
    )

    def recording_image_b64_or_default(self) -> str:
        """Return the recording image in base64 format or a default image if one is not set."""
        return self.recording_image_b64 or recall_b64data.recording_default_jpeg

    def not_recording_image_b64_or_default(self) -> str:
        """Return the not recording image in base64 format or a default image if one is not set."""
        return self.not_recording_image_b64 or recall_b64data.not_recording_default_jpeg

    def recording_message_b64_or_default(self) -> str:
        """Return the recording message in base64 format or a default message if one is not set."""
        return self.recording_message_b64 or recall_b64data.start_audio


@dataclass
class NotificationPreferences(ZeplynDataclassJsonMixin):
    meeting_processed_notification_enabled: bool = field(
        default=False,
        metadata={
            "title": "Meeting Processed Notification Enabled",
            "description": "Whether or not to send you an email notification when a meeting is has been fully processed.",
        },
    )
    email_meeting_notes_post_sync: bool = field(
        default=False,
        metadata={
            "title": "Email Meeting Notes Post Sync",
            "description": "Whether or not to email meeting notes to you after a meeting is processed",
        },
    )
    mid_meeting_nudge_minutes_before_end: int = field(default=10, metadata={"ui_shown": False})


@dataclass
class CalendarPreferences(ZeplynDataclassJsonMixin):
    auto_join_ignored_keywords: list[str] = field(
        default_factory=list,
        metadata={
            "title": "Auto-ignore meetings with these keywords",
            "description": "Meetings containing these keywords in the title will be automatically ignored.",
        },
    )


_deepgram_languages = [
    {"const": "bg", "title": "Bulgarian"},
    {"const": "ca", "title": "Catalan"},
    {"const": "cs", "title": "Czech"},
    {"const": "da", "title": "Danish"},
    {"const": "da-DK", "title": "Danish (Denmark)"},
    {"const": "de", "title": "German"},
    {"const": "de-CH", "title": "German (Switzerland)"},
    {"const": "el", "title": "Greek"},
    {"const": "en", "title": "English"},
    {"const": "en-AU", "title": "English (Australia)"},
    {"const": "en-GB", "title": "English (United Kingdom)"},
    {"const": "en-IN", "title": "English (India)"},
    {"const": "en-NZ", "title": "English (New Zealand)"},
    {"const": "en-US", "title": "English (United States)"},
    {"const": "es", "title": "Spanish"},
    {"const": "es-419", "title": "Spanish (Latin America)"},
    {"const": "et", "title": "Estonian"},
    {"const": "fi", "title": "Finnish"},
    {"const": "fr", "title": "French"},
    {"const": "fr-CA", "title": "French (Canada)"},
    {"const": "hi", "title": "Hindi"},
    {"const": "hu", "title": "Hungarian"},
    {"const": "id", "title": "Indonesian"},
    {"const": "it", "title": "Italian"},
    {"const": "ja", "title": "Japanese"},
    {"const": "ko", "title": "Korean"},
    {"const": "ko-KR", "title": "Korean (South Korea)"},
    {"const": "lt", "title": "Lithuanian"},
    {"const": "lv", "title": "Latvian"},
    {"const": "ms", "title": "Malay"},
    {"const": "nl", "title": "Dutch"},
    {"const": "nl-BE", "title": "Dutch (Belgium)"},
    {"const": "no", "title": "Norwegian"},
    {"const": "pl", "title": "Polish"},
    {"const": "pt", "title": "Portuguese"},
    {"const": "pt-BR", "title": "Portuguese (Brazil)"},
    {"const": "pt-PT", "title": "Portuguese (Portugal)"},
    {"const": "ro", "title": "Romanian"},
    {"const": "ru", "title": "Russian"},
    {"const": "sk", "title": "Slovak"},
    {"const": "sv", "title": "Swedish"},
    {"const": "sv-SE", "title": "Swedish (Sweden)"},
    {"const": "th", "title": "Thai"},
    {"const": "th-TH", "title": "Thai (Thailand)"},
    {"const": "tr", "title": "Turkish"},
    {"const": "uk", "title": "Ukrainian"},
    {"const": "vi", "title": "Vietnamese"},
    {"const": "zh-CN", "title": "Chinese (Simplified)"},
    {"const": "zh-HK", "title": "Chinese (Hong Kong)"},
    {"const": "zh-TW", "title": "Chinese (Taiwan)"},
]


@dataclass
class MeetingPrepPreferences(ZeplynDataclassJsonMixin):
    days_before_meeting_to_generate: int = field(default=7, metadata={"ui_shown": False})
    days_before_meeting_to_email: int = field(default=7, metadata={"ui_shown": False})


@dataclass
class Preferences(ZeplynDataclassJsonMixin):
    # Nested fields
    email_settings: EmailConfiguration = field(default_factory=EmailConfiguration)
    bot_preferences: BotPreferences = field(default_factory=BotPreferences)
    sync_preferences: SyncPreferences = field(default_factory=SyncPreferences)
    notification_preferences: NotificationPreferences = field(default_factory=NotificationPreferences)
    calendar_preferences: CalendarPreferences = field(default_factory=CalendarPreferences)
    meeting_prep_preferences: MeetingPrepPreferences = field(default_factory=MeetingPrepPreferences)

    # Primitive fields
    attach_transcript_to_follow_up_emails: bool = field(
        default=False,
        metadata={
            "title": "Attach Transcript to Follow-Up Emails",
            "description": "Whether or not to attach the transcript to follow-up emails when you send them.",
            "ui_shown": False,
        },
    )
    show_transcript_in_frontend: bool = field(default=False, metadata={"ui_shown": False})
    send_task_reminder_email: bool = field(
        default=False,
        metadata={
            "title": "Send a daily email with your tasks",
            "description": "Whether or not to send you a daily email summarizing your recently-assigned and soon-to-be-due tasks.",
        },
    )
    delete_buffer: bool = field(default=False, metadata={"ui_shown": False})
    due_date_offset_seconds: int = field(default=0, metadata={"ui_shown": False})
    asr_language_code: str = field(
        default="",
        metadata={
            "title": "Language for speech recognition",
            "description": "The language to use for speech recognition. This is used for transcribing your meetings.",
            "json_schema_extra": {"oneOf": sorted(_deepgram_languages, key=lambda x: x["title"])},
        },
    )
    default_meeting_type: str = field(
        default="",
        metadata={
            "title": "Default Meeting Type",
            "description": "The default meeting type to use when creating new meetings.",
            "json_schema_extra": {"x-populator": PreferencesSchemaPopulators.MEETING_TYPES},
        },
    )


def get_default_crm_configuration() -> dict[str, Json]:
    return CrmConfiguration().to_dict()


def get_default_preferences() -> dict[str, Json]:
    p = Preferences()
    p.delete_buffer = True
    p.asr_language_code = "en"
    p.bot_preferences.enable_video = True
    p.bot_preferences.enable_audio_output = True
    p.show_transcript_in_frontend = True
    p.notification_preferences.mid_meeting_nudge_minutes_before_end = 10
    return p.to_dict()
