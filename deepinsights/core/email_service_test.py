from unittest import mock

import pytest
from django.core.mail.message import EmailMessage, EmailMultiAlternatives
from pytest_django.fixtures import SettingsWrapper

from deepinsights.core.email_service import ZeplynEmailService


def test_send_email_basic(settings: SettingsWrapper, mailoutbox: list[EmailMessage]) -> None:
    settings.DEFAULT_FROM_EMAIL = "<EMAIL>"

    result = ZeplynEmailService().send_email(
        subject="Test Subject", body="Test Body", recipients="<EMAIL>"
    )

    assert len(mailoutbox) == 1
    mail = mailoutbox[0]
    assert mail.subject == "Test Subject"
    assert mail.body == "Test Body"
    assert mail.from_email == "<EMAIL>"
    assert mail.to == ["<EMAIL>"]
    assert mail.cc == []
    assert mail.bcc == []
    assert mail.reply_to == []

    assert result["message_id"].startswith("django-email-")
    assert result["status"] == "Sent"
    assert result["mails_sent"] == 1


def test_send_email_with_html(
    settings: SettingsWrapper, mailoutbox: list[EmailMessage | EmailMultiAlternatives]
) -> None:
    settings.DEFAULT_FROM_EMAIL = "<EMAIL>"
    html_body = "<html><body><h1>Test HTML Email</h1><p>This is a test.</p></body></html>"

    result = ZeplynEmailService().send_email(
        subject="HTML Email", body=html_body, recipients="<EMAIL>", is_html=True
    )
    assert len(mailoutbox) == 1
    mail = mailoutbox[0]
    assert mail.subject == "HTML Email"
    assert mail.from_email == "<EMAIL>"
    assert mail.to == ["<EMAIL>"]

    if isinstance(mail, EmailMultiAlternatives):
        assert mail.body == ""
        assert len(mail.alternatives) == 1
        content, mimetype = mail.alternatives[0]
        assert content == html_body
        assert mimetype == "text/html"
    else:
        assert mail.body == html_body


def test_send_email_with_text_attachment(settings: SettingsWrapper, mailoutbox: list[EmailMessage]) -> None:
    settings.DEFAULT_FROM_EMAIL = "<EMAIL>"

    attachment_content = "This is attachment content"
    attachments = [{"content": attachment_content, "filename": "test.txt", "type": "text"}]

    result = ZeplynEmailService().send_email(
        subject="Email with Attachment",
        body="Please see attachment",
        recipients="<EMAIL>",
        attachments=attachments,
    )

    assert len(mailoutbox) == 1
    mail = mailoutbox[0]
    assert mail.subject == "Email with Attachment"
    assert mail.body == "Please see attachment"
    assert mail.to == ["<EMAIL>"]

    assert len(mail.attachments) == 1
    filename, content, mimetype = mail.attachments[0]
    assert filename == "test.txt"
    assert content == "This is attachment content"

    assert result["status"] == "Sent"


def test_send_email_with_binary_attachment(settings: SettingsWrapper, mailoutbox: list[EmailMessage]) -> None:
    settings.DEFAULT_FROM_EMAIL = "<EMAIL>"

    attachment_content = b"Binary content \x01\x02\x03"
    attachments = [{"content": attachment_content, "filename": "file.bin", "type": "binary"}]

    result = ZeplynEmailService().send_email(
        subject="Email with Binary Attachment",
        body="Please see binary attachment",
        recipients="<EMAIL>",
        attachments=attachments,
    )

    assert len(mailoutbox) == 1
    mail = mailoutbox[0]
    assert mail.subject == "Email with Binary Attachment"
    assert mail.to == ["<EMAIL>"]

    assert len(mail.attachments) == 1
    filename, content, mimetype = mail.attachments[0]
    assert filename == "file.bin"
    assert content == attachment_content

    assert result["status"] == "Sent"


def test_send_email_with_custom_sender_and_reply_to(mailoutbox: list[EmailMessage]) -> None:
    custom_sender = "<EMAIL>"
    reply_to = "<EMAIL>"

    result = ZeplynEmailService().send_email(
        subject="Custom Sender Email",
        body="This email has a custom sender",
        recipients="<EMAIL>",
        sender=custom_sender,
        reply_to=reply_to,
    )

    assert len(mailoutbox) == 1
    mail = mailoutbox[0]
    assert mail.subject == "Custom Sender Email"
    assert mail.body == "This email has a custom sender"
    assert mail.from_email == custom_sender
    assert mail.to == ["<EMAIL>"]
    assert mail.reply_to == [reply_to]

    assert result["status"] == "Sent"


@mock.patch("deepinsights.core.email_service.EmailMessage")
def test_send_email_failed(mock_email_message: mock.MagicMock, settings: SettingsWrapper) -> None:
    settings.DEFAULT_FROM_EMAIL = "<EMAIL>"
    mock_instance = mock_email_message.return_value
    mock_instance.send.return_value = 0

    result = ZeplynEmailService().send_email(
        subject="Test Subject", body="Test Body", recipients="<EMAIL>"
    )

    assert result["status"] == "Failed"
    assert result["mails_sent"] == 0


@mock.patch("deepinsights.core.email_service.EmailMessage")
def test_send_email_error_handling(mock_email_message: mock.MagicMock) -> None:
    mock_instance = mock_email_message.return_value
    mock_instance.send.side_effect = Exception("Email sending failed")

    with pytest.raises(Exception) as excinfo:
        ZeplynEmailService().send_email(
            subject="Error Test", body="This should fail", recipients="<EMAIL>"
        )

    assert "Failed to send email" in str(excinfo.value)
    assert "Error Test" in str(excinfo.value)
    assert "<EMAIL>" in str(excinfo.value)


def test_send_email_cc_bcc_as_string(settings: SettingsWrapper, mailoutbox: list[EmailMessage]) -> None:
    settings.DEFAULT_FROM_EMAIL = "<EMAIL>"

    result = ZeplynEmailService().send_email(
        subject="Test Subject",
        body="Test Body",
        recipients="<EMAIL>",
        cc="<EMAIL>",
        bcc="<EMAIL>",
    )

    assert len(mailoutbox) == 1
    mail = mailoutbox[0]
    assert mail.subject == "Test Subject"
    assert mail.body == "Test Body"
    assert mail.from_email == "<EMAIL>"
    assert mail.to == ["<EMAIL>"]
    assert mail.cc == ["<EMAIL>"]
    assert mail.bcc == ["<EMAIL>"]

    assert result["status"] == "Sent"
    assert result["mails_sent"] == 1
