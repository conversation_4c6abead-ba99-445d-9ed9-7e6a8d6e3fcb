{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
{{ block.super }}
<link rel="stylesheet" href="{% static 'admin/css/forms.css' %}">
<style>
    .wizard-steps {
        margin-bottom: 20px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 4px;
    }

    .wizard-step {
        display: inline-block;
        margin-right: 20px;
        padding: 8px 16px;
        border-radius: 4px;
    }

    .wizard-step.active {
        background: #79aec8;
        color: white;
    }

    .wizard-step.completed {
        background: #417690;
        color: white;
    }

    .form-section {
        background: white;
        padding: 20px;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
    }

    .wizard-navigation {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }

    .error-message {
        color: #ba2121;
        padding: 10px;
        margin-bottom: 20px;
        border: 1px solid #ba2121;
        border-radius: 4px;
        background-color: #fff8f8;
    }

    /* User selection specific styles */
    .user-selection-list {
        margin-top: 15px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 4px;
    }

    .user-selection-list ul {
        list-style: none;
        padding: 0;
        margin: 10px 0;
    }

    .user-selection-list li {
        padding: 8px 12px;
        margin: 4px 0;
        background: white;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .user-selection-controls {
        margin-bottom: 15px;
    }

    .user-selection-controls button {
        margin-right: 10px;
        background: #417690;
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
        cursor: pointer;
    }

    .user-selection-controls button:hover {
        background: #79aec8;
    }

    .user-selection-header {
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }

    .no-users-message {
        background: #fff8f8;
        padding: 15px;
        border-radius: 4px;
        margin-top: 10px;
    }

    .no-users-message ul {
        margin-left: 20px;
        margin-top: 10px;
    }

    /* Checkbox list styling */
    .checkbox-list li {
        display: flex;
        align-items: center;
    }

    .checkbox-list input[type="checkbox"] {
        margin-right: 10px;
    }

    .checkbox-list label {
        flex: 1;
        margin: 0;
        cursor: pointer;
    }
</style>
{% endblock %}

{% block content %}
<div id="content-main">
    <div class="wizard-steps">
        {% for step_name, is_current in wizard_steps %}
        <div
            class="wizard-step {% if is_current %}active{% endif %} {% if not is_current and forloop.counter < wizard.steps.step1 %}completed{% endif %}">
            <span class="step-number">{{ forloop.counter }}</span> {{ step_name }}
        </div>
        {% endfor %}
    </div>

    {% if form.errors %}
    <div class="error-message">
        Please correct the errors below.
        {{ form.errors }}
    </div>
    {% endif %}

    {% if messages %}
    <ul class="messagelist">
        {% for message in messages %}
        <li{% if message.tags %} class="{{ message.tags }}" {% endif %}>{{ message }}</li>
            {% endfor %}
    </ul>
    {% endif %}

    <form method="post" enctype="multipart/form-data" class="form-section">
        {% csrf_token %}
        {{ wizard.management_form }}

        {% if wizard.steps.current == 'select_users' %}
        <div class="user-selection-header">
            <h3>Select Users to Send Welcome Emails</h3>
            {% if organization_name %}
            <p>Organization: {{ organization_name }}</p>
            {% endif %}
        </div>

        <div class="user-selection-list">
            {% with users=wizard.form.fields.users.queryset %}
            {% if users.exists %}
            <div class="user-selection-controls">
                <button type="button" onclick="selectAll()">Select All</button>
                <button type="button" onclick="deselectAll()">Deselect All</button>
            </div>
            <div class="checkbox-list">
                {{ wizard.form.as_p }}
            </div>
            {% else %}
            <div class="no-users-message">
                <strong>No new users found. This could mean:</strong>
                <ul>
                    <li>No users were successfully created in the previous step</li>
                    <li>All users already have passwords set</li>
                    <li>The users were created more than 1 hour ago</li>
                </ul>
                <p>Please go back to the previous step if you need to import users.</p>
            </div>
            {% endif %}
            {% endwith %}
        </div>

        <script>
            function selectAll() {
                document.querySelectorAll('input[name="select_users-users"]').forEach(cb => cb.checked = true);
            }

            function deselectAll() {
                document.querySelectorAll('input[name="select_users-users"]').forEach(cb => cb.checked = false);
            }
        </script>
        {% else %}
        {% if wizard.form.forms %}
        {{ wizard.form.management_form }}
        {% for form in wizard.form.forms %}
        {{ form.as_p }}
        {% endfor %}
        {% else %}
        {{ wizard.form.as_p }}
        {% endif %}
        {% endif %}

        <div class="wizard-navigation">
            {% if wizard.steps.prev %}
            <button name="wizard_goto_step" type="submit" value="{{ wizard.steps.prev }}" class="button">
                ← Previous Step
            </button>
            {% endif %}

            <button type="submit" class="default" style="float: right;">
                {% if wizard.steps.current == wizard.steps.last %}
                Finish
                {% else %}
                Next Step →
                {% endif %}
            </button>
        </div>
    </form>
</div>
{% endblock %}
