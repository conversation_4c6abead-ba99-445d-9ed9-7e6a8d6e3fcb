{% extends "admin/base_site.html" %}
{% load i18n admin_urls %}

{% block content %}
<form action="{% url opts|admin_urlname:'changelist' %}" method="post" enctype="multipart/form-data">
    {% csrf_token %}
    <input type="hidden" name="action" value="{{ action }}" />
    {% for obj in objects %}
    <input type="hidden" name="_selected_action" value="{{ obj.pk }}" />
    {% endfor %}
    {{ form.as_p }}
    <input type="submit" name="apply" value="Update Bot Preferences" />
</form>
{% endblock %}
