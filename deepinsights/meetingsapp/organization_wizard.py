import logging
import os

from django import forms
from django.conf import settings
from django.contrib import messages
from django.core.files.storage import FileSystemStorage
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.utils import timezone
from formtools.wizard.views import SessionWizardView

from deepinsights.core.admin_mixins import BotPreferencesHandler, UpdateBotPreferencesForm
from deepinsights.meetingsapp.admin_utils import process_users_from_csv
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.admin_utils import handle_bulk_user_onboarding

logger = logging.getLogger(__name__)


class OrganizationForm(forms.ModelForm):  # type: ignore[type-arg]
    """ModelForm for Organization creation and editing with unique name validation."""

    class Meta:
        model = Organization
        fields = ["name", "description", "transcript_ttl"]

    def clean_name(self):  # type: ignore[no-untyped-def]
        """Validate organization name uniqueness.

        Returns:
            str: Cleaned organization name if unique
        Raises:
            ValidationError: If organization with name already exists
        """

        name = self.cleaned_data["name"]
        logger.debug("Validating organization name: %s", name)
        if Organization.objects.filter(name=name).exists():
            logger.error("Organization with name %s already exists", name)
            raise forms.ValidationError("An organization with this name already exists.")
        return name


class UserImportForm(forms.Form):
    """Form for importing users via CSV file."""

    csv_file = forms.FileField(
        label="CSV File",
        required=False,
        help_text="Upload a CSV file with user details. Must include columns: Email, Name, First Name, Last Name, License Type.",
    )

    def clean_csv_file(self):  # type: ignore[no-untyped-def]
        """Validate uploaded file is a CSV.

        Returns:
            File: Validated CSV file
        Raises:
            ValidationError: If file missing or not CSV format
        """
        csv_file = self.cleaned_data["csv_file"]
        if not csv_file:
            raise forms.ValidationError("No file was uploaded.")
        if not csv_file.name.endswith(".csv"):
            raise forms.ValidationError("File must be a CSV document.")
        return csv_file


class UserSelectionForm(forms.Form):
    """Form for selecting users to send welcome emails."""

    users = forms.ModelMultipleChoiceField(  # type: ignore[var-annotated]
        queryset=None,
        widget=forms.CheckboxSelectMultiple,
        required=False,
        help_text="Select the users you want to send welcome emails to.",
        label="New Users",
    )

    def __init__(self, organization=None, wizard=None, *args, **kwargs):  # type: ignore[no-untyped-def]
        """Initialize form with filtered user queryset.

        Args:
            organization: Organization to filter users for
            wizard: FormWizard instance for additional filtering
        """
        self.wizard = wizard
        super().__init__(*args, **kwargs)
        if organization:
            base_queryset = organization.users.filter(password="")

            if self.wizard and hasattr(self.wizard.storage, "extra_data"):
                import_timestamp = self.wizard.storage.extra_data.get("import_timestamp")
                if import_timestamp:
                    try:
                        import_time = timezone.datetime.fromisoformat(import_timestamp)  # type: ignore[attr-defined]
                        base_queryset = base_queryset.filter(date_joined__gte=import_time)
                    except Exception as e:
                        logger.error("Error parsing import timestamp", exc_info=e)

            self.fields["users"].queryset = base_queryset.order_by("-date_joined")  # type: ignore[attr-defined]

            self.fields["users"].label_from_instance = self.label_from_instance  # type: ignore[attr-defined]

            # Add count to help text
            user_count = self.fields["users"].queryset.count()  # type: ignore[attr-defined]
            self.fields[
                "users"
            ].help_text = f"{user_count} new user(s) found. Select the users you want to send welcome emails to."

    def label_from_instance(self, user):  # type: ignore[no-untyped-def]
        """Custom label format for each user checkbox."""
        return f"{user.name} ({user.email}) - {user.role}"


class OrganizationOnboardingWizard(SessionWizardView):  # type: ignore[misc]
    """A multi-step form wizard for onboarding new organizations and their users.

    This wizard guides administrators through the process of:
    1. Creating a new organization
    2. Importing users via CSV
    3. Selecting users to generate passwords and receive welcome emails
    4. Configuring bot preferences for the organization

    Flow:
    -----
    1. Organization Creation ('org' step):
       - Collects organization details (name, description, transcript TTL)
       - Creates organization immediately upon step completion
       - Stores organization ID and name in wizard's storage

    2. User Import ('users' step):
       - Accepts CSV file upload with user details
       - Processes users and creates accounts
       - Creates necessary licenses
       - Stores import timestamp and results in wizard's storage

    3. User Selection ('select_users' step):
       - Displays newly created users from the import
       - Filters users based on import timestamp
       - Allows selection of users to receive welcome emails

    4. Bot Preferences ('preferences' step):
       - Configures organization-specific bot settings
       - Handles image uploads for recording/not-recording states
       - Sets notetaker name

    Storage:
    --------
    The wizard maintains state using django-formtools' SessionWizardView storage.
    Key storage items include:
    - organization_instance: Dict with 'id' and 'name' of created organization
    - import_timestamp: ISO format timestamp of user import
    - users_import_result: Dict with counts of created/skipped users/licenses
    - forms_dict: Dictionary of form instances for final processing

    Form List:
    ----------
    - org: OrganizationForm
    - users: UserImportForm
    - select_users: UserSelectionForm
    - preferences: UpdateBotPreferencesForm

    Attributes:
        form_list (dict): Mapping of step names to form classes
        file_storage (FileSystemStorage): Storage for temporary file uploads
        template_name (str): Template used for rendering the wizard

    Example:
    --------
    This wizard is typically accessed through the Django admin interface:

    Notes:
    ------
    - The wizard creates the organization in the first step rather than waiting
      for completion to allow subsequent steps to reference it
    - User selection is filtered to show only users created during the current
      wizard session using the import timestamp
    - Form validation errors in any step will prevent progression to next step
    - All steps maintain error logging and proper exception handling
    - The wizard cleans up its storage upon successful completion

    Error Handling:
    --------------
    - Each step includes try/except blocks with detailed error logging
    - Validation errors are displayed to the user via Django messages
    - Storage/state errors result in redirect to organization list view
    - Database errors are caught and logged with full stack traces
    """

    form_list = [
        ("org", OrganizationForm),
        ("users", UserImportForm),
        ("select_users", UserSelectionForm),
        ("preferences", UpdateBotPreferencesForm),
    ]

    file_storage = FileSystemStorage(location=os.path.join(settings.MEDIA_ROOT, "temp_uploads"))
    template_name = "admin/organization_wizard.html"

    def process_step(self, form):  # type: ignore[no-untyped-def]
        """Process each step before moving to the next."""
        step_data = self.get_form_step_data(form)
        current_step = self.steps.current

        if current_step == "org":
            try:
                admin_user = self.request.user.get_full_name() or self.request.user.email
                current_time = timezone.now()
                creation_description = f"Created on {current_time.strftime('%Y-%m-%d %H:%M:%S')} by {admin_user}"

                # Create and save organization immediately
                organization = Organization.objects.create(
                    name=form.cleaned_data["name"],
                    description=creation_description or form.cleaned_data.get("description", ""),
                    transcript_ttl=form.cleaned_data.get("transcript_ttl"),
                )
                # Store organization data for later use
                self.storage.extra_data["organization_instance"] = {"id": organization.id, "name": organization.name}
                logger.info("Created organization: %s (ID: %s)", organization.name, organization.id)

            except Exception as e:
                logger.error("Error creating organization", exc_info=e)
                raise

        elif current_step == "users":
            try:
                org_data = self.storage.extra_data.get("organization_instance")
                if not org_data or not org_data.get("id"):
                    raise ValueError("Organization data not found")

                organization = Organization.objects.get(id=org_data["id"])
                csv_file = form.cleaned_data["csv_file"]

                import_timestamp = timezone.now()
                self.storage.extra_data["import_timestamp"] = import_timestamp.isoformat()

                users_created, users_skipped, licenses_created, licenses_skipped, errors = process_users_from_csv(
                    organization, csv_file
                )

                self.storage.extra_data["users_import_result"] = {
                    "users_created": users_created,
                    "users_skipped": users_skipped,
                    "licenses_created": licenses_created,
                    "licenses_skipped": licenses_skipped,
                    "errors": errors,
                }

            except Exception as e:
                logger.error("Error processing users ", exc_info=e)
                raise

        return step_data

    def get_form_kwargs(self, step=None):  # type: ignore[no-untyped-def]
        kwargs = super().get_form_kwargs(step)

        if step == "select_users":
            org_data = self.storage.extra_data.get("organization_instance")
            if org_data and org_data.get("id"):
                try:
                    organization = Organization.objects.get(id=org_data["id"])
                    kwargs.update({"organization": organization, "wizard": self})
                except Organization.DoesNotExist:
                    logger.error("Organization with id %s not found", org_data["id"])
                except Exception as e:
                    logger.error("Error getting organization: %s", str(e))

        return kwargs

    def get_context_data(self, form, **kwargs):  # type: ignore[no-untyped-def]
        context = super().get_context_data(form=form, **kwargs)

        wizard_steps = [
            ("Create Organization", self.steps.current == "org"),
            ("Import Users", self.steps.current == "users"),
            ("Select Users for Email", self.steps.current == "select_users"),
            ("Configure Bot Preferences", self.steps.current == "preferences"),
        ]

        context.update(
            {
                "title": "Organization Onboarding",
                "subtitle": f"Step {self.steps.step1} of {self.steps.count}: {self.steps.current.title()}",
                "wizard_steps": wizard_steps,
            }
        )

        if self.steps.current == "select_users":
            org_data = self.storage.extra_data.get("organization_instance")
            import_result = self.storage.extra_data.get("users_import_result", {})

            if org_data:
                context.update(
                    {
                        "organization_name": org_data.get("name"),
                        "users_created": import_result.get("users_created", 0),
                        "users_skipped": import_result.get("users_skipped", 0),
                        "import_errors": import_result.get("errors", []),
                    }
                )

        return context

    def render_done(self, form, **kwargs):  # type: ignore[no-untyped-def]
        try:
            final_form = self.get_form(
                step=self.steps.current,
                data=self.storage.get_step_data(self.steps.current),
                files=self.storage.get_step_files(self.steps.current),
            )

            if not final_form.is_valid():
                logger.error("Final form validation failed. Errors: %s", final_form.errors)
                return self.render_revalidation_failure(self.steps.current, final_form, **kwargs)

            form_list = []
            forms_dict = {}

            for step in self.form_list.keys():  # type: ignore[attr-defined]
                form_class = self.form_list[step]
                form_data = self.storage.get_step_data(step)
                form_files = self.storage.get_step_files(step)

                if step == self.steps.current:
                    form_instance = final_form
                else:
                    form_kwargs = {
                        "data": form_data,
                        "files": form_files,
                        "prefix": self.get_form_prefix(step, form_class),
                        "initial": self.get_form_initial(step),
                    }

                    if step == "select_users":
                        org_data = self.storage.extra_data.get("organization_instance")
                        if org_data and org_data.get("id"):
                            try:
                                organization = Organization.objects.get(id=org_data["id"])
                                form_kwargs.update({"organization": organization, "wizard": self})
                            except Organization.DoesNotExist:
                                logger.error("Organization not found for ID: %s", org_data["id"])

                    form_instance = form_class(**form_kwargs)

                form_list.append(form_instance)
                forms_dict[step] = form_instance

            self.storage.extra_data["forms_dict"] = forms_dict

            return self.done(form_list, **kwargs)  # type: ignore[no-untyped-call]

        except Exception as e:
            logger.exception("Error in render_done")
            messages.error(self.request, "An error occurred: %s", str(e))
            return HttpResponseRedirect(reverse("admin:meetingsapp_organization_changelist"))

    def done(self, form_list, **kwargs):  # type: ignore[no-untyped-def]
        """Process final submission."""
        try:
            org_data = self.storage.extra_data.get("organization_instance")
            if not org_data or not org_data.get("id"):
                raise ValueError("Organization data not found in storage")

            try:
                organization = Organization.objects.get(id=org_data["id"])
            except Organization.DoesNotExist:
                raise ValueError(f"Organization with id {org_data['id']} not found")

            # Create forms dictionary using the correct keys
            forms_dict = dict(zip(self.form_list.keys(), form_list))  # type: ignore[attr-defined]

            # Process selected users
            selection_form = forms_dict.get("select_users")
            if selection_form:
                step_data = self.storage.get_step_data("select_users")
                if step_data:
                    selected_user_ids = step_data.getlist("select_users-users")
                    if selected_user_ids:
                        selected_users = organization.users.filter(id__in=selected_user_ids)
                        success = handle_bulk_user_onboarding(
                            users=selected_users,  # type: ignore[arg-type]
                            request=self.request,
                            log_prefix=f"OrganizationWizard ({organization.name}):",
                        )
                        if not success:
                            raise ValueError("Failed to process user emails")
                        messages.success(
                            self.request, f"Successfully sent welcome emails to {len(selected_users)} users"
                        )

            # Process bot preferences
            preferences_form = forms_dict.get("preferences")
            if preferences_form and preferences_form.is_valid():
                cleaned_data = preferences_form.cleaned_data

                # Log the data we're about to process
                logger.debug("Processing bot preferences with data: %s", cleaned_data)

                success, error = BotPreferencesHandler.process_and_update(
                    organization,
                    not_recording_image=cleaned_data.get("not_recording_image"),
                    recording_image=cleaned_data.get("recording_image"),
                    notetaker_name=cleaned_data.get("notetaker_name"),
                )

                if not success:
                    raise ValueError("Failed to update bot preferences: %s" % error)
                messages.success(self.request, "Successfully updated bot preferences")

            logger.info(
                "Successfully completed onboarding for organization: %s (ID: %s)", organization.name, organization.id
            )
            self.storage.reset()

            return HttpResponseRedirect(reverse("admin:meetingsapp_organization_changelist"))

        except Exception as e:
            logger.exception("Error in organization wizard completion")
            messages.error(self.request, str(e))
            return HttpResponseRedirect(reverse("admin:meetingsapp_organization_changelist"))
