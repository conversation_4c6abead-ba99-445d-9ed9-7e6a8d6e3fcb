import logging

from django import forms
from django.contrib import messages
from django.contrib.admin.views.decorators import staff_member_required
from django.http import HttpResponse, HttpResponseRedirect
from django.urls import reverse
from django.utils.decorators import method_decorator
from django.views.generic import FormView

from deepinsights.meetingsapp.management.commands.loadnotes import load_dumped_data


class OrganizationImportForm(forms.Form):
    file = forms.FileField(
        help_text="Upload a file containing organization data dumped via the admin console or the dumpnotes command."
    )

    replace_identifiers = forms.BooleanField(
        required=False,
        help_text="Replace all UUIDs and other unique identifiers in the data with new ones. This is useful if you're importing data in an attempt to 'clone' an org rather than populate a new database.",
    )


logger = logging.getLogger(__name__)


@method_decorator(staff_member_required, name="dispatch")
class OrganizationImportView(FormView):  # type: ignore[type-arg]
    template_name = "admin/meetingsapp/organization/organization_import.html"
    form_class = OrganizationImportForm

    def form_valid(self, form: OrganizationImportForm) -> HttpResponse:
        try:
            file = form.cleaned_data["file"]
            replace_identifiers = form.cleaned_data["replace_identifiers"]

            load_dumped_data(
                file.read(), rewrite_uuids=replace_identifiers, rewrite_users_and_org=replace_identifiers, num_tries=2
            )

            # Add success message
            messages.success(self.request, "Import completed.")
            # Redirect to waiting page
            return HttpResponseRedirect(reverse("admin:meetingsapp_organization_changelist"))
        except Exception as e:
            logger.error("Error importing data", exc_info=e)
            messages.error(self.request, f"Error importing data: {str(e)}")
            return self.form_invalid(form)
