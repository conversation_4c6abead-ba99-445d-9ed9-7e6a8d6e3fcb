from unittest.mock import MagicMock, patch

from django.contrib.messages import get_messages
from django.contrib.messages.middleware import MessageMiddleware
from django.contrib.sessions.middleware import SessionMiddleware
from django.core.exceptions import ValidationError
from django.core.files.uploadedfile import SimpleUploadedFile
from django.http import HttpRequest, HttpResponseRedirect
from django.test import RequestFactory, TestCase
from django.urls import reverse
from django.utils.datastructures import MultiValueDict

from deepinsights.meetingsapp.models.prompt import Prompt
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingDataTemplate
from deepinsights.meetingsapp.models.structured_meeting_data_schema import StructuredMeetingDataSchema
from deepinsights.meetingsapp.template_generation import TemplateGeneratorForm, TemplateGeneratorView


class TemplateGeneratorFormTest(TestCase):
    def setUp(self) -> None:
        self.form_data = {
            "template_type": "summary",
            "name": "Test Template",
            "internal_name": "test_template",
            "questions_text": "Test questions",
        }

    def test_form_validation_valid(self) -> None:
        form = TemplateGeneratorForm(data=self.form_data)
        self.assertTrue(form.is_valid())

    def test_form_validation_missing_required_fields(self) -> None:
        form = TemplateGeneratorForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn("template_type", form.errors)
        self.assertIn("name", form.errors)
        self.assertIn("internal_name", form.errors)

    def test_form_validation_no_questions(self) -> None:
        form_data = self.form_data.copy()
        form_data.pop("questions_text")
        form = TemplateGeneratorForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn("__all__", form.errors)
        self.assertEqual(form.errors["__all__"][0], "Please either upload a file or enter questions directly.")

    def test_form_validation_with_file(self) -> None:
        form_data = self.form_data.copy()
        form_data.pop("questions_text")
        file = SimpleUploadedFile("questions.txt", b"Test questions", content_type="text/plain")
        form = TemplateGeneratorForm(data=form_data, files=MultiValueDict({"questions_file": [file]}))
        self.assertTrue(form.is_valid())

    def test_form_validation_invalid_file_type(self) -> None:
        form_data = self.form_data.copy()
        form_data.pop("questions_text")
        file = SimpleUploadedFile("questions.pdf", b"Test questions", content_type="application/pdf")
        form = TemplateGeneratorForm(data=form_data, files=MultiValueDict({"questions_file": [file]}))
        self.assertTrue(form.is_valid())  # File type validation is not implemented


class TemplateGeneratorViewTest(TestCase):
    def setUp(self) -> None:
        # Create the schemas needed by migrations
        self.structured_schema, _ = StructuredMeetingDataSchema.objects.get_or_create(
            name="structured_data_schema",
            defaults={
                "schema": {
                    "$defs": {
                        "review_entry": {
                            "properties": {"kind": {"enum": ["header", "toggle", "select", "multiselect", "bullet"]}}
                        }
                    }
                }
            },
        )

        self.unstructured_schema, _ = StructuredMeetingDataSchema.objects.get_or_create(
            name="unstructured_text",
            defaults={"schema": {}},  # Empty schema for unstructured text
        )

        self.factory = RequestFactory()
        self.view = TemplateGeneratorView()
        self.view.request = self.factory.get("/")

        # Add session and message middleware
        self._add_session_and_messages(self.view.request)

        # Create test data
        self.summary_prompt = Prompt.objects.create(
            unique_name="presonalized_summary_prompt_v1", user_prompt="Test prompt content"
        )
        self.checklist_prompt = Prompt.objects.create(
            unique_name="generic_template_prompt_v2", user_prompt="Test checklist prompt content"
        )

    def _add_session_and_messages(self, request: HttpRequest) -> None:
        SessionMiddleware(lambda x: x).process_request(request)
        MessageMiddleware(lambda x: x).process_request(request)
        request.session.save()

    def test_get_prompt_for_template_type_valid(self) -> None:
        prompt = self.view.get_prompt_for_template_type("summary")
        self.assertEqual(prompt, self.summary_prompt)

        prompt = self.view.get_prompt_for_template_type("checklist")
        self.assertEqual(prompt, self.checklist_prompt)

    def test_get_prompt_for_template_type_invalid(self) -> None:
        with self.assertRaises(ValidationError):
            self.view.get_prompt_for_template_type("invalid_type")

    @patch("deepinsights.meetingsapp.template_generation.generate_template_data.delay")
    def test_form_valid_summary_template(self, mock_generate_template: MagicMock) -> None:
        form_data = {
            "template_type": "summary",
            "name": "Test Summary",
            "internal_name": "test_summary",
            "questions_text": "Test questions",
        }
        form = TemplateGeneratorForm(data=form_data)
        form.is_valid()

        response = self.view.form_valid(form)
        assert isinstance(response, HttpResponseRedirect)

        # Verify the task was called with correct arguments
        mock_generate_template.assert_called_once()
        call_args = mock_generate_template.call_args[0]
        self.assertEqual(call_args[0], "This is a summary template\n\nTest questions")  # questions_text
        self.assertEqual(call_args[1], self.structured_schema.id)  # schema_id
        self.assertEqual(call_args[2], "Test Summary")  # name
        self.assertEqual(call_args[3], "test_summary")  # internal_name
        self.assertEqual(call_args[4], "personalized_summary")  # kind
        self.assertEqual(call_args[5], self.summary_prompt.id)  # prompt_id

        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, reverse("admin:meetingsapp_structuredmeetingdatatemplate_changelist"))

        messages = list(get_messages(self.view.request))
        self.assertEqual(len(messages), 1)
        self.assertIn("Template generation started", str(messages[0]))

    @patch("deepinsights.meetingsapp.template_generation.generate_template_data.delay")
    def test_form_valid_agenda_template(self, mock_generate_template: MagicMock) -> None:
        form_data = {
            "template_type": "agenda",
            "name": "Test Agenda",
            "internal_name": "test_agenda",
            "questions_text": "Test questions",
        }
        form = TemplateGeneratorForm(data=form_data)
        form.is_valid()

        response = self.view.form_valid(form)
        assert isinstance(response, HttpResponseRedirect)

        mock_generate_template.assert_not_called()
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, reverse("admin:meetingsapp_structuredmeetingdatatemplate_changelist"))

        # Verify template was created
        template = StructuredMeetingDataTemplate.objects.get(internal_name="test_agenda")
        self.assertEqual(template.title, "Test Agenda")
        self.assertEqual(template.kind, "agenda")
        self.assertEqual(template.schema_definition, self.unstructured_schema)
        assert template.initial_data is not None
        self.assertEqual(template.initial_data["content"], "Test questions")
        self.assertEqual(template.initial_data["format"], "markdown")

        messages = list(get_messages(self.view.request))
        self.assertEqual(len(messages), 1)
        self.assertIn("Template generation started", str(messages[0]))

    def test_form_valid_missing_schema(self) -> None:
        # Delete the schema to simulate missing schema
        StructuredMeetingDataSchema.objects.all().delete()

        form_data = {
            "template_type": "summary",
            "name": "Test Summary",
            "internal_name": "test_summary",
            "questions_text": "Test questions",
        }
        form = TemplateGeneratorForm(data=form_data)
        form.is_valid()

        response = self.view.form_valid(form)

        self.assertEqual(response.status_code, 200)  # Form invalid response
        messages = list(get_messages(self.view.request))
        self.assertEqual(len(messages), 1)
        self.assertIn("Required schema not found", str(messages[0]))

    @patch("deepinsights.meetingsapp.template_generation.generate_template_data.delay")
    def test_form_valid_with_custom_prompt(self, mock_generate_template: MagicMock) -> None:
        custom_prompt = Prompt.objects.create(unique_name="custom_prompt", user_prompt="Custom prompt content")

        form_data = {
            "template_type": "summary",
            "name": "Test Summary",
            "internal_name": "test_summary",
            "questions_text": "Test questions",
            "custom_prompt": custom_prompt.id,
        }
        form = TemplateGeneratorForm(data=form_data)
        form.is_valid()

        response = self.view.form_valid(form)
        assert isinstance(response, HttpResponseRedirect)

        # Verify the task was called with correct arguments
        mock_generate_template.assert_called_once()
        call_args = mock_generate_template.call_args[0]
        self.assertEqual(call_args[0], "This is a summary template\n\nTest questions")  # questions_text
        self.assertEqual(call_args[1], self.structured_schema.id)  # schema_id
        self.assertEqual(call_args[2], "Test Summary")  # name
        self.assertEqual(call_args[3], "test_summary")  # internal_name
        self.assertEqual(call_args[4], "personalized_summary")  # kind
        self.assertEqual(call_args[5], custom_prompt.id)  # prompt_id

        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, reverse("admin:meetingsapp_structuredmeetingdatatemplate_changelist"))

        messages = list(get_messages(self.view.request))
        self.assertEqual(len(messages), 1)
        self.assertIn("Template generation started", str(messages[0]))

    def test_form_valid_exception_handling(self) -> None:
        form_data = {
            "template_type": "summary",
            "name": "Test Summary",
            "internal_name": "test_summary",
            "questions_text": "Test questions",
        }
        form = TemplateGeneratorForm(data=form_data)
        form.is_valid()

        with patch.object(self.view, "get_prompt_for_template_type", side_effect=Exception("Test error")):
            response = self.view.form_valid(form)

        self.assertEqual(response.status_code, 200)  # Form invalid response
        messages = list(get_messages(self.view.request))
        self.assertEqual(len(messages), 1)
        self.assertIn("Error generating template: Test error", str(messages[0]))

    @patch("deepinsights.meetingsapp.template_generation.generate_template_data.delay")
    def test_form_valid_checklist_template(self, mock_generate_template: MagicMock) -> None:
        form_data = {
            "template_type": "checklist",
            "name": "Test Checklist",
            "internal_name": "test_checklist",
            "questions_text": "Test checklist questions",
        }
        form = TemplateGeneratorForm(data=form_data)
        form.is_valid()

        response = self.view.form_valid(form)
        assert isinstance(response, HttpResponseRedirect)

        # Verify the task was called with correct arguments
        mock_generate_template.assert_called_once()
        call_args = mock_generate_template.call_args[0]
        self.assertEqual(call_args[0], "This is a checklist template\n\nTest checklist questions")  # questions_text
        self.assertEqual(call_args[1], self.structured_schema.id)  # schema_id
        self.assertEqual(call_args[2], "Test Checklist")  # name
        self.assertEqual(call_args[3], "test_checklist")  # internal_name
        self.assertEqual(call_args[4], "test_checklist")  # kind
        self.assertEqual(call_args[5], self.checklist_prompt.id)  # prompt_id

        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, reverse("admin:meetingsapp_structuredmeetingdatatemplate_changelist"))

        messages = list(get_messages(self.view.request))
        self.assertEqual(len(messages), 1)
        self.assertIn("Template generation started", str(messages[0]))
