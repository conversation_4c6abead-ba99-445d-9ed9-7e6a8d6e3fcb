# Generated by Django 4.2 on 2024-02-29 01:43

import deepinsights.meetingsapp.models.organization
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0026_alter_historicalnote_summary_alter_note_summary"),
    ]

    operations = [
        migrations.Alter<PERSON><PERSON>(
            model_name="historicalorganization",
            name="crm_configuration",
            field=models.JSONField(default=deepinsights.meetingsapp.models.organization.get_default_crm_configuration),
        ),
        migrations.Alter<PERSON>ield(
            model_name="historicalorganization",
            name="description",
            field=models.TextField(default="A new Zeplyn client.", verbose_name="Description"),
        ),
        migrations.Alter<PERSON>ield(
            model_name="historicalorganization",
            name="metadata",
            field=models.J<PERSON><PERSON>ield(default=dict),
        ),
        migrations.AlterField(
            model_name="organization",
            name="crm_configuration",
            field=models.JSONField(default=deepinsights.meetingsapp.models.organization.get_default_crm_configuration),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="organization",
            name="description",
            field=models.TextField(default="A new Zeplyn client.", verbose_name="Description"),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="organization",
            name="metadata",
            field=models.JSONField(default=dict),
        ),
    ]
