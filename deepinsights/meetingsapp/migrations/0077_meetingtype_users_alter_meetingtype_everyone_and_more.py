# Generated by Django 4.2.16 on 2024-11-19 22:15

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0076_annual_review_followups"),
    ]

    operations = [
        migrations.AddField(
            model_name="meetingtype",
            name="users",
            field=models.ManyToManyField(
                blank=True,
                help_text="The users who have access to this type of meeting. If this is empty, then no specific users have access to this type of meeting. Users may still have access to this meeting via the `organzations` or `everyone` fields.",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="meetingtype",
            name="everyone",
            field=models.BooleanField(
                default=False,
                help_text="Whether or not this meeting type is available to everyone. If this is true, then all users have access to this type of meeting. If this is false, then only users allowlisted by the organizations list or users list have access to this meeting type.",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="meetingtype",
            name="organizations",
            field=models.ManyToManyField(
                blank=True,
                help_text="The organizations which have access to this type of meeting.If this is empty, then no specific organizations have access to this type of meeting. Users may still have access to this meeting via the `users` or `everyone` fields.",
                to="meetingsapp.organization",
            ),
        ),
    ]
