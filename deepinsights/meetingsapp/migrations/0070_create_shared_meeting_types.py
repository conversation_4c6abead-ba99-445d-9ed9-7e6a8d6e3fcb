from django.db import migrations


def create_shared_meeting_types(apps, schema_editor):
    MeetingType = apps.get_model("meetingsapp", "MeetingType")

    db_alias = schema_editor.connection.alias
    create_meeting_type = MeetingType.objects.using(db_alias).create

    create_meeting_type(
        key="client",
        name="Client",
        category="client",
    )
    create_meeting_type(
        key="internal",
        name="Internal",
        category="internal",
    )
    create_meeting_type(
        key="debrief",
        name="Debrief",
        category="debrief",
    )


def delete_shared_meeting_types(apps, schema_editor):
    MeetingType = apps.get_model("meetingsapp", "MeetingType")
    db_alias = schema_editor.connection.alias
    MeetingType.objects.using(db_alias).filter(key__in=["client", "internal", "debrief"]).delete()


class Migration(migrations.Migration):
    dependencies = [("meetingsapp", "0069_alter_meetingtype_category")]

    operations = [migrations.RunPython(create_shared_meeting_types, reverse_code=delete_shared_meeting_types)]
