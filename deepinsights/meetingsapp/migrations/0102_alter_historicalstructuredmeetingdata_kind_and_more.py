# Generated by Django 4.2.17 on 2025-01-16 22:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("meetingsapp", "0101_structuredmeetingdatatemplaterule_all_meetings"),
    ]

    operations = [
        migrations.AlterField(
            model_name="historicalstructuredmeetingdata",
            name="kind",
            field=models.Char<PERSON>ield(
                help_text="What 'kind' of structured meeting data this is.\n\nThis has two major uses (at this time):\n- to provide an identifier for shared templates that are generated by a database migration, and which we may need to reference in the future to modify (or remove )- to provide a way for us to sync data fields to specific fields in specific CRMs for specific templatesThis was originially designed as an escape valve to help us render structured meeting data in the frontend. However, over time we've come to release that using the schema.$id is a better way to do this, since that allows us to centralize on a limited number of schema types but with many different meeting data templates. Notably, we can't use the schema.$id in CRM sync workflows because the schema does not necessarily provide semantic information about the data fields, only the shape of the data."
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="structuredmeetingdata",
            name="kind",
            field=models.CharField(
                help_text="What 'kind' of structured meeting data this is.\n\nThis has two major uses (at this time):\n- to provide an identifier for shared templates that are generated by a database migration, and which we may need to reference in the future to modify (or remove )- to provide a way for us to sync data fields to specific fields in specific CRMs for specific templatesThis was originially designed as an escape valve to help us render structured meeting data in the frontend. However, over time we've come to release that using the schema.$id is a better way to do this, since that allows us to centralize on a limited number of schema types but with many different meeting data templates. Notably, we can't use the schema.$id in CRM sync workflows because the schema does not necessarily provide semantic information about the data fields, only the shape of the data."
            ),
        ),
        migrations.AlterField(
            model_name="structuredmeetingdatatemplate",
            name="kind",
            field=models.CharField(
                help_text="What 'kind' of structured meeting data this is.\n\nThis has two major uses (at this time):\n- to provide an identifier for shared templates that are generated by a database migration, and which we may need to reference in the future to modify (or remove )- to provide a way for us to sync data fields to specific fields in specific CRMs for specific templatesThis was originially designed as an escape valve to help us render structured meeting data in the frontend. However, over time we've come to release that using the schema.$id is a better way to do this, since that allows us to centralize on a limited number of schema types but with many different meeting data templates. Notably, we can't use the schema.$id in CRM sync workflows because the schema does not necessarily provide semantic information about the data fields, only the shape of the data."
            ),
        ),
    ]
