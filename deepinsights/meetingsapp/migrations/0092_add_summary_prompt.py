# Generated by Django 4.2.16 on 2024-12-27 04:28

from django.db import migrations

from deepinsights.meetingsapp.migrations import search_prompt


def create_search_prompt(apps, schema_editor):
    Prompt = apps.get_model("meetingsapp", "Prompt")
    db_alias = schema_editor.connection.alias

    Prompt.objects.using(db_alias).create(
        name="search prompt",
        unique_name="search_prompt_v1",
        version="1.0",
        text=search_prompt,
        version_notes="Initial version of search prompt",
    )


def reverse_search_prompt(apps, schema_editor):
    Prompt = apps.get_model("meetingsapp", "Prompt")
    db_alias = schema_editor.connection.alias
    Prompt.objects.using(db_alias).filter(unique_name="search_prompt_v1").delete()


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0091_historicalprompt_unique_name_prompt_unique_name"),
    ]

    operations = [
        migrations.RunPython(create_search_prompt, reverse_code=reverse_search_prompt),
    ]
