from django.db import migrations

schema = {
    "definitions": {
        "evidence_entry": {
            "type": "object",
            "properties": {"quote": {"type": "string"}, "timestamp": {"type": "string"}},
            "required": ["quote"],
            "additionalProperties": False,
        },
        "compliance_entry": {
            "type": "object",
            "properties": {
                "id": {"type": "string"},
                "kind": {"type": "string", "enum": ["toggle", "select"]},
                "topic": {"type": "string"},
                "discussed": {"type": "boolean"},
                "options": {"type": "array", "items": {"type": "string"}},
                "selected": {"type": "string"},
                "explanation": {"type": "string"},
                "evidence": {
                    "type": "array",
                    "items": {"$ref": "#/definitions/evidence_entry"},
                },
            },
            "required": ["id", "kind", "topic", "discussed"],
            "additionalProperties": False,
        },
    },
    "type": "object",
    "properties": {
        "compliance_checks": {
            "type": "array",
            "items": {"$ref": "#/definitions/compliance_entry"},
            "uniqueItems": True,
        },
    },
    "required": [
        "compliance_checks",
    ],
    "additionalProperties": False,
}

data = {
    "compliance_checks": [
        {
            "id": "financial_status",
            "kind": "toggle",
            "topic": "Current financial status",
            "discussed": False,
        },
        {
            "id": "allocations_holdings",
            "kind": "toggle",
            "topic": "Asset allocation and/or account holdings",
            "discussed": False,
        },
        {
            "id": "investment_performance",
            "kind": "toggle",
            "topic": "Investment performance",
            "discussed": False,
        },
        {
            "id": "insurance_needs",
            "kind": "toggle",
            "topic": "Insurance needs/planning",
            "discussed": False,
        },
        {
            "id": "time_horizon",
            "kind": "toggle",
            "topic": "Current time horizon",
            "discussed": False,
        },
        {
            "id": "risk_tolerance",
            "kind": "select",
            "topic": "Current risk tolerance",
            "discussed": False,
            "options": ["High", "Medium", "Low"],
            "selected": "",
        },
        {
            "id": "investment_goals",
            "kind": "select",
            "topic": "Current investment objectives",
            "discussed": False,
            "options": [
                "Aggressive Growth",
                "Growth Focus",
                "Balanced/Growth & Income",
                "Income Focus",
                "Preservation of Principal",
            ],
            "selected": "",
        },
    ],
}

context = """Example Transcript:
Advisor    "00:15:20-->00:15:40"
Let's review your current financial position. Your portfolio currently has $500,000 in retirement accounts.

Client    "00:15:40-->00:15:55"
Yes, and I recently inherited $200,000 which I'd like to invest.

Advisor    "00:15:55-->00:16:15"
Your risk tolerance questionnaire from last month indicates moderate risk tolerance.

Advisor    "00:16:15-->00:16:45"
For your time horizon, you mentioned wanting to retire in 15 years.

Client    "00:16:45-->00:17:00"
That's right, I'm aiming to retire at 65.

Advisor    "00:17:00-->00:17:30"
Let's look at your current investment allocation. You're currently at 70% stocks, 30% bonds.

Client    "00:17:30-->00:17:50"
I'm comfortable with that mix given the current market.

Advisor    "00:17:50-->00:18:10"
Your portfolio performance has been strong, up 8% year to date.

Client    "00:18:10-->00:18:25"
That's good to hear. What about insurance coverage?

Advisor    "00:18:25-->00:18:45"
We didn't get into insurance today, let's schedule a follow-up to discuss that specifically.

Example Output:
{
    "compliance_checks": [
        {
            "id": "financial_status",
            "topic": "Current financial status",
            "discussed": true,
            "explanation": "Reviewed current portfolio value and recent inheritance",
            "evidence": [
                {
                    "timestamp": "00:15:20",
                    "quote": "Your portfolio currently has $500,000 in retirement accounts"
                },
                {
                    "timestamp": "00:15:40",
                    "quote": "Yes, and I recently inherited $200,000 which I'd like to invest"
                }
            ]
        },
        {
            "id": "allocations_holdings",
            "topic": "Asset allocation and/or account holdings",
            "discussed": true,
            "explanation": "Reviewed current asset allocation between stocks and bonds",
            "evidence": [
                {
                    "timestamp": "00:17:00",
                    "quote": "Let's look at your current investment allocation. You're currently at 70% stocks, 30% bonds"
                }
            ]
        },
        {
            "id": "investment_performance",
            "topic": "Investment performance",
            "discussed": true,
            "explanation": "Discussed year-to-date portfolio performance",
            "evidence": [
                {
                    "timestamp": "00:17:50",
                    "quote": "Your portfolio performance has been strong, up 8% year to date"
                }
            ]
        },
        {
            "id": "insurance_needs",
            "topic": "Insurance needs",
            "discussed": false,
            "explanation": "Insurance discussion deferred to follow-up meeting",
            "evidence": [
                {
                    "timestamp": "00:18:25",
                    "quote": "We didn't get into insurance today, let's schedule a follow-up to discuss that specifically"
                }
            ]
        },
        {
            "id": "time_horizon",
            "topic": "Current time horizon",
            "discussed": true,
            "explanation": "Discussed retirement timeline of 15 years with target retirement age of 65",
            "evidence": [
                {
                    "timestamp": "00:16:15",
                    "quote": "For your time horizon, you mentioned wanting to retire in 15 years"
                },
                {
                    "timestamp": "00:16:45",
                    "quote": "That's right, I'm aiming to retire at 65"
                }
            ]
        },
        {
            "id": "risk_tolerance",
            "topic": "Current risk tolerance",
            "discussed": true,
            "options": ["High", "Medium", "Low"],
            "selected": "Medium",
            "explanation": "Confirmed moderate risk tolerance based on recent questionnaire",
            "evidence": [
                {
                    "timestamp": "00:15:55",
                    "quote": "Your risk tolerance questionnaire from last month indicates moderate risk tolerance"
                }
            ]
        },
        {
            "id": "investment_goals",
            "kind": "select",
            "topic": "Current investment objectives",
            "discussed": False,
            "options": [
                "Aggressive Growth",
                "Growth Focus",
                "Balanced/Growth & Income",
                "Income Focus",
                "Preservation of Principal",
            ],
            "selected": "",
            "explanation": "Investment objectives were not explicitly discussed during the meeting",
            "evidence": []
        },
    ]
}
"""

prompt_text = """You are a compliance auditor for financial advisors, analyzing meeting transcripts to verify if mandatory compliance topics were discussed
and determine whether each required topic was adequately covered, providing detailed evidence from the transcript. In some cases, you will also need to
select from one of a set of options that most closely matches

Evaluation Guidelines:
1. Only mark a topic as "discussed: true" if it was explicitly discussed
2. Include exact quotes with timestamps as evidence
3. For each topic discussed, provide a brief explanation of what was covered
4. For topics not covered, mark as "discussed: false" with no evidence
5. Do not make assumptions about implied discussions
6. Use the start time from the timestamp range as reference
7. If a topic is a "select" kind, select the option that most closely matches the discussion or leave empty if unsure
8. If a topic is a "toggle" kind, leave the "selected" and "options" fields empty, and only mark as "discussed: true" if explicitly discussed

{example}

For each compliance topic, your output must include:
1. An "id" field matching the provided schema
2. A "kind" field indicating if the topic is a toggle or select. This should be exactly copied from the input data.
3. A "topic" field matching the provided schema
4. A "discussed" boolean field indicating if the topic was covered
5. An "explanation" field summarizing what was discussed or noting if it wasn't covered
6. An "evidence" array containing relevant quotes with timestamps (use start time from timestamp range)
7. If the "kind" of the topic is "select", include the "options" array copied from the input data
8. If the "kind" of the topic is "select" and the topic was discussed, include the "selected" option, populated with the value in "options" that most closely matches the discussion. If you are unsure, or no option matches, leave this field empty.

Please analyze the following meeting transcript and determine if each required compliance topic was adequately discussed. Provide your analysis in the exact format specified above.

Required Compliance Schema:
{schema}

Initial Data Structure:
{data}

Transcript:
{transcript}

Analyze the transcript and provide results in the exact format specified, ensuring all required fields are present and maintaining the specified order of topics.
"""


def create_annual_review_meeting_and_follow_up(apps, schema_editor):
    MeetingType = apps.get_model("meetingsapp", "MeetingType")
    Organization = apps.get_model("meetingsapp", "Organization")
    MeetingFollowUpTemplate = apps.get_model("meetingsapp", "MeetingFollowUpTemplate")
    Prompt = apps.get_model("meetingsapp", "Prompt")
    db_alias = schema_editor.connection.alias

    # Create the follow-up prompt and template.
    prompt = Prompt.objects.using(db_alias).create(
        name="Compliance (annual review)",
        text=prompt_text,
    )
    compliance_template = MeetingFollowUpTemplate.objects.using(db_alias).create(
        title="Compliance checklist",
        kind="compliance_checklist",
        schema=schema,
        initial_data=data,
        context=context,
        prompt=prompt,
    )

    # Create the annual review meeting type.
    annual_review_meeting = MeetingType.objects.using(db_alias).create(
        name="Annual Review",
        key="annual_review_sequoia",
        category="client",
    )
    annual_review_meeting.meeting_follow_up_templates.add(compliance_template)

    # Associate the meeting type with the Sequoia and Zeplyn orgs.
    if zeplyn_org := Organization.objects.filter(name="Zeplyn Internal").first():
        annual_review_meeting.organizations.add(zeplyn_org)
    if sequoia_org := Organization.objects.filter(name="Sequoia Financial Group").first():
        annual_review_meeting.organizations.add(sequoia_org)

    annual_review_meeting.save()


def remove_annual_review_meeting_and_follow_up(apps, schema_editor):
    MeetingType = apps.get_model("meetingsapp", "MeetingType")
    db_alias = schema_editor.connection.alias

    # Get the meeting type.
    meeting_type = MeetingType.objects.using(db_alias).get(key="annual_review_sequoia")

    # Delete the associated templates and prompts.
    templates = meeting_type.meeting_follow_up_templates.all()
    [template.prompt.delete() for template in templates]
    [template.delete() for template in templates]

    # Delete the meeting type itself.
    meeting_type.delete()


class Migration(migrations.Migration):
    dependencies = [("meetingsapp", "0071_prompt_historicalprompt_and_more")]
    operations = [
        migrations.RunPython(
            create_annual_review_meeting_and_follow_up,
            remove_annual_review_meeting_and_follow_up,
        ),
    ]
