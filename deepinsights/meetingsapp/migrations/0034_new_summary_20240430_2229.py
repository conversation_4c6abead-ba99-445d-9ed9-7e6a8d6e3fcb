# Generated by Django 4.2 on 2024-04-30 22:29

from django.db import migrations

from deepinsights.meetingsapp.models.note import Summary, generate_section_from_string


def create_new_summary2(apps, schema_editor):
    Note = apps.get_model("meetingsapp", "Note")
    db_alias = schema_editor.connection.alias
    notes = (
        Note.objects.using(db_alias)
        .defer(
            "raw_asr_response",
            "diarized_trans_with_names",
            "raw_transcript",
            "summary_by_topics",
            "advisor_notes",
            "key_takeaways",
        )
        .exclude(summary_by_topics__isnull=True)
        .exclude(summary_by_topics=[])
    )
    for note in notes:
        try:
            summary_sections = []
            for section_text in note.summary_by_topics:
                summary_sections.append(generate_section_from_string(section_string=section_text))
            summary = Summary(sections=summary_sections)
            note.summary = summary.to_dict()
        except Exception as e:
            print("summary corrupted for note", note.uuid)
    Note.objects.using(db_alias).bulk_update(notes, ["summary"])


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0033_alter_historicalnote_status_alter_note_status"),
    ]

    operations = [
        migrations.RunPython(create_new_summary2),
    ]
