# Generated by Django 4.2 on 2024-05-15 04:39

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0035_meetingbot"),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name="historicalnote",
            name="salesforce_case_id",
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="note",
            name="salesforce_case_id",
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="historicalnote",
            name="raw_asr_response",
            field=models.JSO<PERSON>ield(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="historicalnote",
            name="summary",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name="note",
            name="raw_asr_response",
            field=models.J<PERSON><PERSON>ield(blank=True, null=True),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="note",
            name="summary",
            field=models.J<PERSON><PERSON>ield(blank=True, default=dict, null=True),
        ),
    ]
