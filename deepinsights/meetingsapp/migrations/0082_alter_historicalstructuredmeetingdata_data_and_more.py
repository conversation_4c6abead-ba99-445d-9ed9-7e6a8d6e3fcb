# Generated by Django 4.2.16 on 2024-12-11 19:22

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("meetingsapp", "0081_rename_historicalmeetingfollowup_historicalstructuredmeetingdata_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="historicalstructuredmeetingdata",
            name="data",
            field=models.JSONField(
                blank=True,
                help_text="The data associated with this structured meeting data instance. If schema is non-null, it's expected that the data in this field will adhere to the schema (and will not contain extra fields not in the schema).",
            ),
        ),
        migrations.AlterField(
            model_name="historicalstructuredmeetingdata",
            name="kind",
            field=models.CharField(
                help_text="What 'kind' of structured meeting data this is. Since we do not know in advance what kind of meeting structured data types we will need, and how the frontend might need to render them, this is essentially an escape valve to allow us to have customized logic on the frontend. It is also used to provide an identifier for shared templates that are generated by a database migration, and which we may need to reference in the future to modify it (or remove it)."
            ),
        ),
        migrations.AlterField(
            model_name="historicalstructuredmeetingdata",
            name="note",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                help_text="The note this structured meeting data instance is associated with (or none).",
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                to="meetingsapp.note",
            ),
        ),
        migrations.AlterField(
            model_name="historicalstructuredmeetingdata",
            name="schema",
            field=models.JSONField(
                blank=True,
                help_text="The details of the data format for structured meeting data. This is intended to be a valid JSON schema (https://json-schema.org/), and may be used to generate a form on the frontend that will be used to update the data in the structured meeting data instance. In most cases, this should be non-null, but that is not enforced at the database level. If present, this will be used to validate the data field of a structured meeting data created from this template.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="historicalstructuredmeetingdata",
            name="title",
            field=models.CharField(
                blank=True,
                help_text="The title of this structured meeting data instance. This is mainly for humans and will be shown to users in the app interface, but it is also conceivable that it could be passed to the language model as context.",
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="meetingtype",
            name="meeting_follow_up_templates",
            field=models.ManyToManyField(
                blank=True,
                help_text="Templates for structured meeting data that should be associated with this meeting as follow-ups. For any of these that are selected, a structured meeting data follow-up will be generated by the LLM when the meeting is processed.",
                to="meetingsapp.structuredmeetingdatatemplate",
            ),
        ),
        migrations.AlterField(
            model_name="structuredmeetingdata",
            name="data",
            field=models.JSONField(
                blank=True,
                help_text="The data associated with this structured meeting data instance. If schema is non-null, it's expected that the data in this field will adhere to the schema (and will not contain extra fields not in the schema).",
            ),
        ),
        migrations.AlterField(
            model_name="structuredmeetingdata",
            name="kind",
            field=models.CharField(
                help_text="What 'kind' of structured meeting data this is. Since we do not know in advance what kind of meeting structured data types we will need, and how the frontend might need to render them, this is essentially an escape valve to allow us to have customized logic on the frontend. It is also used to provide an identifier for shared templates that are generated by a database migration, and which we may need to reference in the future to modify it (or remove it)."
            ),
        ),
        migrations.AlterField(
            model_name="structuredmeetingdata",
            name="note",
            field=models.ForeignKey(
                blank=True,
                help_text="The note this structured meeting data instance is associated with (or none).",
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="meetingsapp.note",
            ),
        ),
        migrations.AlterField(
            model_name="structuredmeetingdata",
            name="schema",
            field=models.JSONField(
                blank=True,
                help_text="The details of the data format for structured meeting data. This is intended to be a valid JSON schema (https://json-schema.org/), and may be used to generate a form on the frontend that will be used to update the data in the structured meeting data instance. In most cases, this should be non-null, but that is not enforced at the database level. If present, this will be used to validate the data field of a structured meeting data created from this template.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="structuredmeetingdata",
            name="title",
            field=models.CharField(
                blank=True,
                help_text="The title of this structured meeting data instance. This is mainly for humans and will be shown to users in the app interface, but it is also conceivable that it could be passed to the language model as context.",
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="structuredmeetingdatatemplate",
            name="initial_data",
            field=models.JSONField(
                blank=True,
                help_text="The initial data that should be used when creating a structured meeting data from this template. This can be null, in which case there will be no default data and the schema will be used to determine any defaults (if any).",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="structuredmeetingdatatemplate",
            name="internal_name",
            field=models.CharField(
                blank=True,
                help_text="An internally-visible name used in the admin console to identify this type of structured data. This should never be shown to users; it is only for internal reference.",
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="structuredmeetingdatatemplate",
            name="kind",
            field=models.CharField(
                help_text="What 'kind' of structured data template this is. Since we do not know in advance what kind of meeting stuctured data types we will need, and how the frontend might need to render them, this is essentially an escape valve to allow us to have customized logic on the frontend. It is also used to provide an identifier for shared templates that are generated by a database migration, and which we may need to reference in the future to modify it (or remove it)."
            ),
        ),
        migrations.AlterField(
            model_name="structuredmeetingdatatemplate",
            name="prompt",
            field=models.ForeignKey(
                blank=True,
                help_text="The prompt that should be passed to a language model when generating a structured meeting data from this template.",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="meetingsapp.prompt",
            ),
        ),
        migrations.AlterField(
            model_name="structuredmeetingdatatemplate",
            name="schema",
            field=models.JSONField(
                blank=True,
                help_text="The details of the data format for the structured meeting data template. This is intended to be a valid JSON schema (https://json-schema.org/), and may be used to generate a form on the frontend that will be used to update the structured meeting data. In most cases, this should be non-null, but that is not enforced at the database level. If present, this will be used to validate the data field of a structured meeting data created from this template.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="structuredmeetingdatatemplate",
            name="title",
            field=models.CharField(
                blank=True,
                help_text="The title of this structured data template. This is mainly for humans and will be shown to users in the app interface, but it is also conceivable that it could be passed to the language model as context.",
                max_length=200,
                null=True,
            ),
        ),
    ]
