# Generated by Django 4.2.18 on 2025-03-08 00:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("meetingsapp", "0117_onboarding_discovery_meeting_types"),
    ]

    operations = [
        migrations.AlterField(
            model_name="historicalstructuredmeetingdataschema",
            name="name",
            field=models.CharField(
                db_index=True,
                help_text="Name of the schema. This is used for reference and displayed in the admin interface. This is alsoused as a key to reference the schema in the code, so it must be unique and cannot be changed.",
                max_length=200,
            ),
        ),
        migrations.AlterField(
            model_name="structuredmeetingdataschema",
            name="name",
            field=models.CharField(
                help_text="Name of the schema. This is used for reference and displayed in the admin interface. This is alsoused as a key to reference the schema in the code, so it must be unique and cannot be changed.",
                max_length=200,
                unique=True,
            ),
        ),
    ]
