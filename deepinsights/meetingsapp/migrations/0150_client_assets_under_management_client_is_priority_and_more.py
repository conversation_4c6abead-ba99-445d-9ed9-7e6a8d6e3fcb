# Generated by Django 4.2.22 on 2025-06-24 22:30

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0150_historicaltask_workflow_type_raw_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="client",
            name="assets_under_management",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="The total assets under management for this client, in the base currency of the client's organization.",
                max_digits=20,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="client",
            name="is_priority",
            field=models.BooleanField(
                default=False,
                help_text="Whether this client is a priority client for the organization. This can be used to flag clients that require special attention or service.",
            ),
        ),
        migrations.AddField(
            model_name="client",
            name="life_phase",
            field=models.CharField(
                blank=True,
                choices=[
                    ("accumulation", "Accumulation"),
                    ("consolidation", "Consolidation"),
                    ("distribution", "Distribution"),
                    ("retirement", "Retirement"),
                ],
                help_text="The life phase of the client, e.g., 'accumulation', 'consolidation', 'distribution', etc.",
                max_length=100,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="client",
            name="onboarding_date",
            field=models.DateField(
                blank=True,
                help_text="The date when the client was onboarded to the organization. Note that this is note the date the client was created in Zeplyn, but rather the date that the firm onboarded the client.",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="client",
            name="segment",
            field=models.CharField(
                blank=True,
                choices=[("bronze", "Bronze"), ("silver", "Silver"), ("gold", "Gold"), ("platinum", "Platinum")],
                help_text="The market segment this client belongs to, e.g., 'retail', 'institutional', etc.",
                max_length=100,
                null=True,
            ),
        ),
    ]
