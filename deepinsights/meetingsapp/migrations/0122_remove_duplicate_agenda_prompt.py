# Generated by Django 4.2.18 on 2025-03-12 23:51

from django.db import migrations

from deepinsights.meetingsapp.migrations import (
    pre_meeting_agenda_generator_prompt_key,
)


def remove_duplicate_agenda_prompt_if_present(apps, schema_editor):
    Prompt = apps.get_model("meetingsapp", "Prompt")
    db_alias = schema_editor.connection.alias
    Prompt.objects.using(db_alias).filter(unique_name=pre_meeting_agenda_generator_prompt_key).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0121_add_internal_prompt"),
    ]

    operations = [
        migrations.RunPython(remove_duplicate_agenda_prompt_if_present, reverse_code=migrations.RunPython.noop)
    ]
