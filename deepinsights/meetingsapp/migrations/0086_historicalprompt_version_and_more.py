# Generated by Django 4.2.16 on 2024-12-23 17:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("meetingsapp", "0085_remove_historicalmeetingbot_end_time_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="historicalprompt",
            name="version",
            field=models.Char<PERSON>ield(default="1.0", help_text="Version number of the prompt.", max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="historicalprompt",
            name="version_notes",
            field=models.TextField(
                blank=True, help_text="Notes about this version - what changed, what's being tested, etc.", null=True
            ),
        ),
        migrations.AddField(
            model_name="prompt",
            name="version",
            field=models.CharField(default="1.0", help_text="Version number of the prompt.", max_length=50, null=True),
        ),
        migrations.AddField(
            model_name="prompt",
            name="version_notes",
            field=models.TextField(
                blank=True, help_text="Notes about this version - what changed, what's being tested, etc.", null=True
            ),
        ),
    ]
