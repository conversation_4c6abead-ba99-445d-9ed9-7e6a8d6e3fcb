from django.db import migrations

import deepinsights.core.ml.prompt_templates.followup_email as email_prompt

GB_EMAIL_TEMPLATE = """

Hi [Client Name],

Thanks for your time today. See the notes from our discussion below. Additionally, the slides from our conversation have been uploaded to your <link> [investment portal]( https://griffinblack.portal.tamaracinc.com/Login.aspx?ReturnUrl=%2f ) </link>. Please reach out with any questions.

Actions
·         [Client] Please upload a copy of the following items to the provided secure link.
<link> [Click here to upload secure files]( https://griffinblack-my.sharepoint.com/:f:/p/incomingfiles/ElTnimk4zz9FoWjaFrzIwU0BLjw3-c362WYyGFhbT0Xg1g ) </link>
Tax Returns for 2023
Updated Driver's License
Estate Plan Documents
·         [Client] Sign IPS Refresh when it arrives in your inbox.
·         [Advisor] Send 2025 annual review meeting invite.

Notes
Please see the slides on your investment portal for more specifics on the topics discussed.
Your core portfolio is currently following a “Balanced” strategy with 60% stocks and 40% bonds and alternatives.
The target withdrawal rate from your portfolio is $1,234. You shared that your current withdrawal amount feels comfortable and not constraining so we will continue as is for the next year.
If you would like to leave funds behind for an heir, then we would need to decrease your monthly withdrawals. Take some time to think about this as a goal and let us know if this seems like something you are interested in.
We project your tax rate to increase slightly over time. The difference is not enough to recommend a Roth conversion for this year. However, we will continue to monitor your how your income will affect your tax bracket and Medicare premiums.
Your next Annual Review is scheduled for one year from now ([Month, day], 2025). If for any reason this time is no longer a good fit, please let us know.

Thanks!
[advisor name]
"""


def create_default_email_template(apps, schema_editor):
    MeetingSummaryEmailTemplate = apps.get_model("meetingsapp", "MeetingSummaryEmailTemplate")
    db_alias = schema_editor.connection.alias

    # Create default template
    MeetingSummaryEmailTemplate.objects.using(db_alias).create(
        name="Default Email Template",
        internal_name="default_template",
        email_template_content="",
        system_prompt=email_prompt.SYSTEM_PROMPT,
        generation_prompt=email_prompt.HUMAN_PROMPT,
        description="Default template for meeting summary emails",
        tone="default",
        wordiness="default",
        amount_discussion="default",
        voice="default",
        everyone=True,  # Make it available to everyone by default
    )

    MeetingSummaryEmailTemplate.objects.using(db_alias).create(
        name="Griffin Black Template",
        internal_name="Griffin Black Email Summary Template",
        email_template_content=GB_EMAIL_TEMPLATE,
        system_prompt=email_prompt.SYSTEM_PROMPT,
        generation_prompt=email_prompt.HUMAN_PROMPT,
        description="Default Griffin Black for meeting summary emails, contains hyperlinks",
        tone="default",
        wordiness="default",
        amount_discussion="default",
        voice="default",
        use_html=True,
    )


def remove_default_email_template(apps, schema_editor):
    MeetingSummaryEmailTemplate = apps.get_model("meetingsapp", "MeetingSummaryEmailTemplate")
    db_alias = schema_editor.connection.alias

    # Remove both default templates
    MeetingSummaryEmailTemplate.objects.using(db_alias).filter(
        internal_name__in=["default_template", "default_template_gb"]
    ).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0088_meetingsummaryemailtemplate"),
    ]

    operations = [
        migrations.RunPython(create_default_email_template, remove_default_email_template),
    ]
