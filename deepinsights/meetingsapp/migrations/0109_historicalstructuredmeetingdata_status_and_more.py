# Generated by Django 4.2.18 on 2025-02-14 21:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("meetingsapp", "0108_add_agenda_and_client_insights_prompt_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="historicalstructuredmeetingdata",
            name="status",
            field=models.CharField(
                choices=[
                    ("created", "Created"),
                    ("processing", "Processing"),
                    ("completed", "Completed"),
                    ("failed", "Failed"),
                ],
                default="completed",
                help_text="The status of the structured meeting data instance. This tracks the progress of the LLM data generation process.",
            ),
        ),
        migrations.AddField(
            model_name="structuredmeetingdata",
            name="status",
            field=models.CharField(
                choices=[
                    ("created", "Created"),
                    ("processing", "Processing"),
                    ("completed", "Completed"),
                    ("failed", "Failed"),
                ],
                default="completed",
                help_text="The status of the structured meeting data instance. This tracks the progress of the LLM data generation process.",
            ),
        ),
    ]
