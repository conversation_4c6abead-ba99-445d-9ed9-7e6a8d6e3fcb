# Generated by Django 4.2.16 on 2024-11-16 00:31

from django.db import migrations
from jsonschema import validate

schema = {
    "$id": "https://zeplyn.ai/structured_review_data.schema.json",
    "$schema": "https://json-schema.org/draft/2020-12/schema",
    "description": (
        "Describes the format of structured meeting follow-up data that is generated from a transcript of a meeting."
    ),
    "title": "Structured meeting review data",
    "type": "object",
    "required": [
        "review_entries",
    ],
    "properties": {
        "review_entries": {
            "type": "array",
            "items": {"$ref": "#/$defs/review_entry"},
            "uniqueItems": True,
        },
    },
    "additionalProperties": False,
    "$defs": {
        "evidence_entry": {
            "type": "object",
            "required": ["quote"],
            "properties": {"quote": {"type": "string"}, "timestamp": {"type": "string"}},
            "additionalProperties": False,
        },
        "review_entry": {
            "type": "object",
            "required": ["id", "kind", "topic"],
            "properties": {
                "id": {"type": "string"},
                "kind": {"type": "string", "enum": ["toggle", "select", "multiselect"]},
                "topic": {"type": "string"},
                "options": {"type": "array", "items": {"type": "string"}},
                "discussed": {"type": "boolean"},
                "selected": {"type": "string"},
                "multi_selected": {"type": "array", "items": {"type": "string"}},
                "explanation": {"type": "string"},
                "evidence": {
                    "type": "array",
                    "items": {"$ref": "#/$defs/evidence_entry"},
                },
            },
            "additionalProperties": False,
        },
    },
}

financial_checklist_data = {
    "review_entries": [
        {
            "id": "net_worth",
            "kind": "select",
            "topic": "Net worth",
            "options": ["Under $1,000,000", "$1,000,000 - $5,000,000", "$5,000,000 - $25,000,000", "Over $25,000,000"],
        },
        {
            "id": "liquid_net_worth",
            "kind": "select",
            "topic": "Liquid net worth",
            "options": ["Under $1,000,000", "$1,000,000 - $5,000,000", "$5,000,000 - $25,000,000", "Over $25,000,000"],
        },
        {
            "id": "annual_income",
            "kind": "select",
            "topic": "Annual income",
            "options": ["Under $250,000", "$250,000 - $500,000", "Over $500,000"],
        },
        {
            "id": "time_horizon_for_distribution_start",
            "kind": "select",
            "topic": "Time horizon - distributon start",
            "options": ["Less than 3 years", "3-10 years", "Over 10 years"],
        },
        {
            "id": "time_horizon_for_distribution_continue",
            "kind": "select",
            "topic": "Time horizon - distributon continue",
            "options": ["Less than 3 years", "3-10 years", "Over 10 years"],
        },
        {
            "id": "risk_tolerance",
            "kind": "select",
            "topic": "Current risk tolerance",
            "options": ["High", "Medium", "Low"],
        },
        {
            "id": "federal_tax_bracket",
            "kind": "select",
            "topic": "Federal tax bracket",
            "options": ["10%", "12%", "22%", "24%", "32%", "35%", "37%"],
        },
        {
            "id": "investment_goals",
            "kind": "select",
            "topic": "Investment objectives",
            "options": [
                "Aggressive Growth",
                "Growth Focus",
                "Balanced / Growth & Income",
                "Income Focus",
                "Preservation of Principal",
            ],
        },
        {
            "id": "source_of_income",
            "kind": "multiselect",
            "topic": "Source of income",
            "options": [
                "Earned income",
                "Investment income",
                "Social Security",
                "Other",
            ],
        },
        {
            "id": "prior_investment_experience",
            "kind": "multiselect",
            "topic": "Prior investment experience",
            "options": [
                "Bank Savings, CDs, Money Market Funds",
                "Bonds",
                "Life Insurance",
                "Annuities",
                "Mutual Funds",
                "Stocks",
                "Alternative (REITs, Private Equity, Hedge Funds, etc.)",
                "Other",
            ],
        },
        {
            "id": "source_of_funding",
            "kind": "multiselect",
            "topic": "Source of funding",
            "options": [
                "Income/Saving/Checking",
                "In-Service Distribution",
                "Investment",
                "Gift/Inheritance/Death Proceeds",
                "Employer Sponsored Retirement Plan",
                "Home Equity Credit/Reverse Mortgage",
                "Sale of Business or Property",
                "Life Insurance",
            ],
        },
    ],
}

financial_checklist_context = """Example Transcript:
Advisor    "00:15:20-->00:15:40"
Let's review your current financial position. Your portfolio currently has $500,000 in retirement accounts.

Client    "00:15:40-->00:15:55"
Yes, and I recently inherited $200,000 which I'd like to invest.

Advisor    "00:15:55-->00:16:15"
Your risk tolerance questionnaire from last month indicates moderate risk tolerance.

Advisor    "00:16:15-->00:16:45"
For your time horizon, you mentioned wanting to retire in 15 years.

Client    "00:16:45-->00:17:00"
That's right, I'm aiming to retire at 65.

Advisor    "00:17:00-->00:17:30"
Let's look at your current investment allocation. You're currently at 70% stocks, 30% bonds.

Client    "00:17:30-->00:17:50"
I'm comfortable with that mix given the current market.

Advisor    "00:17:50-->00:18:10"
Your portfolio performance has been strong, up 8% year to date.

Client    "00:18:10-->00:18:25"
That's good to hear. What about insurance coverage?

Advisor    "00:18:25-->00:18:45"
We didn't get into insurance today, let's schedule a follow-up to discuss that specifically.

Example Output:
{
    "review_entries": [
        {
            "id": "net_worth",
            "kind": "select",
            "topic": "Net worth",
            "options": ["Under $1,000,000", "$1,000,000 - $5,000,000", "$5,000,000 - $25,000,000", "Over $25,000,000"],
            "selected": ["Under $1,000,000"],
            "explanation": "Reviewed current portfolio value and recent inheritance",
            "evidence": [
                {
                    "timestamp": "00:15:20",
                    "quote": "Your portfolio currently has $500,000 in retirement accounts"
                },
                {
                    "timestamp": "00:15:40",
                    "quote": "Yes, and I recently inherited $200,000 which I'd like to invest"
                }
            ]   
        },
        {
            "id": "liquid_net_worth",
            "kind": "select",
            "topic": "Liquid net worth",
            "options": ["Under $1,000,000", "$1,000,000 - $5,000,000", "$5,000,000 - $25,000,000", "Over $25,000,000"],
            "selected": ["Under $1,000,000"],
            "explanation": "Reviewed current portfolio value and recent inheritance",
            "evidence": [
                {
                    "timestamp": "00:15:20",
                    "quote": "Your portfolio currently has $500,000 in retirement accounts"
                },
                {
                    "timestamp": "00:15:40",
                    "quote": "Yes, and I recently inherited $200,000 which I'd like to invest"
                }
            ]
        },
        {
            "id": "annual_income",
            "kind": "select",
            "topic": "Annual income",
            "options": ["Under $250,000", "$250,000 - $500,000", "Over $500,000"],
            "selected": "",
            "explanation": "Annual income was not explicitly discussed during the meeting",
        },
        {
            "id": "time_horizon_for_distribution_start",
            "kind": "select",
            "topic": "Time horizon - distributon start",
            "options": ["Less than 3 years", "3-10 years", "Over 10 years"],
            "selected: "",
            "explanation": "Time horizon for distribution start was not explicitly discussed during the meeting",
        },
        {
            "id": "time_horizon_for_distribution_continue",
            "kind": "select",
            "topic": "Time horizon - distributon continue",
            "options": ["Less than 3 years", "3-10 years", "Over 10 years"],
            "selected: "",
            "explanation": "Time horizon for distribution continuation was not explicitly discussed during the meeting",
        },
        {
            "id": "risk_tolerance",
            "kind": "select",
            "topic": "Current risk tolerance",
            "options": ["High", "Medium", "Low"],
            "selected": "Medium",
            "explanation": "Confirmed moderate risk tolerance based on recent questionnaire",
            "evidence": [
                {
                    "timestamp": "00:15:55",
                    "quote": "Your risk tolerance questionnaire from last month indicates moderate risk tolerance"
                }
            ]
        },
        {
            "id": "federal_tax_bracket",
            "kind": "select",
            "topic": "Federal tax bracket",
            "options": ["10%", "12%", "22%", "24%", "32%", "35%", "37%"],
            "selected": "",
            "explanation": "Federal tax bracket was not explicitly discussed during the meeting",
        },
        {
            "id": "investment_goals",
            "kind": "select",
            "topic": "Investment objectives",
            "options": [
                "Aggressive Growth",
                "Growth Focus",
                "Balanced / Growth & Income",
                "Income Focus",
                "Preservation of Principal",
            ],
            "selected": "",
            "explanation": "Investment objectives were not explicitly discussed during the meeting",
        },
        {
            "id": "source_of_income",
            "kind": "multiselect",
            "topic": "Source of income",
            "options": [
                "Earned income",
                "Investment income",
                "Social Security",
                "Other",
            ],
            "multi_selected": [],
            "explanation": "Source of income was not explicitly discussed during the meeting",
        },
        {
            "id": "prior_investment_experience",
            "kind": "multiselect",
            "topic": "Prior investment experience",
            "options": [
                "Bank Savings, CDs, Money Market Funds",
                "Bonds",
                "Life Insurance",
                "Annuities",
                "Mutual Funds",
                "Stocks",
                "Alternative (REITs, Private Equity, Hedge Funds, etc.)",
                "Other",
            ],
            "multi_selected": [],
            "explanation": "Prior investment experience was not explicitly discussed during the meeting",
        },
        {
            "id": "source_of_funding",
            "kind": "multiselect",
            "topic": "Source of funding",
            "options": [
                "Income/Saving/Checking",
                "In-Service Distribution",
                "Investment",
                "Gift/Inheritance/Death Proceeds",
                "Employer Sponsored Retirement Plan",
                "Home Equity Credit/Reverse Mortgage",
                "Sale of Business or Property",
                "Life Insurance",
            ],
            "multi_selected": ["Gift/Inheritance/Death Proceeds"],
            "explanation": "Reviewed recent inheritance as a source of funding",
            "evidence": [
                {
                    "timestamp": "00:15:40",
                    "quote": "Yes, and I recently inherited $200,000 which I'd like to invest"
                }
            ]
        },
    ],
}
"""

financial_checklist_prompt_text = """You are a compliance auditor for financial advisors, analyzing meeting transcripts to verify if financial review checklist topics were discussed
and determine whether each required topic was adequately covered, providing detailed evidence from the transcript. You will either need to select one option that
most closely matches the discussion from a list of options; or select multiple options that match the discussion from a list of options.

Evaluation Guidelines:
1. Include exact quotes with timestamps as evidence
2. For each topic discussed, provide a brief explanation of what was covered
3. For topics not covered, provide an explanation that it was not discussed
4. Do not make assumptions about implied discussions
5. Use the start time from the timestamp range as reference
6. If a topic is a "select" kind, select the option that most closely matches the discussion. Leave empty if no options match or you are unsure.
7. If a topic is a "multiselect" kind, select all options that match the discussion. Leave empty if no options match or you are unsure.

{example}

For each compliance topic, your output must include:
1. An "id" field matching the provided schema
2. A "topic" field matching the provided schema
3. An "explanation" field summarizing what was discussed or noting if it wasn't covered
4. An "evidence" array containing relevant quotes with timestamps (use start time from timestamp range)
5. If the "kind" of the topic is "select", include the "options" array copied from the input data
6. If the "kind" of the topic is "select" and the topic was discussed, include the "selected" option, populated with the value in "options" that most closely matches the discussion. If you are unsure, or no option matches, leave this field empty.
7. If the "kind" of the topic is "multiselect", include the "options" array copied from the input data
8. If the "kind" of the topic is "multiselect" and the topic was discussed, include the "multiselected" option, populated with each value in "options" that matches the discussion. If you are unsure, or no option matches, leave this field as an empty array.

Please analyze the following meeting transcript and determine if each required financial review checklist topic adequately discussed. Provide your analysis in the exact format specified above.

Required Schema:
{schema}

Initial Data Structure:
{data}

Transcript:
{transcript}

Analyze the transcript and provide results in the exact format specified, ensuring all required fields are present and maintaining the specified order of topics.
"""

relationship_checklist_data = {
    "review_entries": [
        {
            "id": "relationship_status",
            "kind": "select",
            "topic": "Relationship Status",
            "options": ["Below average", "Average", "Above average"],
        },
        {
            "id": "relationship_complexity",
            "kind": "select",
            "topic": "Relationship Complexity",
            "options": ["Low", "Average", "High"],
        },
        {
            "id": "activity_communications_level",
            "kind": "select",
            "topic": "Activity/Communication Level",
            "options": ["Low", "Average", "High"],
        },
        {
            "id": "revenue_potential",
            "kind": "select",
            "topic": "Revenue potential",
            "options": ["Low", "Moderate", "High"],
            "selected": "High",
        },
    ]
}

relationship_checklist_context = """Example Transcript:
Advisor    "00:15:20-->00:15:40"
Let's review your current financial position. Your portfolio currently has $500,000 in retirement accounts.

Client    "00:15:40-->00:15:55"
Yes, and I recently inherited $200,000 which I'd like to invest.

Advisor    "00:15:55-->00:16:15"
Your risk tolerance questionnaire from last month indicates moderate risk tolerance.

Advisor    "00:16:15-->00:16:45"
For your time horizon, you mentioned wanting to retire in 15 years.

Client    "00:16:45-->00:17:00"
That's right, I'm aiming to retire at 65.

Advisor    "00:17:00-->00:17:30"
Let's look at your current investment allocation. You're currently at 70% stocks, 30% bonds.

Client    "00:17:30-->00:17:50"
I'm comfortable with that mix given the current market.

Advisor    "00:17:50-->00:18:10"
Your portfolio performance has been strong, up 8% year to date.

Client    "00:18:10-->00:18:25"
That's good to hear. What about insurance coverage?

Advisor    "00:18:25-->00:18:45"
We didn't get into insurance today, let's schedule a follow-up to discuss that specifically.

Example Output:
{
    "review_entries": [
        {
            "id": "relationship_status",
            "kind": "select",
            "topic": "Relationship Status",
            "options": ["Below average", "Average", "Above average"],
            "selected": "Average",
        },
        {
            "id": "relationship_complexity",
            "kind": "select",
            "topic": "Relationship Complexity",
            "options": ["Low", "Average", "High"],
            "selected": "Average",
        },
        {
            "id": "activity_communications_level",
            "kind": "select",
            "topic": "Activity/Communication Level",
            "options": ["Low", "Average", "High"],
            "selected": "Average",
        },
        {
            "id": "revenue_potential",
            "kind": "select",
            "topic": "Revenue potential",
            "options": ["Low", "Moderate", "High"],
            "selected": "High",
        },
    ]
}
"""

relationship_checklist_prompt_text = """You are a meeting analyst, tasked with the job of determining information about a client's relationship to your financial firm. You will be given a transcript of a meeting between a financial advisor and their client/clients. Based on the information in this meeting transcript, you need to make a determination about the client's relationship to the firm, on several different dimensions. These may include dimensions such as:
- the strength of the client's attachment to the firm
- the potential for the firm to make revenue from the customer
- the complexity of managing the relationship with the client.

Evaluation Guidelines:

1. Include exact quotes with timestamps as evidence
2. You will need to develop a deep understanding of the client based on the transcript; it is not likely that specific words or phrases will provide a conclusive answer, but the overall context of the transcript may.
3. You may need to infer the client's feelings and emotions in order to make determinations.
4. Select the option that most closely matches the discussion. If you don't have a good guess, then pick one of the middle options.
5. For each topic discussed, provide a brief explanation of what was covered
6. For topics not covered, provide an explanation that it was not discussed
7. Use the start time from the timestamp range as reference
8. If a topic is a "select" kind, select the option that most closely matches the discussion. Leave empty if no options match or you are unsure.
9. If a topic is a "multiselect" kind, select all options that match the discussion. Leave empty if no options match or you are unsure.

{example}

For each relationship topic, your output must include:
1. An "id" field matching the provided schema
2. A "topic" field matching the provided schema
3. An "explanation" field summarizing what was discussed or noting if it wasn't covered
4. An "evidence" array containing relevant quotes with timestamps (use start time from timestamp range)
5. If the "kind" of the topic is "select", include the "options" array copied from the input data
6. If the "kind" of the topic is "select" and the topic was discussed, include the "selected" option, populated with the value in "options" that most closely matches the discussion. If you are unsure, or no option matches, leave this field empty.
7. If the "kind" of the topic is "multiselect", include the "options" array copied from the input data
8. If the "kind" of the topic is "multiselect" and the topic was discussed, include the "multiselected" option, populated with each value in "options" that matches the discussion. If you are unsure, or no option matches, leave this field as an empty array.

Please analyze the following meeting transcript and determine the most accurate relationship status option for each of the provided topics. Provide your analysis in the exact format specified above.

Required Schema:
{schema}

Initial Data Structure:
{data}

Transcript:
{transcript}

Analyze the transcript and provide results in the exact format specified, ensuring all required fields are present and maintaining the specified order of topics."""


def create_annual_review_follow_ups(apps, schema_editor):
    MeetingFollowUpTemplate = apps.get_model("meetingsapp", "MeetingFollowUpTemplate")
    Prompt = apps.get_model("meetingsapp", "Prompt")
    db_alias = schema_editor.connection.alias

    # Update the compliance template to use the updated schema format.
    compliance_template = MeetingFollowUpTemplate.objects.get(kind="compliance_checklist")
    compliance_template.schema = schema
    data = compliance_template.initial_data
    if entries := data.get("compliance_checks"):
        data["review_entries"] = entries
        del data["compliance_checks"]
    compliance_template.initial_data = data

    validate(compliance_template.initial_data, compliance_template.schema)
    compliance_template.save()

    financial_checklist_prompt = Prompt.objects.using(db_alias).create(
        name="Financial checklist (annual review)",
        text=financial_checklist_prompt_text,
    )
    MeetingFollowUpTemplate.objects.using(db_alias).create(
        title="Financial Review Checklist",
        kind="financial_review_checklist",
        schema=schema,
        initial_data=financial_checklist_data,
        context=financial_checklist_context,
        prompt=financial_checklist_prompt,
    )

    relationship_checklist_prompt = Prompt.objects.using(db_alias).create(
        name="Relationship checklist",
        text=relationship_checklist_prompt_text,
    )
    MeetingFollowUpTemplate.objects.using(db_alias).create(
        title="Relationship Checklist",
        kind="relationship_checklist",
        schema=schema,
        initial_data=relationship_checklist_data,
        context=relationship_checklist_context,
        prompt=relationship_checklist_prompt,
    )


def remove_annual_review_follow_ups(apps, schema_editor):
    MeetingFollowUpTemplate = apps.get_model("meetingsapp", "MeetingFollowUpTemplate")
    db_alias = schema_editor.connection.alias

    MeetingFollowUpTemplate.objects.using(db_alias).filter(kind="financial_review_checklist").delete()
    MeetingFollowUpTemplate.objects.using(db_alias).filter(kind="relationship_checklist").delete()


class Migration(migrations.Migration):
    dependencies = [("meetingsapp", "0075_alter_historicalprompt_name_and_more")]

    operations = [
        migrations.RunPython(
            create_annual_review_follow_ups,
            remove_annual_review_follow_ups,
        ),
    ]
