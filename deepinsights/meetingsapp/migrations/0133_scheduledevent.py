# Generated by Django 4.2.20 on 2025-05-08 00:34

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0132_alter_oauthcredentials_integration"),
    ]

    operations = [
        migrations.CreateModel(
            name="ScheduledEvent",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.<PERSON><PERSON>anField(default=False)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ("start_time", models.DateTimeField(help_text="The start time of the event.")),
                ("end_time", models.DateTimeField(help_text="The end time of the event.")),
                (
                    "user_specific_source_id",
                    models.CharField(
                        help_text="The user-specific identifier of the event in the source system which provided it (likely a calendar provider). This is distinct from the shared source ID because calendar systems can have representations of events that are specific to each user, and we may want to store information about an event for a specific user rather than generically across all users who have the same event.",
                        max_length=255,
                    ),
                ),
                (
                    "shared_source_id",
                    models.CharField(
                        blank=True,
                        help_text="The shared/common identifier of the event in the source system which provided it (likely a calendar provider). This identifier is meant to be the same across all ScheduledEvent instances when the underlying event is the same (within reason; we don't expect to be able to unify the same real-world event across different calendar systems or unify two different calendar events in a calendar system that match one real-world event but don't have any shared identifier in the calendar system). This is distinct from the user source ID because calendar systems can have representations of events that are specific to each user, and we may want to store information about an event for a specific user rather than generically across all users who have the same event.",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "source_data",
                    models.JSONField(
                        blank=True,
                        help_text="The data from the source system which provided this event (i.e., the calendar provider or CRM).",
                        null=True,
                    ),
                ),
                (
                    "autojoin_behavior",
                    models.CharField(
                        choices=[("default", "Default"), ("enabled", "Enabled"), ("disabled", "Disabled")],
                        default="default",
                        help_text="The user-determined autojoin behavior for this event. Note that this value on a single scheduled event instance by itself does not indicate whether a bot will automatically join the the meeting. It's possible that another user attending the same meeting has enabled autojoin for this meeting, and so a bot will join the meeting.",
                        max_length=10,
                    ),
                ),
                (
                    "note",
                    models.ForeignKey(
                        blank=True,
                        help_text="The note this scheduled event is associated with.",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="scheduled_events",
                        to="meetingsapp.note",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="The user who this scheduled event is associated with.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="scheduled_events",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("user", "user_specific_source_id")},
            },
        ),
    ]
