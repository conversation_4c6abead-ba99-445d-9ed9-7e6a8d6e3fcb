# Generated by Django 4.2.18 on 2025-04-19 09:56

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0128_oauthaccesstoken_oauthapplication_oauthrefreshtoken_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserImpersonation",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.Bo<PERSON>anField(default=False)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                (
                    "impersonated_email",
                    models.EmailField(help_text="The email of the impersonated user", max_length=254),
                ),
                (
                    "impersonating_email",
                    models.EmailField(help_text="The email of the impersonating user", max_length=254),
                ),
                ("access_token", models.TextField(help_text="The access token for the impersonation")),
                ("purpose", models.TextField(help_text="The purpose of the impersonation")),
                (
                    "user_impersonated",
                    models.ForeignKey(
                        help_text="The user to be impersonated",
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="user_impersonated",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user_impersonating",
                    models.ForeignKey(
                        help_text="The user impersonating",
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="user_impersonation",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
