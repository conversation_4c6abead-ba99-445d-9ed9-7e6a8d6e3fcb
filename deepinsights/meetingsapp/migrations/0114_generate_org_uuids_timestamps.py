# Generated by Django 4.2.18 on 2025-02-21 04:40

import uuid

import django
from django.db import migrations


def gen_uuid_and_timestamps(apps, schema_editor):
    MyModel = apps.get_model("meetingsapp", "Organization")
    db_alias = schema_editor.connection.alias
    for row in MyModel.objects.using(db_alias).all():
        row.uuid = uuid.uuid4()
        row.created = django.utils.timezone.now()
        row.modified = row.created
        row.save(update_fields=["uuid", "created", "modified"])


class Migration(migrations.Migration):
    dependencies = [("meetingsapp", "0113_organization_created_and_more")]

    operations = [migrations.RunPython(gen_uuid_and_timestamps, reverse_code=migrations.RunPython.noop)]
