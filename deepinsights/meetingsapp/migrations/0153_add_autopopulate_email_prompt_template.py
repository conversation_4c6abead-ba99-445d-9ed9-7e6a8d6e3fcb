from django.db import migrations

system_prompt = """
You are a financial advisor's assistant tasked with drafting follow-up emails after client meetings. Your role is to create personalized, compliant emails based on meeting summaries and client preferences.
"""
generation_prompt = """
You are an expert AI assistant for wealth managers and financial advisors. Your task is to draft a concise, client-facing follow-up email.

Your most important instruction is to analyze the email_template examples and perfectly replicate their style, tone, and formatting. The final email you generate must look like it was written by the same person who wrote the examples.

Step-by-Step Process:

Analyze Template Style: Study the email_template examples to master the required structure, including the greeting, closing, sentence structure (e.g., paragraphs vs. bullet points), and the use of formatting like bolded headers.

Extract Information: Carefully read the entire meeting summary provided in the context to identify all key discussion points, decisions, and action items.

Draft the Email: Write the new email by applying the style from the templates to the information from the context.

CRUCIAL SAFETY GUIDELINES: You must strictly follow these rules.

NO HALLUCINATION - STICK TO THE SOURCE: The content of the email must be based exclusively on the information provided in the context. Do not add, assume, or invent any facts, details, topics, or action items that are not explicitly mentioned in the meeting summary. If it's not in the context, it does not go in the email.

NO OMISSION - BE COMPREHENSIVE: Ensure that all significant points, key decisions, and actionable tasks mentioned in the context are accurately included in the final email. Do not leave out critical information that the client needs to know.

Final Output Requirements:

Format with Markdown: Your entire output must be a single block of text formatted in Markdown. When you replicate formatting from the templates (like bolded text or bulleted lists), you must use the correct Markdown syntax (e.g., **bold text**, - list item).

Email Body Only: Generate only the email body. Do not include a "Subject:" line.

Handle Missing Client Name: If the client name doesn't exist or is empty, write the email greeting as Hi [Client name(s)].

No Extra Messages: Do not output any message, explanation, or text other than the final Markdown email body itself.

Do not say things such as "here is the response based on provided context"

context:
{context}

email_template: {email_template}"""

email_template = """Using the provided context, write a follow-up email to the client summarizing the meeting.

Email Structure:

Hi [Client First Names],

It was great meeting with you. [Insert a brief, personal sentence related to the meeting or a personal connection if appropriate, otherwise omit.]

Below is a quick recap of what we discussed, along with our key next steps:

**[Section Title 1]**

* [Summary point 1.]
* [Summary point 2.]

**[Section Title 2]**

* [Summary point 1.]
* [Summary point 2.]
(...and additional sections as needed for each topic discussed)

**Action Items**

**[Client Name(s)]:**
* [Action item for the client.]
* [Another action item for the client, if applicable.]

**[Advisor Name]:**
* [Action item for the advisor.]
* [Another action item for the advisor, if applicable.]

Please let me know if I missed anything or if you have any questions.

All the best,

[Advisor Name]

Example of a Generated Email (Based on the Template):

Hi Terri & Denise,

It was great meeting with you both today—thank you for taking the time to catch up with Emily and me.

Below is a quick recap of what we discussed, along with the key action items we'll be moving forward with:

**Personal Updates**

* So glad to hear you enjoyed your trip to Italy—Thelma's 11th country!

**Thelma's School & Upcoming Tuition**

* Thelma is set to start 6th grade this fall, with tuition due in June.
* Estimated annual cost: $46K, to be paid as a lump sum.
* The current plan is to pull:
    * $36K from the $166K temporary cash restriction in SNOXX
    * $10K from the 529 plan
* (Final number pending confirmation—we'll hold off on moving funds until you confirm.)

**Cash Flow & Income**

* Denise's base salary remains at $250K, but overall income will be lower this year due to the loss of a major client and no company profits.
* You also loaned $60K to the firm, which will be repaid, but will not contribute to current-year income.

**Emergency Savings & Near-Term Plans**

* Emergency savings are currently around $166K, comfortably within your target range of $150K-$200K.
* The home remodel is already paid for, and while there's a small chance of capital going into a future venture, you're feeling stable and not anticipating needing to act on that in the near term.

**Investment & Market Outlook**

* Your current equity strategy remains appropriate, with 9% in cash positioned to be deployed opportunistically during market volatility.
* We'll continue to monitor market conditions and look for opportunities to upgrade the portfolio.

**Action Items**

**Terri & Denise:**
* Please upload all insurance documents to the portal so we can review and assess coverage adequacy.
* Confirm Thelma's tuition amount once finalized.

**Summitry Advanced Plan Edits (for Will Berg):**
* Add an early retirement scenario.
* Add another scenario in which Denise's income diminishes over the her last 10 working years.

Please let me know if I missed anything or if you have any questions.

All the best,

Will Berg
"""


def create_autopopulate_email_prompt_template(apps, schema_editor):
    """Create a template record that will be used to auto-populate the generation prompt field."""
    MeetingSummaryEmailTemplate = apps.get_model("meetingsapp", "MeetingSummaryEmailTemplate")
    db_alias = schema_editor.connection.alias

    MeetingSummaryEmailTemplate.objects.using(db_alias).create(
        name="Auto-populate Email Prompt Template",
        internal_name="autopopulate_email_prompt",
        system_prompt=system_prompt,
        generation_prompt=generation_prompt,
        email_template_content="",
        description="This template is used to auto-populate the generation prompt field when creating new email templates. It should not be used for actual email generation.",
        everyone=False,  # Not available to users, only for admin use
    )


def remove_autopopulate_email_prompt_template(apps, schema_editor):
    """Remove the auto-populate email prompt template."""
    MeetingSummaryEmailTemplate = apps.get_model("meetingsapp", "MeetingSummaryEmailTemplate")
    db_alias = schema_editor.connection.alias

    MeetingSummaryEmailTemplate.objects.using(db_alias).filter(internal_name="autopopulate_email_prompt").delete()


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0152_scheduledevent_attendee_matched_participants"),
    ]

    operations = [
        migrations.RunPython(
            create_autopopulate_email_prompt_template,
            remove_autopopulate_email_prompt_template,
        ),
    ]
