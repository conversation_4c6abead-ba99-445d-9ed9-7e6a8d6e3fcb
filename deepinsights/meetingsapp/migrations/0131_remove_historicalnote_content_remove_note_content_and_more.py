# Generated by Django 4.2.20 on 2025-05-02 02:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("meetingsapp", "0130_alter_oauthcredentials_options_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="historicalnote",
            name="content",
        ),
        migrations.RemoveField(
            model_name="note",
            name="content",
        ),
        migrations.AddField(
            model_name="historicalnote",
            name="data_source",
            field=models.CharField(
                blank=True,
                choices=[
                    ("recall", "Recall"),
                    ("twilio", "Twilio"),
                    ("audio_file", "Audio File"),
                    ("audio_buffers", "Audio Buffers"),
                ],
                max_length=255,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="note",
            name="data_source",
            field=models.Char<PERSON>ield(
                blank=True,
                choices=[
                    ("recall", "Recall"),
                    ("twilio", "<PERSON>wilio"),
                    ("audio_file", "Audio File"),
                    ("audio_buffers", "Audio Buffers"),
                ],
                max_length=255,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="historicalnote",
            name="note_type",
            field=models.Char<PERSON>ield(
                blank=True,
                choices=[("meeting_recording", "meeting_recording"), ("voice_memo", "voice_memo")],
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="note",
            name="note_type",
            field=models.<PERSON>rField(
                blank=True,
                choices=[("meeting_recording", "meeting_recording"), ("voice_memo", "voice_memo")],
                null=True,
            ),
        ),
    ]
