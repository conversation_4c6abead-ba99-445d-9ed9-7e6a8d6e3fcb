# Generated by Django 4.2 on 2024-05-07 19:57

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import uuid


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0034_new_summary_20240430_2229"),
    ]

    operations = [
        migrations.CreateModel(
            name="MeetingBot",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_active", models.<PERSON>olean<PERSON>ield(default=True)),
                ("is_deleted", models.<PERSON>oleanField(default=False)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ("recall_bot_id", models.CharField(blank=True, null=True)),
                ("start_time", models.DateTimeField(blank=True, null=True)),
                ("end_time", models.DateTimeField(blank=True, null=True)),
                ("meeting_link", models.CharField(blank=True, null=True)),
                ("recording_start_time", models.DateTimeField(blank=True, null=True)),
                ("recording_end_time", models.DateTimeField(blank=True, null=True)),
                ("processing_start_time", models.DateTimeField(blank=True, null=True)),
                ("processing_end_time", models.DateTimeField(blank=True, null=True)),
                ("metadata", models.JSONField(blank=True, null=True)),
                (
                    "bot_owner",
                    models.ForeignKey(
                        blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
                    ),
                ),
                (
                    "note",
                    models.ForeignKey(
                        blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to="meetingsapp.note"
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
