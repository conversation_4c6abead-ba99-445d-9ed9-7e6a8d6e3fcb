# Generated by Django 4.2.20 on 2025-04-30 00:07

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("meetingsapp", "0129_userimpersonation"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="oauthcredentials",
            options={"verbose_name": "OAuth user credential", "verbose_name_plural": "OAuth user credentials"},
        ),
        migrations.CreateModel(
            name="OAuthClientCredentials",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ("provider", models.CharField(choices=[("microsoft", "Microsoft")], max_length=50)),
                ("tenant_id", models.CharField(max_length=255)),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="oauth_client_credentials",
                        to="meetingsapp.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "OAuth client credential",
                "verbose_name_plural": "OAuth client credentials",
                "unique_together": {("organization", "provider")},
            },
        ),
    ]
