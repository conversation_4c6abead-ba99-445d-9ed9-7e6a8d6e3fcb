# Generated by Django 4.2 on 2024-04-22 20:53

from django.db import migrations


def add_authorized_users(apps, schema_editor):
    """
    Adds the Author object in Book.author to the
    many-to-many relationship in Book.authors
    """
    Note = apps.get_model("meetingsapp", "Note")

    for note in Note.objects.defer(
        "raw_asr_response",
        "diarized_trans_with_names",
        "summary_by_topics",
        "raw_transcript",
        "advisor_notes",
        "key_takeaways",
    ):
        note.authorized_users.add(note.note_owner)
        note.save()


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0031_note_authorized_users"),
    ]

    operations = [
        migrations.RunPython(add_authorized_users),
    ]
