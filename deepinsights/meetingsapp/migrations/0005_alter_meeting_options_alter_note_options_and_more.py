# Generated by Django 4.2 on 2023-10-06 03:40
import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('meetingsapp', '0004_remove_meeting_notes'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='meeting',
            options={},
        ),
        migrations.AlterModelOptions(
            name='note',
            options={},
        ),
        migrations.AlterModelOptions(
            name='recording',
            options={},
        ),
        migrations.AlterModelOptions(
            name='task',
            options={},
        ),
        migrations.AddField(
            model_name='note',
            name='advisorNotes',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(blank=True, null=True), blank=True, null=True, size=None),
        ),
        migrations.AddField(
            model_name='task',
            name='note',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='meetingsapp.note'),
        ),
    ]
