# Generated by Django 4.2 on 2024-09-25 10:53

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0059_alter_oauthcredentials_integration"),
    ]

    operations = [
        migrations.AddField(
            model_name="historicalorganization",
            name="transcript_ttl",
            field=models.IntegerField(
                blank=True,
                default=None,
                help_text="Number of days to keep transcripts. Leave empty for indefinite storage.",
                null=True,
                validators=[django.core.validators.MinValueValidator(1)],
                verbose_name="Transcript Time-to-Live (days)",
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="transcript_ttl",
            field=models.IntegerField(
                blank=True,
                default=None,
                help_text="Number of days to keep transcripts. Leave empty for indefinite storage.",
                null=True,
                validators=[django.core.validators.MinValueValidator(1)],
                verbose_name="Transcript Time-to-Live (days)",
            ),
        ),
    ]
