# Generated by Django 4.2.18 on 2025-04-14 19:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import phonenumber_field.modelfields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0124_client_owner"),
    ]

    operations = [
        migrations.CreateModel(
            name="PhoneNumber",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                (
                    "type",
                    models.CharField(
                        blank=True,
                        choices=[("mobile", "Mobile"), ("home", "Home"), ("office", "Office"), ("other", "Other")],
                        help_text="What type of phone number this is",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "primary",
                    models.BooleanField(
                        default=False, help_text="Whether this is the primary phone number for the related entity"
                    ),
                ),
                (
                    "number",
                    phonenumber_field.modelfields.PhoneNumberField(
                        blank=True,
                        help_text="The phone number.\n\nThis field is somewhat intelligent: if you pass in an invalid phone number it will return an error. If you provide a valid US phone number (without country code), it will automatically add the country code. If you provide in a valid phone number with a country code, it will use that country code.\n\nNote that the number is stored and rendered in E.164 format (e.g., +12125551212), regardless of how you enter it.",
                        max_length=128,
                        null=True,
                        region=None,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="phone_numbers",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.AddConstraint(
            model_name="phonenumber",
            constraint=models.UniqueConstraint(
                condition=models.Q(("primary", True)),
                fields=("user",),
                name="unique_user_primary_phone_number",
                violation_error_message="A user can only have one primary phone number.",
            ),
        ),
    ]
