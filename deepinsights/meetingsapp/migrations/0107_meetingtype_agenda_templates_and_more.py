# Generated by Django 4.2.18 on 2025-02-12 23:58

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import simple_history.models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0106_audiobuffer"),
    ]

    operations = [
        migrations.AddField(
            model_name="meetingtype",
            name="agenda_templates",
            field=models.ManyToManyField(
                blank=True,
                help_text="The agenda templates that are associated with this meeting type.",
                related_name="agenda_templates",
                to="meetingsapp.structuredmeetingdatatemplate",
            ),
        ),
        migrations.CreateModel(
            name="HistoricalClientInteraction",
            fields=[
                ("id", models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("uuid", models.UUIDField(db_index=True, default=uuid.uuid4, editable=False)),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")], max_length=1),
                ),
                (
                    "agenda",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="meetingsapp.structuredmeetingdata",
                    ),
                ),
                (
                    "client_prep",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="meetingsapp.structuredmeetingdata",
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "meeting_type",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="The type of the meeting associated with this interaction.",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="meetingsapp.meetingtype",
                    ),
                ),
                (
                    "note",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="The note associated with this interaction.",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="meetingsapp.note",
                    ),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        help_text="The user who owns this interaction.",
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical client interaction",
                "verbose_name_plural": "historical client interactions",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="ClientInteraction",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                (
                    "agenda",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to="meetingsapp.structuredmeetingdata",
                    ),
                ),
                (
                    "client_prep",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to="meetingsapp.structuredmeetingdata",
                    ),
                ),
                (
                    "clients",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The clients who are involved in this interaction.",
                        related_name="+",
                        to="meetingsapp.client",
                    ),
                ),
                (
                    "meeting_type",
                    models.ForeignKey(
                        help_text="The type of the meeting associated with this interaction.",
                        on_delete=django.db.models.deletion.PROTECT,
                        to="meetingsapp.meetingtype",
                    ),
                ),
                (
                    "note",
                    models.OneToOneField(
                        blank=True,
                        help_text="The note associated with this interaction.",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="meetingsapp.note",
                    ),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        help_text="The user who owns this interaction.",
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
