# Generated by Django 4.2.21 on 2025-05-28 21:39

from django.db import migrations


def remove_all_but_newest_scheduled_events_from_notes(apps, schema_editor):
    Note = apps.get_model("meetingsapp", "Note")

    for note in Note.objects.filter(scheduled_events__isnull=False):
        scheduled_events = note.scheduled_events.order_by("-created")
        if scheduled_events.count() <= 1:
            continue
        first_event = scheduled_events.first()
        scheduled_events.exclude(id=first_event.id).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0141_alter_historicalprompt_user_prompt_and_more"),
    ]

    operations = [
        migrations.RunPython(
            code=remove_all_but_newest_scheduled_events_from_notes, reverse_code=migrations.RunPython.noop
        )
    ]
