# Generated by Django 4.2.22 on 2025-06-30 21:07

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0150_client_assets_under_management_client_is_priority_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="scheduledevent",
            name="attendee_matched_participants",
            field=models.JSONField(
                blank=True,
                help_text="A cache used to store calendar event attendees that have been matched to Zeplyn users or clients. If present it will be be a list of EventParticipant objects. This is used to avoid having to re-match participants every time the event is fetched (which is an expensive operation because it requires scanningthe entirety of the organizations' users and clients to find matches). ",
                null=True,
            ),
        ),
    ]
