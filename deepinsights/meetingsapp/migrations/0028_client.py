# Generated by Django 4.2 on 2024-03-14 03:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import uuid


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0027_alter_historicalorganization_crm_configuration_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Client",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_active", models.<PERSON>olean<PERSON>ield(default=True)),
                ("is_deleted", models.<PERSON><PERSON>anField(default=False)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ("name", models.CharField()),
                ("first_name", models.CharField(null=True)),
                ("last_name", models.CharField(null=True)),
                ("job_title", models.CharField(null=True)),
                ("email", models.EmailField(max_length=254, null=True)),
                ("crm_id", models.CharField(blank=True, max_length=100, null=True)),
                ("client_type", models.CharField(default="individual", max_length=100)),
                ("crm_system", models.CharField(default="redtail", max_length=100)),
                (
                    "advisor",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
                ),
                (
                    "organization",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="meetingsapp.organization"),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
