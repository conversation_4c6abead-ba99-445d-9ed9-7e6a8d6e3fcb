# Generated by Django 4.2 on 2024-11-08 19:27

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0067_historicalnote_meeting_type_meetingtype_category_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="agenda",
            name="context",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="agendatemplate",
            name="context",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="meetingfollowup",
            name="context",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="meetingfollowuptemplate",
            name="context",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="meetingtype",
            name="context",
            field=models.TextField(blank=True, null=True),
        ),
    ]
