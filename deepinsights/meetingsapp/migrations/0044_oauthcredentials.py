# Generated by Django 4.2 on 2024-05-31 22:32

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import uuid


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0043_alter_attendee_client_alter_attendee_user"),
    ]

    operations = [
        migrations.CreateModel(
            name="OAuthCredentials",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ("access_token", models.CharField(default="")),
                ("refresh_token", models.CharField(default="")),
                ("expires_in", models.DateTimeField()),
                ("created_at", models.DateTimeField(null=True)),
                ("refresh_token_expires_in", models.DateTimeField()),
                ("scope", models.CharField(default="")),
                ("integration", models.CharField(choices=[("wealthbox", "wealthbox")], default="")),
                (
                    "user",
                    models.ForeignKey(
                        null=True, on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
