# Generated by Django 4.2.18 on 2025-02-21 04:40

import uuid

import django.utils.timezone
import model_utils.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0112_audiobuffer_mime_type_audiobuffer_nonce"),
    ]

    operations = [
        migrations.AddField(
            model_name="organization",
            name="created",
            field=model_utils.fields.AutoCreatedField(
                default=django.utils.timezone.now,
                editable=False,
                verbose_name="created",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="modified",
            field=model_utils.fields.AutoLastModifiedField(
                default=django.utils.timezone.now,
                editable=False,
                verbose_name="modified",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="uuid",
            field=models.UUIDField(default=uuid.uuid4, editable=False, null=True),
        ),
    ]
