# Generated by Django 4.2.22 on 2025-06-10 17:10

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("meetingsapp", "0146_historicalnote_crm_sync_targets_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="scheduledevent",
            name="removed_by_user",
            field=models.BooleanField(
                default=False,
                help_text="Whether or not this scheduled event has been removed by the user. This is distinct from removed_from_provider, which indicates whether the event has been removed from the source system (e.g., a calendar provider).",
            ),
        ),
        migrations.AddField(
            model_name="scheduledevent",
            name="removed_from_provider",
            field=models.BooleanField(
                default=False,
                help_text="Whether or not this scheduled event has been removed from/deleted from the provider. Note that this does not mean that the event has necessarily been completely deleted from the provider; it may have been moved forward beyond the lookforward interval for synchronization, in which case it will be un-removed when it falls back within the lookforward interval.",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="scheduledevent",
            name="note",
            field=models.OneToOneField(
                blank=True,
                help_text="The note this scheduled event is associated with.",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="_scheduled_event",
                to="meetingsapp.note",
            ),
        ),
    ]
