# Generated by Django 4.2.16 on 2024-12-11 19:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("meetingsapp", "0082_alter_historicalstructuredmeetingdata_data_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="meetingtype",
            name="meeting_follow_up_templates",
            field=models.ManyToManyField(
                blank=True,
                help_text="Templates for follow-ups that should be associated with this meeting. For any of these that are selected, a follow-up will be generated by the LLM when the meeting is processed.",
                to="meetingsapp.structuredmeetingdatatemplate",
            ),
        ),
    ]
