# Generated by Django 4.2.18 on 2025-02-24 18:59

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("meetingsapp", "0109_historicalstructuredmeetingdata_status_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="clientinteraction",
            name="advisor_notes",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="+",
                to="meetingsapp.structuredmeetingdata",
            ),
        ),
        migrations.AddField(
            model_name="historicalclientinteraction",
            name="advisor_notes",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                to="meetingsapp.structuredmeetingdata",
            ),
        ),
    ]
