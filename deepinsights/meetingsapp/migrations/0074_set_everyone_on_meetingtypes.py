# Generated by Django 4.2.16 on 2024-11-15 18:42

from django.db import migrations


def update_everyone_on_meeting_types(apps, schema_editor):
    MeetingType = apps.get_model("meetingsapp", "MeetingType")
    db_alias = schema_editor.connection.alias

    # The annual reivew Sequoia meeting is not available to everyone.
    annual_review_sequoia = MeetingType.objects.using(db_alias).get(key="annual_review_sequoia")
    annual_review_sequoia.everyone = False
    annual_review_sequoia.save()

    # The other meetings are available to everyone.
    for m in MeetingType.objects.using(db_alias).filter(key__in=["client", "debrief", "internal"]):
        m.everyone = True
        m.save()


def reverse_update_everyone_on_meeting_types(apps, schema_editor):
    MeetingType = apps.get_model("meetingsapp", "MeetingType")
    db_alias = schema_editor.connection.alias

    for m in MeetingType.objects.using(db_alias).all():
        m.everyone = False
        m.save()


class Migration(migrations.Migration):
    dependencies = [("meetingsapp", "0073_meetingtype_everyone")]
    operations = [
        migrations.RunPython(
            update_everyone_on_meeting_types,
            reverse_code=reverse_update_everyone_on_meeting_types,
        ),
    ]
