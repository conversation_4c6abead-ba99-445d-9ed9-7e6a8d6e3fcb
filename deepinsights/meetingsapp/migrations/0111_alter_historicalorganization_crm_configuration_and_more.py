# Generated by Django 4.2.18 on 2025-02-25 20:15

from django.db import migrations, models

import deepinsights.core.preferences.preferences

class Migration(migrations.Migration):

    dependencies = [
        ("meetingsapp", "0110_clientinteraction_advisor_notes_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="historicalorganization",
            name="crm_configuration",
            field=models.JSONField(
                blank=True, default=deepinsights.core.preferences.preferences.get_default_crm_configuration
            ),
        ),
        migrations.AlterField(
            model_name="organization",
            name="crm_configuration",
            field=models.JSONField(
                blank=True, default=deepinsights.core.preferences.preferences.get_default_crm_configuration
            ),
        ),
    ]
