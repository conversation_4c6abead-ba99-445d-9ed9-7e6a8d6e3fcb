# Generated by Django 4.2.17 on 2025-02-03 18:45

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0104_alter_client_phone_number_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="meetingprep",
            name="client",
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="meetingsapp.client"),
        ),
        migrations.AlterField(
            model_name="meetingprep",
            name="user",
            field=models.ForeignKey(
                null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AlterField(
            model_name="note",
            name="note_owner",
            field=models.ForeignKey(
                null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.<PERSON>er<PERSON><PERSON>(
            model_name="oauthcredentials",
            name="user",
            field=models.ForeignKey(
                null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AlterField(
            model_name="searchquery",
            name="requestor",
            field=models.ForeignKey(
                help_text="The user who made the search query",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="search_queries",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="structuredmeetingdata",
            name="note",
            field=models.ForeignKey(
                blank=True,
                help_text="The note this structured meeting data instance is associated with (or none).",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="meetingsapp.note",
            ),
        ),
    ]
