# Generated by Django 4.2 on 2024-10-18 19:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0063_client_back_populate"),
    ]

    operations = [
        migrations.AlterField(
            model_name="client",
            name="advisor",
            field=models.ForeignKey(
                blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AlterField(
            model_name="client",
            name="email",
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AlterField(
            model_name="client",
            name="first_name",
            field=models.Char<PERSON>ield(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="client",
            name="job_title",
            field=models.Char<PERSON><PERSON>(blank=True, null=True),
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name="client",
            name="last_name",
            field=models.Char<PERSON>ield(blank=True, null=True),
        ),
    ]
