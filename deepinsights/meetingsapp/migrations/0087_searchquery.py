# Generated by Django 4.2.16 on 2024-12-25 20:21

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0086_historicalprompt_version_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="SearchQuery",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ("query", models.TextField(help_text="The query asked by the user")),
                ("structured_response", models.JSONField(help_text="The structured output in Summary Section format")),
                (
                    "notes",
                    models.ManyToManyField(
                        help_text="The notes this search query was connected to",
                        related_name="search_queries",
                        to="meetingsapp.note",
                    ),
                ),
                (
                    "requestor",
                    models.ForeignKey(
                        help_text="The user who made the search query",
                        on_delete=django.db.models.deletion.RESTRICT,
                        related_name="search_queries",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
