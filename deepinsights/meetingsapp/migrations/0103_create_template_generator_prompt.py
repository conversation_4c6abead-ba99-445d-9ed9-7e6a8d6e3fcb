# Generated by Django 4.2.16 on 2025-01-16 14:38

from django.db import migrations

from deepinsights.meetingsapp.migrations import template_prompt


def create_template_generator_prompt(apps, schema_editor):
    Prompt = apps.get_model("meetingsapp", "Prompt")
    db_alias = schema_editor.connection.alias

    Prompt.objects.using(db_alias).create(
        name="Template Generator Prompt",
        unique_name="template_generator_prompt_v1",
        version="1.0",
        text=template_prompt,
        version_notes="Initial version of template generator prompt",
    )


def reverse_template_generator_prompt(apps, schema_editor):
    Prompt = apps.get_model("meetingsapp", "Prompt")
    db_alias = schema_editor.connection.alias
    Prompt.objects.using(db_alias).filter(unique_name="template_generator_prompt_v1").delete()


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0102_alter_historicalstructuredmeetingdata_kind_and_more"),
    ]

    operations = [
        migrations.RunPython(create_template_generator_prompt, reverse_code=reverse_template_generator_prompt),
    ]
