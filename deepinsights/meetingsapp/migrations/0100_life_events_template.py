# Generated by Django 4.2.16 on 2025-01-03 18:51

import logging

from django.db import migrations

from deepinsights.meetingsapp.migrations import life_event_data
from deepinsights.meetingsapp.models.structured_meeting_data import (
    StructuredMeetingDataTemplate as StructuredMeetingDataTemplateModel,
)


def create_life_events_template(apps, schema_editor):
    StructuredMeetingDataTemplate = apps.get_model("meetingsapp", "StructuredMeetingDataTemplate")
    StructuredMeetingDataSchema = apps.get_model("meetingsapp", "StructuredMeetingDataSchema")
    db_alias = schema_editor.connection.alias
    schema = StructuredMeetingDataSchema.objects.using(db_alias).get(name="structured_data_schema")

    try:
        prompt = StructuredMeetingDataTemplate.objects.using(db_alias).get(kind="financial_review_checklist").prompt
    except Exception as e:
        logging.error("Could not find Financial Review Checklist template. Skipping prompt association.")
        prompt = None

    StructuredMeetingDataTemplate.objects.create(
        title="Life Events",
        internal_name="Bento life events",
        kind=StructuredMeetingDataTemplateModel.Kind.BENTO_LIFE_EVENTS,
        initial_data=life_event_data,
        schema_definition=schema,
        prompt=prompt,
    )


def remove_life_events_template(apps, schema_editor):
    StructuredMeetingDataTemplate = apps.get_model("meetingsapp", "StructuredMeetingDataTemplate")
    db_alias = schema_editor.connection.alias

    StructuredMeetingDataTemplate.objects.using(db_alias).filter(
        kind=StructuredMeetingDataTemplateModel.Kind.BENTO_LIFE_EVENTS
    ).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0099_remove_meetingtype_meeting_follow_up_templates"),
    ]

    operations = [
        migrations.RunPython(create_life_events_template, remove_life_events_template),
    ]
