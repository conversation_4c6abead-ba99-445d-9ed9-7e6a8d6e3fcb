# Generated by Django 4.2 on 2024-02-18 11:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0022_historicalnote_raw_asr_response_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="task",
            name="meeting",
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to="meetingsapp.meeting"),
        ),
        migrations.AlterField(
            model_name="task",
            name="note",
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to="meetingsapp.note"),
        ),
        migrations.AlterField(
            model_name="task",
            name="task_owner",
            field=models.ForeignKey(
                null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
    ]
