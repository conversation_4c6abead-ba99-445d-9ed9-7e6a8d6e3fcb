# Generated by Django 4.2.20 on 2025-05-08 18:39

from django.conf import settings
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0133_scheduledevent"),
    ]

    operations = [
        migrations.RenameModel(
            old_name="MeetingPrep",
            new_name="ClientRecap",
        ),
        migrations.RenameModel(
            old_name="HistoricalMeetingPrep",
            new_name="HistoricalClientRecap",
        ),
        migrations.AlterModelOptions(
            name="historicalclientrecap",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical client recap",
                "verbose_name_plural": "historical client recaps",
            },
        ),
    ]
