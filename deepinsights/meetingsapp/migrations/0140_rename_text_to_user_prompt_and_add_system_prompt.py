# Generated by Django 4.2.20 on 2025-05-23 21:48

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0139_alter_oauthclientcredentials_tenant_id"),
    ]

    operations = [
        migrations.Rename<PERSON>ield(
            model_name="historicalprompt",
            old_name="text",
            new_name="user_prompt",
        ),
        migrations.RenameField(
            model_name="prompt",
            old_name="text",
            new_name="user_prompt",
        ),
        migrations.AddField(
            model_name="historicalprompt",
            name="system_prompt",
            field=models.TextField(
                blank=True,
                help_text="System-level instructions for the LLM. This provides context and constraints for the model's behavior.",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="prompt",
            name="system_prompt",
            field=models.TextField(
                blank=True,
                help_text="System-level instructions for the LLM. This provides context and constraints for the model's behavior.",
                null=True,
            ),
        ),
    ]
