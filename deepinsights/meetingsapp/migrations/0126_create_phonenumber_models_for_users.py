# Generated by Django 4.2.18 on 2025-04-10 01:45

from django.db import migrations


def create_primary_phone_number_model_for_users(apps, schema_editor):
    PhoneNumber = apps.get_model("meetingsapp", "PhoneNumber")
    User = apps.get_model("users", "User")
    db_alias = schema_editor.connection.alias

    users = User.objects.using(db_alias).filter(phone_number__isnull=False)
    for user in users:
        PhoneNumber.objects.using(db_alias).update_or_create(
            user=user,
            number=user.phone_number,
            primary=True,
        )


class Migration(migrations.Migration):
    dependencies = [("meetingsapp", "0125_phonenumber_and_more"), ("users", "0026_alter_user_phone_number")]

    operations = [
        migrations.RunPython(
            create_primary_phone_number_model_for_users,
            migrations.RunPython.noop,
        ),
    ]
