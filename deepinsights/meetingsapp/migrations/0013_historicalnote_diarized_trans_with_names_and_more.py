# Generated by Django 4.2 on 2023-11-11 16:19

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('meetingsapp', '0012_historicalmeeting_speaker_metadata_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicalnote',
            name='diarized_trans_with_names',
            field=models.Char<PERSON>ield(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicalnote',
            name='summary_by_topics',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(blank=True, null=True), blank=True, null=True, size=None),
        ),
        migrations.AddField(
            model_name='note',
            name='diarized_trans_with_names',
            field=models.CharField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='note',
            name='summary_by_topics',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.Char<PERSON><PERSON>(blank=True, null=True), blank=True, null=True, size=None),
        ),
    ]
