# Generated by Django 4.2.18 on 2025-04-17 01:22

import uuid

import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import oauth2_provider.generators
import oauth2_provider.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0127_add_bullet_to_kind_enum"),
    ]

    run_before = [
        ("oauth2_provider", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="OAuthAccessToken",
            fields=[
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("token", models.TextField()),
                (
                    "token_checksum",
                    oauth2_provider.models.TokenChecksumField(db_index=True, max_length=64, unique=True),
                ),
                ("expires", models.DateTimeField()),
                ("scope", models.TextField(blank=True)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("updated", models.DateTimeField(auto_now=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="OAuthApplication",
            fields=[
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                (
                    "client_id",
                    models.CharField(
                        db_index=True,
                        default=oauth2_provider.generators.generate_client_id,
                        max_length=100,
                        unique=True,
                    ),
                ),
                ("redirect_uris", models.TextField(blank=True, help_text="Allowed URIs list, space separated")),
                (
                    "post_logout_redirect_uris",
                    models.TextField(
                        blank=True, default="", help_text="Allowed Post Logout URIs list, space separated"
                    ),
                ),
                (
                    "client_type",
                    models.CharField(choices=[("confidential", "Confidential"), ("public", "Public")], max_length=32),
                ),
                (
                    "authorization_grant_type",
                    models.CharField(
                        choices=[
                            ("authorization-code", "Authorization code"),
                            ("implicit", "Implicit"),
                            ("password", "Resource owner password-based"),
                            ("client-credentials", "Client credentials"),
                            ("openid-hybrid", "OpenID connect hybrid"),
                        ],
                        max_length=32,
                    ),
                ),
                (
                    "client_secret",
                    oauth2_provider.models.ClientSecretField(
                        blank=True,
                        db_index=True,
                        default=oauth2_provider.generators.generate_client_secret,
                        help_text="Hashed on Save. Copy it now if this is a new secret.",
                        max_length=255,
                    ),
                ),
                ("hash_client_secret", models.BooleanField(default=True)),
                ("name", models.CharField(blank=True, max_length=255)),
                ("skip_authorization", models.BooleanField(default=False)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "algorithm",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("", "No OIDC support"),
                            ("RS256", "RSA with SHA-2 256"),
                            ("HS256", "HMAC with SHA-2 256"),
                        ],
                        default="",
                        max_length=5,
                    ),
                ),
                (
                    "allowed_origins",
                    models.TextField(
                        blank=True, default="", help_text="Allowed origins list to enable CORS, space separated"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(app_label)s_%(class)s",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="OAuthRefreshToken",
            fields=[
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("token", models.CharField(max_length=255)),
                ("token_family", models.UUIDField(blank=True, editable=False, null=True)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("updated", models.DateTimeField(auto_now=True)),
                ("revoked", models.DateTimeField(null=True)),
                (
                    "access_token",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="refresh_token",
                        to=settings.OAUTH2_PROVIDER_ACCESS_TOKEN_MODEL,
                    ),
                ),
                (
                    "application",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to=settings.OAUTH2_PROVIDER_APPLICATION_MODEL
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(app_label)s_%(class)s",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
                "unique_together": {("token", "revoked")},
            },
        ),
        migrations.CreateModel(
            name="OAuthIDToken",
            fields=[
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("jti", models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name="JWT Token ID")),
                ("expires", models.DateTimeField()),
                ("scope", models.TextField(blank=True)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("updated", models.DateTimeField(auto_now=True)),
                (
                    "application",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.OAUTH2_PROVIDER_APPLICATION_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(app_label)s_%(class)s",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="OAuthGrant",
            fields=[
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("code", models.CharField(max_length=255, unique=True)),
                ("expires", models.DateTimeField()),
                ("redirect_uri", models.TextField()),
                ("scope", models.TextField(blank=True)),
                ("created", models.DateTimeField(auto_now_add=True)),
                ("updated", models.DateTimeField(auto_now=True)),
                ("code_challenge", models.CharField(blank=True, default="", max_length=128)),
                (
                    "code_challenge_method",
                    models.CharField(
                        blank=True, choices=[("plain", "plain"), ("S256", "S256")], default="", max_length=10
                    ),
                ),
                ("nonce", models.CharField(blank=True, default="", max_length=255)),
                ("claims", models.TextField(blank=True)),
                (
                    "application",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to=settings.OAUTH2_PROVIDER_APPLICATION_MODEL
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="%(app_label)s_%(class)s",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="oauthaccesstoken",
            name="application",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to=settings.OAUTH2_PROVIDER_APPLICATION_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="oauthaccesstoken",
            name="id_token",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="access_token",
                to=settings.OAUTH2_PROVIDER_ID_TOKEN_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="oauthaccesstoken",
            name="source_refresh_token",
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="refreshed_access_token",
                to=settings.OAUTH2_PROVIDER_REFRESH_TOKEN_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="oauthaccesstoken",
            name="user",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(app_label)s_%(class)s",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
