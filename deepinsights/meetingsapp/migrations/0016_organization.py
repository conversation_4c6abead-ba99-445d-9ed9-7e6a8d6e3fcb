# Generated by Django 4.2 on 2023-12-13 07:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('meetingsapp', '0015_alter_historicaltask_metadata_alter_task_metadata'),
    ]

    operations = [
        migrations.CreateModel(
            name='Organization',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='Organization Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('metadata', models.JSONField()),
                ('crm_configuration', models.JSONField()),
            ],
            options={
                'verbose_name': 'Organization',
                'verbose_name_plural': 'Organizations',
            },
        ),
    ]
