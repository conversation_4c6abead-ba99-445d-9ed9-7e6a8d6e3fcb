# Generated by Django 4.2 on 2024-11-01 21:48

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0066_agendatemplate_meetingfollowuptemplate_meetingtype_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="historicalnote",
            name="meeting_type",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                to="meetingsapp.meetingtype",
            ),
        ),
        migrations.AddField(
            model_name="meetingtype",
            name="category",
            field=models.CharField(choices=[("client", "Client"), ("internal", "Internal")], default="client"),
        ),
        migrations.AddField(
            model_name="note",
            name="meeting_type",
            field=models.ForeignKey(
                blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to="meetingsapp.meetingtype"
            ),
        ),
        migrations.AlterField(
            model_name="meetingtype",
            name="agenda_templates",
            field=models.ManyToManyField(blank=True, to="meetingsapp.agendatemplate"),
        ),
        migrations.AlterField(
            model_name="meetingtype",
            name="meeting_follow_up_templates",
            field=models.ManyToManyField(blank=True, to="meetingsapp.meetingfollowuptemplate"),
        ),
        migrations.AlterField(
            model_name="meetingtype",
            name="organizations",
            field=models.ManyToManyField(blank=True, to="meetingsapp.organization"),
        ),
    ]
