# Generated by Django 4.2.20 on 2025-05-21 19:01

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0135_alter_scheduledevent_shared_source_id_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="MidMeetingNudge",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("success", "Success"),
                            ("not-processed", "Not Processed"),
                            ("processing", "Processing"),
                            ("error", "Error"),
                        ],
                        default="not-processed",
                        help_text="The status of us sending the nudge",
                        max_length=20,
                    ),
                ),
                ("scheduled_time", models.DateTimeField(help_text="The scheduled time to send the nudge.")),
                (
                    "sent_time",
                    models.DateTimeField(blank=True, help_text="The time the nudge was actually sent.", null=True),
                ),
                (
                    "note",
                    models.ForeignKey(
                        help_text="The meeting note we're trying to nudge about",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="meetingsapp.note",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="The host of the meeting to nudge",
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
