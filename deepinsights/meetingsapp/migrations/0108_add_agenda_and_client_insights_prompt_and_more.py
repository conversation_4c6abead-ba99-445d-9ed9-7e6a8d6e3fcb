from django.db import migrations

from deepinsights.meetingsapp.migrations import unstructured_meeting_data_schema, unstructured_meeting_data_schema_name

prompt_unique_name = "meeting_agenda_generator"
client_insights_prompt_name = "client_insights_generator"


def create_agenda_and_client_insights_prompt(apps, schema_editor):
    Prompt = apps.get_model("meetingsapp", "Prompt")
    StructuredMeetingDataSchema = apps.get_model("meetingsapp", "StructuredMeetingDataSchema")
    db_alias = schema_editor.connection.alias

    StructuredMeetingDataSchema.objects.using(db_alias).create(
        name=unstructured_meeting_data_schema_name,
        schema=unstructured_meeting_data_schema,
        description="Schema for unstructured textual content, like agendas",
    )

    # Create both prompts
    Prompt.objects.using(db_alias).bulk_create(
        [
            Prompt(
                name="Meeting Agenda Generator",
                unique_name=prompt_unique_name,
                version="1.0",
                text="""As an experienced financial advisor, please fill in this agenda template with relevant information
from the client's history. Use specific details from the provided summaries and household information.

The output should maintain the same format and structure as the input template, but include relevant details
from the client history under each section. If there is no relevant information for a section, keep the
section but note "No relevant history available."

Client History:
{client_history}

Household Information:
{household_info}

Agenda Template:
{agenda_template}

Please fill in the agenda template while maintaining its original format. Reference specific details from the client history
where relevant, such as previous discussions, decisions, and numerical data. Include dates when mentioning past events.""",
            ),
            Prompt(
                name="Client Insights Generator",
                unique_name=client_insights_prompt_name,
                version="1.0",
                text="""You are an AI assistant helping financial advisors prepare for client meetings. Your task is to analyze previous meeting summaries and organize the relevant information into a structured format with exactly two levels:

1. **Top-level headers**: Broad topics (e.g., "Personal Overview").
2. **Bullet points (subentries)**: Detailed information related to the top-level headers.

IMPORTANT: Both top-level headers and bullet points must include `"kind": "header"` due to design constraints.

### INPUT CONTEXT
- SCHEMA: {schema}
- MEETING AGENDA: {agenda}
- CONTEXT: {context}

### INSTRUCTIONS
1. **Structure:**
   - Create top-level headers for broad topics such as:
     - Personal Overview
     - Financial Overview
     - Meeting-Relevant Information
     - Tasks and Progress
   - Under each top-level header, list bullet points (subentries) containing the **most detailed** information available.
   - **No further nesting** beyond these two levels (header → subentries).

2. **Level of Detail:**
   - Include **as much detail as possible** from the provided summaries:
     - **Names, dates, figures, places**, and any other specifics that appear in the summary should be included.
   - **Do NOT infer or guess** details not explicitly stated in the summary. If something is not in the summary, **do not include it**.

3. **Content Organization:**
   - **Personal Overview**: Family information, ages, job changes, or major personal updates.
   - **Financial Overview**: Income, cash flow, investments, retirement accounts, tax considerations, etc.
   - **Meeting-Relevant Information**:
     - Highlight **agenda-focused details** that the advisor needs for the **upcoming meeting**.
     - Include **key discussion points**, **client concerns**, **open questions**, and any **decisions** that need to be made during the meeting.
     - Mention any **documents, data, or additional materials** the advisor should have on hand.
     - If there are **time-sensitive items**, deadlines, or references to important dates, include them here so the advisor can quickly see what must be addressed at the meeting.
   - **Tasks and Progress**:
     - Provide a **snapshot** of **ongoing action items**, **their current status**, and **next steps**.
     - If tasks are assigned to specific individuals, include **who** is responsible.
     - Note any **deadlines**, **completion dates**, or **blocked tasks** awaiting external input or events.
     - The purpose is to help the advisor track completed and pending tasks, ensuring clarity on what has been done and what remains.

4. **Accuracy Emphasis:**
   - Only use information **directly** provided in the summaries/context.
   - Do not add extra assumptions or interpretations.
   - Maximize detail while retaining factual accuracy from the source data.

### OUTPUT FORMAT
Return valid JSON structured like this:

{{
"review_entries": [
  {{
    "id": "category_id",
    "kind": "header",
    "topic": "Top-level category name (e.g., Personal Overview)",
    "subentries": [
      {{
        "id": "bullet_point_id",
        "kind": "header",
        "topic": "A fully detailed point of information"
      }},
      {{
        "id": "another_bullet_point_id",
        "kind": "header",
        "topic": "Another fully detailed point of information"
      }}
    ]
  }}
]
}}

### EXAMPLE OUTPUT

{{
"review_entries": [
  {{
    "id": "personal_overview",
    "kind": "header",
    "topic": "Personal Overview",
    "subentries": [
      {{
        "id": "family_background",
        "kind": "header",
        "topic": "Aaron and Ariel Price, residing in New York, are undergoing job transitions and business growth."
      }},
      {{
        "id": "salary_change",
        "kind": "header",
        "topic": "Aaron will begin a new role with a $90,000 annual salary starting May 2025."
      }}
    ]
  }},
  {{
    "id": "meeting_relevant_information",
    "kind": "header",
    "topic": "Meeting-Relevant Information",
    "subentries": [
      {{
        "id": "discussion_points",
        "kind": "header",
        "topic": "Potential move to Singapore—advisor must review international tax implications and timeline."
      }},
      {{
        "id": "document_requirements",
        "kind": "header",
        "topic": "Client requests detailed breakdown of current portfolio holdings; bring updated investment statements."
      }}
    ]
  }},
  {{
    "id": "tasks_and_progress",
    "kind": "header",
    "topic": "Tasks and Progress",
    "subentries": [
      {{
        "id": "401k_setup",
        "kind": "header",
        "topic": "Aaron to finalize 401(k) paperwork by June 1; HR confirmed plan details are ready."
      }},
      {{
        "id": "income_planning",
        "kind": "header",
        "topic": "Exploring feasibility of an ADU (Accessory Dwelling Unit) for extra rental income; awaiting contractor quotes."
      }}
    ]
  }}
]
}}

Adhere to these specifications to ensure the final JSON output accurately reflects all relevant details from the summaries without adding any unverified information or leaving out critical meeting and task details.""",
            ),
        ]
    )


def reverse_agenda_and_client_insights_prompt(apps, schema_editor):
    """Reverse the migration by deleting the prompts and schema"""
    Prompt = apps.get_model("meetingsapp", "Prompt")
    StructuredMeetingDataSchema = apps.get_model("meetingsapp", "StructuredMeetingDataSchema")
    db_alias = schema_editor.connection.alias

    # Delete prompts first
    Prompt.objects.using(db_alias).filter(unique_name__in=[prompt_unique_name, client_insights_prompt_name]).delete()

    # Then delete schema
    StructuredMeetingDataSchema.objects.using(db_alias).filter(name=unstructured_meeting_data_schema_name).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0107_meetingtype_agenda_templates_and_more"),
    ]

    operations = [
        migrations.RunPython(create_agenda_and_client_insights_prompt, reverse_agenda_and_client_insights_prompt),
    ]
