# Generated by Django 4.2 on 2023-08-25 17:25

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Meeting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('is_active', models.BooleanField(default=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('zoom_meeting_id', models.CharField(blank=True, max_length=100, null=True)),
                ('zoom_meeting_uuid', models.CharField(blank=True, max_length=100, null=True)),
                ('account_id', models.CharField(blank=True, max_length=100, null=True)),
                ('topic', models.CharField(blank=True, max_length=255, null=True)),
                ('start_time', models.DateTimeField(blank=True, null=True)),
                ('timezone', models.CharField(blank=True, max_length=10, null=True)),
                ('host_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('is_processed', models.BooleanField(default=False)),
                ('calendar_object', models.JSONField()),
                ('calendar_id', models.CharField(unique=True)),
            ],
            options={
                'verbose_name': 'Meeting',
            },
        ),
        migrations.CreateModel(
            name='Note',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('is_active', models.BooleanField(default=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('file_path', models.URLField(blank=True, null=True)),
                ('file_type', models.CharField(blank=True, max_length=255, null=True)),
                ('note_type', models.CharField(blank=True, choices=[('meeting_recording', 'meeting_recording'), ('image', 'image'), ('voice_memo', 'voice_memo'), ('transcript', 'transcript')], null=True)),
                ('metadata', models.JSONField(blank=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('uploaded', 'uploaded'), ('processed', 'processed'), ('missing', 'missing')], null=True)),
            ],
            options={
                'verbose_name': 'Note',
            },
        ),
        migrations.CreateModel(
            name='Recording',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('is_active', models.BooleanField(default=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('zoom_meeting_id', models.CharField(blank=True, max_length=255, null=True)),
                ('file_type', models.CharField(blank=True, max_length=255, null=True)),
                ('recording_file', models.URLField(blank=True, null=True)),
                ('user_type', models.CharField(blank=True, choices=[('host', 'host'), ('participant', 'participant')], max_length=100, null=True)),
                ('transcribe', models.URLField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Recording',
            },
        ),
        migrations.CreateModel(
            name='Task',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('is_active', models.BooleanField(default=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('task_title', models.CharField(max_length=255)),
                ('task_desc', models.TextField(blank=True, null=True)),
                ('completed', models.BooleanField(blank=True, default=False)),
                ('due_date', models.DateTimeField(null=True)),
                ('meeting', models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='meetingsapp.meeting')),
            ],
            options={
                'verbose_name': 'Task',
            },
        ),
    ]
