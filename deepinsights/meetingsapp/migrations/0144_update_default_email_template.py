from django.db import migrations

default_template_generation_prompt = """
Using the provided notes, generate a **concise, client-facing follow-up email** that summarizes the most important discussion points and action items. The email must strictly follow the formatting rules below.

**MARKDOWN FORMATTING RULES:**
1.  **BE CONCISE:** Each bullet point within the sections defined by the provided email template should be a brief summary of a key decision, finding, or action. Avoid verbose sentences and conversational filler.
2.  **MANDATORY BULLETS:** All informational text under section titles (as defined in the provided email template) **MUST** be a bullet point. Do not write paragraphs. Each distinct point gets its own bullet.
3.  **USE MARKDOWN SYNTAX:** The entire output must be formatted using standard Markdown. Ensure there is an empty line after section titles if specified or shown in the provided email template.
4.  **SECTION TITLES:** Use Markdown bolding for section titles (e.g., `**Retirement Planning**`), as guided by the provided email template. An empty line **MUST** be included immediately after every section title to ensure correct Markdown parsing and readability.
5.  **BULLET POINTS:** Use a hyphen (`-`) or an asterisk (`*`) followed by a space to create bulleted list items.
6.  **ACTION ITEMS:**
    * Generate action items as specified in the provided email template.
    * If the provided email template indicates separate sections for client and advisor tasks, ensure they are clearly distinguished.
    * Each action item must be a distinct bullet point.
    * If using headers, make sure to insert an empty line after the header to ensure correct Markdown parsing and readability.
7.  **NO SUBJECT LINE:** The generated email body should not include a "Subject:" line, unless explicitly part of the content within the provided email template.


---

context:
{context}

email template:
{email_template}
"""

default_email_template = """
Template Structure:

Hi [Client First Names],

It was great meeting with you. [Insert a brief, personal sentence related to the meeting or a personal connection if appropriate, otherwise omit.]

Below is a quick recap of what we discussed, along with our key next steps:

**[Section Title 1]**

* [Summary point 1.]
* [Summary point 2.]

**[Section Title 2]**

* [Summary point 1.]
* [Summary point 2.]
(...and additional sections as needed for each topic discussed)

**Action Items**

**[Client Name(s)]:**
* [Action item for the client.]
* [Another action item for the client, if applicable.]

**[Advisor Name]:**
* [Action item for the advisor.]
* [Another action item for the advisor, if applicable.]

Please let me know if I missed anything or if you have any questions.

All the best,

[Advisor Name]

Example of a Generated Email (Based on the Template):

Hi Terri & Denise,

It was great meeting with you both today—thank you for taking the time to catch up with Emily and me.

Below is a quick recap of what we discussed, along with the key action items we'll be moving forward with:

**Personal Updates**

* So glad to hear you enjoyed your trip to Italy—Thelma's 11th country!

**Thelma's School & Upcoming Tuition**

* Thelma is set to start 6th grade this fall, with tuition due in June.
* Estimated annual cost: $46K, to be paid as a lump sum.
* The current plan is to pull:
    * $36K from the $166K temporary cash restriction in SNOXX
    * $10K from the 529 plan
* (Final number pending confirmation—we'll hold off on moving funds until you confirm.)

**Cash Flow & Income**

* Denise's base salary remains at $250K, but overall income will be lower this year due to the loss of a major client and no company profits.
* You also loaned $60K to the firm, which will be repaid, but will not contribute to current-year income.

**Emergency Savings & Near-Term Plans**

* Emergency savings are currently around $166K, comfortably within your target range of $150K-$200K.
* The home remodel is already paid for, and while there's a small chance of capital going into a future venture, you're feeling stable and not anticipating needing to act on that in the near term.

**Investment & Market Outlook**

* Your current equity strategy remains appropriate, with 9% in cash positioned to be deployed opportunistically during market volatility.
* We'll continue to monitor market conditions and look for opportunities to upgrade the portfolio.

**Action Items**

**Terri & Denise:**
* Please upload all insurance documents to the portal so we can review and assess coverage adequacy.
* Confirm Thelma's tuition amount once finalized.

**Summitry Advanced Plan Edits (for Will Berg):**
* Add an early retirement scenario.
* Add another scenario in which Denise's income diminishes over the her last 10 working years.

Please let me know if I missed anything or if you have any questions.

All the best,

Will Berg
"""


def update_default_template(apps, schema_editor):
    MeetingSummaryEmailTemplate = apps.get_model("meetingsapp", "MeetingSummaryEmailTemplate")

    MeetingSummaryEmailTemplate.objects.update_or_create(
        internal_name="default_template",
        defaults={
            "name": "Default Email Template",
            "system_prompt": "You are a financial advisor's assistant tasked with drafting follow-up emails after client meetings. Your role is to create personalized, compliant emails based on meeting summaries and client preferences.",
            "generation_prompt": default_template_generation_prompt,
            "email_template_content": default_email_template,
            "description": "Default template for meeting summary emails",
            "use_html": False,
            "tone": "default",
            "wordiness": "default",
            "amount_discussion": "default",
            "voice": "default",
            "everyone": True,
        },
    )


def reverse_default_template(apps, schema_editor):
    # No need to do anything in reverse since we're updating an existing template
    pass


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0143_alter_scheduledevent_note"),
    ]

    operations = [
        migrations.RunPython(update_default_template, reverse_default_template),
    ]
