# Generated by Django 4.2.16 on 2024-12-26 17:44
from django.db import migrations

structured_review_schema = {
    "$id": "https://zeplyn.ai/structured_review_data.schema.json",
    "type": "object",
    "$defs": {
        "review_entry": {
            "type": "object",
            "required": ["id", "kind", "topic"],
            "properties": {
                "id": {"type": "string"},
                "kind": {
                    "enum": ["header", "toggle", "select", "multiselect"],
                    "type": "string",
                },
                "topic": {"type": "string"},
                "options": {"type": "array", "items": {"type": "string"}},
                "evidence": {"type": "array", "items": {"$ref": "#/$defs/evidence_entry"}},
                "selected": {"type": "string"},
                "discussed": {"type": "boolean"},
                "subentries": {
                    "type": "array",
                    "items": {"$ref": "#/$defs/review_entry"},
                },
                "explanation": {"type": "string"},
                "multi_selected": {"type": "array", "items": {"type": "string"}},
            },
            "additionalProperties": False,
        },
        "evidence_entry": {
            "type": "object",
            "required": ["quote"],
            "properties": {
                "quote": {"type": "string"},
                "timestamp": {"type": "string"},
            },
            "additionalProperties": False,
        },
    },
    "title": "Structured meeting review data",
    "$schema": "https://json-schema.org/draft/2020-12/schema",
    "required": ["review_entries"],
    "properties": {
        "review_entries": {
            "type": "array",
            "items": {"$ref": "#/$defs/review_entry"},
            "uniqueItems": True,
        }
    },
    "description": "Describes the format of structured meeting follow-up data that is generated from a transcript of a meeting.",
    "additionalProperties": False,
}

tabular_numerical_schema = {
    "$schema": "https://json-schema.org/draft/2020-12/schema",
    "$id": "https://zeplyn.ai/tabular_numerical_data.schema.json",
    "title": "Numerical Meeting Data Schema",
    "description": "Schema for extracting numerical data from meeting transcripts",
    "type": "object",
    "required": ["table_definition", "data"],
    "properties": {
        "table_definition": {
            "type": "object",
            "required": ["columns"],
            "properties": {
                "title": {"type": "string"},
                "description": {"type": "string"},
                "columns": {
                    "type": "array",
                    "minItems": 3,
                    "items": {
                        "type": "object",
                        "required": ["id", "caption", "data_type"],
                        "properties": {
                            "id": {"type": "string"},
                            "caption": {"type": "string"},
                            "data_type": {"type": "string"},
                        },
                    },
                },
            },
        },
        "data": {
            "type": "array",
            "items": {
                "type": "object",
                "required": ["values", "format"],
                "properties": {
                    "values": {
                        "type": "object",
                        "additionalProperties": True,
                    },
                    "format": {
                        "type": "object",
                        "properties": {
                            "decimal_places": {"type": "integer"},
                            "show_symbol": {"type": "boolean"},
                            "date_format": {"type": "string"},
                            "value_type": {
                                "type": "string",
                                "enum": ["number", "percentage", "currency", "date"],
                            },
                        },
                        "required": ["value_type"],
                    },
                    "evidence": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "required": ["quote", "timestamp"],
                            "properties": {
                                "quote": {"type": "string"},
                                "timestamp": {"type": "string"},
                            },
                        },
                    },
                },
            },
        },
    },
}


def create_schemas(apps, schema_editor):
    """
    Creates two schema records in the StructuredMeetingDataSchema model:
    1) Structured Meeting Review Data schema
    2) Tabular Numerical Data schema
    """
    StructuredMeetingDataSchema = apps.get_model("meetingsapp", "StructuredMeetingDataSchema")
    db_alias = schema_editor.connection.alias

    StructuredMeetingDataSchema.objects.using(db_alias).create(
        name="structured_data_schema",
        description="JSON Schema for structured meeting follow-up data",
        schema=structured_review_schema,
    )

    StructuredMeetingDataSchema.objects.using(db_alias).create(
        name="tabular_numerical_data_schema",
        description="JSON Schema for extracting numerical data from meeting transcripts",
        schema=tabular_numerical_schema,
    )


def reverse_schemas(apps, schema_editor):
    """
    Reverts the changes made in create_schemas by removing both schema records.
    """
    StructuredMeetingDataSchema = apps.get_model("meetingsapp", "StructuredMeetingDataSchema")
    db_alias = schema_editor.connection.alias

    StructuredMeetingDataSchema.objects.using(db_alias).filter(
        name__in=["structured_data_schema", "tabular_numerical_data_schema"]
    ).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0093_structuredmeetingdataschema_and_more"),
    ]

    operations = [
        migrations.RunPython(create_schemas, reverse_code=reverse_schemas),
    ]
