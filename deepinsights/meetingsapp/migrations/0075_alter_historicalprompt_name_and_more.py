# Generated by Django 4.2.16 on 2024-11-18 19:32

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import simple_history.models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0074_set_everyone_on_meetingtypes"),
    ]

    operations = [
        migrations.AlterField(
            model_name="historicalprompt",
            name="name",
            field=models.CharField(
                blank=True,
                help_text="Name of the prompt. This is only for human reference, it is not used by the LLM.",
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="historicalprompt",
            name="text",
            field=models.TextField(
                blank=True, help_text="The text of the prompt. This is passed to the LLM.", null=True
            ),
        ),
        migrations.AlterField(
            model_name="meetingfollowuptemplate",
            name="context",
            field=models.TextField(
                blank=True,
                help_text="Context that can be provided to a language model when using data from this object as an input.This should be provided by an engineer on the team, based on an understanding of the customer's needs.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="meetingfollowuptemplate",
            name="initial_data",
            field=models.JSONField(
                blank=True,
                help_text="The initial data that should be used when creating a follow-up from this template. This can be null, in which case there will be no default data and the schema will be used to determine any defaults (if any).",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="meetingfollowuptemplate",
            name="kind",
            field=models.CharField(
                help_text="What 'kind' of follow-up template this is. Since we do not know in advance what kind of meeting follow up types we will need, and how the frontend might need to render them, this is essentially an escape valve to allow us to have customized logic on the frontend. It is also used to provide an identifier for shared templates that are generated by a database migration, and which we may need to reference in the future to modify it (or remove it)."
            ),
        ),
        migrations.AlterField(
            model_name="meetingfollowuptemplate",
            name="prompt",
            field=models.ForeignKey(
                blank=True,
                help_text="The prompt that should be passed to a language model when generating a follow-up from this template.",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="meetingsapp.prompt",
            ),
        ),
        migrations.AlterField(
            model_name="meetingfollowuptemplate",
            name="schema",
            field=models.JSONField(
                blank=True,
                help_text="The details of the data format for follow-up template. This is intended to be a valid JSON schema (https://json-schema.org/), and may be used to generate a form on the frontend that will be used to update the follow-up data. In most cases, this should be non-null, but that is not enforced at the database level. If present, this will be used to validate the data field of a follow-up created from this template.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="meetingfollowuptemplate",
            name="title",
            field=models.CharField(
                blank=True,
                help_text="The title of this follow-up template. This is mainly for humans and will be shown to users in the app interface, but it is also conceivable that it could be passed to the language model as context.",
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="meetingtype",
            name="agenda_templates",
            field=models.ManyToManyField(
                blank=True,
                help_text="Templates for agendas that can be used with this type of meeting.",
                to="meetingsapp.agendatemplate",
            ),
        ),
        migrations.AlterField(
            model_name="meetingtype",
            name="category",
            field=models.CharField(
                choices=[("client", "Client"), ("internal", "Internal"), ("debrief", "Debrief")],
                default="client",
                help_text="The category for this meeting. Meetings with external clients have additional requirements, such as consent for notetaking.",
            ),
        ),
        migrations.AlterField(
            model_name="meetingtype",
            name="context",
            field=models.TextField(
                blank=True,
                help_text="Context that can be provided to a language model when using data from this object as an input.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="meetingtype",
            name="everyone",
            field=models.BooleanField(
                default=False,
                help_text="Whether or not this meeting type is available to everyone. If this is true, then all users have access to this type of meeting. If this is false, then only users allowlisted by the organizations list have access to this meeting type.",
            ),
        ),
        migrations.AlterField(
            model_name="meetingtype",
            name="key",
            field=models.CharField(
                blank=True,
                help_text="A key that maps this meeting type to the older hard-coded meeting types. This should usually be left blank. If there is no key, then this meeting type has no mapping to the older hard-coded meeting types.",
                null=True,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="meetingtype",
            name="meeting_follow_up_templates",
            field=models.ManyToManyField(
                blank=True,
                help_text="Templates for follow-ups that should be associated with this meeting. For any of these that are selected, a follow-up will be generated by the LLM when the meeting is processed.",
                to="meetingsapp.meetingfollowuptemplate",
            ),
        ),
        migrations.AlterField(
            model_name="meetingtype",
            name="name",
            field=models.CharField(
                help_text='The user-visible name of this type of meeting (e.g., "Client", "Internal"). This will often correspond to the key above. The key, however, is unique, whereas multiple meeting types can have the same user-visible name.',
                max_length=200,
            ),
        ),
        migrations.AlterField(
            model_name="meetingtype",
            name="organizations",
            field=models.ManyToManyField(
                blank=True,
                help_text="The organizations which have access to this type of meeting.If this is empty, then all organizations have access to this type of meeting.",
                to="meetingsapp.organization",
            ),
        ),
        migrations.AlterField(
            model_name="prompt",
            name="name",
            field=models.CharField(
                blank=True,
                help_text="Name of the prompt. This is only for human reference, it is not used by the LLM.",
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="prompt",
            name="text",
            field=models.TextField(
                blank=True, help_text="The text of the prompt. This is passed to the LLM.", null=True
            ),
        ),
        migrations.CreateModel(
            name="HistoricalMeetingFollowUp",
            fields=[
                ("id", models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("uuid", models.UUIDField(db_index=True, default=uuid.uuid4, editable=False)),
                ("title", models.CharField(blank=True, max_length=200, null=True)),
                ("kind", models.CharField()),
                ("schema", models.JSONField(blank=True)),
                ("data", models.JSONField(blank=True)),
                ("context", models.TextField(blank=True, null=True)),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")], max_length=1),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "note",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="meetingsapp.note",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical meeting follow up",
                "verbose_name_plural": "historical meeting follow ups",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name="HistoricalAgenda",
            fields=[
                ("id", models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("uuid", models.UUIDField(db_index=True, default=uuid.uuid4, editable=False)),
                ("title", models.CharField(blank=True, max_length=200, null=True)),
                ("sections", models.JSONField()),
                ("context", models.TextField(blank=True, null=True)),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")], max_length=1),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "note",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="+",
                        to="meetingsapp.note",
                    ),
                ),
            ],
            options={
                "verbose_name": "historical agenda",
                "verbose_name_plural": "historical agendas",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
