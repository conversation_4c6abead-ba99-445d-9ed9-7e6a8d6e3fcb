# Generated by Django 4.2 on 2024-01-14 07:24

import deepinsights.meetingsapp.models.organization
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0020_merge_20240114_0650"),
    ]

    operations = [
        migrations.Alter<PERSON><PERSON>(
            model_name="historicalorganization",
            name="crm_configuration",
            field=models.JSONField(
                blank=True,
                default=deepinsights.meetingsapp.models.organization.get_default_crm_configuration,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="historicalorganization",
            name="preferences",
            field=models.JSONField(
                blank=True, default=deepinsights.meetingsapp.models.organization.get_default_preferences, null=True
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="organization",
            name="crm_configuration",
            field=models.JSONField(
                blank=True,
                default=deepinsights.meetingsapp.models.organization.get_default_crm_configuration,
                null=True,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="organization",
            name="preferences",
            field=models.<PERSON><PERSON><PERSON><PERSON>(
                blank=True, default=deepinsights.meetingsapp.models.organization.get_default_preferences, null=True
            ),
        ),
    ]
