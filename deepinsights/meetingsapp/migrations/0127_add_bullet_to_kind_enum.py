from django.db import migrations


def update_schema(apps, schema_editor):
    StructuredMeetingDataSchema = apps.get_model("meetingsapp", "StructuredMeetingDataSchema")
    db_alias = schema_editor.connection.alias

    schema = StructuredMeetingDataSchema.objects.using(db_alias).get(name="structured_data_schema")
    schema_data = schema.schema

    # Add "bullet" to the kind enum
    schema_data["$defs"]["review_entry"]["properties"]["kind"]["enum"].append("bullet")
    schema.schema = schema_data
    schema.save()


def reverse_schema(apps, schema_editor):
    StructuredMeetingDataSchema = apps.get_model("meetingsapp", "StructuredMeetingDataSchema")
    db_alias = schema_editor.connection.alias

    schema = StructuredMeetingDataSchema.objects.using(db_alias).get(name="structured_data_schema")
    schema_data = schema.schema

    # Remove "bullet" from the kind enum.""
    schema_data["$defs"]["review_entry"]["properties"]["kind"]["enum"].remove("bullet")
    schema.schema = schema_data
    schema.save()


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0126_create_phonenumber_models_for_users"),
    ]

    operations = [
        migrations.RunPython(update_schema, reverse_code=reverse_schema),
    ]
