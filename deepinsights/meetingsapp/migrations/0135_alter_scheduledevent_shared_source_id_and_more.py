# Generated by Django 4.2.20 on 2025-05-16 18:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("meetingsapp", "0134_rename_meetingprep_clientrecap_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="scheduledevent",
            name="shared_source_id",
            field=models.TextField(
                blank=True,
                help_text="The shared/common identifier of the event in the source system which provided it (likely a calendar provider). This identifier is meant to be the same across all ScheduledEvent instances when the underlying event is the same (within reason; we don't expect to be able to unify the same real-world event across different calendar systems or unify two different calendar events in a calendar system that match one real-world event but don't have any shared identifier in the calendar system). This is distinct from the user source ID because calendar systems can have representations of events that are specific to each user, and we may want to store information about an event for a specific user rather than generically across all users who have the same event.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="scheduledevent",
            name="user_specific_source_id",
            field=models.TextField(
                blank=True,
                help_text="The user-specific identifier of the event in the source system which provided it (likely a calendar provider). This is distinct from the shared source ID because calendar systems can have representations of events that are specific to each user, and we may want to store information about an event for a specific user rather than generically across all users who have the same event.",
                null=True,
            ),
        ),
    ]
