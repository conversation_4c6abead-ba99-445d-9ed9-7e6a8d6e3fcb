# Generated by Django 4.2.16 on 2024-12-13 19:19

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0084_client_phone_number"),
    ]

    operations = [
        migrations.Remove<PERSON>ield(
            model_name="historicalmeetingbot",
            name="end_time",
        ),
        migrations.Remove<PERSON>ield(
            model_name="historicalmeetingbot",
            name="processing_end_time",
        ),
        migrations.RemoveField(
            model_name="historicalmeetingbot",
            name="processing_start_time",
        ),
        migrations.RemoveField(
            model_name="historicalmeetingbot",
            name="recording_end_time",
        ),
        migrations.RemoveField(
            model_name="historicalmeetingbot",
            name="recording_start_time",
        ),
        migrations.RemoveField(
            model_name="historicalmeetingbot",
            name="start_time",
        ),
        migrations.RemoveField(
            model_name="meetingbot",
            name="end_time",
        ),
        migrations.RemoveField(
            model_name="meetingbot",
            name="processing_end_time",
        ),
        migrations.Remo<PERSON><PERSON><PERSON>(
            model_name="meetingbot",
            name="processing_start_time",
        ),
        migrations.Remove<PERSON>ield(
            model_name="meetingbot",
            name="recording_end_time",
        ),
        migrations.RemoveField(
            model_name="meetingbot",
            name="recording_start_time",
        ),
        migrations.RemoveField(
            model_name="meetingbot",
            name="start_time",
        ),
        migrations.AlterField(
            model_name="historicalmeetingbot",
            name="meeting_link",
            field=models.CharField(
                blank=True,
                help_text="The link to the meeting that the bot will join. This may mean different things depending on the bot provider; it is essentially an identifier that tells the bot provider how to access a meeting.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="historicalmeetingbot",
            name="recall_bot_id",
            field=models.CharField(
                blank=True,
                help_text="The ID of the bot. For historical reasoncs, this is called 'recall_bot_id', but it is in fact the ID of the bot/notetaker in the relevant system (e.g., Twilio, Recall).",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="meetingbot",
            name="meeting_link",
            field=models.CharField(
                blank=True,
                help_text="The link to the meeting that the bot will join. This may mean different things depending on the bot provider; it is essentially an identifier that tells the bot provider how to access a meeting.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="meetingbot",
            name="recall_bot_id",
            field=models.CharField(
                blank=True,
                help_text="The ID of the bot. For historical reasoncs, this is called 'recall_bot_id', but it is in fact the ID of the bot/notetaker in the relevant system (e.g., Twilio, Recall).",
                null=True,
            ),
        ),
    ]
