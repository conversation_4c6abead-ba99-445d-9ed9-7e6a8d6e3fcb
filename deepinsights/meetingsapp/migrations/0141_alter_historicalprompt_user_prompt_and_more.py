# Generated by Django 4.2.20 on 2025-05-24 16:45

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0140_rename_text_to_user_prompt_and_add_system_prompt"),
    ]

    operations = [
        migrations.AlterField(
            model_name="historicalprompt",
            name="user_prompt",
            field=models.TextField(
                blank=True,
                help_text="User level instructions for the LLM which passes user specific data to the model.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="prompt",
            name="user_prompt",
            field=models.TextField(
                blank=True,
                help_text="User level instructions for the LLM which passes user specific data to the model.",
                null=True,
            ),
        ),
    ]
