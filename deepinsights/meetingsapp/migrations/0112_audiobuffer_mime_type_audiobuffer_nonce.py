# Generated by Django 4.2.18 on 2025-02-27 00:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("meetingsapp", "0111_alter_historicalorganization_crm_configuration_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="audiobuffer",
            name="mime_type",
            field=models.CharField(
                blank=True,
                help_text="The MIME type of the audio data (e.g., audio/webm;codecs=opus, audio/mp4).",
                max_length=255,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="audiobuffer",
            name="nonce",
            field=models.CharField(
                blank=True,
                help_text="A nonce that can be used to match related buffers when there are multiple sets of buffers associated with a note. This is useful for cases where the user starts recording an audio stream, then resets and starts recording again.",
                max_length=255,
                null=True,
            ),
        ),
    ]
