# Generated by Django 4.2 on 2023-08-25 17:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('meetingsapp', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='task',
            name='task_owner',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='recording',
            name='meeting',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='meetingsapp.meeting'),
        ),
        migrations.AddField(
            model_name='note',
            name='meeting',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='meetingsapp.meeting'),
        ),
        migrations.AddField(
            model_name='note',
            name='note_owner',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='meeting',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='zoom_auth_user', to=settings.AUTH_USER_MODEL),
        ),
    ]
