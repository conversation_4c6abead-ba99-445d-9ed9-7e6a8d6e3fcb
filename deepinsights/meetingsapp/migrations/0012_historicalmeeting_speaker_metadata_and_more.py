# Generated by Django 4.2 on 2023-10-29 20:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('meetingsapp', '0011_merge_20231029_2010'),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name='historicalmeeting',
            name='speaker_metadata',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicalmeeting',
            name='transcript',
            field=models.CharField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='historicalmeeting',
            name='calendar_id',
            field=models.CharField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='historicalmeeting',
            name='calendar_object',
            field=models.JSONField(null=True),
        ),
        migrations.AlterField(
            model_name='historicalmeeting',
            name='timezone',
            field=models.Char<PERSON>ield(blank=True, null=True),
        ),
    ]
