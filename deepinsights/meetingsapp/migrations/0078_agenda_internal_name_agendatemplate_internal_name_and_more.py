# Generated by Django 4.2.16 on 2024-11-22 20:11

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("meetingsapp", "0077_meetingtype_users_alter_meetingtype_everyone_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="agenda",
            name="internal_name",
            field=models.TextField(
                blank=True,
                help_text="An internally-visible name used in the admin console to identify this type of meeting. This should never be shown to users; it is only for internal reference.",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="agendatemplate",
            name="internal_name",
            field=models.TextField(
                blank=True,
                help_text="An internally-visible name used in the admin console to identify this type of meeting. This should never be shown to users; it is only for internal reference.",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="historicalagenda",
            name="internal_name",
            field=models.TextField(
                blank=True,
                help_text="An internally-visible name used in the admin console to identify this type of meeting. This should never be shown to users; it is only for internal reference.",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="historicalmeetingfollowup",
            name="internal_name",
            field=models.CharField(
                blank=True,
                help_text="An internally-visible name used in the admin console to identify this type of meeting. This should never be shown to users; it is only for internal reference.",
                max_length=200,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="meetingfollowup",
            name="internal_name",
            field=models.CharField(
                blank=True,
                help_text="An internally-visible name used in the admin console to identify this type of meeting. This should never be shown to users; it is only for internal reference.",
                max_length=200,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="meetingfollowuptemplate",
            name="internal_name",
            field=models.CharField(
                blank=True,
                help_text="An internally-visible name used in the admin console to identify this type of meeting. This should never be shown to users; it is only for internal reference.",
                max_length=200,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="meetingtype",
            name="internal_name",
            field=models.TextField(
                blank=True,
                help_text="An internally-visible name used in the admin concole to identify this type of meeting. This should never be shown to users; it is only for internal reference.",
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="agenda",
            name="context",
            field=models.TextField(
                blank=True,
                help_text="Context that can be provided to a language model when using data from this object as an input.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="agenda",
            name="note",
            field=models.ForeignKey(
                blank=True,
                help_text="The note this agenda is associated with (or none).",
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="meetingsapp.note",
            ),
        ),
        migrations.AlterField(
            model_name="agenda",
            name="sections",
            field=models.JSONField(
                help_text="The details of the agenda. Expected to contain a list of AgendaItem objects."
            ),
        ),
        migrations.AlterField(
            model_name="agenda",
            name="title",
            field=models.CharField(
                blank=True,
                help_text="A title for this agenda. Displayed to the user in the UI, and to staff in the admin UI.",
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="agendatemplate",
            name="context",
            field=models.TextField(
                blank=True,
                help_text="Context that can be provided to a language model when using data from this object as an input.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="agendatemplate",
            name="sections",
            field=models.JSONField(
                help_text="The details of the agenda. Expected to contain a list of AgendaItem objects."
            ),
        ),
        migrations.AlterField(
            model_name="agendatemplate",
            name="title",
            field=models.CharField(
                blank=True,
                help_text="A title for this agenda. Displayed to the user in the UI, and to staff in the admin UI.",
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="historicalagenda",
            name="context",
            field=models.TextField(
                blank=True,
                help_text="Context that can be provided to a language model when using data from this object as an input.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="historicalagenda",
            name="note",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                help_text="The note this agenda is associated with (or none).",
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                to="meetingsapp.note",
            ),
        ),
        migrations.AlterField(
            model_name="historicalagenda",
            name="sections",
            field=models.JSONField(
                help_text="The details of the agenda. Expected to contain a list of AgendaItem objects."
            ),
        ),
        migrations.AlterField(
            model_name="historicalagenda",
            name="title",
            field=models.CharField(
                blank=True,
                help_text="A title for this agenda. Displayed to the user in the UI, and to staff in the admin UI.",
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="historicalmeetingfollowup",
            name="context",
            field=models.TextField(
                blank=True,
                help_text="Context that can be provided to a language model when using data from this object as an input.This should be provided by an engineer on the team, based on an understanding of the customer's needs.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="historicalmeetingfollowup",
            name="data",
            field=models.JSONField(
                blank=True,
                help_text="The data associated with this follow-up. If schema is non-null, it's expected that the data in this field will adhere to the schema (and will not contain extra fields not in the schema).",
            ),
        ),
        migrations.AlterField(
            model_name="historicalmeetingfollowup",
            name="kind",
            field=models.CharField(
                help_text="What 'kind' of follow-up template this is. Since we do not know in advance what kind of meeting follow up types we will need, and how the frontend might need to render them, this is essentially an escape valve to allow us to have customized logic on the frontend. It is also used to provide an identifier for shared templates that are generated by a database migration, and which we may need to reference in the future to modify it (or remove it)."
            ),
        ),
        migrations.AlterField(
            model_name="historicalmeetingfollowup",
            name="note",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                help_text="The note this follow-up is associated with (or none).",
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                to="meetingsapp.note",
            ),
        ),
        migrations.AlterField(
            model_name="historicalmeetingfollowup",
            name="schema",
            field=models.JSONField(
                blank=True,
                help_text="The details of the data format for follow-up template. This is intended to be a valid JSON schema (https://json-schema.org/), and may be used to generate a form on the frontend that will be used to update the follow-up data. In most cases, this should be non-null, but that is not enforced at the database level. If present, this will be used to validate the data field of a follow-up created from this template.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="historicalmeetingfollowup",
            name="title",
            field=models.CharField(
                blank=True,
                help_text="The title of this follow-up template. This is mainly for humans and will be shown to users in the app interface, but it is also conceivable that it could be passed to the language model as context.",
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="meetingfollowup",
            name="context",
            field=models.TextField(
                blank=True,
                help_text="Context that can be provided to a language model when using data from this object as an input.This should be provided by an engineer on the team, based on an understanding of the customer's needs.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="meetingfollowup",
            name="data",
            field=models.JSONField(
                blank=True,
                help_text="The data associated with this follow-up. If schema is non-null, it's expected that the data in this field will adhere to the schema (and will not contain extra fields not in the schema).",
            ),
        ),
        migrations.AlterField(
            model_name="meetingfollowup",
            name="kind",
            field=models.CharField(
                help_text="What 'kind' of follow-up template this is. Since we do not know in advance what kind of meeting follow up types we will need, and how the frontend might need to render them, this is essentially an escape valve to allow us to have customized logic on the frontend. It is also used to provide an identifier for shared templates that are generated by a database migration, and which we may need to reference in the future to modify it (or remove it)."
            ),
        ),
        migrations.AlterField(
            model_name="meetingfollowup",
            name="note",
            field=models.ForeignKey(
                blank=True,
                help_text="The note this follow-up is associated with (or none).",
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="meetingsapp.note",
            ),
        ),
        migrations.AlterField(
            model_name="meetingfollowup",
            name="schema",
            field=models.JSONField(
                blank=True,
                help_text="The details of the data format for follow-up template. This is intended to be a valid JSON schema (https://json-schema.org/), and may be used to generate a form on the frontend that will be used to update the follow-up data. In most cases, this should be non-null, but that is not enforced at the database level. If present, this will be used to validate the data field of a follow-up created from this template.",
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="meetingfollowup",
            name="title",
            field=models.CharField(
                blank=True,
                help_text="The title of this follow-up template. This is mainly for humans and will be shown to users in the app interface, but it is also conceivable that it could be passed to the language model as context.",
                max_length=200,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="meetingtype",
            name="category",
            field=models.CharField(
                choices=[("client", "Client"), ("internal", "Internal"), ("debrief", "Debrief")],
                default="client",
                help_text="The category for this meeting. Meetings with external clients have additional requirements, such as consent for notetaking.<br><br>- client: a meeting with a client or clients<br>- internal: an internal meeting within a firm, with no clients present<br>- debrief: a meeting the advisor has by themselves or with a small internal team to discuss a previous interaction",
            ),
        ),
        migrations.AlterField(
            model_name="meetingtype",
            name="key",
            field=models.CharField(
                blank=True,
                help_text="A key that maps this meeting type to the older hard-coded meeting types. This should usually be left blank. If there is no key, then this meeting type has no mapping to the older hard-coded meeting types. You should generally leave this blank.",
                null=True,
                unique=True,
            ),
        ),
        migrations.AlterField(
            model_name="meetingtype",
            name="name",
            field=models.CharField(
                help_text='The user-visible title/name of this type of meeting (e.g., "Client", "Internal"). This may correspond to the key above. The key, however, is unique, whereas multiple meeting types can have the same user-visible name.',
                max_length=200,
            ),
        ),
    ]
