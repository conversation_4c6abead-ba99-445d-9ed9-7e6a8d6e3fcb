from django.db import migrations
from django.db.models import Q


def update_clients(apps, schema_editor):
    Client = apps.get_model("meetingsapp", "Client")
    Note = apps.get_model("meetingsapp", "Note")
    Attendee = apps.get_model("meetingsapp", "Attendee")
    MeetingPrep = apps.get_model("meetingsapp", "MeetingPrep")
    db_alias = schema_editor.connection.alias

    unique_clients = {}

    for client in Client.objects.using(db_alias).iterator():
        key = (client.crm_id, client.organization)

        if key not in unique_clients:
            new_client = Client.objects.using(db_alias).create(
                name=client.name,
                first_name=client.first_name,
                last_name=client.last_name,
                job_title=client.job_title,
                email=client.email,
                organization=client.organization,
                crm_id=client.crm_id,
                client_type=client.client_type,
                crm_system=client.crm_system,
            )

            unique_clients[key] = new_client

        if client.advisor:
            unique_clients.get(key).authorized_users.add(client.advisor)
            unique_clients.get(key).save()

        meeting_preps = MeetingPrep.objects.using(db_alias).filter(client=client).only("client")

        for meeting_prep in meeting_preps:
            meeting_prep.client = unique_clients.get(key)
            meeting_prep.save()

        notes = Note.objects.using(db_alias).filter(Q(client__uuid=str(client.uuid))).only("client")

        for note in notes:
            note_client = note.client
            note_client["uuid"] = str(unique_clients.get(key).uuid)
            note_client["name"] = unique_clients.get(key).name
            note.client = note_client
            note.save()

        attendees = Attendee.objects.using(db_alias).filter(client=client).only("client")

        for attendee in attendees:
            attendee.client = unique_clients.get(key)
            attendee.save()

    Client.objects.using(db_alias).exclude(advisor=None).update(is_deleted=True)


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0062_alter_client_advisor"),
    ]

    operations = [
        migrations.RunPython(update_clients, elidable=True),
    ]
