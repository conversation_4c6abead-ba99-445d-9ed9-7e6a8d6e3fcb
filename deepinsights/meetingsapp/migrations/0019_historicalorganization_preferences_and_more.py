# Generated by Django 4.2 on 2024-01-13 08:18

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0018_alter_historicalnote_file_path_alter_note_file_path_and_more"),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name="historicalorganization",
            name="preferences",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name="organization",
            name="preferences",
            field=models.J<PERSON><PERSON>ield(blank=True, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="historicalorganization",
            name="crm_configuration",
            field=models.<PERSON><PERSON><PERSON>ield(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="historicalorganization",
            name="metadata",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="organization",
            name="crm_configuration",
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name="organization",
            name="metadata",
            field=models.J<PERSON><PERSON><PERSON>(blank=True, null=True),
        ),
    ]
