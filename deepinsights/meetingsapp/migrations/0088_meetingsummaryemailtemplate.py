# Generated by Django 4.2.16 on 2024-12-26 22:26

from django.conf import settings
from django.db import migrations, models
import django.utils.timezone
import model_utils.fields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0087_searchquery"),
    ]

    operations = [
        migrations.CreateModel(
            name="MeetingSummaryEmailTemplate",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ("name", models.CharField(help_text="The user-visible name of this email template", max_length=200)),
                (
                    "internal_name",
                    models.TextField(
                        blank=True,
                        help_text="An internally-visible name used in the admin console to identify this template. This should never be shown to users; it is only for internal reference.Can include the client name.",
                        max_length=200,
                        null=True,
                    ),
                ),
                (
                    "system_prompt",
                    models.TextField(
                        default="You are a financial advisor's assistant tasked with drafting follow-up emails after client meetings. Your role is to create personalized, compliant emails based on meeting summaries and client preferences.",
                        help_text="The system prompt for this email generator.",
                    ),
                ),
                (
                    "generation_prompt",
                    models.TextField(
                        default="Using the following information, generate a follow-up email to be sent by the financial advisor to their client after the meeting. If the client name is empty, adjust email accordingly.\n\nAdvisor Name: {advisor_name}\nClient Name: {client_name}\nTime of Day: {time_of_day}\n\nKey Takeaways:\n{key_takeaways}\n\nAction Items:\n{action_items}\n\nUsing this information, please generate a follow-up email to be sent by the financial advisor to their client after the meeting.\n\nThe follow-up should use the text between <template></template> as a template. You should incorporate the key takeaways and action items into the email as appropriate and match the template style. If the template is an empty string, please write a professional, generic email structure.\nif the template contains <link> </link> tags, replace the link text with html links\n\n<template>{email_template}</template>\n\nInstructions:\n1. Begin the email with the greeting exactly as specified in the template. This is crucial and must always be included.\n2. For the greeting, replace [time of day] with the provided time of day (morning, afternoon, or evening).\n3. For the greeting, replace [Client Name] with the first name that isn't the advisor's name from the provided client names.\n4. Carefully read the provided key takeaways and action items from the meeting summary.\n5. Use the provided template as the base structure for the email. Do not deviate from this structure. Keep indentation consistent with the template.\n6. Replace any remaining placeholders in the template with the appropriate information provided.\n7. If the template includes a section for key takeaways, incorporate the exact key takeaways provided. Do not add, remove, or modify these takeaways.\n8. If the template includes a section for next steps or action items, use only the provided action items, copying them verbatim and strictly in the same order as provided; do not change the order. If no action items are provided and the template includes a next steps section, leave it blank or remove it entirely.\n9. Do not add any suggestions, next steps, or action items that are not explicitly provided in the input information.\n10. Do not add any sections, paragraphs, or significant content that are not part of the template.\n11. Maintain the exact structure, tone, and content of the template, only filling in the placeholders and incorporating the provided information where explicitly indicated.\n12. Ensure the language complies with SEC and FINRA guidelines for communication by Financial Advisors.\n13. If the template includes a signature or closing, use it exactly as provided. If it doesn't, do not add one.\n14. Do not add any information, suggestions, or content that is not present in the provided key takeaways, action items, or template.\n15. Once generated, double-check that you've included everything, especially the greeting.\n\nif the email contains  html links return the body of the email as valid HTML encoded text (make sure to have the appropriate html tags for hyperlinks.) otherwise return the body of the email as plain text\n\nuse bulleted lists for the action items and key takeaways and not numbered lists.\n\nPlease write in a personal, collaborative first-person voice, being mindful that the language of the email must comply with SEC and FINRA guidelines for communication by Financial Advisors. Output the content within <email> </email> tags.",
                        help_text="The prompt for email generation. a.k.a HUMAN_PROMPT",
                    ),
                ),
                (
                    "email_template_content",
                    models.TextField(
                        blank=True,
                        help_text="The actual email template content. Can include placeholders for dynamic content that will be replaced when generating the email.Can be left empty for a default email.",
                        null=True,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="Optional description of when and how to use this template", null=True
                    ),
                ),
                (
                    "use_html",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the email template should be generated as HTML. If false, the email will be plain text.",
                    ),
                ),
                (
                    "tone",
                    models.CharField(
                        choices=[
                            ("default", "Default"),
                            ("casual", "Casual - Friendly"),
                            ("business", "Business"),
                            ("formal", "Very Formal"),
                        ],
                        default="default",
                        help_text="The overall tone of the email",
                        max_length=20,
                    ),
                ),
                (
                    "wordiness",
                    models.CharField(
                        choices=[
                            ("default", "Default"),
                            ("terse", "Terse"),
                            ("summary", "Summary"),
                            ("verbose", "Verbose"),
                            ("verbatim", "Verbatim"),
                        ],
                        default="default",
                        help_text="How detailed/verbose the email should be",
                        max_length=20,
                    ),
                ),
                (
                    "amount_discussion",
                    models.CharField(
                        choices=[("default", "Default"), ("include", "Include"), ("omit", "Omit")],
                        default="default",
                        help_text="Whether to include specific amounts discussed in the meeting",
                        max_length=20,
                    ),
                ),
                (
                    "voice",
                    models.CharField(
                        choices=[
                            ("default", "Default"),
                            ("user1", "User 1 emails (To be added later)"),
                            ("user2", "User 2 email (To be added)"),
                        ],
                        default="default",
                        help_text="Which voice/persona to use in the email",
                        max_length=20,
                    ),
                ),
                (
                    "everyone",
                    models.BooleanField(
                        default=False,
                        help_text="Whether or not this template is available to everyone. If true, all users have access to this template. If false, only users allowlisted by the organizations list or users list have access.",
                    ),
                ),
                (
                    "organizations",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The organizations which have access to this template. If empty, no specific organizations have access. Users may still have access via the `users` or `everyone` fields.",
                        to="meetingsapp.organization",
                    ),
                ),
                (
                    "users",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The users who have access to this template. If empty, no specific users have access. Users may still have access via the `organizations` or `everyone` fields.",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
