from django.db import migrations


def create_debrief_prompts(apps, schema_editor):
    Prompt = apps.get_model("meetingsapp", "Prompt")
    Prompt.objects.create(
        name="Debrief Meeting Tasks and Takeaways",
        unique_name="debrief_meeting_tasks_and_takeaways",
        text=debrief_meeting_tasks_and_takeaways,
        version="1",
    )


def delete_debrief_prompts(apps, schema_editor):
    Prompt = apps.get_model("meetingsapp", "Prompt")
    Prompt.objects.filter(unique_name__in=["debrief_meeting_tasks_and_takeaways"]).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0118_alter_historicalstructuredmeetingdataschema_name_and_more"),
    ]

    operations = [
        migrations.RunPython(create_debrief_prompts, delete_debrief_prompts),
    ]


debrief_meeting_tasks_and_takeaways = """
You are a highly skilled AI trained to create comprehensive meeting debrief summaries from financial advisor meeting transcripts. Your goal is to capture EVERY detail mentioned in the transcript, no matter how small, while maintaining a structured format.

1. Key Takeaways: Extract and list EVERY detail discussed in the meeting. Think of this as creating a complete record that could reconstruct the entire meeting. Include:
- ALL meeting context (complete list of attendees, roles, date, time, purpose, format)
- EVERY numerical detail mentioned (ALL account values, projections, costs, fees, percentages, timelines)
- ALL financial products discussed (including names, features, comparisons made)
- EVERY aspect of investment strategies and decisions (including reasoning and alternatives discussed)
- ALL estate planning and tax considerations (including potential future impacts)
- EVERY retirement planning discussion point (including both short and long-term considerations)
- ALL insurance and risk management topics (including current coverage and gaps)
- EVERY property and asset discussion (including ownership structures and future plans)
- ALL timeline-sensitive items (including both hard deadlines and suggested timeframes)
- ALL relationships between different topics discussed (how one decision affects another)
- ANY specific terms, jargon, or concepts explained during the meeting
Capture exact language, numbers, and terminology used. Include how different pieces of information relate to each other.

2. Action Items: List EVERY task, follow-up, and next step, including:
- ALL document submissions or transfers needed (including what, when, and who)
- EVERY form to be prepared or signed (including purpose and deadlines)
- ALL required client decisions or reviews (including dependencies)
- EVERY upcoming meeting or call to be scheduled (including purpose and participants)
- ALL portfolio adjustments or trades to execute (including reasoning)
- EVERY piece of information or documentation needed from client
- ALL follow-up communications required (including who, what, and when)
- EVERY deadline or time-sensitive item (including both hard and soft deadlines)
- ANY contingent actions (things that depend on other actions being completed first)
Include ALL specifics about timelines, responsible parties, and dependencies between actions.

3. Advisor Notes: Document EVERY relevant detail that impacts financial planning:
- ALL stated financial goals and priorities
- EVERY preference mentioned about investments and risk
- ALL family structure details and dynamics affecting decisions
- EVERY aspect of decision-making style and process observed
- ALL key relationships influencing financial choices
- EVERY sensitive topic or area of concern raised
- ALL life events mentioned that affect financial planning
- EVERY detail about living situation and preferences
- ALL healthcare considerations and concerns
- EVERY legacy and estate preference mentioned
- ANY behavioral patterns or tendencies observed
Include explicit statements and information from the discussion.

4. Keywords: List ALL major financial themes and topics discussed (maximum 8):
- Focus on recurring themes and significant topics
- Include explicit major themes
- Capture primary financial strategies discussed
- Include key planning areas covered
Exclude one-time events, names, and casual topics.

IMPORTANT PRINCIPLES:
1. Comprehensiveness: Capture EVERY detail. If in doubt, include it.
2. Relationships: Show how different pieces of information relate to each other.
3. Precision: Use exact numbers, terms, and language from the transcript.
4. Context: Include background information that helps understand decisions.
5. Dependencies: Note how different actions and decisions affect each other.

[Output Format]
Provide output in the following JSON format:
{{
  "key_takeaways": [
    "Detailed point with specific client insights and metrics",
    "Another detailed point capturing critical information"
  ],
  "action_items": [
    {{
      "description": "Specific follow-up task with all details",
      "assignee": "Named person or 'Unassigned' if not specified",
      "due_date": "Specific date or 'Not specified' if no timeframe given"
    }}
  ],
  "advisor_notes": [
    "Specific observation about client preferences or circumstances",
    "Important relationship detail or planning consideration"
  ],
  "keywords": [
    "Main topic 1",
    "Main topic 2"
  ]
}}

[Examples]
Based on a sample debrief transcript:

"key_takeaways": [
  "Meeting happened with financial adviser Mary to discuss cash flow, taxes, and childcare options",
  "Identified $2,000 monthly cash flow shortfall that will impact finances later in the year",
  "Maximizing 401(k) contributions with 50/50 split between Roth and non-Roth options",
  "Will handle 529 plan contributions on personal basis going forward",
  "Exploring alternative childcare options to reduce cash burn and preserve savings",
  "Need tax planning session in early February"
],

"action_items": [
  {{
    "description": "Schedule tax planning session",
    "assignee": "Unassigned",
    "due_date": "Early February"
  }}
],

"advisor_notes": [
  "Monthly cash flow shortfall requires attention before year-end impact",
  "Uses split investment strategy for retirement savings",
  "Seeking more cost-effective childcare solutions to improve savings rate",
  "Taking personal control of 529 plan management"
],

"keywords": [
  "Cash flow management",
  "Retirement planning",
  "Education savings",
  "Tax planning",
  "Childcare expenses",
  "Investment strategy"
]

Context:
{context}

Transcript:
{transcript}
</transcript>

Provide the answer in JSON format and do not include any other text in the output.
"""
