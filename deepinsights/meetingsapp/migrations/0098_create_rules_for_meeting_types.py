# Generated by Django 4.2.16 on 2025-01-03 00:30
from django.db import migrations


def create_rules_for_meeting_types(apps, schema_editor):
    MeetingType = apps.get_model("meetingsapp", "MeetingType")
    StructuredMeetingDataTemplateRule = apps.get_model("meetingsapp", "StructuredMeetingDataTemplateRule")
    db_alias = schema_editor.connection.alias

    for meeting_type in MeetingType.objects.using(db_alias).all():
        if not meeting_type.meeting_follow_up_templates.exists():
            continue
        rule = StructuredMeetingDataTemplateRule.objects.using(db_alias).create(
            everyone=meeting_type.everyone,
        )
        rule.meeting_types.set([meeting_type])
        rule.follow_up_templates.set(meeting_type.meeting_follow_up_templates.all())
        rule.organizations.set(meeting_type.organizations.all())
        rule.users.set(meeting_type.users.all())
        rule.save()


def migrate_rules_to_meeting_types(apps, schema_editor):
    StructuredMeetingDataTemplateRule = apps.get_model("meetingsapp", "StructuredMeetingDataTemplateRule")
    db_alias = schema_editor.connection.alias

    for rule in StructuredMeetingDataTemplateRule.objects.using(db_alias).all():
        for meeting_type in rule.meeting_types.all():
            meeting_type.meeting_follow_up_templates.set(rule.follow_up_templates.all())
            meeting_type.save()


class Migration(migrations.Migration):
    dependencies = [("meetingsapp", "0097_structuredmeetingdatatemplaterule")]

    operations = [
        migrations.RunPython(
            create_rules_for_meeting_types,
            migrate_rules_to_meeting_types,
        ),
    ]
