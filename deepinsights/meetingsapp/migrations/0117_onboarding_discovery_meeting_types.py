# Generated by Django 4.2.18 on 2025-03-07 20:31


from django.db import migrations

from deepinsights.meetingsapp.migrations import (
    annual_review_agenda_initial_data,
    annual_review_agenda_kind,
    discovery_agenda_initial_data,
    discovery_agenda_kind,
    discovery_meeting_key,
    onboarding_agenda_initial_data,
    onboarding_agenda_kind,
    onboarding_meeting_key,
    pre_meeting_agenda_generator_prompt_key,
    pre_meeting_agenda_generator_prompt_text,
    unstructured_meeting_data_schema_name,
)


def create_meeting_types(apps, schema_editor):
    MeetingType = apps.get_model("meetingsapp", "MeetingType")
    Prompt = apps.get_model("meetingsapp", "Prompt")
    StructuredMeetingDataTemplate = apps.get_model("meetingsapp", "StructuredMeetingDataTemplate")
    StructuredMeetingDataSchema = apps.get_model("meetingsapp", "StructuredMeetingDataSchema")

    db_alias = schema_editor.connection.alias

    # The objects may exist in some environments already.
    update_or_create_meeting_type = MeetingType.objects.using(db_alias).update_or_create
    update_or_create_structured_meeting_data_template = StructuredMeetingDataTemplate.objects.using(
        db_alias
    ).update_or_create

    unstructured_text_schema = StructuredMeetingDataSchema.objects.get(name=unstructured_meeting_data_schema_name)

    # Create a prompt for generating agendas.
    agenda_generator_prompt = Prompt.objects.create(
        name="Pre-meeting Agenda Generator",
        unique_name=pre_meeting_agenda_generator_prompt_key,
        version="1.0",
        text=pre_meeting_agenda_generator_prompt_text,
    )

    # Create the discovery meeting type.
    discovery_meeting, _ = update_or_create_meeting_type(
        key=discovery_meeting_key,
        name="Discovery Meeting",
        internal_name="Discovery Meeting",
        category="client",
        everyone=True,
    )
    discovery_agenda, _ = update_or_create_structured_meeting_data_template(
        title="Discovery Agenda",
        internal_name="Discovery Agenda",
        kind=discovery_agenda_kind,
        initial_data=discovery_agenda_initial_data,
        schema_definition=unstructured_text_schema,
        prompt=agenda_generator_prompt,
    )
    discovery_meeting.agenda_templates.add(discovery_agenda)
    discovery_meeting.save()

    # Create an onboarding meeting type.
    onboarding_meeting, _ = update_or_create_meeting_type(
        key="onboarding_meeting",
        name="Onboarding Meeting",
        internal_name="Onboarding Meeting",
        category="client",
        everyone=True,
    )
    onboarding_agenda, _ = update_or_create_structured_meeting_data_template(
        title="Onboarding Agenda",
        internal_name="Onboarding Agenda",
        kind=onboarding_agenda_kind,
        initial_data=onboarding_agenda_initial_data,
        schema_definition=unstructured_text_schema,
        prompt=agenda_generator_prompt,
    )
    onboarding_meeting.agenda_templates.add(onboarding_agenda)
    onboarding_meeting.save()

    annual_review_meeting = MeetingType.objects.using(db_alias).get(key="annual_review_sequoia")
    annual_review_agenda, _ = update_or_create_structured_meeting_data_template(
        title="Annual Review Agenda",
        internal_name="Annual Review Agenda",
        kind=annual_review_agenda_kind,
        initial_data=annual_review_agenda_initial_data,
        schema_definition=unstructured_text_schema,
        prompt=agenda_generator_prompt,
    )
    annual_review_meeting.agenda_templates.add(annual_review_agenda)
    annual_review_meeting.save()


def delete_meeting_types(apps, schema_editor):
    MeetingType = apps.get_model("meetingsapp", "MeetingType")
    StructuredMeetingDataTemplate = apps.get_model("meetingsapp", "StructuredMeetingDataTemplate")
    Prompt = apps.get_model("meetingsapp", "Prompt")
    db_alias = schema_editor.connection.alias

    StructuredMeetingDataTemplate.objects.using(db_alias).filter(
        kind__in=[discovery_agenda_kind, onboarding_agenda_kind, annual_review_agenda_kind]
    ).delete()
    MeetingType.objects.using(db_alias).filter(key__in=[discovery_meeting_key, onboarding_meeting_key]).delete()
    Prompt.objects.using(db_alias).filter(unique_name=pre_meeting_agenda_generator_prompt_key).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0116_add_agenda_followup_prompt"),
    ]

    operations = [migrations.RunPython(create_meeting_types, reverse_code=delete_meeting_types)]
