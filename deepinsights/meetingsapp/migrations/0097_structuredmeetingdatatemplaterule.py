# Generated by Django 4.2.16 on 2025-01-04 04:57

import deepinsights.utils.choice_array_field
from django.conf import settings
from django.db import migrations, models
import django.utils.timezone
import model_utils.fields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0096_remove_structuredmeetingdatatemplate_schema"),
    ]

    operations = [
        migrations.CreateModel(
            name="StructuredMeetingDataTemplateRule",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                (
                    "internal_name",
                    models.TextField(
                        blank=True,
                        help_text="An internally-visible name used in the admin concole to identify this rule. This should never be shown to users; it is only for internal reference.",
                        max_length=200,
                        null=True,
                    ),
                ),
                (
                    "meeting_categories",
                    deepinsights.utils.choice_array_field.ChoiceArrayField(
                        base_field=models.CharField(
                            blank=True, choices=[("client", "Client"), ("internal", "Internal"), ("debrief", "Debrief")]
                        ),
                        blank=True,
                        default=list,
                        help_text="The meeting type categories for which this rule should be applied. See the definition of MeetingType.Category for more information about what these mean. If this is empty, then this rule applies to no meeting type categories.",
                        size=None,
                    ),
                ),
                (
                    "everyone",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this template rule should apply to everyone (true) or only to specific users or organizations (false). ",
                    ),
                ),
                (
                    "follow_up_templates",
                    models.ManyToManyField(
                        help_text="The structured meeting data templates that should be associated with this rule.",
                        to="meetingsapp.structuredmeetingdatatemplate",
                    ),
                ),
                (
                    "meeting_types",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The meeting types this rule applies to. If this is empty, then this rule applies to no meeting types.",
                        to="meetingsapp.meetingtype",
                    ),
                ),
                (
                    "organizations",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The organizations to which this template rule applies. If this is empty, then no specific organizations will have this rule applied to their meetings. Users may still have this rule applied per the `users` or `everyone` fields.",
                        to="meetingsapp.organization",
                    ),
                ),
                (
                    "users",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The users who have access to this type of meeting. If this is empty, then no specific users will have this rule applied to their meetings. Users may still have this rule applied per the `organizations` or `everyone` fields.",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
