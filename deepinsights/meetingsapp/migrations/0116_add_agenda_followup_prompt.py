from django.db import migrations

prompt_unique_name = "agenda_followup_generator"


def create_agenda_followup_prompt(apps, schema_editor):
    Prompt = apps.get_model("meetingsapp", "Prompt")
    db_alias = schema_editor.connection.alias

    # Create the agenda follow-up prompt
    Prompt.objects.using(db_alias).create(
        name="Agenda Follow-up Generator",
        unique_name=prompt_unique_name,
        version="1.0",
        text="""You are analyzing a meeting transcript to create structured meeting data based on an agenda template.
Your task is to extract how each agenda item was discussed and present it as a structured JSON object following a specific schema.

# SCHEMA:
{schema}

# AGENDA TEMPLATE (MARKDOWN FORMAT):
{agenda_template}

# MEETING TRANSCRIPT:
{transcript}

## INSTRUCTIONS:
1. First, parse the markdown agenda template to identify headers and bullet points:
   - Headers are typically denoted by "#", "##", etc.
   - Bullet points are typically denoted by "-", "*", or numeric lists

2. For each header in the agenda, create a header entry in the output JSON

3. For each bullet point under a header, create a toggle entry that shows whether the topic was discussed:
   - Set "discussed" to true if there is evidence the topic was discussed in the transcript
   - Set "discussed" to false if the topic was not discussed
   - Provide an "explanation" with bullet points explaining what was discussed for that topic
   - Use "•" for bullet points in the explanation field with line breaks between points

## OUTPUT FORMAT:
Your output must be valid JSON that follows this structure:
- The root object contains a single "review_entries" array
- Each entry in the array is either a header (for sections) or a toggle (for topics)
- Headers have "kind":"header" and contain the section name
- Toggles have "kind":"toggle", with "discussed" and "explanation" fields

## EXAMPLE:

Given a markdown agenda template like:
```markdown
# Investment Overview
- Current portfolio performance
- Asset allocation strategy
- Market outlook

# Retirement Planning
- Contribution updates
- Withdrawal strategy
- Tax considerations
```

The output JSON should look like:
```json
{{
  "review_entries": [
    {{
      "id": "investment_overview",
      "kind": "header",
      "topic": "Investment Overview"
    }},
    {{
      "id": "current_portfolio_performance",
      "kind": "toggle",
      "topic": "Current portfolio performance",
      "discussed": true,
      "explanation": "• Portfolio grew by 8.2% over the past quarter\n• Technology stocks outperformed expectations\n• Fixed income investments underperformed due to interest rate changes"
    }},
    {{
      "id": "asset_allocation_strategy",
      "kind": "toggle",
      "topic": "Asset allocation strategy",
      "discussed": true,
      "explanation": "• Agreed to increase equity allocation from 60% to 65%\n• Will reduce bond exposure due to interest rate environment\n• Considering adding alternative investments for diversification"
    }},
    {{
      "id": "market_outlook",
      "kind": "toggle",
      "topic": "Market outlook",
      "discussed": false,
      "explanation": ""
    }},
    {{
      "id": "retirement_planning",
      "kind": "header",
      "topic": "Retirement Planning"
    }},
    {{
      "id": "contribution_updates",
      "kind": "toggle",
      "topic": "Contribution updates",
      "discussed": true,
      "explanation": "• Client increased 401(k) contribution to the maximum allowable\n• Will make additional $6,000 IRA contribution before tax deadline"
    }},
    {{
      "id": "withdrawal_strategy",
      "kind": "toggle",
      "topic": "Withdrawal strategy",
      "discussed": false,
      "explanation": ""
    }},
    {{
      "id": "tax_considerations",
      "kind": "toggle",
      "topic": "Tax considerations",
      "discussed": true,
      "explanation": "• Discussed Roth conversion strategy for this year\n• Projected tax impact of retirement withdrawals\n• Recommended tax-loss harvesting opportunities"
    }}
  ]
}}
```

IMPORTANT:
1. First parse the markdown agenda template to identify headers and bullet points
2. Generate valid JSON with no trailing commas
3. Include ALL agenda topics as toggles
4. Make IDs lowercase with underscores
5. For each toggle, set "discussed" to true ONLY if there is clear evidence in the transcript
6. For discussed topics, provide detailed bullet points in the explanation field
7. Format explanations with bullet points using the "•" character and line breaks
8. Return ONLY the JSON with no additional commentary""",
    )


def reverse_agenda_followup_prompt(apps, schema_editor):
    """Reverse the migration by deleting the prompt"""
    Prompt = apps.get_model("meetingsapp", "Prompt")
    db_alias = schema_editor.connection.alias

    # Delete prompt
    Prompt.objects.using(db_alias).filter(unique_name=prompt_unique_name).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0115_organization_make_uuid_unique_timestamps_nonnull"),
    ]

    operations = [
        migrations.RunPython(create_agenda_followup_prompt, reverse_agenda_followup_prompt),
    ]
