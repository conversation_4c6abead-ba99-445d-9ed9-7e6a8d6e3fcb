# Generated by Django 4.2.16 on 2024-12-26 18:28

import uuid

import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0092_add_summary_prompt"),
    ]

    operations = [
        migrations.CreateModel(
            name="StructuredMeetingDataSchema",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the schema. This is used for reference and displayed in the admin interface.",
                        max_length=200,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Description of what this schema is used for and any special considerations.",
                        null=True,
                    ),
                ),
                ("schema", models.JSONField(help_text="The JSON schema definition that will be used for validation.")),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="HistoricalStructuredMeetingDataSchema",
            fields=[
                ("id", models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("uuid", models.UUIDField(db_index=True, default=uuid.uuid4, editable=False)),
                (
                    "name",
                    models.CharField(
                        help_text="Name of the schema. This is used for reference and displayed in the admin interface.",
                        max_length=200,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Description of what this schema is used for and any special considerations.",
                        null=True,
                    ),
                ),
                ("schema", models.JSONField(help_text="The JSON schema definition that will be used for validation.")),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")], max_length=1),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical structured meeting data schema",
                "verbose_name_plural": "historical structured meeting data schemas",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.AddField(
            model_name="structuredmeetingdatatemplate",
            name="schema_definition",
            field=models.ForeignKey(
                blank=True,
                help_text="Reference to the schema definition that will be used for validation.",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="meetingsapp.structuredmeetingdataschema",
            ),
        ),
    ]
