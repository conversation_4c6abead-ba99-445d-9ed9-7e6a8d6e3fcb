# Generated by Django 4.2.18 on 2025-02-11 18:30

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("meetingsapp", "0105_alter_meetingprep_client_alter_meetingprep_user_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="AudioBuffer",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="created"
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now, editable=False, verbose_name="modified"
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("uuid", models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ("data", models.BinaryField(default=b"", help_text="The audio data for this chunk.")),
                (
                    "sequence",
                    models.IntegerField(
                        default=-1,
                        help_text="The sequence number of this chunk.This is used to order the chunks when generating a combined audio file.",
                    ),
                ),
                (
                    "duration",
                    models.IntegerField(
                        blank=True,
                        help_text="The duration of this chunk in seconds. This is used for generating a M3U8 playlist for chunks that are HLS stream chunks.",
                        null=True,
                    ),
                ),
                (
                    "note",
                    models.ForeignKey(
                        blank=True,
                        help_text="The note this audio buffer is associated with.",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="audio_buffers",
                        to="meetingsapp.note",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
