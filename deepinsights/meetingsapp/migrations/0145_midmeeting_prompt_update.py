from django.db import migrations


def revert_prompt(apps, schema_editor) -> None:
    old_text = """You are analyzing a meeting transcript to create structured meeting data based on an agenda template.
Your task is to extract whether each agenda item was discussed and present items that were not discussed as comma-separated text. 

# SCHEMA:
{schema}

# AGENDA TEMPLATE (MARKDOWN FORMAT):
{agenda_template}

# MEETING TRANSCRIPT:
{transcript}

## INSTRUCTIONS:
1. First, parse the markdown agenda template to identify headers and bullet points:
   - Headers are typically denoted by "#", "##", etc.
   - Bullet points are typically denoted by "-", "*", or numeric lists

2. For each header in the agenda, if the transcript shows that it was not discussed, add the text of the header entry in the output.

## OUTPUT FORMAT:
Your output must be a list of comma-separated header text values, one for each header that was not discussed in the transcript.

## EXAMPLE:

Given a markdown agenda template like:
```markdown
# Investment Overview
- Current portfolio performance
- Asset allocation strategy
- Market outlook

# Retirement Planning
- Contribution updates
- Withdrawal strategy
- Tax considerations
```

The output text should look like:
```
Retirement Planning
```

IMPORTANT:
1. First parse the markdown agenda template to identify headers and bullet points, then compare to the transcript.
2. Return ONLY the headers that were not discussed, with no additional commentary
3. Return ONLY the output, with no analysis."""

    Prompt = apps.get_model("meetingsapp", "Prompt")

    completion_prompt = Prompt.objects.filter(unique_name="agenda_completion_generator").first()
    completion_prompt.version = "1"
    completion_prompt.user_prompt = old_text
    completion_prompt.save()


def update_prompt(apps, schema_editor) -> None:
    new_text = """You are analyzing a meeting transcript to create structured meeting data based on an agenda template.
Your task is to extract whether each agenda item was discussed and present items that were not discussed as comma-separated text. 

# AGENDA TEMPLATE (MARKDOWN FORMAT):
{agenda_template}

# MEETING TRANSCRIPT:
{transcript}

## INSTRUCTIONS:
1. First, parse the markdown agenda template to identify headers and bullet points:
   - Headers are typically denoted by "#", "##", etc.
   - Bullet points are typically denoted by "-", "*", or numeric lists

2. For each header in the agenda, if the transcript shows that it was not discussed, add the text of the header entry in the output.

## OUTPUT FORMAT:
Your output must be a list of comma-separated header text values, one for each header that was not discussed in the transcript.

## EXAMPLE:

Given a markdown agenda template like:
```markdown
# Objective: Check in with client

# Agenda
## Investment Overview
- Current portfolio performance
- Asset allocation strategy
- Market outlook

## Retirement Planning
- Contribution updates
- Withdrawal strategy
- Tax considerations

## Insurance Planning
- Insurance updates 
- New insurance

## Personal and Business Developments
- Personal developments
- Changes to business
```

The output text should look like:
```
Investment Overview, Retirement Planning, Personal and Business Developments
```

IMPORTANT:
1. First parse the markdown agenda template to identify headers and bullet points, then compare to the transcript.
2. Return ONLY the headers that were not discussed, with no additional commentary
3. Return ONLY the output, with no analysis."""

    Prompt = apps.get_model("meetingsapp", "Prompt")
    completion_prompt = Prompt.objects.filter(unique_name="agenda_completion_generator").first()

    completion_prompt.version = "2"
    completion_prompt.user_prompt = new_text
    completion_prompt.save()


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0144_update_default_email_template"),
    ]

    operations = [
        migrations.RunPython(update_prompt, revert_prompt),
    ]
