from django.db import migrations


def forward_schema_data(apps, schema_editor):
    StructuredMeetingDataTemplate = apps.get_model("meetingsapp", "StructuredMeetingDataTemplate")
    StructuredMeetingDataSchema = apps.get_model("meetingsapp", "StructuredMeetingDataSchema")

    default_schema = StructuredMeetingDataSchema.objects.get(name="structured_data_schema")

    for template in StructuredMeetingDataTemplate.objects.all():
        template.schema_definition = default_schema
        template.save()


def reverse_schema_data(apps, schema_editor):
    StructuredMeetingDataTemplate = apps.get_model("meetingsapp", "StructuredMeetingDataTemplate")
    StructuredMeetingDataSchema = apps.get_model("meetingsapp", "StructuredMeetingDataSchema")

    default_schema = StructuredMeetingDataSchema.objects.get(name="structured_data_schema")

    for template in StructuredMeetingDataTemplate.objects.all():
        if template.schema_definition == default_schema:
            template.schema = default_schema.schema
            template.save()


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0094_add_initial_schema_data"),
    ]

    operations = [
        migrations.RunPython(forward_schema_data, reverse_schema_data),
    ]
