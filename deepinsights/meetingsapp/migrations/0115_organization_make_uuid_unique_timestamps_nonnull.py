# Generated by Django 4.2.18 on 2025-02-21 04:41

import uuid

import django
import model_utils
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [("meetingsapp", "0114_generate_org_uuids_timestamps")]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="organization",
            name="uuid",
            field=models.UUIDField(default=uuid.uuid4, editable=False, unique=True),
        ),
        migrations.AlterField(
            model_name="organization",
            name="created",
            field=model_utils.fields.AutoCreatedField(
                default=django.utils.timezone.now,
                editable=False,
                verbose_name="created",
            ),
        ),
        migrations.AlterField(
            model_name="organization",
            name="modified",
            field=model_utils.fields.AutoLastModifiedField(
                default=django.utils.timezone.now,
                editable=False,
                verbose_name="modified",
            ),
        ),
    ]
