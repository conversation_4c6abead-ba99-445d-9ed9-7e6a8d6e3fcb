# Generated by Django 4.2 on 2024-05-17 18:20

import deepinsights.core.preferences.preferences
from django.conf import settings
import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
import simple_history.models
import uuid


# Functions from the following migrations need manual copying.
# Move them and any dependencies into this file, then update the
# RunPython operations to refer to the local versions:
# deepinsights.meetingsapp.migrations.0032_auth_userdata_20240422_2053
# deepinsights.meetingsapp.migrations.0034_new_summary_20240430_2229

class Migration(migrations.Migration):

    replaces = [('meetingsapp', '0001_initial'), ('meetingsapp', '0002_initial'), ('meetingsapp', '0003_meeting_notes_note_content'), ('meetingsapp', '0004_remove_meeting_notes'), ('meetingsapp', '0005_alter_meeting_options_alter_note_options_and_more'), ('meetingsapp', '0006_rename_advisornotes_note_advisor_notes'), ('meetingsapp', '0007_note_key_takeaways'), ('meetingsapp', '0008_meeting_speaker_metadata_meeting_transcript'), ('meetingsapp', '0009_alter_meeting_timezone'), ('meetingsapp', '0010_alter_meeting_calendar_id_and_more'), ('meetingsapp', '0008_historicaltask_historicalrecording_historicalnote_and_more'), ('meetingsapp', '0011_merge_20231029_2010'), ('meetingsapp', '0012_historicalmeeting_speaker_metadata_and_more'), ('meetingsapp', '0013_historicalnote_diarized_trans_with_names_and_more'), ('meetingsapp', '0013_historicalnote_client_note_client'), ('meetingsapp', '0014_historicaltask_metadata_task_metadata'), ('meetingsapp', '0015_alter_historicaltask_metadata_alter_task_metadata'), ('meetingsapp', '0016_organization'), ('meetingsapp', '0017_historicalorganization'), ('meetingsapp', '0018_historicalnote_raw_transcript_note_raw_transcript'), ('meetingsapp', '0018_alter_historicalnote_file_path_alter_note_file_path_and_more'), ('meetingsapp', '0019_merge_20240113_2233'), ('meetingsapp', '0019_historicalorganization_preferences_and_more'), ('meetingsapp', '0020_merge_20240114_0650'), ('meetingsapp', '0021_alter_historicalorganization_crm_configuration_and_more'), ('meetingsapp', '0022_historicalnote_raw_asr_response_and_more'), ('meetingsapp', '0023_alter_task_meeting_alter_task_note_and_more'), ('meetingsapp', '0024_alter_task_note'), ('meetingsapp', '0025_historicalnote_summary_note_summary_and_more'), ('meetingsapp', '0026_alter_historicalnote_summary_alter_note_summary'), ('meetingsapp', '0027_alter_historicalorganization_crm_configuration_and_more'), ('meetingsapp', '0028_client'), ('meetingsapp', '0029_historicaltask_assignee_task_assignee'), ('meetingsapp', '0030_alter_historicaltask_task_title_and_more'), ('meetingsapp', '0031_note_authorized_users'), ('meetingsapp', '0032_auth_userdata_20240422_2053'), ('meetingsapp', '0033_alter_historicalnote_status_alter_note_status'), ('meetingsapp', '0034_new_summary_20240430_2229'), ('meetingsapp', '0035_meetingbot'), ('meetingsapp', '0036_historicalnote_salesforce_case_id_and_more')]

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Meeting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('is_active', models.BooleanField(default=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('zoom_meeting_id', models.CharField(blank=True, max_length=100, null=True)),
                ('zoom_meeting_uuid', models.CharField(blank=True, max_length=100, null=True)),
                ('account_id', models.CharField(blank=True, max_length=100, null=True)),
                ('topic', models.CharField(blank=True, max_length=255, null=True)),
                ('start_time', models.DateTimeField(blank=True, null=True)),
                ('timezone', models.CharField(blank=True, null=True)),
                ('host_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('is_processed', models.BooleanField(default=False)),
                ('calendar_object', models.JSONField(null=True)),
                ('calendar_id', models.CharField(blank=True, null=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='zoom_auth_user', to=settings.AUTH_USER_MODEL)),
                ('speaker_metadata', models.JSONField(blank=True, null=True)),
                ('transcript', models.CharField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Recording',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('is_active', models.BooleanField(default=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('zoom_meeting_id', models.CharField(blank=True, max_length=255, null=True)),
                ('file_type', models.CharField(blank=True, max_length=255, null=True)),
                ('recording_file', models.URLField(blank=True, null=True)),
                ('user_type', models.CharField(blank=True, choices=[('host', 'host'), ('participant', 'participant')], max_length=100, null=True)),
                ('transcribe', models.URLField(blank=True, null=True)),
                ('meeting', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='meetingsapp.meeting')),
            ],
        ),
        migrations.CreateModel(
            name='Note',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('is_active', models.BooleanField(default=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('file_path', models.CharField(blank=True, null=True)),
                ('file_type', models.CharField(blank=True, max_length=255, null=True)),
                ('note_type', models.CharField(blank=True, choices=[('meeting_recording', 'meeting_recording'), ('image', 'image'), ('voice_memo', 'voice_memo'), ('transcript', 'transcript')], null=True)),
                ('metadata', models.JSONField(blank=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('uploaded', 'uploaded'), ('processed', 'processed'), ('missing', 'missing')], null=True)),
                ('meeting', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='meetingsapp.meeting')),
                ('note_owner', models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL)),
                ('content', models.JSONField(blank=True, null=True)),
                ('advisor_notes', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(blank=True, null=True), blank=True, null=True, size=None)),
                ('key_takeaways', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(blank=True, null=True), blank=True, null=True, size=None)),
                ('diarized_trans_with_names', models.CharField(blank=True, null=True)),
                ('summary_by_topics', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(blank=True, null=True), blank=True, null=True, size=None)),
                ('client', models.JSONField(blank=True, null=True)),
                ('raw_transcript', models.CharField(blank=True, null=True)),
                ('raw_asr_response', models.JSONField(null=True)),
                ('summary', models.JSONField(default=dict, null=True)),
                ('authorized_users', models.ManyToManyField(blank=True, related_name='authorized_notes', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='HistoricalRecording',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('is_active', models.BooleanField(default=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('uuid', models.UUIDField(db_index=True, default=uuid.uuid4, editable=False)),
                ('zoom_meeting_id', models.CharField(blank=True, max_length=255, null=True)),
                ('file_type', models.CharField(blank=True, max_length=255, null=True)),
                ('recording_file', models.URLField(blank=True, null=True)),
                ('user_type', models.CharField(blank=True, choices=[('host', 'host'), ('participant', 'participant')], max_length=100, null=True)),
                ('transcribe', models.URLField(blank=True, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('meeting', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='meetingsapp.meeting')),
            ],
            options={
                'verbose_name': 'historical recording',
                'verbose_name_plural': 'historical recordings',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalMeeting',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('is_active', models.BooleanField(default=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('uuid', models.UUIDField(db_index=True, default=uuid.uuid4, editable=False)),
                ('zoom_meeting_id', models.CharField(blank=True, max_length=100, null=True)),
                ('zoom_meeting_uuid', models.CharField(blank=True, max_length=100, null=True)),
                ('account_id', models.CharField(blank=True, max_length=100, null=True)),
                ('topic', models.CharField(blank=True, max_length=255, null=True)),
                ('start_time', models.DateTimeField(blank=True, null=True)),
                ('timezone', models.CharField(blank=True, null=True)),
                ('host_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('is_processed', models.BooleanField(default=False)),
                ('calendar_object', models.JSONField(null=True)),
                ('calendar_id', models.CharField(blank=True, null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('user', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('speaker_metadata', models.JSONField(blank=True, null=True)),
                ('transcript', models.CharField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'historical meeting',
                'verbose_name_plural': 'historical meetings',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalNote',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('is_active', models.BooleanField(default=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('uuid', models.UUIDField(db_index=True, default=uuid.uuid4, editable=False)),
                ('file_path', models.CharField(blank=True, null=True)),
                ('file_type', models.CharField(blank=True, max_length=255, null=True)),
                ('note_type', models.CharField(blank=True, choices=[('meeting_recording', 'meeting_recording'), ('image', 'image'), ('voice_memo', 'voice_memo'), ('transcript', 'transcript')], null=True)),
                ('metadata', models.JSONField(blank=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('uploaded', 'uploaded'), ('processed', 'processed'), ('missing', 'missing')], null=True)),
                ('content', models.JSONField(blank=True, null=True)),
                ('advisor_notes', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(blank=True, null=True), blank=True, null=True, size=None)),
                ('key_takeaways', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(blank=True, null=True), blank=True, null=True, size=None)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('meeting', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='meetingsapp.meeting')),
                ('note_owner', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('diarized_trans_with_names', models.CharField(blank=True, null=True)),
                ('summary_by_topics', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(blank=True, null=True), blank=True, null=True, size=None)),
                ('client', models.JSONField(blank=True, null=True)),
                ('raw_transcript', models.CharField(blank=True, null=True)),
                ('raw_asr_response', models.JSONField(null=True)),
                ('summary', models.JSONField(default=dict, null=True)),
            ],
            options={
                'verbose_name': 'historical note',
                'verbose_name_plural': 'historical notes',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='HistoricalOrganization',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='Organization Name')),
                ('description', models.TextField(default='A new Zeplyn client.', verbose_name='Description')),
                ('metadata', models.JSONField(default=dict)),
                ('crm_configuration', models.JSONField(default=deepinsights.core.preferences.preferences.get_default_crm_configuration)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('preferences', models.JSONField(blank=True, default=deepinsights.core.preferences.preferences.get_default_preferences, null=True)),
            ],
            options={
                'verbose_name': 'historical Organization',
                'verbose_name_plural': 'historical Organizations',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='Organization',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='Organization Name')),
                ('description', models.TextField(default='A new Zeplyn client.', verbose_name='Description')),
                ('metadata', models.JSONField(default=dict)),
                ('crm_configuration', models.JSONField(default=deepinsights.core.preferences.preferences.get_default_crm_configuration)),
                ('preferences', models.JSONField(blank=True, default=deepinsights.core.preferences.preferences.get_default_preferences, null=True)),
            ],
            options={
                'verbose_name': 'Organization',
                'verbose_name_plural': 'Organizations',
            },
        ),
        migrations.CreateModel(
            name='Client',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('is_active', models.BooleanField(default=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('name', models.CharField()),
                ('first_name', models.CharField(null=True)),
                ('last_name', models.CharField(null=True)),
                ('job_title', models.CharField(null=True)),
                ('email', models.EmailField(max_length=254, null=True)),
                ('crm_id', models.CharField(blank=True, max_length=100, null=True)),
                ('client_type', models.CharField(default='individual', max_length=100)),
                ('crm_system', models.CharField(default='redtail', max_length=100)),
                ('advisor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='meetingsapp.organization')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='HistoricalTask',
            fields=[
                ('id', models.BigIntegerField(auto_created=True, blank=True, db_index=True, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('is_active', models.BooleanField(default=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('uuid', models.UUIDField(db_index=True, default=uuid.uuid4, editable=False)),
                ('task_title', models.CharField()),
                ('task_desc', models.TextField(blank=True, null=True)),
                ('completed', models.BooleanField(blank=True, default=False)),
                ('due_date', models.DateTimeField(null=True)),
                ('history_id', models.AutoField(primary_key=True, serialize=False)),
                ('history_date', models.DateTimeField(db_index=True)),
                ('history_change_reason', models.CharField(max_length=100, null=True)),
                ('history_type', models.CharField(choices=[('+', 'Created'), ('~', 'Changed'), ('-', 'Deleted')], max_length=1)),
                ('history_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('meeting', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='meetingsapp.meeting')),
                ('note', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to='meetingsapp.note')),
                ('task_owner', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
                ('metadata', models.JSONField(blank=True, default=dict, null=True)),
                ('assignee', models.ForeignKey(blank=True, db_constraint=False, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='+', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'historical task',
                'verbose_name_plural': 'historical tasks',
                'ordering': ('-history_date', '-history_id'),
                'get_latest_by': ('history_date', 'history_id'),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
        migrations.CreateModel(
            name='Task',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('is_active', models.BooleanField(default=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('task_title', models.CharField()),
                ('task_desc', models.TextField(blank=True, null=True)),
                ('completed', models.BooleanField(blank=True, default=False)),
                ('due_date', models.DateTimeField(null=True)),
                ('meeting', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='meetingsapp.meeting')),
                ('task_owner', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('note', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='meetingsapp.note')),
                ('metadata', models.JSONField(blank=True, default=dict, null=True)),
                ('assignee', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_tasks', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AlterField(
            model_name='historicalnote',
            name='status',
            field=models.CharField(blank=True, choices=[('uploaded', 'uploaded'), ('processed', 'processed'), ('missing', 'missing'), ('scheduled', 'scheduled'), ('finalized', 'finalized')], null=True),
        ),
        migrations.AlterField(
            model_name='note',
            name='status',
            field=models.CharField(blank=True, choices=[('uploaded', 'uploaded'), ('processed', 'processed'), ('missing', 'missing'), ('scheduled', 'scheduled'), ('finalized', 'finalized')], null=True),
        ),
        migrations.CreateModel(
            name='MeetingBot',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', model_utils.fields.AutoCreatedField(default=django.utils.timezone.now, editable=False, verbose_name='created')),
                ('modified', model_utils.fields.AutoLastModifiedField(default=django.utils.timezone.now, editable=False, verbose_name='modified')),
                ('is_active', models.BooleanField(default=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('recall_bot_id', models.CharField(blank=True, null=True)),
                ('start_time', models.DateTimeField(blank=True, null=True)),
                ('end_time', models.DateTimeField(blank=True, null=True)),
                ('meeting_link', models.CharField(blank=True, null=True)),
                ('recording_start_time', models.DateTimeField(blank=True, null=True)),
                ('recording_end_time', models.DateTimeField(blank=True, null=True)),
                ('processing_start_time', models.DateTimeField(blank=True, null=True)),
                ('processing_end_time', models.DateTimeField(blank=True, null=True)),
                ('metadata', models.JSONField(blank=True, null=True)),
                ('bot_owner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('note', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='meetingsapp.note')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='historicalnote',
            name='salesforce_case_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='note',
            name='salesforce_case_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='historicalnote',
            name='raw_asr_response',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='historicalnote',
            name='summary',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AlterField(
            model_name='note',
            name='raw_asr_response',
            field=models.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='note',
            name='summary',
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
    ]
