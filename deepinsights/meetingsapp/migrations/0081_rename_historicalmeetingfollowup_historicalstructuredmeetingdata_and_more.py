# Generated by Django 4.2.16 on 2024-12-11 19:21

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("meetingsapp", "0080_remove_historicalagenda_history_user_and_more"),
    ]

    operations = [
        migrations.RenameModel(
            old_name="HistoricalMeetingFollowUp",
            new_name="HistoricalStructuredMeetingData",
        ),
        migrations.RenameModel(
            old_name="MeetingFollowUp",
            new_name="StructuredMeetingData",
        ),
        migrations.RenameModel(
            old_name="MeetingFollowUpTemplate",
            new_name="StructuredMeetingDataTemplate",
        ),
        migrations.AlterModelOptions(
            name="historicalstructuredmeetingdata",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical structured meeting data",
                "verbose_name_plural": "historical structured meeting datas",
            },
        ),
    ]
