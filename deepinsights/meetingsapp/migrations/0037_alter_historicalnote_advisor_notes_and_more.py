# Generated by Django 4.2 on 2024-05-17 19:14

from django.db import migrations, models
import django_jsonform.models.fields


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0001_squashed_0036_historicalnote_salesforce_case_id_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="historicalnote",
            name="advisor_notes",
            field=django_jsonform.models.fields.ArrayField(
                base_field=models.CharField(blank=True, null=True), blank=True, null=True, size=None
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="historicalnote",
            name="key_takeaways",
            field=django_jsonform.models.fields.ArrayField(
                base_field=models.CharField(blank=True, null=True), blank=True, null=True, size=None
            ),
        ),
        migrations.AlterField(
            model_name="historicalnote",
            name="summary_by_topics",
            field=django_jsonform.models.fields.ArrayField(
                base_field=models.Char<PERSON>ield(blank=True, null=True), blank=True, null=True, size=None
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="note",
            name="advisor_notes",
            field=django_jsonform.models.fields.ArrayField(
                base_field=models.Char<PERSON>ield(blank=True, null=True), blank=True, null=True, size=None
            ),
        ),
        migrations.AlterField(
            model_name="note",
            name="key_takeaways",
            field=django_jsonform.models.fields.ArrayField(
                base_field=models.CharField(blank=True, null=True), blank=True, null=True, size=None
            ),
        ),
        migrations.AlterField(
            model_name="note",
            name="summary_by_topics",
            field=django_jsonform.models.fields.ArrayField(
                base_field=models.CharField(blank=True, null=True), blank=True, null=True, size=None
            ),
        ),
    ]
