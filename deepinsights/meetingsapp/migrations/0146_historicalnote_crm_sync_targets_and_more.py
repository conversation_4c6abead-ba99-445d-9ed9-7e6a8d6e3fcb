# Generated by Django 4.2.21 on 2025-06-03 00:09

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("meetingsapp", "0145_midmeeting_prompt_update"),
    ]

    operations = [
        migrations.AddField(
            model_name="historicalnote",
            name="crm_sync_targets",
            field=models.JSONField(
                blank=True,
                help_text="Where information about this note should be synced in the CRM. Not that this may contain a 'partial'/'unresolved' set of targets, which may require further disambiguation from the user in order to be used by the CRM as a final sync target. In other words, this may not represent exactly where the note will be synced, until the note is actually synced and finalized, at which point the value will represent where it was synced.",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="note",
            name="crm_sync_targets",
            field=models.J<PERSON><PERSON>ield(
                blank=True,
                help_text="Where information about this note should be synced in the CRM. Not that this may contain a 'partial'/'unresolved' set of targets, which may require further disambiguation from the user in order to be used by the CRM as a final sync target. In other words, this may not represent exactly where the note will be synced, until the note is actually synced and finalized, at which point the value will represent where it was synced.",
                null=True,
            ),
        ),
    ]
