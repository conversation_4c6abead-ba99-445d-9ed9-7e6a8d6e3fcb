import asyncio
import csv
import datetime
import io
import json
import logging
import os
import random
from collections import Counter
from concurrent.futures import Future, ThreadPoolExecutor
from datetime import timed<PERSON>ta
from functools import wraps
from typing import Any, Callable, Generator, ParamSpec, TypeVar
from uuid import UUID

import httpx
import redis
from asgiref.sync import async_to_sync
from celery import Task as CeleryTask
from celery import shared_task
from deepgram.transcription import TranscriptionSource
from django.conf import settings
from django.core.cache import cache
from django.db import IntegrityError, connection
from django.db.models import Q
from django.utils import timezone
from pydantic import ValidationError

from deepinsights.core.email_service import ZeplynEmailService
from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent, EventParticipantsList
from deepinsights.core.integrations.calendar.events_fetcher import fetch_calendar_events
from deepinsights.core.integrations.calendar.participant_utils import events_with_zeplyn_attendees
from deepinsights.core.integrations.crm.crm_models import CRMAccount, CRMNote
from deepinsights.core.integrations.meetingbot.recall_ai import RecallBotController
from deepinsights.core.ml.agenda import generate_filled_agenda
from deepinsights.core.ml.asr import fetch_and_save_deepgram_transcript
from deepinsights.core.ml.client_recap import (
    build_client_recap_for_client,
    convert_client_recap_to_markdown,
    generate_client_recap,
)
from deepinsights.core.ml.genai import call_model
from deepinsights.core.ml.models import Summary, SummarySection, TasksAndTakeaways
from deepinsights.core.ml.process_transcript import process_transcript
from deepinsights.core.ml.scripts.calendar_events import export_calendar_events_to_csv
from deepinsights.core.ml.scripts.user_metrics import get_all_metrics
from deepinsights.core.ml.voice_memo import (
    get_agenda_completion_info,
    get_agenda_follow_up_data,
    get_follow_up_structured_data,
    get_summary,
    get_tasks_and_takeaways,
)
from deepinsights.core.ml.voice_memo_utils import parse_response_into_json
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.audio_buffer import AudioBuffer
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.client_interaction import ClientInteraction
from deepinsights.meetingsapp.models.client_recap import ClientRecap, ClientRecapStatus
from deepinsights.meetingsapp.models.meeting_bot import MeetingBot
from deepinsights.meetingsapp.models.mid_meeting_nudge import MidMeetingNudge, MidMeetingNudgeStatus
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.prompt import Prompt
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent
from deepinsights.meetingsapp.models.structured_meeting_data import (
    StructuredMeetingData,
    StructuredMeetingDataTemplate,
    StructuredMeetingDataTemplateRule,
)
from deepinsights.meetingsapp.models.structured_meeting_data_schema import StructuredMeetingDataSchema
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User

logger = logging.getLogger(__name__)

email_service = ZeplynEmailService()

_P = ParamSpec("_P")
_R = TypeVar("_R", covariant=True)


# Prevents a task from running concurrently with other tasks that are operating on matching objects.
# The lock is keyed on the value of the argument at the specified (0-based) index.
#
# Note that retries of a task are essentially separate tasks, so the lock does not stay locked
# until the retries are finished.
def locked_task(arg_index: int) -> Callable[[Callable[_P, _R | None]], Callable[_P, _R | None]]:
    def decorator(f: Callable[_P, _R | None]) -> Callable[_P, _R | None]:
        @wraps(f)
        def dec(*args: Any, **kwargs: Any) -> _R | None:
            if not settings.ENABLE_CELERY_TASK_LOCKING:
                logger.info("Celery task locking disabled. Skipping lock for task %s", f.__name__)
                return f(*args, **kwargs)

            if arg_index is None or not isinstance(arg_index, int) or arg_index < 0 or arg_index >= len(args):
                raise ValueError(f"Invalid argument index {arg_index} for task {f.__name__}")

            # Derive the key from the `arg_index`-th argument.
            key = f"{args[arg_index]}"

            # We use Redis as our cache in all environments; however, to be defensive, make sure that
            # the cache supports locking.
            if not hasattr(cache, "lock"):
                logger.warning("Cache does not support locking. Skipping lock for Celery task %s", f.__name__)
                return f(*args, **kwargs)

            res = None
            try:
                with cache.lock(key, timeout=settings.CELERY_TASK_LOCK_TIMEOUT):
                    res = f(*args, **kwargs)
            except redis.exceptions.LockNotOwnedError as e:
                logger.error(
                    "Task %s tried to release an un-owned lock. This implies that the task took longer "
                    "than the timeout. Other tasks may have uexpectedly run in parallel.",
                    f.__name__,
                    exc_info=e,
                )
                return res
            except Exception:
                raise
            else:
                return res

        return dec

    return decorator


# Modified from
# https://stackoverflow.com/questions/57211476/django-orm-leaks-connections-when-using-threadpoolexecutor.
class DjangoConnectionThreadPoolExecutor(ThreadPoolExecutor):
    """
    When a function is passed into the ThreadPoolExecutor via the new submit method, this will wrap
    the function, and make sure that close_django_db_connection() is called inside the thread when
    it's finished so Django doesn't leak DB connections.
    """

    def generate_thread_closing_wrapper(self, fn: Callable[..., Any]) -> Callable[..., Any]:
        @wraps(fn)
        def new_func(*args: Any, **kwargs: Any) -> Any:
            try:
                return fn(*args, **kwargs)
            finally:
                connection.close()

        return new_func

    def submit_with_connection_close(self, fn: Callable[..., Any], *args: Any, **kwargs: Any) -> Future[Any]:
        return self.submit(self.generate_thread_closing_wrapper(fn), *args, **kwargs)


def compute_task_due_date(user: User | None) -> datetime.datetime:
    # TODO(divam): Accept more data and set the due date based on task type.
    due_date = timezone.now() + datetime.timedelta(
        seconds=user.get_preferences().due_date_offset_seconds if user else 0
    )
    return due_date


def substitute_names_in_transcript(transcript: str, mapping: dict[str, str]) -> str:
    for speaker, name in mapping.items():
        transcript = transcript.replace(speaker, name)
    return transcript


def update_attendees(note: Note, processed_transcript: dict[str, Any]) -> bool:
    """
    Update the speaker percentages, durations, and aliases for all attendees of a meeting note.

    This function processes the speaker percentages, meeting duration, and speaker mapping from the
    'processed_transcript' dictionary, calculates the speaking time for each attendee, and updates
    the corresponding Attendee objects in the database. If data is not available for an attendee,
    the corresponding fields will be set to None.

    Parameters:
    - note (Note): The Note object associated with the meeting.
    - processed_transcript (Dict[str, Any]): A dictionary containing meeting information. It may include:
        - 'speaker_percentage' (Dict[str, float]): A mapping of speaker aliases to their speaking percentage.
        - 'meeting_duration' (int): The total duration of the meeting in seconds.
        - 'speaker_mapping' (Dict[str, str]): A mapping of speaker aliases to attendee names.

    Returns:
    - bool: True if the update was successful, False if there was an error.
    """
    try:
        # Fetch all attendees for the note
        attendees = note.attendees.all()

        # Extract data from processed_transcript
        speaker_percentages = processed_transcript.get("speaker_percentage", {})
        meeting_duration = processed_transcript.get("meeting_duration", 0)
        speaker_mapping = processed_transcript.get("speaker_mapping")

        # Create a reverse mapping of attendee names to speaker aliases if speaker_mapping exists
        reverse_mapping = {name: alias for alias, name in speaker_mapping.items()} if speaker_mapping else {}

        for attendee in attendees:
            speaker_alias = reverse_mapping.get(attendee.attendee_name) if reverse_mapping else None

            if speaker_alias:
                # Update speaker alias
                attendee.speaker_alias = speaker_alias

            # Update speaker percentage and time
            percentage = speaker_percentages.get(speaker_alias)
            if percentage is not None and meeting_duration:
                attendee.speaker_percentage = percentage
                speaker_duration_seconds = round(meeting_duration * (percentage / 100))
                attendee.speaker_time = timedelta(seconds=speaker_duration_seconds)

        # Bulk update all attendees
        Attendee.objects.bulk_update(attendees, ["speaker_alias", "speaker_percentage", "speaker_time"])

        logging.info("Successfully updated attendees for note %s", note.uuid)
        return True

    except Exception as e:
        logging.error("Failed to update attendees for note %s", note.uuid, exc_info=e)
    return False


def _process_agenda_followup(note: Note, transcript: str, set_processing_status_first: bool = False) -> None:
    """
    Process agenda follow-up for a note if meeting type has associated agenda templates or
    if the note is associated with a client interaction that has an agenda.

    Args:
        note: The note object
        transcript: The transcript text
        set_processing_status_first: Whether to create with processing status first and update after
    """

    # Check if note has a client interaction with agenda
    client_interaction = ClientInteraction.objects.filter(note=note).first()
    has_client_interaction_agenda = client_interaction and client_interaction.agenda is not None

    # Check if meeting type has agenda templates
    has_meeting_type_agenda = note.meeting_type and note.meeting_type.agenda_templates.exists()

    logging.info(
        "Processing agenda follow-up for note %s: has_client_interaction_agenda=%s, has_meeting_type_agenda=%s",
        note.uuid,
        has_client_interaction_agenda,
        has_meeting_type_agenda,
    )

    if has_client_interaction_agenda or has_meeting_type_agenda:
        try:
            # Find an appropriate schema
            schema = StructuredMeetingDataSchema.objects.get(name="structured_data_schema")
            logging.info("Found schema %s for agenda follow-up for note %s", schema.name, note.uuid)

            structured_data = None

            if set_processing_status_first:
                # Create a placeholder structured meeting data entry
                structured_data = StructuredMeetingData.objects.create(
                    title="Agenda Follow-up",
                    internal_name="agenda_followup",
                    kind="agenda_followup",
                    schema=schema.schema if schema else None,
                    data={},
                    note=note,
                    status=StructuredMeetingData.Status.PROCESSING,
                )
                logging.info(
                    "Created placeholder structured data for agenda follow-up with ID %s for note %s",
                    structured_data.uuid,
                    note.uuid,
                )

            # Generate the agenda follow-up data
            logging.info("Generating agenda follow-up data for note %s", note.uuid)
            agenda_followup_data = get_agenda_follow_up_data(note, transcript)

            if agenda_followup_data:
                logging.info("Successfully generated agenda follow-up data for note %s", note.uuid)
            else:
                logging.warning("Failed to generate agenda follow-up data for note %s, got empty result", note.uuid)

            if set_processing_status_first and structured_data:
                # Update the existing structured data
                structured_data.data = agenda_followup_data
                structured_data.status = (
                    StructuredMeetingData.Status.COMPLETED
                    if agenda_followup_data
                    else StructuredMeetingData.Status.FAILED
                )
                structured_data.save()
                logging.info(
                    "Updated structured data with status %s for agenda follow-up ID %s for note %s",
                    structured_data.status,
                    structured_data.id,
                    note.uuid,
                )
            elif agenda_followup_data:
                # Create the structured meeting data directly
                created_data = StructuredMeetingData.objects.create(
                    title="Agenda Follow-up",
                    internal_name="agenda_followup",
                    kind="agenda_followup",
                    schema=schema.schema if schema else None,
                    data=agenda_followup_data,
                    note=note,
                    status=StructuredMeetingData.Status.COMPLETED,
                )
                logging.info(
                    "Created structured data for agenda follow-up with ID %s for note %s", created_data.id, note.uuid
                )
        except Exception as e:
            logging.error(
                "Error processing agenda follow-up for note %s. Continuing with other processing.",
                note.uuid,
                exc_info=e,
            )
            if set_processing_status_first and "structured_data" in locals() and structured_data:
                structured_data.status = StructuredMeetingData.Status.FAILED
                structured_data.save()
    else:
        logging.info(
            "Skipping agenda follow-up for note %s: No client interaction agenda or meeting type templates found",
            note.uuid,
        )


def _send_email_for_note_processing_completed(note: Note) -> None:
    users_to_email: list[User] = [
        u
        for u in note.authorized_users.all()
        if u.get_preferences().notification_preferences.meeting_processed_notification_enabled
    ]
    if not users_to_email:
        logging.info("No users to whom to send 'note ready to review' emails for note %s", note.uuid)
        return

    meeting_name = (note.metadata or {}).get("meeting_name", "your recent meeting")

    for user in users_to_email:
        logging.info("Sending 'note ready for review' email to %s for %s", str(user.uuid), note.uuid)
        send_email_with_attachments(
            subject=f"Your Zeplyn notes for {meeting_name} are ready for review",
            body=f"<a href='{note.public_url()}'>Click here</a> to review your notes.",
            attachments=[],
            recipient=user.email,
            is_html=True,
        )


def _process_note_common(
    note: Note, user: User, raw_asr_response: dict[str, Any] | list[dict[str, Any]], asr_process: str
) -> None:
    """
    Process the common parts of transcript processing for both bot recordings and note recordings.

    Args:
        note (Note): The note object associated with the recording.
        user (User): The user object associated with the note.
        raw_asr_response: a dictionary of speakers to utterances, or a list of utterances.
        asr_process: The service used to get the transcript.
    """
    logging.info("Starting note processing for note in process_note_common for note %s, user %s", note.uuid, user.uuid)
    processed_transcript = process_transcript(note, user, raw_asr_response, asr_process)
    logging.info("Processed transcript for note %s", note.uuid)

    temp_transcript: str = processed_transcript.get("transcript", "")
    if processed_transcript.get("transcript") and processed_transcript.get("speaker_mapping"):
        temp_transcript = substitute_names_in_transcript(
            processed_transcript["transcript"], processed_transcript["speaker_mapping"]
        )
        logging.debug("Processed transcript with substituted names for note %s: %s", note.uuid, processed_transcript)

    # Attach transcript data to the note, so that it is not lost if there is no other content or an
    # error with the LLM processing.
    note.diarized_trans_with_names = processed_transcript.get("transcript")
    note.raw_transcript = processed_transcript.get("raw_transcript")
    note.metadata = {**(note.metadata or {}), "meeting_duration": processed_transcript.get("meeting_duration")}
    if note.raw_transcript and (
        "speaker_time" in processed_transcript
        or "speaker_percentage" in processed_transcript
        or "speaker_mapping" in processed_transcript
    ):
        attendees_updated = update_attendees(note=note, processed_transcript=processed_transcript)
        if attendees_updated:
            logging.info("Attendees updated successfully for note %s", note.uuid)
        else:
            logging.info("Attendees not updated for note %s", note.uuid)

    # Generate meeting intelligence and attach it to the note (if there is any).
    logging.info("Generating tasks and takeaways for note %s", note.uuid)
    tasks_and_takeaways, has_tasks_and_takeaways_content = get_tasks_and_takeaways(note, temp_transcript or "")

    logging.info("Generating summary for note %s", note.uuid)
    summary, has_summary_content = get_summary(note, temp_transcript)

    if not has_summary_content and not has_tasks_and_takeaways_content:
        logging.error(
            "No LLM content generated from transcript for note %s. This could indicate an empty note or an LLM error.",
            note.uuid,
            exc_info=True,
        )
        note.metadata = {**(note.metadata or {}), "tags": ["Empty note"]}
        note.summary = Summary(
            sections=[SummarySection(topic="Empty meeting", bullets=["Nothing was discussed in this meeting."])]
        ).model_dump()
        note.status = Note.PROCESSING_STATUS.processed
        return

    logging.info("Updating note %s with LLM-generated data", note.uuid)
    process_note_enhancements_async.delay_on_commit(note.uuid, user.uuid, temp_transcript)
    _update_note_and_tasks_with_tasks_and_takeaways(note, user, tasks_and_takeaways)
    note.summary = summary.model_dump()
    note.status = Note.PROCESSING_STATUS.processed
    _send_email_for_note_processing_completed(note)
    logging.info("Completed note processing for note %s", note.uuid)


# Given a Twilio meeting bot, returns a resolved media URL for the bot.
def _resolved_twilio_bot_media_url(bot: MeetingBot, note: Note, user: User) -> str | None:
    if temporary_download_url := note.temporary_s3_download_url():
        return temporary_download_url
    media_download_info = bot.get_media_download_info()
    if not media_download_info:
        logging.error("No download URL found for bot: %s", bot.uuid)
        return None
    download_url, username, password = media_download_info
    if not username or not password:
        logging.error("No credentials found for bot media: %s", bot.uuid)
        return None
    response = httpx.get(download_url, auth=(username, password))
    note.save_with_audio_data(user, "phone_call_recording", note.created, "mp3", io.BytesIO(response.content))
    return note.temporary_s3_download_url()


def _asr_response_has_data(raw_asr_response: dict[str, Any] | list[dict[str, Any]]) -> bool:
    if isinstance(raw_asr_response, list):
        return len(raw_asr_response) > 0
    return True if raw_asr_response.get("results", {}).get("utterances") else False


@shared_task(
    bind=True,
    time_limit=720,
    soft_time_limit=600,
    autoretry_for=(Exception,),
    retry_backoff=5,
    max_retries=3,
    # Jitter is commonly a good practice. However, the celery jitter implementation has a tendency
    # to create very short timeouts (it jitters between 0 and the exponential timeout), which isn't
    # ideal for failures in this task (which might be due to issues with the APIs we are calling
    # into). Since these tasks are run somewhat randomly as a result of user actions, the jitter isn't
    # quite as useful for us, and we'd rather have a more predictable retry pattern.
    retry_jitter=False,
)
@locked_task(arg_index=1)
def process_bot_recording(self: CeleryTask[..., None], bot_id: UUID, force_update: bool) -> None:
    """
    Process a Recall bot recording, generate transcripts, and update the associated note.

    Args:
        bot_id (UUID): The UUID of the MeetingBot to process.
        force_update (bool, optional): Force update even if the note is already processed. Defaults to False.
    """
    bot = MeetingBot.objects.get(uuid=bot_id)
    if not (note := bot.note):
        raise Exception("MeetingBot {} does not have an associated note".format(bot.uuid))
    if not (user := note.note_owner):
        raise Exception("Note {} does not have an associated user".format(note.uuid))
    if note.status == Note.PROCESSING_STATUS.processed and not force_update:
        logging.info("Note %s already processed, skipping processing for %s", note.uuid, bot.bot_id_for_logging)
        return

    should_retry_if_failed = self.max_retries and self.request.retries < self.max_retries
    delete_media = True
    process = "recall"
    if Flags.EnableProcessRecallBotTranscriptsWithZeplynDeepgramIntegration.is_active_for_user(user):
        logging.info("Processing bot recording via Deepgram for MeetingBot %s for note %s", bot.uuid, note.uuid)
        process = "deepgram"
        if not (raw_asr_response := note.raw_asr_response):
            if not (media_url := download_info[0] if (download_info := bot.get_media_download_info()) else None):
                if should_retry_if_failed:
                    logging.error("No media found for bot: %s", bot.bot_id_for_logging)
                    raise Exception("No media found for bot. Failed to process {}".format(bot.bot_id_for_logging))
            if not (
                raw_asr_response := fetch_and_save_deepgram_transcript({"url": media_url}, note, user)
                if media_url
                else None
            ):
                if should_retry_if_failed:
                    raise Exception("No asr response returned. Failed to process {}".format(bot.bot_id_for_logging))
                # This is a workaround for the bug described in
                # https://zeplyn.atlassian.net/browse/ENG-646; ideally, we would construct a correct
                # representation of an empty Deepgram API response, but this is simpler.
                process = "recall"
                raw_asr_response = []
                note.raw_asr_response = raw_asr_response
                note.save()

                logging.error(
                    "[Local bot processing] Empty ASR response returned after %d retries. "
                    "Processing as empty note: %s, for note %s",
                    self.max_retries,
                    bot.bot_id_for_logging,
                    note.uuid,
                )
    elif not (raw_asr_response := bot.get_transcript()):
        if should_retry_if_failed:
            raise Exception(
                "No ASR response returned. Failed to process {} for note {}".format(bot.bot_id_for_logging, note.uuid)
            )
        logging.error(
            "[Recall bot processing] Empty ASR response returned after %d retries. Bot has speaker "
            "timeline: %s. Trying with local bot processing flow: %s",
            self.max_retries,
            bot.has_speaker_timeline(),
            bot.bot_id_for_logging,
        )

        # If we don't get a bot transcript, save the media so that Recall can debug issues. Note that this will
        # also cause a lot of "expectedly empty" media not to be deleted, but this is a tradeoff we're making
        # to try to debug the Zoom recording issues we've been having.
        delete_media = False

        # If there was no Recall transcript, try again with the same pipeline used for local bot processing.
        # This is a workaround for the fact that there are some Recall meetings that seem to have no transcript
        # even though there was media and there is no evidence of issues.
        if (media_url := download_info[0] if (download_info := bot.get_media_download_info()) else None) and (
            deepgram_response := fetch_and_save_deepgram_transcript({"url": media_url}, note, user)
        ):
            logging.info(
                "[Recall bot processing] Deepgram response returned after Recall transcript failed for %s for note %s",
                bot.bot_id_for_logging,
                note.uuid,
            )
            process = "deepgram"
            raw_asr_response = deepgram_response
        else:
            logging.info(
                "[Recall bot processing] Deepgram response also failed for %s. "
                "Assuming that the recording is empty for note %s",
                bot.bot_id_for_logging,
                note.uuid,
            )

    # This call has a side effect of updating the note object. However we don't save the note in this call because
    # calls to note.save() are not testable due to issues with serializing JSON.
    _process_note_common(note, user, raw_asr_response, process)
    note.data_source = Note.DataSource.RECALL
    note.save()
    # Only delete media if the processing was successful, and we got an ASR response with non-empty content.
    if delete_media and _asr_response_has_data(raw_asr_response):
        bot.delete_media()
    else:
        logging.error("Not deleting media for %s. This media must be deleted manually.", bot.bot_id_for_logging)
    logging.info("Done processing note: %s", note.uuid)


def _audio_buffer_data_for_note(note: Note) -> TranscriptionSource:
    buffers = AudioBuffer.objects.filter(note=note).order_by("sequence", "created")
    last = buffers.last()
    if not last:
        logging.error("No audio buffers found for note: %s", note.uuid)
        raise Exception(f"No audio buffers found for note: {note.uuid}")

    # Get all buffers with the same nonce as the last (newest) buffer. We assume that the newest buffer is the
    # one that represents what the user wants to transcribe.
    buffers = buffers.filter(nonce=last.nonce)

    # Handle the case when (for some reason) there are multiple MIME types for the buffers.
    mime_types = Counter(buffers.values_list("mime_type", flat=True))
    mime_types_set = set(mime_types)
    mime_type = None

    # If there's only one MIME type, use that.
    if len(mime_types_set) == 1:
        mime_type = mime_types_set.pop()

    # If there are multiple MIME types, filter the buffers so only the buffers with the most MIME
    # types are used.
    elif len(mime_types) > 1:
        logging.error("Multiple MIME types found for note: %s. Using the one with the most buffers", note.uuid)
        mime_type = mime_types.most_common(1)[0][0]
        buffers = buffers.filter(mime_type=mime_type)

    if not mime_type:
        logging.error("No MIME type found for audio buffers for note: %s", note.uuid)
        raise Exception(f"No MIME type found for audio buffers for note: {note.uuid}")

    audio_data = io.BytesIO()
    for buffer in buffers:
        audio_data.write(buffer.data)
    audio_data.seek(0)
    return {"buffer": audio_data.read(), "mimetype": mime_type}


@shared_task(
    time_limit=720,
    soft_time_limit=600,
    autoretry_for=(Exception,),
    retry_backoff=5,
    max_retries=3,
    # Jitter is commonly a good practice. However, the celery jitter implementation has a tendency
    # to create very short timeouts (it jitters between 0 and the exponential timeout), which isn't
    # ideal for failures in this task (which might be due to issues with the APIs we are calling
    # into). Since these tasks are run somewhat randomly as a result of user actions, the jitter isn't
    # quite as useful for us, and we'd rather have a more predictable retry pattern.
    retry_jitter=False,
)
@locked_task(arg_index=1)
def process_note_recording(
    user_id: UUID, note_id: UUID, force_process: bool = False, use_audio_buffers: bool = False
) -> None:
    """
    Process a note recording, generate transcripts, and update the note.

    Args:
        user_id (UUID): The UUID of the user associated with the note.
        note_id (UUID): The UUID of the note to process.
        force_process (bool, optional): Force (re)processing even if the note is already processed.
          Defaults to False.
    """
    logging.info("Processing note mic audio recording: %s, user: %s", note_id, user_id)
    user = User.objects.get(uuid=user_id)
    note = Note.objects.get(uuid=note_id)
    if note.status == Note.PROCESSING_STATUS.processed and not force_process:
        logging.info("Note %s already processed for user %s, skipping processing", note.uuid, user.uuid)
        return
    if not (raw_asr_response := note.raw_asr_response):
        if use_audio_buffers:
            if not (
                raw_asr_response := fetch_and_save_deepgram_transcript(_audio_buffer_data_for_note(note), note, user)
            ):
                logging.error("No ASR response returned. Failed to process note_id %s", note_id)
                raise Exception(f"No ASR response returned. Failed to process note_id {note_id}")
        else:
            if not (media_url := note.temporary_s3_download_url()):
                logging.error("No media found for note: %s", note.uuid)
                raise Exception(f"No media found for note. Failed to process note_id {note_id}")
            if not (raw_asr_response := fetch_and_save_deepgram_transcript({"url": media_url}, note, user)):
                logging.error("No ASR response returned. Failed to process note_id %s", note_id)
                raise Exception(f"No ASR response returned. Failed to process note_id {note_id}")

    # This call has a side effect of updating the note object. However we don't save the note in this call because
    # calls to note.save() are not testable due to issues with serializing JSON.
    _process_note_common(note, user, raw_asr_response, "deepgram")
    note.data_source = Note.DataSource.AUDIO_BUFFERS if use_audio_buffers else Note.DataSource.AUDIO_FILE
    if user.get_preferences().delete_buffer:
        note.delete_media()
    note.save()

    logging.info("Done processing note: %s, user %s", note.uuid, user.uuid)


@shared_task(time_limit=720, soft_time_limit=600)
@locked_task(arg_index=0)
def reprocess_note(note_id: UUID, tasks: bool = False, summary: bool = False, follow_ups: bool = False) -> None:
    """
    Reprocess a note with specified options.

    Args:
        note_id: UUID of the note to reprocess
        tasks: Whether to reprocess tasks and takeaways
        summary: Whether to reprocess the summary
        follow_ups: Whether to reprocess templates/follow ups
    """
    note = Note.objects.get(uuid=note_id)
    note.status = Note.PROCESSING_STATUS.uploaded
    temp_transcript = note.diarized_trans_with_names
    if not temp_transcript:
        logging.error("No transcript found for note: %s", note.uuid)
        raise Exception("No transcript found for note:%s", note.uuid)
    # The code below does not check whether the contents are LLM generated; the assumption is that
    # this task is being run by an administrator, who specifically wants to reprocess this note and
    # is aware of the contents of the transcript.
    speaker_mapping = {
        attendee.speaker_alias: attendee.attendee_name
        for attendee in note.attendees.all()
        if attendee.speaker_alias and attendee.attendee_name
    }
    if temp_transcript and speaker_mapping:
        temp_transcript = substitute_names_in_transcript(temp_transcript, speaker_mapping)
    if tasks:
        new_tasks_and_takeaways, _ = get_tasks_and_takeaways(note, temp_transcript)
        # Delete existing tasks attached to this note before creating new ones
        Task.objects.filter(note=note).delete()
        _update_note_and_tasks_with_tasks_and_takeaways(note, note.note_owner, new_tasks_and_takeaways)
    if summary:
        new_summary, _ = get_summary(note, temp_transcript)
        note.summary = new_summary.model_dump()
    if follow_ups:
        if not (user := note.note_owner):
            logging.error("No user found for note: %s", note.uuid)
            raise Exception("No user found for note: %s", note.uuid)
        process_note_enhancements_async.delay_on_commit(note.uuid, user.uuid, temp_transcript)

    note.status = Note.PROCESSING_STATUS.processed
    note.save()


@shared_task(time_limit=720, soft_time_limit=600)
@locked_task(arg_index=0)
def process_note_enhancements_async(note_uuid: UUID, user_uuid: UUID, transcript: str) -> None:
    """Slow followups to note processing, that use LLMs -- structured data templates, agenda followup, workflows for tasks"""
    note = Note.objects.get(uuid=note_uuid)
    user = User.objects.get(uuid=user_uuid)
    StructuredMeetingData.objects.filter(note=note).update(is_deleted=True)

    def create_structured_data(template_uuid: UUID) -> None:
        try:
            template = StructuredMeetingDataTemplate.objects.get(uuid=template_uuid)
            structured_data = template.create_structured_meeting_data(note, {}, StructuredMeetingData.Status.PROCESSING)
            structured_data.data = get_follow_up_structured_data(template, transcript, note)
            structured_data.status = (
                StructuredMeetingData.Status.COMPLETED if structured_data.data else StructuredMeetingData.Status.FAILED
            )
            structured_data.save()
        except Exception as e:
            structured_data.status = StructuredMeetingData.Status.FAILED
            logging.error("Error processing follow-up %s.", template.uuid, exc_info=e)
            structured_data.save()

    def create_agenda_followup() -> None:
        _process_agenda_followup(note, transcript, set_processing_status_first=True)

    def add_workflow_type_to_tasks_for_note() -> None:
        note = Note.objects.get(uuid=note_uuid)
        for task in Task.objects.filter(note=note):
            task.populate_workflow_type()

    with DjangoConnectionThreadPoolExecutor() as executor:
        # Process regular structured data templates
        for template in StructuredMeetingDataTemplateRule.relevant_follow_up_templates(note.meeting_type, user):
            if template.kind == "personalized_summary":
                # Personalized summaries are handled separately
                continue
            executor.submit_with_connection_close(create_structured_data, template.uuid)

        # Process agenda follow-up if meeting type has associated agenda templates

        executor.submit_with_connection_close(create_agenda_followup)
        executor.submit_with_connection_close(add_workflow_type_to_tasks_for_note)


def _update_note_and_tasks_with_tasks_and_takeaways(
    note: Note, user: User | None, tasks_and_takeaways: TasksAndTakeaways
) -> None:
    note.advisor_notes = tasks_and_takeaways.advisor_notes
    note.key_takeaways = tasks_and_takeaways.key_takeaways
    for task_title in [a.description for a in tasks_and_takeaways.action_items]:
        Task.objects.create(task_title=task_title, task_owner=user, note=note, due_date=compute_task_due_date(user))
    note.metadata = {**(note.metadata or {}), "tags": tasks_and_takeaways.keywords}


@shared_task(time_limit=720, soft_time_limit=600)
@locked_task(arg_index=0)
def reprocess_note_after_swapping(note_uuid: UUID, speaker_mapping: dict[str, str]) -> None:
    try:
        note = Note.objects.get(uuid=note_uuid)
        temp_transcript = note.diarized_trans_with_names
        if temp_transcript and speaker_mapping:
            temp_transcript = substitute_names_in_transcript(temp_transcript, speaker_mapping)

        tasks_and_takeaways, has_tasks_and_takeaways_content = get_tasks_and_takeaways(note, temp_transcript or "")
        summary, has_summary_content = get_summary(note, temp_transcript or "")

        if not has_summary_content and not has_tasks_and_takeaways_content:
            logging.error(
                "No summary and tasks and takeaways generated, failed to reprocess note after swapping for note %s",
                note.uuid,
                exc_info=True,
            )
            return
        logging.debug("Summary is: %s, Tasks and takeaways are: %s", summary, tasks_and_takeaways)
        _update_note_and_tasks_with_tasks_and_takeaways(note, note.note_owner, tasks_and_takeaways)
        note.summary = summary.model_dump()
        note.status = Note.PROCESSING_STATUS.processed
        note.save()
        logging.info("Note reprocessed successfully after swapping: %s", note.uuid)

    except Exception as e:
        logging.error("Failed to reprocess note after swapping", exc_info=e)
        raise


def __add_or_update_client(crm_id: str, user: User, client: CRMAccount) -> None:
    """
    Add a client to the database if it does not exist, or update the existing client.

    Parameters:
    - crm_id (string): The CRM ID of the client.
    - user (User): The User object associated with the client.
    - client (CRMAccount): Information about the client from the CRM.
    """
    defaults: dict[str, Any] = {
        "name": client.name,
        "client_type": client.client_type,
        "crm_system": client.crm_system,
    }
    if client.first_name:
        defaults["first_name"] = client.first_name
    if client.last_name:
        defaults["last_name"] = client.last_name
    if client.email:
        defaults["email"] = client.email
    if client.phone_number:
        defaults["phone_number"] = client.phone_number
    if client.is_owned_by_user:
        defaults["owner"] = user

    try:
        db_client, created = Client.objects.update_or_create(
            crm_id=crm_id, organization=user.organization, defaults=defaults
        )
        if created:
            logging.info("Client %s: %s", "created" if created else "updated", db_client.uuid)
        db_client.authorized_users.add(user)
    except Client.MultipleObjectsReturned as e:
        logging.error("Multiple clients found for crm_id %s. Skipping update.", crm_id, exc_info=e)


@shared_task(time_limit=1440, soft_time_limit=1200)
@locked_task(arg_index=0)
def sync_crm_clients(user_id: UUID) -> None:
    """
    Synchronize clients from the CRM for a given user.

    Parameters:
    - user_id (str): The UUID of the User object.
    """
    user = User.objects.get(uuid=user_id)
    crm_handler = user.crm_handler
    if not crm_handler:
        logging.debug("User is not in a CRM enabled org")
        return

    clients = crm_handler.get_accounts_by_owner_email_and_name(user.email)
    logging.debug("clients: %s", clients)
    if not clients:
        logging.info("No clients to sync for user: %s", user.uuid)
        return
    for client in clients:
        logging.debug("Client: %s", client)
        __add_or_update_client(client.crm_id, user, client)

    logging.info("Synced %d clients for user: %s", len(clients), user.uuid)


def delete_older_notes() -> None:
    thirty_days_ago = timezone.now() - datetime.timedelta(days=30)
    # Convert the datetime to an ISO 8601 string for comparison
    thirty_days_ago_str = thirty_days_ago.isoformat()

    # Filter notes where metadata->>'scheduled_at' is less than the one_day_ago_str
    old_scheduled_notes = Note.objects.filter(metadata__scheduled_at__lt=thirty_days_ago_str, status="scheduled")

    # Retrieve and delete associated MeetingBot instances
    for note in old_scheduled_notes:
        MeetingBot.objects.filter(note=note).delete()

    old_scheduled_notes.delete()


@shared_task(time_limit=720, soft_time_limit=600)
def process_client_recap(
    client_recap_uuid: UUID, user_uuid: UUID, additional_data: list[str], callback_url: str | None = None
) -> None:
    # Avoid a circular import
    from api.routers.client import ClientRecap as APIClientRecap
    from api.routers.client import ClientRecapDetails

    try:
        generate_client_recap(client_recap_uuid, user_uuid, [CRMNote.model_validate_json(a) for a in additional_data])
    except Exception as e:
        logging.error("Error generating client recap", exc_info=e)
        raise

    if not callback_url:
        return

    def post_recap(status: str, recap: APIClientRecap | None) -> None:
        try:
            httpx.post(
                callback_url,
                json=ClientRecapDetails(status=status, recap=recap).model_dump(mode="json"),
            )
        except Exception as e:
            logging.error("Error posting recap to callback URL", exc_info=e)

    try:
        recap = ClientRecap.objects.get(uuid=client_recap_uuid)
        post_recap(recap.status, APIClientRecap.from_db_client_recap(recap))
    except ClientRecap.DoesNotExist:
        logging.error("Client recap not found for UUID: %s", client_recap_uuid)
        post_recap(
            ClientRecapStatus.FAILED,
            APIClientRecap(recap=[], created_at=datetime.datetime.now(datetime.timezone.utc), references=[]),
        )


def get_date_for_note(note: Note) -> datetime.datetime | None:
    """Extract and parse date from note."""
    scheduled_at = (note.metadata or {}).get("scheduled_at")
    if not scheduled_at:
        return note.created

    try:
        # Clean and parse datetime string
        clean_date = scheduled_at.replace("T", " ").split("+")[0].strip()
        try:
            # Try parsing with microseconds
            return timezone.make_aware(datetime.datetime.strptime(clean_date, "%Y-%m-%d %H:%M:%S.%f"))
        except ValueError:
            # Try parsing without microseconds
            return timezone.make_aware(datetime.datetime.strptime(clean_date, "%Y-%m-%d %H:%M:%S"))
    except ValueError as e:
        logging.error("Failed to parse date: %s", scheduled_at, exc_info=e)
        return None


def _send_agenda_nudge_to_chat(bot: MeetingBot, host_message: str, everyone_message: str) -> bool:
    return bot.send_chat_message(host_message, everyone_message)


def _send_agenda_nudge_process_nudge(
    note: Note, nudge_db: MidMeetingNudge, host_message: str, everyone_message: str
) -> None:
    meeting_bot = note.meetingbot_set.first()
    if not meeting_bot:
        logging.debug("Not sending nudge, can't get bot for note %s", note.id)
        nudge_db.status = MidMeetingNudgeStatus.ERROR
        nudge_db.save()
        return

    if _send_agenda_nudge_to_chat(meeting_bot, host_message, everyone_message):
        nudge_db.status = MidMeetingNudgeStatus.SUCCESS
    else:
        nudge_db.status = MidMeetingNudgeStatus.ERROR
    nudge_db.sent_time = timezone.now()
    nudge_db.save()


def _get_outstanding_topics_from_agenda(note: Note, transcript: str) -> str:
    result = get_agenda_completion_info(note, transcript)
    return result


def _get_interim_transcript_for_note(note: Note, user: User) -> str | None:
    meeting_bot = note.meetingbot_set.first()
    if not meeting_bot:
        logging.info("Not getting transcript for nudge, can't get bot for note %s", note.id)
        return None
    raw_transcript = meeting_bot.get_transcript()
    processed_transcript = process_transcript(note, user, raw_transcript, "recall")
    inner_transcript: str = processed_transcript.get("transcript", "")
    return inner_transcript


def send_agenda_nudges() -> None:
    now = timezone.now()

    base_app_link = os.environ.get("APP_DOMAIN")

    # For users with flag enabled
    users_to_consider = User.objects
    for user in users_to_consider.iterator():
        if not Flags.EnableMidMeetingNudge.is_active_for_user(user):
            logging.debug("Not sending nudge, feature not enabled for user %s", user.id)
            continue

        # Notes that haven't completed and been processed yet
        notes_to_consider = Note.objects.filter(status=Note.PROCESSING_STATUS.scheduled, note_owner=user)

        # Matching criteria to send a nudge:
        # Meeting has begun (start time exists, below)
        # Meeting has not ended yet (status=scheduled, above)
        # Meeting is <= NUDGE_MINUTES before ending (below)
        # Nudge has not been sent yet (below)
        # There is a topic that has not been discussed that the host needs to be nudged on
        nudge_minutes = user.get_preferences().notification_preferences.mid_meeting_nudge_minutes_before_end

        for note in notes_to_consider.iterator():
            nudge_db, _ = MidMeetingNudge.objects.get_or_create(
                user=user,
                note=note,
                defaults={
                    "scheduled_time": now,
                    "sent_time": None,
                    "status": MidMeetingNudgeStatus.NOT_PROCESSED,
                },
            )
            nudge_already_sent = nudge_db.status != MidMeetingNudgeStatus.NOT_PROCESSED
            logging.debug("For note %s: nudge_already_sent=%s", note.id, nudge_already_sent)

            scheduled_event = ScheduledEvent.objects.filter(note=note, start_time__lte=now, end_time__gte=now).first()
            start_time = scheduled_event.start_time if scheduled_event else None
            end_time = scheduled_event.end_time if scheduled_event else None
            logging.debug("For note %s: start_time=%s end_time=%s", note.id, start_time, end_time)

            if not start_time:
                logging.debug("Not sending nudge, start time null for note %s", note.id)
                continue

            nudge_time = end_time - timedelta(minutes=nudge_minutes) if end_time else None

            if nudge_time and nudge_time <= (
                start_time + timedelta(minutes=5)
            ):  # ENG-1398: don't nudge if nudge time in first five minutes of meeting
                logging.debug("Not sending nudge, would be within first five minutes of meeting for note %s", note.id)
                # We don't want to retry to send
                nudge_db.status = MidMeetingNudgeStatus.SKIPPED
                nudge_db.save()
                continue

            if (
                not nudge_time
                or now <= nudge_time  # time we're running the script is still before time we were supposed to send
                or nudge_already_sent
            ):
                logging.debug(
                    "We will not nudge for %s - nudge time at %s has not passed before %s, or nudge already sent %s",
                    note.id,
                    nudge_time,
                    now,
                    nudge_already_sent,
                )
                logging.debug("Not sending nudge, criteria not met for ongoing note %s", note.id)
                continue

            transcript = _get_interim_transcript_for_note(note, user)
            logging.debug("Transcript for note %s: %s", note.id, transcript)
            if not transcript:
                logging.info("Not sending nudge, couldn't get transcript for note %s", note.id)
                continue

            topics_not_discussed = _get_outstanding_topics_from_agenda(note, str(transcript))
            logging.debug("Topics not discussed for note %s: %s", note.id, topics_not_discussed)
            if not topics_not_discussed:
                logging.debug("Not sending nudge, topics_not_discussed is empty for note %s", note.id)
                # We don't want to rerun the API calls at the next celery run if all topics were discussed
                nudge_db.status = MidMeetingNudgeStatus.PROCESSED
                nudge_db.save()
                continue

            full_note_link = f"{base_app_link}/notes/create/{str(note.uuid)}?noteID={str(note.uuid)}&tab=prep"

            base_message = "Your meeting is nearing the end and not all agenda topics have been covered."
            full_message = f"{base_message}\nStill need to cover: {topics_not_discussed}\nFull agenda: {full_note_link}"
            # Fall back to shorter message on non-Zoom platforms, which don't support host-only and have a 500-char limit
            # Zoom has a 4096 char limit
            host_message = full_message if len(full_message) <= 4096 else base_message
            everyone_message = full_message if len(full_message) <= 500 else base_message

            logging.debug("We will nudge for %s - date %s has passed before %s", note.id, nudge_time, now)
            nudge_db.status = MidMeetingNudgeStatus.PROCESSING
            nudge_db.save()
            _send_agenda_nudge_process_nudge(note, nudge_db, host_message, everyone_message)


def delete_old_transcripts() -> None:
    now = timezone.now()

    # Get organizations query
    orgs_query = Organization.objects.filter(transcript_ttl__gte=1)

    # Iterate over organizations
    for org in orgs_query.iterator():
        threshold_date = now - datetime.timedelta(days=org.transcript_ttl or 0)

        # Build query for client notes with transcripts
        metadata_conditions = (Q(meeting_type__isnull=False) & Q(meeting_type__category="client")) | (
            Q(meeting_type__isnull=True) & Q(metadata__meeting_type="client")
        )

        transcript_conditions = (
            Q(diarized_trans_with_names__isnull=False)
            | Q(raw_transcript__isnull=False)
            | Q(raw_asr_response__isnull=False)
        )

        # Build notes query
        notes_query = Note.objects.filter(
            metadata_conditions, transcript_conditions, note_owner__organization=org
        ).only("id", "uuid", "metadata")
        notes_to_update = []
        for note in notes_query.iterator(chunk_size=100):
            relevant_date = get_date_for_note(note)

            if relevant_date and relevant_date < threshold_date:
                notes_to_update.append(note)
        if notes_to_update:
            delete_transcripts_for_notes(notes_to_update)
            logging.info(
                "Deleted old transcripts for %s notes: %s",
                org.name,
                ",".join(str(note.uuid) for note in notes_to_update),
            )
        else:
            logging.info("No old transcripts found for %s", org.name)


def delete_transcripts_for_notes(notes: list[Note]) -> None:
    """Deletes all transcript data for the given notes."""
    for note in notes:
        note.diarized_trans_with_names = None
        note.raw_transcript = None
        note.raw_asr_response = None
    Note.objects.bulk_update(notes, ["diarized_trans_with_names", "raw_transcript", "raw_asr_response"])


# Updates the Recall bots for the given calendar for the given user.
@shared_task(
    bind=True,
    time_limit=720,
    soft_time_limit=600,
)
@locked_task(arg_index=3)
def update_bots_for_calendar_events(
    self: CeleryTask,  # type: ignore[type-arg]
    calendar_id: str,
    last_updated_ts: str | None,
    user_uuid: UUID,
    *,
    event_ids_to_process: list[str] | None = None,
    calendar_platform_ids_to_process: list[str] | None = None,
    force_update: bool = False,
) -> None:
    user = User.objects.get(uuid=user_uuid)
    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        calendar_id=calendar_id,
        last_updated_ts=last_updated_ts,
        user=user,
        enable_live_transcription=not (
            Flags.EnableProcessRecallBotTranscriptsWithZeplynDeepgramIntegration.is_active_for_user(user)
        ),
        enable_native_zoom_bot=Flags.EnableNativeZoomBots.is_active_for_user(user) or False,
        event_ids_to_process=event_ids_to_process,
        calendar_platform_ids_to_process=calendar_platform_ids_to_process,
        force_update=force_update,
    )
    if fully_processed and not failed_event_ids:
        logging.info("Successfully updated bots for calendar %s", calendar_id)
        return

    logging.error(
        "Failed to fully update bots for calendar %s. Calendar was %sfully processed. Failed event IDs: %s",
        calendar_id,
        "" if fully_processed else "not ",
        failed_event_ids,
    )

    # If all events were processed, reprocess only the ones that failed; if not all events were
    # processed, reprocess all events that were requested for the last retry run of this task.
    events_to_retry = failed_event_ids if fully_processed else event_ids_to_process
    calendar_platform_ids_to_retry = None if fully_processed else calendar_platform_ids_to_process

    raise self.retry(
        # Add some jitter, in case we are processing events for two different calendars that share
        # an event.
        countdown=random.randint(0, 30),
        max_retries=3,
        args=(calendar_id, last_updated_ts, user_uuid),
        kwargs={
            "event_ids_to_process": events_to_retry,
            "calendar_platform_ids_to_process": calendar_platform_ids_to_retry,
        },
        exc=Exception(
            "Failed to fully update bots for calendar {}. Review previous logs to determine the exact issue.".format(
                calendar_id
            )
        ),
    )


def generate_csv_file(
    filename: str, headers: list[str], data_generator: Callable[[], Generator[list[str], None, None] | list[str]]
) -> str:
    """
    Generate a CSV file with the given filename, headers, and data.

    Args:
        filename (str): Name of the CSV file to create
        headers (list[str]): List of column headers
        data_generator (callable): A function that yields rows of data

    Returns:
        str: Full path to the generated CSV file
    """
    current_dir = os.getcwd()
    file_path = os.path.join(current_dir, filename)

    try:
        with open(file_path, "w", newline="") as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(headers)

            for row in data_generator():
                writer.writerow(row)

        return file_path
    except Exception as e:
        logging.error("Failed to generate CSV file %s ", filename, exc_info=e)
        raise


def send_email_with_attachments(
    subject: str,
    body: str,
    attachments: list[tuple[str, str]],
    sender: str = "Zeplyn <<EMAIL>>",
    recipient: str = "<EMAIL>",
    is_html: bool = False,
) -> None:
    """
    Send an email with multiple attachments.

    Args:
        subject (str): Email subject line
        body (str): Email body text
        attachments (list[tuple]): List of (filename, file_path) tuples
        sender (str, optional): Sender email address.
        recipient (str, optional): Recipient email address.
    """

    try:
        email_attachments = []
        for filename, file_path in attachments:
            if os.path.exists(file_path):
                with open(file_path, "rb") as file:
                    file_content = file.read()
                    email_attachments.append({"content": file_content, "filename": filename, "type": "binary"})
                logging.info("Attached: %s", filename)
            else:
                logging.info("%s not found at %s", filename, file_path)

        email_service.send_email(
            subject=subject,
            body=body,
            recipients=recipient,
            sender=sender,
            attachments=email_attachments,
            is_html=is_html,
        )

    except Exception as e:
        logging.error("An error occurred while sending the email", exc_info=e)
    finally:
        # Clean up all generated files, regardless of whether they were successfully attached
        for _, file_path in attachments:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logging.info("Cleaned up file: %s", file_path)
            except Exception as e:
                logging.error("Failed to clean up file %s", file_path, exc_info=e)


def export_org_metrics() -> None:
    """
    Generate and email organization metrics reports.
    """
    csv_files = get_all_metrics()

    current_dir = os.getcwd()

    environment = os.environ.get("APP_DOMAIN") or os.environ.get("DJANGO_SETTINGS_MODULE") or "Unknown environment"
    subject = f"Weekly Org Metrics Reports for {environment}"
    body = "Find the weekly metrics for the week in the attached CSV files:"

    attachments = []
    for csv_file in csv_files:
        file_path = os.path.join(current_dir, csv_file)
        attachments.append((csv_file, file_path))

    send_email_with_attachments(subject, body, attachments)


def export_calendar_events_data(
    org_ids: list[str] | None = None,
    user_ids: list[str] | None = None,
    start_date: str | None = None,
    end_date: str | None = None,
    filepath: str | None = None,
) -> None:
    current_dir = os.getcwd()

    if not filepath:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"calendar_events_{timestamp}.csv"
        filepath = os.path.join(current_dir, default_filename)

    asyncio.run(
        export_calendar_events_to_csv(
            filepath=filepath, org_ids=org_ids, user_ids=user_ids, start_date=start_date, end_date=end_date
        )
    )

    environment = os.environ.get("APP_DOMAIN") or os.environ.get("DJANGO_SETTINGS_MODULE") or "Unknown environment"

    subject_parts = [f"Calendar Events Data for {environment}"]
    if org_ids:
        subject_parts.append(f"Orgs: {','.join(org_ids[:3])}{' and more' if len(org_ids) > 3 else ''}")
    if user_ids:
        subject_parts.append(f"Users: {','.join(user_ids[:3])}{' and more' if len(user_ids) > 3 else ''}")
    if start_date and end_date:
        subject_parts.append(f"Period: {start_date} to {end_date}")
    elif start_date:
        subject_parts.append(f"From: {start_date}")
    elif end_date:
        subject_parts.append(f"Until: {end_date}")

    subject = " - ".join(subject_parts)

    body_parts = ["Calendar events data is attached in the CSV file."]
    if org_ids:
        body_parts.append(f"Organizations: {', '.join(org_ids)}")
    if user_ids:
        body_parts.append(f"Users: {', '.join(user_ids)}")
    if start_date or end_date:
        date_range = f"Date range: {start_date if start_date else 'earliest'} to {end_date if end_date else 'latest'}"
        body_parts.append(date_range)

    body = "\n\n".join(body_parts)

    filename = os.path.basename(filepath)

    attachments = [(filename, filepath)]

    send_email_with_attachments(subject=subject, body=body, attachments=attachments)


def email_waitlisted_user_report() -> None:
    """
    Generate and email a report of all users in waitlisted or expired state.
    """

    def generate_user_data() -> Generator[list[str], None, None]:
        users = User.objects.filter(status__in=["waitlisted", "expired"])
        for user in users:
            org_name = "No Organization"
            if user.organization:
                org_name = user.organization.name or "Unnamed Organization"
            status = user.status or "Unknown Status"
            yield [
                str(user.id),
                user.name or f"{user.first_name} {user.last_name}".strip(),
                user.email,
                org_name,
                status,
            ]

    # Generate CSV file with waitlisted and expired users
    filename = "waitlisted_expired_users_report.csv"
    headers = ["User ID", "Name", "Email", "Organization", "Status"]
    file_path = generate_csv_file(filename, headers, generate_user_data)

    # Prepare environment and email details
    environment = os.environ.get("APP_DOMAIN") or os.environ.get("DJANGO_SETTINGS_MODULE") or "Unknown environment"
    subject = f"Waitlisted and Expired Users Report for {environment}"
    body = "Please find attached the report of waitlisted and expired users."

    send_email_with_attachments(subject=subject, body=body, attachments=[(filename, file_path)])


@shared_task(
    bind=True,
    time_limit=720,
    soft_time_limit=600,
    autoretry_for=(Exception,),
    retry_backoff=5,
    max_retries=3,
    # Jitter is commonly a good practice. However, the celery jitter implementation has a tendency
    # to create very short timeouts (it jitters between 0 and the exponential timeout), which isn't
    # ideal for failures in this task (which might be due to issues with the APIs we are calling
    # into). Since these tasks are run somewhat randomly as a result of user actions, the jitter isn't
    # quite as useful for us, and we'd rather have a more predictable retry pattern.
    retry_jitter=False,
)
@locked_task(arg_index=1)
def process_phone_call_recording(self: CeleryTask[..., None], bot_id: UUID, force_update: bool) -> None:
    """
    Process a phone call recording, generate transcripts, and update the associated note.

    Args:
        bot_id (UUID): The UUID of the MeetingBot to process.
        force_update (bool, optional): Force update even if the note is already processed. Defaults to False.
    """
    logging.info("Processing phone call recording for bot: %s", bot_id)
    bot = MeetingBot.objects.get(uuid=bot_id)
    if not (note := bot.note):
        raise Exception(f"MeetingBot {bot.uuid} does not have an associated note")
    if not (user := note.note_owner):
        raise Exception(f"Note {note.uuid} does not have an associated user")
    if note.status == "processed" and not force_update:
        logging.info("Note %s already processed, skipping processing for %s", note.uuid, bot.bot_id_for_logging)
        return

    should_retry_on_failure = self.max_retries and self.request.retries < self.max_retries
    if not (raw_asr_response := note.raw_asr_response):
        if not (bot_media_url := _resolved_twilio_bot_media_url(bot, note, user)):
            if should_retry_on_failure:
                raise Exception(f"No media URL available for bot {bot.uuid}")
        if not (
            raw_asr_response := (
                fetch_and_save_deepgram_transcript({"url": bot_media_url}, note, user) if bot_media_url else None
            )
        ):
            if should_retry_on_failure:
                raise Exception(f"No ASR response returned. Failed to process {bot.bot_id_for_logging}")
            raw_asr_response = {"metadata": {"duration": 0}, "results": {"utterances": []}}

    # This call has a side effect of updating the note object. However we don't save the note in this call because
    # calls to note.save() are not testable due to issues with serializing JSON.
    _process_note_common(note, user, raw_asr_response, "deepgram")
    note.data_source = Note.DataSource.TWILIO
    note.save()
    bot.delete_media()
    note.delete_media()
    logging.info("Completed phone call note processing for bot %s, note %s", bot.uuid, note.uuid)


@shared_task(
    time_limit=720,
    soft_time_limit=600,
    autoretry_for=(Exception,),
    retry_backoff=5,
    max_retries=3,
    retry_jitter=False,
)
def generate_template_data(
    questions_text: str, schema_id: int, name: str, internal_name: str, kind: str, prompt_id: int
) -> None:
    """
    Generate structured data template from questions using a predefined prompt.

    Args:
        questions_text: The questions to generate the template from
        schema_id: ID of the schema definition
        name: User-visible title of the template
        internal_name: Internal name for the template
        kind: Type of template
        prompt_id: ID of the prompt to use
    """
    logging.info("Generating template data for schema %s with questions: %s", schema_id, questions_text)
    schema_def = StructuredMeetingDataSchema.objects.get(id=schema_id)

    # Note that this prompt is different from the input prompt. The input prompt is just being passed through to
    # be added to the template but is note used here.
    template_generator_prompt = Prompt.objects.get(unique_name="template_generator_prompt_v2")
    if not template_generator_prompt.user_prompt:
        logging.error("Template generator prompt is empty")
        raise ValueError("Template generator prompt is empty")
    formatted_prompt = template_generator_prompt.user_prompt.format(
        schema=json.dumps(schema_def.schema, indent=2),
        questions=questions_text,
    )

    try:
        response = call_model(formatted_prompt)
        data = parse_response_into_json(response)

        if not isinstance(data, dict) or "initial_data" not in data:
            raise ValueError("Invalid response structure")

        fill_template_prompt = Prompt.objects.get(id=prompt_id)

        StructuredMeetingDataTemplate.objects.create(
            schema_definition=schema_def,
            title=name,
            internal_name=internal_name,
            kind=kind,
            initial_data=data["initial_data"],
            prompt=fill_template_prompt,
        )

        logging.info("Successfully generated template data: %s", data)
        return

    except (json.JSONDecodeError, ValueError) as e:
        logging.error("Failed to generate template data after all retries: %s", str(e))
        raise ValueError("Failed to generate valid template data") from e


@shared_task(
    time_limit=720,
    soft_time_limit=600,
    autoretry_for=(Exception,),
    retry_backoff=5,
    max_retries=3,
    retry_jitter=False,
)
def delete_organization_data_task(organization_id: str) -> None:
    """
    Celery task to handle organization deletion and associated data cleanup.
    Sends email notification upon completion.
    """
    try:
        logging.info("Deleting all Data for org with id %s", organization_id)
        organization = Organization.objects.get(id=organization_id)
        org_name = organization.name

        # Gather statistics before deletion
        stats = {
            "organization_name": org_name,
            "users_count": User.objects.filter(organization=organization).count(),
            "notes_count": Note.objects.filter(note_owner__organization=organization).count(),
            "clients_count": Client.objects.filter(organization=organization).count(),
            "tasks_count": Task.objects.filter(task_owner__organization=organization).count(),
            "meeting_bots_count": MeetingBot.objects.filter(bot_owner__organization=organization).count(),
            "client_recaps_count": ClientRecap.objects.filter(user__organization=organization).count(),
        }

        # Delete organization (will cascade to related objects)
        Organization.objects.filter(id=organization.id).delete()

        email_body = f"""
        Organization Deletion Summary:

        Organization Name: {stats['organization_name']}
        Deletion Timestamp: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}

        Data Deleted:
        - Users: {stats['users_count']}
        - Notes: {stats['notes_count']}
        - Clients: {stats['clients_count']}
        - Tasks: {stats['tasks_count']}
        - Meeting Bots: {stats['meeting_bots_count']}
        - Meeting Preparations: {stats['client_recaps_count']}

        This data has been permanently deleted and cannot be recovered.
        """

        response = email_service.send_email(
            sender=settings.DEFAULT_FROM_EMAIL,
            recipients="<EMAIL>",
            body=email_body,
            subject=f"Organization Deletion Summary - {org_name}",
        )
        logging.info("Organization deletion complete. Email sent with Message ID: %s", response["message_id"])

    except Exception as e:
        logging.error("Failed to delete organization %s", organization_id, exc_info=e)
        raise


@shared_task(
    time_limit=720,
    soft_time_limit=600,
    autoretry_for=(Exception,),
    retry_backoff=5,
    max_retries=3,
    retry_jitter=False,
)
@locked_task(arg_index=2)
def generate_agenda(
    user_uuid: UUID,
    client_uuid: UUID,
    interaction_id: UUID,
    agenda_template_content: str = "",
) -> None:
    """
    Generate the agenda for a meeting.

    Args:
        user_uuid: The UUID of the user requesting the meeting prep
        client_uuid: The UUID of the client for whom to prepare the meeting
        meeting_type_uuid: The UUID of the meeting type
        interaction_id: The UUID of the ClientInteraction to update
    """
    user = User.objects.get(uuid=user_uuid)
    client = Client.objects.get(uuid=client_uuid)
    interaction = ClientInteraction.objects.get(uuid=interaction_id)

    if not interaction.agenda:
        logging.error("No agenda found for interaction %s", interaction_id)
        raise ValueError("No agenda found for interaction %s", interaction_id)

    try:
        if not agenda_template_content:
            logging.error("Agenda template content is empty for interaction %s", interaction_id)
            interaction.agenda.status = StructuredMeetingData.Status.FAILED
            interaction.agenda.save()
            return
        # Generate filled agenda data
        agenda_data = generate_filled_agenda(user, client, agenda_template_content)

        # Update the interaction with the new agenda
        interaction.agenda.data = agenda_data
        interaction.agenda.status = StructuredMeetingData.Status.COMPLETED
        interaction.agenda.save()

    except Exception:
        interaction.agenda.status = StructuredMeetingData.Status.FAILED
        interaction.agenda.save()
        logger.error(
            "Error generating agenda: user %s, client %s, interaction %s",
            str(user_uuid),
            str(client_uuid),
            str(interaction_id),
            exc_info=True,
        )
        raise


def _generate_client_intelligence(user_uuid: UUID, client_uuid: UUID, interaction_uuid: UUID) -> str | None:
    """
    Generate the client intelligence for a meeting.

    Args:
        user_uuid: The UUID of the user requesting the client intelligence
        client_uuid: The UUID of the client for whom to prepare the meeting
        interaction_id: The UUID of the ClientInteraction to update

    Returns:
        str | None: The generated markdown text or None if generation failed
    """
    try:
        # Generate client intelligence JSON
        # Historically we called client_intelligence as meeting_prep. They are similar concepts. But
        # the concept of meeting_prep has expanded. More info at go/recaprecap
        user = User.objects.get(uuid=user_uuid)
        client = Client.objects.get(uuid=client_uuid)
        client_intelligence_json = build_client_recap_for_client(user, client, [])

        if client_intelligence_json:
            # Use the new function from client_recap.py
            markdown_text = convert_client_recap_to_markdown(client_intelligence_json)
            logging.info("Successfully generated and saved client intelligence for interaction %s", interaction_uuid)
            return markdown_text
        else:
            logging.error(
                "No client intelligence generated for client %s and interaction %s", client_uuid, interaction_uuid
            )
            raise Exception(
                "No client intelligence generated for client %s and interaction %s", client_uuid, interaction_uuid
            )
    except Exception as e:
        logging.error(
            "Error generating client intelligence with user %s and client %s",
            str(user_uuid),
            str(client_uuid),
            exc_info=e,
        )
        return None


@shared_task(time_limit=720, soft_time_limit=600)
@locked_task(arg_index=2)
def create_advisor_notes_and_fill_with_client_intelligence(
    user_uuid: UUID, client_uuid: UUID, interaction_uuid: UUID
) -> None:
    user = User.objects.get(uuid=user_uuid)
    client = Client.objects.get(uuid=client_uuid)
    interaction = ClientInteraction.objects.get(uuid=interaction_uuid)
    if not interaction.advisor_notes:
        logging.error("No advisor notes found for interaction %s", interaction_uuid)
        raise ValueError("No advisor notes found for interaction %s", interaction_uuid)
    try:
        client_intelligence = _generate_client_intelligence(user.uuid, client.uuid, interaction.uuid)
        interaction.advisor_notes.data = {
            "content": client_intelligence or "",
            "format": "markdown",
        }
        interaction.advisor_notes.status = StructuredMeetingData.Status.COMPLETED
        interaction.advisor_notes.save()
        interaction.save()
    except Exception as e:
        interaction.advisor_notes.status = StructuredMeetingData.Status.FAILED
        interaction.advisor_notes.save()
        logging.error("Error creating client intelligence: %s", str(e), exc_info=True)
        raise


@shared_task(time_limit=720, soft_time_limit=600)
@locked_task(arg_index=0)
def reconcile_calendar_events(user_uuid: UUID, force_update: bool = False) -> None:
    lookahead = datetime.timedelta(seconds=settings.CALENDAR_EVENT_SYNC_DEFAULT_LOOKAHEAD_SECONDS)
    user = User.objects.get(uuid=user_uuid)

    if not Flags.UseScheduledEventsForCalendarAPI.is_active_for_user(user):
        logging.info("Skipping calendar reconciliation for user %s; flag is not active", user_uuid)
        return

    # Don't run this too often.
    cache_key = f"calendar_reconciliation_{user.uuid}_{lookahead.total_seconds()}"
    if not force_update and cache.get(cache_key):
        logging.info("Calendar reconciliation skipped for user %s; reconciliation was too recent", user_uuid)
        return
    # Get the events from the calendar providers.
    events, all_succeeded = async_to_sync(fetch_calendar_events)(user, lookahead)

    # Update the events in the database.
    updated_events: list[ScheduledEvent] = []
    autojoin_update_events: list[ScheduledEvent] = []
    created_count = 0
    updated_count = 0
    for event in events:
        try:
            scheduled_event, created = ScheduledEvent.objects.get_or_create(
                user=user,
                user_specific_source_id=event.user_specific_id,
                shared_source_id=event.id,
                defaults={
                    "start_time": event.start_time,
                    "end_time": event.end_time,
                },
            )
        except IntegrityError:
            scheduled_event = ScheduledEvent.objects.get(
                user=user,
                user_specific_source_id=event.user_specific_id,
            )
            created = False
            logging.error(
                "A ScheduledEvent with the same user and user-specific identifier already exists, but with a different "
                "shared event ID. This is unexpected, since we expect calendar source identifiers to be unchanging over "
                "time. Updating the shared source ID of this event to match the calendar entry. "
                "Scheduled event UUID: %s, user-specific ID: %s, old sharedID: %s, new shared ID: %s",
                scheduled_event.uuid,
                event.user_specific_id,
                scheduled_event.shared_source_id,
                event.id,
            )
            scheduled_event.shared_source_id = event.id
        try:
            previous_source_data = CalendarEvent.model_validate(scheduled_event.source_data)
        except ValidationError:
            previous_source_data = None
        attendee_matched_event = events_with_zeplyn_attendees([event], user)[0]
        scheduled_event.start_time = event.start_time
        scheduled_event.end_time = event.end_time
        scheduled_event.source_data = event.model_dump(mode="json")
        scheduled_event.attendee_matched_participants = EventParticipantsList(
            attendee_matched_event.participants
        ).model_dump(mode="json")
        was_previously_removed_from_provider = scheduled_event.removed_from_provider
        scheduled_event.removed_from_provider = False
        scheduled_event.save()
        updated_events.append(scheduled_event)

        if created:
            created_count += 1
        else:
            updated_count += 1
        if (
            not previous_source_data
            or previous_source_data.meeting_urls != event.meeting_urls
            or previous_source_data.start_time != event.start_time
            or previous_source_data.end_time != event.end_time
            or was_previously_removed_from_provider
        ):
            autojoin_update_events.append(scheduled_event)

    # Delete upcoming events that are from a provider but don't have a matching provider event.
    #
    # This isn't quite perfect; it will delete events that have been moved to the future outside of
    # the reconciliation window. We are assuming that events this far out can be deleted and then
    # recreated if necessary, and that the lookahead window is long enough that it is unlikely that
    # these events are linked to other objects in the database.
    events_to_delete = ScheduledEvent.objects.filter(
        user=user,
        user_specific_source_id__isnull=False,
        end_time__gte=timezone.now(),
        start_time__lte=timezone.now() + lookahead,
    ).exclude(pk__in=[e.pk for e in updated_events])
    autojoin_update_events.extend([e for e in events_to_delete])
    deleted_count = events_to_delete.update(removed_from_provider=True)

    logging.info(
        "Reconciled calendar events for user %s. Created %d events, updated (potentially with no changes) %d events, deleted %d events.",
        user_uuid,
        created_count,
        updated_count,
        deleted_count,
    )

    if not all_succeeded:
        logging.error(
            "Failed to fetch all calendar events for user %s. Reconciled events that were fetched.", user_uuid
        )
    else:
        cache.set(cache_key, True, settings.CALENDAR_RECONCILIATION_TASK_TTL_SECONDS)

    # Update the autojoin bots for the events that were updated (in a relevant way).
    if not autojoin_update_events:
        logging.info("No events to update autojoin for user %s", user.uuid)
        return

    update_autojoin_bots.delay(
        user_uuid, [e.uuid for e in autojoin_update_events], update_recall=False, force_update=force_update
    )


@shared_task(time_limit=720, soft_time_limit=600)
@locked_task(arg_index=0)
def update_autojoin_bots(
    user_uuid: UUID,
    scheduled_event_uuids: list[UUID] | None,
    *,
    update_recall: bool = False,
    force_update: bool = False,
) -> None:
    user = User.objects.get(uuid=user_uuid)

    events_query = ScheduledEvent.objects.filter(user=user)
    if scheduled_event_uuids:
        events_query = events_query.filter(uuid__in=scheduled_event_uuids)

    # TODO: implement autojoin without relying on Recall, for scheduled notes, CRM events and in
    # place of Recall's auto-join functionality.

    if not user.recall_calendar_id:
        logging.info(
            "Skipping update of Recall autojoin bots for user %s: user does not have Recall autojoin enabled", user_uuid
        )
        return
    if not update_recall:
        logging.info(
            "Skipping update of Recall autojoin bots for user %s: Recall event update not requested by caller",
            user_uuid,
        )
        return

    if not (recall_calendar_platform := RecallBotController.connected_calendar_platform(user.recall_calendar_id)):
        logging.info(
            "Skipping update of Recall autojoin bots for user %s: user does not have a connected Recall calendar",
            user_uuid,
        )
        return

    calendar_events = events_query.filter(source_data__isnull=False)
    recall_autojoin_event_ids: list[str] = []
    for event in calendar_events:
        try:
            raw_data = CalendarEvent.model_validate(event.source_data)
            if raw_data.provider == recall_calendar_platform:
                recall_autojoin_event_ids.append(raw_data.user_specific_id)
        except ValidationError as e:
            logging.error("Failed to validate raw source data for event %s", event.uuid, exc_info=e)

    if not recall_autojoin_event_ids:
        logging.info(
            "Finished update of Recall autojoin bots for user %s: no events to update",
            user_uuid,
        )
        return

    update_bots_for_calendar_events.delay(
        user.recall_calendar_id,
        None,
        user_uuid,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=recall_autojoin_event_ids,
        force_update=force_update,
    )
