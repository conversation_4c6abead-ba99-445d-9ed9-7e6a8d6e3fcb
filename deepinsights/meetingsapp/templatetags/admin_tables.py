from typing import Any, Dict, List

from django import template
from django.utils.safestring import mark_safe

register = template.Library()


@register.simple_tag
def render_admin_table(
    title: str, headers: List[str], rows: List[Dict[str, Any]], empty_message: str = "No items available"
) -> str:
    """
    Render an admin-style table with the given headers and rows.

    Args:
        title: Table title
        headers: List of column headers
        rows: List of dictionaries where keys match headers
        empty_message: Message to display when no rows are present
    """
    if not rows:
        return f'<div class="module aligned"><p>{empty_message}</p></div>'

    html = f"""
    <div class="module aligned">
        <h2>{title}</h2>
        <table>
            <thead>
                <tr>
    """

    # Add headers
    for header in headers:
        html += f"<th>{header}</th>"

    html += """
                </tr>
            </thead>
            <tbody>
    """

    # Add rows
    for row in rows:
        html += "<tr>"
        for header in headers:
            value = row.get(header, "")
            html += f"<td>{value}</td>"
        html += "</tr>"

    html += """
            </tbody>
        </table>
    </div>
    """

    return mark_safe(html)
