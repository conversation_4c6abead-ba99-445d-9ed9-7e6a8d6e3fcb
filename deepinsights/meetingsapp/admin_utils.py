import csv
import logging
from datetime import datetime
from io import StringIO
from typing import Any, Callable, Sequence, Union
from urllib.parse import urlencode

from django import forms
from django.contrib import admin, messages
from django.contrib.auth.models import AnonymousUser
from django.db.models import Q
from django.db.models.query import QuerySet
from django.http import HttpRequest, HttpResponse
from django.shortcuts import redirect, render
from django.urls import path, reverse
from django.utils import timezone
from django.utils.html import format_html
from django.utils.safestring import SafeString
from django_celery_beat.admin import PeriodicTaskAdmin
from django_celery_beat.models import PeriodicTask
from pydantic import ValidationError

from app.celery import export_monthly_calendar_events
from deepinsights.core.integrations.meetingbot.recall_ai import RecallBotController
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.phone_number import PhoneNumber
from deepinsights.meetingsapp.models.structured_meeting_data import (
    StructuredMeetingDataTemplate,
    StructuredMeetingDataTemplateRule,
)
from deepinsights.meetingsapp.tasks import delete_organization_data_task
from deepinsights.meetingsapp.templatetags.admin_tables import render_admin_table
from deepinsights.users.models.user import User, UserMetadata

logger = logging.getLogger(__name__)


def process_users_from_csv(  # type: ignore[no-untyped-def]
    organization,
    csv_file,
    extra_context: dict[str, Any] = None,  # type: ignore[assignment]
) -> tuple[int, int, int, int, list[str]]:
    """
    Process users from CSV file and create users/licenses for an organization.

    Args:
        organization: Organization model instance
        csv_file: CSV file object
        extra_context: Additional context for user metadata (optional)

    Returns:
        Tuple containing:
            - users_created: Number of users created
            - users_skipped: Number of existing users skipped
            - licenses_created: Number of licenses created
            - licenses_skipped: Number of existing licenses skipped
            - errors: List of error messages
    """
    users_created = 0
    users_skipped = 0
    licenses_created = 0
    licenses_skipped = 0
    errors = []

    logger.info("Starting CSV processing for organization: %s", organization.name)

    try:
        if hasattr(csv_file, "read"):
            decoded_file = csv_file.read().decode("utf-8")
        else:
            decoded_file = csv_file

    except Exception as e:
        error_msg = "Error reading CSV file: %s" % str(e)
        errors.append(error_msg)
        logger.error(error_msg)
        return users_created, users_skipped, licenses_created, licenses_skipped, errors

    try:
        csv_data = csv.DictReader(StringIO(decoded_file))
        required_fields = ["Email", "Name", "License Type", "First Name", "Last Name"]

        # Validate CSV header
        header = csv_data.fieldnames
        if not header:
            raise ValueError("CSV file is empty or malformed")

        missing_fields = [field for field in required_fields if field not in header]
        if missing_fields:
            raise ValueError("Missing required fields in CSV: %s" % ", ".join(missing_fields))
    except Exception as e:
        error_msg = "Error processing CSV file: %s" % str(e)
        errors.append(error_msg)
        logger.error(error_msg)
        return users_created, users_skipped, licenses_created, licenses_skipped, errors

    org_name = organization.name

    for row_number, row in enumerate(csv_data, start=2):
        try:
            all_empty = all([(not row.get(f, "").strip()) for f in required_fields])
            if all_empty:
                logging.info("Row %d: No data provided", row_number)
                continue

            if not (email := row.get("Email", "").strip()):
                errors.append("Row %d: Error creating user, no email provided" % row_number)
                continue

            # Create or get user
            user = User.objects.filter(email=email).first()
            if user:
                users_skipped += 1
                logger.info("Skipped existing user %s", email)
                continue

            try:
                license_type = User.LicenseType(row.get("License Type", "advisor").lower())
            except ValueError:
                license_type = User.LicenseType.advisor

            user = User.objects.create(
                email=email,
                name=row.get("Name", "").strip(),
                username=email,
                first_name=row.get("First Name", "").strip(),
                last_name=row.get("Last Name", "").strip(),
                organization=organization,
                role=license_type.value,
                status=User.STATUS_CHOICES.active,
                license_type=license_type,
                is_superuser=row.get("is_superuser", "False").lower() == "true",
                pin=row.get("Phone Pin", "").strip(),
            )

            if phone_number := row.get("Phone", "").strip():
                PhoneNumber.objects.create(
                    user=user,
                    number=phone_number,
                    primary=True,
                )

            user.user_context = "%s is a wealth management firm. %s is a %s at %s." % (
                org_name,
                user.name,
                user.get_role_for_llm(),
                org_name,
            )
            user.biasing_words = ["Zeplyn"]

            if extra_context:
                # Only set extra context that matches the model fields.
                try:
                    model = UserMetadata.model_validate(user.metadata)
                    for key, value in extra_context.items():
                        if hasattr(model, key):
                            setattr(model, key, value)
                        else:
                            logger.warning(f"Unexpected key '{key}' in extra_context for user {user.email}")
                    user.metadata = model.model_dump(exclude_none=True)
                except ValidationError as e:
                    error_msg = "Row %d: Error validating extra context for user %s: %s" % (
                        row_number,
                        email,
                        str(e),
                    )
                    errors.append(error_msg)
                    logger.error(error_msg)

            user.save()
            users_created += 1
            logger.info("Created new user %s", email)

        except Exception as e:
            error_msg = "Row %d: Error processing user %s: %s" % (row_number, email, str(e))
            errors.append(error_msg)
            logger.error(error_msg)

    logger.info(
        "CSV processing completed. Created: %d, Skipped: %d, Licenses created: %d, Licenses skipped: %d",
        users_created,
        users_skipped,
        licenses_created,
        licenses_skipped,
    )

    return users_created, users_skipped, licenses_created, licenses_skipped, errors


def delete_organization_and_associated_data(
    self: admin.ModelAdmin[Organization], request: HttpRequest, queryset: QuerySet[Organization]
) -> None:
    if queryset.count() > 1:
        self.message_user(request, "Please select exactly one organization for this action.", level=messages.ERROR)
        return

    organization = queryset.first()
    if not organization:
        self.message_user(request, "No organization selected.", level=messages.ERROR)
        return

    try:
        # Start the celery task
        delete_organization_data_task.delay(str(organization.id))

        self.message_user(
            request,
            (
                f"Organization deletion process started for {organization.name}. "
                "You will receive an email confirmation when complete."
            ),
            level=messages.SUCCESS,
        )

    except Exception as e:
        self.message_user(request, f"Error scheduling organization deletion: {str(e)}", level=messages.ERROR)


# Admin action to disable an organization by:
# 1. Removing Recall.ai calendar integrations for all users
# 2. Setting user statuses to expired
# 3. Updating organization description with expiry date
def disable_organization(
    modeladmin: admin.ModelAdmin[Organization], request: HttpRequest, queryset: QuerySet[Organization]
) -> None:
    if queryset.count() > 1:
        modeladmin.message_user(request, "Please select exactly one organization for this action.", messages.ERROR)
        return

    organization = queryset.first()
    if not organization:
        modeladmin.message_user(request, "No organization selected.", messages.ERROR)
        return

    users = User.objects.filter(organization=organization)

    users_processed = 0
    calendars_unlinked = 0
    failed_unlinks: list[str] = []

    recall_controller = RecallBotController()

    for user in users:
        try:
            if calendar_id := user.recall_calendar_id:
                if recall_controller.unlink_calendar(calendar_id):
                    calendars_unlinked += 1
                else:
                    failed_unlinks.append(user.email)
                user.recall_calendar_id = None
                user.recall_calendar_platform = None

            user.status = User.STATUS_CHOICES.expired
            user.save()
            users_processed += 1

        except Exception as e:
            modeladmin.message_user(request, f"Error processing user {user.email}: {str(e)}", messages.ERROR)

    current_time = timezone.now()
    if isinstance(request.user, AnonymousUser):
        admin_user = "Anonymous User"
    else:
        admin_user = request.user.get_full_name() or request.user.email or "Unknown User"
    expiry_note = f"\n\nExpired on {current_time.strftime('%Y-%m-%d %H:%M:%S')} by admin action by {admin_user}"
    try:
        organization.description += expiry_note
        organization.save()
    except Exception as e:
        modeladmin.message_user(request, f"Error updating organization description: {str(e)}", messages.ERROR)
        return

    base_msg = (
        f"Successfully disabled organization. Processed {users_processed} users and "
        f"successfully unlinked {calendars_unlinked} calendars."
    )

    if failed_unlinks:
        warning_msg = (
            f"{base_msg}\nWARNING: Failed to unlink calendars for the following users "
            f"(manual unlinking may be required): {', '.join(failed_unlinks)}"
        )
        modeladmin.message_user(request, warning_msg, messages.WARNING)
    else:
        modeladmin.message_user(request, base_msg, messages.SUCCESS)


def _generate_template_table(
    templates: Union[QuerySet[StructuredMeetingDataTemplate], Sequence[StructuredMeetingDataTemplate]],
    title: str,
    include_meeting_types: bool = True,
    include_access_level: bool = True,
    access_level_logic: Callable[[StructuredMeetingDataTemplate], str] | None = None,
    meeting_types_getter: Callable[[StructuredMeetingDataTemplate], Sequence[MeetingType]] | None = None,
) -> SafeString:
    """
    Generic function to generate HTML tables for templates and agendas.

    Args:
        templates: QuerySet or Sequence of StructuredMeetingDataTemplate objects
        title: Title for the table
        include_meeting_types: Whether to include meeting types column
        include_access_level: Whether to include the access level column
        access_level_logic: Optional function to determine access level for each template
        meeting_types_getter: Optional function to get meeting types for a template

    Returns:
        SafeString containing the HTML table
    """

    # Check if templates is a QuerySet or a Sequence (like list) to handle both cases appropriately
    # Examples:
    # 1. QuerySet case: for structured meeting data templates, templates = StructuredMeetingDataTemplate.objects.filter(is_active=True)
    # 2. Sequence case: for agenda templates, templates = [template1, template2]  # Already evaluated templates
    if isinstance(templates, QuerySet):
        has_templates = templates.exists()
    else:
        has_templates = bool(templates)

    if not has_templates:
        return format_html("<p>No {0} available</p>", title.lower())

    headers = ["Template", "Kind", "Internal Name", "Prompt"]
    if include_meeting_types:
        headers.append("Meeting Types")
    if include_access_level:
        headers.append("Access Level")

    rows = []
    for template in templates:
        template_url = reverse("admin:meetingsapp_structuredmeetingdatatemplate_change", args=[template.id])
        prompt_link = ""
        if template.prompt:
            prompt_url = reverse("admin:meetingsapp_prompt_change", args=[template.prompt.id])
            prompt_link = f'<a href="{prompt_url}">{template.prompt.name or "Unnamed prompt"}</a>'

        row_data = {
            "Template": f'<a href="{template_url}">{template.title or template.internal_name}</a>',
            "Kind": template.kind,
            "Internal Name": template.internal_name or "",
            "Prompt": prompt_link,
        }

        if include_meeting_types and meeting_types_getter:
            meeting_type_links = []
            for meeting_type in meeting_types_getter(template):
                meeting_type_url = reverse("admin:meetingsapp_meetingtype_change", args=[meeting_type.id])
                meeting_type_links.append(f'<a href="{meeting_type_url}">{meeting_type.name}</a>')
            row_data["Meeting Types"] = ", ".join(meeting_type_links)

        if include_access_level and access_level_logic:
            row_data["Access Level"] = access_level_logic(template)

        rows.append(row_data)

    return format_html(render_admin_table(title, headers, rows))


def get_template_display_html(
    templates: Union[QuerySet[StructuredMeetingDataTemplate], Sequence[StructuredMeetingDataTemplate]],
    title: str = "Templates",
) -> SafeString:
    """Generate HTML for displaying templates in admin pages."""
    return _generate_template_table(templates, title, include_meeting_types=False, include_access_level=False)


def get_associated_templates(note: Note) -> list[StructuredMeetingDataTemplate]:
    """Get templates associated with a note based on its meeting type and owner."""
    if not note.meeting_type or not note.note_owner:
        logger.debug("No meeting type or note owner for note %s", note.id)
        return []

    logger.info(
        "Fetching templates for note %s with meeting_type=%s and owner=%s",
        note.id,
        note.meeting_type.id,
        note.note_owner.id,
    )

    templates = list(
        StructuredMeetingDataTemplateRule.relevant_follow_up_templates(
            note.meeting_type, note.note_owner
        ).select_related("prompt")
    )

    if not templates:
        logger.info(
            "No templates found for note %s. Meeting type: %s, Owner: %s",
            note.id,
            note.meeting_type.name,
            note.note_owner.email,
        )
    else:
        template_names = [template.title for template in templates if template.title]
        logger.debug("Found templates for note %s: %s", note.id, ", ".join(template_names))

    return templates


def get_note_followup_templates_html(note: Note) -> SafeString:
    """Return HTML for templates associated with the note based on meeting type and rules."""
    templates = get_associated_templates(note)
    if not templates:
        return format_html("<p>No templates found for this note.</p>")

    return get_template_display_html(templates, title="Associated Templates")


def get_note_agenda_template_html(note: Note) -> SafeString:
    """Get HTML display of agenda templates associated with a note's meeting type."""
    if not note.meeting_type:
        return format_html("<p>No meeting type specified</p>")

    agenda_templates = note.meeting_type.agenda_templates.all().select_related("prompt")
    return get_template_display_html(agenda_templates, title="Agenda Templates")


def get_org_active_templates_html(organization: Organization) -> SafeString:
    """Get HTML display of all active templates for an organization."""
    # For new organizations (not saved yet), only show templates marked as everyone=True
    if not organization.pk:
        rules = (
            StructuredMeetingDataTemplateRule.objects.filter(everyone=True)
            .prefetch_related("follow_up_templates", "meeting_types")
            .distinct()
        )
    else:
        # Get all rules that apply to this organization with prefetched relationships
        rules = (
            StructuredMeetingDataTemplateRule.objects.filter(Q(everyone=True) | Q(organizations=organization))
            .prefetch_related("follow_up_templates", "meeting_types")
            .distinct()
        )

    # Get all templates from these rules with prefetched relationships
    template_ids = rules.values_list("follow_up_templates", flat=True).distinct()
    templates = (
        StructuredMeetingDataTemplate.objects.filter(id__in=template_ids)
        .select_related("prompt")
        .prefetch_related(
            "structuredmeetingdatatemplaterule_set",
            "structuredmeetingdatatemplaterule_set__meeting_types",
            "structuredmeetingdatatemplaterule_set__organizations",
        )
    )

    def get_access_level(template: StructuredMeetingDataTemplate) -> str:
        template_rules = rules.filter(follow_up_templates=template)
        return "Global" if any(rule.everyone for rule in template_rules) else "Organization"

    def get_meeting_types(template: StructuredMeetingDataTemplate) -> Sequence[MeetingType]:
        # Get meeting types associated with these rules for this template
        template_rules = rules.filter(follow_up_templates=template)
        meeting_types = MeetingType.objects.filter(
            Q(everyone=True) | Q(organizations=organization), structuredmeetingdatatemplaterule__in=template_rules
        ).distinct()
        return list(meeting_types)

    return _generate_template_table(
        templates, "Active Templates", access_level_logic=get_access_level, meeting_types_getter=get_meeting_types
    )


def get_org_active_agendas_html(organization: Organization) -> SafeString:
    """Get HTML display of all active agenda templates for an organization.

    Shows all agenda templates that are accessible to the organization through:
    1. Global meeting types (everyone=True)
    2. Organization-specific meeting types
    """
    # Get all meeting types that apply to this organization
    meeting_types = (
        MeetingType.objects.filter(Q(everyone=True) | Q(organizations=organization))
        .only("id", "everyone", "name")  # Only fetch fields we need
        .distinct()
    )

    # Get all agenda templates from these meeting types
    agenda_templates = (
        StructuredMeetingDataTemplate.objects.filter(agenda_templates__in=meeting_types)
        .select_related("prompt")
        .prefetch_related(
            "agenda_templates",
            "agenda_templates__organizations",  # Prefetch organizations for meeting types
        )
        .only("id", "title", "internal_name", "kind", "prompt")  # Only fetch needed fields
        .distinct()
    )

    def get_access_level(template: StructuredMeetingDataTemplate) -> str:
        template_meeting_types = [mt for mt in meeting_types if template in mt.agenda_templates.all()]
        return "Global" if any(mt.everyone for mt in template_meeting_types) else "Organization"

    def get_meeting_types(template: StructuredMeetingDataTemplate) -> Sequence[MeetingType]:
        return [mt for mt in meeting_types if template in mt.agenda_templates.all()]

    return _generate_template_table(
        agenda_templates,
        "Active Agenda Templates",
        access_level_logic=get_access_level,
        meeting_types_getter=get_meeting_types,
    )


def get_user_active_templates_html(user: User) -> SafeString:
    """Get HTML display of all active templates for a user."""
    # Get rules that apply to this user directly or through their organization
    org_condition = Q(organizations=user.organization) if user.organization else Q()
    rules = StructuredMeetingDataTemplateRule.objects.filter(
        Q(everyone=True) | Q(users=user) | org_condition
    ).distinct()

    # Get templates from these rules
    template_ids = rules.values_list("follow_up_templates", flat=True).distinct()
    templates = StructuredMeetingDataTemplate.objects.filter(id__in=template_ids).select_related("prompt")

    def get_access_level(template: StructuredMeetingDataTemplate) -> str:
        template_rules = rules.filter(follow_up_templates=template)
        if any(rule.everyone for rule in template_rules):
            return "Global"
        if user.organization and any(user.organization in rule.organizations.all() for rule in template_rules):
            return "Organization"
        return "User"

    return _generate_template_table(templates, "Active Templates", access_level_logic=get_access_level)


def get_user_active_agendas_html(user: User) -> SafeString:
    """Get HTML display of all active agenda templates for a user.

    Shows all agenda templates that are accessible to the user through:
    1. Global meeting types (everyone=True)
    2. User-specific meeting types
    3. Organization-specific meeting types (if the user belongs to an organization)
    """
    # Include organization-specific meeting types if the user has an organization
    org_condition = Q(organizations=user.organization) if user.organization else Q()

    meeting_types = (
        MeetingType.objects.filter(Q(everyone=True) | Q(users=user) | org_condition)
        .only("id", "everyone", "name")  # Only fetch fields we need
        .prefetch_related("organizations", "users")  # Prefetch for access level determination
        .distinct()
    )

    agenda_templates = (
        StructuredMeetingDataTemplate.objects.filter(agenda_templates__in=meeting_types)
        .select_related("prompt")
        .prefetch_related(
            "agenda_templates",
            "agenda_templates__users",
            "agenda_templates__organizations",  # Also prefetch organizations
        )
        .only("id", "title", "internal_name", "kind", "prompt")  # Only fetch needed fields
        .distinct()
    )

    def get_access_level(template: StructuredMeetingDataTemplate) -> str:
        template_meeting_types = meeting_types.filter(agenda_templates=template)
        if any(mt.everyone for mt in template_meeting_types):
            return "Global"
        if user.organization and any(user.organization in mt.organizations.all() for mt in template_meeting_types):
            return "Organization"
        return "User"

    def get_meeting_types(template: StructuredMeetingDataTemplate) -> Sequence[MeetingType]:
        return list(meeting_types.filter(agenda_templates=template))

    return _generate_template_table(
        agenda_templates,
        "Active Agenda Templates",
        access_level_logic=get_access_level,
        meeting_types_getter=get_meeting_types,
    )


# Form for configuring calendar event export parameters
class CalendarExportForm(forms.Form):
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput, required=False)
    org_ids = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={"rows": 2}),
        help_text="Comma-separated list of organization IDs to filter by (optional, defaults to all orgs)",
    )
    user_ids = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={"rows": 2}),
        help_text="Comma-separated list of user IDs to filter by (optional, defaults to all users)",
    )
    start_date = forms.CharField(
        required=False, help_text="Start date in format mm-dd-yyyy (optional, defaults to 30 days ago)"
    )
    end_date = forms.CharField(required=False, help_text="End date in format mm-dd-yyyy (optional, defaults to today)")

    def clean_start_date(self):  # type: ignore[no-untyped-def]
        start_date = self.cleaned_data.get("start_date")
        if not start_date:
            return start_date

        try:
            datetime.strptime(start_date, "%m-%d-%Y")
            return start_date
        except ValueError:
            raise forms.ValidationError("Start date must be in mm-dd-yyyy format")

    def clean_end_date(self):  # type: ignore[no-untyped-def]
        end_date = self.cleaned_data.get("end_date")
        if not end_date:
            return end_date

        try:
            datetime.strptime(end_date, "%m-%d-%Y")
            return end_date
        except ValueError:
            raise forms.ValidationError("End date must be in mm-dd-yyyy format")

    def clean(self):  # type: ignore[no-untyped-def]
        cleaned_data = super().clean()
        start_date = cleaned_data.get("start_date") if cleaned_data else None
        end_date = cleaned_data.get("end_date") if cleaned_data else None

        if start_date and end_date:
            try:
                start_date_obj = datetime.strptime(start_date, "%m-%d-%Y")
                end_date_obj = datetime.strptime(end_date, "%m-%d-%Y")

                if start_date_obj > end_date_obj:
                    raise forms.ValidationError("Start date cannot be after end date")
            except ValueError:
                pass

        return cleaned_data


class CustomPeriodicTaskAdmin(PeriodicTaskAdmin):  # type: ignore[misc]
    list_display = ["name", "enabled", "crontab", "interval", "start_time", "last_run_at", "one_off"]

    def get_urls(self):  # type: ignore[no-untyped-def]
        urls = super().get_urls()
        custom_urls = [
            path(
                "run-calendar-export/",
                self.admin_site.admin_view(self.run_calendar_export_view),
                name="run-calendar-export",
            ),
        ]
        return custom_urls + urls

    def run_calendar_export_view(self, request: HttpRequest) -> HttpResponse:
        selected_ids = request.GET.get("ids", "").split(",") if request.GET.get("ids") else []

        if selected_ids:
            invalid_tasks = PeriodicTask.objects.filter(id__in=selected_ids).exclude(
                task="app.celery.export_monthly_calendar_events"
            )
            if invalid_tasks.exists():
                self.message_user(
                    request,
                    "Error: Can only run calendar export on export_monthly_calendar_events tasks",
                    level=messages.ERROR,
                )
                return redirect("admin:django_celery_beat_periodictask_changelist")

        if request.method == "POST":
            form = CalendarExportForm(request.POST)
            if form.is_valid():
                org_ids = (
                    [org_id.strip() for org_id in form.cleaned_data["org_ids"].split(",")]
                    if form.cleaned_data["org_ids"]
                    else None
                )
                user_ids = (
                    [user_id.strip() for user_id in form.cleaned_data["user_ids"].split(",")]
                    if form.cleaned_data["user_ids"]
                    else None
                )
                start_date = form.cleaned_data["start_date"] or None
                end_date = form.cleaned_data["end_date"] or None

                task_result = export_monthly_calendar_events.delay(
                    org_ids=org_ids, user_ids=user_ids, start_date=start_date, end_date=end_date
                )

                self.message_user(
                    request,
                    f"Calendar export task started with task ID: {task_result.id}. "
                    f"The export file will be emailed to the configured recipient.",
                )
                return redirect("admin:django_celery_beat_periodictask_changelist")
        else:
            initial_data = {"_selected_action": selected_ids} if selected_ids else {}
            form = CalendarExportForm(initial=initial_data)

        context = {
            "opts": self.model._meta,
            "form": form,
            "title": "Run Calendar Export",
            "queryset": [{"pk": id} for id in selected_ids],
        }
        return render(request, "admin/meetingsapp/run_calendar_export.html", context)

    def run_calendar_export(  # type: ignore[no-untyped-def]
        self, request: HttpRequest, queryset: QuerySet[PeriodicTask], description=None
    ) -> HttpResponse:
        selected = request.POST.getlist("_selected_action")
        base_url = reverse("admin:run-calendar-export")
        query_params = urlencode({"ids": ",".join(selected)})
        url = f"{base_url}?{query_params}"
        return redirect(url)

    def get_actions(self, request: HttpRequest):  # type: ignore[no-untyped-def]
        actions = super().get_actions(request)

        actions["run_calendar_export"] = (
            lambda modeladmin, request, queryset: self.run_calendar_export(request, queryset),
            "run_calendar_export",
            "Run calendar export",
        )
        return actions
