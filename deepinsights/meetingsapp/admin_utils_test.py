import csv
from io import <PERSON><PERSON>
from typing import Any
from unittest.mock import Mock, patch

import pytest
from django.contrib import messages
from django.contrib.admin.sites import AdminSite
from django.contrib.auth.models import AnonymousUser
from django.core.files.uploadedfile import SimpleUploadedFile
from django.http import HttpRequest, QueryDict
from django.test import RequestFactory, TestCase
from django_celery_beat.models import PeriodicTask

from deepinsights.meetingsapp.admin_utils import (
    CalendarExportForm,
    CustomPeriodicTaskAdmin,
    delete_organization_and_associated_data,
    disable_organization,
    get_associated_templates,
    get_note_agenda_template_html,
    get_note_followup_templates_html,
    get_org_active_agendas_html,
    get_org_active_templates_html,
    get_user_active_agendas_html,
    get_user_active_templates_html,
    process_users_from_csv,
)
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.structured_meeting_data import (
    StructuredMeetingDataTemplate,
    StructuredMeetingDataTemplateRule,
)
from deepinsights.users.models.user import User

pytestmark = [pytest.mark.django_db]


@pytest.fixture
def mock_admin() -> Mock:
    return Mock()


@pytest.fixture
def organization() -> Organization:
    return Organization.objects.create(name="Test Organization", description="Test Description")


@pytest.fixture
def mock_request() -> Mock:
    request = Mock(spec=HttpRequest)
    request.user = Mock()
    request.user.get_full_name.return_value = "Admin User"
    request.user.email = "<EMAIL>"
    return request


@pytest.fixture
def organization_with_users(organization: Organization) -> Organization:
    User.objects.create(
        email="<EMAIL>",
        name="User 1",
        username="User 1",
        organization=organization,
        metadata={"recall_calendar_id": "calendar1", "recall_calendar_platform": "google"},
    )
    User.objects.create(
        email="<EMAIL>",
        name="User 2",
        username="User 2",
        organization=organization,
        metadata={"recall_calendar_id": "calendar2", "recall_calendar_platform": "microsoft"},
    )
    User.objects.create(
        email="<EMAIL>",
        name="User 3",
        username="User 3",
        organization=organization,
    )
    return organization


@pytest.fixture
def admin_request_factory() -> RequestFactory:
    return RequestFactory()


@pytest.fixture
def mock_periodic_task() -> Mock:
    mock_task = Mock(spec=PeriodicTask)
    mock_task.id = 1
    mock_task.pk = 1
    mock_task.name = "test_task"
    return mock_task


@pytest.fixture
def calendar_admin() -> CustomPeriodicTaskAdmin:
    return CustomPeriodicTaskAdmin(PeriodicTask, AdminSite())


class TestProcessUsersFromCSV:
    def _create_csv_file(self, data: list[dict[str, Any]]) -> SimpleUploadedFile:
        output = StringIO()
        if data:
            writer = csv.DictWriter(output, fieldnames=data[0].keys())
            writer.writeheader()
            writer.writerows(data)
        return SimpleUploadedFile("test.csv", output.getvalue().encode("utf-8"), content_type="text/csv")

    def test_successful_processing(self, organization: Organization) -> None:
        csv_data = [
            {
                "Email": "<EMAIL>",
                "Name": "Test User 1",
                "License Type": "advisor",
                "First Name": "Test",
                "Last Name": "User 1",
                "Phone Pin": "1234",
                "Phone": "************",
            },
            {
                "Email": "<EMAIL>",
                "Name": "Test User 2",
                "License Type": "csa",
                "First Name": "Test",
                "Last Name": "User 2",
            },
        ]
        csv_file = self._create_csv_file(csv_data)

        users_created, users_skipped, licenses_created, licenses_skipped, errors = process_users_from_csv(
            organization, csv_file
        )

        assert users_created == 2
        assert users_skipped == 0
        assert licenses_created == 0
        assert licenses_skipped == 0
        assert len(errors) == 0

        user1 = User.objects.get(email="<EMAIL>")
        assert user1.name == "Test User 1"
        assert user1.role == User.LicenseType.advisor
        assert user1.organization == organization
        assert user1.pin == "1234"
        assert user1.primary_phone_number == "+12125557890"

        user2 = User.objects.get(email="<EMAIL>")
        assert user2.name == "Test User 2"
        assert user2.role == User.LicenseType.csa
        assert user2.organization == organization

    @pytest.mark.parametrize(
        "license_type, expected",
        [
            ("advisor", User.LicenseType.advisor),
            ("csa", User.LicenseType.csa),
            ("staff", User.LicenseType.staff),
            ("Advisor", User.LicenseType.advisor),
            ("adViSor", User.LicenseType.advisor),
            ("CsA", User.LicenseType.csa),
            ("staFF", User.LicenseType.staff),
            ("invalid", User.LicenseType.advisor),
            ("", User.LicenseType.advisor),
        ],
    )
    def test_license_type_normalization(
        self, organization: Organization, license_type: str, expected: User.LicenseType
    ) -> None:
        csv_file = self._create_csv_file(
            [
                {
                    "Email": "<EMAIL>",
                    "Name": "Test User",
                    "License Type": license_type,
                    "First Name": "Test",
                    "Last Name": "User",
                }
            ]
        )

        users_created, users_skipped, licenses_created, licenses_skipped, errors = process_users_from_csv(
            organization, csv_file
        )

        assert users_created == 1
        assert users_skipped == 0
        assert licenses_created == 0
        assert licenses_skipped == 0
        assert not errors

        assert User.objects.get(email="<EMAIL>").license_type == expected

    def test_skip_existing_users(self, organization: Organization) -> None:
        User.objects.create(
            email="<EMAIL>",
            name="Existing User",
            username="<EMAIL>",
            organization=organization,
        )

        csv_data = [
            {
                "Email": "<EMAIL>",
                "Name": "Existing User",
                "License Type": "advisor",
                "First Name": "Existing",
                "Last Name": "User",
            },
            {
                "Email": "<EMAIL>",
                "Name": "New User",
                "License Type": "advisor",
                "First Name": "New",
                "Last Name": "User",
            },
        ]
        csv_file = self._create_csv_file(csv_data)

        users_created, users_skipped, licenses_created, licenses_skipped, errors = process_users_from_csv(
            organization, csv_file
        )

        assert users_created == 1
        assert users_skipped == 1
        assert len(errors) == 0

    def test_missing_required_fields(self, organization: Organization) -> None:
        csv_data = [
            {
                "Email": "<EMAIL>",
                "Name": "Test User",  # Missing type, first_name, last_name
            }
        ]
        csv_file = self._create_csv_file(csv_data)

        users_created, users_skipped, licenses_created, licenses_skipped, errors = process_users_from_csv(
            organization, csv_file
        )

        assert users_created == 0
        assert users_skipped == 0
        assert any("Missing required fields" in error for error in errors)

    def test_user_missing_required_fields(self, organization: Organization) -> None:
        csv_data = [
            # Valid user to provide header columns.
            {
                "Email": "<EMAIL>",
                "Name": "Test User",
                "First Name": "Test",
                "Last Name": "User",
                "License Type": "advisor",
            },
            # Empty
            {},
            # All fields empty
            {
                "Email": "",
                "Name": "",
                "First Name": "",
                "Last Name": "",
                "License Type": "",
            },
            # Email field exists.
            {
                "Email": "<EMAIL>",
                "Name": "",
                "First Name": "",
                "Last Name": "",
                "License Type": "",
            },
            # Non-email field exists.
            {
                "Email": "",
                "Name": "Invalid user",
                "First Name": "",
                "Last Name": "",
                "License Type": "",
            },
        ]
        csv_file = self._create_csv_file(csv_data)

        users_created, users_skipped, licenses_created, licenses_skipped, errors = process_users_from_csv(
            organization, csv_file
        )

        assert users_created == 2
        assert users_skipped == 0
        assert len(errors) == 1
        assert "no email provided" in errors[0]

    def test_empty_csv(self, organization: Organization) -> None:
        csv_file = self._create_csv_file([])

        users_created, users_skipped, licenses_created, licenses_skipped, errors = process_users_from_csv(
            organization, csv_file
        )

        assert users_created == 0
        assert users_skipped == 0
        assert any("CSV file is empty or malformed" in error for error in errors)

    def test_missing_email(self, organization: Organization) -> None:
        csv_data = [
            {"Email": "", "Name": "Test User", "License Type": "advisor", "First Name": "Test", "Last Name": "User"}
        ]
        csv_file = self._create_csv_file(csv_data)

        users_created, users_skipped, licenses_created, licenses_skipped, errors = process_users_from_csv(
            organization, csv_file
        )

        assert users_created == 0
        assert users_skipped == 0
        assert any("no email provided" in error for error in errors)

    def test_metadata_context(self, organization: Organization) -> None:
        csv_data = [
            {
                "Email": "<EMAIL>",
                "Name": "Test User",
                "License Type": "advisor",
                "First Name": "Test",
                "Last Name": "User",
            }
        ]
        csv_file = self._create_csv_file(csv_data)

        users_created, users_skipped, licenses_created, licenses_skipped, errors = process_users_from_csv(
            organization, csv_file, {"extra": "context", "recall_calendar_id": "123"}
        )

        assert users_created == 1
        assert len(errors) == 0

        user = User.objects.get(email="<EMAIL>")
        expected_context = "%s is a wealth management firm. Test User is a advisor at %s." % (
            organization.name,
            organization.name,
        )
        assert user.user_context == expected_context
        assert user.biasing_words == ["Zeplyn"]
        assert user.recall_calendar_id == "123"
        assert "extra" not in user.metadata

    def test_file_read_error(self, organization: Organization) -> None:
        with patch("django.core.files.uploadedfile.SimpleUploadedFile.read") as mock_read:
            mock_read.side_effect = UnicodeDecodeError("utf-8", b"invalid", 0, 1, "invalid byte")
            csv_file = SimpleUploadedFile("test.csv", b"invalid content", content_type="text/csv")

            users_created, users_skipped, licenses_created, licenses_skipped, errors = process_users_from_csv(
                organization, csv_file
            )

            assert users_created == 0
            assert users_skipped == 0
            assert any("Error reading CSV file" in error for error in errors)

    def test_invalid_csv_format(self, organization: Organization) -> None:
        csv_file = SimpleUploadedFile("test.csv", b"invalid,csv,format\nno,proper,headers", content_type="text/csv")

        users_created, users_skipped, licenses_created, licenses_skipped, errors = process_users_from_csv(
            organization, csv_file
        )

        assert users_created == 0
        assert users_skipped == 0
        assert any("Missing required fields" in error for error in errors)

    def test_csv_string_content(self, organization: Organization) -> None:
        csv_content = "Email,Name,License Type,First Name,Last Name\<EMAIL>,Test User,staff,Test,User\n"

        users_created, users_skipped, licenses_created, licenses_skipped, errors = process_users_from_csv(
            organization, csv_content
        )

        assert users_created == 1
        assert users_skipped == 0
        assert licenses_created == 0
        assert licenses_skipped == 0
        assert not errors

        user = User.objects.get(email="<EMAIL>")
        assert user.name == "Test User"
        assert user.role == User.LicenseType.staff
        assert user.organization == organization


@patch("deepinsights.meetingsapp.admin_utils.delete_organization_data_task")
def test_delete_organization_data_success(
    mock_task: Mock, mock_admin: Mock, mock_request: Mock, organization: Organization
) -> None:
    queryset = Organization.objects.filter(pk=organization.pk)

    delete_organization_and_associated_data(mock_admin, mock_request, queryset)

    mock_task.delay.assert_called_once_with(str(organization.id))
    mock_admin.message_user.assert_called_once_with(
        mock_request,
        f"Organization deletion process started for {organization.name}. You will receive an email confirmation when complete.",
        level=messages.SUCCESS,
    )


def test_delete_organization_data_multiple_orgs(mock_admin: Mock, mock_request: Mock) -> None:
    org1 = Organization.objects.create(name="Org 1")
    org2 = Organization.objects.create(name="Org 2")
    queryset = Organization.objects.filter(pk__in=[org1.pk, org2.pk])

    delete_organization_and_associated_data(mock_admin, mock_request, queryset)

    mock_admin.message_user.assert_called_once_with(
        mock_request, "Please select exactly one organization for this action.", level=messages.ERROR
    )


def test_delete_organization_data_no_org(mock_admin: Mock, mock_request: Mock) -> None:
    queryset = Organization.objects.none()

    delete_organization_and_associated_data(mock_admin, mock_request, queryset)

    mock_admin.message_user.assert_called_once_with(mock_request, "No organization selected.", level=messages.ERROR)


@patch("deepinsights.meetingsapp.admin_utils.delete_organization_data_task")
def test_delete_organization_data_task_error(
    mock_task: Mock, mock_admin: Mock, mock_request: Mock, organization: Organization
) -> None:
    queryset = Organization.objects.filter(pk=organization.pk)
    mock_task.delay.side_effect = Exception("Test error")

    delete_organization_and_associated_data(mock_admin, mock_request, queryset)

    mock_admin.message_user.assert_called_once_with(
        mock_request, "Error scheduling organization deletion: Test error", level=messages.ERROR
    )


def test_disable_organization_data_multiple_orgs(
    mock_admin: Mock,
    mock_request: Mock,
) -> None:
    org1 = Organization.objects.create(name="Org 1")
    org2 = Organization.objects.create(name="Org 2")
    queryset = Organization.objects.filter(pk__in=[org1.pk, org2.pk])

    disable_organization(mock_admin, mock_request, queryset)

    mock_admin.message_user.assert_called_once_with(
        mock_request, "Please select exactly one organization for this action.", messages.ERROR
    )


def test_disable_organization_data_no_org(
    mock_admin: Mock,
    mock_request: Mock,
) -> None:
    queryset = Organization.objects.none()
    disable_organization(mock_admin, mock_request, queryset)
    mock_admin.message_user.assert_called_once_with(mock_request, "No organization selected.", messages.ERROR)


@patch("deepinsights.meetingsapp.admin_utils.RecallBotController")
def test_successful_disable_organization(
    mock_recall_controller: Mock, mock_admin: Mock, mock_request: Mock, organization_with_users: Organization
) -> None:
    mock_recall_controller.return_value.unlink_calendar.return_value = True
    assert User.objects.filter(metadata__has_key="recall_calendar_id").count() == 2
    assert User.objects.filter(metadata__has_key="recall_calendar_platform").count() == 2

    queryset = Organization.objects.filter(pk=organization_with_users.pk)
    disable_organization(mock_admin, mock_request, queryset)

    assert mock_recall_controller.return_value.unlink_calendar.call_count == 2

    assert User.objects.filter(organization=organization_with_users, status=User.STATUS_CHOICES.expired).count() == 3
    assert User.objects.filter(metadata__has_key="recall_calendar_id").count() == 0
    assert User.objects.filter(metadata__has_key="recall_calendar_platform").count() == 0

    organization_with_users.refresh_from_db()
    assert "Expired on" in organization_with_users.description
    assert "Admin User" in organization_with_users.description

    mock_admin.message_user.assert_called_once_with(
        mock_request,
        "Successfully disabled organization. Processed 3 users and successfully unlinked 2 calendars.",
        messages.SUCCESS,
    )


@patch("deepinsights.meetingsapp.admin_utils.RecallBotController")
def test_anonymous_admin_disable_organization(
    mock_recall_controller: Mock, mock_admin: Mock, mock_request: Mock, organization_with_users: Organization
) -> None:
    mock_request.user = AnonymousUser()
    mock_recall_controller.return_value.unlink_calendar.return_value = True

    queryset = Organization.objects.filter(pk=organization_with_users.pk)
    disable_organization(mock_admin, mock_request, queryset)

    organization_with_users.refresh_from_db()
    assert "Anonymous User" in organization_with_users.description


@patch("deepinsights.meetingsapp.admin_utils.RecallBotController")
def test_calendar_unlink_failure(
    mock_recall_controller: Mock, mock_admin: Mock, mock_request: Mock, organization_with_users: Organization
) -> None:
    mock_recall_controller.return_value.unlink_calendar.return_value = False

    queryset = Organization.objects.filter(pk=organization_with_users.pk)
    disable_organization(mock_admin, mock_request, queryset)

    assert User.objects.filter(organization=organization_with_users, status=User.STATUS_CHOICES.expired).count() == 3

    actual_message = mock_admin.message_user.call_args[0][1]
    actual_emails = actual_message.split("required): ")[1]  # Get the email portion

    expected_message = (
        "Successfully disabled organization. Processed 3 users and successfully unlinked 0 calendars.\n"
        f"WARNING: Failed to unlink calendars for the following users (manual unlinking may be required): {actual_emails}"
    )

    mock_admin.message_user.assert_called_once_with(mock_request, expected_message, messages.WARNING)


@patch("deepinsights.meetingsapp.admin_utils.RecallBotController")
@patch("deepinsights.users.models.user.User.save")
def test_user_save_error(
    mock_save: Mock,
    mock_recall_controller: Mock,
    mock_admin: Mock,
    mock_request: Mock,
    organization_with_users: Organization,
) -> None:
    mock_save.side_effect = Exception("Database error")
    mock_recall_controller.return_value.unlink_calendar.return_value = True

    queryset = Organization.objects.filter(pk=organization_with_users.pk)
    disable_organization(mock_admin, mock_request, queryset)

    assert mock_admin.message_user.call_count == 4  # 3 error messages + 1 success message

    mock_admin.message_user.assert_any_call(
        mock_request, "Error <NAME_EMAIL>: Database error", messages.ERROR
    )
    mock_admin.message_user.assert_any_call(
        mock_request, "Error <NAME_EMAIL>: Database error", messages.ERROR
    )


@patch("deepinsights.meetingsapp.models.organization.Organization.save")
def test_organization_save_error(
    mock_save: Mock, mock_admin: Mock, mock_request: Mock, organization_with_users: Organization
) -> None:
    mock_save.side_effect = Exception("Database error")

    queryset = Organization.objects.filter(pk=organization_with_users.pk)
    disable_organization(mock_admin, mock_request, queryset)

    mock_admin.message_user.assert_called_once_with(
        mock_request, "Error updating organization description: Database error", messages.ERROR
    )


def test_user_without_full_name_or_email(mock_admin: Mock, mock_request: Mock, organization: Organization) -> None:
    request_user = Mock()
    request_user.get_full_name.return_value = ""
    request_user.email = ""
    mock_request.user = request_user

    User.objects.create(
        email="<EMAIL>", username="<EMAIL>", name="Test User", organization=organization
    )

    queryset = Organization.objects.filter(pk=organization.pk)
    disable_organization(mock_admin, mock_request, queryset)

    organization.refresh_from_db()
    assert "Unknown User" in organization.description


class TestCalendarExportForm:
    def test_clean_date_range_valid(self) -> None:
        form = CalendarExportForm(
            data={
                "start_date": "01-01-2023",
                "end_date": "12-31-2023",
            }
        )
        assert form.is_valid()

    def test_clean_start_date_invalid(self) -> None:
        """Test that an invalid start date fails validation."""
        form = CalendarExportForm(data={"start_date": "2023-01-01"})  # Wrong format
        assert not form.is_valid()
        assert "start_date" in form.errors
        assert "must be in mm-dd-yyyy format" in form.errors["start_date"][0]

    def test_clean_end_date_valid(self) -> None:
        """Test that a valid end date passes validation."""
        form = CalendarExportForm(data={"end_date": "12-31-2023"})
        form.is_valid()  # Run validation
        assert form.cleaned_data["end_date"] == "12-31-2023"

    def test_clean_end_date_invalid(self) -> None:
        """Test that an invalid end date fails validation."""
        form = CalendarExportForm(data={"end_date": "invalid-date"})
        assert not form.is_valid()
        assert "end_date" in form.errors
        assert "must be in mm-dd-yyyy format" in form.errors["end_date"][0]

    def test_clean_date_range_invalid(self) -> None:
        """Test that an invalid date range fails validation."""
        form = CalendarExportForm(data={"start_date": "12-31-2023", "end_date": "01-01-2023"})
        assert not form.is_valid()
        assert "Start date cannot be after end date" in form.non_field_errors()[0]

    def test_empty_dates_valid(self) -> None:
        """Test that empty dates pass validation."""
        form = CalendarExportForm(data={"_selected_action": "1"})
        assert form.is_valid()

    def test_org_ids_validation(self) -> None:
        """Test that org_ids passes validation."""
        form = CalendarExportForm(data={"org_ids": "1,2,3"})
        assert form.is_valid()
        assert form.cleaned_data["org_ids"] == "1,2,3"

    def test_user_ids_validation(self) -> None:
        """Test that user_ids passes validation."""
        form = CalendarExportForm(data={"user_ids": "1,2,3"})
        assert form.is_valid()
        assert form.cleaned_data["user_ids"] == "1,2,3"


class TestCustomPeriodicTaskAdmin:
    def test_get_urls(self, calendar_admin: CustomPeriodicTaskAdmin) -> None:
        urls = calendar_admin.get_urls()  # type: ignore[no-untyped-call]

        custom_url = None
        for url in urls:
            if hasattr(url, "name") and url.name == "run-calendar-export":
                custom_url = url
                break

        assert custom_url is not None, "Custom URL 'run-calendar-export' not found"

    @patch("deepinsights.meetingsapp.admin_utils.render")
    def test_run_calendar_export_view_get(
        self, mock_render: Mock, calendar_admin: CustomPeriodicTaskAdmin, admin_request_factory: RequestFactory
    ) -> None:
        request = admin_request_factory.get("/admin/django_celery_beat/periodictask/run-calendar-export/")
        request.GET = {"ids": "1,2,3"}  # type: ignore[assignment]

        with patch.object(calendar_admin, "message_user"):
            with patch("deepinsights.meetingsapp.admin_utils.PeriodicTask.objects.filter") as mock_filter:
                mock_filter.return_value.exclude.return_value.exists.return_value = False
                calendar_admin.run_calendar_export_view(request)

                mock_render.assert_called_once()
                context = mock_render.call_args[0][2]
                assert "form" in context
                assert isinstance(context["form"], CalendarExportForm)
                assert context["title"] == "Run Calendar Export"
                assert [q["pk"] for q in context["queryset"]] == ["1", "2", "3"]

    @patch("deepinsights.meetingsapp.admin_utils.redirect")
    def test_run_calendar_export_view_get_invalid_tasks(
        self, mock_redirect: Mock, calendar_admin: CustomPeriodicTaskAdmin, admin_request_factory: RequestFactory
    ) -> None:
        request = admin_request_factory.get("/admin/django_celery_beat/periodictask/run-calendar-export/")
        request.GET = {"ids": "1,2,3"}  # type: ignore[assignment]

        with patch.object(calendar_admin, "message_user") as mock_message_user:
            with patch("deepinsights.meetingsapp.admin_utils.PeriodicTask.objects.filter") as mock_filter:
                mock_filter.return_value.exclude.return_value.exists.return_value = True
                calendar_admin.run_calendar_export_view(request)

                mock_message_user.assert_called_once_with(
                    request,
                    "Error: Can only run calendar export on export_monthly_calendar_events tasks",
                    level=messages.ERROR,
                )
                mock_redirect.assert_called_once_with("admin:django_celery_beat_periodictask_changelist")

    @patch("deepinsights.meetingsapp.admin_utils.export_monthly_calendar_events")
    @patch("deepinsights.meetingsapp.admin_utils.redirect")
    def test_run_calendar_export_view_post_valid(
        self,
        mock_redirect: Mock,
        mock_export_task: Mock,
        calendar_admin: CustomPeriodicTaskAdmin,
        admin_request_factory: RequestFactory,
    ) -> None:
        request = admin_request_factory.post(
            "/admin/django_celery_beat/periodictask/run-calendar-export/",
            {
                "org_ids": "1,2,3",
                "user_ids": "4,5,6",
                "start_date": "01-01-2023",
                "end_date": "12-31-2023",
            },
        )

        mock_task_result = Mock()
        mock_task_result.id = "task-123"
        mock_export_task.delay.return_value = mock_task_result

        with patch.object(calendar_admin, "message_user") as mock_message_user:
            result = calendar_admin.run_calendar_export_view(request)

            mock_export_task.delay.assert_called_once_with(
                org_ids=["1", "2", "3"], user_ids=["4", "5", "6"], start_date="01-01-2023", end_date="12-31-2023"
            )

            mock_message_user.assert_called_once()
            assert "Calendar export task started with task ID: task-123" in mock_message_user.call_args[0][1]
            mock_redirect.assert_called_once_with("admin:django_celery_beat_periodictask_changelist")

    @patch("deepinsights.meetingsapp.admin_utils.render")
    def test_run_calendar_export_view_post_invalid(
        self, mock_render: Mock, calendar_admin: CustomPeriodicTaskAdmin, admin_request_factory: RequestFactory
    ) -> None:
        request = admin_request_factory.post(
            "/admin/django_celery_beat/periodictask/run-calendar-export/",
            {
                "start_date": "invalid-date",  # Invalid date format
            },
        )

        calendar_admin.run_calendar_export_view(request)

        mock_render.assert_called_once()
        context = mock_render.call_args[0][2]
        assert "form" in context
        assert not context["form"].is_valid()

    @patch("deepinsights.meetingsapp.admin_utils.redirect")
    @patch("deepinsights.meetingsapp.admin_utils.reverse")
    @patch("deepinsights.meetingsapp.admin_utils.urlencode")
    def test_run_calendar_export_action(
        self,
        mock_urlencode: Mock,
        mock_reverse: Mock,
        mock_redirect: Mock,
        calendar_admin: CustomPeriodicTaskAdmin,
        admin_request_factory: RequestFactory,
    ) -> None:
        request = admin_request_factory.post("/admin/django_celery_beat/periodictask/")
        request.POST = QueryDict("", mutable=True)  # type: ignore[assignment]
        request.POST.setlist("_selected_action", ["1", "2", "3"])  # type: ignore[misc]

        queryset = Mock()

        mock_reverse.return_value = "/admin/django_celery_beat/periodictask/run-calendar-export/"
        mock_urlencode.return_value = "ids=1,2,3"

        result = calendar_admin.run_calendar_export(request, queryset)

        mock_urlencode.assert_called_once_with({"ids": ",".join(["1", "2", "3"])})

        expected_url = "/admin/django_celery_beat/periodictask/run-calendar-export/?ids=1,2,3"
        mock_redirect.assert_called_once_with(expected_url)

    def test_get_actions(self, calendar_admin: CustomPeriodicTaskAdmin, admin_request_factory: RequestFactory) -> None:
        request = admin_request_factory.get("/admin/django_celery_beat/periodictask/")

        request.user = Mock()
        request.user.has_perm.return_value = True  # Give permission to view all actions

        actions = calendar_admin.get_actions(request)

        assert "run_calendar_export" in actions
        assert len(actions["run_calendar_export"]) == 3
        assert actions["run_calendar_export"][2] == "Run calendar export"


class TestTemplateQueries(TestCase):
    def setUp(self) -> None:
        # Create test organization
        self.org = Organization.objects.create(name="Test Org")

        # Create test user
        self.user = User.objects.create(email="<EMAIL>", username="testuser", organization=self.org)

        # Create meeting types
        self.global_meeting_type = MeetingType.objects.create(name="Global Meeting", everyone=True)
        self.org_meeting_type = MeetingType.objects.create(name="Org Meeting", everyone=False)
        self.org_meeting_type.organizations.add(self.org)

        # Create follow-up templates
        self.global_followup_template = StructuredMeetingDataTemplate.objects.create(
            title="Global Follow-up Template", internal_name="global_followup_template", kind="follow_up"
        )
        self.org_followup_template = StructuredMeetingDataTemplate.objects.create(
            title="Org Follow-up Template", internal_name="org_followup_template", kind="follow_up"
        )

        # Create agenda templates
        self.global_agenda_template = StructuredMeetingDataTemplate.objects.create(
            title="Global Agenda Template", internal_name="global_agenda_template", kind="agenda"
        )
        self.org_agenda_template = StructuredMeetingDataTemplate.objects.create(
            title="Org Agenda Template", internal_name="org_agenda_template", kind="agenda"
        )

        # Create template rules for follow-up templates
        self.global_rule = StructuredMeetingDataTemplateRule.objects.create(everyone=True)
        self.global_rule.follow_up_templates.add(self.global_followup_template)
        self.global_rule.meeting_types.add(self.global_meeting_type)

        self.org_rule = StructuredMeetingDataTemplateRule.objects.create(everyone=False)
        self.org_rule.follow_up_templates.add(self.org_followup_template)
        self.org_rule.organizations.add(self.org)
        self.org_rule.meeting_types.add(self.org_meeting_type)

        # Add agenda templates to meeting types
        self.global_meeting_type.agenda_templates.add(self.global_agenda_template)
        self.org_meeting_type.agenda_templates.add(self.org_agenda_template)

    def test_get_org_active_templates_html(self) -> None:
        # Test with follow-up templates - should see both global and org-specific templates
        html = get_org_active_templates_html(self.org)
        self.assertIn("Global Follow-up Template", str(html))
        self.assertIn("Org Follow-up Template", str(html))
        self.assertIn("Global", str(html))  # Should show "Global" access level
        self.assertIn("Organization", str(html))  # Should show "Organization" access level

        # Test with empty org - should still see global template
        empty_org = Organization.objects.create(name="Empty Org")
        html = get_org_active_templates_html(empty_org)
        self.assertIn("Global Follow-up Template", str(html))
        self.assertIn("Global", str(html))  # Should show "Global" access level
        self.assertNotIn("Org Follow-up Template", str(html))

        # Test with unsaved organization (pk is None) - should only see global templates
        unsaved_org = Organization(name="Unsaved Org")
        html = get_org_active_templates_html(unsaved_org)
        self.assertIn("Global Follow-up Template", str(html))
        self.assertIn("Global", str(html))  # Should show "Global" access level
        self.assertNotIn("Org Follow-up Template", str(html))
        self.assertNotIn("Organization", str(html))  # Should not show org access level for unsaved org

    def test_get_org_active_agendas_html(self) -> None:
        # Test with agenda templates
        html = get_org_active_agendas_html(self.org)
        self.assertIn("Global Agenda Template", str(html))
        self.assertIn("Org Agenda Template", str(html))
        self.assertIn("Global", str(html))  # Should show "Global" access level
        self.assertIn("Organization", str(html))  # Should show "Organization" access level
        self.assertIn("Global Meeting", str(html))  # Should show meeting type name

        # Test with empty org - should still see global template
        empty_org = Organization.objects.create(name="Empty Org")
        html = get_org_active_agendas_html(empty_org)
        self.assertIn("Global Agenda Template", str(html))
        self.assertIn("Global", str(html))  # Should show "Global" access level
        self.assertNotIn("Org Agenda Template", str(html))

    def test_get_user_active_templates_html(self) -> None:
        # Test various levels of template rule visibility

        # 1. First test with user specifically added to rule
        self.org_rule.users.add(self.user)

        # Test with follow-up templates - should see both global and organization-specific templates
        html = get_user_active_templates_html(self.user)
        self.assertIn("Global Follow-up Template", str(html))
        self.assertIn("Org Follow-up Template", str(html))
        self.assertIn("Global", str(html))  # Should show "Global" access level
        self.assertIn("Organization", str(html))  # Should show "Organization" access level

        # 2. Test org-level access by creating a rule for the organization
        # Create new org-specific rule and template
        org_level_rule = StructuredMeetingDataTemplateRule.objects.create(everyone=False)
        org_level_template = StructuredMeetingDataTemplate.objects.create(
            title="Org Level Template", internal_name="org_level_template", kind="follow_up"
        )
        org_level_rule.follow_up_templates.add(org_level_template)
        org_level_rule.organizations.add(self.org)

        # Create a user with org access but not specifically added to any rule
        org_user = User.objects.create(email="<EMAIL>", username="org_user", organization=self.org)
        html = get_user_active_templates_html(org_user)
        self.assertIn("Global Follow-up Template", str(html))  # Should see global template
        self.assertIn("Org Level Template", str(html))  # Should see org-level template
        self.assertIn("Organization", str(html))  # Should see "Organization" access level

        # 3. Test with user without organization - should only see global template
        empty_user = User.objects.create(email="<EMAIL>", username="emptyuser")
        html = get_user_active_templates_html(empty_user)
        self.assertIn("Global Follow-up Template", str(html))
        self.assertIn("Global", str(html))  # Should show "Global" access level
        self.assertNotIn("Org Follow-up Template", str(html))
        self.assertNotIn("Org Level Template", str(html))

    def test_get_user_active_agendas_html(self) -> None:
        # Test various levels of access

        # 1. First test with user having direct access to meeting type through org
        # We'll verify organization access first
        html = get_user_active_agendas_html(self.user)
        self.assertIn("Global Agenda Template", str(html))
        self.assertIn("Org Agenda Template", str(html))
        self.assertIn("Global", str(html))  # Should show "Global" access level
        self.assertIn("Organization", str(html))  # Should show "Organization" access level
        self.assertIn("Global Meeting", str(html))  # Should show meeting type name
        self.assertIn("Org Meeting", str(html))  # Should show meeting type name

        # 2. Test with a user who only has org-level access but no direct template access
        org_only_user = User.objects.create(email="<EMAIL>", username="org_user", organization=self.org)
        # Don't add to meeting type directly, but user should still see templates through org

        # Create a new meeting type that's specifically for org but not directly for user
        org_level_meeting_type = MeetingType.objects.create(name="Org Level Meeting", everyone=False)
        org_level_meeting_type.organizations.add(self.org)

        # Create a template for this meeting type
        org_level_template = StructuredMeetingDataTemplate.objects.create(
            title="Org Level Template", internal_name="org_level_template", kind="agenda"
        )
        org_level_meeting_type.agenda_templates.add(org_level_template)

        html = get_user_active_agendas_html(org_only_user)
        self.assertIn("Global Agenda Template", str(html))  # Global still visible
        self.assertIn("Global Meeting", str(html))
        self.assertIn("Organization", str(html))  # Should show "Organization" access level
        self.assertIn("Org Level Template", str(html))  # Should see org-level template

        # 3. Test with empty user (no org, no direct access) - should still see global template
        empty_user = User.objects.create(email="<EMAIL>", username="emptyuser")
        html = get_user_active_agendas_html(empty_user)
        self.assertIn("Global Agenda Template", str(html))
        self.assertIn("Global", str(html))  # Should show "Global" access level
        self.assertNotIn("Org Agenda Template", str(html))
        self.assertNotIn("Organization", str(html))  # Should not show org access level without org

    def test_get_associated_templates(self) -> None:
        # Create a note with meeting type
        note = Note.objects.create(meeting_type=self.global_meeting_type, note_owner=self.user)

        # Test with follow-up templates
        templates = get_associated_templates(note)
        self.assertEqual(len(templates), 1)
        self.assertEqual(templates[0], self.global_followup_template)

        # Test with org-specific meeting type
        self.org_rule.users.add(self.user)  # Add user to rule to make it visible
        note_with_org_meeting = Note.objects.create(meeting_type=self.org_meeting_type, note_owner=self.user)
        templates = get_associated_templates(note_with_org_meeting)
        self.assertEqual(len(templates), 1)
        self.assertEqual(templates[0], self.org_followup_template)

        # Test with meeting type but no associated templates
        meeting_type_without_template = MeetingType.objects.create(name="No Template Meeting", everyone=True)
        note_with_meeting_type = Note.objects.create(meeting_type=meeting_type_without_template, note_owner=self.user)
        templates = get_associated_templates(note_with_meeting_type)
        self.assertEqual(len(templates), 0)

        # Test with note without meeting type
        note_without_meeting = Note.objects.create(note_owner=self.user)
        templates = get_associated_templates(note_without_meeting)
        self.assertEqual(len(templates), 0)

    def test_get_note_followup_templates_html(self) -> None:
        # Create a note with meeting type
        note = Note.objects.create(meeting_type=self.global_meeting_type, note_owner=self.user)

        # Test with follow-up templates
        html = get_note_followup_templates_html(note)
        self.assertIn("Global Follow-up Template", str(html))
        self.assertNotIn("Global Agenda Template", str(html))

        # Test with org-specific meeting type
        self.org_rule.users.add(self.user)  # Add user to rule to make it visible
        note_with_org_meeting = Note.objects.create(meeting_type=self.org_meeting_type, note_owner=self.user)
        html = get_note_followup_templates_html(note_with_org_meeting)
        self.assertIn("Org Follow-up Template", str(html))

        # Test with meeting type but no follow-up template
        meeting_type_without_template = MeetingType.objects.create(name="No Template Meeting", everyone=True)
        note_with_meeting_type = Note.objects.create(meeting_type=meeting_type_without_template, note_owner=self.user)
        html = get_note_followup_templates_html(note_with_meeting_type)
        self.assertIn("No templates found for this note", str(html))

        # Test with note without meeting type
        note_without_meeting = Note.objects.create(note_owner=self.user)
        html = get_note_followup_templates_html(note_without_meeting)
        self.assertIn("No templates found for this note", str(html))

    def test_get_note_agenda_template_html(self) -> None:
        # Create a note with global meeting type
        note = Note.objects.create(meeting_type=self.global_meeting_type, note_owner=self.user)

        # Test with agenda templates
        html = get_note_agenda_template_html(note)
        self.assertIn("Global Agenda Template", str(html))
        self.assertNotIn("Global Follow-up Template", str(html))

        # Test with org-specific meeting type
        note_with_org_meeting = Note.objects.create(meeting_type=self.org_meeting_type, note_owner=self.user)
        html = get_note_agenda_template_html(note_with_org_meeting)
        self.assertIn("Org Agenda Template", str(html))
        self.assertNotIn("Global Agenda Template", str(html))

        # Test with meeting type that has no agenda template
        meeting_type_without_template = MeetingType.objects.create(name="No Template Meeting", everyone=True)
        note_with_meeting_type = Note.objects.create(meeting_type=meeting_type_without_template, note_owner=self.user)
        html = get_note_agenda_template_html(note_with_meeting_type)
        self.assertIn("No", str(html))
        self.assertIn("agenda templates available", str(html))

        # Test with note without meeting type
        note_without_meeting = Note.objects.create(note_owner=self.user)
        html = get_note_agenda_template_html(note_without_meeting)
        self.assertIn("No meeting type specified", str(html))

    def tearDown(self) -> None:
        # Clean up all created objects
        Note.objects.all().delete()
        StructuredMeetingDataTemplateRule.objects.all().delete()
        StructuredMeetingDataTemplate.objects.all().delete()
        MeetingType.objects.all().delete()
        User.objects.all().delete()
        Organization.objects.all().delete()
