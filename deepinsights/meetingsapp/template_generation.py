from typing import Any

from django import forms
from django.contrib import messages
from django.contrib.admin.views.decorators import staff_member_required
from django.core.exceptions import ValidationError
from django.http import HttpResponse, HttpResponseRedirect
from django.urls import reverse
from django.utils.decorators import method_decorator
from django.views.generic import FormView

from deepinsights.meetingsapp.models.prompt import Prompt
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingDataTemplate
from deepinsights.meetingsapp.models.structured_meeting_data_schema import StructuredMeetingDataSchema
from deepinsights.meetingsapp.tasks import generate_template_data


class TemplateGeneratorForm(forms.Form):
    """Form for generating structured meeting data templates from questions."""

    # The type choices indicate the output format and the prompt used to generate it.
    TEMPLATE_TYPE_CHOICES = [
        ("summary", "Summary"),
        ("checklist", "Checklist"),
        ("mixed", "Mixed"),
        ("agenda", "Agenda"),
    ]

    template_type = forms.ChoiceField(
        choices=TEMPLATE_TYPE_CHOICES,
        required=True,
        help_text="Select the type of template to generate.",
    )

    name = forms.CharField(
        max_length=200,
        help_text="The user-visible title/name of this type of meeting.",
    )

    internal_name = forms.CharField(
        max_length=200,
        help_text="An internally-visible name used in the admin console to identify this type of meeting.",
    )

    questions_file = forms.FileField(required=False, help_text="Upload a file containing your questions.")

    questions_text = forms.CharField(
        widget=forms.Textarea(attrs={"rows": 10}), required=False, help_text="Or enter your questions directly here."
    )

    # Advanced Configuration
    custom_prompt = forms.ModelChoiceField(
        queryset=Prompt.objects.all(),
        required=False,
        help_text="Advanced: Select a custom prompt to override the default prompt for this template type. This will overwrite the template selection.",
    )

    def clean(self) -> dict[str, Any]:
        cleaned_data = super().clean()
        if cleaned_data is None:  # First handle the None case
            raise ValidationError("Form cleaning failed")
        if not cleaned_data.get("questions_text") and not cleaned_data.get("questions_file"):
            raise ValidationError("Please either upload a file or enter questions directly.")
        return cleaned_data


@method_decorator(staff_member_required, name="dispatch")
class TemplateGeneratorView(FormView):  # type: ignore[type-arg]
    template_name = "admin/meetingsapp/structuredmeetingdatatemplate/generate_template.html"
    form_class = TemplateGeneratorForm

    def get_prompt_for_template_type(self, template_type: str) -> Prompt:
        """Get the appropriate prompt based on template type."""
        prompt_mapping = {
            "summary": "presonalized_summary_prompt_v1",
            "checklist": "generic_template_prompt_v2",
            "mixed": "generic_template_prompt_v2",
            "agenda": "meeting_agenda_generator",
        }
        if template_type not in prompt_mapping:
            raise ValidationError(f"Invalid template type: {template_type}")
        try:
            return Prompt.objects.get(unique_name=prompt_mapping[template_type])
        except Prompt.DoesNotExist:
            raise ValidationError(f"Prompt not found for template type: {template_type}")

    def form_valid(self, form: TemplateGeneratorForm) -> HttpResponse:
        try:
            template_type = form.cleaned_data["template_type"]
            questions_text = form.cleaned_data["questions_text"]
            name = form.cleaned_data["name"]
            internal_name = form.cleaned_data["internal_name"]
            custom_prompt = form.cleaned_data.get("custom_prompt")
            if template_type == "summary":
                kind = "personalized_summary"
            else:
                kind = internal_name.lower().replace(" ", "_")

            if template_type == "agenda":
                try:
                    schema_unstructured = StructuredMeetingDataSchema.objects.get(name="unstructured_text")
                    prompt = custom_prompt if custom_prompt else self.get_prompt_for_template_type(template_type)
                    StructuredMeetingDataTemplate.objects.create(
                        title=name,
                        internal_name=internal_name,
                        kind="agenda",
                        schema_definition=schema_unstructured,
                        initial_data={
                            "content": questions_text,
                            "format": "markdown",
                        },
                        prompt=prompt,
                    )
                except StructuredMeetingDataSchema.DoesNotExist:
                    raise ValidationError("Required schema not found")
            else:
                try:
                    schema = StructuredMeetingDataSchema.objects.get(name="structured_data_schema")
                    prompt = custom_prompt if custom_prompt else self.get_prompt_for_template_type(template_type)
                    # Call the task asynchronously
                    questions_text = "This is a {} template".format(template_type) + "\n\n" + questions_text
                    generate_template_data.delay(questions_text, schema.id, name, internal_name, kind, prompt.id)
                except StructuredMeetingDataSchema.DoesNotExist:
                    raise ValidationError("Required schema not found")

            # Add success message
            messages.success(
                self.request,
                "Template generation started. This process may take a few minutes. "
                "You can continue using the admin interface while the template is being generated.",
            )
            # Redirect to waiting page
            return HttpResponseRedirect(reverse("admin:meetingsapp_structuredmeetingdatatemplate_changelist"))
        except Exception as e:
            messages.error(self.request, f"Error generating template: {str(e)}")
            return self.form_invalid(form)
