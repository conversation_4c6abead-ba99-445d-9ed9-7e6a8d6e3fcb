import json
import logging
import textwrap
import uuid
from datetime import timedelta

import markdown
import zoneinfo
from django.conf import settings
from django.utils import timezone
from liquid import Template
from pydantic import ValidationError

from api.routers.calendar import CalendarEvent
from api.routers.note import create_or_update_interaction, get_or_create_note, set_note_meeting_metadata
from deepinsights.core.email_service import ZeplynEmailService
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent
from deepinsights.ml_agents.models.agent_config import (
    AgentInput,
    AgentOutputEnumeratedListSubset,
    ListMetadata,
    expect_list_items,
    get_agent_config_and_run_agent,
)
from deepinsights.users.models.user import User


def markdown_content_email(items: list[dict[str, str]]) -> str:
    item_list = [
        f"""## {item['note_title']}

Start time: {item['event_start_time']}

[📝 View on Zeplyn]({item['note_link']})

Agenda:
{item['agenda_data']}"""
        for item in items
    ]
    return "\n" + "\n".join(item_list)


def send_meeting_prep_advance_email() -> None:
    now = timezone.now()
    timezone_format = zoneinfo.ZoneInfo("America/New_York")

    default_template = """# Your upcoming meetings

This email shows data until {{end_date}}

{{agendas}}"""

    template = textwrap.dedent(default_template)
    # Render the template using Liquid.
    renderer = Template(template)
    # Remove all filters; we don't want custom templates to use filters, only simple replacement.
    renderer.env.filters = {}
    # Ignore unknown filters; we don't want the rendering to crash.
    renderer.env.strict_filters = False

    # For users with flag enabled
    users_to_consider = User.objects
    for user in users_to_consider.iterator():
        if not Flags.EnableMeetingPrepAdvanceEmail.is_active_for_user(user):
            logging.debug("Not emailing user upcoming meeting prep, feature not enabled for user %s", user.id)
            continue

        days_in_advance = user.get_preferences().meeting_prep_preferences.days_before_meeting_to_email
        lookahead_time = now + timedelta(days=days_in_advance)

        agenda_contexts = []

        events_to_consider = ScheduledEvent.objects.filter(
            user=user, start_time__gt=now, start_time__lte=lookahead_time
        ).order_by("start_time")
        for event in events_to_consider.iterator():
            if not event.note or not event.note.has_client_interaction():
                continue
            interaction = event.note.clientinteraction
            if interaction and interaction.agenda and interaction.agenda.data:
                content = interaction.agenda.data.get("content")
                if content:
                    agenda_contexts.append(
                        {
                            "agenda_data": content,
                            "note_title": event.note.title(),
                            "event_start_time": event.start_time.astimezone(timezone_format).strftime(
                                "%B %d, %H:%M %Z"
                            ),
                            "note_link": f"{settings.APP_DOMAIN}/notes/create/{event.note.uuid}?noteID={event.note.uuid}&tab=prep",
                        }
                    )

        if agenda_contexts:
            agenda_markdown = markdown_content_email(agenda_contexts)
            pretty_date = lookahead_time.astimezone(timezone_format).strftime("%B %d %Y")
            markdown_for_email = renderer.render(**{"end_date": pretty_date, "agendas": agenda_markdown})
            # Render the Markdown to HTML, ensuring that line breaks are turned into HTML <br>s.
            email_content = markdown.markdown(markdown_for_email, extensions=["nl2br"])

            user_email = str(user.email)
            logging.info("sending meeting prep email to user: %s", user_email)
            subject = f"Upcoming meetings - until {pretty_date}"
            attachments = None

            email_service = ZeplynEmailService()
            email_service.send_email(
                sender="Zeplyn <<EMAIL>>",
                reply_to="<EMAIL>",
                subject=subject,
                recipients=[user_email],
                cc=[],
                bcc=[],
                body=email_content,
                attachments=attachments,
                is_html=True,
            )
        else:
            logging.info("Not sending email for user %s, no upcoming prepped meetings", user.uuid)


def _infer_meeting_type(event: ScheduledEvent) -> str:
    """Asks the LLM which of a user's meeting types to use for a scheduled event.
    Returns the UUID of an existing meeting type that applies to this user,
    or falls back to the user's default meeting type, which may not be set"""
    default_meeting_type_uuid = event.user.get_preferences().default_meeting_type
    potential_meeting_types = [
        json.dumps({"uuid": str(m.uuid), "title": m.name}) for m in MeetingType.for_user(event.user).order_by("name")
    ]

    if not event.user.organization:
        logging.warn(
            "User %s does not have an organization, cannot infer meeting type for event %s with org data",
            event.user.uuid,
            event.uuid,
        )
        return default_meeting_type_uuid

    note_context = f"""The note owner is {event.user.name}, at {event.user.organization.name}.\nThe note metadata is {event.source_data}.\nThe potential meeting types for this note are: {potential_meeting_types}"""
    agent_input = AgentInput(note_context=note_context)

    with expect_list_items(ListMetadata(max_items=1, permitted_items=potential_meeting_types)):
        result = get_agent_config_and_run_agent(
            agent_owner=event.user.organization, agent_name="meetingtype_for_note", agent_input=agent_input
        )

    if not isinstance(result, AgentOutputEnumeratedListSubset):
        logging.error(
            "Agent output was of wrong type - internal misconfiguration of AgentConfig for meetingtype_for_note"
        )
        return default_meeting_type_uuid

    if not result.items:
        logging.info(
            "No item was returned from LLM for meetingtype_for_note event %s note %s",
            event.uuid,
            event.note.uuid if event.note else None,
        )
        return default_meeting_type_uuid

    try:
        # Output validation by confirming the UUID exists in the database and is applicable for that user
        meeting_type_info = json.loads(result.items[0])  # ignore: union-attr
        proposed_uuid = meeting_type_info.get("uuid")
        meeting_type = MeetingType.for_user(event.user).get(uuid=uuid.UUID(proposed_uuid))
        return str(meeting_type.uuid)
    except Exception as e:
        logging.error(
            "Couldn't get meeting type for event %s note %s",
            event.uuid,
            event.note.uuid if event.note else None,
            exc_info=e,
        )
    return default_meeting_type_uuid


def create_meeting_prep_in_advance() -> None:
    now = timezone.now()

    # For users with flag enabled
    users_to_consider = User.objects
    for user in users_to_consider.iterator():
        if not Flags.EnableMeetingPrepAutogeneration.is_active_for_user(user):
            logging.debug("Not autogenerating meeting prep in advance, feature not enabled for user %s", user.id)
            continue

        days_in_advance = user.get_preferences().meeting_prep_preferences.days_before_meeting_to_generate
        lookahead_time = now + timedelta(days=days_in_advance)

        events_to_consider = ScheduledEvent.objects.filter(
            user=user, start_time__gt=now, start_time__lte=lookahead_time
        )

        for event in events_to_consider.iterator():
            if not event.note:
                # first create note for event, then populate interaction for it
                # stripped-down version of what we do in create_or_update_note in API
                note = get_or_create_note(None, user, now)
                event.note = note
                event.save()

            if not event.note.note_type:
                note_type = Note.NOTE_TYPE.voice_memo
                event.note.note_type = note_type
                event.note.save()

            if not event.note.metadata:
                # New note needs to be populated from the CalendarEvent
                try:
                    calendar_event = CalendarEvent.model_validate(event.source_data)
                    set_note_meeting_metadata(
                        note=event.note,
                        scheduled_start_time=calendar_event.start_time,
                        meeting_name=calendar_event.title,
                        meeting_source_id=calendar_event.id,
                        # for fields we don't intend to change, pass in as None
                        client_name=None,
                        client_id=None,
                        meeting_type=None,
                    )
                except ValidationError as ve:
                    logging.warning(
                        "Unable to set note %s metadata from ScheduledEvent %s - invalid event calendar source data",
                        event.note.uuid,
                        event.uuid,
                    )

            if not event.note.meeting_type:
                inferred_meeting_type = _infer_meeting_type(event)

                if inferred_meeting_type:
                    set_note_meeting_metadata(
                        note=event.note,
                        meeting_type=inferred_meeting_type,
                        # for fields we don't intend to change, pass in as None
                        scheduled_start_time=None,
                        meeting_name=None,
                        meeting_source_id=None,
                        client_name=None,
                        client_id=None,
                    )

            if event.note.meeting_type:
                create_or_update_interaction(event.note, user)
            else:
                logging.info(
                    "Note %s does not have a meeting type, and there was no default available, skipping interaction creation",
                    event.note.uuid,
                )
