{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrahead %}
{{ block.super }}
<style type="text/css">
    .form-row {
        padding: 10px;
        border-bottom: 1px solid #eee;
    }

    .help-text {
        color: #666;
        font-size: 13px;
        margin: 5px 0;
    }

    textarea {
        width: 90%;
        max-width: 800px;
    }

    .file-upload {
        margin: 10px 0;
    }

    .file-btn {
        display: inline-block;
        padding: 10px 15px;
        background: var(--button-bg);
        color: var(--button-fg);
        cursor: pointer;
        border-radius: 4px;
    }

    #file-name {
        margin-left: 10px;
        font-style: italic;
    }

    .button-row {
        margin: 10px 0;
        display: flex;
        gap: 10px;
        align-items: center;
    }

    input[type="file"] {
        display: none;
    }

    select {
        width: 200px;
        padding: 5px;
        margin: 5px 0;
    }

    .template-type-info {
        background-color: #f8f8f8;
        padding: 10px;
        margin: 10px 0;
        border-radius: 4px;
        border-left: 4px solid #79aec8;
    }

    .advanced-section {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 2px solid #eee;
    }

    .advanced-section h3 {
        color: #666;
        margin-bottom: 15px;
    }
</style>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const fileInput = document.getElementById('id_questions_file');
        const fileNameSpan = document.getElementById('file-name');
        const textArea = document.getElementById('id_questions_text');
        const templateTypeSelect = document.getElementById('id_template_type');
        const templateTypeInfo = document.getElementById('template-type-info');

        const templateTypeDescriptions = {
            'summary': 'Generates a summary template for meeting notes',
            'checklist': 'Creates a checklist template for tracking tasks',
            'mixed': 'Combines summary and checklist elements',
            'agenda': 'Creates an agenda template for meeting planning'
        };

        function updateTemplateTypeInfo() {
            const selectedType = templateTypeSelect.value;
            templateTypeInfo.textContent = templateTypeDescriptions[selectedType] || '';
        }

        templateTypeSelect.addEventListener('change', updateTemplateTypeInfo);
        updateTemplateTypeInfo();

        fileInput.addEventListener('change', function () {
            const file = this.files[0];
            if (file) {
                fileNameSpan.textContent = file.name;

                // Read file contents into textarea
                const reader = new FileReader();
                reader.onload = function (e) {
                    textArea.value = e.target.result;
                };
                reader.readAsText(file);
            } else {
                fileNameSpan.textContent = 'No file selected';
            }
        });
    });
</script>
{% endblock %}

{% block content %}
<div id="content-main">
    <form method="post" enctype="multipart/form-data">
        {% csrf_token %}

        <fieldset class="module aligned">
            <h2>Generate Template from Questions</h2>

            <div class="form-row">
                <div>
                    <label for="id_template_type">Template Type:</label>
                    {{ form.template_type }}
                    {% if form.template_type.help_text %}
                    <p class="help-text">{{ form.template_type.help_text }}</p>
                    {% endif %}
                    <div id="template-type-info" class="template-type-info"></div>
                    {{ form.template_type.errors }}
                </div>
            </div>

            <div class="form-row">
                <div>
                    <label for="id_name">Name:</label>
                    {{ form.name }}
                    {% if form.name.help_text %}
                    <p class="help-text">{{ form.name.help_text }}</p>
                    {% endif %}
                    {{ form.name.errors }}
                </div>
            </div>

            <div class="form-row">
                <div>
                    <label for="id_internal_name">Internal Name:</label>
                    {{ form.internal_name }}
                    {% if form.internal_name.help_text %}
                    <p class="help-text">{{ form.internal_name.help_text }}</p>
                    {% endif %}
                    {{ form.internal_name.errors }}
                </div>
            </div>

            <div class="form-row">
                <div class="button-row">
                    <label class="file-btn" for="id_questions_file">
                        Choose File
                    </label>
                    {{ form.questions_file }}
                    <span id="file-name">No file selected</span>
                </div>
                {% if form.questions_file.help_text %}
                <p class="help-text">{{ form.questions_file.help_text }}</p>
                {% endif %}
                {{ form.questions_file.errors }}
            </div>

            <div class="form-row">
                <div>
                    <label for="id_questions_text">Questions:</label>
                    {{ form.questions_text }}
                    {% if form.questions_text.help_text %}
                    <p class="help-text">{{ form.questions_text.help_text }}</p>
                    {% endif %}
                    {{ form.questions_text.errors }}
                </div>
            </div>
        </fieldset>

        <fieldset class="module aligned advanced-section">
            <h3>Advanced Configuration</h3>

            <div class="form-row">
                <div>
                    <label for="id_custom_prompt">Custom Prompt:</label>
                    {{ form.custom_prompt }}
                    {% if form.custom_prompt.help_text %}
                    <p class="help-text">{{ form.custom_prompt.help_text }}</p>
                    {% endif %}
                    {{ form.custom_prompt.errors }}
                </div>
            </div>
        </fieldset>

        <div class="submit-row">
            <input type="submit" value="Generate Template" class="default" name="_save">
        </div>
    </form>
</div>
{% endblock %}
