{% extends "admin/base_site.html" %}
{% load i18n admin_urls %}

{% block content %}
<div id="content-main">
    <form method="post" action="">
        {% csrf_token %}
        <input type="hidden" name="action" value="run_calendar_export">
        {% for obj in queryset %}
        <input type="hidden" name="_selected_action" value="{{ obj.pk }}">
        {% endfor %}

        <div>
            <fieldset class="module aligned">
                <h2>{{ title }}</h2>
                <div class="description">
                    <p>This will run the calendar export task with the specified parameters. If no parameters are
                        provided, default values will be used.</p>
                    <p>The export will be processed in the background and the results will be emailed to the configured
                        recipient.</p>
                </div>

                {% for field in form %}
                <div class="form-row {% if field.errors %}errors{% endif %}">
                    <div>
                        {% if field.errors %}
                        {{ field.errors }}
                        {% endif %}
                        {{ field.label_tag }}
                        {{ field }}
                        {% if field.help_text %}
                        <div class="help">{{ field.help_text|safe }}</div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </fieldset>
        </div>

        <div class="submit-row">
            <input type="submit" class="default" value="Run Export Task">
            <a href="{% url 'admin:django_celery_beat_periodictask_changelist' %}" class="button cancel-link">Cancel</a>
        </div>
    </form>
</div>
{% endblock %}
