import json
import logging
from pathlib import Path
from typing import Any, Dict
from uuid import UUID

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction

from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingData
from deepinsights.meetingsapp.models.task import Task

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Export notes and their related data to a single JSON file."

    def add_arguments(self, parser: Any) -> None:
        parser.add_argument(
            "note_uuids",
            nargs="+",
            type=str,
            help="One or more UUIDs of the Notes to export",
        )
        parser.add_argument(
            "--output-file",
            type=str,
            default="exported_notes.json",
            help="Path to save the exported JSON file",
        )

    def handle(self, *args: Any, **options: Any) -> None:
        note_uuids = options["note_uuids"]
        output_file = Path(options["output_file"])

        # Convert string UUIDs to UUID objects
        try:
            note_uuids = [UUID(uuid) for uuid in note_uuids]
        except ValueError as e:
            raise CommandError(f"Invalid UUID format: {e}")

        # Create output directory if it doesn't exist
        output_file.parent.mkdir(parents=True, exist_ok=True)

        # Get the notes
        notes = Note.objects.filter(uuid__in=note_uuids)
        if not notes.exists():
            raise CommandError("No valid notes found to export")
        found_uuids = {str(note.uuid) for note in notes}
        missing_uuids = {str(uuid) for uuid in note_uuids} - found_uuids

        if missing_uuids:
            logger.warning("Notes with UUIDs %s do not exist", ", ".join(missing_uuids))

        # Export data
        with transaction.atomic():
            export_data = {}
            for note in notes:
                try:
                    note_data = self._export_note(note)
                    export_data[str(note.uuid)] = note_data
                except Exception as e:
                    logger.error("Failed to export note %s: %s", note.uuid, str(e))
                    if input("Continue with remaining notes? (y/n): ").lower() != "y":
                        raise CommandError("Export aborted by user")

            # Write all data to a single file
            self._write_json(export_data, output_file)
            logger.info("Successfully exported %d notes to %s", len(export_data), output_file)

    def _export_note(self, note: Note) -> Dict[str, Any]:
        """Export a Note and its related data to a dictionary."""
        note_data = self._serialize_note(note)

        # Export Tasks
        tasks = Task.objects.filter(note=note)
        if tasks.exists():
            note_data["tasks"] = [self._serialize_task(task) for task in tasks]

        # Export Structured Meeting Data
        structured_data = StructuredMeetingData.objects.filter(note=note)
        if structured_data.exists():
            note_data["structured_data"] = [self._serialize_structured_data(data) for data in structured_data]

        return note_data

    def _serialize_note(self, note: Note) -> Dict[str, Any]:
        """Serialize a Note object to a dictionary."""
        return {
            "uuid": str(note.uuid),
            "file_path": note.file_path,
            "file_type": note.file_type,
            "note_type": note.note_type,
            "metadata": note.metadata,
            "status": note.status,
            "data_source": note.data_source,
            "diarized_trans_with_names": note.diarized_trans_with_names,
            "advisor_notes": note.advisor_notes,
            "key_takeaways": note.key_takeaways,
            "summary_by_topics": note.summary_by_topics,
            "summary": note.summary,
            "salesforce_case_id": note.salesforce_case_id,
            "follow_up_email_contents": note.follow_up_email_contents,
            "client": note.client,
            "note_owner": {
                "uuid": str(note.note_owner.uuid),
                "email": note.note_owner.email,
            }
            if note.note_owner
            else None,
            "meeting_type": {
                "uuid": str(note.meeting_type.uuid),
                "key": note.meeting_type.key,
            }
            if note.meeting_type
            else None,
            "created": note.created.isoformat(),
            "modified": note.modified.isoformat(),
        }

    def _serialize_task(self, task: Task) -> Dict[str, Any]:
        """Serialize a Task object to a dictionary."""
        return {
            "uuid": str(task.uuid),
            "task_title": task.task_title,
            "task_desc": task.task_desc,
            "completed": task.completed,
            "due_date": task.due_date.isoformat() if task.due_date else None,
            "metadata": task.metadata,
            "note_uuid": str(task.note.uuid) if task.note else None,
            "task_owner_uuid": str(task.task_owner.uuid) if task.task_owner else None,
            "assignee_uuid": str(task.assignee.uuid) if task.assignee else None,
            "created": task.created.isoformat(),
            "modified": task.modified.isoformat(),
        }

    def _serialize_structured_data(self, data: StructuredMeetingData) -> Dict[str, Any]:
        """Serialize a StructuredMeetingData object to a dictionary."""
        return {
            "uuid": str(data.uuid),
            "title": data.title,
            "internal_name": data.internal_name,
            "kind": data.kind,
            "schema": data.schema,
            "data": data.data,
            "context": data.context,
            "status": data.status,
            "note_uuid": str(data.note.uuid) if data.note else None,
            "created": data.created.isoformat(),
            "modified": data.modified.isoformat(),
        }

    def _write_json(self, data: Any, filepath: Path) -> None:
        """Write data to a JSON file."""
        with open(filepath, "w") as f:
            json.dump(data, f, indent=2)
        logger.info("Wrote data to %s", filepath)
