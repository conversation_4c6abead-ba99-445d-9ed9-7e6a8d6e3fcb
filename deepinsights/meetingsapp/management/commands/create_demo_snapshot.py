import subprocess
from argparse import Argument<PERSON><PERSON><PERSON>
from datetime import datetime, timezone
from pathlib import Path
from typing import Any

from django.core.management import call_command
from django.core.management.base import BaseCommand, CommandError
from django.db import connection
from django.db.migrations.executor import MigrationExecutor

from deepinsights.meetingsapp.management.snapshot_models import SnapshotMetadata
from deepinsights.meetingsapp.models.note import Note


class Command(BaseCommand):
    help = "Create a complete demo snapshot with Git commit tracking and migration state"

    def add_arguments(self, parser: ArgumentParser) -> None:
        parser.add_argument("snapshot_name", type=str, help="Name of the snapshot to create.")
        parser.add_argument(
            "--output-dir",
            type=str,
            default="demo_snapshots",
            help="Directory to store snapshots (default: demo_snapshots)",
        )
        parser.add_argument("--description", type=str, help="Description of the snapshot.")

    def handle(self, *args: Any, **options: Any) -> None:
        snapshot_name = options["snapshot_name"]
        output_dir = Path(options["output_dir"])

        self._check_git_status()

        output_dir.mkdir(exist_ok=True)
        snapshot_path = output_dir / f"{snapshot_name}.json"
        if snapshot_path.exists():
            confirm = input(f"Snapshot '{snapshot_name}' exists. Overwrite? (y/N): ")
            if confirm.lower() != "y":
                raise CommandError("Cancelled.")

        self.stdout.write(f"Creating complete database snapshot '{snapshot_name}'...")

        git_commit = self._get_git_commit()

        zeplyn_apps = ["users", "meetingsapp", "core", "ml_agents"]
        app_migrations = {}

        for app_name in zeplyn_apps:
            last_migration = self._get_last_app_migration(app_name)
            app_migrations[app_name] = last_migration
            if not last_migration:
                self.stdout.write(self.style.WARNING(f"Could not determine last migration for '{app_name}'."))

        with open(snapshot_path, "w") as snapshot_file:
            call_command(
                "dumpdata",
                "--exclude",
                "auth.permission",
                "--exclude",
                "contenttypes",
                "--exclude",
                "sessions.session",
                "--exclude",
                "admin.logentry",
                "-v",
                "2",
                "--traceback",
                stdout=snapshot_file,
                indent=2,
            )

        meta_path = output_dir / f"{snapshot_name}.meta.json"
        metadata = SnapshotMetadata(
            git_commit=git_commit,
            created_at=datetime.now(timezone.utc).isoformat(),
            total_notes=Note.objects.count(),
            app_migrations=app_migrations,
            description=options["description"] or f"Database snapshot '{snapshot_name}'",
        )

        with open(meta_path, "w") as f:
            f.write(metadata.model_dump_json(indent=2))

        self.stdout.write(self.style.SUCCESS(f'✅ Created snapshot "{snapshot_name}"'))
        self.stdout.write(f"   Fixture: {snapshot_path}")
        self.stdout.write(f"   Metadata: {meta_path}")
        self.stdout.write(f"   Git commit: {git_commit[:8]}")
        self.stdout.write("   App migrations:")
        for app_name, migration in app_migrations.items():
            self.stdout.write(f"     {app_name}: {migration or 'Unknown'}")

    def _get_last_app_migration(self, app_name: str) -> str | None:
        """Gets the name of the last applied migration for a specific app."""
        executor = MigrationExecutor(connection)
        try:
            # Get the leaf nodes (the latest migrations) for the app.
            targets = executor.loader.graph.leaf_nodes(app_name)

            # Find which of these latest migrations have been applied to the DB.
            for target in targets:
                if target in executor.recorder.applied_migrations():
                    # Return the name of the first applied leaf migration found
                    return executor.loader.get_migration(*target).name
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error getting migration state: {e}"))
        return None

    def _check_git_status(self) -> None:
        """Check if working tree has uncommitted changes to tracked files."""
        try:
            result = subprocess.run(["git", "status", "--porcelain"], capture_output=True, text=True, check=True)
            # Filter out untracked files (lines starting with "??") before checking
            tracked_changes = [line for line in result.stdout.strip().split("\n") if line and not line.startswith("??")]
            if tracked_changes:
                raise CommandError(
                    "Working tree has uncommitted changes to tracked files. "
                    "Commit or stash changes before restoring snapshot."
                )
        except subprocess.CalledProcessError:
            raise CommandError("Failed to check git status. Ensure you're in a git repository.")

    def _get_git_commit(self) -> str:
        """Get current git commit hash."""
        try:
            result = subprocess.run(["git", "rev-parse", "HEAD"], capture_output=True, text=True, check=True)
            return result.stdout.strip()
        except subprocess.CalledProcessError:
            raise CommandError("Failed to get git commit hash.")
