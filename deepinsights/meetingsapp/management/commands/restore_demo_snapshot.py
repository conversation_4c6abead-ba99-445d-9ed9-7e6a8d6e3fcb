import json
import subprocess
from argparse import Argument<PERSON>ars<PERSON>
from datetime import timed<PERSON><PERSON>
from pathlib import Path
from typing import Any, Dict

from django.core.management import call_command
from django.core.management.base import BaseCommand, CommandError
from django.db import connection
from django.utils import timezone

from deepinsights.meetingsapp.management.snapshot_models import SnapshotMetadata
from deepinsights.meetingsapp.models.note import Note


class Command(BaseCommand):
    help = "Restore a snapshot with schema migration and flush workflow"

    def add_arguments(self, parser: ArgumentParser) -> None:
        parser.add_argument("snapshot_name", type=str, help="Name of the snapshot to restore.")
        parser.add_argument("--placement-config", type=str, help="JSON file specifying note placement")
        parser.add_argument("--input-dir", type=str, default="demo_snapshots", help="Directory containing snapshots")
        parser.add_argument(
            "--redistribute-note-times",
            action="store_true",
            help="Redistribute note times according to placement config.",
        )

    def handle(self, *args: Any, **options: Any) -> None:
        snapshot_name = options["snapshot_name"]
        input_dir = Path(options["input_dir"])
        snapshot_path = input_dir / f"{snapshot_name}.json"
        meta_path = input_dir / f"{snapshot_name}.meta.json"

        if not snapshot_path.exists() or not meta_path.exists():
            raise CommandError(f'Snapshot "{snapshot_name}" or its metadata not found in {input_dir}')

        with open(meta_path, "r") as f:
            metadata = SnapshotMetadata(**json.load(f))

        if not metadata.app_migrations:
            raise CommandError("Snapshot metadata is missing migration information. Please recreate it.")

        self.stdout.write("📋 Snapshot migration state:")
        for app_name, migration in metadata.app_migrations.items():
            self.stdout.write(f"   {app_name}: {migration or 'Unknown'}")

        self.stdout.write(f"Restoring snapshot '{snapshot_name}'...")
        confirm = input(
            "⚠️  This will replace ALL database data! You can create an aws snapshot of the database before running this command."
            "Please refer to https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/USER_CreateSnapshot.html  for instructions on "
            "how to create an aws snapshot. When ready continue press y? (y/N): "
        )
        if confirm.lower() != "y":
            raise CommandError("Cancelled.")

        # Step 1: Roll back each app to its specific snapshot migration
        for app_name, migration_name in metadata.app_migrations.items():
            if migration_name:
                self.stdout.write(f"    Targeting app '{app_name}' to migration '{migration_name}'...")
                try:
                    subprocess.run(
                        ["python", "manage.py", "migrate", app_name, migration_name],
                        capture_output=True,
                        text=True,
                        check=True,  # This will raise CalledProcessError if the command fails.
                    )
                    self.stdout.write(self.style.SUCCESS(f"    Successfully rolled back {app_name}."))
                except subprocess.CalledProcessError as e:
                    # If a rollback fails, we now know exactly which one and why.
                    self.stdout.write(
                        self.style.ERROR(f"    ERROR: Failed to roll back {app_name} to {migration_name}.")
                    )
                    # The error from the migrate command itself is printed for clear debugging.
                    self.stdout.write(f"    Stderr: {e.stderr}")
                    raise CommandError(f"Migration rollback failed for app '{app_name}'. Cannot continue.")
            else:
                # This handles if an app is in the metadata but has a null/empty migration name.
                self.stdout.write(
                    self.style.WARNING(
                        f"    Skipping app '{app_name}' as it has no specified migration in the snapshot."
                    )
                )

        # Step 2: Wipe ALL data from the database to prevent conflicts
        self.stdout.write("Step 2: Wiping all database data with 'flush'...")
        subprocess.check_call(["python", "manage.py", "flush", "--no-input"])

        # Step 3: Load the snapshot data into the clean, correctly-shaped database
        with connection.cursor() as cursor:
            cursor.execute("SET session_replication_role = replica;")

        # Load data with ignorenonexistent flag
        call_command("loaddata", str(snapshot_path), ignorenonexistent=True)

        with connection.cursor() as cursor:
            cursor.execute("SET session_replication_role = DEFAULT;")

        # Step 4: Bring the restored data's schema forward to the latest version
        self.stdout.write("Step 4: Migrating database forward to current schema...")
        subprocess.check_call(["python", "manage.py", "migrate"])

        self.stdout.write(self.style.SUCCESS(f"✅ Database restored from {snapshot_path}"))

        # Load placement config if provided
        placement_config_path = options.get("placement_config")
        redistribute_note_times = options.get("redistribute_note_times", False)

        if placement_config_path:
            config_path = Path(placement_config_path)
            if not config_path.exists():
                self.stdout.write(self.style.ERROR(f"Placement config not found at {config_path}"))
                return

            with open(config_path, "r") as f:
                placement_config = json.load(f)

            # Apply date adjustments if placement config provided and redistribution is enabled
            if redistribute_note_times:
                self.stdout.write("Applying note placement configuration...")
                self._apply_note_placement(placement_config)
            else:
                self.stdout.write("Note redistribution disabled - keeping original dates")
        else:
            self.stdout.write("No placement config provided - notes will have original dates")

        self._print_meeting_distribution()
        self.stdout.write(self.style.SUCCESS(f'✅ Snapshot "{snapshot_name}" restored successfully!'))

    def _apply_note_placement(self, config: Dict[str, Any]) -> None:
        """Apply note placement configuration to adjust dates."""
        now = timezone.now()
        today_uuids = config.get("today", [])
        future_uuids = config.get("future", [])

        all_notes = Note.objects.all().order_by("created")
        placed_uuids = set(str(uuid) for uuid in today_uuids + future_uuids)
        past_notes = all_notes.exclude(uuid__in=placed_uuids)

        # Process today notes
        for uuid_str in today_uuids:
            note = self._find_note_by_uuid(uuid_str)
            if not note:
                continue
            target_time = now.replace(hour=10, minute=0, second=0, microsecond=0)
            note.created = target_time
            note.modified = target_time
            note.status = Note.PROCESSING_STATUS.processed
            note.save()
            self.stdout.write(f"    Today: '{note.title() if note else 'Unknown'}'")

        # Process future notes
        for i, uuid_str in enumerate(future_uuids):
            note = self._find_note_by_uuid(uuid_str)
            if not note:
                continue
            days_ahead = i + 1
            target_time = now + timedelta(days=days_ahead)
            if not note.metadata:
                note.metadata = {}
            note.metadata["scheduled_at"] = target_time.isoformat()
            note.status = Note.PROCESSING_STATUS.scheduled
            note.save()
            self.stdout.write(f"    Future: '{note.title()}' in {days_ahead} days")

        # Spread past notes chronologically over 25 days (oldest gets oldest date)
        if past_notes:
            for i, note in enumerate(past_notes):
                # Distribute evenly: oldest note gets 25 days ago, newest gets 1 day ago
                if len(past_notes) > 1:
                    days_ago = 25 - int((i * 24) / (len(past_notes) - 1))
                else:
                    days_ago = 25  # If only one note, place it 25 days ago
                days_ago = max(1, days_ago)  # Ensure at least 1 day ago

                target_time = now - timedelta(days=days_ago)
                target_time = target_time.replace(hour=10, minute=0, second=0, microsecond=0)
                note.created = target_time
                note.modified = target_time
                note.status = Note.PROCESSING_STATUS.processed
                if note.metadata and "scheduled_at" in note.metadata:
                    del note.metadata["scheduled_at"]
                note.save()
                self.stdout.write(f"    Past: '{note.title()}' {days_ago} days ago")

    def _find_note_by_uuid(self, uuid_str: str) -> Note | None:
        """Find a note by UUID string."""
        try:
            return Note.objects.get(uuid=uuid_str)
        except Note.DoesNotExist:
            self.stdout.write(self.style.WARNING(f"    Note with UUID {uuid_str} not found"))
            return None

    def _print_meeting_distribution(self) -> None:
        """Print current meeting distribution."""
        now = timezone.now()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = today_start + timedelta(days=1)

        total_notes = Note.objects.count()
        today_meetings = Note.objects.filter(created__gte=today_start, created__lt=today_end).count()
        future_meetings = Note.objects.filter(status=Note.PROCESSING_STATUS.scheduled).count()
        past_meetings = total_notes - today_meetings - future_meetings

        self.stdout.write("")
        self.stdout.write("📊 Current meeting distribution:")
        self.stdout.write(f"   Today's meetings: {today_meetings}")
        self.stdout.write(f"   Past meetings: {past_meetings}")
        self.stdout.write(f"   Future meetings: {future_meetings}")
        self.stdout.write(f"   Total: {total_notes}")
