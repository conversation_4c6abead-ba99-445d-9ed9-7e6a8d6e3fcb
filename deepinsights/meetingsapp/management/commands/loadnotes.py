import argparse
import logging
import re
import sys
import typing
import uuid
from typing import Any

from django.apps import apps
from django.core import serializers
from django.core.management.base import BaseCommand
from django.db import transaction

from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.users.models.user import User


def _get_meeting_type_by_natural_key(key: Any) -> MeetingType | None:
    try:
        return MeetingType.objects.get(key=key)
    except MeetingType.DoesNotExist:
        try:
            return MeetingType.objects.get(uuid=key)
        except MeetingType.DoesNotExist:
            return None


def load_dumped_data(
    data: str | bytes | typing.TextIO | typing.BinaryIO,
    rewrite_uuids: bool = False,
    rewrite_users_and_org: bool = False,
    num_tries: int = 1,
) -> None:
    User.natural_key = lambda self: (self.uuid,)  # type: ignore[attr-defined, method-assign]
    for model in apps.get_models():
        if hasattr(model, "uuid"):
            model._default_manager.get_by_natural_key = lambda key, model=model: model.objects.get(uuid=key)  # type: ignore[attr-defined]

    # There are pre-created meeting types in the database, so we need to use those instead of creating new ones.
    MeetingType._default_manager.get_by_natural_key = _get_meeting_type_by_natural_key  # type: ignore[attr-defined]

    buf = data
    if hasattr(buf, "read"):
        buf = buf.read()
    if isinstance(buf, bytes):
        buf = buf.decode("utf-8")

    if rewrite_uuids:
        uuid_map: dict[str, str] = {}

        def replace_uuid(match: re.Match[str]) -> str:
            old_uuid = match.group(0)
            if old_uuid not in uuid_map:
                uuid_map[old_uuid] = str(uuid.uuid4())
            return uuid_map[old_uuid]

        buf = re.sub(
            r"[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}",
            replace_uuid,
            buf,
        )

    for _ in range(num_tries):
        try:
            objects = serializers.deserialize(
                "json",
                buf,
                ignorenonexistent=True,
                handle_forward_references=True,
            )
        except Exception as e:
            raise Exception(f"Problem reading data file data: {e}") from e

        objs_with_deferred_fields = []
        for obj in objects:
            try:
                if rewrite_users_and_org and obj.object._meta.model_name == "user":
                    obj.object.username = f"{uuid.uuid4()}@example.com"  # type: ignore[attr-defined]
                    obj.object.email = obj.object.username  # type: ignore[attr-defined]
                if rewrite_users_and_org and obj.object._meta.model_name == "organization":
                    obj.object.name = f"Cloned {obj.object.name}"  # type: ignore[attr-defined]
                with transaction.atomic():
                    obj.save()
                    if obj.deferred_fields:
                        objs_with_deferred_fields.append(obj)
            except Exception as e:
                logging.error(f"Problem saving objects from data file: {e}")
                continue
        for obj in objs_with_deferred_fields:
            try:
                with transaction.atomic():
                    obj.save_deferred_fields()
            except Exception as e:
                logging.error(f"Problem reading data file data: {e}")
                continue


class Command(BaseCommand):
    help = "Load notes exported by dumpnotes"

    def add_arguments(self, parser):  # type: ignore[no-untyped-def]
        parser.add_argument("infile", nargs="?", type=argparse.FileType("r"), default=sys.stdin)

    def handle(self, *args, **options):  # type: ignore[no-untyped-def]
        load_dumped_data(options["infile"], num_tries=2)
