import json
import logging
from pathlib import Path
from typing import Any, Dict, Optional
from uuid import UUID

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction

from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingData
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Import Notes and their related data from a single JSON file"

    def add_arguments(self, parser: Any) -> None:
        parser.add_argument(
            "--input-file",
            type=str,
            required=True,
            help="Path to the JSON file containing the exported data",
        )
        parser.add_argument(
            "--owner-email",
            type=str,
            help="Email of the user who will own the imported notes (optional)",
        )

    def handle(self, *args: Any, **options: Any) -> None:
        input_file = Path(options["input_file"])
        owner_email = options.get("owner_email")

        # Check if input file exists
        if not input_file.exists():
            raise CommandError(f"Input file {input_file} does not exist")

        # Get the owner user if email is provided
        owner: Optional["User"] = None
        if owner_email:
            try:
                owner = User.objects.get(email=owner_email)
            except User.DoesNotExist:
                raise CommandError(f"Owner user with email {owner_email} does not exist")

        # Read the input file
        try:
            with open(input_file) as f:
                import_data = json.load(f)
        except json.JSONDecodeError as e:
            raise CommandError(f"Invalid JSON file: {e}")

        # Import data
        with transaction.atomic():
            for note_uuid, note_data in import_data.items():
                try:
                    self._import_note(note_data, owner)
                except Exception as e:
                    logger.error("Failed to import note %s: %s", note_uuid, str(e))
                    if input("Continue with remaining notes? (y/n): ").lower() != "y":
                        raise CommandError("Import aborted by user")

    def _import_note(self, note_data: Dict[str, Any], owner: Optional[User]) -> None:
        """Import a Note and its related data."""
        # Create or update Note
        note = self._create_note(note_data, owner)

        # Import Tasks
        if "tasks" in note_data:
            for task_data in note_data["tasks"]:
                self._create_task(task_data, note)

        # Import Structured Meeting Data
        if "structured_data" in note_data:
            for data in note_data["structured_data"]:
                self._create_structured_data(data, note)

        logger.info("Successfully imported Note %s and its related data", note.uuid)

    def _create_note(self, note_data: Dict[str, Any], owner: Optional["User"]) -> Note:
        """Create a Note from the imported data. Skip if note already exists."""
        note_uuid = UUID(note_data["uuid"])

        # Check if note already exists
        if Note.objects.filter(uuid=note_uuid).exists():
            logger.warning("Note %s already exists, skipping import", note_uuid)
            return Note.objects.get(uuid=note_uuid)

        # Get or create meeting type if specified
        meeting_type = None
        if note_data.get("meeting_type"):
            meeting_type, _ = MeetingType.objects.get_or_create(
                key=note_data["meeting_type"]["key"],
                defaults={"uuid": UUID(note_data["meeting_type"]["uuid"])},
            )

        # Handle note owner logic
        note_owner = owner
        if not note_owner:
            # First try to get owner from note_owner email if available
            if note_data.get("note_owner", {}).get("email"):
                try:
                    note_owner = User.objects.get(email=note_data["note_owner"]["email"])
                except User.DoesNotExist:
                    logger.warning(
                        "Could not find user with email %s from note_owner", note_data["note_owner"]["email"]
                    )

            # If still no owner, try UUID from note_owner
            if not note_owner and note_data.get("note_owner", {}).get("uuid"):
                try:
                    note_owner = User.objects.get(uuid=UUID(note_data["note_owner"]["uuid"]))
                except (User.DoesNotExist, ValueError):
                    logger.warning("Could not find user with UUID %s from note_owner", note_data["note_owner"]["uuid"])

        # Create new note
        note = Note.objects.create(
            uuid=note_uuid,
            file_path=note_data.get("file_path"),
            file_type=note_data.get("file_type"),
            note_type=note_data.get("note_type"),
            metadata=note_data.get("metadata"),
            status=note_data.get("status"),
            data_source=note_data.get("data_source"),
            diarized_trans_with_names=note_data.get("diarized_trans_with_names"),
            raw_transcript=note_data.get("raw_transcript"),
            advisor_notes=note_data.get("advisor_notes"),
            key_takeaways=note_data.get("key_takeaways"),
            summary_by_topics=note_data.get("summary_by_topics"),
            summary=note_data.get("summary"),
            raw_asr_response=note_data.get("raw_asr_response"),
            salesforce_case_id=note_data.get("salesforce_case_id"),
            follow_up_email_contents=note_data.get("follow_up_email_contents"),
            client=note_data.get("client"),
            meeting_type=meeting_type,
            note_owner=note_owner,
        )

        # Add note owner to authorized users if they exist
        if note_owner:
            note.authorized_users.add(note_owner)

        logger.info("Created Note %s", note.uuid)
        return note

    def _create_task(self, task_data: Dict[str, Any], note: Note) -> Task:
        """Create a Task from the imported data. Skip if task already exists."""
        task_uuid = UUID(task_data["uuid"])

        # Check if task already exists
        if Task.objects.filter(uuid=task_uuid).exists():
            logger.warning("Task %s already exists, skipping import", task_uuid)
            return Task.objects.get(uuid=task_uuid)

        # Get task owner if specified
        task_owner = None
        if task_data.get("task_owner_uuid"):
            try:
                task_owner = User.objects.get(uuid=UUID(task_data["task_owner_uuid"]))
            except User.DoesNotExist:
                logger.warning("Task owner %s does not exist", task_data["task_owner_uuid"])

        # Get assignee if specified
        assignee = None
        if task_data.get("assignee_uuid"):
            try:
                assignee = User.objects.get(uuid=UUID(task_data["assignee_uuid"]))
            except User.DoesNotExist:
                logger.warning("Assignee %s does not exist", task_data["assignee_uuid"])

        # Create new task
        task = Task.objects.create(
            uuid=task_uuid,
            task_title=str(task_data.get("task_title", "")) if task_data.get("task_title") else "",
            task_desc=task_data.get("task_desc"),
            completed=bool(task_data.get("completed", False)),
            due_date=task_data.get("due_date"),
            metadata=task_data.get("metadata"),
            note=note,
            task_owner=task_owner,
            assignee=assignee,
        )

        logger.info("Created Task %s", task.uuid)
        return task

    def _create_structured_data(self, data: Dict[str, Any], note: Note) -> StructuredMeetingData:
        """Create a StructuredMeetingData from the imported data. Skip if already exists."""
        data_uuid = UUID(data["uuid"])

        # Check if structured data already exists
        if StructuredMeetingData.objects.filter(uuid=data_uuid).exists():
            logger.warning("StructuredMeetingData %s already exists, skipping import", data_uuid)
            return StructuredMeetingData.objects.get(uuid=data_uuid)

        # Create new structured data
        structured_data = StructuredMeetingData.objects.create(
            uuid=data_uuid,
            title=data.get("title"),
            internal_name=data.get("internal_name"),
            kind=str(data.get("kind", "")) if data.get("kind") else "",
            schema=data.get("schema"),
            data=data.get("data"),
            context=data.get("context"),
            status=str(data.get("status", "")) if data.get("status") else "",
            note=note,
        )

        logger.info("Created StructuredMeetingData %s", structured_data.uuid)
        return structured_data
