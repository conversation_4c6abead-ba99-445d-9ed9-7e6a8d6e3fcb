from datetime import timedelta
from typing import Dict, List
from unittest.mock import patch

from django.test import TestCase
from django.utils import timezone

from deepinsights.meetingsapp.management.commands.restore_demo_snapshot import Command
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


class RestoreDemoSnapshotTest(TestCase):
    def setUp(self) -> None:
        self.command = Command()
        self.test_user = User.objects.create(email="<EMAIL>", username="testuser", name="Test User")

        # Create test notes with different creation times.
        self.now = timezone.now()
        self.note1 = Note.objects.create(
            note_owner=self.test_user,
            metadata={"meeting_name": "Test Note 1."},
            created=self.now - timedelta(days=5),
            modified=self.now - timedelta(days=5),
            status=Note.PROCESSING_STATUS.processed,
        )
        self.note2 = Note.objects.create(
            note_owner=self.test_user,
            metadata={"meeting_name": "Test Note 2."},
            created=self.now - timedelta(days=3),
            modified=self.now - timedelta(days=3),
            status=Note.PROCESSING_STATUS.processed,
        )
        self.note3 = Note.objects.create(
            note_owner=self.test_user,
            metadata={"meeting_name": "Test Note 3."},
            created=self.now - timedelta(days=1),
            modified=self.now - timedelta(days=1),
            status=Note.PROCESSING_STATUS.processed,
        )
        # Add notes older than a month to test distribution functionality
        self.note4 = Note.objects.create(
            note_owner=self.test_user,
            metadata={"meeting_name": "Old Test Note 1."},
            created=self.now - timedelta(days=45),
            modified=self.now - timedelta(days=45),
            status=Note.PROCESSING_STATUS.processed,
        )
        self.note5 = Note.objects.create(
            note_owner=self.test_user,
            metadata={"meeting_name": "Old Test Note 2."},
            created=self.now - timedelta(days=60),
            modified=self.now - timedelta(days=60),
            status=Note.PROCESSING_STATUS.processed,
        )

    def test_apply_note_placement_today_notes(self) -> None:
        """Test that notes are properly placed for today."""
        config: Dict[str, List[str]] = {
            "today": [str(self.note1.uuid)],
            "future": [],
        }

        with patch.object(timezone, "now", return_value=self.now):
            self.command._apply_note_placement(config)

        self.note1.refresh_from_db()
        expected_time = self.now.replace(hour=10, minute=0, second=0, microsecond=0)
        self.assertEqual(self.note1.created, expected_time)
        # modified field gets auto-updated by Django when save() is called,
        # so we don't test the exact time
        self.assertIsNotNone(self.note1.modified)
        self.assertEqual(self.note1.status, Note.PROCESSING_STATUS.processed)

    def test_apply_note_placement_future_notes(self) -> None:
        """Test that notes are properly scheduled for future dates."""
        config: Dict[str, List[str]] = {
            "today": [],
            "future": [str(self.note1.uuid), str(self.note2.uuid)],
        }

        with patch.object(timezone, "now", return_value=self.now):
            self.command._apply_note_placement(config)

        self.note1.refresh_from_db()
        self.note2.refresh_from_db()

        # First future note should be scheduled 1 day ahead
        expected_time1 = self.now + timedelta(days=1)
        metadata1 = self.note1.metadata or {}
        self.assertEqual(metadata1["scheduled_at"], expected_time1.isoformat())
        self.assertEqual(self.note1.status, Note.PROCESSING_STATUS.scheduled)

        # Second future note should be scheduled 2 days ahead
        expected_time2 = self.now + timedelta(days=2)
        metadata2 = self.note2.metadata or {}
        self.assertEqual(metadata2["scheduled_at"], expected_time2.isoformat())
        self.assertEqual(self.note2.status, Note.PROCESSING_STATUS.scheduled)

    def test_apply_note_placement_past_notes_distribution(self) -> None:
        """Test that past notes are properly distributed over 25 days."""
        config: Dict[str, List[str]] = {
            "today": [],
            "future": [],
        }

        with patch.object(timezone, "now", return_value=self.now):
            self.command._apply_note_placement(config)

        # Refresh all notes from db
        all_notes = [self.note1, self.note2, self.note3, self.note4, self.note5]
        for note in all_notes:
            note.refresh_from_db()

        # All notes should be in the past (between 1-25 days ago)
        for note in all_notes:
            self.assertLess(note.created, self.now)
            self.assertGreater(note.created, self.now - timedelta(days=26))
            self.assertEqual(note.status, Note.PROCESSING_STATUS.processed)
            self.assertEqual(note.created.hour, 10)
            self.assertEqual(note.created.minute, 0)
            self.assertEqual(note.created.second, 0)
            self.assertEqual(note.created.microsecond, 0)

    def test_apply_note_placement_past_notes_chronological_order(self) -> None:
        """Test that past notes maintain chronological order (oldest gets oldest date)."""
        config: Dict[str, List[str]] = {
            "today": [],
            "future": [],
        }

        original_order = [self.note5, self.note4, self.note1, self.note2, self.note3]
        with patch.object(timezone, "now", return_value=self.now):
            self.command._apply_note_placement(config)

        # Refresh all notes from db
        all_notes = [self.note1, self.note2, self.note3, self.note4, self.note5]
        for note in all_notes:
            note.refresh_from_db()

        # Sort notes by their new placement times (oldest to newest)
        notes_by_new_placement = sorted(all_notes, key=lambda n: n.created)

        self.assertEqual(
            notes_by_new_placement, original_order, "Notes should maintain chronological order after placement"
        )

    def test_apply_note_placement_single_past_note(self) -> None:
        """Test that a single past note is placed 25 days ago."""
        # Delete other notes to have only one
        self.note2.delete()
        self.note3.delete()
        self.note4.delete()
        self.note5.delete()

        config: Dict[str, List[str]] = {
            "today": [],
            "future": [],
        }

        with patch.object(timezone, "now", return_value=self.now):
            self.command._apply_note_placement(config)

        self.note1.refresh_from_db()
        expected_time = self.now - timedelta(days=25)
        expected_time = expected_time.replace(hour=10, minute=0, second=0, microsecond=0)
        self.assertEqual(self.note1.created, expected_time)

    def test_apply_note_placement_mixed_configuration(self) -> None:
        """Test configuration with today, future, and past notes."""
        config: Dict[str, List[str]] = {
            "today": [str(self.note1.uuid)],
            "future": [str(self.note2.uuid)],
        }

        with patch.object(timezone, "now", return_value=self.now):
            self.command._apply_note_placement(config)

        self.note1.refresh_from_db()
        self.note2.refresh_from_db()
        self.note3.refresh_from_db()
        self.note4.refresh_from_db()
        self.note5.refresh_from_db()

        # note1 should be today
        expected_today = self.now.replace(hour=10, minute=0, second=0, microsecond=0)
        self.assertEqual(self.note1.created, expected_today)
        self.assertEqual(self.note1.status, Note.PROCESSING_STATUS.processed)

        # note2 should be future
        expected_future = self.now + timedelta(days=1)
        metadata2 = self.note2.metadata or {}
        self.assertEqual(metadata2["scheduled_at"], expected_future.isoformat())
        self.assertEqual(self.note2.status, Note.PROCESSING_STATUS.scheduled)

        # Past notes (note3, note4, note5) should be distributed across 25 days
        # Since there are 3 past notes, they should be spread with the oldest
        # original note getting the oldest placement
        past_notes = [self.note3, self.note4, self.note5]
        for note in past_notes:
            self.assertLess(note.created, self.now)
            self.assertGreater(note.created, self.now - timedelta(days=26))
            self.assertEqual(note.status, Note.PROCESSING_STATUS.processed)

    def test_apply_note_placement_removes_scheduled_at_from_past_notes(
        self,
    ) -> None:
        """Test that scheduled_at is removed from past notes' metadata."""
        # Set up a note with scheduled_at in metadata
        self.note1.metadata = {"scheduled_at": "2023-01-01T10:00:00"}
        self.note1.save()

        config: Dict[str, List[str]] = {
            "today": [],
            "future": [],
        }

        with patch.object(timezone, "now", return_value=self.now):
            self.command._apply_note_placement(config)

        self.note1.refresh_from_db()
        self.assertNotIn("scheduled_at", self.note1.metadata or {})

    def test_find_note_by_uuid_existing_note(self) -> None:
        """Test finding an existing note by UUID."""
        found_note = self.command._find_note_by_uuid(str(self.note1.uuid))
        self.assertEqual(found_note, self.note1)

    def test_find_note_by_uuid_nonexistent_note(self) -> None:
        """Test finding a non-existent note by UUID returns None."""
        fake_uuid = "00000000-0000-0000-0000-000000000000"
        found_note = self.command._find_note_by_uuid(fake_uuid)
        self.assertIsNone(found_note)
