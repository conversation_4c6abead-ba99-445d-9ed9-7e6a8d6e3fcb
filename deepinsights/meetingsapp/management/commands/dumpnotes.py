import queue
import uuid
from typing import Any

from django.apps import apps
from django.core import serializers
from django.core.management.base import BaseCommand
from django.db.models import Model, Q

from deepinsights.core.behaviours import UUIDMixin
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


def notes_data_for_export(note_ids: list[str]) -> str:
    # Get all notes, by pk and/or uuid
    def is_uuid(id: Any) -> bool:
        try:
            uuid.UUID(id)
            return True
        except Exception:
            return False

    pks = filter(lambda x: x.isdigit(), note_ids)
    uuids = filter(is_uuid, note_ids)
    notes = Note.objects.filter(Q(pk__in=pks) | Q(uuid__in=uuids))

    q: queue.Queue[UUIDMixin] = queue.Queue()

    seen: set[UUIDMixin] = set()
    seen_uuids: set[uuid.UUID] = set()

    for note in notes:
        q.put(note)

    while not q.empty():
        o = q.get()
        if o.uuid in seen_uuids:
            continue
        seen.add(o)
        seen_uuids.add(o.uuid)
        for field in o._meta.get_fields():
            if not hasattr(field, "is_relation") or not field.is_relation:
                continue
            related_model = field.related_model
            if not isinstance(related_model, Model):
                continue
            related_model_meta = related_model._meta
            if not (related_model_meta.app_label == "meetingsapp" or related_model_meta.model_name == "user"):
                continue
            name = field.get_accessor_name() if hasattr(field, "get_accessor_name") else field.name
            if not name:
                continue
            if not hasattr(o, name):
                continue
            foreign = getattr(o, name)
            if hasattr(foreign, "all"):
                for r in foreign.all():
                    q.put(r)
            elif foreign:
                q.put(foreign)

    objects = list(seen)

    def sort_key(o: UUIDMixin) -> str:
        if not o._meta.model_name:
            return ""
        return (
            "AAA"
            if "organization" in o._meta.model_name
            else "AAB"
            if "user" in o._meta.model_name
            else "AAC"
            if "note" in o._meta.model_name
            else o._meta.model_name
        )

    objects.sort(key=sort_key)
    User.natural_key = lambda self: (self.uuid,)  # type: ignore[method-assign, attr-defined]
    for model in apps.get_models():
        if hasattr(model, "uuid"):
            model.natural_key = lambda self: (self.uuid,)  # type: ignore[attr-defined]

    return serializers.serialize(  # type: ignore[no-any-return]
        "json",
        objects,
        indent=2,
        use_natural_foreign_keys=True,
        use_natural_primary_keys=True,
    )


class Command(BaseCommand):
    help = "Export notes and all relevant model objects"

    def add_arguments(self, parser):  # type: ignore[no-untyped-def]
        parser.add_argument("note_ids", nargs="+", type=str)

    def handle(self, *args, **options):  # type: ignore[no-untyped-def]
        print(notes_data_for_export(options.get("note_ids", [])))
