from typing import Optional

from pydantic import BaseModel


class SnapshotMetadata(BaseModel):
    git_commit: str
    created_at: str
    total_notes: int
    description: str
    app_migrations: dict[str, Optional[str]] = {}


class PlacementConfig(BaseModel):
    """Pydantic model for placement configuration validation."""

    today: list[str] = []  # List of note UUIDs to place in today's tab in the UI.
    future: list[str] = []  # List of note UUIDs to place in future tab (scheduled meetings).
