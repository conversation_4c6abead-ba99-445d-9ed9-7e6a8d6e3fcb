import json
from datetime import timed<PERSON><PERSON>
from unittest.mock import MagicMock, patch

from django.test import TestCase
from django.utils import timezone

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.meetingsapp.models.client_interaction import ClientInteraction
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingData
from deepinsights.meetingsapp.tasks_meeting_prep import (
    _infer_meeting_type,
    create_meeting_prep_in_advance,
    send_meeting_prep_advance_email,
)
from deepinsights.ml_agents.models.agent_config import AgentConfig, AgentOutputEnumeratedListSubset
from deepinsights.users.models.user import User


class TestTasksMeetingPrep(TestCase):
    """Test cases for meeting prep tasks."""

    @patch("django.utils.timezone")
    @patch("deepinsights.flags.flagdefs.Flags.EnableMeetingPrepAdvanceEmail.is_active_for_user")
    @patch("deepinsights.core.email_service.ZeplynEmailService.send_email")
    def test_email_meeting_prep_success(
        self, mock_email_service: MagicMock, mock_flags: MagicMock, mock_tz: MagicMock
    ) -> None:
        """Test meeting prep email sending"""

        mock_tz.now.return_value = timezone.now()
        mock_flags.return_value = True

        user = User.objects.create(name="Test User Name", email="<EMAIL>")
        note = Note.objects.create(note_owner=user, created=timezone.now(), status=Note.PROCESSING_STATUS.scheduled)
        agenda = StructuredMeetingData.objects.create(
            data={"format": "markdown", "content": "This is a sample"}, note=note, kind="agenda"
        )
        meeting_type = MeetingType.objects.create(name="Test Meeting Type", category=MeetingType.Category.CLIENT)
        interaction = ClientInteraction.objects.create(note=note, agenda=agenda, meeting_type=meeting_type)
        event = ScheduledEvent.objects.create(
            note=note,
            user=user,
            start_time=timezone.now() + timedelta(minutes=30),
            end_time=timezone.now() + timedelta(minutes=90),
        )

        send_meeting_prep_advance_email()
        mock_flags.assert_called_once()
        mock_email_service.assert_called_once()

    @patch("django.utils.timezone")
    @patch("deepinsights.flags.flagdefs.Flags.EnableMeetingPrepAdvanceEmail.is_active_for_user")
    @patch("deepinsights.core.email_service.ZeplynEmailService.send_email")
    def test_email_meeting_prep_improper_format_agenda(
        self, mock_email_service: MagicMock, mock_flags: MagicMock, mock_tz: MagicMock
    ) -> None:
        """Test meeting prep email is not sent when the agenda doesn't have a content field"""

        mock_tz.now.return_value = timezone.now()
        mock_flags.return_value = True

        user = User.objects.create(name="Test User Name", email="<EMAIL>")
        note = Note.objects.create(note_owner=user, created=timezone.now(), status=Note.PROCESSING_STATUS.scheduled)
        agenda = StructuredMeetingData.objects.create(
            data={"wrong": "Bad format not a dict with content"}, note=note, kind="agenda"
        )
        meeting_type = MeetingType.objects.create(name="Test Meeting Type", category=MeetingType.Category.CLIENT)
        interaction = ClientInteraction.objects.create(note=note, agenda=agenda, meeting_type=meeting_type)
        event = ScheduledEvent.objects.create(
            note=note,
            user=user,
            start_time=timezone.now() + timedelta(minutes=30),
            end_time=timezone.now() + timedelta(minutes=90),
        )

        send_meeting_prep_advance_email()
        mock_flags.assert_called_once()
        mock_email_service.assert_not_called()

    @patch("django.utils.timezone")
    @patch("deepinsights.flags.flagdefs.Flags.EnableMeetingPrepAdvanceEmail.is_active_for_user")
    @patch("deepinsights.core.email_service.ZeplynEmailService.send_email")
    def test_email_meeting_prep_flag_off(
        self, mock_email_service: MagicMock, mock_flags: MagicMock, mock_tz: MagicMock
    ) -> None:
        """Test meeting prep email is not sent when the flag is off"""

        mock_tz.now.return_value = timezone.now()
        mock_flags.return_value = False

        user = User.objects.create(name="Test User Name", email="<EMAIL>")
        note = Note.objects.create(note_owner=user, created=timezone.now(), status=Note.PROCESSING_STATUS.scheduled)
        agenda = StructuredMeetingData.objects.create(
            data={"format": "markdown", "content": "This is a sample"}, note=note, kind="agenda"
        )
        meeting_type = MeetingType.objects.create(name="Test Meeting Type", category=MeetingType.Category.CLIENT)
        interaction = ClientInteraction.objects.create(note=note, agenda=agenda, meeting_type=meeting_type)
        event = ScheduledEvent.objects.create(note=note, user=user, start_time=timezone.now(), end_time=timezone.now())

        send_meeting_prep_advance_email()
        mock_flags.assert_called_once()
        mock_email_service.assert_not_called()

    @patch("django.utils.timezone")
    @patch("deepinsights.flags.flagdefs.Flags.EnableMeetingPrepAdvanceEmail.is_active_for_user")
    @patch("deepinsights.core.email_service.ZeplynEmailService.send_email")
    def test_email_meeting_prep_no_agendas(
        self, mock_email_service: MagicMock, mock_flags: MagicMock, mock_tz: MagicMock
    ) -> None:
        """Test meeting prep email is not sent when there is no agenda"""

        mock_tz.now.return_value = timezone.now()
        mock_flags.return_value = True

        user = User.objects.create(name="Test User Name", email="<EMAIL>")
        note = Note.objects.create(note_owner=user, created=timezone.now(), status=Note.PROCESSING_STATUS.scheduled)
        event = ScheduledEvent.objects.create(note=note, user=user, start_time=timezone.now(), end_time=timezone.now())

        send_meeting_prep_advance_email()
        mock_flags.assert_called_once()
        mock_email_service.assert_not_called()

    @patch("django.utils.timezone")
    @patch("deepinsights.flags.flagdefs.Flags.EnableMeetingPrepAdvanceEmail.is_active_for_user")
    @patch("deepinsights.core.email_service.ZeplynEmailService.send_email")
    def test_email_meeting_prep_too_far_out(
        self, mock_email_service: MagicMock, mock_flags: MagicMock, mock_tz: MagicMock
    ) -> None:
        """Test meeting prep email is not sent when event is too far in the future"""

        mock_tz.now.return_value = timezone.now()
        mock_flags.return_value = True

        user = User.objects.create(name="Test User Name", email="<EMAIL>")
        note = Note.objects.create(note_owner=user, created=timezone.now(), status=Note.PROCESSING_STATUS.scheduled)
        agenda = StructuredMeetingData.objects.create(
            data={"format": "markdown", "content": "This is a sample"}, note=note, kind="agenda"
        )
        meeting_type = MeetingType.objects.create(name="Test Meeting Type", category=MeetingType.Category.CLIENT)
        interaction = ClientInteraction.objects.create(note=note, agenda=agenda, meeting_type=meeting_type)
        event = ScheduledEvent.objects.create(
            note=note,
            user=user,
            start_time=timezone.now() + timedelta(days=20),
            end_time=timezone.now() + timedelta(days=20, minutes=30),
        )

        send_meeting_prep_advance_email()
        mock_flags.assert_called_once()
        mock_email_service.assert_not_called()

    @patch("django.utils.timezone")
    @patch("deepinsights.flags.flagdefs.Flags.EnableMeetingPrepAdvanceEmail.is_active_for_user")
    @patch("deepinsights.core.email_service.ZeplynEmailService.send_email")
    def test_email_meeting_prep_past(
        self, mock_email_service: MagicMock, mock_flags: MagicMock, mock_tz: MagicMock
    ) -> None:
        """Test meeting prep email is not sent for a meeting in the past"""

        mock_tz.now.return_value = timezone.now()
        mock_flags.return_value = True

        user = User.objects.create(name="Test User Name", email="<EMAIL>")
        note = Note.objects.create(note_owner=user, created=timezone.now(), status=Note.PROCESSING_STATUS.scheduled)
        agenda = StructuredMeetingData.objects.create(
            data={"format": "markdown", "content": "This is a sample"}, note=note, kind="agenda"
        )
        meeting_type = MeetingType.objects.create(name="Test Meeting Type", category=MeetingType.Category.CLIENT)
        interaction = ClientInteraction.objects.create(note=note, agenda=agenda, meeting_type=meeting_type)
        event = ScheduledEvent.objects.create(
            note=note,
            user=user,
            start_time=timezone.now() - timedelta(days=20),
            end_time=timezone.now() - timedelta(days=20, minutes=30),
        )

        send_meeting_prep_advance_email()
        mock_flags.assert_called_once()
        mock_email_service.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks_meeting_prep._infer_meeting_type")
    @patch("django.utils.timezone")
    @patch("deepinsights.flags.flagdefs.Flags.EnableMeetingPrepAutogeneration.is_active_for_user")
    @patch("deepinsights.meetingsapp.tasks_meeting_prep.create_or_update_interaction")
    def test_create_meeting_prep_success(
        self,
        mock_create_interaction: MagicMock,
        mock_flags: MagicMock,
        mock_tz: MagicMock,
        mock_infer_meeting_type: MagicMock,
    ) -> None:
        """Test meeting prep in advance succeeds when a note but not an interaction exists"""

        mock_tz.now.return_value = timezone.now()
        mock_flags.return_value = True
        user = User.objects.create()
        custom_meeting_type = MeetingType.objects.create(key="custom", name="Custom", everyone=True)
        preferences = user.preferences or {}
        preferences["default_meeting_type"] = str(custom_meeting_type.uuid)
        user.preferences = preferences
        user.save()
        note = Note.objects.create(
            note_owner=user,
            created=timezone.now(),
            status=Note.PROCESSING_STATUS.scheduled,
            meeting_type=custom_meeting_type,
        )
        event = ScheduledEvent.objects.create(
            note=note,
            user=user,
            start_time=timezone.now() + timedelta(minutes=30),
            end_time=timezone.now() + timedelta(minutes=90),
        )

        create_meeting_prep_in_advance()
        mock_flags.assert_called_once()
        mock_infer_meeting_type.assert_not_called()
        mock_create_interaction.assert_called_once_with(note, user)

    @patch("deepinsights.meetingsapp.tasks_meeting_prep._infer_meeting_type")
    @patch("django.utils.timezone")
    @patch("deepinsights.flags.flagdefs.Flags.EnableMeetingPrepAutogeneration.is_active_for_user")
    @patch("deepinsights.meetingsapp.tasks_meeting_prep.create_or_update_interaction")
    def test_create_meeting_prep_no_default_meetingtype(
        self,
        mock_create_interaction: MagicMock,
        mock_flags: MagicMock,
        mock_tz: MagicMock,
        mock_infer_meeting_type: MagicMock,
    ) -> None:
        """Test meeting prep in advance infers the meeting type when there's a note with no meeting type"""

        mock_tz.now.return_value = timezone.now()
        mock_flags.return_value = True
        user = User.objects.create()
        note = Note.objects.create(note_owner=user, created=timezone.now(), status=Note.PROCESSING_STATUS.scheduled)
        event = ScheduledEvent.objects.create(
            note=note,
            user=user,
            start_time=timezone.now() + timedelta(minutes=30),
            end_time=timezone.now() + timedelta(minutes=90),
        )
        mock_infer_meeting_type.return_value = ""

        create_meeting_prep_in_advance()
        mock_flags.assert_called_once()
        mock_infer_meeting_type.assert_called_once()
        mock_create_interaction.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks_meeting_prep._infer_meeting_type")
    @patch("django.utils.timezone")
    @patch("deepinsights.flags.flagdefs.Flags.EnableMeetingPrepAutogeneration.is_active_for_user")
    @patch("deepinsights.meetingsapp.tasks_meeting_prep.create_or_update_interaction")
    def test_create_meeting_prep_success_create_note_interaction(
        self,
        mock_create_interaction: MagicMock,
        mock_flags: MagicMock,
        mock_tz: MagicMock,
        mock_infer_meeting_type: MagicMock,
    ) -> None:
        """Test meeting prep in advance succeeds when there is not yet a note or interaction"""

        mock_tz.now.return_value = timezone.now()
        mock_flags.return_value = True
        user = User.objects.create()
        custom_meeting_type = MeetingType.objects.create(key="custom", name="Custom", everyone=True)
        preferences = user.preferences or {}
        preferences["default_meeting_type"] = str(custom_meeting_type.uuid)
        user.preferences = preferences
        user.save()
        mock_infer_meeting_type.return_value = str(custom_meeting_type.uuid)
        start_time = timezone.now() + timedelta(minutes=30)
        end_time = timezone.now() + timedelta(minutes=90)
        event = ScheduledEvent.objects.create(
            user=user,
            start_time=start_time,
            end_time=end_time,
            source_data=CalendarEvent(
                provider="google",
                id="test",
                user_specific_id="test",
                title="A title",
                body=None,
                start_time=start_time,
                end_time=end_time,
                all_day=False,
                participants=[],
                meeting_urls=[],
            ).model_dump(mode="json"),
        )

        create_meeting_prep_in_advance()
        mock_flags.assert_called_once()
        mock_infer_meeting_type.assert_called_once()
        mock_create_interaction.assert_called_once()
        event.refresh_from_db()
        assert event.note and event.note.meeting_type and event.note.note_type
        assert event.note.title() != "Err:Contact support:No Metadata"

    @patch("deepinsights.meetingsapp.tasks_meeting_prep._infer_meeting_type")
    @patch("django.utils.timezone")
    @patch("deepinsights.flags.flagdefs.Flags.EnableMeetingPrepAutogeneration.is_active_for_user")
    @patch("deepinsights.meetingsapp.tasks_meeting_prep.create_or_update_interaction")
    def test_create_meeting_prep_flag_off(
        self,
        mock_create_interaction: MagicMock,
        mock_flags: MagicMock,
        mock_tz: MagicMock,
        mock_infer_meeting_type: MagicMock,
    ) -> None:
        """Test meeting prep in advance does nothing when flag is off"""

        mock_tz.now.return_value = timezone.now()
        mock_flags.return_value = False
        user = User.objects.create()
        note = Note.objects.create(note_owner=user, created=timezone.now(), status=Note.PROCESSING_STATUS.scheduled)
        event = ScheduledEvent.objects.create(note=note, user=user, start_time=timezone.now(), end_time=timezone.now())

        create_meeting_prep_in_advance()
        mock_flags.assert_called_once()
        mock_infer_meeting_type.assert_not_called()
        mock_create_interaction.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks_meeting_prep._infer_meeting_type")
    @patch("django.utils.timezone")
    @patch("deepinsights.flags.flagdefs.Flags.EnableMeetingPrepAutogeneration.is_active_for_user")
    @patch("deepinsights.meetingsapp.tasks_meeting_prep.create_or_update_interaction")
    def test_create_meeting_prep_too_far_out(
        self,
        mock_create_interaction: MagicMock,
        mock_flags: MagicMock,
        mock_tz: MagicMock,
        mock_infer_meeting_type: MagicMock,
    ) -> None:
        """Test meeting prep in advance does nothing for events after end of lookahead window"""

        mock_tz.now.return_value = timezone.now()
        mock_flags.return_value = True
        user = User.objects.create()
        note = Note.objects.create(note_owner=user, created=timezone.now(), status=Note.PROCESSING_STATUS.scheduled)
        event = ScheduledEvent.objects.create(
            note=note,
            user=user,
            start_time=timezone.now() + timedelta(days=15),
            end_time=timezone.now() + timedelta(days=15),
        )

        create_meeting_prep_in_advance()
        mock_flags.assert_called_once()
        mock_infer_meeting_type.assert_not_called()
        mock_create_interaction.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks_meeting_prep._infer_meeting_type")
    @patch("django.utils.timezone")
    @patch("deepinsights.flags.flagdefs.Flags.EnableMeetingPrepAutogeneration.is_active_for_user")
    @patch("deepinsights.meetingsapp.tasks_meeting_prep.create_or_update_interaction")
    def test_create_meeting_prep_past(
        self,
        mock_create_interaction: MagicMock,
        mock_flags: MagicMock,
        mock_tz: MagicMock,
        mock_infer_meeting_type: MagicMock,
    ) -> None:
        """Test meeting prep in advance does nothing for events which already occurred"""

        mock_tz.now.return_value = timezone.now()
        mock_flags.return_value = True
        user = User.objects.create()
        note = Note.objects.create(note_owner=user, created=timezone.now(), status=Note.PROCESSING_STATUS.scheduled)
        event = ScheduledEvent.objects.create(
            note=note,
            user=user,
            start_time=timezone.now() - timedelta(days=15),
            end_time=timezone.now() - timedelta(days=15),
        )

        create_meeting_prep_in_advance()
        mock_flags.assert_called_once()
        mock_infer_meeting_type.assert_not_called()
        mock_create_interaction.assert_not_called()

    def test_infer_meeting_type_returns_default_no_agentconfig(self) -> None:
        user = User.objects.create()
        custom_meeting_type = MeetingType.objects.create(key="custom", name="Custom", everyone=True)
        preferences = user.preferences or {}
        preferences["default_meeting_type"] = str(custom_meeting_type.uuid)
        user.preferences = preferences
        user.save()
        _ = [a.delete() for a in AgentConfig.objects.filter(name="meetingtype_for_note")]
        event = ScheduledEvent.objects.create(
            user=user,
            start_time=timezone.now() + timedelta(minutes=30),
            end_time=timezone.now() + timedelta(minutes=90),
        )
        assert _infer_meeting_type(event) == str(custom_meeting_type.uuid)

    def test_infer_meeting_type_returns_default_invalid_agentconfig(self) -> None:
        org = Organization.objects.create(name="test org")
        user = User.objects.create(name="test user", organization=org)
        custom_meeting_type = MeetingType.objects.create(key="custom", name="Custom", everyone=True)
        preferences = user.preferences or {}
        preferences["default_meeting_type"] = str(custom_meeting_type.uuid)
        user.preferences = preferences
        user.save()
        event = ScheduledEvent.objects.create(
            user=user,
            start_time=timezone.now() + timedelta(minutes=30),
            end_time=timezone.now() + timedelta(minutes=90),
        )
        _ = [a.delete() for a in AgentConfig.objects.filter(name="meetingtype_for_note")]
        agent_config = AgentConfig.objects.update_or_create(
            owner=org,
            name="meetingtype_for_note",
            user_prompt="",  # Empty is invalid
            version_notes="First version",
            user_prompt_metadata_example={"note_context": "test"},
            user_prompt_metadata_schema=["note_context"],
            system_prompt=None,
            system_prompt_metadata=None,
            output_validation_schema="list_subset",
            llm_model="gpt-4o-2024-08-06",
        )

        assert _infer_meeting_type(event) == str(custom_meeting_type.uuid)

    def test_infer_meeting_type_returns_default_valueerror_prompt_agentconfig(self) -> None:
        org = Organization.objects.create(name="test org")
        user = User.objects.create(name="test user", organization=org)
        custom_meeting_type = MeetingType.objects.create(key="custom", name="Custom", everyone=True)
        preferences = user.preferences or {}
        preferences["default_meeting_type"] = str(custom_meeting_type.uuid)
        user.preferences = preferences
        user.save()
        event = ScheduledEvent.objects.create(
            user=user,
            start_time=timezone.now() + timedelta(minutes=30),
            end_time=timezone.now() + timedelta(minutes=90),
        )
        _ = [a.delete() for a in AgentConfig.objects.filter(name="meetingtype_for_note")]
        agent_config = AgentConfig.objects.update_or_create(
            owner=org,
            name="meetingtype_for_note",
            user_prompt="This is a test",
            version_notes="First version",
            user_prompt_metadata_example={"other_context": "test"},  # should be note_context
            user_prompt_metadata_schema=["other_context"],
            system_prompt=None,
            system_prompt_metadata=None,
            output_validation_schema="list_subset",
            llm_model="gpt-4o-2024-08-06",
        )

        assert _infer_meeting_type(event) == str(custom_meeting_type.uuid)

    @patch("pydantic_ai.Agent.run_sync")
    def test_infer_meeting_type_returns_default_no_agent_results(self, mock_agent_run: MagicMock) -> None:
        org = Organization.objects.create(name="test org")
        user = User.objects.create(name="test user", organization=org)
        custom_meeting_type = MeetingType.objects.create(key="custom", name="Custom", everyone=True)
        preferences = user.preferences or {}
        preferences["default_meeting_type"] = str(custom_meeting_type.uuid)
        user.preferences = preferences
        user.save()
        event = ScheduledEvent.objects.create(
            user=user,
            start_time=timezone.now() + timedelta(minutes=30),
            end_time=timezone.now() + timedelta(minutes=90),
        )
        mock_agent_run.side_effect = [Exception()]

        assert _infer_meeting_type(event) == str(custom_meeting_type.uuid)

    @patch("pydantic_ai.Agent.run_sync")
    def test_infer_meeting_type_returns_default_no_items(self, mock_agent_run: MagicMock) -> None:
        org = Organization.objects.create(name="test org")
        user = User.objects.create(name="test user", organization=org)
        custom_meeting_type = MeetingType.objects.create(key="custom", name="Custom", everyone=True)
        preferences = user.preferences or {}
        preferences["default_meeting_type"] = str(custom_meeting_type.uuid)
        user.preferences = preferences
        user.save()
        event = ScheduledEvent.objects.create(
            user=user,
            start_time=timezone.now() + timedelta(minutes=30),
            end_time=timezone.now() + timedelta(minutes=90),
        )
        mock_agent_run.return_value.output = AgentOutputEnumeratedListSubset(items=[])

        assert _infer_meeting_type(event) == str(custom_meeting_type.uuid)

    @patch("pydantic_ai.Agent.run_sync")
    def test_infer_meeting_type_returns_default_no_result_in_db(self, mock_agent_run: MagicMock) -> None:
        org = Organization.objects.create(name="test org")
        user = User.objects.create(name="test user", organization=org)
        custom_meeting_type = MeetingType.objects.create(key="custom", name="Custom", everyone=True)
        preferences = user.preferences or {}
        preferences["default_meeting_type"] = str(custom_meeting_type.uuid)
        user.preferences = preferences
        user.save()
        event = ScheduledEvent.objects.create(
            user=user,
            start_time=timezone.now() + timedelta(minutes=30),
            end_time=timezone.now() + timedelta(minutes=90),
        )
        agent_config = AgentConfig.objects.update_or_create(
            owner=org,
            name="meetingtype_for_note",
            user_prompt="Test",
            version_notes="First version",
            user_prompt_metadata_example={"note_context": "test"},
            user_prompt_metadata_schema=["note_context"],
            system_prompt=None,
            system_prompt_metadata=None,
            output_validation_schema="list_subset",
            llm_model="gpt-4o-2024-08-06",
        )
        mock_agent_run.return_value.output = AgentOutputEnumeratedListSubset(items=[json.dumps({"uuid": "not a uuid"})])

        assert _infer_meeting_type(event) == str(custom_meeting_type.uuid)

    @patch("pydantic_ai.Agent.run_sync")
    def test_infer_meeting_type_success(self, mock_agent_run: MagicMock) -> None:
        org = Organization.objects.create(name="test org")
        user = User.objects.create(name="test user", organization=org)
        custom_meeting_type = MeetingType.objects.create(key="custom", name="Custom", everyone=True)
        custom_meeting_type_2 = MeetingType.objects.create(key="custom2", name="Custom2", everyone=True)
        preferences = user.preferences or {}
        preferences["default_meeting_type"] = str(custom_meeting_type.uuid)
        user.preferences = preferences
        user.save()
        event = ScheduledEvent.objects.create(
            user=user,
            start_time=timezone.now() + timedelta(minutes=30),
            end_time=timezone.now() + timedelta(minutes=90),
        )
        agent_config = AgentConfig.objects.update_or_create(
            owner=org,
            name="meetingtype_for_note",
            user_prompt="Test",
            version_notes="First version",
            user_prompt_metadata_example={"note_context": "test"},
            user_prompt_metadata_schema=["note_context"],
            system_prompt=None,
            system_prompt_metadata=None,
            output_validation_schema="list_subset",
            llm_model="gpt-4o-2024-08-06",
        )
        mock_agent_run.return_value.output = AgentOutputEnumeratedListSubset(
            items=[json.dumps({"uuid": str(custom_meeting_type_2.uuid)})]
        )

        assert _infer_meeting_type(event) == str(custom_meeting_type_2.uuid)
