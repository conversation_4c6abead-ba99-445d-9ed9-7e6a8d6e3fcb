from typing import Protocol
from uuid import UUID

from celery.result import AsyncResult


class BotProcessingTask(Protocol):
    """A protocol for Celery tasks that process transcript data for bots.

    This may not be the best way to add type safety to these tasks--the underlying task type has a
    lot more available operations--but trying to make the type annotations work correctly proved
    complex.
    """

    # Trying to specify a type for the AsyncResult caused issues while running the process. We don't
    # use the result (it's None, so the correct type here would be AsyncResult[None]), so it was
    # more expedient to ignore the type argument than to try to debug further.
    def delay_on_commit(self, bot_id: UUID, force_update: bool) -> AsyncResult:  # type: ignore[type-arg]
        ...
