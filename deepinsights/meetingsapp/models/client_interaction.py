from django.db import models
from simple_history.models import HistoricalRecords

from deepinsights.core.behaviours import StatusMixin, UUIDMixin


# An interaction with a client.
#
# In many cases, this will represent just a single meeting with a client. However, it may also
# represent several notes/meetings, all of which relate to the same interaction with a client.
# For example, an interaction may have a pre-meeting internal note, a client meeting note, and a
# follow-up debrief note.
class ClientInteraction(StatusMixin, UUIDMixin):
    history = HistoricalRecords(inherit=True)

    # Clients

    clients = models.ManyToManyField(
        to="meetingsapp.Client",
        blank=True,
        help_text="The clients who are involved in this interaction.",
        related_name="+",
    )

    # Structured data (pre-meeting)

    agenda = models.OneToOneField(
        "meetingsapp.StructuredMeetingData", on_delete=models.SET_NULL, null=True, blank=True, related_name="+"
    )
    client_prep = models.OneToOneField(
        "meetingsapp.StructuredMeetingData", on_delete=models.SET_NULL, null=True, blank=True, related_name="+"
    )
    advisor_notes = models.OneToOneField(
        "meetingsapp.StructuredMeetingData", on_delete=models.SET_NULL, null=True, blank=True, related_name="+"
    )

    # References to other data model/external entities

    meeting_type = models.ForeignKey(
        "meetingsapp.MeetingType",
        on_delete=models.PROTECT,
        help_text="The type of the meeting associated with this interaction.",
    )

    note = models.OneToOneField(
        to="meetingsapp.Note",
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        help_text="The note associated with this interaction.",
    )
