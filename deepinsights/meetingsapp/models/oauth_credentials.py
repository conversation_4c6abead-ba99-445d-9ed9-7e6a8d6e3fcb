from django.db import models
from django_stubs_ext.db.models import TypedModelMeta
from model_utils import Choices

from deepinsights.core.behaviours import UUIDMixin
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.user import User


class OAuthCredentials(UUIDMixin):
    """Stores information for user-level OAuth flows."""

    INTEGRATIONS = Choices("wealthbox", "microsoft", "google", "salesforce", "microsoft_dynamics", "advisor_engine")

    access_token = models.CharField(default="", null=False)
    refresh_token = models.CharField(default="")
    expires_in = models.DateTimeField()
    refresh_token_expires_in = models.DateTimeField()
    created_at = models.DateTimeField(null=True, blank=True)
    scope = models.CharField(default="")
    integration = models.CharField(choices=INTEGRATIONS, default="")
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True)

    class Meta(TypedModelMeta):
        verbose_name = "OAuth user credential"
        verbose_name_plural = "OAuth user credentials"


class OAuthClientCredentials(UUIDMixin):
    """Stores client credential information for organization-level OAuth flows."""

    class Providers(models.TextChoices):
        MICROSOFT = "microsoft", "Microsoft"

    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name="oauth_client_credentials")
    provider = models.CharField(max_length=50, choices=Providers.choices)
    tenant_id = models.CharField(
        max_length=255,
        help_text="The tenant ID of the provider entity/organization. For Microsoft, this is the Entra tenant ID, which is shown at https://entra.microsoft.com/#home",
    )

    class Meta(TypedModelMeta):
        unique_together = ("organization", "provider")
        verbose_name = "OAuth client credential"
        verbose_name_plural = "OAuth client credentials"

    def __str__(self) -> str:
        return f"{self.organization.name} - {self.provider}"
