from oauth2_provider.models import (
    AbstractAccessToken,
    AbstractApplication,
    AbstractGrant,
    AbstractIDToken,
    AbstractRefreshToken,
)

from deepinsights.core.behaviours import StatusMixin, UUIDMixin


class OAuthApplication(AbstractApplication, StatusMixin, UUIDMixin):
    """An OAuth2 application, used for third-party API access."""

    pass


class OAuthAccessToken(AbstractAccessToken, StatusMixin, UUIDMixin):
    """An OAuth2 access token, used for third-party API access."""

    pass


class OAuthRefreshToken(AbstractRefreshToken, StatusMixin, UUIDMixin):
    """An OAuth2 refresh token, used for third-party API access."""

    pass


class OAuthIDToken(AbstractIDToken, StatusMixin, UUIDMixin):
    """An OAuth2 ID token, used for third-party API access."""

    pass


class OAuthGrant(AbstractGrant, StatusMixin, UUIDMixin):
    """An OAuth2 grant, used for third-party API access."""

    pass
