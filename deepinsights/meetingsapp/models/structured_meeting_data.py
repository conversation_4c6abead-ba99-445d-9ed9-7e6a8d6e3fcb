import enum
from typing import Any

from django.db import models
from django.db.models import Q, QuerySet, TextChoices
from simple_history.models import HistoricalRecords

from deepinsights.core.behaviours import StatusMixin, UUIDMixin
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.prompt import Prompt
from deepinsights.meetingsapp.models.structured_meeting_data_schema import StructuredMeetingDataSchema
from deepinsights.users.models.user import User
from deepinsights.utils.choice_array_field import ChoiceArrayField


class StructuredMeetingDataTemplate(StatusMixin, UUIDMixin):
    """
    A template for Zeplyn-database-backed structured meeting data for a meeting.

    This is meant to contain structured meeting data in a representation that can be used to
    generate meeting "artifacts".

    It's assumed that users that have access to a meeting type that references this template have
    access to this template.
    """

    # Well-defined "kind"s of templates that we can use to identify the type of structured data.
    #
    # If you need to use a template "kind" in different places in the codebase, define a kind here,
    # rather than referencing a string constant in multiple places. The expected use case for these
    # is that we check them in the CRM sync code to determine how to map fields from the structured
    # data into specific CRM fields. There may be other use cases where having these values is useful,
    # but these are the ones that we are aware of today.
    class Kind(enum.StrEnum):
        BENTO_LIFE_EVENTS = "bento_life_events"
        SEQUOIA_COMPLIANCE_CHECKLIST = "compliance_checklist"

    title = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        help_text=(
            "The title of this structured data template. "
            "This is mainly for humans and will be shown to users in the app interface, but it is "
            "also conceivable that it could be passed to the language model as context."
        ),
    )

    internal_name = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        help_text=(
            "An internally-visible name used in the admin console to identify this type of structured data. "
            "This should never be shown to users; it is only for internal reference."
        ),
    )

    kind = models.CharField(
        blank=False,
        null=False,
        help_text=(
            "What 'kind' of structured meeting data this is."
            "\n\n"
            "This has two major uses (at this time):\n"
            "- to provide an identifier for shared templates that are generated by a database migration, and which we "
            "may need to reference in the future to modify (or remove )"
            "- to provide a way for us to sync data fields to specific fields in specific CRMs for specific templates"
            "This was originially designed as an escape valve to help us render structured meeting data in the frontend. "
            "However, over time we've come to release that using the schema.$id is a better way to do this, "
            "since that allows us to centralize on a limited number of schema types but with many different "
            "meeting data templates. Notably, we can't use the schema.$id in CRM sync workflows because the schema "
            "does not necessarily provide semantic information about the data fields, only the shape of the data."
        ),
    )

    schema_definition = models.ForeignKey(
        StructuredMeetingDataSchema,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Reference to the schema definition that will be used for validation.",
    )

    initial_data = models.JSONField(
        blank=True,
        null=True,
        help_text=(
            "The initial data that should be used when creating a structured meeting data from this template. "
            "This can be null, in which case there will be no default data and the schema will be "
            "used to determine any defaults (if any)."
        ),
    )

    prompt = models.ForeignKey(
        Prompt,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text=(
            "The prompt that should be passed to a language model when generating a structured meeting data from this template."
        ),
    )

    context = models.TextField(
        blank=True,
        null=True,
        help_text=(
            "Context that can be provided to a language model when using data from this object as an input."
            "This should be provided by an engineer on the team, based on an understanding of the customer's needs."
        ),
    )

    # Creates a StructuredMeetingData from this template, associated with a note and containing the given
    # data.
    def create_structured_meeting_data(
        self,
        note: Note,
        data: dict[str, Any],
        status: "StructuredMeetingData.Status | None" = None,
    ) -> "StructuredMeetingData":
        """
        Creates a StructuredMeetingData instance based on this template.

        Args:
            note: The Note object to associate with the structured meeting data
            data: The data to use for the structured meeting data

        Returns:
            A new StructuredMeetingData instance
        """
        schema = self.schema_definition.schema if self.schema_definition else None

        return StructuredMeetingData.objects.create(
            title=self.title,
            internal_name=self.internal_name,
            kind=self.kind,
            schema=schema,
            data=data,
            context=self.context,
            note=note,
            status=status or StructuredMeetingData.Status.COMPLETED,
        )

    def __str__(self) -> str:
        internal_name = self.internal_name or "<unnamed>"
        external_name = self.title or "<empty>"
        return f"{internal_name} (external name: {external_name})"


# A structured data item for a meeting (e.g., a compliance checklist)
#
# Note that this is not intended to represent tasks generated from the meeting (see the Tasks
# model), but instead typically a workflow that would follow all meetings regardless of the
# meeting's contents, based on the type of the meeting.
class StructuredMeetingData(StatusMixin, UUIDMixin):
    title = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        help_text=(
            "The title of this structured meeting data instance. "
            "This is mainly for humans and will be shown to users in the app interface, but it is "
            "also conceivable that it could be passed to the language model as context."
        ),
    )

    internal_name = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        help_text=(
            "An internally-visible name used in the admin console to identify this type of meeting. "
            "This should never be shown to users; it is only for internal reference."
        ),
    )

    kind = models.CharField(
        blank=False,
        null=False,
        help_text=(
            "What 'kind' of structured meeting data this is."
            "\n\n"
            "This has two major uses (at this time):\n"
            "- to provide an identifier for shared templates that are generated by a database migration, and which we "
            "may need to reference in the future to modify (or remove )"
            "- to provide a way for us to sync data fields to specific fields in specific CRMs for specific templates"
            "This was originially designed as an escape valve to help us render structured meeting data in the frontend. "
            "However, over time we've come to release that using the schema.$id is a better way to do this, "
            "since that allows us to centralize on a limited number of schema types but with many different "
            "meeting data templates. Notably, we can't use the schema.$id in CRM sync workflows because the schema "
            "does not necessarily provide semantic information about the data fields, only the shape of the data."
        ),
    )

    schema = models.JSONField(
        blank=True,
        null=True,
        help_text=(
            "The details of the data format for structured meeting data. This is intended to be a valid "
            "JSON schema (https://json-schema.org/), and may be used to generate a form on the frontend "
            "that will be used to update the data in the structured meeting data instance. In most cases, "
            "this should be non-null, but that is not enforced at the database level. If present, this "
            "will be used to validate the data field of a structured meeting data created from this template."
        ),
    )

    data = models.JSONField(
        blank=True,
        help_text=(
            "The data associated with this structured meeting data instance. "
            "If schema is non-null, it's expected that the data in this field will adhere to the schema "
            "(and will not contain extra fields not in the schema)."
        ),
    )

    note = models.ForeignKey(
        Note,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="The note this structured meeting data instance is associated with (or none).",
    )

    context = models.TextField(
        blank=True,
        null=True,
        help_text=(
            "Context that can be provided to a language model when using data from this object as an input."
            "This should be provided by an engineer on the team, based on an understanding of the customer's needs."
        ),
    )

    class Status(TextChoices):
        CREATED = "created", "Created"
        PROCESSING = "processing", "Processing"
        COMPLETED = "completed", "Completed"
        FAILED = "failed", "Failed"

    status = models.CharField(
        blank=False,
        null=False,
        choices=Status.choices,
        default=Status.COMPLETED,
        help_text=(
            "The status of the structured meeting data instance. This tracks the progress of the LLM data "
            "generation process."
        ),
    )

    history = HistoricalRecords()

    def __str__(self) -> str:
        internal_name = self.internal_name or "<unnamed>"
        external_name = self.title or "<empty>"
        return f"{internal_name} (external name: {external_name})"


# A rule that defines when to generate structured meeting data from a template for a meeting.
class StructuredMeetingDataTemplateRule(StatusMixin, UUIDMixin):
    follow_up_templates = models.ManyToManyField(
        to=StructuredMeetingDataTemplate,
        blank=False,
        help_text="The structured meeting data templates that should be associated with this rule.",
    )

    internal_name = models.TextField(
        blank=True,
        null=True,
        max_length=200,
        help_text=(
            "An internally-visible name used in the admin concole to identify this rule. "
            "This should never be shown to users; it is only for internal reference."
        ),
    )

    all_meetings = models.BooleanField(
        default=False,
        help_text=(
            "Whether this template rule should apply to all meetings (true) or only to specific "
            "meetings or meeting categories (false)."
        ),
    )

    meeting_types = models.ManyToManyField(
        to=MeetingType,
        blank=True,
        help_text="The meeting types this rule applies to. If this is empty, then this rule applies to no meeting types.",
    )

    meeting_categories = ChoiceArrayField(
        base_field=models.CharField(choices=MeetingType.Category.choices, blank=True),
        help_text=(
            "The meeting type categories for which this rule should be applied. "
            "See the definition of MeetingType.Category for more information about what these mean. "
            "If this is empty, then this rule applies to no meeting type categories."
        ),
        blank=True,
        default=list,
    )

    everyone = models.BooleanField(
        default=False,
        help_text=(
            "Whether this template rule should apply to everyone (true) or only to specific users or organizations (false). "
        ),
    )

    organizations = models.ManyToManyField(
        to=Organization,
        blank=True,
        help_text=(
            "The organizations to which this template rule applies. "
            "If this is empty, then no specific organizations will have this rule applied to their meetings. "
            "Users may still have this rule applied per the `users` or `everyone` fields."
        ),
    )

    users = models.ManyToManyField(
        to=User,
        blank=True,
        help_text=(
            "The users who have access to this type of meeting. "
            "If this is empty, then no specific users will have this rule applied to their meetings. "
            "Users may still have this rule applied per the `organizations` or `everyone` fields."
        ),
    )

    # Returns the StructuredMeetingDataTemplates that are relevant to the given user and meeting type.
    @classmethod
    def relevant_follow_up_templates(
        cls, meeting_type: MeetingType | None, user: User
    ) -> QuerySet[StructuredMeetingDataTemplate]:
        templates = (
            # Find rules that match the meeting type or category (if it exists)
            cls.objects.filter(
                Q(all_meetings=True)
                | Q(meeting_types__isnull=False) & Q(meeting_types=meeting_type)
                | Q(meeting_categories__contains=[meeting_type.category])
                if meeting_type
                else Q()
            )
            # Filter rules that don't apply to the user or organization.
            .filter(
                Q(everyone=True)
                | (Q(organizations__isnull=False) & Q(organizations=user.organization))
                | (Q(users__isnull=False) & Q(users=user))
            )
            # Remove duplicates due to overlapping matching filters.
            .distinct()
            # Get a list of all the templates.
            .values_list("follow_up_templates", flat=True)
        )

        # Return a list of templates with a consistent order.
        return StructuredMeetingDataTemplate.objects.filter(id__in=templates).order_by("id")

    def __str__(self) -> str:
        return self.internal_name or "<unnamed>"
