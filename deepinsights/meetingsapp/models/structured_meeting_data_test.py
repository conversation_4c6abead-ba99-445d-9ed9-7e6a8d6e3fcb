import pytest

from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.structured_meeting_data import (
    StructuredMeetingData,
    StructuredMeetingDataTemplate,
    StructuredMeetingDataTemplateRule,
)
from deepinsights.meetingsapp.models.structured_meeting_data_schema import StructuredMeetingDataSchema
from deepinsights.users.models.user import User

pytestmark = [pytest.mark.django_db]


@pytest.mark.parametrize(
    "data",
    [
        {"field": "value"},
        {},
    ],
)
def test_create_structured_meeting_data(data: dict[str, str]) -> None:
    note = Note.objects.create()

    # Create the schema definition first
    schema_definition = StructuredMeetingDataSchema.objects.create(
        name="test_schema",
        description="Test schema",
        schema={"type": "object", "properties": {"field": {"type": "string"}}},
    )

    template = StructuredMeetingDataTemplate.objects.create(
        title="Test Template",
        internal_name="Internal name",
        kind="test_kind",
        schema_definition=schema_definition,
        initial_data={"field": "value"},
        context="Test context",
    )

    structured_data = template.create_structured_meeting_data(note=note, data=data)

    assert structured_data.title == template.title
    assert structured_data.kind == template.kind
    assert structured_data.schema == schema_definition.schema  # Updated to check against schema_definition
    assert structured_data.data == data
    assert structured_data.context == template.context
    assert structured_data.note == note
    assert structured_data.internal_name == template.internal_name
    assert structured_data.status == StructuredMeetingData.Status.COMPLETED


@pytest.mark.parametrize(
    "status, expected_status",
    [
        (StructuredMeetingData.Status.PROCESSING, StructuredMeetingData.Status.PROCESSING),
        (StructuredMeetingData.Status.COMPLETED, StructuredMeetingData.Status.COMPLETED),
        (None, StructuredMeetingData.Status.COMPLETED),
    ],
)
def test_create_structured_meeting_data_with_status(
    status: StructuredMeetingData.Status, expected_status: StructuredMeetingData.Status
) -> None:
    note = Note.objects.create()

    schema_definition = StructuredMeetingDataSchema.objects.create(
        name="test_schema",
        description="Test schema",
        schema={"type": "object", "properties": {"field": {"type": "string"}}},
    )

    template = StructuredMeetingDataTemplate.objects.create(
        title="Test Template",
        internal_name="Internal name",
        kind="test_kind",
        schema_definition=schema_definition,
        initial_data={"field": "value"},
        context="Test context",
    )

    structured_data = template.create_structured_meeting_data(note=note, data={}, status=status)

    assert structured_data.status == expected_status


def test_relevant_follow_up_templates_for_organization() -> None:
    organization = Organization.objects.create(name="Test Organization")
    user = User.objects.create(username="testuser", organization=organization)
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", category=MeetingType.Category.CLIENT)

    template = StructuredMeetingDataTemplate.objects.create(
        title="Test Template",
        internal_name="Internal name",
        kind="test_kind",
    )

    rule = StructuredMeetingDataTemplateRule.objects.create(
        internal_name="Test Rule",
        everyone=False,
    )
    rule.meeting_types.add(meeting_type)
    rule.organizations.add(organization)
    rule.follow_up_templates.add(template)
    rule.save()

    relevant_templates = StructuredMeetingDataTemplateRule.relevant_follow_up_templates(meeting_type, user)

    assert list(relevant_templates) == [template]


def test_relevant_follow_up_templates_for_user() -> None:
    organization = Organization.objects.create(name="Test Organization")
    user = User.objects.create(username="testuser", organization=organization)
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", category=MeetingType.Category.CLIENT)

    template = StructuredMeetingDataTemplate.objects.create(
        title="Test Template",
        internal_name="Internal name",
        kind="test_kind",
    )

    rule = StructuredMeetingDataTemplateRule.objects.create(
        internal_name="Test Rule",
        everyone=False,
    )
    rule.meeting_types.add(meeting_type)
    rule.users.add(user)
    rule.follow_up_templates.add(template)
    rule.save()

    relevant_templates = StructuredMeetingDataTemplateRule.relevant_follow_up_templates(meeting_type, user)

    assert list(relevant_templates) == [template]


def test_relevant_follow_up_templates_for_everyone() -> None:
    user = User.objects.create(username="testuser")
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", category=MeetingType.Category.CLIENT)

    template = StructuredMeetingDataTemplate.objects.create(
        title="Test Template",
        internal_name="Internal name",
        kind="test_kind",
    )
    rule = StructuredMeetingDataTemplateRule.objects.create(
        internal_name="Test Rule",
        everyone=True,
    )
    rule.meeting_types.add(meeting_type)
    rule.follow_up_templates.add(template)
    rule.save()

    relevant_templates = StructuredMeetingDataTemplateRule.relevant_follow_up_templates(meeting_type, user)

    assert list(relevant_templates) == [template]


def test_relevant_follow_up_templates_for_meeting_category() -> None:
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", category=MeetingType.Category.CLIENT)
    user = User.objects.create(username="testuser")
    template = StructuredMeetingDataTemplate.objects.create(
        title="Test Template",
        internal_name="Internal name",
        kind="test_kind",
    )
    rule = StructuredMeetingDataTemplateRule.objects.create(
        internal_name="Test Rule",
        everyone=True,
        meeting_categories=[MeetingType.Category.CLIENT],
    )
    rule.follow_up_templates.add(template)
    rule.save()

    relevant_templates = StructuredMeetingDataTemplateRule.relevant_follow_up_templates(meeting_type, user)

    assert list(relevant_templates) == [template]


def test_relevant_follow_up_templates_multiple_templates() -> None:
    organization = Organization.objects.create(name="Test Organization")
    user = User.objects.create(username="testuser", organization=organization)
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", category=MeetingType.Category.CLIENT)

    template_one = StructuredMeetingDataTemplate.objects.create(
        title="Test Template 1",
        internal_name="Internal name 1",
        kind="test_kind",
    )

    template_two = StructuredMeetingDataTemplate.objects.create(
        title="Test Template 2",
        internal_name="Internal name 2",
        kind="test_kind",
    )

    StructuredMeetingDataTemplate.objects.create(
        title="Test Template 3",
        internal_name="Internal name 3",
        kind="test_kind",
    )

    rule_one = StructuredMeetingDataTemplateRule.objects.create(
        internal_name="Test Rule 1",
        everyone=False,
    )
    rule_one.meeting_types.add(meeting_type)
    rule_one.organizations.add(organization)
    rule_one.follow_up_templates.add(template_one)
    rule_one.save()

    rule_two = StructuredMeetingDataTemplateRule.objects.create(
        internal_name="Test Rule 2",
        everyone=False,
    )
    rule_two.meeting_types.add(meeting_type)
    rule_two.users.add(user)
    rule_two.follow_up_templates.add(template_two)
    rule_two.save()

    relevant_templates = StructuredMeetingDataTemplateRule.relevant_follow_up_templates(meeting_type, user)

    assert list(relevant_templates) == [template_one, template_two]


def test_relevant_follow_up_templates_multiple_meeting_types() -> None:
    organization = Organization.objects.create(name="Test Organization")
    user = User.objects.create(username="testuser", organization=organization)
    meeting_type_one = MeetingType.objects.create(name="Test Meeting Type 1", category=MeetingType.Category.CLIENT)
    meeting_type_two = MeetingType.objects.create(name="Test Meeting Type 2", category=MeetingType.Category.CLIENT)
    MeetingType.objects.create(name="Test Meeting Type 3", category=MeetingType.Category.CLIENT)

    template_one = StructuredMeetingDataTemplate.objects.create(
        title="Test Template 1",
        internal_name="Internal name 1",
        kind="test_kind",
    )

    template_two = StructuredMeetingDataTemplate.objects.create(
        title="Test Template 2",
        internal_name="Internal name 2",
        kind="test_kind",
    )

    StructuredMeetingDataTemplate.objects.create(
        title="Test Template 3",
        internal_name="Internal name 3",
        kind="test_kind",
    )

    rule = StructuredMeetingDataTemplateRule.objects.create(
        internal_name="Test Rule 1",
        everyone=False,
    )
    rule.meeting_types.set([meeting_type_one, meeting_type_two])
    rule.organizations.add(organization)
    rule.follow_up_templates.set([template_one, template_two])
    rule.save()

    relevant_templates = StructuredMeetingDataTemplateRule.relevant_follow_up_templates(meeting_type_one, user)

    assert list(relevant_templates) == [template_one, template_two]

    relevant_templates = StructuredMeetingDataTemplateRule.relevant_follow_up_templates(meeting_type_two, user)

    assert list(relevant_templates) == [template_one, template_two]


def test_relevant_follow_up_templates_overlapping_user_organization_everyone_access() -> None:
    organization = Organization.objects.create(name="Test Organization")
    user = User.objects.create(username="testuser", organization=organization)
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", category=MeetingType.Category.CLIENT)

    template = StructuredMeetingDataTemplate.objects.create(
        title="Test Template 1",
        internal_name="Internal name 1",
        kind="test_kind",
    )

    rule = StructuredMeetingDataTemplateRule.objects.create(
        internal_name="Test Rule 2",
        everyone=True,
    )
    rule.meeting_types.add(meeting_type)
    rule.users.add(user)
    rule.organizations.add(organization)
    rule.follow_up_templates.add(template)
    rule.save()

    relevant_templates = StructuredMeetingDataTemplateRule.relevant_follow_up_templates(meeting_type, user)

    assert list(relevant_templates) == [template]


@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_relevant_follow_up_templates_async() -> None:
    organization = await Organization.objects.acreate(name="Test Organization")
    user = await User.objects.acreate(username="testuser", organization=organization)
    meeting_type = await MeetingType.objects.acreate(name="Test Meeting Type", category=MeetingType.Category.CLIENT)

    template = await StructuredMeetingDataTemplate.objects.acreate(
        title="Test Template 1",
        internal_name="Internal name 1",
        kind="test_kind",
    )

    rule = await StructuredMeetingDataTemplateRule.objects.acreate(
        internal_name="Test Rule 2",
        everyone=True,
    )
    await rule.meeting_types.aadd(meeting_type)
    await rule.users.aadd(user)
    await rule.organizations.aadd(organization)
    await rule.follow_up_templates.aadd(template)
    await rule.asave()

    relevant_templates = StructuredMeetingDataTemplateRule.relevant_follow_up_templates(meeting_type, user)

    assert list([t async for t in relevant_templates]) == [template]


def test_relevant_follow_up_templates_multiple_rules() -> None:
    organization = Organization.objects.create(name="Test Organization")
    user = User.objects.create(username="testuser", organization=organization)
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", category=MeetingType.Category.CLIENT)

    template_one = StructuredMeetingDataTemplate.objects.create(
        title="Test Template 1",
        internal_name="Internal name 1",
        kind="test_kind",
    )

    template_two = StructuredMeetingDataTemplate.objects.create(
        title="Test Template 2",
        internal_name="Internal name 2",
        kind="test_kind",
    )

    StructuredMeetingDataTemplate.objects.create(
        title="Test Template 3",
        internal_name="Internal name 3",
        kind="test_kind",
    )

    rule_one = StructuredMeetingDataTemplateRule.objects.create(
        internal_name="Test Rule 1",
        everyone=False,
    )
    rule_one.meeting_types.add(meeting_type)
    rule_one.organizations.add(organization)
    rule_one.follow_up_templates.set([template_one, template_two])
    rule_one.save()

    rule_two = StructuredMeetingDataTemplateRule.objects.create(
        internal_name="Test Rule 2",
        everyone=False,
    )
    rule_two.meeting_types.add(meeting_type)
    rule_two.users.add(user)
    rule_two.follow_up_templates.set([template_one, template_two])
    rule_two.save()

    relevant_templates = StructuredMeetingDataTemplateRule.relevant_follow_up_templates(meeting_type, user)

    assert list(relevant_templates) == [template_one, template_two]


def test_relevant_follow_up_templates_rule_without_meeting_type_or_category() -> None:
    organization = Organization.objects.create(name="Test Organization")
    user = User.objects.create(username="testuser", organization=organization)
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", category=MeetingType.Category.CLIENT)

    template = StructuredMeetingDataTemplate.objects.create(
        title="Test Template 1",
        internal_name="Internal name 1",
        kind="test_kind",
    )

    rule = StructuredMeetingDataTemplateRule.objects.create(
        internal_name="Test Rule 2",
        everyone=False,
    )
    rule.organizations.add(organization)
    rule.follow_up_templates.add(template)
    rule.save()

    assert not StructuredMeetingDataTemplateRule.relevant_follow_up_templates(meeting_type, user).exists()


def test_relevant_follow_up_templates_no_meeting_type_provided() -> None:
    organization = Organization.objects.create(name="Test Organization")
    user = User.objects.create(username="testuser", organization=organization)

    template_one = StructuredMeetingDataTemplate.objects.create(
        title="Test Template 1",
        internal_name="Internal name 1",
        kind="test_kind",
    )

    template_two = StructuredMeetingDataTemplate.objects.create(
        title="Test Template 2",
        internal_name="Internal name 2",
        kind="test_kind",
    )

    StructuredMeetingDataTemplate.objects.create(
        title="Test Template 3",
        internal_name="Internal name 3",
        kind="test_kind",
    )

    rule_one = StructuredMeetingDataTemplateRule.objects.create(
        internal_name="Test Rule 1",
        everyone=False,
    )
    rule_one.organizations.add(organization)
    rule_one.follow_up_templates.set([template_one, template_two])
    rule_one.save()

    rule_two = StructuredMeetingDataTemplateRule.objects.create(
        internal_name="Test Rule 2",
        everyone=False,
    )
    rule_two.users.add(user)
    rule_two.follow_up_templates.set([template_one, template_two])
    rule_two.save()

    StructuredMeetingDataTemplateRule.objects.create(
        internal_name="Test Rule 3",
        everyone=False,
    )

    relevant_templates = StructuredMeetingDataTemplateRule.relevant_follow_up_templates(None, user)

    assert list(relevant_templates) == [template_one, template_two]


def test_relevant_follow_up_templates_rule_all_meetings() -> None:
    organization = Organization.objects.create(name="Test Organization")
    user = User.objects.create(username="testuser", organization=organization)
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", category=MeetingType.Category.CLIENT)

    template = StructuredMeetingDataTemplate.objects.create(
        title="Test Template 1",
        internal_name="Internal name 1",
        kind="test_kind",
    )

    rule = StructuredMeetingDataTemplateRule.objects.create(
        internal_name="Test Rule 2",
        everyone=False,
        all_meetings=True,
    )
    rule.organizations.add(organization)
    rule.follow_up_templates.add(template)
    rule.save()

    relevant_templates = StructuredMeetingDataTemplateRule.relevant_follow_up_templates(meeting_type, user)
    assert list(relevant_templates) == [template]


def test_relevant_follow_up_templates_rule_all_meetings_and_meeting_types() -> None:
    organization = Organization.objects.create(name="Test Organization")
    user = User.objects.create(username="testuser", organization=organization)
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", category=MeetingType.Category.CLIENT)
    other_meeting_type = MeetingType.objects.create(name="Other Meeting Type", category=MeetingType.Category.CLIENT)

    template = StructuredMeetingDataTemplate.objects.create(
        title="Test Template 1",
        internal_name="Internal name 1",
        kind="test_kind",
    )

    rule = StructuredMeetingDataTemplateRule.objects.create(
        internal_name="Test Rule 2",
        everyone=False,
        all_meetings=True,
    )
    rule.organizations.add(organization)
    rule.meeting_types.add(other_meeting_type)
    rule.follow_up_templates.add(template)
    rule.save()

    relevant_templates = StructuredMeetingDataTemplateRule.relevant_follow_up_templates(meeting_type, user)
    assert list(relevant_templates) == [template]


def test_relevant_follow_up_templates_rule_all_meetings_and_meeting_categories() -> None:
    organization = Organization.objects.create(name="Test Organization")
    user = User.objects.create(username="testuser", organization=organization)
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", category=MeetingType.Category.CLIENT)

    template = StructuredMeetingDataTemplate.objects.create(
        title="Test Template 1",
        internal_name="Internal name 1",
        kind="test_kind",
    )

    rule = StructuredMeetingDataTemplateRule.objects.create(
        internal_name="Test Rule 2",
        everyone=False,
        all_meetings=True,
        meeting_categories=[MeetingType.Category.INTERNAL],
    )
    rule.organizations.add(organization)
    rule.follow_up_templates.add(template)
    rule.save()

    relevant_templates = StructuredMeetingDataTemplateRule.relevant_follow_up_templates(meeting_type, user)
    assert list(relevant_templates) == [template]
