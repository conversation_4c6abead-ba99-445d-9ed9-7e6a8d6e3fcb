from django.db import models

from deepinsights.core.behaviours import StatusMixin, UUIDMixin


class AudioBuffer(StatusMixin, UUIDMixin):
    note = models.ForeignKey(
        "meetingsapp.Note",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="audio_buffers",
        help_text="The note this audio buffer is associated with.",
    )
    data = models.BinaryField(blank=False, null=False, default=b"", help_text="The audio data for this chunk.")
    sequence = models.IntegerField(
        blank=False,
        null=False,
        default=-1,
        help_text=(
            "The sequence number of this chunk."
            "This is used to order the chunks when generating a combined audio file."
        ),
    )
    duration = models.IntegerField(
        blank=True,
        null=True,
        help_text=(
            "The duration of this chunk in seconds. "
            "This is used for generating a M3U8 playlist for chunks that are HLS stream chunks."
        ),
    )
    mime_type = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text="The MIME type of the audio data (e.g., audio/webm;codecs=opus, audio/mp4).",
    )
    nonce = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text=(
            "A nonce that can be used to match related buffers when there are multiple sets of buffers associated "
            "with a note. This is useful for cases where the user starts recording an audio stream, then resets and "
            "starts recording again."
        ),
    )
