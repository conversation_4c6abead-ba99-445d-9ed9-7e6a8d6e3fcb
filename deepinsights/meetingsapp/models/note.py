import datetime
import logging
import os
import textwrap
from dataclasses import dataclass
from typing import TYPE_CHECKING, Any, BinaryIO, Iterable, List, Literal, Mapping
from uuid import UUID

import humanize
import markdown
import pydantic
from dataclasses_json import dataclass_json
from django.conf import settings
from django.db import models
from django_jsonform.models.fields import <PERSON><PERSON>y<PERSON>ield
from liquid import Template
from model_utils import Choices
from simple_history.models import HistoricalRecords

from deepinsights.core.aws import AWS
from deepinsights.core.behaviours import StatusMixin, UUIDMixin
from deepinsights.core.integrations.crm.crm_models import CRMSyncItemSelection, CRMSyncSection, CRMSyncTargets
from deepinsights.meetingsapp.models.audio_buffer import AudioBuffer
from deepinsights.users.models.user import User

if TYPE_CHECKING:
    from api.routers.note_models import SummarySection as SummarySectionModel
    from deepinsights.meetingsapp.models.attendees import Attendee
    from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent


@dataclass_json
@dataclass
class SummarySection:
    # this is a conversion from summary to a dataclass
    topic: str
    bullets: list[str]

    def as_text(self) -> str:
        rich_text = f"\n{self.topic}\n"
        for bullet in self.bullets:
            rich_text += f" • {bullet}\n"
        return rich_text


@dataclass_json
@dataclass
class Summary:
    # this is a conversion from summary to a dataclass
    sections: list[SummarySection]

    def find_section_by_topic(self, topic: str) -> str:
        for section in self.sections:
            if topic.lower() in section.topic.lower():
                return section.as_text()
        return ""

    def find_section_index_by_topic(self, topic: str) -> int | None:
        for index, section in enumerate(self.sections):
            if topic.lower() in section.topic.lower():
                return index
        return None

    def as_text(self: "Summary") -> str:
        """Convert the summary to a text format.

        Returns:
            A string representation of the summary with all sections.
        """
        rich_text = ""
        for section in self.sections:
            rich_text += section.as_text()
        return rich_text


def generate_section_from_string(section_string: str) -> SummarySection:
    # this is a conversion from summary_by_topics to a dataclass
    topic, text = section_string.split(":", maxsplit=1)
    topic = topic.strip(" 1234567890.*#")
    bullets = [x.strip(" -*") for x in text.strip().split("\n")]
    return SummarySection(topic=topic, bullets=bullets)


class CRMSyncTargetInfo(pydantic.BaseModel):
    """Represents the state of a CRM sync resolution workflow for a note.

    Some CRM implementations may require the user to select a specific entity related to a client
    rather than being able to fully infer where to sync based on a client. This model is used to
    store the status of that resolution workflow, so that the user can be prompted to select the
    "next" entity in the resolution tree.
    """

    # The sync targets information provided by a CRM.
    sync_targets: CRMSyncTargets | None = None


class Note(StatusMixin, UUIDMixin):
    NOTE_TYPE = Choices("meeting_recording", "voice_memo")
    PROCESSING_STATUS = Choices("uploaded", "processed", "missing", "scheduled", "finalized")
    FEATURES = Choices("remap_speakers")

    class DataSource(models.TextChoices):
        RECALL = "recall", "Recall"
        TWILIO = "twilio", "Twilio"
        AUDIO_FILE = "audio_file", "Audio File"
        AUDIO_BUFFERS = "audio_buffers", "Audio Buffers"

    # s3 file path
    file_path = models.CharField(null=True, blank=True)
    file_type = models.CharField(max_length=255, null=True, blank=True)
    note_type = models.CharField(choices=NOTE_TYPE, null=True, blank=True)
    metadata = models.JSONField(null=True, blank=True)
    status = models.CharField(choices=PROCESSING_STATUS, null=True, blank=True)
    data_source = models.CharField(choices=DataSource.choices, max_length=255, null=True, blank=True)
    note_owner = models.ForeignKey(User, on_delete=models.CASCADE, null=True)
    authorized_users = models.ManyToManyField(User, related_name="authorized_notes", blank=True)
    diarized_trans_with_names = models.CharField(null=True, blank=True)
    raw_transcript = models.CharField(null=True, blank=True)
    advisor_notes = ArrayField(models.CharField(null=True, blank=True), null=True, blank=True)
    key_takeaways = ArrayField(models.CharField(null=True, blank=True), null=True, blank=True)
    summary_by_topics = ArrayField(models.CharField(null=True, blank=True), null=True, blank=True)
    summary = models.JSONField(default=dict, null=True, blank=True)
    raw_asr_response = models.JSONField(null=True, blank=True)
    salesforce_case_id = models.CharField(max_length=255, null=True, blank=True)
    follow_up_email_contents = models.JSONField(null=True, blank=True)
    meeting_type = models.ForeignKey("MeetingType", on_delete=models.PROTECT, null=True, blank=True)

    client = models.JSONField(null=True, blank=True)
    crm_sync_targets = models.JSONField(
        null=True,
        blank=True,
        help_text=(
            "Where information about this note should be synced in the CRM. Not that this may contain "
            "a 'partial'/'unresolved' set of targets, which may require further disambiguation from "
            "the user in order to be used by the CRM as a final sync target. In other words, this may not "
            "represent exactly where the note will be synced, until the note is actually synced and finalized, "
            "at which point the value will represent where it was synced."
        ),
    )
    history = HistoricalRecords(inherit=True)

    def __str__(self) -> str:
        return f"{self.title()}"

    def get_summary(self) -> Summary:
        return Summary.from_dict(self.summary)  # type: ignore[attr-defined, no-any-return]

    def title(self) -> str:
        if self.metadata:
            return self.metadata.get("meeting_name", "<No Title>")  # type: ignore[no-any-return]
        return "Err:Contact support:No Metadata"

    @property
    def scheduled_at(self) -> str | None:
        if self.metadata:
            return self.metadata.get("scheduled_at", None)  # type: ignore[no-any-return]
        return None

    @property
    def bot_uuid(self) -> UUID | None:
        # Using `all()` here allows us to benefit from any prefetching done.
        bots = self.meetingbot_set.all()
        if not bots or len(bots) == 0:
            return None
        return bots[0].uuid

    @property
    def features(self) -> list[str]:
        """
        Determine the available features for this note based on its current state.

        This property checks the note's attributes and related objects to decide which
        features should be enabled. Currently, it only checks for the ability to remap
        speakers, but it can be expanded to include other features in the future.

        Returns:
            list[str]: A list of enabled feature names from the FEATURES Choices.
                       Currently, it may include:
                       - 'remap_speakers': If speaker remapping is available for this note.
        """
        features = []

        # Speaker remapping is only available if:
        # - the note has a raw_transcript
        # - the note is not finalized
        if self.raw_transcript and self.status != self.PROCESSING_STATUS.finalized:
            features.append(self.FEATURES.remap_speakers)

        return features

    @property
    def category(self) -> Any:
        return (
            self.meeting_type.category
            if self.meeting_type
            else self.metadata.get("meeting_type", "client")
            if self.metadata
            else "client"
        )

    def get_timestring_from_seconds(self, seconds):  # type: ignore[no-untyped-def]
        # converts seconds into a string of format Hh Mm Ss
        td = datetime.timedelta(seconds=seconds)
        return humanize.precisedelta(td, minimum_unit="seconds")

    def should_include_section(
        self, sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None, section: CRMSyncSection
    ) -> bool:
        if sync_items is None:
            return True  # Default to including all sections if sync_items not provided

        section_config = sync_items.get(section)
        if section_config is None:
            return True  # Default to including if section not specified

        if isinstance(section_config, CRMSyncItemSelection):
            return section_config.include_section

        return True  # Default fallback

    def get_tasks_to_include_crm_sync(
        self, sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None
    ) -> Iterable[Any]:  # circular import with Task
        included_tasks = sync_items.get(CRMSyncSection.TASKS) if sync_items else None
        if not included_tasks or included_tasks.included_items is None:
            return self.task_set.all()

        return self.task_set.filter(uuid__in=included_tasks.included_items)

    def _generate_summary(  # type: ignore[no-untyped-def]
        self,
        *,
        custom_template: str | None,
        title: str,
        user: User | None,
        use_html_formatting=False,
        sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = None,
    ) -> str:
        include_meeting_details = self.should_include_section(sync_items, CRMSyncSection.MEETING_DETAILS)
        include_attendees = self.should_include_section(sync_items, CRMSyncSection.ATTENDEES)
        include_keywords = self.should_include_section(sync_items, CRMSyncSection.KEYWORDS)
        include_tasks = self.should_include_section(sync_items, CRMSyncSection.TASKS)
        include_key_takeaways = self.should_include_section(sync_items, CRMSyncSection.KEY_TAKEAWAYS)
        include_advisor_notes = self.should_include_section(sync_items, CRMSyncSection.ADVISOR_NOTES)
        include_summary = self.should_include_section(sync_items, CRMSyncSection.SUMMARY)

        meeting_details = ""
        if include_meeting_details:
            if self.metadata.get("meeting_duration"):  # type: ignore[union-attr]
                meeting_details += "**Meeting Duration:** " + self.get_timestring_from_seconds(  # type: ignore[no-untyped-call]
                    int(self.metadata.get("meeting_duration", "0"))  # type: ignore[union-attr]
                )
            is_client_meeting = self.category == "client"
            meeting_details += "\n\nConsent was received for notetaking" if is_client_meeting else ""

        attendees = ""
        if include_attendees:
            attendees = ", ".join(self.get_attendees())
            attendees = attendees or "Attendees not noted"

        keywords = ""
        if include_keywords:
            keywords = ", ".join(self.metadata.get("tags", []))  # type: ignore[union-attr]
            keywords = keywords or "No keywords"

        def markdown_list(strings: list[str], default: str) -> str:
            item_list = [f"- {item}" for item in (strings or [])]
            return ("\n" + "\n".join(item_list)) if item_list else default

        action_items = ""
        if include_tasks:
            action_items = markdown_list(
                [task.task_title for task in self.get_tasks_to_include_crm_sync(sync_items=sync_items)],
                "No action items",
            )

        key_takeaways = ""
        if include_key_takeaways:
            key_takeaways = markdown_list(self.key_takeaways, "No key takeaways")

        advisor_notes = ""
        if include_advisor_notes:
            advisor_notes = markdown_list(self.advisor_notes, "No advisor notes")

        summary = ""
        if include_summary:
            if self.summary:
                try:
                    for section in self.summary["sections"]:
                        topic, bullets = section["topic"], section["bullets"]
                        summary += "\n" + topic + "\n\n"
                        for bullet in bullets:
                            summary += "- " + bullet + "\n"
                except Exception:
                    logging.error("Error while parsing summary sections for note: %s", self.uuid, exc_info=True)
            else:
                logging.warning("No summary sections found for note: %s", self.uuid)
            has_summary = True if summary else False
            summary = summary or "No summary available for meeting"

        certification_prefix = _generate_certification_text(user)

        template_context = {
            "title": title,
            "certification_prefix": certification_prefix,
            "meeting_details": meeting_details,
            "has_meeting_details": include_meeting_details and bool(meeting_details),
            "attendees": attendees,
            "has_attendees": include_attendees and bool(attendees),
            "keywords": keywords,
            "has_keywords": include_keywords and bool(keywords),
            "action_items": action_items,
            "has_action_items": include_tasks and bool(action_items and action_items != "No action items"),
            "key_takeaways": key_takeaways,
            "has_key_takeaways": include_key_takeaways and bool(key_takeaways and key_takeaways != "No key takeaways"),
            "advisor_notes": advisor_notes,
            "has_advisor_notes": include_advisor_notes and bool(advisor_notes and advisor_notes != "No advisor notes"),
            "summary": summary,
            "has_summary": include_summary and bool(summary and summary != "No summary available for meeting"),
        }

        default_template = """\
        # {{title}}
        {%- if certification_prefix != "" %}

        {{certification_prefix}}
        {%- endif %}
        {%- if has_meeting_details %}

        ## Meeting details

        {{meeting_details}}
        {%- endif %}
        {%- if has_attendees %}

        **Attendees:** {{attendees}}
        {%- endif %}
        {%- if has_keywords %}

        **Keywords:** {{keywords}}
        {%- endif %}
        {%- if has_action_items %}

        **Action Items**
        {{action_items}}
        {%- endif %}
        {%- if has_key_takeaways %}

        **Key Takeaways**
        {{key_takeaways}}
        {%- endif %}
        {%- if has_advisor_notes %}

        **Advisor Notes**
        {{advisor_notes}}
        {%- endif %}
        {%- if has_summary %}

        ## Meeting Summary
        {{summary}}
        {%- endif %}
        """

        template = textwrap.dedent(custom_template or default_template)

        # Render the template using Liquid.
        renderer = Template(template)
        # Remove all filters; we don't want custom templates to use filters, only simple replacement.
        renderer.env.filters = {}
        # Ignore unknown filters; we don't want the rendering to crash.
        renderer.env.strict_filters = False
        markdown_content = renderer.render(**template_context)

        if not use_html_formatting:
            return markdown_content

        # Render the Markdown to HTML, ensuring that line breaks are turned into HTML <br>s.
        return markdown.markdown(markdown_content, extensions=["nl2br"])

    def get_summary_for_crm(
        self, use_html_formatting: bool, sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = None
    ) -> str:
        title = f"**Notes from {self.metadata.get('meeting_name')} on {self.created.date().isoformat()}**"  # type: ignore[union-attr]
        user = self.note_owner
        custom_template = user.get_crm_configuration().summary_template  # type: ignore[union-attr]

        return self._generate_summary(
            custom_template=custom_template,
            title=title,
            user=user,
            use_html_formatting=use_html_formatting,
            sync_items=sync_items,
        )

    def get_summary_for_email(self, user: User, subject: str) -> str:
        custom_template = user.get_preferences().email_settings.meeting_notes_email_template
        base_summary = self._generate_summary(
            custom_template=custom_template, title=subject, user=user, use_html_formatting=True
        )
        try:
            view_link_html = f'<p><a href="{self.public_url()}" style="color: #0066cc; text-decoration: none; font-weight: bold;">📝 View Notes in Zeplyn</a></p>'
            if base_summary.startswith("<h1>"):
                title_end = base_summary.find("</h1>") + 5
                modified_summary = (
                    base_summary[:title_end] + "\n\n" + view_link_html + "\n\n" + base_summary[title_end:]
                )
            else:
                modified_summary = view_link_html + "\n\n" + base_summary

            return modified_summary
        except Exception as e:
            logging.error("Failed to add view link to email summary for note %s: %s", self.uuid, str(e), exc_info=True)
            return base_summary

    def get_attendees(self) -> List[str]:
        return [attendee.get_name() for attendee in self.attendees.all()]

    def get_attendees_by_type(
        self, attendee_iterator: Iterable["Attendee"] | None = None
    ) -> dict[Literal["clients", "advisors"], str]:
        """
        Return the list of attendees separated by type (clients vs advisors) in the meeting.

        Returns:
            dict[Literal["clients", "advisors"], str]: A dictionary containing:
                - 'clients': Comma-separated string of client names, or "None" if no clients
                - 'advisors': Comma-separated string of advisor names, or "None" if no advisors
        """
        attendees = attendee_iterator or self.attendees.all().order_by("-speaker_percentage")

        client_attendees = []
        advisor_attendees = []

        for attendee in attendees:
            if attendee.client:
                client_attendees.append(attendee.get_name())
            else:
                advisor_attendees.append(attendee.get_name())

        clients_str = ", ".join(client_attendees) if client_attendees else "None"
        advisors_str = ", ".join(advisor_attendees) if advisor_attendees else "None"

        return {"clients": clients_str, "advisors": advisors_str}

    def _s3_key(
        self, note_id: str, note_type: str, creation_time: datetime.datetime, user_id: str, file_type: str
    ) -> str:
        try:
            return os.path.join(user_id, note_type, creation_time.strftime("%Y-%m-%d"), f"{note_id}.{file_type}")
        except Exception as e:
            logging.error("Could not generate S3 upload key for note %s", note_id, exc_info=e)
            raise e

    # Saves the audio data to S3 and updates the note with the file path.
    def save_with_audio_data(
        self,
        user: User,
        note_type: str,
        creation_time: datetime.datetime,
        file_type: str,
        audio_data: BinaryIO,
    ) -> None:
        note_id = self.uuid
        s3_key = self._s3_key(str(note_id), note_type, creation_time, str(user.uuid), file_type)
        self.file_path = os.path.join(settings.AWS_S3_FILE_FOLDER, s3_key)
        self.file_type = file_type
        self.status = Note.PROCESSING_STATUS.uploaded
        logging.info("Note updated with audio data information: %s", note_id)
        AWS().upload_file(self.file_path, audio_data)
        logging.info("Note audio data upload finished: %s", note_id)
        self.save()

    # Returns a new temporary download URL for the audio data associated with this note.
    def temporary_s3_download_url(self) -> str | None:
        if not self.file_path:
            return None
        return AWS().get_temp_url(self.file_path)

    # Deletes media associated with this note in S3 (and audio buffers in the database)
    def delete_media(self) -> None:
        # Always delete audio buffers if they exist.
        AudioBuffer.objects.filter(note=self).delete()
        logging.info("Audio buffers deleted for note: %s", self.uuid)
        if not self.file_path:
            logging.warning("Requested media deletion for note without any associated media: %s", self.uuid)
            return None
        AWS().delete_s3_file(self.file_path)
        self.file_path = None
        self.save()
        logging.info("Deleted media for note: %s", self.uuid)

    async def add_to_summary(self, summary_section: "SummarySectionModel", index_to_replace: int | None = None) -> int:
        try:
            if not self.summary:
                self.summary = {"sections": []}

            new_summary_section = SummarySection(topic=summary_section.topic, bullets=summary_section.bullets)
            summary = Summary.from_dict(self.summary)  # type: ignore[attr-defined]

            new_index = None
            if index_to_replace is not None and 0 <= index_to_replace < len(summary.sections):
                summary.sections[index_to_replace] = new_summary_section
                new_index = index_to_replace
            else:
                # fall back to previous append behavior
                summary.sections.append(new_summary_section)
            self.summary = summary.to_dict()
            await self.asave()
            return new_index if new_index is not None else len(summary.sections) - 1
        except Exception as e:
            raise Exception(f"Failed to add summary section to note: {e}") from e

    def has_client_interaction(self) -> bool:
        return hasattr(self, "clientinteraction")

    # A URL that can be used to access this note via the frontend.
    def public_url(self) -> str:
        return f"{settings.APP_DOMAIN}/notes/{self.uuid}"

    @property
    def scheduled_event(self) -> "ScheduledEvent | None":
        return self._scheduled_event if hasattr(self, "_scheduled_event") else None


def is_authorized_to_view_note(user: User, note: Note) -> bool:
    return (
        user == note.note_owner
        or user in note.authorized_users.all()
        or user.is_superuser
        or (
            (user.license_type == User.LicenseType.csa or user.license_type == User.LicenseType.staff)
            and (user.organization == note.note_owner.organization if note.note_owner else False)
        )
    )


def get_agenda_template_content(note: Note) -> str:
    if note.meeting_type and note.meeting_type.agenda_templates.exists():
        agenda_template = note.meeting_type.agenda_templates.first()
        if agenda_template is not None and agenda_template.initial_data is not None:
            content = agenda_template.initial_data.get("content")
            return str(content) if content is not None else ""
    return ""


def _generate_certification_text(user: User | None) -> str:
    if not user:
        return ""

    sync_preferences = user.get_preferences().sync_preferences
    if sync_preferences.advisor_certification_required:
        timestamp_with_tz = datetime.datetime.now(datetime.timezone.utc).strftime("%Y-%m-%d %H:%M:%S %Z")
        certification_text = sync_preferences.advisor_certification_synced_text.format(
            user_email=user.email, timestamp_with_tz=timestamp_with_tz
        )
        return certification_text

    return ""
