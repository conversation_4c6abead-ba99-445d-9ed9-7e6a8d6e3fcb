from django.db import models

from deepinsights.core.behaviours import StatusMixin, UUIDMixin


class SearchQuery(StatusMixin, UUIDMixin):
    requestor = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        related_name="search_queries",
        help_text="The user who made the search query",
    )
    query = models.TextField(help_text="The query asked by the user")
    structured_response = models.JSONField(help_text="The structured output in Summary Section format")

    # Assets this query was connected to.
    notes = models.ManyToManyField(
        "meetingsapp.Note",
        related_name="search_queries",
        help_text="The notes this search query was connected to",
    )
