import logging
from json import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rror
from typing import Any, Iterable, Literal
from uuid import UUID

from django.db import models
from pydantic import ValidationError
from simple_history.models import HistoricalRecords

from deepinsights.core.behaviours import StatusMixin, UUIDMixin
from deepinsights.core.integrations.crm.crm_models import CRMUser, CRMWorkflow
from deepinsights.meetingsapp.models.note import Note
from deepinsights.ml_agents.models.agent_config import (
    AgentInput,
    AgentOutputEnumeratedListSubset,
    ListMetadata,
    expect_list_items,
    get_agent_config_and_run_agent,
)
from deepinsights.users.models.user import User


class Task(StatusMixin, UUIDMixin):
    task_title = models.CharField(blank=False)
    task_desc = models.TextField(null=True, blank=True)
    completed = models.BooleanField(default=False, blank=True)
    due_date = models.DateTimeField(null=True)
    task_owner = models.ForeignKey(User, on_delete=models.CASCADE, null=True)
    note = models.ForeignKey(Note, on_delete=models.CASCADE, null=True)
    metadata = models.JSONField(null=True, blank=True, default=dict)
    assignee = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name="assigned_tasks")
    history = HistoricalRecords(inherit=True)
    workflow_type_raw = models.JSONField(blank=True, null=True, default=dict)
    crm_assignee = models.JSONField(blank=True, null=True, default=dict)

    @property
    def possible_assignees(self) -> Iterable[User]:
        return self.task_owner.organization.users.all() if self.task_owner and self.task_owner.organization else []

    @property
    def workflow_type(self) -> CRMWorkflow | None:
        if self.workflow_type_raw:
            try:
                return CRMWorkflow.model_validate(self.workflow_type_raw)
            except Exception:
                logging.error("Could not construct CRMWorkflow from database workflow_type_raw")
        return None

    def get_crm_assignee(self) -> CRMUser | None:
        if self.crm_assignee:
            try:
                return CRMUser.model_validate(self.crm_assignee)
            except Exception:
                logging.error("Could not construct CRMUser from database crm_assignee")
        return None

    def populate_workflow_type(self) -> CRMWorkflow | None:
        if self.workflow_type:
            # don't populate if already exists
            return self.workflow_type

        if not self.note or not self.note.note_owner or not self.note.note_owner.organization:
            return None
        # Get list of workflow types from API
        permitted_items = [
            x.model_dump_json() for x in self.note.note_owner.crm_handler.fetch_workflows_for_user(self.note.note_owner)
        ]
        if not permitted_items:
            return None
        # Call LLM to determine correct workflow type for task
        from deepinsights.core.ml.prompt_builder import PromptContext  # circular import fix

        prompt_context = PromptContext(
            self.note
        )  # TODO(anne): want to deprecate PromptContext in future, but this is an incremental change
        context = prompt_context._get_meeting_context()
        context += f" The task title is: {self.task_title}. "
        context += f"The potential workflows to select from are: {permitted_items}. "

        prompt_input = AgentInput(transcript=self.note.diarized_trans_with_names, note_context=context)

        # Call agent, does Pydantic validation on the way out automatically
        with expect_list_items(ListMetadata(max_items=1, permitted_items=permitted_items)):
            result = get_agent_config_and_run_agent(
                agent_owner=self.note.note_owner.organization, agent_name="workflow_for_task", agent_input=prompt_input
            )

        if not isinstance(result, AgentOutputEnumeratedListSubset):
            logging.error(
                "Agent output was of wrong type - internal misconfiguration of AgentConfig for workflow_for_task"
            )
            return None

        if not result.items:
            logging.info("No item was returned from LLM for task %s", self.uuid)
            return None

        try:
            result_workflow = CRMWorkflow.model_validate_json(result.items[0])
            # save in raw field for future use
            self.workflow_type_raw = result_workflow.model_dump_json()
            self.save()
            return result_workflow
        except (ValidationError, JSONDecodeError) as ve:
            logging.error("Couldn't get workflow type for task %s", self.uuid, exc_info=ve)
        return None

    def __str__(self) -> str:
        return str(self.task_title)

    def get_completed(self) -> Literal["complete", "incomplete"]:
        return "complete" if self.completed else "incomplete"

    def get_parent_note_uuid(self) -> UUID | None:
        return self.note.uuid if self.note else None

    def save(self, *args: Any, **kwargs: Any) -> None:
        if not self.assignee:
            self.assignee = self.task_owner
        super().save(*args, **kwargs)
