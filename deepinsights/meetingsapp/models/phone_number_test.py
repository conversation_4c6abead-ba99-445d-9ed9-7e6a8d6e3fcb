import pytest
from django.db import IntegrityError

from deepinsights.meetingsapp.models.phone_number import PhoneNumber
from deepinsights.users.models.user import User

pytestmark = pytest.mark.django_db(transaction=True)


class TestPhoneNumberMeta:
    def test_unique_user_primary_phone_number_constraint(self, django_user_model: User) -> None:
        user = django_user_model.objects.create(username="testuser")
        PhoneNumber.objects.create(user=user, number="+12125551212", primary=True)

        with pytest.raises(IntegrityError, match="unique_user_primary_phone_number"):
            PhoneNumber.objects.create(user=user, number="+12125551213", primary=True)
        assert user.phone_numbers.count() == 1

    def test_multiple_non_primary_phone_numbers_allowed(self, django_user_model: User) -> None:
        user = django_user_model.objects.create(username="testuser")
        PhoneNumber.objects.create(user=user, number="+12125551212", primary=False)
        PhoneNumber.objects.create(user=user, number="+12125551213", primary=False)

        assert user.phone_numbers.count() == 2
        assert not user.phone_numbers.filter(primary=True).exists()

    def test_one_primary_and_multiple_non_primary_allowed(self, django_user_model: User) -> None:
        user = django_user_model.objects.create(username="testuser")
        PhoneNumber.objects.create(user=user, number="+12125551212", primary=True)
        PhoneNumber.objects.create(user=user, number="+12125551213", primary=False)
        PhoneNumber.objects.create(user=user, number="+12125551214", primary=False)

        assert user.phone_numbers.count() == 3
        assert user.phone_numbers.filter(primary=True).count() == 1

    def test_multiple_primary_phone_numbers_for_different_users_allowed(self, django_user_model: User) -> None:
        user1 = django_user_model.objects.create(username="testuser1")
        user2 = django_user_model.objects.create(username="testuser2")
        PhoneNumber.objects.create(user=user1, number="+12125551212", primary=True)
        PhoneNumber.objects.create(user=user2, number="+12125551213", primary=True)

        assert user1.phone_numbers.filter(primary=True).count() == 1
        assert user2.phone_numbers.filter(primary=True).count() == 1

    def test_constraint_not_enforced_for_null_user(self) -> None:
        PhoneNumber.objects.create(user=None, number="+12125551212", primary=True)
        PhoneNumber.objects.create(user=None, number="+12125551213", primary=True)

        assert PhoneNumber.objects.filter(user=None, primary=True).count() == 2

    def test_constraint_only_applies_when_primary_is_true(self, django_user_model: User) -> None:
        user = django_user_model.objects.create(username="testuser")
        PhoneNumber.objects.create(user=user, number="+12125551212", primary=False)
        PhoneNumber.objects.create(user=user, number="+12125551213", primary=False)

        # No IntegrityError should be raised
        assert user.phone_numbers.filter(primary=False).count() == 2
        assert user.phone_numbers.filter(primary=True).count() == 0

    def test_one_primary_and_multiple_non_primary_allowed_via_reverse_relationship(
        self, django_user_model: User
    ) -> None:
        user = django_user_model.objects.create(username="testuser")
        user.phone_numbers.add(
            PhoneNumber.objects.create(number="+12125551212", primary=True),
            PhoneNumber.objects.create(number="+12125551213", primary=False),
            PhoneNumber.objects.create(number="+12125551214", primary=False),
        )

        assert user.phone_numbers.count() == 3
        assert user.phone_numbers.filter(primary=True).count() == 1

    def test_unique_user_primary_phone_number_constraint_via_reverse_relationship(
        self, django_user_model: User
    ) -> None:
        user = django_user_model.objects.create(username="testuser")
        user.phone_numbers.add(PhoneNumber.objects.create(number="+12125551212", primary=True))

        with pytest.raises(IntegrityError, match="unique_user_primary_phone_number"):
            user.phone_numbers.add(PhoneNumber.objects.create(number="+12125551213", primary=True))
        assert user.phone_numbers.count() == 1
