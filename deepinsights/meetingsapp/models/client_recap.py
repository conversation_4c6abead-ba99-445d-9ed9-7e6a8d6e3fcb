import logging
from enum import StrEnum
from typing import Any, Literal
from uuid import UUID

from django.db import models
from simple_history.models import HistoricalRecords

from deepinsights.core.behaviours import UUIDMixin
from deepinsights.core.integrations.crm.wealthbox import Wealthbox
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


class ClientRecapStatus(StrEnum):
    UNKNOWN = "unknown"
    CREATED = "created"
    PROCESSING = "processing"
    PROCESSED = "processed"
    FAILED = "failed"

    @classmethod
    def _missing_(cls, value: object) -> "ClientRecapStatus":
        return cls.UNKNOWN


class ClientRecap(UUIDMixin):
    summary = models.JSONField(default=dict, null=True, blank=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True)
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    history = HistoricalRecords(inherit=True)
    status = models.CharField(max_length=255, default="created")

    def __get_crm_link(self, user: User, source: str | None, meeting_uuid: UUID) -> str | None:
        if not source:
            return None
        match source.lower():
            case "wealthbox":
                user_crm_id = Wealthbox().get_user_account_by_self(user)
                return f"https://app.crmworkspace.com/{user_crm_id}/status_updates/{meeting_uuid}"
            case "redtail":
                return f"https://crm.redtailtechnology.com/contacts/{self.client.crm_id}/activities/{meeting_uuid}"
            case "salesforce":
                salesforce_config = user.get_crm_configuration().salesforce
                if not (base_url := salesforce_config.salesforce_endpoint):
                    logging.error(
                        "Salesforce endpoint not found in CRM configuration for user %s",
                        user.uuid,
                    )
                    return None
                base_url = base_url.rstrip("/")
                # Salesforce generally appears to be quite good at resolving its IDs, even without context.
                return f"{base_url}/{meeting_uuid}"
        return None

    def __create_reference(self, reference: dict[str, Any]) -> dict[str, Any] | None:
        source: str | None = reference.get("source")
        meeting_uuid: UUID = reference.get("meeting_uuid")  # type: ignore[assignment]
        if source == "Zeplyn":
            summary: str = reference.get("summary")  # type: ignore[assignment]
            if not (note := Note.objects.filter(uuid=meeting_uuid).first()):
                logging.warning("Note not found for meeting_uuid: %s. Not returning a reference", meeting_uuid)
                return None
            return {
                "source": "Zeplyn",
                "meeting_uuid": meeting_uuid,
                "link": f"{note.public_url()}?tab=summary&topic={note.get_summary().find_section_index_by_topic(summary)}",
                "hover_text": note.get_summary().find_section_by_topic(summary),
            }

        if link := reference.get("reference_link"):
            return {"source": source, "meeting_uuid": meeting_uuid, "link": link}

        # Fall back to the old behavior, to handle cases where only the source and the meeting_uuid
        # are stored in the recap
        if self.user and (link := self.__get_crm_link(self.user, source, meeting_uuid)):
            return {"source": source, "meeting_uuid": meeting_uuid, "link": link}

        return None

    def __get_all_references(self) -> list[dict[str, Any]]:
        references: list[dict[str, Any]] = []
        reference_map = {}  # Maps meeting_uuid + summary to reference index

        # First pass: collect all unique references
        for category in (self.summary or {}).get("recap", []):
            for bullet in category.get("bullets", []):
                for reference in bullet.get("references", []):
                    ref_uuid = reference.get("meeting_uuid")
                    if not ref_uuid:
                        continue

                    # Create a unique key based on meeting_uuid and summary (for Zeplyn sources)
                    summary = reference.get("summary", "")
                    ref_key = f"{ref_uuid}_{hash(summary) if summary else ''}"

                    if ref_key not in reference_map:
                        ref = self.__create_reference(reference)
                        if not ref:
                            logging.info(f"Reference not found for meeting_uuid: {ref_uuid}")
                            continue

                        # Add the summary hash to the reference for easier lookup later
                        if summary:
                            ref["summary_hash"] = hash(summary)

                        # Store in our references list and track its index
                        references.append(ref)
                        reference_map[ref_key] = len(references) - 1

        return references

    def __get_reference_index(self, meeting_uuid, references, summary=None):  # type: ignore[no-untyped-def]
        if not meeting_uuid:
            return None

        # For Zeplyn sources with summary, match on both meeting_uuid and summary_hash
        if summary:
            summary_hash = hash(summary)
            for i, item in enumerate(references):
                if item["meeting_uuid"] == meeting_uuid and item.get("summary_hash") == summary_hash:
                    return i

        # If we don't have a summary or didn't find a match with the summary,
        # just match on meeting_uuid (for CRM references)
        for i, item in enumerate(references):
            if item["meeting_uuid"] == meeting_uuid:
                return i

        return None

    def get_client_recap_summary(self) -> dict[Literal["client_recap_summary", "references", "created_at"], Any]:
        references = self.__get_all_references()
        client_recap_summary = [
            {
                "topic": category.get("topic"),
                "bullets": [
                    {
                        "text": bullet.get("text"),
                        "references": [
                            reference_index
                            for reference in bullet.get("references", [])
                            if (
                                reference_index := self.__get_reference_index(  # type: ignore[no-untyped-call]
                                    reference.get("meeting_uuid"),
                                    references=references,
                                    summary=reference.get("summary"),
                                )
                            )
                            is not None
                        ],
                    }
                    for bullet in category.get("bullets", [])
                ],
            }
            for category in (self.summary or {}).get("recap", [])
        ]
        return {"client_recap_summary": client_recap_summary, "references": references, "created_at": self.created}
