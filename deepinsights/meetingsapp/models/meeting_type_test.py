import pytest

from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.user import User


@pytest.fixture
def test_user(django_user_model: User, test_org: Organization) -> User:
    return django_user_model.objects.create(username="testuser", organization=test_org)


@pytest.fixture
def test_org() -> Organization:
    return Organization.objects.create(name="Test Organization")


@pytest.mark.django_db
class TestMeetingType:
    def test_for_user_no_access(self, test_user: User, django_user_model: User) -> None:
        MeetingType.objects.all().delete()  # Clear existing meeting types

        test_user.organization = None
        test_user.save()

        org_two = Organization.objects.create(name="Org Two")
        user_two = django_user_model.objects.create(username="user_two", organization=org_two)
        org_two_type = MeetingType.objects.create(
            key="org_two_type",
            name="Org Two Type",
            internal_name="Org Two Type Internal",
            everyone=False,
        )
        org_two_type.organizations.add(org_two)
        user_two_type = MeetingType.objects.create(
            key="user_two_type",
            name="User Two Type",
            internal_name="User Two Type Internal",
            everyone=False,
        )
        user_two_type.users.add(user_two)

        inaccessible_type = MeetingType.objects.create(
            key="no_access_type",
            name="No Access Type",
            internal_name="No Access Type Internal",
            everyone=False,
        )

        assert MeetingType.for_user(test_user).count() == 0

    def test_for_user_with_access(self, test_user: User, test_org: Organization) -> None:
        MeetingType.objects.all().delete()  # Clear existing meeting types

        org_accessible_type = MeetingType.objects.create(
            key="accessible_type",
            name="Org Accessible Type",
            internal_name="Org Accessible Type Internal",
            everyone=False,
        )
        org_accessible_type.organizations.add(test_org)

        user_accessible_type = MeetingType.objects.create(
            key="user_accessible_type",
            name="User Accessible Type",
            internal_name="User Accessible Type Internal",
            everyone=False,
        )
        user_accessible_type.users.add(test_user)

        everyone_type = MeetingType.objects.create(
            key="everyone_type",
            name="Everyone Type",
            internal_name="Everyone Type Internal",
            everyone=True,
        )

        inaccessible_type = MeetingType.objects.create(
            key="inaccessible_type",
            name="Inaccessible Type",
            internal_name="Inaccessible Type Internal",
            everyone=False,
        )

        assert set(MeetingType.for_user(test_user)) == {
            org_accessible_type,
            user_accessible_type,
            everyone_type,
        }

    def test_distinct(self, test_user: User, test_org: Organization, django_user_model: User) -> None:
        MeetingType.objects.all().delete()  # Clear existing meeting types

        org_two = Organization.objects.create(name="Org Two")
        user_two = django_user_model.objects.create(username="user_two", organization=test_org)
        org_accessible_type = MeetingType.objects.create(
            key="org_accessible_type",
            name="Org Accessible Type",
            everyone=True,
        )
        org_accessible_type.organizations.set([test_org, org_two])
        org_accessible_type.save()

        user_accessible_type = MeetingType.objects.create(
            key="user_accessible_type",
            name="User Accessible Type",
            everyone=True,
        )
        user_accessible_type.users.set([test_user, user_two])
        user_accessible_type.save()

        assert MeetingType.for_user(test_user).count() == 2
