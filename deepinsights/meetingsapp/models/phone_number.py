from django.db import models
from phonenumber_field.modelfields import PhoneNumberField
from phonenumber_field.phonenumber import PhoneNumber as PhoneNumberType

from deepinsights.core.behaviours import StatusMixin, UUIDMixin


class PhoneNumber(StatusMixin, UUIDMixin):
    class Type(models.TextChoices):
        mobile = "mobile", "Mobile"
        home = "home", "Home"
        office = "office", "Office"
        other = "other", "Other"

    type = models.CharField(
        choices=Type.choices, max_length=255, null=True, blank=True, help_text="What type of phone number this is"
    )
    primary = models.BooleanField(
        default=False,
        help_text="Whether this is the primary phone number for the related entity",
    )
    number: models.Field[PhoneNumberType | str | None, PhoneNumberType | None] = PhoneNumberField(
        blank=True,
        null=True,
        help_text=(
            "The phone number."
            "\n\n"
            "This field is somewhat intelligent: if you pass in an invalid phone number it will return an error. "
            "If you provide a valid US phone number (without country code), it will automatically add the country "
            "code. If you provide in a valid phone number with a country code, it will use that country code."
            "\n\n"
            "Note that the number is stored and rendered in E.164 format (e.g., +12125551212), regardless of how "
            "you enter it."
        ),
    )
    user = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        related_name="phone_numbers",
        null=True,
        blank=True,
    )

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["user"],
                condition=models.Q(primary=True),
                name="unique_user_primary_phone_number",
                violation_error_message="A user can only have one primary phone number.",
            ),
        ]
