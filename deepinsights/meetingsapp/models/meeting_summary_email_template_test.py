from unittest.mock import MagicMock, patch

import pytest

from deepinsights.meetingsapp.models.meeting_summary_email_template import (
    MeetingSummaryEmailTemplate,
    get_default_generation_prompt,
)


@pytest.mark.django_db
class TestGetDefaultGenerationPrompt:
    """Test cases for the get_default_generation_prompt function."""

    def test_returns_prompt_when_template_exists(self) -> None:
        """Test that the function returns the generation_prompt when the template exists."""
        expected_prompt = "Test default generation prompt"
        MeetingSummaryEmailTemplate.objects.create(
            name="Auto Populate Template",
            internal_name="autopopulate_email_prompt",
            generation_prompt=expected_prompt,
        )

        result = get_default_generation_prompt()

        assert result == expected_prompt

    def test_returns_empty_string_when_template_not_found(self) -> None:
        """Test that the function returns empty string when no template with the internal_name exists."""
        # Ensure no templates with the target internal_name exist
        MeetingSummaryEmailTemplate.objects.filter(internal_name="autopopulate_email_prompt").delete()

        result = get_default_generation_prompt()

        assert result == ""

    @patch("deepinsights.meetingsapp.models.meeting_summary_email_template.logging.error")
    @patch(
        "deepinsights.meetingsapp.models.meeting_summary_email_template." "MeetingSummaryEmailTemplate.objects.filter"
    )
    def test_handles_exception_gracefully(self, mock_filter: MagicMock, mock_log_error: MagicMock) -> None:
        """Test that the function handles exceptions gracefully and logs the error."""
        mock_filter.side_effect = Exception("Database error")

        result = get_default_generation_prompt()

        assert result == ""
        mock_log_error.assert_called_once_with("Error getting default generation prompt", exc_info=True)
