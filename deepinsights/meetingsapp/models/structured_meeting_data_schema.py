from django.db import models
from simple_history.models import HistoricalRecords

from deepinsights.core.behaviours import StatusMixin, UUIDMixin


class StructuredMeetingDataSchema(StatusMixin, UUIDMixin):
    """
    Model to store and version JSON schemas that can be used by other models
    for structured data validation.
    """

    name = models.CharField(
        max_length=200,
        unique=True,
        help_text=(
            "Name of the schema. This is used for reference and displayed in the admin interface. This is also"
            "used as a key to reference the schema in the code, so it must be unique and cannot be changed."
        ),
    )
    description = models.TextField(
        blank=True, null=True, help_text="Description of what this schema is used for and any special considerations."
    )
    schema = models.JSONField(help_text="The JSON schema definition that will be used for validation.")
    history = HistoricalRecords()

    def __str__(self) -> str:
        return self.name or "Unnamed schema"
