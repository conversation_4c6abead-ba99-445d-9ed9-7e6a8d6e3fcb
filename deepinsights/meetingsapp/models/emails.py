from dataclasses import dataclass

from dataclasses_json import dataclass_json

from deepinsights.core.preferences.dataclass_mixins import ZeplynDataclassJsonMixin


@dataclass_json
@dataclass
class Email(ZeplynDataclassJsonMixin):
    body: str = None  # type: ignore[assignment]
    subject: str = None  # type: ignore[assignment]
    to: str = None  # type: ignore[assignment]
    cc: list[str] = None  # type: ignore[assignment]
    bcc: list[str] = None  # type: ignore[assignment]
    email_checksum: str = None  # type: ignore[assignment]
