import json
import uuid

from django.core.exceptions import ValidationError
from django.test import TestCase

from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.user import User


class AttendeeModelTestCase(TestCase):
    def setUp(self):  # type: ignore[no-untyped-def]
        self.organization = Organization.objects.create()
        self.user = User.objects.create(
            name="testuser",
            email="<EMAIL>",
            organization=self.organization,
        )
        self.test_client = Client.objects.create(name="testclient", organization=self.organization)
        self.test_client.authorized_users.add(self.user)
        self.test_client.save()
        self.note = Note.objects.create(metadata={}, note_owner=self.user)

    def test_attendee_creation(self):  # type: ignore[no-untyped-def]
        attendee = Attendee.objects.create(note=self.note, attendee_name="<PERSON>")
        self.assertEqual(attendee.get_name(), "<PERSON>")
        self.assertEqual(attendee.get_attendee_type(), Attendee.AttendeeType.UNKNOWN.value)

        attendee.client = self.test_client
        attendee.attendee_name = None
        attendee.save()
        self.assertEqual(attendee.get_name(), self.test_client.name)
        self.assertEqual(attendee.get_attendee_type(), Attendee.AttendeeType.CLIENT.value)

        attendee.user = self.user
        attendee.client = None
        attendee.save()
        self.assertEqual(attendee.get_name(), self.user.name)
        self.assertEqual(attendee.get_attendee_type(), Attendee.AttendeeType.USER.value)

    def test_attendee_clean(self):  # type: ignore[no-untyped-def]
        attendee = Attendee(note=self.note, attendee_name=None, client=None, user=None)
        with self.assertRaises(ValidationError):
            attendee.clean()

        attendee.client = self.test_client
        attendee.user = self.user
        with self.assertRaises(ValidationError):
            attendee.clean()

    def test_parse_attendees_json(self):  # type: ignore[no-untyped-def]
        uuid_str = str(uuid.uuid4())
        json_data = json.dumps([{"name": "John Doe", "type": "client", "uuid": uuid_str}])
        attendees = Attendee.parse_attendees(json_data)  # type: ignore[no-untyped-call]
        self.assertEqual(len(attendees), 1)
        self.assertEqual(attendees[0]["name"], "John Doe")
        self.assertEqual(attendees[0]["type"], "client")

    def test_parse_attendees_string(self):  # type: ignore[no-untyped-def]
        string_data = "John Doe, Jane Doe"
        attendees = Attendee.parse_attendees(string_data)  # type: ignore[no-untyped-call]
        self.assertEqual(len(attendees), 2)
        self.assertEqual(attendees[0]["name"], "John Doe")
        self.assertEqual(attendees[0]["type"], "unknown")
        self.assertEqual(attendees[1]["name"], "Jane Doe")
        self.assertEqual(attendees[1]["type"], "unknown")

    def test_reconcile_attendees(self):  # type: ignore[no-untyped-def]
        # case 1: simple first time addition
        attendees_data = [
            {"name": "testclient", "type": "client", "uuid": str(self.test_client.uuid)},
            {"name": "testuser", "type": "user", "uuid": str(self.user.uuid)},
        ]
        Attendee.reconcile_attendees(self.note, attendees_data)
        self.assertEqual(Attendee.objects.filter(note=self.note).count(), 2)

        attendee_client = Attendee.objects.get(note=self.note, client=self.test_client)
        self.assertEqual(attendee_client.get_name(), "testclient")
        self.assertEqual(attendee_client.get_attendee_type(), Attendee.AttendeeType.CLIENT.value)

        attendee_user = Attendee.objects.get(note=self.note, user=self.user)
        self.assertEqual(attendee_user.get_name(), "testuser")
        self.assertEqual(attendee_user.get_attendee_type(), Attendee.AttendeeType.USER.value)

        # case 2: addition with additional attendee(s)
        test_name = "Rob Kiyo"
        attendees_data.append({"name": test_name, "type": "unknown", "uuid": "123e4567-e89b-12d3-a456-************"})

        Attendee.reconcile_attendees(self.note, attendees_data)
        self.assertEqual(Attendee.objects.filter(note=self.note).count(), 3)

        attendee_robert = Attendee.objects.filter(note=self.note, attendee_name=test_name).first()
        self.assertIsNotNone(attendee_robert)
        if attendee_robert:
            self.assertEqual(attendee_robert.get_name(), test_name)
            self.assertEqual(attendee_robert.get_attendee_type(), Attendee.AttendeeType.UNKNOWN.value)

        # case 3: remove attendee(s)
        attendees_data.pop()
        Attendee.reconcile_attendees(self.note, attendees_data)
        self.assertEqual(Attendee.objects.filter(note=self.note).count(), 2)
