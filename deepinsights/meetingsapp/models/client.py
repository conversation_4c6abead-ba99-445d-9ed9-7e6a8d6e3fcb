from django.db import models
from django.db.models.functions import Upper
from django_stubs_ext.db.models import TypedModelMeta
from phonenumber_field.modelfields import PhoneNumberField
from phonenumber_field.phonenumber import PhoneNumber

from deepinsights.core.behaviours import StatusMixin, UUIDMixin
from deepinsights.meetingsapp.models import organization
from deepinsights.users.models.user import User


class Client(UUIDMixin, StatusMixin):
    name = models.CharField()
    first_name = models.CharField(null=True, blank=True)
    last_name = models.CharField(null=True, blank=True)
    job_title = models.CharField(null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    phone_number: models.Field[PhoneNumber | str | None, PhoneNumber | None] = PhoneNumberField(
        blank=True,
        null=True,
        help_text=(
            "A phone number for the client."
            "\n\n"
            "This field is somewhat intelligent: if you pass in an invalid phone number it will return an error. "
            "If you provide a valid US phone number (without country code), it will automatically add the country "
            "code. If you provide in a valid phone number with a country code, it will use that country code."
            "\n\n"
            "Note that the number is stored and rendered in E.164 format (e.g., +12125551212), regardless of how "
            "you enter it."
        ),
    )
    organization = models.ForeignKey(organization.Organization, on_delete=models.CASCADE)
    crm_id = models.CharField(max_length=100, null=True, blank=True)
    client_type = models.CharField(max_length=100, default="individual")
    crm_system = models.CharField(max_length=100, default="")
    authorized_users = models.ManyToManyField(User, related_name="authorized_clients")
    owner = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name="owned_clients")
    assets_under_management = models.DecimalField(
        max_digits=20,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=(
            "The total assets under management for this client, in the base currency of the client's organization."
        ),
    )
    onboarding_date = models.DateField(
        null=True,
        blank=True,
        help_text=(
            "The date when the client was onboarded to the organization. Note that this is note the date the "
            "client was created in Zeplyn, but rather the date that the firm onboarded the client."
        ),
    )

    class Segment(models.TextChoices):
        bronze = "bronze", "Bronze"
        silver = "silver", "Silver"
        gold = "gold", "Gold"
        platinum = "platinum", "Platinum"

    segment = models.CharField(
        max_length=100,
        choices=Segment.choices,
        null=True,
        blank=True,
        help_text="The market segment this client belongs to, e.g., 'retail', 'institutional', etc.",
    )

    class LifePhase(models.TextChoices):
        accumulation = "accumulation", "Accumulation"
        consolidation = "consolidation", "Consolidation"
        distribution = "distribution", "Distribution"
        retirement = "retirement", "Retirement"

    life_phase = models.CharField(
        max_length=100,
        choices=LifePhase.choices,
        null=True,
        blank=True,
        help_text="The life phase of the client, e.g., 'accumulation', 'consolidation', 'distribution', etc.",
    )

    is_priority = models.BooleanField(
        default=False,
        help_text=(
            "Whether this client is a priority client for the organization. This can be used to flag "
            "clients that require special attention or service."
        ),
    )

    def __str__(self) -> str:
        return self.name

    class Meta(TypedModelMeta):
        indexes = [
            models.Index(Upper("email"), name="client_email_upper_index"),
        ]
