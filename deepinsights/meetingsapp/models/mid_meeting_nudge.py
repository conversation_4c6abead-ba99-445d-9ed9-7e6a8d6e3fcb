from django.db import models

from deepinsights.core.behaviours import UUIDMixin
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


class MidMeetingNudgeStatus(models.TextChoices):
    # Key =  value, admin dashboard display name
    SUCCESS = "success", "Success"
    NOT_PROCESSED = "not-processed", "Not Processed"
    PROCESSING = "processing", "Processing"
    ERROR = "error", "Error"
    PROCESSED = (
        "processed",
        "Processed, Not Sent",
    )  # used when all topics were covered already, we do call LLM but don't nudge
    SKIPPED = (
        "skipped",
        "Processing Skipped, Not Sent",
    )  # used when the meeting is too short to nudge for, we don't call LLM


class MidMeetingNudge(UUIDMixin):
    user = models.ForeignKey(User, on_delete=models.CASCADE, help_text="The host of the meeting to nudge")
    note = models.ForeignKey(Note, on_delete=models.CASCADE, help_text="The meeting note we're trying to nudge about")
    status = models.CharField(
        choices=MidMeetingNudgeStatus.choices,
        default=MidMeetingNudgeStatus.NOT_PROCESSED,
        max_length=20,
        help_text="The status of us sending the nudge",
    )
    scheduled_time = models.DateTimeField(help_text="The scheduled time to send the nudge.")
    sent_time = models.DateTimeField(null=True, blank=True, help_text="The time the nudge was actually sent.")
