import datetime
import logging
from enum import StrEnum
from typing import Any
from uuid import UUID

import phonenumbers
from asgiref.sync import sync_to_async
from django.conf import settings
from django.core.exceptions import SynchronousOnlyOperation
from django.db import models
from pydantic import BaseModel
from simple_history.models import HistoricalRecords

from deepinsights.core.behaviours import StatusMixin, UUIDMixin
from deepinsights.core.integrations.meetingbot.bot_controller import (
    BotController,
    BotMeetingType,
    BotResponse,
    BotStatus,
)
from deepinsights.core.integrations.meetingbot.recall_ai import RecallBotController
from deepinsights.core.integrations.meetingbot.twilio import TwilioCallBotController, TwilioConferenceBotController
from deepinsights.core.preferences.preferences import BotPreferences
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.bot_processing_task import BotProcessingTask
from deepinsights.users.models.user import User


class MeetingBot(StatusMixin, UUIDMixin):
    recall_bot_id = models.CharField(
        null=True,
        blank=True,
        help_text=(
            "The ID of the bot. For historical reasoncs, this is called 'recall_bot_id', but it is "
            "in fact the ID of the bot/notetaker in the relevant system (e.g., Twilio, Recall)."
        ),
    )
    meeting_link = models.CharField(
        null=True,
        blank=True,
        help_text=(
            "The link to the meeting that the bot will join. This may mean different things depending "
            "on the bot provider; it is essentially an identifier that tells the bot provider how to "
            "access a meeting."
        ),
    )
    bot_owner = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    note = models.ForeignKey("Note", on_delete=models.CASCADE, null=True, blank=True)

    history = HistoricalRecords(inherit=True)

    class LinkType(StrEnum):
        NONE = "none"
        PHONE_NUMBER = "phone_number"
        VIDEO_CALL = "video_call"

    # Returns the type of the meeting link and, if it's a phone number, the parsed phone number.
    def link_type(self) -> tuple[LinkType, phonenumbers.PhoneNumber | None]:
        if not self.meeting_link:
            return self.LinkType.NONE, None

        # If the meeting link is longer than 15 characters (the maximum length for an E.164 number,
        # which is the format we standardize on though not the only one that could be provided
        # here), or if it has a forward slash, it's almost certainly not a phone number. HOWEVER,
        # phonenumbers.parse is permissive by design, attempting to extract whatever might be a
        # phone number from the provided text, leaving validation as a later step; e.g., parsing
        # "https://zoom.us/j/7156907842" will return a phone number instance, which then can be
        # validated with `phonenumbers.is_valid_number`.
        if len(self.meeting_link) > 15 or "/" in self.meeting_link:
            return self.LinkType.VIDEO_CALL, None

        try:
            if cleaned_phone_number := phonenumbers.parse(self.meeting_link, "US"):
                # If the phone number is not valid, then assume this is a Recall meeting. This is
                # not a likely scenario given the pre checks above, but it's possible.
                if not phonenumbers.is_valid_number(cleaned_phone_number):
                    return self.LinkType.VIDEO_CALL, None
                return self.LinkType.PHONE_NUMBER, cleaned_phone_number
        except phonenumbers.phonenumberutil.NumberParseException:
            pass
        # It's not a phone number, so it's a Recall meeting.
        return self.LinkType.VIDEO_CALL, None

    # Returns the BotController that should be used for this meeting bot.
    def _bot_controller(self) -> BotController:
        try:
            # Check if we're in an async context by performing a synchronous database query.
            MeetingBot.objects.get(pk=self.pk)
        except SynchronousOnlyOperation:
            # If so, log an error in prod and raise an error in debug mode.
            logging.error(
                "_bot_controller() called from async context, which is not supported. This may cause crashes.",
                exc_info=True,
            )
            if settings.DEBUG:
                raise

        link_type, phone_number = self.link_type()
        if link_type == self.LinkType.NONE or link_type == self.LinkType.VIDEO_CALL:
            return RecallBotController(self.recall_bot_id or "")

        # Twilio SIDs start with "CA" for calls and "CF" for conferences. Based on information
        # inferred from Twilio's blog posts, it seems reasonably safe to assume that this is a
        # stable pattern.
        if self.recall_bot_id:
            if self.recall_bot_id.startswith("CA"):
                return TwilioCallBotController(self.recall_bot_id)
            if self.recall_bot_id.startswith("CF"):
                return TwilioConferenceBotController(self.recall_bot_id)

        # If we don't have a phone number for the user, then we can't call the user at the provided
        # phone number. Therefore, this is a single-party call on the Twilio side.
        if not self.bot_owner or not self.bot_owner.phone_numbers.exists():
            return TwilioCallBotController(self.recall_bot_id or "")

        # If one of the user's phone numbers, or the user's organization's phone number, is the same
        # as the meeting link, then this is a single-party call on the Twilio side. Note that inbound
        # calls will have a meeting link that is the inbound phone number, which will be one of the
        # user's phone numbers or the organization's phone number.
        if (
            self.bot_owner.phone_numbers.filter(number=phone_number).exists()
            or self.bot_owner.organization
            and self.bot_owner.organization.phone_number == phone_number
        ):
            return TwilioCallBotController(self.recall_bot_id or "")

        # The user has a phone number and that number is different from the meeting link, so this
        # is a multi-party call on the Twilio side.
        return TwilioConferenceBotController(self.recall_bot_id or "")

    def get_status(self) -> BotStatus:
        return self._bot_controller().get_bot_status()

    async def aget_status(self) -> BotStatus:
        bot_controller = await sync_to_async(self._bot_controller)()
        return await bot_controller.aget_bot_status()

    @property
    def supports_scheduled_bots(self) -> bool:
        # Ideally, this would be a method on the BotController, but currently the code that uses
        # this is called both synchronously and asynchronously, which makes it difficult to
        # get the bot controller. So, for now, we just check the type of the link.
        return self.link_type()[0] == self.LinkType.VIDEO_CALL

    @property
    def meeting_type(self) -> BotMeetingType:
        return self._bot_controller().meeting_type

    class _CommonBotCreationInfo(BaseModel):
        meeting_url: str
        bot_preferences: BotPreferences
        enable_live_transcription: bool
        enable_native_zoom_bot: bool
        asr_language_code: str
        bot_base_name: str
        internal_id: UUID
        user_phone_number: phonenumbers.PhoneNumber | None

        class Config:
            arbitrary_types_allowed = True

    def _collect_common_bot_creation_info(self) -> _CommonBotCreationInfo | None:
        """Collects and returns the common bot creation information for creating or updating a provider meeting bot."""
        if not self.meeting_link:
            logging.error(
                "Attempted to create an immediately-recording bot without a meeting link for bot %s", self.uuid
            )
            return None

        if not self.bot_owner:
            logging.error("Attempted to create an immediately-recording bot for bot without an owner: %s", self.uuid)
            return None

        user_preferences = self.bot_owner.get_preferences()
        asr_language_code = user_preferences.asr_language_code
        if not asr_language_code:
            logging.warning("ASR language code not set for user: %s. Using default ('en') for now", self.bot_owner.uuid)
            asr_language_code = "en"

        return MeetingBot._CommonBotCreationInfo(
            meeting_url=self.meeting_link,
            bot_preferences=user_preferences.bot_preferences,
            enable_live_transcription=not Flags.EnableProcessRecallBotTranscriptsWithZeplynDeepgramIntegration.is_active_for_user(
                self.bot_owner
            ),
            enable_native_zoom_bot=Flags.EnableNativeZoomBots.is_active_for_user(self.bot_owner) or False,
            asr_language_code=asr_language_code,
            bot_base_name=self.bot_owner.first_name or self.bot_owner.name,
            internal_id=self.uuid,
            user_phone_number=self.bot_owner.primary_phone_number,
        )

    def create_bot_and_start_recording(self) -> None:
        if not (creation_info := self._collect_common_bot_creation_info()):
            return
        controller = self._bot_controller()
        controller.create_bot_and_start_recording(
            meeting_url=creation_info.meeting_url,
            bot_preferences=creation_info.bot_preferences,
            enable_live_transcription=creation_info.enable_live_transcription,
            enable_native_zoom_bot=creation_info.enable_native_zoom_bot,
            asr_language_code=creation_info.asr_language_code,
            bot_base_name=creation_info.bot_base_name,
            internal_id=creation_info.internal_id,
            user_phone_number=creation_info.user_phone_number,
        )
        self.recall_bot_id = controller.bot_id
        self.save()

    def create_or_update_scheduled_bot(self, scheduled_join_time: datetime.datetime) -> None:
        if not (creation_info := self._collect_common_bot_creation_info()):
            return
        controller = self._bot_controller()
        controller.create_or_update_scheduled_bot(
            meeting_url=creation_info.meeting_url,
            join_time=scheduled_join_time,
            bot_preferences=creation_info.bot_preferences,
            enable_live_transcription=creation_info.enable_live_transcription,
            enable_native_zoom_bot=creation_info.enable_native_zoom_bot,
            asr_language_code=creation_info.asr_language_code,
            bot_base_name=creation_info.bot_base_name,
            internal_id=creation_info.internal_id,
            user_phone_number=creation_info.user_phone_number,
        )
        self.recall_bot_id = controller.bot_id
        self.save()

    def delete_bot(self) -> None:
        if not self.recall_bot_id:
            return
        controller = self._bot_controller()
        response = controller.delete_bot()
        if response.status >= 300:
            logging.error("Failed to delete bot: %s", response)
            return
        self.recall_bot_id = None
        self.save()

    @property
    def supports_pause_resume(self) -> bool:
        return self._bot_controller().supports_pause_resume

    def pause_recording(self) -> BotResponse:
        return self._bot_controller().pause_recording()

    def resume_recording(self) -> BotResponse:
        return self._bot_controller().resume_recording()

    def leave_meeting(self) -> BotResponse:
        return self._bot_controller().leave_call()

    def get_transcript(self) -> list[dict[str, Any]]:
        return self._bot_controller().get_bot_transcript()

    def has_speaker_timeline(self) -> bool:
        return self._bot_controller().bot_has_speaker_timeline_data()

    def delete_media(self) -> None:
        self._bot_controller().delete_media()

    @property
    def supports_send_chat_message(self) -> bool:
        return self._bot_controller().supports_send_chat_message

    def send_chat_message(self, host_message: str | None, everyone_message: str) -> bool:
        return self._bot_controller().send_chat_message(host_message, everyone_message)

    def get_media_download_info(self) -> tuple[str, str | None, str | None] | None:
        return self._bot_controller().get_media_download_info()

    def __str__(self) -> str:
        return self.meeting_link or "No meeting link provided"

    @property
    def bot_id_for_logging(self) -> str:
        return self._bot_controller().debugging_string

    @property
    def provider_bot_url(self) -> str:
        return self._bot_controller().provider_bot_url

    @property
    def processing_task(self) -> BotProcessingTask:
        return self._bot_controller().processing_task
