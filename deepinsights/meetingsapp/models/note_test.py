import datetime
import io
import logging
import textwrap
from typing import Mapping
from unittest.mock import MagicMock, patch

import pytest
from django.test import TestCase
from pytest_django.fixtures import SettingsWrapper

from api.routers.note_models import Summary as APISummary
from api.routers.note_models import SummarySection as APISummarySection
from deepinsights.core.integrations.crm.crm_models import CRMSyncItemSelection, CRMSyncSection
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.audio_buffer import AudioBuffer
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.client_interaction import ClientInteraction
from deepinsights.meetingsapp.models.meeting_bot import MeetingBot
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import (
    Note,
    Summary,
    SummarySection,
    get_agenda_template_content,
    is_authorized_to_view_note,
)
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User


@pytest.fixture
def organization() -> Organization:
    return Organization.objects.create(name="org-1")


@pytest.fixture
def user(django_user_model: User, organization: Organization) -> User:
    return django_user_model.objects.create_user("<EMAIL>", organization=organization)


def test_get_summary_for_email_with_default_template(django_user_model: User) -> None:
    org = Organization.objects.create()
    user = django_user_model.objects.create_user("<EMAIL>", organization=org)
    note = Note.objects.create(metadata={})
    assert note.get_summary_for_email(user, "subject")


def test_get_summary_for_email_user_override(django_user_model: User) -> None:
    org = Organization.objects.create(preferences={"email_settings": {"meeting_notes_email_template": "Hello Org!"}})
    user = django_user_model.objects.create_user(
        "<EMAIL>",
        organization=org,
        preferences={"email_settings": {"meeting_notes_email_template": "Hello User!"}},
    )
    note = Note.objects.create(metadata={})
    assert (
        note.get_summary_for_email(user, "subject")
        == f'<p><a href="{note.public_url()}" style="color: #0066cc; text-decoration: none; font-weight: bold;">📝 View Notes in Zeplyn</a></p>\n\n<p>Hello User!</p>'
    )


def test_get_summary_for_email_org_override(django_user_model: User) -> None:
    org = Organization.objects.create(preferences={"email_settings": {"meeting_notes_email_template": "Hello Org!"}})
    user = django_user_model.objects.create_user("<EMAIL>", organization=org)

    note = Note.objects.create(metadata={})
    assert (
        note.get_summary_for_email(user, "subject")
        == f'<p><a href="{note.public_url()}" style="color: #0066cc; text-decoration: none; font-weight: bold;">📝 View Notes in Zeplyn</a></p>\n\n<p>Hello Org!</p>'
    )


def test_get_summary_for_email_and_crm_use_different_templates(django_user_model: User) -> None:
    org = Organization.objects.create()
    user = django_user_model.objects.create_user(
        "<EMAIL>",
        organization=org,
        preferences={"email_settings": {"meeting_notes_email_template": "Hello email!"}},
        crm_configuration={"summary_template": "Hello crm!"},
    )
    note = Note.objects.create(metadata={}, note_owner=user)
    assert (
        note.get_summary_for_email(user, "subject")
        == f'<p><a href="{note.public_url()}" style="color: #0066cc; text-decoration: none; font-weight: bold;">📝 View Notes in Zeplyn</a></p>\n\n<p>Hello email!</p>'
    )
    assert note.get_summary_for_crm(use_html_formatting=False) == "Hello crm!"


def test_get_summary_for_crm_markup(django_user_model: User) -> None:
    user = django_user_model.objects.create_user("<EMAIL>")
    note = Note.objects.create(metadata={}, note_owner=user)
    actual = note.get_summary_for_crm(use_html_formatting=True)
    assert actual
    assert actual.startswith("<h1>")

    actual = note.get_summary_for_crm(use_html_formatting=False)
    assert not actual.startswith("<h1>")


def test_get_summary_for_crm_client_meeting(django_user_model: User) -> None:
    user = django_user_model.objects.create_user("<EMAIL>")
    note = Note.objects.create(metadata={"meeting_type": "client"}, note_owner=user)
    assert "Consent was received for notetaking" in note.get_summary_for_crm(use_html_formatting=False)


def test_get_summary_for_crm_non_client_meeting(django_user_model: User) -> None:
    user = django_user_model.objects.create_user("<EMAIL>")
    for meeting_type in [None, "", "internal", "debrief"]:
        note = Note.objects.create(metadata={"meeting_type": meeting_type}, note_owner=user)
        assert "Consent was received for notetaking" not in note.get_summary_for_crm(use_html_formatting=False)


def test_get_summary_for_crm_org_override(django_user_model: User) -> None:
    org = Organization.objects.create()
    user = django_user_model.objects.create_user("<EMAIL>", organization=org)
    org.crm_configuration["summary_template"] = "hello!"
    note = Note.objects.create(metadata={}, note_owner=user)
    assert note.get_summary_for_crm(use_html_formatting=False) == "hello!"


def test_get_summary_for_crm_user_override(django_user_model: User) -> None:
    org = Organization.objects.create()
    user = django_user_model.objects.create_user("<EMAIL>", organization=org)
    org.crm_configuration["summary_template"] = "hello org!"
    user.crm_configuration["summary_template"] = "hello user!"

    note = Note.objects.create(metadata={}, note_owner=user)
    assert note.get_summary_for_crm(use_html_formatting=False) == "hello user!"


summary_hiding_template = """\
    # {{title}}

    ## Meeting details
    {{meeting_details}}

    {% if has_summary -%}
    **Summary**
    {{summary}}
    {% endif -%}

    **Attendees:** {{attendees}}

    **Keywords:** {{keywords}}

    **Action Items**
    {{action_items}}

    **Key Takeaways**
    {{key_takeaways}}

    **Advisor Notes**
    {{advisor_notes}}
    """


def test_get_summary_for_crm_hiding_empty_summary(django_user_model: User) -> None:
    org = Organization.objects.create()
    user = django_user_model.objects.create_user("<EMAIL>", organization=org)
    org.crm_configuration["summary_template"] = summary_hiding_template

    note = Note.objects.create(
        note_owner=user,
        key_takeaways=["key1", "key2"],
        advisor_notes=["note1", "note2"],
        summary=None,
        metadata={
            "meeting_name": "Test meeting",
            "meeting_duration": 1000,
            "meeting_type": "client",
            "tags": ["tag1", "tag2"],
        },
    )
    note.created = datetime.datetime(2024, 8, 1)
    Attendee.objects.create(note=note, attendee_name="Client1")
    Attendee.objects.create(note=note, attendee_name="Client2")
    Task.objects.create(note=note, task_title="Task1")
    Task.objects.create(note=note, task_title="Task2")

    expected = textwrap.dedent(
        """\
        # **Notes from Test meeting on 2024-08-01**

        ## Meeting details
        **Meeting Duration:** 16 minutes and 40 seconds

        Consent was received for notetaking

        **Attendees:** Client1, Client2

        **Keywords:** tag1, tag2

        **Action Items**

        - Task1
        - Task2

        **Key Takeaways**

        - key1
        - key2

        **Advisor Notes**

        - note1
        - note2
    """
    )
    assert note.get_summary_for_crm(use_html_formatting=False) == expected


def test_get_summary_for_crm_not_hiding_non_empty_summary(django_user_model: User) -> None:
    org = Organization.objects.create()
    user = django_user_model.objects.create_user("<EMAIL>", organization=org)
    org.crm_configuration["summary_template"] = summary_hiding_template

    note = Note.objects.create(
        note_owner=user,
        key_takeaways=["key1", "key2"],
        advisor_notes=["note1", "note2"],
        summary={
            "sections": [
                {
                    "topic": "Topic 1",
                    "bullets": ["Bullet 1", "Bullet 2"],
                },
                {
                    "topic": "Topic 2",
                    "bullets": ["Bullet 3", "Bullet 4"],
                },
            ]
        },
        metadata={
            "meeting_name": "Test meeting",
            "meeting_duration": 1000,
            "meeting_type": "client",
            "tags": ["tag1", "tag2"],
        },
    )
    note.created = datetime.datetime(2024, 8, 1)
    Attendee.objects.create(note=note, attendee_name="Client1")
    Attendee.objects.create(note=note, attendee_name="Client2")
    Task.objects.create(note=note, task_title="Task1")
    Task.objects.create(note=note, task_title="Task2")

    expected = textwrap.dedent(
        """\
        # **Notes from Test meeting on 2024-08-01**

        ## Meeting details
        **Meeting Duration:** 16 minutes and 40 seconds

        Consent was received for notetaking

        **Summary**

        Topic 1

        - Bullet 1
        - Bullet 2

        Topic 2

        - Bullet 3
        - Bullet 4

        **Attendees:** Client1, Client2

        **Keywords:** tag1, tag2

        **Action Items**

        - Task1
        - Task2

        **Key Takeaways**

        - key1
        - key2

        **Advisor Notes**

        - note1
        - note2
    """
    )
    assert note.get_summary_for_crm(use_html_formatting=False) == expected


def test_get_summary_for_crm_certification_test(django_user_model: User) -> None:
    user = django_user_model.objects.create_user("<EMAIL>")
    note = Note.objects.create(metadata={"meeting_name": "Test Meeting"}, note_owner=user)
    note.created = datetime.datetime(2024, 8, 1)
    summary = note.get_summary_for_crm(use_html_formatting=False)

    assert "Reviewed and certified by" not in summary


@pytest.mark.parametrize(
    "certification_required, certification_expected",
    [
        (True, True),
        (False, False),
    ],
)
@patch("deepinsights.meetingsapp.models.note.datetime")
def test_get_summary_for_crm_certification(
    mock_datetime: MagicMock,
    certification_required: bool,
    certification_expected: bool,
    django_user_model: User,
) -> None:
    mock_timezone = MagicMock()
    mock_timezone.utc = "UTC"
    mock_datetime.timezone = mock_timezone

    mock_now = MagicMock()
    mock_now.strftime.return_value = "2024-08-15 14:30:00 UTC"
    mock_datetime.datetime.now.return_value = mock_now

    user = django_user_model.objects.create_user(
        "<EMAIL>",
        email="<EMAIL>",
        preferences={
            "sync_preferences": {
                "advisor_certification_required": certification_required,
                "advisor_certification_synced_text": "Reviewed and certified by {user_email}, at {timestamp_with_tz}",
            }
        },
    )

    note = Note.objects.create(metadata={"meeting_name": "Test Meeting"}, note_owner=user)
    note.created = datetime.datetime(2024, 8, 1)

    summary = note.get_summary_for_crm(use_html_formatting=False)

    certification_text = "Reviewed and <NAME_EMAIL>, at 2024-08-15 14:30:00 UTC"
    if certification_expected:
        assert certification_text in summary
    else:
        assert certification_text not in summary


@patch("deepinsights.meetingsapp.models.note.datetime")
def test_get_summary_for_crm_custom_certification_text(mock_datetime: MagicMock, django_user_model: User) -> None:
    mock_timezone = MagicMock()
    mock_timezone.utc = "UTC"
    mock_datetime.timezone = mock_timezone

    mock_now = MagicMock()
    mock_now.strftime.return_value = "2024-08-15 14:30:00 UTC"
    mock_datetime.datetime.now.return_value = mock_now

    custom_text = "This note was reviewed by {user_email} on {timestamp_with_tz} and is compliant with our policies."

    user = django_user_model.objects.create_user(
        "<EMAIL>",
        email="<EMAIL>",
        preferences={
            "sync_preferences": {
                "advisor_certification_required": True,
                "advisor_certification_synced_text": custom_text,
            }
        },
    )
    note = Note.objects.create(metadata={"meeting_name": "Test Meeting"}, note_owner=user)
    note.created = datetime.datetime(2024, 8, 1)

    summary = note.get_summary_for_crm(use_html_formatting=False)

    expected_text = (
        "This note was <NAME_EMAIL> on 2024-08-15 14:30:00 UTC and is compliant with our policies."
    )
    assert expected_text in summary


def test_is_not_authorized_to_view_note(user: User, django_user_model: User) -> None:
    note_owner = django_user_model.objects.create_user("<EMAIL>")
    note = Note.objects.create(note_owner=note_owner)
    assert not is_authorized_to_view_note(user, note)


def test_is_authorized_to_view_note_owner(user: User) -> None:
    note = Note.objects.create(note_owner=user)
    assert is_authorized_to_view_note(user, note)


def test_is_authorized_to_view_note_authorized_user(user: User, django_user_model: User) -> None:
    note_owner = django_user_model.objects.create_user("<EMAIL>")
    note = Note.objects.create(note_owner=note_owner)
    note.authorized_users.add(user)
    assert is_authorized_to_view_note(user, note)


def test_is_authorized_to_view_note_superuser(user: User, django_user_model: User) -> None:
    superuser = django_user_model.objects.create_superuser("<EMAIL>", password="password")
    note = Note.objects.create(note_owner=user)
    assert is_authorized_to_view_note(superuser, note)


@pytest.mark.parametrize(
    "license_type, expected",
    [
        (User.LicenseType.advisor, False),
        (User.LicenseType.csa, True),
        (User.LicenseType.staff, True),
    ],
)
def test_client_authorization_for_user_license_type(
    user: User,
    django_user_model: User,
    organization: Organization,
    license_type: User.LicenseType,
    expected: bool,
) -> None:
    note_owner = django_user_model.objects.create_user("<EMAIL>", organization=organization)
    note = Note.objects.create(note_owner=note_owner)
    user.license_type = license_type
    user.save()

    assert is_authorized_to_view_note(user, note) == expected


@pytest.mark.parametrize(
    "license_type, expected",
    [
        (User.LicenseType.advisor, False),
        (User.LicenseType.csa, False),
        (User.LicenseType.staff, False),
    ],
)
def test_client_authorization_for_user_license_type_no_note_owner(
    user: User,
    license_type: User.LicenseType,
    expected: bool,
) -> None:
    note = Note.objects.create()
    user.license_type = license_type
    user.save()

    assert is_authorized_to_view_note(user, note) == expected


@pytest.mark.parametrize(
    "license_type, expected",
    [
        (User.LicenseType.advisor, False),
        (User.LicenseType.csa, False),
        (User.LicenseType.staff, False),
    ],
)
def test_client_authorization_for_user_license_type_different_org(
    user: User,
    django_user_model: User,
    license_type: User.LicenseType,
    expected: bool,
) -> None:
    note_owner = django_user_model.objects.create_user(
        "<EMAIL>", organization=Organization.objects.create(name="org-2")
    )
    note = Note.objects.create(note_owner=note_owner)
    user.license_type = license_type
    user.save()

    assert is_authorized_to_view_note(user, note) == expected


@pytest.mark.django_db
def test_features_remap_speakers_available() -> None:
    note = Note.objects.create(raw_transcript="test transcript", status=Note.PROCESSING_STATUS.processed)
    Attendee.objects.create(note=note, attendee_name="Test User", speaker_alias="Speaker 1")

    assert Note.FEATURES.remap_speakers in note.features


@pytest.mark.django_db
def test_features_remap_speakers_not_available_when_finalized() -> None:
    note = Note.objects.create(raw_transcript="test transcript", status=Note.PROCESSING_STATUS.finalized)
    Attendee.objects.create(note=note, attendee_name="Test User", speaker_alias="Speaker 1")

    assert Note.FEATURES.remap_speakers not in note.features


@pytest.mark.django_db
def test_features_remap_speakers_not_available_without_transcript() -> None:
    note = Note.objects.create(raw_transcript=None, status=Note.PROCESSING_STATUS.processed)
    Attendee.objects.create(note=note, attendee_name="Test User", speaker_alias="Speaker 1")

    assert Note.FEATURES.remap_speakers not in note.features


@pytest.mark.django_db
def test_features_remap_speakers_available_without_speaker_alias() -> None:
    note = Note.objects.create(raw_transcript="test transcript", status=Note.PROCESSING_STATUS.processed)
    Attendee.objects.create(note=note, attendee_name="Test User", speaker_alias=None)

    assert Note.FEATURES.remap_speakers in note.features


@pytest.mark.django_db
def test_features_remap_speakers_available_without_speaker_alias_for_bot_meeting() -> None:
    note = Note.objects.create(raw_transcript="test transcript", status=Note.PROCESSING_STATUS.processed)
    MeetingBot.objects.create(note=note)
    Attendee.objects.create(note=note, attendee_name="Test User", speaker_alias=None)

    assert Note.FEATURES.remap_speakers in note.features


@pytest.mark.django_db
def test_features_remap_speakers_available_with_speaker_alias_for_bot_meeting() -> None:
    note = Note.objects.create(raw_transcript="test transcript", status=Note.PROCESSING_STATUS.processed)
    MeetingBot.objects.create(note=note)
    Attendee.objects.create(note=note, attendee_name="Test User", speaker_alias=None)
    Attendee.objects.create(note=note, attendee_name="Test Bot", speaker_alias="Speaker 2")

    assert Note.FEATURES.remap_speakers in note.features


@patch("deepinsights.meetingsapp.models.note.AWS")
def test_save_with_audio_data_success(mock_aws: MagicMock, user: User, settings: SettingsWrapper) -> None:
    settings.AWS_S3_FILE_FOLDER = "test-folder"
    note = Note.objects.create()
    content = io.BytesIO(b"test")
    note.save_with_audio_data(
        user, "voice_memo", datetime.datetime(2022, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc), "mp3", content
    )

    note.refresh_from_db()
    expected_file_path = f"test-folder/{user.uuid}/voice_memo/2022-01-01/{note.uuid}.mp3"
    assert note.file_path == expected_file_path
    assert note.file_type == "mp3"
    assert note.status == Note.PROCESSING_STATUS.uploaded
    mock_aws.return_value.upload_file.assert_called_once_with(expected_file_path, content)


@patch("deepinsights.meetingsapp.models.note.AWS")
def test_save_with_audio_data_failure(mock_aws: MagicMock, user: User, settings: SettingsWrapper) -> None:
    settings.AWS_S3_FILE_FOLDER = "test-folder"
    note = Note.objects.create()
    content = io.BytesIO(b"test")
    mock_aws.return_value.upload_file.side_effect = Exception("Upload failed")
    with pytest.raises(Exception):
        note.save_with_audio_data(
            user, "voice_memo", datetime.datetime(2022, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc), "mp3", content
        )

    note.refresh_from_db()
    assert not note.file_path
    assert not note.file_type
    assert not note.status


@pytest.mark.django_db
@patch("deepinsights.meetingsapp.models.note.AWS")
def test_temporary_s3_download_url_success(mock_aws: MagicMock) -> None:
    note = Note.objects.create(file_path="test/path/to/file")
    mock_aws.return_value.get_temp_url.return_value = "http://example.com/temp-url"

    url = note.temporary_s3_download_url()

    assert url == "http://example.com/temp-url"
    mock_aws.return_value.get_temp_url.assert_called_once_with("test/path/to/file")


@pytest.mark.django_db
@patch("deepinsights.meetingsapp.models.note.AWS")
def test_temporary_s3_download_url_no_file_path(mock_aws: MagicMock) -> None:
    note = Note.objects.create(file_path=None)

    url = note.temporary_s3_download_url()

    assert url is None
    mock_aws.return_value.get_temp_url.assert_not_called()


@pytest.mark.django_db
@patch("deepinsights.meetingsapp.models.note.AWS")
def test_delete_media_success(mock_aws: MagicMock, caplog: pytest.LogCaptureFixture) -> None:
    note = Note.objects.create(file_path="test/path/to/file")
    AudioBuffer.objects.create(note=note, data=b"test", sequence=0)
    AudioBuffer.objects.create(note=note, data=b"test", sequence=1)
    AudioBuffer.objects.create(data=b"test", sequence=0)
    with caplog.at_level(logging.INFO):
        note.delete_media()

    note.refresh_from_db()
    assert note.file_path is None
    assert AudioBuffer.objects.filter(note=note).count() == 0
    assert AudioBuffer.objects.count() == 1
    mock_aws.return_value.delete_s3_file.assert_called_once_with("test/path/to/file")
    assert "Deleted media for note" in caplog.text


@pytest.mark.django_db
@patch("deepinsights.meetingsapp.models.note.AWS")
def test_delete_media_no_file_path(mock_aws: MagicMock, caplog: pytest.LogCaptureFixture) -> None:
    note = Note.objects.create(file_path=None)
    AudioBuffer.objects.create(note=note, data=b"test", sequence=0)
    AudioBuffer.objects.create(note=note, data=b"test", sequence=1)
    AudioBuffer.objects.create(data=b"test", sequence=0)
    with caplog.at_level(logging.WARNING):
        note.delete_media()

    note.refresh_from_db()
    assert AudioBuffer.objects.filter(note=note).count() == 0
    assert AudioBuffer.objects.count() == 1
    mock_aws.return_value.delete_s3_file.assert_not_called()
    assert "Requested media deletion for note without any associated media" in caplog.text


@pytest.mark.django_db
@patch("deepinsights.meetingsapp.models.note.AWS")
def test_delete_media_failure(mock_aws: MagicMock) -> None:
    note = Note.objects.create(file_path="test/path/to/file")
    AudioBuffer.objects.create(note=note, data=b"test", sequence=0)
    AudioBuffer.objects.create(note=note, data=b"test", sequence=1)
    AudioBuffer.objects.create(data=b"test", sequence=0)
    mock_aws.return_value.delete_s3_file.side_effect = Exception("Delete failed")
    with pytest.raises(Exception):
        note.delete_media()

    note.refresh_from_db()
    # Audio buffers are deleted first.
    assert AudioBuffer.objects.filter(note=note).count() == 0
    assert AudioBuffer.objects.count() == 1
    assert note.file_path == "test/path/to/file"


@pytest.mark.django_db
@patch("deepinsights.meetingsapp.models.note.AWS")
def test_delete_media_buffer_deletion_failure(mock_aws: MagicMock) -> None:
    note = Note.objects.create(file_path="test/path/to/file")
    AudioBuffer.objects.create(note=note, data=b"test", sequence=0)
    AudioBuffer.objects.create(note=note, data=b"test", sequence=1)
    with patch.object(AudioBuffer.objects, "filter", side_effect=Exception("Delete failed")), pytest.raises(Exception):
        note.delete_media()

    note.refresh_from_db()
    assert AudioBuffer.objects.filter(note=note).count() == 2
    assert note.file_path == "test/path/to/file"


@pytest.mark.django_db
def test_has_client_interaction_no_interaction(user: User, django_user_model: User) -> None:
    note = Note.objects.create()
    assert not note.has_client_interaction()


@pytest.mark.django_db
def test_has_client_interaction_has_interaction(user: User, django_user_model: User) -> None:
    note = Note.objects.create()
    meeting_type = MeetingType.objects.first()
    assert meeting_type
    ClientInteraction.objects.create(note=note, meeting_type=meeting_type)
    note.refresh_from_db()
    assert note.has_client_interaction()


@pytest.mark.django_db
def test_attendees_list_llm_with_both_clients_and_advisors(user: User, organization: Organization) -> None:
    note = Note.objects.create()
    Attendee.objects.create(
        note=note,
        attendee_name="Test User",
        speaker_alias="Speaker 1",
        user=user,
    )
    Attendee.objects.create(
        note=note,
        attendee_name="Test User 2",
        speaker_alias="Speaker 2",
        client=Client.objects.create(name="Client", organization=organization),
    )

    expected = {"clients": "Test User 2", "advisors": "Test User"}
    assert note.get_attendees_by_type() == expected


@pytest.mark.django_db
def test_attendees_list_llm_with_no_clients(user: User) -> None:
    note = Note.objects.create()
    Attendee.objects.create(
        note=note,
        attendee_name="Test User",
        speaker_alias="Speaker 1",
        user=user,
    )
    expected_no_clients = {"clients": "None", "advisors": "Test User"}
    assert note.get_attendees_by_type() == expected_no_clients


@pytest.mark.django_db
def test_attendees_list_llm_with_no_advisors(organization: Organization) -> None:
    note = Note.objects.create()
    Attendee.objects.create(
        note=note,
        attendee_name="Test User 2",
        speaker_alias="Speaker 2",
        client=Client.objects.create(name="Client", organization=organization),
    )
    expected_no_advisors = {"clients": "Test User 2", "advisors": "None"}
    assert note.get_attendees_by_type() == expected_no_advisors


@pytest.mark.django_db
def test_attendees_list_llm_with_no_attendees() -> None:
    note = Note.objects.create()
    expected_no_attendees = {"clients": "None", "advisors": "None"}
    assert note.get_attendees_by_type() == expected_no_attendees


@pytest.mark.django_db
def test_get_agenda_template_content() -> None:
    # Test case 1: Note with meeting type and agenda template
    meeting_type = MeetingType.objects.create(name="Test Meeting Type")
    meeting_type.agenda_templates.create(initial_data={"content": "Test agenda content"})
    note = Note.objects.create(meeting_type=meeting_type)
    assert get_agenda_template_content(note) == "Test agenda content"

    # Test case 2: Note with meeting type but no agenda template
    meeting_type_no_template = MeetingType.objects.create(name="No Template Type")
    note_no_template = Note.objects.create(meeting_type=meeting_type_no_template)
    assert get_agenda_template_content(note_no_template) == ""

    # Test case 3: Note with no meeting type
    note_no_meeting_type = Note.objects.create()
    assert get_agenda_template_content(note_no_meeting_type) == ""

    # Test case 4: Agenda template with no initial data
    meeting_type_no_data = MeetingType.objects.create(name="No Data Type")
    meeting_type_no_data.agenda_templates.create()
    note_no_data = Note.objects.create(meeting_type=meeting_type_no_data)
    assert get_agenda_template_content(note_no_data) == ""

    # Test case 5: Agenda template with initial data but no content
    meeting_type_no_content = MeetingType.objects.create(name="No Content Type")
    meeting_type_no_content.agenda_templates.create(initial_data={})
    note_no_content = Note.objects.create(meeting_type=meeting_type_no_content)
    assert get_agenda_template_content(note_no_content) == ""


@pytest.mark.django_db
def test_public_url(settings: SettingsWrapper, user: User) -> None:
    settings.APP_DOMAIN = "http://example.com"
    note = Note.objects.create(note_owner=user)
    assert note.public_url() == f"http://example.com/notes/{note.uuid}"


class SummaryTestCase(TestCase):
    async def get_sample_note(self) -> Note:
        org = await Organization.objects.acreate()
        user_with_org = await User.objects.acreate(email="<EMAIL>", organization=org)

        note = await Note.objects.acreate(
            note_owner=user_with_org,
            key_takeaways=["Important point 1", "Important point 2"],
            advisor_notes=["Note from advisor 1", "Note from advisor 2"],
            summary={
                "sections": [
                    {"topic": "Discussion Points", "bullets": ["Point 1", "Point 2"]},
                    {"topic": "Action Items", "bullets": ["Item 1", "Item 2"]},
                ]
            },
            metadata={
                "meeting_name": "Test Meeting",
                "meeting_duration": 3600,  # 1 hour
                "meeting_type": "client",
                "tags": ["important", "quarterly"],
            },
        )

        await Attendee.objects.acreate(note=note, attendee_name="John Doe")
        await Attendee.objects.acreate(note=note, attendee_name="Jane Smith")

        await Task.objects.acreate(note=note, task_title="Follow up on proposal")
        await Task.objects.acreate(note=note, task_title="Schedule next meeting")

        return note

    def test_generate_summary_json(self) -> None:
        summary = Summary(
            sections=[
                SummarySection(topic="Topic 1", bullets=["Bullet 1", "Bullet 2"]),
                SummarySection(topic="Topic 2", bullets=["Bullet 3", "Bullet 4"]),
                SummarySection(topic="Topic 3", bullets=["Bullet 5", "Bullet 6"]),
            ]
        )

        expected_output = {
            "sections": [
                {
                    "topic": "Topic 1",
                    "bullets": ["Bullet 1", "Bullet 2"],
                },
                {
                    "topic": "Topic 2",
                    "bullets": ["Bullet 3", "Bullet 4"],
                },
                {
                    "topic": "Topic 3",
                    "bullets": ["Bullet 5", "Bullet 6"],
                },
            ]
        }
        assert summary.to_dict() == expected_output  # type: ignore[attr-defined]

    def test_as_text(self) -> None:
        summary = Summary(
            sections=[
                SummarySection(topic="Topic 1", bullets=["Bullet 1", "Bullet 2"]),
                SummarySection(topic="Topic 2", bullets=["Bullet 3", "Bullet 4"]),
            ]
        )

        expected_output = """
Topic 1
 • Bullet 1
 • Bullet 2

Topic 2
 • Bullet 3
 • Bullet 4
"""
        assert summary.as_text() == expected_output

    @pytest.mark.asyncio
    async def test_add_section_append(self) -> None:
        sample_note = await self.get_sample_note()
        summary = APISummary(
            sections=[
                APISummarySection(topic="Topic 1", bullets=["Bullet 1", "Bullet 2"]),
                APISummarySection(topic="Topic 2", bullets=["Bullet 3", "Bullet 4"]),
                APISummarySection(topic="Topic 3", bullets=["Bullet 5", "Bullet 6"]),
            ]
        )
        sample_note.summary = summary.model_dump()
        await sample_note.asave()

        new_section = APISummarySection(topic="Topic 4", bullets=["Bullet 7", "Bullet 8"])

        new_index = await sample_note.add_to_summary(new_section)
        await sample_note.arefresh_from_db()
        assert len(sample_note.get_summary().sections) == 4
        assert "Topic 4" in sample_note.get_summary().as_text()
        assert new_index == 3

    @pytest.mark.asyncio
    async def test_add_section_interleave_fallback(self) -> None:
        sample_note = await self.get_sample_note()
        summary = APISummary(
            sections=[
                APISummarySection(topic="Topic 1", bullets=["Bullet 1", "Bullet 2"]),
                APISummarySection(topic="Topic 2", bullets=["Bullet 3", "Bullet 4"]),
                APISummarySection(topic="Topic 3", bullets=["Bullet 5", "Bullet 6"]),
            ]
        )
        sample_note.summary = summary.model_dump()
        await sample_note.asave()

        new_section = APISummarySection(topic="Topic 4", bullets=["Bullet 7", "Bullet 8"])

        new_index = await sample_note.add_to_summary(new_section, 5)
        await sample_note.arefresh_from_db()
        assert len(sample_note.get_summary().sections) == 4
        assert "Topic 4" in sample_note.get_summary().as_text()
        assert new_index == 3

    @pytest.mark.asyncio
    async def test_add_section_interleave(self) -> None:
        sample_note = await self.get_sample_note()
        summary = APISummary(
            sections=[
                APISummarySection(topic="Topic 1", bullets=["Bullet 1", "Bullet 2"]),
                APISummarySection(topic="Topic 2", bullets=["Bullet 3", "Bullet 4"]),
                APISummarySection(topic="Topic 3", bullets=["Bullet 5", "Bullet 6"]),
            ]
        )
        sample_note.summary = summary.model_dump()
        await sample_note.asave()

        new_section = APISummarySection(topic="Topic 4", bullets=["Bullet 7", "Bullet 8"])

        new_index = await sample_note.add_to_summary(new_section, 1)
        await sample_note.arefresh_from_db()
        assert len(sample_note.get_summary().sections) == 3
        assert "Topic 4" in sample_note.get_summary().as_text()
        assert "Topic 2" not in sample_note.get_summary().as_text()
        assert new_index == 1


class TestCRMSyncItems:
    @pytest.fixture
    def user_with_org(self, django_user_model: User) -> User:
        org = Organization.objects.create()
        return django_user_model.objects.create_user("<EMAIL>", organization=org)

    @pytest.fixture
    def sample_note(self, user_with_org: User) -> Note:
        note = Note.objects.create(
            note_owner=user_with_org,
            key_takeaways=["Important point 1", "Important point 2"],
            advisor_notes=["Note from advisor 1", "Note from advisor 2"],
            summary={
                "sections": [
                    {"topic": "Discussion Points", "bullets": ["Point 1", "Point 2"]},
                    {"topic": "Action Items", "bullets": ["Item 1", "Item 2"]},
                ]
            },
            metadata={
                "meeting_name": "Test Meeting",
                "meeting_duration": 3600,  # 1 hour
                "meeting_type": "client",
                "tags": ["important", "quarterly"],
            },
        )

        Attendee.objects.create(note=note, attendee_name="John Doe")
        Attendee.objects.create(note=note, attendee_name="Jane Smith")

        Task.objects.create(note=note, task_title="Follow up on proposal")
        Task.objects.create(note=note, task_title="Schedule next meeting")

        return note

    def test_should_include_section_with_none_sync_items(self, sample_note: Note) -> None:
        assert sample_note.should_include_section(None, CRMSyncSection.MEETING_DETAILS)
        assert sample_note.should_include_section(None, CRMSyncSection.ATTENDEES)
        assert sample_note.should_include_section(None, CRMSyncSection.KEYWORDS)
        assert sample_note.should_include_section(None, CRMSyncSection.TASKS)
        assert sample_note.should_include_section(None, CRMSyncSection.KEY_TAKEAWAYS)
        assert sample_note.should_include_section(None, CRMSyncSection.ADVISOR_NOTES)
        assert sample_note.should_include_section(None, CRMSyncSection.SUMMARY)

    def test_should_include_section_with_empty_sync_items(self, sample_note: Note) -> None:
        sync_items: Mapping[CRMSyncSection, CRMSyncItemSelection] | None = {}
        assert sample_note.should_include_section(sync_items, CRMSyncSection.MEETING_DETAILS)
        assert sample_note.should_include_section(sync_items, CRMSyncSection.ATTENDEES)

    def test_should_include_section_with_explicit_true(self, sample_note: Note) -> None:
        sync_items = {
            CRMSyncSection.MEETING_DETAILS: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.ATTENDEES: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True),
        }
        assert sample_note.should_include_section(sync_items, CRMSyncSection.MEETING_DETAILS)
        assert sample_note.should_include_section(sync_items, CRMSyncSection.ATTENDEES)
        assert sample_note.should_include_section(sync_items, CRMSyncSection.SUMMARY)

    def test_should_include_section_with_explicit_false(self, sample_note: Note) -> None:
        sync_items = {
            CRMSyncSection.MEETING_DETAILS: CRMSyncItemSelection(include_section=False),
            CRMSyncSection.ATTENDEES: CRMSyncItemSelection(include_section=False),
            CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=False),
        }
        assert not sample_note.should_include_section(sync_items, CRMSyncSection.MEETING_DETAILS)
        assert not sample_note.should_include_section(sync_items, CRMSyncSection.ATTENDEES)
        assert not sample_note.should_include_section(sync_items, CRMSyncSection.SUMMARY)

    def test_should_include_section_mixed_configuration(self, sample_note: Note) -> None:
        sync_items = {
            CRMSyncSection.MEETING_DETAILS: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.ATTENDEES: CRMSyncItemSelection(include_section=False),
            CRMSyncSection.KEYWORDS: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=False),
        }
        assert sample_note.should_include_section(sync_items, CRMSyncSection.MEETING_DETAILS)
        assert not sample_note.should_include_section(sync_items, CRMSyncSection.ATTENDEES)
        assert sample_note.should_include_section(sync_items, CRMSyncSection.KEYWORDS)
        assert not sample_note.should_include_section(sync_items, CRMSyncSection.TASKS)
        # Sections not in sync_items should default to True
        assert sample_note.should_include_section(sync_items, CRMSyncSection.KEY_TAKEAWAYS)

    def test_get_tasks_to_include(self, sample_note: Note) -> None:
        task = Task.objects.create(note=sample_note, task_title="Task1")
        task2 = Task.objects.create(note=sample_note, task_title="Task2")

        sync_items = {CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True, included_items=[str(task.uuid)])}
        tasks = sample_note.get_tasks_to_include_crm_sync(sync_items=sync_items)
        task_uuids = [t.uuid for t in tasks]
        assert task2.uuid not in task_uuids
        assert task.uuid in task_uuids

    def test_get_summary_for_crm_with_all_sections_enabled(self, sample_note: Note) -> None:
        sync_items = {
            CRMSyncSection.MEETING_DETAILS: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.ATTENDEES: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.KEYWORDS: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.KEY_TAKEAWAYS: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.ADVISOR_NOTES: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True),
        }

        summary = sample_note.get_summary_for_crm(use_html_formatting=False, sync_items=sync_items)

        assert "**Meeting Duration:**" in summary
        assert "Consent was received for notetaking" in summary
        assert "**Attendees:** John Doe, Jane Smith" in summary
        assert "**Keywords:** important, quarterly" in summary
        assert "Follow up on proposal" in summary
        assert "Schedule next meeting" in summary
        assert "Important point 1" in summary
        assert "Important point 2" in summary
        assert "Note from advisor 1" in summary
        assert "Note from advisor 2" in summary
        assert "Discussion Points" in summary
        assert "Action Items" in summary

    def test_get_summary_for_crm_with_all_sections_disabled(self, sample_note: Note) -> None:
        sync_items = {
            CRMSyncSection.MEETING_DETAILS: CRMSyncItemSelection(include_section=False),
            CRMSyncSection.ATTENDEES: CRMSyncItemSelection(include_section=False),
            CRMSyncSection.KEYWORDS: CRMSyncItemSelection(include_section=False),
            CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=False),
            CRMSyncSection.KEY_TAKEAWAYS: CRMSyncItemSelection(include_section=False),
            CRMSyncSection.ADVISOR_NOTES: CRMSyncItemSelection(include_section=False),
            CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=False),
        }

        summary = sample_note.get_summary_for_crm(use_html_formatting=False, sync_items=sync_items)

        assert "**Meeting Duration:**" not in summary
        assert "Consent was received for notetaking" not in summary
        assert "**Attendees:**" not in summary
        assert "**Keywords:**" not in summary
        assert "**Action Items**" not in summary
        assert "**Key Takeaways**" not in summary
        assert "**Advisor Notes**" not in summary
        assert "## Meeting Summary" not in summary

        assert "Test Meeting" in summary

    def test_get_summary_for_crm_selective_sections(self, sample_note: Note) -> None:
        task = Task.objects.create(note=sample_note, task_title="Task1")
        task2 = Task.objects.create(note=sample_note, task_title="Task2")

        sync_items = {
            CRMSyncSection.MEETING_DETAILS: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.ATTENDEES: CRMSyncItemSelection(include_section=False),
            CRMSyncSection.KEYWORDS: CRMSyncItemSelection(include_section=False),
            CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True, included_items=[str(task.uuid)]),
            CRMSyncSection.KEY_TAKEAWAYS: CRMSyncItemSelection(include_section=False),
            CRMSyncSection.ADVISOR_NOTES: CRMSyncItemSelection(include_section=False),
            CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True),
        }

        summary = sample_note.get_summary_for_crm(use_html_formatting=False, sync_items=sync_items)

        assert "**Meeting Duration:**" in summary
        assert "Consent was received for notetaking" in summary
        assert "Discussion Points" in summary
        assert "Task2" not in summary

        assert "Task1" in summary
        assert "**Attendees:**" not in summary
        assert "**Keywords:**" not in summary
        assert "**Key Takeaways**" not in summary
        assert "**Advisor Notes**" not in summary

    def test_get_summary_for_crm_backward_compatibility(self, sample_note: Note) -> None:
        summary_old = sample_note.get_summary_for_crm(use_html_formatting=False)
        summary_new = sample_note.get_summary_for_crm(use_html_formatting=False, sync_items=None)

        assert summary_old == summary_new

        assert "**Meeting Duration:**" in summary_old
        assert "**Attendees:**" in summary_old
        assert "**Keywords:**" in summary_old
        assert "**Action Items**" in summary_old
        assert "**Key Takeaways**" in summary_old
        assert "**Advisor Notes**" in summary_old
        assert "## Meeting Summary" in summary_old

    def test_get_summary_for_crm_with_empty_note(self, user_with_org: User) -> None:
        empty_note = Note.objects.create(note_owner=user_with_org, metadata={"meeting_name": "Empty Meeting"})

        sync_items = {
            CRMSyncSection.MEETING_DETAILS: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.ATTENDEES: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.KEYWORDS: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.TASKS: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.KEY_TAKEAWAYS: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.ADVISOR_NOTES: CRMSyncItemSelection(include_section=True),
            CRMSyncSection.SUMMARY: CRMSyncItemSelection(include_section=True),
        }

        summary = empty_note.get_summary_for_crm(use_html_formatting=False, sync_items=sync_items)

        assert "Empty Meeting" in summary
        assert "Attendees not noted" in summary
        assert "No keywords" in summary

        assert "**Action Items**" not in summary
        assert "**Key Takeaways**" not in summary
        assert "**Advisor Notes**" not in summary
        assert "## Meeting Summary" not in summary

    def test_empty_sync_items_returns_same_as_before(self, sample_note: Note) -> None:
        summary_none = sample_note.get_summary_for_crm(use_html_formatting=False, sync_items=None)
        summary_empty = sample_note.get_summary_for_crm(use_html_formatting=False, sync_items={})

        assert summary_none == summary_empty
