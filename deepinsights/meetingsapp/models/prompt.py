from django.db import models
from simple_history.models import HistoricalRecords

from deepinsights.core.behaviours import StatusMixin, UUIDMixin


class Prompt(StatusMixin, UUIDMixin):
    """Model to store and version LLM prompts."""

    name = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        help_text="Name of the prompt. This is only for human reference, it is not used by the LLM.",
    )

    unique_name = models.CharField(
        max_length=200,
        null=True,
        help_text="A unique identifier for this prompt.",
    )

    version = models.CharField(
        max_length=50,
        default="1.0",
        null=True,
        help_text="Version number of the prompt.",
    )

    user_prompt = models.TextField(
        blank=True,
        null=True,
        help_text="User level instructions for the LLM which passes user specific data to the model.",
    )

    system_prompt = models.TextField(
        blank=True,
        null=True,
        help_text="System-level instructions for the LLM. This provides context and constraints for the model's behavior.",
    )

    version_notes = models.TextField(
        blank=True, null=True, help_text="Notes about this version - what changed, what's being tested, etc."
    )
    history = HistoricalRecords()

    def __str__(self):  # type: ignore[no-untyped-def]
        return self.name or "Unnamed prompt"
