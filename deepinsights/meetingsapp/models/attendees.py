import json
import logging

from django.core.exceptions import ValidationError
from django.db import models

from deepinsights.core.behaviours import StatusMixin, UUIDMixin
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User

logger = logging.getLogger(__name__)


class Attendee(UUIDMixin, StatusMixin):
    class AttendeeType(models.TextChoices):
        # Key =  value, admin dashboard display name
        CLIENT = "client", "Client"
        USER = "user", "User"
        UNKNOWN = "unknown", "Unknown"

    note = models.ForeignKey(Note, on_delete=models.CASCADE, related_name="attendees")
    attendee_name = models.CharField(max_length=255, null=True, blank=True)
    client = models.ForeignKey(Client, on_delete=models.SET_NULL, null=True, blank=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    speaker_time = models.Du<PERSON><PERSON>ield(null=True, blank=True)
    speaker_percentage = models.FloatField(null=True, blank=True)
    speaker_alias = models.CharField(max_length=255, null=True, blank=True)

    def get_name(self) -> str:
        if self.attendee_name:
            return self.attendee_name
        elif self.client:
            return (
                f"{self.client.first_name} {self.client.last_name}"
                if self.client.first_name and self.client.last_name
                else self.client.name
            )
        elif self.user:
            return (
                f"{self.user.first_name} {self.user.last_name}"
                if self.user.first_name and self.user.last_name
                else self.user.name
            )
        return ""

    def get_attendee_type(self) -> str:
        if self.client:
            return self.AttendeeType.CLIENT.value
        elif self.user:
            return self.AttendeeType.USER.value
        return self.AttendeeType.UNKNOWN.value

    def clean(self) -> None:
        super().clean()
        if not self.client and not self.user and not self.attendee_name:
            raise ValidationError("Attendee must have a name if neither a client nor a user is associated.")
        if self.client and self.user:
            raise ValidationError("Attendee cannot be associated with both a client and a user.")

    def __str__(self) -> str:
        try:
            return self.get_name()
        except Exception:
            return "Name Not Specified"

    @classmethod
    def parse_attendees(cls, attendees):  # type: ignore[no-untyped-def]
        try:
            # Attempt to parse as JSON
            attendees = json.loads(attendees)
            if isinstance(attendees, list):
                return attendees
            else:
                logger.error(f"Could not parse attendees! Invalid JSON: {attendees}")
                raise ValidationError("Invalid JSON format")
        except json.decoder.JSONDecodeError:
            if isinstance(attendees, str):
                names = [name.strip() for name in attendees.split(",") if name.strip()]
                attendees = [{"name": name, "type": cls.AttendeeType.UNKNOWN.value, "uuid": None} for name in names]
                return attendees
            else:
                logger.error(f"Could not parse attendees! Invalid string: {attendees}")
                raise ValidationError("Invalid input format")

    @classmethod
    def reconcile_attendees(cls, note: Note, attendees: list):  # type: ignore[no-untyped-def, type-arg]
        try:
            user_obj = note.note_owner
            existing_attendees = set(cls.objects.filter(note=note).values_list("id", flat=True))
            processed_attendees = set()

            for attendee_data in attendees:
                try:
                    attendee_name = attendee_data["name"]
                    attendee_type = attendee_data["type"]
                    attendee_uuid = attendee_data.get("uuid")

                    # Try to get existing attendee
                    if attendee_type == cls.AttendeeType.CLIENT.value:
                        existing_attendee = cls.objects.filter(note=note, client__uuid=attendee_uuid).first()
                    elif attendee_type == cls.AttendeeType.USER.value:
                        existing_attendee = cls.objects.filter(note=note, user__uuid=attendee_uuid).first()
                    else:
                        existing_attendee = cls.objects.filter(note=note, attendee_name=attendee_name).first()

                    if existing_attendee:
                        # Update existing attendee
                        if attendee_type == cls.AttendeeType.CLIENT.value and attendee_uuid:
                            client, _ = Client.objects.update_or_create(
                                uuid=attendee_uuid,
                                defaults={
                                    "name": attendee_name,
                                    "organization": user_obj.organization if user_obj else None,
                                },
                            )
                            if user_obj:
                                client.authorized_users.add(user_obj)
                            client.save()
                            existing_attendee.client = client
                            existing_attendee.user = None
                        elif attendee_type == cls.AttendeeType.USER.value and attendee_uuid:
                            user = User.objects.get(uuid=attendee_uuid)
                            existing_attendee.user = user
                            existing_attendee.client = None
                        else:
                            existing_attendee.client = None
                            existing_attendee.user = None

                        existing_attendee.attendee_name = attendee_name
                        existing_attendee.save()
                        logger.info(f"Attendee updated: {existing_attendee.get_name()}")
                    else:
                        # Create new attendee
                        if attendee_type == cls.AttendeeType.CLIENT.value and attendee_uuid:
                            client, _ = Client.objects.get_or_create(
                                uuid=attendee_uuid,
                                defaults={
                                    "name": attendee_name,
                                    "organization": user_obj.organization if user_obj else None,
                                },
                            )
                            if user_obj:
                                client.authorized_users.add(user_obj)
                            client.save()
                            attendee = cls.objects.create(note=note, client=client, attendee_name=attendee_name)
                        elif attendee_type == cls.AttendeeType.USER.value and attendee_uuid:
                            user = User.objects.get(uuid=attendee_uuid)
                            attendee = cls.objects.create(note=note, user=user, attendee_name=attendee_name)
                        else:
                            attendee = cls.objects.create(note=note, attendee_name=attendee_name)

                        logger.info(f"Attendee created: {attendee.get_name()}")

                    processed_attendees.add(existing_attendee.id if existing_attendee else attendee.id)

                except Exception:
                    logger.error(f"Could not process attendee: {attendee_data.get('name', 'Unknown')}", exc_info=True)

            # Delete attendees not in the current list
            attendees_to_delete = existing_attendees - processed_attendees
            cls.objects.filter(id__in=attendees_to_delete).delete()
            logger.info(f"Deleted {len(attendees_to_delete)} attendees no longer in the list")

        except Exception as e:
            logger.error(str(e), exc_info=True)
            raise e

    class Meta:
        verbose_name = "Attendee"
        verbose_name_plural = "Attendees"
        constraints = [
            models.UniqueConstraint(fields=["note", "client"], name="unique_note_client"),
            models.UniqueConstraint(fields=["note", "user"], name="unique_note_user"),
        ]
