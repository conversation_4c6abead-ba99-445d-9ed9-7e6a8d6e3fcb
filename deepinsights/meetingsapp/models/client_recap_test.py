from unittest.mock import MagicMock, patch

import pytest
from pytest_django.fixtures import SettingsWrapper

from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.client_recap import ClientRecap
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.user import User


class TestClientRecap:
    @pytest.fixture(autouse=True)
    def setUp(self) -> None:
        self.organization = Organization.objects.create(name="Test Organization")
        self.user = User.objects.create(username="testuser", email="<EMAIL>", organization=self.organization)
        self.client = Client.objects.create(name="Test Client", organization=self.organization, crm_id="456")
        self.client.authorized_users.add(self.user)
        self.client.save()
        self.note = Note.objects.create(
            note_owner=self.user, summary={"sections": [{"topic": "Test Topic", "bullets": ["Test bullet"]}]}
        )
        self.client_recap = ClientRecap.objects.create(
            user=self.user,
            client=self.client,
            summary={
                "recap": [
                    {
                        "topic": "Test Recap Topic",
                        "bullets": [
                            {
                                "text": "Test bullet",
                                "references": [
                                    {"source": "Z<PERSON>lyn", "meeting_uuid": str(self.note.uuid), "summary": "Test Topic"}
                                ],
                            }
                        ],
                    }
                ]
            },
        )

    def test_client_recap_creation(self) -> None:
        assert isinstance(self.client_recap, ClientRecap)
        assert self.client_recap.user == self.user
        assert self.client_recap.client == self.client

    def test_get_client_recap_summary(self, settings: SettingsWrapper) -> None:
        settings.APP_DOMAIN = "https://test.zeplyn.com"
        client_recap_details = self.client_recap.get_client_recap_summary()
        summary = client_recap_details["client_recap_summary"]
        assert isinstance(summary, list)
        assert len(summary) == 1

        category = summary[0]
        assert category["topic"] == "Test Recap Topic"
        assert len(category["bullets"]) == 1

        bullet = category["bullets"][0]
        assert bullet["text"] == "Test bullet"
        assert len(bullet["references"]) == 1

        reference = client_recap_details["references"][0]
        expected_link = f"https://test.zeplyn.com/notes/{self.note.uuid}?tab=summary&topic=0"
        assert reference["link"] == expected_link
        assert reference["hover_text"] == "\nTest Topic\n • Test bullet\n"

    def test_client_recap_with_empty_summary(self) -> None:
        empty_client_recap_details = ClientRecap.objects.create(user=self.user, client=self.client, summary={})
        summary = empty_client_recap_details.get_client_recap_summary()["client_recap_summary"]
        assert summary == []

    def test_client_recap_with_multiple_categories(self) -> None:
        multi_category_prep = ClientRecap.objects.create(
            user=self.user,
            client=self.client,
            summary={
                "recap": [
                    {"topic": "Category 1", "bullets": [{"text": "Bullet 1", "references": []}]},
                    {"topic": "Category 2", "bullets": [{"text": "Bullet 2", "references": []}]},
                ]
            },
        )
        summary = multi_category_prep.get_client_recap_summary()["client_recap_summary"]
        assert len(summary) == 2
        assert summary[0]["topic"] == "Category 1"
        assert summary[1]["topic"] == "Category 2"

    def test_client_recap_with_multiple_bullets(self) -> None:
        multi_bullet_prep = ClientRecap.objects.create(
            user=self.user,
            client=self.client,
            summary={
                "recap": [
                    {
                        "topic": "Multi-bullet Category",
                        "bullets": [{"text": "Bullet 1", "references": []}, {"text": "Bullet 2", "references": []}],
                    }
                ]
            },
        )
        summary = multi_bullet_prep.get_client_recap_summary()["client_recap_summary"]
        assert len(summary[0]["bullets"]) == 2
        assert summary[0]["bullets"][0]["text"] == "Bullet 1"
        assert summary[0]["bullets"][1]["text"] == "Bullet 2"

    def test_client_recap_with_references(self) -> None:
        note2 = Note.objects.create(
            note_owner=self.user,
            summary={"sections": [{"topic": "Another Test Topic", "bullets": ["Another test bullet"]}]},
        )

        multi_reference_prep = ClientRecap.objects.create(
            user=self.user,
            client=self.client,
            summary={
                "recap": [
                    {
                        "topic": "Multi-reference Category",
                        "bullets": [
                            {
                                "text": "Bullet with multiple references",
                                "references": [
                                    {"source": "Zeplyn", "meeting_uuid": str(self.note.uuid), "summary": "Test Topic"},
                                    {
                                        "source": "Zeplyn",
                                        "meeting_uuid": str(note2.uuid),
                                        "summary": "Another Test Topic",
                                    },
                                ],
                            }
                        ],
                    }
                ]
            },
        )

        client_recap_details = multi_reference_prep.get_client_recap_summary()

        summary = client_recap_details["client_recap_summary"]
        references = client_recap_details["references"]
        assert len(references) == 2
        assert references[summary[0]["bullets"][0]["references"][0]] == references[0]

    def test_client_recap_with_multiple_zeplyn_references(self) -> None:
        note2 = Note.objects.create(
            note_owner=self.user,
            summary={"sections": [{"topic": "Another Test Topic", "bullets": ["Another test bullet"]}]},
        )
        multi_reference_prep = ClientRecap.objects.create(
            user=self.user,
            client=self.client,
            summary={
                "recap": [
                    {
                        "topic": "Multi-reference Category",
                        "bullets": [
                            {
                                "text": "Bullet with multiple references",
                                "references": [
                                    {"source": "Zeplyn", "meeting_uuid": str(self.note.uuid), "summary": "Test Topic"},
                                    {
                                        "source": "Zeplyn",
                                        "meeting_uuid": str(note2.uuid),
                                        "summary": "Another Test Topic",
                                    },
                                ],
                            }
                        ],
                    }
                ]
            },
        )
        client_recap_details = multi_reference_prep.get_client_recap_summary()
        summary = client_recap_details["client_recap_summary"]
        assert len(summary[0]["bullets"][0]["references"]) == 2
        assert client_recap_details["references"][0]["hover_text"] == "\nTest Topic\n • Test bullet\n"
        assert client_recap_details["references"][1]["hover_text"] == "\nAnother Test Topic\n • Another test bullet\n"

    def test_client_recap_with_missing_zeplyn_references(self) -> None:
        note2 = Note.objects.create(
            note_owner=self.user,
            summary={"sections": [{"topic": "Another Test Topic", "bullets": ["Another test bullet"]}]},
        )
        note2_uuid = str(note2.uuid)
        note2.delete()

        prep = ClientRecap.objects.create(
            user=self.user,
            client=self.client,
            summary={
                "recap": [
                    {
                        "topic": "Multi-reference Category",
                        "bullets": [
                            {
                                "text": "Bullet with multiple references",
                                "references": [
                                    {"source": "Zeplyn", "meeting_uuid": str(self.note.uuid), "summary": "Test Topic"},
                                    {
                                        "source": "Zeplyn",
                                        "meeting_uuid": note2_uuid,
                                        "summary": "Another Test Topic",
                                    },
                                ],
                            }
                        ],
                    }
                ]
            },
        )
        client_recap_details = prep.get_client_recap_summary()
        summary = client_recap_details["client_recap_summary"]
        assert len(summary[0]["bullets"][0]["references"]) == 1
        assert client_recap_details["references"][0]["hover_text"] == "\nTest Topic\n • Test bullet\n"

    def test_client_recap_mix_zeplyn_structured_data_ref(self) -> None:
        note2 = Note.objects.create(
            note_owner=self.user,
            summary={"sections": [{"topic": "Another Test Topic", "bullets": ["Another test bullet"]}]},
        )
        multi_reference_prep = ClientRecap.objects.create(
            user=self.user,
            client=self.client,
            summary={
                "recap": [
                    {
                        "topic": "Multi-reference Category",
                        "bullets": [
                            {
                                "text": "Bullet with multiple references",
                                "references": [
                                    {"source": "structured data", "meeting_uuid": "", "summary": ""},
                                    {
                                        "source": "Zeplyn",
                                        "meeting_uuid": str(note2.uuid),
                                        "summary": "Another Test Topic",
                                    },
                                ],
                            }
                        ],
                    }
                ]
            },
        )
        client_recap_details = multi_reference_prep.get_client_recap_summary()
        summary = client_recap_details["client_recap_summary"]
        assert len(summary[0]["bullets"][0]["references"]) == 1
        assert client_recap_details["references"][0]["hover_text"] == "\nAnother Test Topic\n • Another test bullet\n"

    def test_client_recap_multiple_structured(self) -> None:
        note2 = Note.objects.create(
            note_owner=self.user,
            summary={"sections": [{"topic": "Another Test Topic", "bullets": ["Another test bullet"]}]},
        )
        multi_reference_prep = ClientRecap.objects.create(
            user=self.user,
            client=self.client,
            summary={
                "recap": [
                    {
                        "topic": "Multi-reference Category",
                        "bullets": [
                            {
                                "text": "Bullet with multiple references",
                                "references": [
                                    {"source": "structured data", "meeting_uuid": "", "summary": ""},
                                    {
                                        "source": "Zeplyn",
                                        "meeting_uuid": str(note2.uuid),
                                        "summary": "Another Test Topic",
                                    },
                                ],
                            }
                        ],
                    },
                    {
                        "topic": "Multi-reference Category 2",
                        "bullets": [
                            {
                                "text": "Bullet with multiple references",
                                "references": [
                                    {"source": "structured data", "meeting_uuid": "", "summary": ""},
                                ],
                            }
                        ],
                    },
                ]
            },
        )
        client_recap_details = multi_reference_prep.get_client_recap_summary()
        summary = client_recap_details["client_recap_summary"]
        assert len(summary[0]["bullets"][0]["references"]) == 1
        assert len(summary[1]["bullets"][0]["references"]) == 0

        assert client_recap_details["references"][0]["hover_text"] == "\nAnother Test Topic\n • Another test bullet\n"

    def test_multiple_sections_from_same_note(self) -> None:
        """Test that references to different sections of the same note are handled correctly."""
        # Create a note with multiple sections
        multi_section_note = Note.objects.create(
            note_owner=self.user,
            summary={
                "sections": [
                    {"topic": "First Section", "bullets": ["Bullet in first section"]},
                    {"topic": "Second Section", "bullets": ["Bullet in second section"]},
                ]
            },
        )

        # Create a client recap in DB with references to both sections of the same note
        client_recap = ClientRecap.objects.create(
            user=self.user,
            client=self.client,
            summary={
                "recap": [
                    {
                        "topic": "Topic with sections from same note",
                        "bullets": [
                            {
                                "text": "References first section",
                                "references": [
                                    {
                                        "source": "Zeplyn",
                                        "meeting_uuid": str(multi_section_note.uuid),
                                        "summary": "First Section",
                                    }
                                ],
                            },
                            {
                                "text": "References second section",
                                "references": [
                                    {
                                        "source": "Zeplyn",
                                        "meeting_uuid": str(multi_section_note.uuid),
                                        "summary": "Second Section",
                                    }
                                ],
                            },
                            {
                                "text": "References both sections",
                                "references": [
                                    {
                                        "source": "Zeplyn",
                                        "meeting_uuid": str(multi_section_note.uuid),
                                        "summary": "First Section",
                                    },
                                    {
                                        "source": "Zeplyn",
                                        "meeting_uuid": str(multi_section_note.uuid),
                                        "summary": "Second Section",
                                    },
                                ],
                            },
                        ],
                    }
                ]
            },
        )

        # Get the client recap summary to send to frontend
        client_recap_details = client_recap.get_client_recap_summary()
        summary = client_recap_details["client_recap_summary"]
        references = client_recap_details["references"]

        # Verify we have exactly 2 unique references (one for each section)
        assert len(references) == 2

        # Get the reference indices for each bullet
        first_bullet_refs = summary[0]["bullets"][0]["references"]
        second_bullet_refs = summary[0]["bullets"][1]["references"]
        third_bullet_refs = summary[0]["bullets"][2]["references"]

        # The first and second bullets should each have one reference
        assert len(first_bullet_refs) == 1
        assert len(second_bullet_refs) == 1

        # The third bullet should have two references
        assert len(third_bullet_refs) == 2

        # The references should be distinct
        assert first_bullet_refs[0] != second_bullet_refs[0]

        # The third bullet's references should include both section references
        assert set(third_bullet_refs) == {first_bullet_refs[0], second_bullet_refs[0]}

        # Check the hover text for the references
        hover_texts = [ref.get("hover_text") for ref in references]
        assert len(hover_texts) == 2
        assert "\nFirst Section\n • Bullet in first section\n" in hover_texts
        assert "\nSecond Section\n • Bullet in second section\n" in hover_texts

    @pytest.mark.parametrize(
        "crm_system, salesforce_type, reference_link, expected_link",
        [
            ("wealthbox", None, "http://example.com", "http://example.com"),
            # This case is tested more thoroughly in the test below.
            ("wealthbox", None, None, "https://app.crmworkspace.com/None/status_updates/123"),
            ("redtail", None, "http://example.com", "http://example.com"),
            ("redtail", None, None, "https://crm.redtailtechnology.com/contacts/456/activities/123"),
            ("salesforce", None, "http://example.com", "http://example.com"),
            ("salesforce", None, None, None),
            ("salesforce", "base", "http://example.com", "http://example.com"),
            ("salesforce", "base", None, None),
            ("external", None, None, None),
            ("external", None, "http://example.com/link", "http://example.com/link"),
        ],
    )
    def test_client_recap_crm_reference_links(
        self,
        crm_system: str,
        salesforce_type: str | None,
        reference_link: str | None,
        expected_link: str,
    ) -> None:
        crm_configuration = self.user.get_crm_configuration()
        crm_configuration.crm_system = crm_system
        crm_configuration.salesforce.type = salesforce_type or ""
        self.user.crm_configuration = crm_configuration.to_dict()
        self.user.save()

        multi_reference_prep = ClientRecap.objects.create(
            user=self.user,
            client=self.client,
            summary={
                "recap": [
                    {
                        "topic": "Multi-reference Category",
                        "bullets": [
                            {
                                "text": "Bullet with multiple references",
                                "references": [
                                    {
                                        "source": crm_system,
                                        "meeting_uuid": "123",
                                        "link": expected_link,
                                        **({"reference_link": reference_link} if reference_link else {}),
                                    },
                                ],
                            }
                        ],
                    }
                ]
            },
        )

        client_recap_details = multi_reference_prep.get_client_recap_summary()
        if not expected_link:
            assert client_recap_details.get("references") == []
        else:
            assert client_recap_details.get("references") == [
                {
                    "meeting_uuid": "123",
                    "source": crm_system,
                    "link": expected_link,
                }
            ]

    @pytest.mark.parametrize(
        "reference_source, expected_link",
        [
            (None, None),
            ("invalid", None),
            ("wealthbox", "https://app.crmworkspace.com/456/status_updates/123"),
            ("Wealthbox", "https://app.crmworkspace.com/456/status_updates/123"),
            ("redtail", "https://crm.redtailtechnology.com/contacts/456/activities/123"),
            ("salesforce", "https://example.com/123"),
        ],
    )
    @patch("deepinsights.meetingsapp.models.client_recap.Wealthbox")
    def test_client_recap_with_source_based_reference(
        self, mock_wealthbox: MagicMock, reference_source: str, expected_link: str
    ) -> None:
        mock_wealthbox.return_value.get_user_account_by_self.return_value = "456"
        self.user.crm_configuration.setdefault("salesforce", {}).setdefault(
            "salesforce_endpoint", "https://example.com"
        )
        self.user.save()

        client_recap = ClientRecap.objects.create(
            user=self.user,
            client=self.client,
            summary={
                "recap": [
                    {
                        "topic": "Test Recap Topic",
                        "bullets": [
                            {
                                "text": "Test bullet",
                                "references": [
                                    {"source": reference_source, "meeting_uuid": "123", "summary": "Summary"}
                                ],
                            }
                        ],
                    }
                ]
            },
        )

        client_recap_details = client_recap.get_client_recap_summary()
        references = client_recap_details["references"]

        if expected_link:
            assert len(references) == 1
            assert references[0]["link"] == expected_link
        else:
            assert len(references) == 0

    def test_client_recap_reference_salesforce_no_instance_url(
        self,
    ) -> None:
        self.user.crm_configuration.setdefault("salesforce", {}).setdefault("type", "base")
        self.user.save()

        client_recap = ClientRecap.objects.create(
            user=self.user,
            client=self.client,
            summary={
                "recap": [
                    {
                        "topic": "Test Recap Topic",
                        "bullets": [
                            {
                                "text": "Test bullet",
                                "references": [{"source": "salesforce", "meeting_uuid": "123", "summary": "Summary"}],
                            }
                        ],
                    }
                ]
            },
        )

        client_recap_details = client_recap.get_client_recap_summary()
        references = client_recap_details["references"]

        assert len(references) == 0
