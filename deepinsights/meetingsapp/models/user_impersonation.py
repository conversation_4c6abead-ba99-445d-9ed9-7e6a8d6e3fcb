from django.db import models

from deepinsights.core.behaviours import StatusMixin, UUIDMixin


class UserImpersonation(UUIDMixin, StatusMixin):
    user_impersonated = models.ForeignKey(
        "users.User",
        related_name="user_impersonated",
        on_delete=models.DO_NOTHING,
        help_text="The user to be impersonated",
    )
    impersonated_email = models.EmailField(
        help_text="The email of the impersonated user",
        blank=False,
    )

    user_impersonating = models.ForeignKey(
        "users.User",
        related_name="user_impersonation",
        on_delete=models.DO_NOTHING,
        help_text="The user impersonating",
    )

    impersonating_email = models.EmailField(
        help_text="The email of the impersonating user",
        blank=False,
    )

    access_token = models.TextField(help_text="The access token for the impersonation", blank=False)

    purpose = models.TextField(help_text="The purpose of the impersonation")
