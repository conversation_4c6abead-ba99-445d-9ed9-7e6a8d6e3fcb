import datetime
import logging
from unittest.mock import ANY, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ock, call, patch

import phonenumbers
import pytest
from aioresponses import aioresponses
from django.core.exceptions import SynchronousOnlyOperation
from pytest_django.fixtures import SettingsWrapper
from twilio.base.exceptions import TwilioRestException

from deepinsights.core.integrations.meetingbot.bot_controller import BotMeetingType, BotStatus
from deepinsights.core.integrations.meetingbot.recall_ai import <PERSON><PERSON>l<PERSON>otController
from deepinsights.core.integrations.meetingbot.twilio import <PERSON><PERSON><PERSON>CallBotController, TwilioConferenceBotController
from deepinsights.core.preferences.preferences import get_default_preferences
from deepinsights.meetingsapp.bot_processing_task import BotProcessingTask
from deepinsights.meetingsapp.models.meeting_bot import MeetingBot
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.phone_number import PhoneNumber
from deepinsights.meetingsapp.tasks import process_bot_recording, process_phone_call_recording
from deepinsights.users.models.user import User

pytestmark = [pytest.mark.django_db]


# The tests in this file are tightly coupled with the underlying bot controller implementations.
# This is essentially a mini integration test, to make sure that changes to this code or to
# the bot controllers do not break bot functionality.


def _example_number(country_code: str, format: int = phonenumbers.PhoneNumberFormat.E164) -> str:
    example_number = phonenumbers.example_number(country_code)
    if not example_number:
        raise ValueError(f"Could not create number for country code {country_code}")
    return phonenumbers.format_number(example_number, format)


def _invalid_number(country_code: str, format: int = phonenumbers.PhoneNumberFormat.E164) -> str:
    example_number = phonenumbers.invalid_example_number(country_code)
    if not example_number:
        raise ValueError(f"Could not create number for country code {country_code}")
    return phonenumbers.format_number(example_number, format)


@pytest.mark.parametrize(
    "bot_id, url, user_phone_number, other_user_phone_number, org_phone_number, expected_bot_controller",
    [
        (None, None, None, None, None, RecallBotController),
        (None, "", None, None, None, RecallBotController),
        (None, "http://example.com", None, None, None, RecallBotController),
        (None, "**********", None, None, None, TwilioCallBotController),
        (None, "**********", None, None, None, TwilioCallBotController),
        (None, "**********", "**********", None, None, TwilioCallBotController),
        (None, "**********", "**********", None, None, TwilioConferenceBotController),
        (None, "+1**********", "**********", None, None, TwilioCallBotController),
        (None, "(*************", "(*************", None, None, TwilioCallBotController),
        (None, _example_number("GB"), None, None, None, TwilioCallBotController),
        (
            None,
            _example_number("AU", phonenumbers.PhoneNumberFormat.INTERNATIONAL),
            None,
            None,
            None,
            TwilioCallBotController,
        ),
        (
            None,
            _example_number("US", phonenumbers.PhoneNumberFormat.NATIONAL),
            None,
            None,
            None,
            TwilioCallBotController,
        ),
        # These are valid phone numbers, but our logic of checking length prevents it from working.
        # This test is here to document current behavior, not to indicate that it's the most correct
        # approach.
        (None, _example_number("US", phonenumbers.PhoneNumberFormat.RFC3966), None, None, None, RecallBotController),
        (None, _example_number("CA", phonenumbers.PhoneNumberFormat.RFC3966), None, None, None, RecallBotController),
        (
            None,
            _example_number("GB", phonenumbers.PhoneNumberFormat.INTERNATIONAL),
            None,
            None,
            None,
            RecallBotController,
        ),
        (None, _example_number("GB", phonenumbers.PhoneNumberFormat.RFC3966), None, None, None, RecallBotController),
        # These are valid phone numbers, but because we assume a US country code, they are not handled.
        (None, _example_number("GB", phonenumbers.PhoneNumberFormat.NATIONAL), None, None, None, RecallBotController),
        (None, _example_number("GB", phonenumbers.PhoneNumberFormat.NATIONAL), None, None, None, RecallBotController),
        # End tests of valid but unhandled phone numbers.
        (None, _invalid_number("US"), None, None, None, RecallBotController),
        (None, "/1**********", None, None, None, RecallBotController),
        (None, "1**********/", None, None, None, RecallBotController),
        (None, "https://zoom.us/j/7156907842", None, None, None, RecallBotController),
        (None, "https://zoom.us/j/7156907842/", "**********", None, None, RecallBotController),
        (None, "https://zoom.us/j/7156907842?pwd=abc", None, None, None, RecallBotController),
        (None, "https://zoom.us/j/7156907842?pwd=abc", "**********", None, None, RecallBotController),
        # Recall bot IDs for Twilio bots
        ("", "http://meet.google.com", None, None, None, RecallBotController),
        ("CA1234567890", "http://meet.google.com", None, None, None, RecallBotController),
        ("invalid", "**********", None, None, None, TwilioCallBotController),
        ("CA1234567890", "**********", "**********", None, None, TwilioCallBotController),
        ("CF1234567890", "**********", None, None, None, TwilioConferenceBotController),
        # User-level phone numbers: any should match the meeting link phone number
        (None, "**********", None, "**********", None, TwilioCallBotController),
        (None, "**********", "**********", "**********", None, TwilioCallBotController),
        (None, "**********", "**********", "2125551214", None, TwilioConferenceBotController),
        # Org-level phone numbers: if the user has phone numbers and the org has a phone number,
        # and the incoming call matches the org phone number, then this is a single-party call.
        (None, "**********", None, None, "**********", TwilioCallBotController),
        (None, "**********", None, None, "**********", TwilioCallBotController),
        (None, "2125551214", "**********", None, "2125551214", TwilioCallBotController),
        (None, "2125551214", "**********", "**********", "2125551214", TwilioCallBotController),
    ],
)
def test_bot_controller(
    bot_id: str | None,
    url: str | None,
    user_phone_number: str | None,
    other_user_phone_number: str | None,
    org_phone_number: str | None,
    expected_bot_controller: type,
) -> None:
    org = Organization.objects.create(name="Test Org")
    user = User.objects.create(organization=org)
    if user_phone_number:
        user.phone_numbers.add(PhoneNumber.objects.create(number=user_phone_number, primary=True))
        user.save()
    if other_user_phone_number:
        user.phone_numbers.add(PhoneNumber.objects.create(number=other_user_phone_number))
        user.save()
    if org_phone_number:
        org.phone_number = org_phone_number
        org.save()

    bot = MeetingBot.objects.create(meeting_link=url, bot_owner=user, recall_bot_id=bot_id)
    assert isinstance(bot._bot_controller(), expected_bot_controller)


@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_bot_controller_async_check(settings: SettingsWrapper, caplog: pytest.LogCaptureFixture) -> None:
    settings.DEBUG = True
    bot = await MeetingBot.objects.acreate(meeting_link="http://example.com")
    with pytest.raises(SynchronousOnlyOperation):
        bot._bot_controller()
        assert len(caplog.records) == 1
        assert "_bot_controller() called from async context, which is not supported." in caplog.text


@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_bot_controller_async_check_without_debug_mode(
    settings: SettingsWrapper, caplog: pytest.LogCaptureFixture
) -> None:
    settings.DEBUG = False
    bot = await MeetingBot.objects.acreate(meeting_link="http://example.com")
    bot._bot_controller()
    assert len(caplog.records) == 1
    assert "_bot_controller() called from async context, which is not supported." in caplog.text


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_get_status_no_bot_id(mock_requests: MagicMock) -> None:
    assert MeetingBot.objects.create().get_status() == BotStatus.NOT_CREATED
    mock_requests.get.assert_not_called()


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_get_status_error(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="bot_id")
    mock_requests.get.side_effect = Exception("Failed to get status")

    assert bot.get_status() == BotStatus.UNKNOWN
    mock_requests.get.assert_called_once_with("https://api.recall.ai/api/v1/bot/bot_id/", headers=ANY)


@pytest.mark.parametrize(
    "status_changes, expected_status",
    [
        ([], BotStatus.UNKNOWN),
        ([{"invalid": "response"}], BotStatus.UNKNOWN),
        ([{"code": "in_waiting_room"}], BotStatus.IN_WAITING_ROOM),
        ([{"code": "joining_call"}, {"code": "in_waiting_room"}], BotStatus.IN_WAITING_ROOM),
        (
            [{"code": "joining_call"}, {"code": "in_waiting_room"}, {"code": "in_call_recording"}],
            BotStatus.IN_CALL_RECORDING,
        ),
        (
            [{"code": "joining_call"}, {"code": "in_waiting_room"}, {"code": "in_call_recording"}],
            BotStatus.IN_CALL_RECORDING,
        ),
        (
            [{"code": "in_call_recording"}, {"code": "in_call_not_recording"}, {"code": "in_call_recording"}],
            BotStatus.IN_CALL_RECORDING,
        ),
        (
            [{"code": "in_call_recording"}, {"code": "in_call_not_recording"}, {"code": "in_call_not_recording"}],
            BotStatus.IN_CALL_NOT_RECORDING,
        ),
        (
            [{"code": "in_call_recording"}, {"code": "call_ended"}],
            BotStatus.CALL_ENDED,
        ),
        (
            [{"code": "in_call_recording"}, {"code": "done"}],
            BotStatus.RECORDING_DONE,
        ),
        (
            [{"code": "recording_permission_denied"}],
            BotStatus.ERROR,
        ),
        (
            [{"code": "recording_permission_denied"}, {"code": "in_call_recording"}],
            BotStatus.IN_CALL_RECORDING,
        ),
    ],
)
class TestRecallBotStatus:
    @patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
    def test_get_status(
        self, mock_requests: MagicMock, status_changes: list[dict[str, str]], expected_status: BotStatus
    ) -> None:
        bot = MeetingBot.objects.create(recall_bot_id="bot_id")
        mock_requests.get.return_value = MagicMock(json=lambda: {"status_changes": status_changes})

        assert bot.get_status() == expected_status
        mock_requests.get.assert_called_once_with("https://api.recall.ai/api/v1/bot/bot_id/", headers=ANY)

    @pytest.mark.django_db
    @pytest.mark.asyncio
    async def test_get_async_status(self, status_changes: list[dict[str, str]], expected_status: BotStatus) -> None:
        bot = await MeetingBot.objects.acreate(recall_bot_id="bot_id")
        with aioresponses() as mocked_responses:
            mocked_responses.get("https://api.recall.ai/api/v1/bot/bot_id/", payload={"status_changes": status_changes})
            assert await bot.aget_status() == expected_status


def test_get_status_for_phone_call_bot_no_bot_id() -> None:
    bot = MeetingBot.objects.create(meeting_link="**********")
    assert bot.get_status() == BotStatus.NOT_CREATED


@pytest.mark.asyncio
async def test_async_get_status_for_phone_call_bot_no_bot_id() -> None:
    bot = await MeetingBot.objects.acreate(meeting_link="**********")
    assert await bot.aget_status() == BotStatus.NOT_CREATED


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_get_status_for_phone_call_bot_call_error(mock_twilio_client: MagicMock) -> None:
    bot = MeetingBot.objects.create(meeting_link="**********", recall_bot_id="bot_id")
    mock_twilio_client.side_effect = TwilioRestException(status=400, uri="uri")
    assert bot.get_status() == BotStatus.UNKNOWN


@pytest.mark.asyncio
@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
async def test_async_get_status_for_phone_call_bot_call_error(mock_twilio_client: MagicMock) -> None:
    bot = await MeetingBot.objects.acreate(meeting_link="**********", recall_bot_id="bot_id")
    mock_twilio_client.side_effect = TwilioRestException(status=400, uri="uri")
    assert await bot.aget_status() == BotStatus.UNKNOWN


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_get_status_for_phone_call_bot_recordings_error(mock_twilio_client: MagicMock) -> None:
    bot = MeetingBot.objects.create(meeting_link="**********", recall_bot_id="bot_id")
    mock_calls = mock_twilio_client.return_value.calls.get.return_value
    mock_calls.fetch.return_value.status = "in-progress"
    mock_calls.recordings.list.side_effect = TwilioRestException(status=400, uri="uri")

    assert bot.get_status() == BotStatus.UNKNOWN


@pytest.mark.asyncio
@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
async def test_async_get_status_for_phone_call_bot_recordings_error(mock_twilio_client: MagicMock) -> None:
    bot = await MeetingBot.objects.acreate(meeting_link="**********", recall_bot_id="bot_id")
    mock_calls = mock_twilio_client.return_value.calls.get.return_value
    mock_calls.fetch_async = AsyncMock(return_value=MagicMock(status="in-progress"))
    mock_calls.recordings.list_async.side_effect = TwilioRestException(status=400, uri="uri")
    assert await bot.aget_status() == BotStatus.UNKNOWN


@pytest.mark.parametrize(
    "call_status, recording_statuses, expected_status",
    [
        # Non in-progress call states
        ("queued", [], BotStatus.IN_WAITING_ROOM),
        ("ringing", [], BotStatus.IN_WAITING_ROOM),
        ("busy", [], BotStatus.ERROR),
        ("failed", [], BotStatus.ERROR),
        ("no-answer", [], BotStatus.ERROR),
        ("canceled", [], BotStatus.ERROR),
        ("completed", [], BotStatus.CALL_ENDED),
        ("invalid", [], BotStatus.UNKNOWN),
        # In-progress call states
        ("in-progress", [], BotStatus.IN_WAITING_ROOM),
        ("in-progress", ["stopped"], BotStatus.IN_CALL_NOT_RECORDING),
        ("in-progress", ["stopped", "paused"], BotStatus.IN_CALL_NOT_RECORDING),
        ("in-progress", ["in-progress"], BotStatus.IN_CALL_RECORDING),
        ("in-progress", ["in-progress", "paused"], BotStatus.IN_CALL_NOT_RECORDING),
        ("in-progress", ["in-progress", "in-progress"], BotStatus.IN_CALL_RECORDING),
        ("in-progress", ["in-progress", "processing"], BotStatus.IN_CALL_RECORDING),
    ],
)
class TestCallStatus:
    @patch("deepinsights.core.integrations.meetingbot.twilio.Client")
    def test_get_status_for_phone_call_bot(
        self,
        mock_twilio_client: MagicMock,
        call_status: str | None,
        recording_statuses: list[str],
        expected_status: BotStatus,
    ) -> None:
        mock_calls = mock_twilio_client.return_value.calls.get.return_value
        mock_calls.fetch.return_value.status = call_status

        mock_calls.recordings.list.return_value = [MagicMock(status=status) for status in recording_statuses]
        bot = MeetingBot.objects.create(meeting_link="**********", recall_bot_id="bot_id")
        assert bot.get_status() == expected_status

    @pytest.mark.asyncio
    @pytest.mark.django_db(transaction=True)
    @patch("deepinsights.core.integrations.meetingbot.twilio.Client")
    async def test_get_async_status_for_phone_call_bot(
        self,
        mock_twilio_client: MagicMock,
        call_status: str | None,
        recording_statuses: list[str],
        expected_status: BotStatus,
    ) -> None:
        mock_calls = mock_twilio_client.return_value.calls.get.return_value
        mock_calls.fetch_async = AsyncMock(return_value=MagicMock(status=call_status))
        mock_calls.recordings.list_async = AsyncMock(
            return_value=[MagicMock(status=status) for status in recording_statuses]
        )
        bot = await MeetingBot.objects.acreate(meeting_link="**********", recall_bot_id="bot_id")
        assert await bot.aget_status() == expected_status


def test_get_status_for_phone_conference_bot_no_bot_id() -> None:
    user = User.objects.create()
    user.phone_numbers.add(PhoneNumber.objects.create(number="**********"))
    user.save()
    bot = MeetingBot.objects.create(bot_owner=user, meeting_link="**********")
    assert bot.get_status() == BotStatus.NOT_CREATED


@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
async def test_async_get_status_for_phone_conference_bot_no_bot_id() -> None:
    user = await User.objects.acreate()
    await user.phone_numbers.aadd(await PhoneNumber.objects.acreate(number="**********"))
    await user.asave()
    bot = await MeetingBot.objects.acreate(bot_owner=user, meeting_link="**********")
    assert await bot.aget_status() == BotStatus.NOT_CREATED


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_get_status_for_phone_conference_bot_call_error(mock_twilio_client: MagicMock) -> None:
    user = User.objects.create()
    user.phone_numbers.add(PhoneNumber.objects.create(number="**********"))
    user.save()
    bot = MeetingBot.objects.create(bot_owner=user, meeting_link="**********", recall_bot_id="bot_id")
    mock_twilio_client.side_effect = TwilioRestException(status=400, uri="uri")
    assert bot.get_status() == BotStatus.UNKNOWN


@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
async def test_get_async_status_for_phone_conference_bot_call_error(mock_twilio_client: MagicMock) -> None:
    user = await User.objects.acreate()
    await user.phone_numbers.aadd(await PhoneNumber.objects.acreate(number="**********"))
    await user.asave()
    bot = await MeetingBot.objects.acreate(bot_owner=user, meeting_link="**********", recall_bot_id="bot_id")
    mock_twilio_client.side_effect = TwilioRestException(status=400, uri="uri")
    assert await bot.aget_status() == BotStatus.UNKNOWN
    assert await bot.aget_status() == BotStatus.UNKNOWN


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_get_status_for_phone_conference_bot_participants_error(mock_twilio_client: MagicMock) -> None:
    user = User.objects.create()
    user.phone_numbers.add(PhoneNumber.objects.create(number="**********"))
    user.save()
    bot = MeetingBot.objects.create(bot_owner=user, meeting_link="**********", recall_bot_id="bot_id")
    mock_conferences = mock_twilio_client.return_value.conferences.get.return_value
    mock_conferences.fetch.return_value.status = "in-progress"
    mock_conferences.participants.list.side_effect = TwilioRestException(status=400, uri="uri")

    assert bot.get_status() == BotStatus.UNKNOWN


@pytest.mark.asyncio
@pytest.mark.django_db(transaction=True)
@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
async def test_async_get_status_for_phone_conference_bot_participants_error(mock_twilio_client: MagicMock) -> None:
    user = await User.objects.acreate()
    await user.phone_numbers.aadd(await PhoneNumber.objects.acreate(number="**********"))
    bot = await MeetingBot.objects.acreate(bot_owner=user, meeting_link="**********", recall_bot_id="bot_id")
    mock_conferences = mock_twilio_client.return_value.conferences.get.return_value
    mock_conferences.fetch_async = AsyncMock(return_value=MagicMock(status="in-progress"))
    mock_conferences.participants.list_async.side_effect = TwilioRestException(status=400, uri="uri")

    assert await bot.aget_status() == BotStatus.UNKNOWN


@pytest.mark.parametrize(
    "conference_status, participant_statuses, expected_status",
    [
        # Non in-progress conference states
        ("init", [], BotStatus.IN_WAITING_ROOM),
        ("completed", [], BotStatus.CALL_ENDED),
        ("invalid", [], BotStatus.UNKNOWN),
        # In-progress call states
        ("in-progress", [], BotStatus.IN_WAITING_ROOM),
        ("in-progress", ["failed"], BotStatus.ERROR),
        ("in-progress", ["connected"], BotStatus.IN_WAITING_ROOM),
        ("in-progress", ["connected", "connected"], BotStatus.IN_CALL_RECORDING),
        ("in-progress", ["connected", "queued"], BotStatus.IN_WAITING_ROOM),
        ("in-progress", ["connected", "connecting"], BotStatus.IN_WAITING_ROOM),
        ("in-progress", ["connected", "ringing"], BotStatus.IN_WAITING_ROOM),
        ("in-progress", ["connected", "connected", "ringing"], BotStatus.IN_WAITING_ROOM),
        ("in-progress", ["connected", "failed"], BotStatus.ERROR),
        ("in-progress", ["connected", "complete"], BotStatus.IN_CALL_RECORDING),
        ("in-progress", ["complete", "complete"], BotStatus.CALL_ENDED),
        ("in-progress", ["unknown", "unknown", "unknown"], BotStatus.UNKNOWN),
    ],
)
class TestConferenceStatus:
    @patch("deepinsights.core.integrations.meetingbot.twilio.Client")
    def test_get_status_for_phone_conference_bot(
        self,
        mock_twilio_client: MagicMock,
        conference_status: str | None,
        participant_statuses: list[str],
        expected_status: BotStatus,
    ) -> None:
        mock_conferences = mock_twilio_client.return_value.conferences.get.return_value
        mock_conferences.fetch.return_value.status = conference_status
        mock_conferences.participants.list.return_value = [MagicMock(status=status) for status in participant_statuses]

        user = User.objects.create()
        user.phone_numbers.add(PhoneNumber.objects.create(number="**********"))
        user.save()
        bot = MeetingBot.objects.create(bot_owner=user, meeting_link="**********", recall_bot_id="bot_id")

        assert bot.get_status() == expected_status

    @pytest.mark.asyncio
    @pytest.mark.django_db(transaction=True)
    @patch("deepinsights.core.integrations.meetingbot.twilio.Client")
    async def test_get_async_status_for_phone_conference_bot(
        self,
        mock_twilio_client: MagicMock,
        conference_status: str | None,
        participant_statuses: list[str],
        expected_status: BotStatus,
    ) -> None:
        mock_conferences = mock_twilio_client.return_value.conferences.get.return_value
        mock_conferences.fetch_async = AsyncMock(return_value=MagicMock(status=conference_status))
        mock_conferences.participants.list_async = AsyncMock(
            return_value=[MagicMock(status=status) for status in participant_statuses]
        )

        user = await User.objects.acreate()
        await user.phone_numbers.aadd(await PhoneNumber.objects.acreate(number="**********"))
        bot = await MeetingBot.objects.acreate(bot_owner=user, meeting_link="**********", recall_bot_id="bot_id")

        assert await bot.aget_status() == expected_status


@pytest.mark.parametrize(
    "url, user_phone_number, expected_meeting_type",
    [
        (None, None, BotMeetingType.VIDEO_CALL),
        ("", None, BotMeetingType.VIDEO_CALL),
        ("http://example.com", None, BotMeetingType.VIDEO_CALL),
        ("**********", None, BotMeetingType.PHONE_CALL),
        ("**********", None, BotMeetingType.PHONE_CALL),
        ("**********", "**********", BotMeetingType.PHONE_CALL),
        ("**********", "**********", BotMeetingType.PHONE_CALL),
    ],
)
def test_meeting_type(url: str | None, user_phone_number: str | None, expected_meeting_type: BotMeetingType) -> None:
    user = User.objects.create()
    if user_phone_number:
        user.phone_numbers.add(PhoneNumber.objects.create(number=user_phone_number))
        user.save()
    bot = MeetingBot.objects.create(meeting_link=url, bot_owner=user)
    assert bot.meeting_type == expected_meeting_type


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_create_bot_no_meeting_link(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(bot_owner=User.objects.create())
    bot.create_bot_and_start_recording()
    bot.refresh_from_db()
    assert not bot.recall_bot_id
    mock_requests.post.assert_not_called()


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_create_bot_no_bot_owner(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(meeting_link="http://example.com")
    bot.create_bot_and_start_recording()
    bot.refresh_from_db()
    assert not bot.recall_bot_id
    mock_requests.post.assert_not_called()


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
@patch("deepinsights.meetingsapp.models.meeting_bot.Flags")
def test_create_bot_success(mock_flags: MagicMock, mock_requests: MagicMock) -> None:
    user = User.objects.create()
    user.preferences = get_default_preferences()
    bot = MeetingBot.objects.create(meeting_link="http://example.com", bot_owner=user)
    mock_flags.EnableProcessRecallBotTranscriptsWithZeplynDeepgramIntegration.is_active_for_user.return_value = False
    mock_flags.EnableNativeZoomBots.is_active_for_user.return_value = True
    mock_requests.post.return_value = MagicMock(status_code=200, json=lambda: {"id": "bot_id"})

    bot.create_bot_and_start_recording()
    assert bot.recall_bot_id == "bot_id"
    mock_requests.post.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/",
        json={
            "transcription_options": {
                "provider": "deepgram",
                "deepgram": {
                    "language": "en",
                    "smart_format": True,
                    "model": "nova-2",
                    "redact": ["pci", "ssn"],
                    "numerals": True,
                    "utterances": True,
                    "keywords": [],
                    "replace": [
                        "zeplin:Zeplyn",
                        "zeplins:Zeplyns",
                        "zeplin's:Zeplyn's",
                        "zeppelin:Zeplyn",
                        "zeppelins:Zeplyns",
                        "zeppelin's:Zeplyn's",
                        "zeplon:Zeplyn",
                        "zeplons:Zeplyns",
                        "zeplon's:Zeplyn's",
                    ],
                    "log_data": False,
                    "mip_opt_out": True,
                },
            },
            "recording_mode_options": {"start_recording_on": "call_join"},
            "recording_mode": "audio_only",
            "bot_name": ANY,
            "metadata": ANY,
            "variant": {"zoom": "native"},
            "automatic_video_output": ANY,
            "automatic_audio_output": ANY,
            "meeting_url": "http://example.com",
        },
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
@patch("deepinsights.meetingsapp.models.meeting_bot.Flags")
def test_create_bot_user_asr_language_code(mock_flags: MagicMock, mock_requests: MagicMock) -> None:
    user = User.objects.create()
    preferences = user.get_preferences()
    preferences.asr_language_code = "es"
    user.preferences = preferences.to_dict()
    user.save()

    bot = MeetingBot.objects.create(meeting_link="http://example.com", bot_owner=user)
    mock_flags.EnableProcessRecallBotTranscriptsWithZeplynDeepgramIntegration.is_active_for_user.return_value = False
    mock_flags.EnableNativeZoomBots.is_active_for_user.return_value = True
    mock_requests.post.return_value = MagicMock(status_code=200, json=lambda: {"id": "bot_id"})

    bot.create_bot_and_start_recording()
    assert bot.recall_bot_id == "bot_id"
    mock_requests.post.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/",
        json={
            "transcription_options": {
                "provider": "deepgram",
                "deepgram": {
                    "language": "es",
                    "smart_format": True,
                    "model": "nova-2",
                    "redact": ["pci", "ssn"],
                    "numerals": True,
                    "utterances": True,
                    "keywords": [],
                    "replace": [
                        "zeplin:Zeplyn",
                        "zeplins:Zeplyns",
                        "zeplin's:Zeplyn's",
                        "zeppelin:Zeplyn",
                        "zeppelins:Zeplyns",
                        "zeppelin's:Zeplyn's",
                        "zeplon:Zeplyn",
                        "zeplons:Zeplyns",
                        "zeplon's:Zeplyn's",
                    ],
                    "log_data": False,
                    "mip_opt_out": True,
                },
            },
            "recording_mode_options": {"start_recording_on": "call_join"},
            "recording_mode": "audio_only",
            "bot_name": ANY,
            "metadata": ANY,
            "variant": {"zoom": "native"},
            "meeting_url": "http://example.com",
        },
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
@patch("deepinsights.meetingsapp.models.meeting_bot.Flags")
def test_create_bot_user_biasing_words(mock_flags: MagicMock, mock_requests: MagicMock) -> None:
    user = User.objects.create()
    preferences = user.get_preferences()
    preferences.asr_language_code = "es"
    user.preferences = preferences.to_dict()
    user.biasing_words = ["test"]
    user.save()

    bot = MeetingBot.objects.create(meeting_link="http://example.com", bot_owner=user)
    mock_flags.EnableProcessRecallBotTranscriptsWithZeplynDeepgramIntegration.is_active_for_user.return_value = False
    mock_flags.EnableNativeZoomBots.is_active_for_user.return_value = True
    mock_requests.post.return_value = MagicMock(status_code=200, json=lambda: {"id": "bot_id"})

    bot.create_bot_and_start_recording()
    assert bot.recall_bot_id == "bot_id"
    mock_requests.post.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/",
        json={
            "transcription_options": {
                "provider": "deepgram",
                "deepgram": {
                    "language": "es",
                    "smart_format": True,
                    "model": "nova-2",
                    "redact": ["pci", "ssn"],
                    "numerals": True,
                    "utterances": True,
                    # This is correct as the code has been written: the user's biasing words are not
                    # currently passed to the bot.
                    "keywords": [],
                    "replace": [
                        "zeplin:Zeplyn",
                        "zeplins:Zeplyns",
                        "zeplin's:Zeplyn's",
                        "zeppelin:Zeplyn",
                        "zeppelins:Zeplyns",
                        "zeppelin's:Zeplyn's",
                        "zeplon:Zeplyn",
                        "zeplons:Zeplyns",
                        "zeplon's:Zeplyn's",
                    ],
                    "log_data": False,
                    "mip_opt_out": True,
                },
            },
            "recording_mode_options": {"start_recording_on": "call_join"},
            "recording_mode": "audio_only",
            "bot_name": ANY,
            "metadata": ANY,
            "variant": {"zoom": "native"},
            "meeting_url": "http://example.com",
        },
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_create_phone_call_bot_error(mock_client: MagicMock, settings: SettingsWrapper) -> None:
    settings.TWILIO_PHONE_NUMBER = "**********"
    settings.WEBHOOK_DOMAIN = "http://example.com"
    mock_create = mock_client.return_value.calls.create
    mock_create.side_effect = TwilioRestException(status=400, uri="uri")
    user = User.objects.create()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)

    bot.create_bot_and_start_recording()

    bot.refresh_from_db()
    assert not bot.recall_bot_id


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_create_phone_call_bot(mock_client: MagicMock, settings: SettingsWrapper) -> None:
    settings.TWILIO_PHONE_NUMBER = "**********"
    settings.WEBHOOK_DOMAIN = "http://example.com"
    mock_create = mock_client.return_value.calls.create
    mock_create.return_value = MagicMock(sid="call_id")
    user = User.objects.create()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)

    bot.create_bot_and_start_recording()

    bot.refresh_from_db()
    assert bot.recall_bot_id == "call_id"
    mock_create.assert_called_once_with(
        to="+1**********",
        from_="+1**********",
        twiml=ANY,
        status_callback="http://example.com/api/v2/bot/twilio/call",
        status_callback_event=["completed"],
    )


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_create_phone_conference_bot_error(mock_client: MagicMock, settings: SettingsWrapper) -> None:
    settings.TWILIO_PHONE_NUMBER = "**********"
    settings.WEBHOOK_DOMAIN = "http://example.com"
    mock_client.side_effect = TwilioRestException(status=400, uri="uri")
    user = User.objects.create()
    user.phone_numbers.add(PhoneNumber.objects.create(number="**********"))
    user.save()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)

    bot.create_bot_and_start_recording()

    bot.refresh_from_db()
    assert not bot.recall_bot_id


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_create_phone_conference_bot(mock_client: MagicMock, settings: SettingsWrapper) -> None:
    settings.TWILIO_PHONE_NUMBER = "2125551210"
    settings.WEBHOOK_DOMAIN = "http://example.com"
    mock_get_conference = mock_client.return_value.conferences.get
    mock_create = mock_get_conference.return_value.participants.create
    mock_create.return_value = MagicMock(conference_sid="conference_sid")
    user = User.objects.create()
    user.phone_numbers.add(PhoneNumber.objects.create(number="**********"))
    user.save()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)

    bot.create_bot_and_start_recording()

    bot.refresh_from_db()
    assert bot.recall_bot_id == "conference_sid"
    mock_create.assert_called_once_with(
        to="+1**********",
        from_="+12125551210",
        label="+1**********",
        end_conference_on_exit=True,
        conference_status_callback="http://example.com/api/v2/bot/twilio/conference",
        conference_status_callback_event=["start", "end", "join", "leave"],
        conference_record=True,
        conference_recording_status_callback="http://example.com/api/v2/bot/twilio/recording",
        conference_recording_status_callback_event=["in-progress", "completed", "failed", "absent"],
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
@patch("deepinsights.meetingsapp.models.meeting_bot.Flags")
def test_create_or_update_scheduled_bot(mock_flags: MagicMock, mock_requests: MagicMock) -> None:
    user = User.objects.create()
    user.preferences = get_default_preferences()
    bot = MeetingBot.objects.create(meeting_link="http://example.com", bot_owner=user)
    mock_flags.EnableProcessRecallBotTranscriptsWithZeplynDeepgramIntegration.is_active_for_user.return_value = False
    mock_flags.EnableNativeZoomBots.is_active_for_user.return_value = True
    mock_requests.post.return_value = MagicMock(status_code=200, json=lambda: {"id": "bot_id"})

    start_time = datetime.datetime(2023, 10, 1, 12, 0, 0)
    bot.create_or_update_scheduled_bot(start_time)
    assert bot.recall_bot_id == "bot_id"
    mock_requests.post.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/",
        json={
            "transcription_options": {
                "provider": "deepgram",
                "deepgram": {
                    "language": "en",
                    "smart_format": True,
                    "model": "nova-2",
                    "redact": ["pci", "ssn"],
                    "numerals": True,
                    "utterances": True,
                    "keywords": [],
                    "replace": [
                        "zeplin:Zeplyn",
                        "zeplins:Zeplyns",
                        "zeplin's:Zeplyn's",
                        "zeppelin:Zeplyn",
                        "zeppelins:Zeplyns",
                        "zeppelin's:Zeplyn's",
                        "zeplon:Zeplyn",
                        "zeplons:Zeplyns",
                        "zeplon's:Zeplyn's",
                    ],
                    "log_data": False,
                    "mip_opt_out": True,
                },
            },
            "recording_mode_options": {"start_recording_on": "call_join"},
            "recording_mode": "audio_only",
            "bot_name": ANY,
            "metadata": ANY,
            "variant": {"zoom": "native"},
            "automatic_video_output": ANY,
            "automatic_audio_output": ANY,
            "meeting_url": "http://example.com",
            "join_at": start_time.isoformat(),
        },
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
@patch("deepinsights.meetingsapp.models.meeting_bot.Flags")
def test_create_or_update_scheduled_bot_existing(mock_flags: MagicMock, mock_requests: MagicMock) -> None:
    user = User.objects.create()
    user.preferences = get_default_preferences()
    bot = MeetingBot.objects.create(meeting_link="http://example.com", bot_owner=user, recall_bot_id="bot_id")
    mock_flags.EnableProcessRecallBotTranscriptsWithZeplynDeepgramIntegration.is_active_for_user.return_value = False
    mock_flags.EnableNativeZoomBots.is_active_for_user.return_value = True
    mock_requests.patch.return_value = MagicMock(status_code=200, json=lambda: {"id": "bot_id"})

    start_time = datetime.datetime(2023, 10, 1, 12, 0, 0)
    bot.create_or_update_scheduled_bot(start_time)
    assert bot.recall_bot_id == "bot_id"
    mock_requests.patch.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/bot_id/",
        json={
            "transcription_options": {
                "provider": "deepgram",
                "deepgram": {
                    "language": "en",
                    "smart_format": True,
                    "model": "nova-2",
                    "redact": ["pci", "ssn"],
                    "numerals": True,
                    "utterances": True,
                    "keywords": [],
                    "replace": [
                        "zeplin:Zeplyn",
                        "zeplins:Zeplyns",
                        "zeplin's:Zeplyn's",
                        "zeppelin:Zeplyn",
                        "zeppelins:Zeplyns",
                        "zeppelin's:Zeplyn's",
                        "zeplon:Zeplyn",
                        "zeplons:Zeplyns",
                        "zeplon's:Zeplyn's",
                    ],
                    "log_data": False,
                    "mip_opt_out": True,
                },
            },
            "recording_mode_options": {"start_recording_on": "call_join"},
            "recording_mode": "audio_only",
            "bot_name": ANY,
            "metadata": ANY,
            "variant": {"zoom": "native"},
            "automatic_video_output": ANY,
            "automatic_audio_output": ANY,
            "meeting_url": "http://example.com",
            "join_at": start_time.isoformat(),
        },
        headers=ANY,
    )


@pytest.mark.parametrize(
    "bot_id, url, user_phone_number, supports_pause_resume",
    [
        # Empty
        (None, None, None, True),
        ("", "", "", True),
        ("", "**********", None, False),
        ("", "**********", "**********", False),
        # Recall
        ("bot_id", None, None, True),
        ("bot_id", "http://example.com", "", True),
        # Twilio call
        ("bot_id", "**********", None, False),
        # Twilio conference
        ("bot_id", "**********", "**********", False),
    ],
)
def test_supports_pause_resume(
    bot_id: str | None, url: str | None, user_phone_number: str | None, supports_pause_resume: bool
) -> None:
    user = User.objects.create()
    if user_phone_number:
        user.phone_numbers.add(PhoneNumber.objects.create(number=user_phone_number))
        user.save()
    bot = MeetingBot.objects.create(meeting_link=url, bot_owner=user, recall_bot_id=bot_id)
    assert bot.supports_pause_resume == supports_pause_resume


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_pause_recording_success(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="bot_id")
    mock_requests.post.return_value = MagicMock(status_code=200, json=lambda: {"status": "paused"})

    response = bot.pause_recording()
    assert response.status == 200
    assert response.details["status"] == "paused"
    mock_requests.post.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/bot_id/pause_recording/",
        json={},
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_pause_recording_failure(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="bot_id")
    mock_requests.post.return_value = MagicMock(status_code=400, json=lambda: {"error": "Failed to pause recording"})

    response = bot.pause_recording()
    assert response.status == 400
    assert response.details["error"] == "Failed to pause recording"
    mock_requests.post.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/bot_id/pause_recording/",
        json={},
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_resume_recording_success(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="bot_id")
    mock_requests.post.return_value = MagicMock(status_code=200, json=lambda: {"status": "resumed"})

    response = bot.resume_recording()
    assert response.status == 200
    assert response.details["status"] == "resumed"
    mock_requests.post.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/bot_id/resume_recording/",
        json={"transcription_options": {"provider": "deepgram"}},
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_resume_recording_failure(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="bot_id")
    mock_requests.post.return_value = MagicMock(status_code=400, json=lambda: {"error": "Failed to resume recording"})

    response = bot.resume_recording()
    assert response.status == 400
    assert response.details["error"] == "Failed to resume recording"
    mock_requests.post.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/bot_id/resume_recording/",
        json={"transcription_options": {"provider": "deepgram"}},
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_leave_meeting_success(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="bot_id")
    mock_requests.post.return_value = MagicMock(status_code=200, json=lambda: {"status": "left"})

    response = bot.leave_meeting()
    assert response.status == 200
    assert response.details["status"] == "left"
    mock_requests.post.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/bot_id/leave_call/",
        json={},
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_leave_meeting_failure(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="bot_id")
    mock_requests.post.return_value = MagicMock(status_code=400, json=lambda: {"error": "Failed to leave meeting"})

    response = bot.leave_meeting()
    assert response.status == 400
    assert response.details["error"] == "Failed to leave meeting"
    mock_requests.post.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/bot_id/leave_call/",
        json={},
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_leave_meeting_success_phone_call(mock_client: MagicMock) -> None:
    user = User.objects.create()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)

    response = bot.leave_meeting()
    assert response.status == 200
    mock_client.return_value.calls.get.return_value.update.assert_called_once_with(status="completed")


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_leave_meeting_failure_phone_call(mock_client: MagicMock) -> None:
    user = User.objects.create()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)
    mock_client.side_effect = TwilioRestException(status=400, uri="uri")

    response = bot.leave_meeting()
    assert response.status == 400


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_leave_meeting_success_phone_conference(mock_client: MagicMock) -> None:
    user = User.objects.create()
    user.phone_numbers.add(PhoneNumber.objects.create(number="**********"))
    user.save()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)
    mock_client.return_value.conferences.get.return_value.update.return_value = MagicMock(status="completed")

    response = bot.leave_meeting()
    assert response.status == 200
    mock_client.return_value.conferences.get.return_value.update.assert_called_once_with(status="completed")


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_leave_meeting_failure_phone_conference(mock_client: MagicMock) -> None:
    user = User.objects.create()
    user.phone_numbers.add(PhoneNumber.objects.create(number="**********"))
    user.save()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)
    mock_client.side_effect = TwilioRestException(status=400, uri="uri")

    response = bot.leave_meeting()
    assert response.status == 400


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_get_transcript_success(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="bot_id")
    response = [
        {
            "speaker": "John Doe",
            "words": [
                {
                    "text": "Hello",
                    "start_timestamp": 0.0,
                    "end_timestamp": 1.0,
                },
                {
                    "text": "world",
                    "start_timestamp": 1.0,
                    "end_timestamp": 2.0,
                },
            ],
        },
        {
            "speaker": "Jane Doe",
            "words": [
                {
                    "text": "Hi",
                    "start_timestamp": 2.0,
                    "end_timestamp": 3.0,
                },
                {
                    "text": "there",
                    "start_timestamp": 3.0,
                    "end_timestamp": 4.0,
                },
            ],
        },
    ]
    mock_requests.get.return_value = MagicMock(status_code=200, json=lambda: response)

    transcript = bot.get_transcript()
    assert transcript == [
        {
            "speaker": "John Doe",
            "transcript": "Hello",
            "start": 0.0,
            "end": 1.0,
        },
        {
            "speaker": "John Doe",
            "transcript": "world",
            "start": 1.0,
            "end": 2.0,
        },
        {
            "speaker": "Jane Doe",
            "transcript": "Hi",
            "start": 2.0,
            "end": 3.0,
        },
        {
            "speaker": "Jane Doe",
            "transcript": "there",
            "start": 3.0,
            "end": 4.0,
        },
    ]
    mock_requests.get.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/bot_id/transcript/?enhanced_diarization=true",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_get_transcript_failure(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="bot_id")
    mock_requests.get.return_value = MagicMock(status_code=400, json=lambda: {"error": "Failed to get transcript"})

    with pytest.raises(Exception):
        assert not bot.get_transcript()
    mock_requests.get.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/bot_id/transcript/?enhanced_diarization=true",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_has_speaker_timeline_true(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="bot_id")
    mock_requests.get.return_value = MagicMock(status_code=200, json=lambda: ["non_empty"])

    assert bot.has_speaker_timeline()
    mock_requests.get.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/bot_id/speaker_timeline/",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_has_speaker_timeline_false(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="bot_id")
    mock_requests.get.return_value = MagicMock(status_code=200, json=lambda: [])

    assert not bot.has_speaker_timeline()
    mock_requests.get.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/bot_id/speaker_timeline/",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_has_speaker_timeline_error(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="bot_id")
    mock_requests.get.return_value = MagicMock(
        status_code=400, json=lambda: {"error": "Failed to get speaker timeline"}
    )

    assert not bot.has_speaker_timeline()
    mock_requests.get.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/bot_id/speaker_timeline/",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_delete_media_success(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="bot_id")
    mock_requests.post.return_value = MagicMock(status_code=200)

    bot.delete_media()
    mock_requests.post.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/bot_id/delete_media/",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_delete_media_failure(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="bot_id")
    mock_requests.post.return_value = MagicMock(status_code=400)

    bot.delete_media()
    mock_requests.post.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/bot_id/delete_media/",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_delete_media_success_phone_call(mock_client: MagicMock, caplog: pytest.LogCaptureFixture) -> None:
    user = User.objects.create()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)
    mock_recordings = [
        MagicMock(sid="recording_id1"),
        MagicMock(sid="recording_id2"),
    ]
    mock_client.return_value.calls.get.return_value.recordings.list.return_value = mock_recordings

    bot.delete_media()
    mock_client.return_value.calls.get.return_value.recordings.list.assert_called_once()
    [m.delete.assert_called_once() for m in mock_recordings]
    assert not caplog.messages


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_delete_media_failure_phone_call(mock_client: MagicMock, caplog: pytest.LogCaptureFixture) -> None:
    user = User.objects.create()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)
    mock_client.side_effect = TwilioRestException(status=400, uri="uri")

    bot.delete_media()
    assert caplog.messages == [f"Error deleting recording(s) for {bot.bot_id_for_logging}"]


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_delete_media_success_phone_conference(mock_client: MagicMock, caplog: pytest.LogCaptureFixture) -> None:
    user = User.objects.create()
    user.phone_numbers.add(PhoneNumber.objects.create(number="**********"))
    user.save()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)
    mock_recordings = [
        MagicMock(sid="recording_id1"),
        MagicMock(sid="recording_id2"),
    ]
    mock_client.return_value.conferences.get.return_value.recordings.list.return_value = mock_recordings

    bot.delete_media()
    mock_client.return_value.conferences.get.return_value.recordings.list.assert_called_once()
    mock_client.return_value.recordings.get.assert_has_calls(
        [call("recording_id1"), call().delete(), call("recording_id2")], call().delete()
    )
    assert not caplog.messages


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_delete_media_failure_phone_conference(mock_client: MagicMock, caplog: pytest.LogCaptureFixture) -> None:
    user = User.objects.create()
    user.phone_numbers.add(PhoneNumber.objects.create(number="**********"))
    user.save()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)
    mock_client.side_effect = TwilioRestException(status=400, uri="uri")

    bot.delete_media()
    assert caplog.messages == [f"Error deleting recording(s) for {bot.bot_id_for_logging}"]


@pytest.mark.parametrize(
    "bot_id, url, user_phone_number, supports_send_chat_message",
    [
        ("bot-id", None, None, True),
        ("bot-id", "**********", None, False),
        ("bot-id", "**********", "**********", False),
    ],
)
def test_supports_send_chat_message(
    bot_id: str | None, url: str | None, user_phone_number: str | None, supports_send_chat_message: bool
) -> None:
    user = User.objects.create()
    if user_phone_number:
        user.phone_numbers.add(PhoneNumber.objects.create(number=user_phone_number))
        user.save()
    bot = MeetingBot.objects.create(meeting_link=url, bot_owner=user, recall_bot_id=bot_id)
    assert bot.supports_send_chat_message == supports_send_chat_message


@patch("deepinsights.core.integrations.meetingbot.recall_ai.RecallBotController.send_chat_message")
def test_send_chat_message(mock_chat_message: MagicMock) -> None:
    mock_chat_message.return_value = True
    bot = MeetingBot.objects.create(recall_bot_id="test-bot-id")
    result = bot.send_chat_message(None, "everyone message")
    mock_chat_message.assert_called_once_with(None, "everyone message")


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_get_media_download_info_no_bot(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create()
    assert not bot.get_media_download_info()
    mock_requests.get.assert_not_called()


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_get_media_download_info_success(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="bot_id")
    mock_requests.get.return_value = MagicMock(status_code=200, json=lambda: {"video_url": "http://example.com/media"})

    media_download_info = bot.get_media_download_info()
    assert media_download_info == ("http://example.com/media", None, None)
    mock_requests.get.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/bot_id/",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_get_media_download_info_failure(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="bot_id")
    mock_requests.get.return_value = MagicMock(status_code=400, json=lambda: {"error": "Failed to get media URL"})

    media_download_info = bot.get_media_download_info()
    assert media_download_info is None
    mock_requests.get.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/bot_id/",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_get_media_download_info_success_phone_call_no_recordings(
    mock_client: MagicMock, caplog: pytest.LogCaptureFixture
) -> None:
    user = User.objects.create()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)
    mock_client.return_value.calls.get.return_value.recordings.list.return_value = []

    assert not bot.get_media_download_info()
    mock_client.return_value.calls.get.return_value.recordings.list.assert_called_once()
    assert not caplog.messages


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_get_media_download_info_success_phone_call_one_recording(
    mock_client: MagicMock, caplog: pytest.LogCaptureFixture, settings: SettingsWrapper
) -> None:
    settings.TWILIO_AUTH_TOKEN_SID = "auth_token_sid"
    settings.TWILIO_AUTH_TOKEN_SECRET = "auth_token_secret"
    user = User.objects.create()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)
    mock_client.return_value.calls.get.return_value.recordings.list.return_value = [MagicMock(sid="recording_id")]
    mock_client.return_value.recordings.get.return_value.fetch.return_value = MagicMock(
        media_url="http://example.com/media"
    )

    assert bot.get_media_download_info() == ("http://example.com/media.mp3", "auth_token_sid", "auth_token_secret")
    mock_client.return_value.recordings.get.assert_called_once_with("recording_id")
    assert not caplog.messages


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_get_media_download_info_success_phone_call_multiple_recordings(
    mock_client: MagicMock, caplog: pytest.LogCaptureFixture, settings: SettingsWrapper
) -> None:
    caplog.set_level(logging.WARNING)
    settings.TWILIO_AUTH_TOKEN_SID = "auth_token_sid"
    settings.TWILIO_AUTH_TOKEN_SECRET = "auth_token_secret"
    user = User.objects.create()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)
    mock_client.return_value.calls.get.return_value.recordings.list.return_value = [
        MagicMock(sid="recording_id"),
        MagicMock(sid="recording_id2"),
    ]
    mock_client.return_value.recordings.get.return_value.fetch.return_value = MagicMock(
        media_url="http://example.com/media"
    )

    assert bot.get_media_download_info() == ("http://example.com/media.mp3", "auth_token_sid", "auth_token_secret")
    mock_client.return_value.recordings.get.assert_called_once_with("recording_id")
    assert caplog.messages == [f"Multiple recordings found for {bot.bot_id_for_logging}. Using the first one."]


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_get_media_download_info_failure_phone_call(mock_client: MagicMock, caplog: pytest.LogCaptureFixture) -> None:
    user = User.objects.create()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)
    mock_client.side_effect = TwilioRestException(status=400, uri="uri")

    assert not bot.get_media_download_info()
    assert caplog.messages == [f"Error fetching media download info for {bot.bot_id_for_logging}"]


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_get_media_download_info_success_phone_conference_no_recordings(
    mock_client: MagicMock, caplog: pytest.LogCaptureFixture
) -> None:
    user = User.objects.create()
    user.phone_numbers.add(PhoneNumber.objects.create(number="**********"))
    user.save()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)
    mock_client.return_value.conferences.get.return_value.recordings.list.return_value = []

    assert not bot.get_media_download_info()
    mock_client.return_value.conferences.get.return_value.recordings.list.assert_called_once()
    assert not caplog.messages


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_get_media_download_info_success_phone_conference_one_recording(
    mock_client: MagicMock, caplog: pytest.LogCaptureFixture, settings: SettingsWrapper
) -> None:
    settings.TWILIO_AUTH_TOKEN_SID = "auth_token_sid"
    settings.TWILIO_AUTH_TOKEN_SECRET = "auth_token_secret"
    user = User.objects.create()
    user.phone_numbers.add(PhoneNumber.objects.create(number="**********"))
    user.save()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)
    mock_client.return_value.conferences.get.return_value.recordings.list.return_value = [MagicMock(sid="recording_id")]
    mock_client.return_value.recordings.get.return_value.fetch.return_value = MagicMock(
        media_url="http://example.com/media"
    )

    assert bot.get_media_download_info() == ("http://example.com/media.mp3", "auth_token_sid", "auth_token_secret")
    mock_client.return_value.recordings.get.assert_called_once_with("recording_id")
    assert not caplog.messages


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_get_media_download_info_success_phone_conference_multiple_recordings(
    mock_client: MagicMock, caplog: pytest.LogCaptureFixture, settings: SettingsWrapper
) -> None:
    caplog.set_level(logging.WARNING)
    settings.TWILIO_AUTH_TOKEN_SID = "auth_token_sid"
    settings.TWILIO_AUTH_TOKEN_SECRET = "auth_token_secret"
    user = User.objects.create()
    user.phone_numbers.add(PhoneNumber.objects.create(number="**********"))
    user.save()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)
    mock_client.return_value.conferences.get.return_value.recordings.list.return_value = [
        MagicMock(sid="recording_id"),
        MagicMock(sid="recording_id2"),
    ]
    mock_client.return_value.recordings.get.return_value.fetch.return_value = MagicMock(
        media_url="http://example.com/media"
    )

    assert bot.get_media_download_info() == ("http://example.com/media.mp3", "auth_token_sid", "auth_token_secret")
    mock_client.return_value.recordings.get.assert_called_once_with("recording_id")
    assert caplog.messages == [f"Multiple recordings found for {bot.bot_id_for_logging}. Using the first one."]


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_get_media_download_info_failure_phone_conference(
    mock_client: MagicMock, caplog: pytest.LogCaptureFixture
) -> None:
    user = User.objects.create()
    user.phone_numbers.add(PhoneNumber.objects.create(number="**********"))
    user.save()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)
    mock_client.side_effect = TwilioRestException(status=400, uri="uri")

    assert not bot.get_media_download_info()
    assert caplog.messages == [f"Error fetching media download info for {bot.bot_id_for_logging}"]


@pytest.mark.parametrize(
    "bot_id, url, user_phone_number, expected_bot_id_for_logging",
    [
        # Empty
        (None, None, None, "<Recall: no bot ID>"),
        ("", "", "", "<Recall: no bot ID>"),
        ("", "**********", None, "<Twilio: no call ID>"),
        ("", "**********", "**********", "<Twilio: no conference ID>"),
        # Recall
        ("bot_id", None, None, "Recall bot bot_id (https://go/recallbot/bot_id)"),
        ("bot_id", "http://example.com", "", "Recall bot bot_id (https://go/recallbot/bot_id)"),
        # Twilio call
        ("bot_id", "**********", None, "Twilio Call bot_id (https://go/twiliocall/bot_id)"),
        # Twilio conference
        ("bot_id", "**********", "**********", "Twilio Conference bot_id (https://go/twilioconference/bot_id)"),
    ],
)
def test_bot_id_for_logging_with_bot_id(
    bot_id: str | None, url: str | None, user_phone_number: str | None, expected_bot_id_for_logging: str
) -> None:
    user = User.objects.create()
    if user_phone_number:
        user.phone_numbers.add(PhoneNumber.objects.create(number=user_phone_number))
        user.save()
    bot = MeetingBot.objects.create(meeting_link=url, bot_owner=user, recall_bot_id=bot_id)
    assert bot.bot_id_for_logging == expected_bot_id_for_logging


@pytest.mark.parametrize(
    "bot_id, url, user_phone_number, expected_bot_provider_url",
    [
        # Empty
        (None, None, None, "<Recall: no bot ID>"),
        ("", "", "", "<Recall: no bot ID>"),
        ("", "**********", None, "<Twilio: no call ID>"),
        ("", "**********", "**********", "<Twilio: no conference ID>"),
        # Recall
        ("bot_id", None, None, "https://go/recallbot/bot_id"),
        ("bot_id", "http://example.com", "", "https://go/recallbot/bot_id"),
        # Twilio call
        ("bot_id", "**********", None, "https://go/twiliocall/bot_id"),
        # Twilio conference
        ("bot_id", "**********", "**********", "https://go/twilioconference/bot_id"),
    ],
)
def test_provider_bot_url_with_bot_id(
    bot_id: str | None, url: str | None, user_phone_number: str | None, expected_bot_provider_url: str
) -> None:
    user = User.objects.create()
    if user_phone_number:
        user.phone_numbers.add(PhoneNumber.objects.create(number=user_phone_number)) if user_phone_number else None
        user.save()
    bot = MeetingBot.objects.create(meeting_link=url, bot_owner=user, recall_bot_id=bot_id)
    assert bot.provider_bot_url == expected_bot_provider_url


@pytest.mark.parametrize(
    "url, user_phone_number, expected_processing_task",
    [
        (None, None, process_bot_recording),
        ("", None, process_bot_recording),
        ("http://example.com", None, process_bot_recording),
        ("**********", None, process_phone_call_recording),
        ("**********", None, process_phone_call_recording),
        ("**********", "**********", process_phone_call_recording),
        ("**********", "**********", process_phone_call_recording),
    ],
)
def test_processing_task(
    url: str | None, user_phone_number: str | None, expected_processing_task: BotProcessingTask | None
) -> None:
    user = User.objects.create()
    if user_phone_number:
        user.phone_numbers.add(PhoneNumber.objects.create(number=user_phone_number))
        user.save()
    bot = MeetingBot.objects.create(meeting_link=url, bot_owner=user)
    assert bot.processing_task == expected_processing_task


@pytest.mark.parametrize(
    "meeting_link, expected_link_type, expected_phone_number",
    [
        (None, MeetingBot.LinkType.NONE, None),
        ("", MeetingBot.LinkType.NONE, None),
        ("http://example.com", MeetingBot.LinkType.VIDEO_CALL, None),
        ("**********", MeetingBot.LinkType.PHONE_NUMBER, phonenumbers.parse("**********", "US")),
        ("+1**********", MeetingBot.LinkType.PHONE_NUMBER, phonenumbers.parse("+1**********", "US")),
        ("(*************", MeetingBot.LinkType.PHONE_NUMBER, phonenumbers.parse("(*************", "US")),
        ("https://zoom.us/j/7156907842", MeetingBot.LinkType.VIDEO_CALL, None),
        ("**********12345", MeetingBot.LinkType.VIDEO_CALL, None),  # Exceeds max length for phone number
        ("**********/extra", MeetingBot.LinkType.VIDEO_CALL, None),  # Contains a forward slash
        (_example_number("GB"), MeetingBot.LinkType.PHONE_NUMBER, phonenumbers.parse(_example_number("GB"), "GB")),
        (
            _example_number("AU", phonenumbers.PhoneNumberFormat.INTERNATIONAL),
            MeetingBot.LinkType.PHONE_NUMBER,
            phonenumbers.parse(_example_number("AU"), "AU"),
        ),
        (
            _example_number("US", phonenumbers.PhoneNumberFormat.NATIONAL),
            MeetingBot.LinkType.PHONE_NUMBER,
            phonenumbers.parse(_example_number("US"), "US"),
        ),
        # These are valid phone numbers, but our logic of checking length prevents it from working.
        # This test is here to document current behavior, not to indicate that it's the most correct
        # approach.
        (_example_number("US", phonenumbers.PhoneNumberFormat.RFC3966), MeetingBot.LinkType.VIDEO_CALL, None),
        (_example_number("CA", phonenumbers.PhoneNumberFormat.RFC3966), MeetingBot.LinkType.VIDEO_CALL, None),
        (_example_number("GB", phonenumbers.PhoneNumberFormat.INTERNATIONAL), MeetingBot.LinkType.VIDEO_CALL, None),
        (_example_number("GB", phonenumbers.PhoneNumberFormat.RFC3966), MeetingBot.LinkType.VIDEO_CALL, None),
        # These are valid phone numbers, but because we assume a US country code, they are not handled.
        (_example_number("GB", phonenumbers.PhoneNumberFormat.NATIONAL), MeetingBot.LinkType.VIDEO_CALL, None),
        (_example_number("GB", phonenumbers.PhoneNumberFormat.NATIONAL), MeetingBot.LinkType.VIDEO_CALL, None),
        # End tests of valid but unhandled phone numbers.
        (_invalid_number("US"), MeetingBot.LinkType.VIDEO_CALL, None),
        ("/1**********", MeetingBot.LinkType.VIDEO_CALL, None),
        ("1**********/", MeetingBot.LinkType.VIDEO_CALL, None),
        ("https://zoom.us/j/7156907842", MeetingBot.LinkType.VIDEO_CALL, None),
        ("https://zoom.us/j/7156907842/", MeetingBot.LinkType.VIDEO_CALL, None),
        ("https://zoom.us/j/7156907842?pwd=abc", MeetingBot.LinkType.VIDEO_CALL, None),
        ("https://zoom.us/j/7156907842?pwd=abc", MeetingBot.LinkType.VIDEO_CALL, None),
    ],
)
def test_link_type(
    meeting_link: str | None,
    expected_link_type: MeetingBot.LinkType,
    expected_phone_number: phonenumbers.PhoneNumber | None,
) -> None:
    bot = MeetingBot(meeting_link=meeting_link)
    link_type, phone_number = bot.link_type()
    assert link_type == expected_link_type
    assert phone_number == expected_phone_number


def test_link_type_invalid_number() -> None:
    bot = MeetingBot(meeting_link="invalid_number")
    link_type, phone_number = bot.link_type()
    assert link_type == MeetingBot.LinkType.VIDEO_CALL
    assert phone_number is None


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_delete_bot_no_bot_id(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create()
    bot.delete_bot()
    mock_requests.delete.assert_not_called()


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_delete_bot_success(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="bot_id")
    mock_requests.delete.return_value = MagicMock(status_code=204)

    bot.delete_bot()
    mock_requests.delete.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/bot_id/",
        headers=ANY,
    )

    bot.refresh_from_db()
    assert not bot.recall_bot_id


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_delete_bot_failure(mock_requests: MagicMock) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="bot_id")
    mock_requests.delete.return_value = MagicMock(status_code=400)

    bot.delete_bot()
    mock_requests.delete.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/bot_id/",
        headers=ANY,
    )

    bot.refresh_from_db()
    assert bot.recall_bot_id == "bot_id"


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_delete_bot_no_bot_id_phone_call(mock_client: MagicMock, caplog: pytest.LogCaptureFixture) -> None:
    user = User.objects.create()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)

    bot.delete_bot()
    mock_client.return_value.calls.get.return_value.delete.assert_not_called()
    assert not caplog.messages


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_delete_bot_success_phone_call(mock_client: MagicMock, caplog: pytest.LogCaptureFixture) -> None:
    user = User.objects.create()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user, recall_bot_id="bot_id")

    bot.delete_bot()
    mock_client.return_value.calls.get.return_value.delete.assert_called_once()
    assert not caplog.messages


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_delete_bot_failure_phone_call(mock_client: MagicMock) -> None:
    user = User.objects.create()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user, recall_bot_id="bot_id")
    mock_client.side_effect = TwilioRestException(status=400, uri="uri")

    bot.delete_bot()
    assert bot.recall_bot_id == "bot_id"


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_delete_bot_no_bot_id_phone_conference(mock_client: MagicMock, caplog: pytest.LogCaptureFixture) -> None:
    user = User.objects.create()
    user.phone_numbers.add(PhoneNumber.objects.create(number="**********"))
    user.save()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user)

    bot.delete_bot()
    mock_client.return_value.conferences.get.return_value.delete.assert_not_called()
    assert not caplog.messages


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_delete_bot_success_phone_conference(mock_client: MagicMock, caplog: pytest.LogCaptureFixture) -> None:
    user = User.objects.create()
    user.phone_numbers.add(PhoneNumber.objects.create(number="**********"))
    user.save()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user, recall_bot_id="bot_id")

    bot.delete_bot()
    mock_client.return_value.conferences.get.return_value.delete.assert_called_once()
    assert not caplog.messages


@patch("deepinsights.core.integrations.meetingbot.twilio.Client")
def test_delete_bot_failure_phone_conference(mock_client: MagicMock) -> None:
    user = User.objects.create()
    user.phone_numbers.add(PhoneNumber.objects.create(number="**********"))
    user.save()
    bot = MeetingBot.objects.create(meeting_link="**********", bot_owner=user, recall_bot_id="bot_id")
    mock_client.side_effect = TwilioRestException(status=400, uri="uri")

    bot.delete_bot()
    assert bot.recall_bot_id == "bot_id"


@pytest.mark.parametrize(
    "meeting_link, expected",
    [
        (None, False),
        ("", False),
        ("http://example.com", True),
        ("+1**********", False),
        ("https://zoom.us/j/7156907842", True),
    ],
)
def test_supports_scheduled_bots(meeting_link: str | None, expected: bool) -> None:
    bot = MeetingBot.objects.create(meeting_link=meeting_link)
    assert bot.supports_scheduled_bots == expected
