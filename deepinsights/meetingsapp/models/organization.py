from django.core.validators import MinV<PERSON>ueValidator
from django.db import models
from phonenumber_field.modelfields import PhoneNumber<PERSON>ield
from phonenumber_field.phonenumber import PhoneNumber
from simple_history.models import HistoricalRecords

from deepinsights.core.behaviours import UUIDMixin
from deepinsights.core.preferences.preferences import (
    CrmConfiguration,
    Preferences,
    get_default_crm_configuration,
    get_default_preferences,
)


class Organization(UUIDMixin, models.Model):
    name = models.CharField(max_length=255, verbose_name="Organization Name")
    phone_number: models.Field[PhoneNumber | str | None, PhoneNumber | None] = PhoneNumberField(
        blank=True,
        null=True,
        help_text=(
            "A phone number for the organization."
            "\n\n"
            "If validated in Twilio, this is used as an outgoing caller ID number."
            "\n\n"
            "This field is somewhat intelligent: if you pass in an invalid phone number it will return an error. "
            "If you provide a valid US phone number (without country code), it will automatically add the country "
            "code. If you provide in a valid phone number with a country code, it will use that country code."
            "\n\n"
            "Note that the number is stored and rendered in E.164 format (e.g., +12125551212), regardless of how "
            "you enter it."
        ),
    )
    description = models.TextField(verbose_name="Description", default="A new Zeplyn client.")
    crm_configuration = models.JSONField(default=get_default_crm_configuration, blank=True)
    # The exclusion done below is for historical reasons. Organization did not always have UUID,
    # created, or modified fields, but we don't want them to be nullable, so the historical
    # records should not include them (because they would have to be nullable for the historical
    # records).
    history = HistoricalRecords(inherit=True, excluded_fields=["uuid", "created", "modified"])
    preferences = models.JSONField(
        null=True,
        blank=True,
        default=get_default_preferences,
    )
    transcript_ttl = models.IntegerField(
        verbose_name="Transcript Time-to-Live (days)",
        help_text="Number of days to keep transcripts. Leave empty for indefinite storage.",
        null=True,
        blank=True,
        default=None,
        validators=[MinValueValidator(1)],
    )

    def __str__(self) -> str:
        return self.name

    class Meta:
        verbose_name = "Organization"
        verbose_name_plural = "Organizations"

    def get_crm_configuration(self) -> CrmConfiguration:
        return CrmConfiguration.from_dict(self.crm_configuration or {})

    def get_preferences(self) -> Preferences:
        return Preferences.from_dict(self.preferences) or Preferences()
