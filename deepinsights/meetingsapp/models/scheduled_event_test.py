from datetime import datetime
from typing import Any

import pytest
from django.db import IntegrityError

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent
from deepinsights.users.models.user import User


@pytest.fixture
def test_user(django_user_model: User) -> User:
    return django_user_model.objects.create_user(username="testuser")


def test_create_or_update_with_calendar_event_creation(test_user: User) -> None:
    calendar_event = CalendarEvent(
        id="event_id",
        provider="google",
        title="New Event Title",
        body="Event body",
        all_day=False,
        start_time=datetime(2022, 1, 2, 10, 0),
        end_time=datetime(2022, 1, 2, 11, 0),
        user_specific_id="event_user_id",
        participants=[],
        meeting_urls=[],
    )

    updated_event, created = ScheduledEvent.create_or_update_with_calendar_event(test_user, calendar_event)

    assert created
    assert updated_event
    assert updated_event.user == test_user
    assert updated_event.start_time == calendar_event.start_time
    assert updated_event.end_time == calendar_event.end_time
    assert updated_event.user_specific_source_id == calendar_event.user_specific_id
    assert updated_event.shared_source_id == calendar_event.id
    assert updated_event.source_data == calendar_event.model_dump(mode="json")
    assert updated_event.autojoin_behavior == ScheduledEvent.AutoJoinOverride.DEFAULT


def test_create_or_update_with_calendar_event_update(test_user: User) -> None:
    scheduled_event = ScheduledEvent.objects.create(
        user=test_user,
        start_time=datetime(2022, 1, 1, 10, 0),
        end_time=datetime(2022, 1, 1, 11, 0),
        user_specific_source_id="event_user_id",
        shared_source_id="event_id",
        autojoin_behavior=ScheduledEvent.AutoJoinOverride.ENABLED,
    )

    calendar_event = CalendarEvent(
        id="event_id",
        provider="google",
        title="New Event Title",
        body="Event body",
        all_day=False,
        start_time=datetime(2022, 1, 2, 10, 0),
        end_time=datetime(2022, 1, 2, 11, 0),
        user_specific_id="event_user_id",
        participants=[],
        meeting_urls=[],
    )

    updated_event, created = ScheduledEvent.create_or_update_with_calendar_event(test_user, calendar_event)

    assert not created
    assert updated_event == scheduled_event
    assert updated_event.source_data == calendar_event.model_dump(mode="json")
    assert updated_event.start_time == calendar_event.start_time
    assert updated_event.end_time == calendar_event.end_time
    assert updated_event.user_specific_source_id == calendar_event.user_specific_id
    assert updated_event.shared_source_id == calendar_event.id
    assert updated_event.autojoin_behavior == ScheduledEvent.AutoJoinOverride.ENABLED


def test_create_or_update_with_calendar_event_update_non_matching_user_event_id(test_user: User) -> None:
    ScheduledEvent.objects.create(
        user=test_user,
        start_time=datetime(2022, 1, 1, 10, 0),
        end_time=datetime(2022, 1, 1, 11, 0),
        user_specific_source_id="non_matching_user_event_id",
        shared_source_id="event_id",
    )

    calendar_event = CalendarEvent(
        id="event_id",
        provider="google",
        title="New Event Title",
        body="Event body",
        all_day=False,
        start_time=datetime(2022, 1, 2, 10, 0),
        end_time=datetime(2022, 1, 2, 11, 0),
        user_specific_id="user_event_id",
        participants=[],
        meeting_urls=[],
    )

    ScheduledEvent.create_or_update_with_calendar_event(test_user, calendar_event)

    assert ScheduledEvent.objects.count() == 2


def test_create_or_update_with_calendar_event_update_non_matching_shared_id(test_user: User) -> None:
    ScheduledEvent.objects.create(
        user=test_user,
        start_time=datetime(2022, 1, 1, 10, 0),
        end_time=datetime(2022, 1, 1, 11, 0),
        user_specific_source_id="user_event_id",
        shared_source_id="non_matching_id",
    )

    calendar_event = CalendarEvent(
        id="event_id",
        provider="google",
        title="New Event Title",
        body="Event body",
        all_day=False,
        start_time=datetime(2022, 1, 2, 10, 0),
        end_time=datetime(2022, 1, 2, 11, 0),
        user_specific_id="user_event_id",
        participants=[],
        meeting_urls=[],
    )

    with pytest.raises(IntegrityError):
        ScheduledEvent.create_or_update_with_calendar_event(test_user, calendar_event)

    assert ScheduledEvent.objects.count() == 1


def test_create_or_update_with_calendar_event_update_non_matching_user(
    test_user: User, django_user_model: User
) -> None:
    other_user = django_user_model.objects.create_user(username="otheruser")
    ScheduledEvent.objects.create(
        user=test_user,
        start_time=datetime(2022, 1, 1, 10, 0),
        end_time=datetime(2022, 1, 1, 11, 0),
        user_specific_source_id="user_event_id",
        shared_source_id="event_id",
    )

    calendar_event = CalendarEvent(
        id="event_id",
        provider="google",
        title="New Event Title",
        body="Event body",
        all_day=False,
        start_time=datetime(2022, 1, 1, 10, 0),
        end_time=datetime(2022, 1, 1, 11, 0),
        user_specific_id="user_event_id",
        participants=[],
        meeting_urls=[],
    )

    ScheduledEvent.create_or_update_with_calendar_event(other_user, calendar_event)

    assert ScheduledEvent.objects.count() == 2


@pytest.mark.django_db
@pytest.mark.parametrize(
    "recall_calendar_id,recall_calendar_platform,source_data,expected",
    [
        # User missing recall_calendar_id
        (None, "google", {"meeting_urls": ["url"], "provider": "google"}, False),
        # User missing recall_calendar_platform
        ("calid", None, {"meeting_urls": ["url"], "provider": "google"}, False),
        # source_data missing meeting_urls
        ("calid", "google", {"provider": "google"}, False),
        # source_data meeting_urls empty
        ("calid", "google", {"meeting_urls": [], "provider": "google"}, False),
        # source_data provider does not match user platform
        ("calid", "google", {"meeting_urls": ["url"], "provider": "outlook"}, False),
        # All conditions met
        ("calid", "google", {"meeting_urls": ["url"], "provider": "google"}, True),
        # source_data is None
        ("calid", "google", None, False),
        # source_data missing provider
        ("calid", "google", {"meeting_urls": ["url"]}, False),
    ],
)
def test_is_recall_autojoin_available(
    recall_calendar_id: str | None,
    recall_calendar_platform: str | None,
    source_data: dict[str, Any],
    expected: bool,
    test_user: User,
    django_user_model: User,
) -> None:
    test_user.recall_calendar_id = recall_calendar_id
    test_user.recall_calendar_platform = recall_calendar_platform
    test_user.save()

    event = ScheduledEvent(
        # Create a different user for the event to ensure that the passed-in user is being used.
        user=django_user_model.objects.create_user(username="otheruser"),
        start_time="2022-01-01T10:00:00Z",
        end_time="2022-01-01T11:00:00Z",
        user_specific_source_id="abc",
        shared_source_id="def",
        source_data=source_data,
    )
    assert event.is_recall_autojoin_available(test_user) is expected


@pytest.mark.django_db
@pytest.mark.parametrize(
    "recall_calendar_id,recall_calendar_platform,source_data,autojoin_behavior,expected",
    [
        # Recall autojoin not available (missing recall_calendar_id)
        (
            None,
            "google",
            {"meeting_urls": ["url"], "provider": "google"},
            ScheduledEvent.AutoJoinOverride.DEFAULT,
            False,
        ),
        # Recall autojoin not available (missing recall_calendar_platform)
        (
            "calid",
            None,
            {"meeting_urls": ["url"], "provider": "google"},
            ScheduledEvent.AutoJoinOverride.DEFAULT,
            False,
        ),
        # Recall autojoin not available (meeting_urls missing)
        ("calid", "google", {"provider": "google"}, ScheduledEvent.AutoJoinOverride.DEFAULT, False),
        # Recall autojoin not available (provider mismatch)
        (
            "calid",
            "google",
            {"meeting_urls": ["url"], "provider": "outlook"},
            ScheduledEvent.AutoJoinOverride.DEFAULT,
            False,
        ),
        # Recall autojoin available, but autojoin_behavior is DISABLED
        (
            "calid",
            "google",
            {"meeting_urls": ["url"], "provider": "google"},
            ScheduledEvent.AutoJoinOverride.DISABLED,
            False,
        ),
        # Recall autojoin available, autojoin_behavior is DEFAULT
        (
            "calid",
            "google",
            {"meeting_urls": ["url"], "provider": "google"},
            ScheduledEvent.AutoJoinOverride.DEFAULT,
            True,
        ),
        # Recall autojoin available, autojoin_behavior is ENABLED
        (
            "calid",
            "google",
            {"meeting_urls": ["url"], "provider": "google"},
            ScheduledEvent.AutoJoinOverride.ENABLED,
            True,
        ),
        # Recall autojoin not available (source_data is None)
        ("calid", "google", None, ScheduledEvent.AutoJoinOverride.DEFAULT, False),
    ],
)
def test_is_recall_autojoin_enabled(
    recall_calendar_id: str | None,
    recall_calendar_platform: str | None,
    source_data: dict[str, Any] | None,
    autojoin_behavior: str,
    expected: bool,
    test_user: User,
    django_user_model: User,
) -> None:
    test_user.recall_calendar_id = recall_calendar_id
    test_user.recall_calendar_platform = recall_calendar_platform
    test_user.save()

    event = ScheduledEvent(
        user=django_user_model.objects.create_user(username="otheruser"),
        start_time="2022-01-01T10:00:00Z",
        end_time="2022-01-01T11:00:00Z",
        user_specific_source_id="abc",
        shared_source_id="def",
        source_data=source_data,
        autojoin_behavior=autojoin_behavior,
    )
    assert event.is_recall_autojoin_enabled(test_user) is expected


def test_is_from_external_system_true(test_user: User) -> None:
    event = ScheduledEvent(
        user=test_user,
        start_time="2022-01-01T10:00:00Z",
        end_time="2022-01-01T11:00:00Z",
        user_specific_source_id="abc",
        shared_source_id="def",
    )
    assert event.is_from_external_system


def test_is_from_external_system_false(test_user: User) -> None:
    event = ScheduledEvent(
        user=test_user,
        start_time="2022-01-01T10:00:00Z",
        end_time="2022-01-01T11:00:00Z",
        user_specific_source_id=None,
        shared_source_id="def",
    )
    assert not event.is_from_external_system
