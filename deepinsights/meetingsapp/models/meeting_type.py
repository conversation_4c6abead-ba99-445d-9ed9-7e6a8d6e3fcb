from typing import Any

from django.db import models

from deepinsights.core.behaviours import StatusMixin, UUIDMixin
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.user import User


class MeetingType(StatusMixin, UUIDMixin):
    """A type of meeting (e.g., a client meeting, an annual review meeting)."""

    # A category for this meeting.
    #
    # This is meant to help us differentiate between meetings that are with clients
    # and meetings that are internal to the organization.
    #
    # This is unfortunately somewhat overlapped with `key`. It re-uses the same values
    # as the old hard-coded meeting types, to ease compatibility with existing flows
    # that rely on those specific constants. It also somewhat overlaps with the idea of
    # meeting "types", but is slightly different: a meeting "type" is more specific than
    # a "category", and two meetings with different "types" can have the same "category".
    # For example, the "client" meeting type and "prospect" meeting type can both be
    # in the "client" category, because they are both meeting with clients.
    class Category(models.TextChoices):
        CLIENT = "client"
        INTERNAL = "internal"
        DEBRIEF = "debrief"

    key = models.CharField(
        unique=True,
        blank=True,
        null=True,
        help_text=(
            "A key that maps this meeting type to the older hard-coded meeting types. "
            "This should usually be left blank. If there is no key, then this meeting type "
            "has no mapping to the older hard-coded meeting types. You should generally leave this "
            "blank."
        ),
    )

    name = models.CharField(
        max_length=200,
        help_text=(
            'The user-visible title/name of this type of meeting (e.g., "Client", "Internal"). '
            "This may correspond to the key above. The key, however, is unique, whereas multiple "
            "meeting types can have the same user-visible name."
        ),
    )

    internal_name = models.TextField(
        blank=True,
        null=True,
        max_length=200,
        help_text=(
            "An internally-visible name used in the admin concole to identify this type of meeting. "
            "This should never be shown to users; it is only for internal reference."
        ),
    )

    category = models.CharField(
        choices=Category.choices,
        default=Category.CLIENT,
        help_text=(
            "The category for this meeting. "
            "Meetings with external clients have additional requirements, such as consent for notetaking."
            "<br><br>- client: a meeting with a client or clients"
            "<br>- internal: an internal meeting within a firm, with no clients present"
            "<br>- debrief: a meeting the advisor has by themselves or with a small internal team "
            "to discuss a previous interaction"
        ),
    )

    everyone = models.BooleanField(
        default=False,
        help_text=(
            "Whether or not this meeting type is available to everyone. "
            "If this is true, then all users have access to this type of meeting. If this is false, then "
            "only users allowlisted by the organizations list or users list have access to this meeting type."
        ),
    )

    organizations = models.ManyToManyField(
        to=Organization,
        blank=True,
        help_text=(
            "The organizations which have access to this type of meeting."
            "If this is empty, then no specific organizations have access to this type of meeting. "
            "Users may still have access to this meeting via the `users` or `everyone` fields."
        ),
    )

    users = models.ManyToManyField(
        to=User,
        blank=True,
        help_text=(
            "The users who have access to this type of meeting. "
            "If this is empty, then no specific users have access to this type of meeting. "
            "Users may still have access to this meeting via the `organzations` or `everyone` fields."
        ),
    )

    agenda_templates = models.ManyToManyField(
        to="meetingsapp.StructuredMeetingDataTemplate",
        related_name="agenda_templates",
        blank=True,
        help_text="The agenda templates that are associated with this meeting type.",
    )

    context = models.TextField(
        blank=True,
        null=True,
        help_text="Context that can be provided to a language model when using data from this object as an input.",
    )

    def __str__(self) -> str:
        internal_name = self.internal_name or "<No internal name>"
        return f"{internal_name} (external name: {self.name})"

    def natural_key(self) -> Any:
        return (self.key,) if self.key else (self.uuid,)

    @classmethod
    def for_user(cls, user: User) -> models.QuerySet["MeetingType"]:
        """
        Returns a queryset of distinct `MeetingType`s to which the given user has access."""
        return cls.objects.filter(
            models.Q(everyone=True)
            | (models.Q(organizations__isnull=False) & models.Q(organizations=user.organization))
            | (models.Q(users__isnull=False) & models.Q(users=user))
        ).distinct()
