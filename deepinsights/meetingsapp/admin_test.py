import base64
import datetime
import io
import uuid
import zipfile
from unittest.mock import <PERSON><PERSON>ock, patch

import pytest
from django.contrib.admin.sites import AdminSite
from django.contrib.messages import get_messages
from django.contrib.messages.middleware import MessageMiddleware
from django.contrib.sessions.middleware import SessionMiddleware
from django.core.files.uploadedfile import SimpleUploadedFile
from django.db.models import Count
from django.http import HttpRequest
from django.test import RequestFactory, TestCase, override_settings
from django.urls import reverse
from django.utils.html import format_html

from deepinsights.core.integrations.meetingbot import recall_b64data
from deepinsights.core.preferences.preferences import Preferences
from deepinsights.meetingsapp.admin import (
    AudioBufferAdmin,
    MeetingBotAdmin,
    NoteAdmin,
    OrganizationAdmin,
    ScheduledEventAdmin,
    TaskAdmin,
)
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.audio_buffer import AudioBuffer
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.meeting_bot import MeetingBot
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.subscription_plan import SubscriptionPlan
from deepinsights.users.models.user import User


class MeetingBotAdminTest(TestCase):
    def setUp(self) -> None:
        self.site = AdminSite()
        self.bot_admin = MeetingBotAdmin(MeetingBot, self.site)
        self.user = User.objects.create(email="<EMAIL>", name="Test User")
        self.note = Note.objects.create(
            note_owner=self.user,
            metadata={"meeting_name": "Test Meeting"},
            status="completed",
            note_type="meeting",
        )
        self.bot = MeetingBot.objects.create(recall_bot_id="123", note=self.note)

    @patch.object(MeetingBot, "processing_task")
    def test_process_note_recording_action(self, mock_processing_task: MagicMock) -> None:
        request = HttpRequest()

        # Add session middleware
        SessionMiddleware(lambda x: x).process_request(request)
        request.session.save()

        # Add message middleware
        MessageMiddleware(lambda x: x).process_request(request)

        queryset = MeetingBot.objects.all()

        self.bot_admin.process_note_recording_action(request, queryset)

        mock_processing_task.delay_on_commit.assert_called_once_with(self.bot.uuid, True)

        # Check if a message was added
        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Reprocessing started for 1 note. Check Celery job status for updates.")


class NoteAdminTest(TestCase):
    def setUp(self) -> None:
        self.site = AdminSite()
        self.organization = Organization.objects.create(name="Test Org", description="A test organization")
        self.user = User.objects.create(email="<EMAIL>", name="Test User")
        self.test_client = Client.objects.create(name="Test Client", organization=self.organization)
        self.test_client.authorized_users.add(self.user)
        self.test_client.save()
        self.note_admin = NoteAdmin(Note, self.site)
        self.factory = RequestFactory()
        self.note = Note.objects.create(
            note_owner=self.user,
            metadata={"meeting_name": "Test Meeting"},
            status="completed",
            note_type="meeting",
        )
        self.deleted_note = Note.objects.create(
            note_owner=self.user,
            metadata={"meeting_name": "Deleted Test Meeting"},
            status="completed",
            note_type="meeting",
            is_deleted=True,
        )
        self.attendee1 = Attendee.objects.create(note=self.note, attendee_name="Test Client", client=self.test_client)
        self.attendee2 = Attendee.objects.create(note=self.note, attendee_name="Test User", user=self.user)

    def test_get_queryset(self) -> None:
        request = self.factory.get("/")
        queryset = self.note_admin.get_queryset(request)

        self.assertIn("note_owner", queryset.query.select_related)  # type: ignore[arg-type]

        # Check prefetch_related
        prefetch_lookups = []
        for lookup in queryset._prefetch_related_lookups:  # type: ignore[attr-defined]
            if isinstance(lookup, str):
                prefetch_lookups.append(lookup)
            elif hasattr(lookup, "prefetch_to"):
                prefetch_lookups.append(lookup.prefetch_to)
        self.assertIn("attendees", prefetch_lookups)
        self.assertIn("structuredmeetingdata_set", prefetch_lookups)
        self.assertIn("task_set", prefetch_lookups)

        # Check deferred fields
        deferred_fields_tuple = queryset.query.deferred_loading
        self.assertTrue(isinstance(deferred_fields_tuple, tuple))
        self.assertTrue(len(deferred_fields_tuple) > 0)
        deferred_fields = deferred_fields_tuple[0]
        self.assertIn("raw_asr_response", deferred_fields)
        self.assertIn("raw_transcript", deferred_fields)

        # Check final queryset content
        actual_ids = set(queryset.values_list("pk", flat=True))
        expected_ids = {self.note.pk, self.deleted_note.pk}
        self.assertEqual(actual_ids, expected_ids)

    def test_delete_queryset(self) -> None:
        request = self.factory.post("/")
        queryset = Note.objects.all()
        with patch.object(queryset, "defer") as mock_defer:
            self.note_admin.delete_queryset(request, queryset)
            mock_defer.assert_called_once_with(None)

    def test_title(self) -> None:
        self.assertEqual(self.note_admin.title(self.note), "Test Meeting")

        note_without_metadata = Note.objects.create(note_owner=self.note.note_owner)
        self.assertEqual(self.note_admin.title(note_without_metadata), "Err:Contact support:No Metadata")

    def test_note_url(self) -> None:
        expected_url = f"{self.note.public_url()}"
        expected_html = format_html(f'<a href="{expected_url}" target="_blank">{expected_url}</a>')
        self.assertEqual(self.note_admin.note_url(self.note), expected_html)

    def test_provider_bot_urls_empty(self) -> None:
        self.assertEqual(self.note_admin.provider_bot_urls(self.note), "N/A")

    def test_provider_bot_urls(self) -> None:
        MeetingBot.objects.create(uuid=uuid.uuid4(), recall_bot_id="123", note=self.note)
        expected_url = "https://go/recallbot/123"
        expected_html = format_html(f'<a href="{expected_url}" target="_blank">{expected_url}</a>')
        self.assertEqual(self.note_admin.provider_bot_urls(self.note), expected_html)

    def test_provider_bot_urls_for_multiple_bots(self) -> None:
        MeetingBot.objects.create(uuid=uuid.uuid4(), recall_bot_id="123", note=self.note)
        MeetingBot.objects.create(uuid=uuid.uuid4(), recall_bot_id="456", note=self.note)
        first_bot_url = "https://go/recallbot/123"
        second_bot_url = "https://go/recallbot/456"
        expected_html = format_html(
            f'<a href="{first_bot_url}" target="_blank">{first_bot_url}</a><br><a href="{second_bot_url}" target="_blank">{second_bot_url}</a>'
        )
        self.assertEqual(self.note_admin.provider_bot_urls(self.note), expected_html)

    def test_zeplyn_bot_urls_empty(self):  # type: ignore[no-untyped-def]
        self.assertEqual(self.note_admin.zeplyn_bot_urls(self.note), "N/A")

    def test_zeplyn_bot_urls(self):  # type: ignore[no-untyped-def]
        bot = MeetingBot.objects.create(uuid=uuid.uuid4(), recall_bot_id="123", note=self.note)
        expected_url = f"/api/admin/meetingsapp/meetingbot/{bot.pk}/change/"
        expected_html = format_html(f'<a href="{expected_url}" target="_blank">{expected_url}</a>')
        self.assertEqual(self.note_admin.zeplyn_bot_urls(self.note), expected_html)

    def test_zeplyn_bot_urls_for_multiple_bots(self):  # type: ignore[no-untyped-def]
        bot_one = MeetingBot.objects.create(uuid=uuid.uuid4(), recall_bot_id="123", note=self.note)
        bot_two = MeetingBot.objects.create(uuid=uuid.uuid4(), recall_bot_id="456", note=self.note)
        first_bot_url = f"/api/admin/meetingsapp/meetingbot/{bot_one.pk}/change/"
        second_bot_url = f"/api/admin/meetingsapp/meetingbot/{bot_two.pk}/change/"
        expected_html = format_html(
            f'<a href="{first_bot_url}" target="_blank">{first_bot_url}</a><br><a href="{second_bot_url}" target="_blank">{second_bot_url}</a>'
        )
        self.assertEqual(self.note_admin.zeplyn_bot_urls(self.note), expected_html)

    def test_s3_url_empty(self):  # type: ignore[no-untyped-def]
        self.assertEqual(self.note_admin.s3_url(self.note), "N/A")

    @override_settings(AWS_MEETING_BUCKET="test_folder", AWS_S3_REGION_NAME="test_region")
    def test_s3_url(self) -> None:
        self.note.file_path = "test/file/path"

        expected_url = "https://test_region.console.aws.amazon.com/s3/buckets/test_folder/test/file/path"
        expected_html = format_html(f'<a href="{expected_url}" target="_blank">{expected_url}</a>')
        self.assertEqual(self.note_admin.s3_url(self.note), expected_html)

    def test_audio_buffer_url_no_buffers(self) -> None:
        self.assertEqual(self.note_admin.audio_buffers_url(self.note), "N/A (no audio buffers)")

    def test_audio_buffer_url(self) -> None:
        AudioBuffer.objects.create(note=self.note, data=b"test_data")
        expected_url = f"/api/admin/meetingsapp/audiobuffer/?note_id__exact={self.note.id}"
        expected_html = format_html(f'<a href="{expected_url}" target="_blank">{expected_url}</a>')
        self.assertEqual(self.note_admin.audio_buffers_url(self.note), expected_html)

    def test_get_attendees(self):  # type: ignore[no-untyped-def]
        result = self.note_admin.get_attendees(self.note)

        # Check for the presence of both attendees
        self.assertIn(f'<a href="/api/admin/meetingsapp/attendee/{self.attendee1.id}/change/">Test Client</a>', result)
        self.assertIn(f'<a href="/api/admin/meetingsapp/client/{self.test_client.id}/change/">Client</a>', result)
        self.assertIn(f'<a href="/api/admin/meetingsapp/attendee/{self.attendee2.id}/change/">Test User</a>', result)
        self.assertIn(f'<a href="/api/admin/users/user/{self.user.id}/change/">User</a>', result)

    def test_get_tasks_link(self) -> None:
        Task.objects.create(task_title="Task 1", task_owner=self.user, note=self.note)
        Task.objects.create(task_title="Task 2", task_owner=self.user, note=self.note)

        note_with_count = Note.objects.annotate(task_count=Count("task")).get(id=self.note.id)

        result = self.note_admin.get_tasks_link(note_with_count)

        tasks_url = reverse("admin:meetingsapp_task_changelist")
        expected_url = f"{tasks_url}?note_id__exact={self.note.id}"

        self.assertIn(expected_url, result)

        self.assertIn("View Tasks (2)", result)

        expected_html = f'<a href="{expected_url}" target="_blank">View Tasks (2)</a>'
        self.assertEqual(result, expected_html)

    @patch("deepinsights.meetingsapp.admin.process_note_recording")
    def test_process_note_recording_action(self, mock_process_note_recording: MagicMock) -> None:
        request = self.factory.post("/")

        # Add session middleware
        SessionMiddleware(lambda x: x).process_request(request)
        request.session.save()

        # Add message middleware
        MessageMiddleware(lambda x: x).process_request(request)

        queryset = Note.objects.all()

        self.note_admin.process_note_recording_action(request, queryset)

        mock_process_note_recording.delay.assert_called_once_with(
            self.note.note_owner.uuid if self.note.note_owner else None, self.note.uuid, force_process=True
        )

        # Check if a message was added
        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Reprocessing started for 1 note. Check Celery job status for updates.")

    @patch("deepinsights.meetingsapp.admin.process_note_recording")
    def test_process_note_recording_from_audio_buffers_action(self, mock_process_note_recording: MagicMock) -> None:
        request = self.factory.post("/")

        # Add session middleware
        SessionMiddleware(lambda x: x).process_request(request)
        request.session.save()

        # Add message middleware
        MessageMiddleware(lambda x: x).process_request(request)

        queryset = Note.objects.all()

        self.note_admin.process_note_recording_from_audio_buffers_action(request, queryset)

        self.assertIsNotNone(self.note.note_owner)
        note_owner_uuid = self.note.note_owner.uuid if self.note.note_owner else None
        mock_process_note_recording.delay.assert_called_once_with(
            note_owner_uuid, self.note.uuid, force_process=True, use_audio_buffers=True
        )

        # Check if a message was added
        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Reprocessing started for 1 note. Check Celery job status for updates.")

    @patch.object(MeetingBot, "processing_task")
    def test_process_bot_recording_action(self, mock_processing_task: MagicMock) -> None:
        request = self.factory.post("/")

        # Add session middleware
        SessionMiddleware(lambda x: x).process_request(request)
        request.session.save()

        # Add message middleware
        MessageMiddleware(lambda x: x).process_request(request)

        queryset = Note.objects.all()

        MeetingBot.objects.create(note=self.note, recall_bot_id="123")
        newer_bot = MeetingBot.objects.create(note=self.note, recall_bot_id="456")

        self.note_admin.process_bot_recording_action(request, queryset)

        mock_processing_task.delay_on_commit.assert_called_once_with(newer_bot.uuid, True)

        # Check if a message was added
        messages = list(get_messages(request))
        self.assertEqual(len(messages), 2)
        self.assertEqual(
            str(messages[0]), f"Note {self.note.uuid} has multiple meeting bots. Processing the newest bot."
        )
        self.assertEqual(str(messages[1]), "Reprocessing started for 1 note. Check Celery job status for updates.")

    def test_readonly_fields(self) -> None:
        self.assertIn("scheduled_start_time", self.note_admin.readonly_fields)
        self.assertIn("scheduled_end_time", self.note_admin.readonly_fields)
        self.assertIn("title", self.note_admin.readonly_fields)
        self.assertIn("provider_bot_urls", self.note_admin.readonly_fields)
        self.assertIn("zeplyn_bot_urls", self.note_admin.readonly_fields)
        self.assertIn("s3_url", self.note_admin.readonly_fields)
        self.assertIn("clientinteraction", self.note_admin.readonly_fields)

    def test_list_display(self) -> None:
        expected_fields = [
            "id",
            "note_owner",
            "title",
            "created",
            "modified",
            "status",
            "is_deleted",
            "note_type",
            "note_url",
            "get_attendees",
        ]
        self.assertEqual(self.note_admin.list_display, expected_fields)

    def test_search_fields(self) -> None:
        expected_fields = ["=id", "=uuid", "=note_owner__email", "metadata__meeting_name"]
        self.assertEqual(self.note_admin.search_fields, expected_fields)

    def test_get_search_results_empty_search(self):  # type: ignore[no-untyped-def]
        request = self.factory.get("/")
        queryset = Note.objects.all()
        result_queryset, use_distinct = self.note_admin.get_search_results(request, queryset, "")

        # Verify deferred fields
        self.assertIn("raw_asr_response", result_queryset.query.deferred_loading[0])
        self.assertIn("raw_transcript", result_queryset.query.deferred_loading[0])
        self.assertFalse(use_distinct)
        self.assertEqual(list(result_queryset), list(queryset))

    def test_get_search_results_by_id(self) -> None:
        request = self.factory.get("/")
        queryset = Note.objects.all()
        result_queryset, use_distinct = self.note_admin.get_search_results(request, queryset, str(self.note.id))

        self.assertEqual(len(result_queryset), 1)
        self.assertEqual(result_queryset.first(), self.note)
        self.assertFalse(use_distinct)

    def test_get_search_results_invalid_id(self) -> None:
        """Test search with invalid numeric input"""
        request = self.factory.get("/")
        queryset = Note.objects.all()
        result_queryset, use_distinct = self.note_admin.get_search_results(request, queryset, "99999")

        self.assertEqual(len(result_queryset), 0)
        self.assertFalse(use_distinct)

    def test_get_search_results_by_email(self) -> None:
        request = self.factory.get("/")
        queryset = Note.objects.all()

        initial_note_count = Note.objects.filter(note_owner__email=self.user.email).count()

        # Create an additional note for a different user to ensure we're not getting those
        different_user = User.objects.create(
            email="<EMAIL>",
            username="<EMAIL>",
            name="Different User",
        )
        Note.objects.create(
            note_owner=different_user,
            metadata={"meeting_name": "Different Meeting"},
            status="completed",
            note_type="meeting",
        )

        result_queryset, use_distinct = self.note_admin.get_search_results(request, queryset, self.user.email)

        # Verify we get only the notes for our test user
        self.assertEqual(len(result_queryset), initial_note_count)
        for note in result_queryset:
            self.assertEqual(note.note_owner.email, self.user.email)
        self.assertFalse(use_distinct)

    def test_get_search_results_by_uuid(self) -> None:
        request = self.factory.get("/")
        queryset = Note.objects.all()
        result_queryset, use_distinct = self.note_admin.get_search_results(request, queryset, str(self.note.uuid))

        self.assertEqual(len(result_queryset), 1)
        self.assertEqual(result_queryset.first(), self.note)
        self.assertFalse(use_distinct)

    def test_get_search_results_invalid_uuid(self) -> None:
        request = self.factory.get("/")
        queryset = Note.objects.all()
        result_queryset, use_distinct = self.note_admin.get_search_results(request, queryset, "not-a-uuid")

        # Should fall back to default search behavior
        expected_queryset, expected_distinct = super(NoteAdmin, self.note_admin).get_search_results(
            request, queryset.defer("raw_asr_response", "raw_transcript"), "not-a-uuid"
        )
        self.assertEqual(list(result_queryset), list(expected_queryset))
        self.assertEqual(use_distinct, expected_distinct)

    def test_get_search_results_by_meeting_name(self) -> None:
        request = self.factory.get("/")
        queryset = Note.objects.all()
        result_queryset, _ = self.note_admin.get_search_results(request, queryset, "Test Meeting")

        self.assertTrue(len(result_queryset) > 0)
        for note in result_queryset:
            self.assertIn("Test Meeting", note.metadata.get("meeting_name", ""))

    def test_list_filter(self) -> None:
        expected_fields = ["status", "note_type", "is_deleted", "note_owner__organization"]
        self.assertEqual(self.note_admin.list_filter, expected_fields)

    def test_actions(self) -> None:
        self.assertIn("process_note_recording_action", self.note_admin.actions)

    def test_filter_horizontal(self) -> None:
        self.assertEqual(self.note_admin.filter_horizontal, ["authorized_users"])

    def test_delete_transcript_action(self) -> None:
        request = self.factory.post("/")

        # Add session middleware
        SessionMiddleware(lambda x: None).process_request(request)  # type: ignore[arg-type]
        request.session.save()

        # Add message middleware
        MessageMiddleware(lambda x: None).process_request(request)  # type: ignore[arg-type]

        note_defaults = {
            "note_owner": self.user,
            "metadata": {"meeting_name": "Test Meeting"},
            "note_type": "meeting",
            "raw_asr_response": "Test ASR",
            "diarized_trans_with_names": "Test Diarized Transcript",
            "raw_transcript": "Raw Transcript",
        }

        Note.objects.create(status="processed", **note_defaults)
        Note.objects.create(status="processed", **note_defaults)
        Note.objects.create(status="finalized", **note_defaults)

        queryset = Note.objects.filter(status="processed")

        self.note_admin.delete_transcript_action(request, queryset)

        for note in Note.objects.filter(status="processed"):
            self.assertIsNone(note.raw_asr_response)
            self.assertIsNone(note.diarized_trans_with_names)
            self.assertIsNone(note.raw_transcript)
        for note in Note.objects.filter(status="finalized"):
            self.assertIsNotNone(note.raw_asr_response)
            self.assertIsNotNone(note.diarized_trans_with_names)
            self.assertIsNotNone(note.raw_transcript)

        # Check if a message was added
        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)

    def test_scheduled_times_empty(self) -> None:
        self.assertIsNone(self.note_admin.scheduled_start_time(self.note))
        self.assertIsNone(self.note_admin.scheduled_end_time(self.note))

    def test_scheduled_start_time_from_metadata(self) -> None:
        self.note.metadata = {**(self.note.metadata or {}), "scheduled_at": "2023-10-01T12:00:00Z"}
        self.note.save()

        self.assertEqual(self.note_admin.scheduled_start_time(self.note), "2023-10-01T12:00:00Z")

    def test_scheduled_times_from_scheduled_event(self) -> None:
        self.note.metadata = {**(self.note.metadata or {}), "scheduled_at": "2023-10-01T12:00:00Z"}
        self.note.save()

        ScheduledEvent.objects.create(
            user=self.user,
            note=self.note,
            start_time=datetime.datetime(2023, 10, 1, 14, 0, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2023, 10, 1, 15, 0, 0, tzinfo=datetime.timezone.utc),
        )

        self.assertEqual(self.note_admin.scheduled_start_time(self.note), "2023-10-01 14:00:00 UTC")
        self.assertEqual(self.note_admin.scheduled_end_time(self.note), "2023-10-01 15:00:00 UTC")


class TaskAdminTest(TestCase):
    def setUp(self) -> None:
        self.site = AdminSite()
        self.task_admin = TaskAdmin(Task, self.site)
        self.user = User.objects.create(email="<EMAIL>", name="Test User")
        self.note = Note.objects.create(
            note_owner=self.user,
            metadata={"meeting_name": "Test Meeting"},
            status="completed",
            note_type="meeting",
        )
        self.task = Task.objects.create(
            task_title="Test Task", task_desc="Test Description", task_owner=self.user, note=self.note
        )
        self.long_title_task = Task.objects.create(
            task_title="This is a very long task title that should be truncated in the admin display view",
            task_owner=self.user,
            note=self.note,
        )

    def test_get_queryset(self) -> None:
        request = HttpRequest()
        queryset = self.task_admin.get_queryset(request)

        select_related_fields = queryset.query.select_related
        if isinstance(select_related_fields, dict):
            self.assertIn("task_owner", select_related_fields)
            self.assertIn("assignee", select_related_fields)
            self.assertIn("note", select_related_fields)

        deferred_fields = queryset.query.deferred_loading[0]
        self.assertIn("metadata", deferred_fields)
        self.assertIn("note__raw_asr_response", deferred_fields)
        self.assertIn("note__raw_transcript", deferred_fields)
        self.assertIn("note__metadata", deferred_fields)

    def test_delete_queryset(self) -> None:
        request = HttpRequest()
        queryset = Task.objects.all()
        with patch.object(queryset, "defer") as mock_defer:
            self.task_admin.delete_queryset(request, queryset)
            mock_defer.assert_called_once_with(None)

    def test_get_completed_status(self) -> None:
        self.assertEqual(self.task_admin.get_completed_status(self.task), "Pending")

        self.task.completed = True
        self.task.save()
        self.assertEqual(self.task_admin.get_completed_status(self.task), "Completed")

    def test_get_truncated_title(self) -> None:
        # Test non-truncated title
        self.assertEqual(self.task_admin.get_truncated_title(self.task), "Test Task")

        # Test truncated title
        truncated = self.task_admin.get_truncated_title(self.long_title_task)
        self.assertTrue(truncated.endswith("..."))
        self.assertTrue(len(truncated) <= 53)

    def test_get_note_link(self) -> None:
        # Test task with note
        expected_url = reverse("admin:meetingsapp_note_change", args=[self.note.id])
        expected_title = "Test Meeting"
        result = self.task_admin.get_note_link(self.task)
        self.assertIn(expected_url, result)
        self.assertIn(expected_title, result)

        # Test task without note
        task_without_note = Task.objects.create(task_title="No Note Task", task_owner=self.user)
        result = self.task_admin.get_note_link(task_without_note)
        self.assertEqual(result, "<span>No note</span>")


class OrganizationAdminTest(TestCase):
    def setUp(self) -> None:
        self.site = AdminSite()
        self.organization_admin = OrganizationAdmin(Organization, self.site)
        self.factory = RequestFactory()
        self.organization = Organization.objects.create(name="Test Org", description="A test organization")

    def _set_recording_image(self, image_data: str) -> None:
        preferences = Preferences()
        preferences.bot_preferences.recording_image_b64 = image_data
        self.organization.preferences = preferences.to_dict()
        self.organization.save()

    def _set_not_recording_image(self, image_data: str) -> None:
        preferences = Preferences()
        preferences.bot_preferences.not_recording_image_b64 = image_data
        self.organization.preferences = preferences.to_dict()
        self.organization.save()

    def _add_session_and_messages(self, request: HttpRequest) -> None:
        SessionMiddleware(lambda response: response).process_request(request)
        MessageMiddleware(lambda response: response).process_request(request)
        request.session.save()

    def test_get_queryset(self) -> None:
        request = self.factory.get("/")
        queryset = self.organization_admin.get_queryset(request)
        deferred_loading = queryset.query.deferred_loading

        expected_fields = frozenset({"crm_configuration", "preferences"})
        expected_only = True

        self.assertEqual(deferred_loading[0], expected_fields)
        self.assertEqual(deferred_loading[1], expected_only)

    def test_delete_queryset(self) -> None:
        request = self.factory.post("/")
        queryset = Organization.objects.all()
        with patch.object(queryset, "defer") as mock_defer:
            self.organization_admin.delete_queryset(request, queryset)
            mock_defer.assert_called_once_with(None)

    def test_import_users_using_csv_valid(self) -> None:
        csv_content = (
            b"Name,Email,License Type,First Name,Last Name\n"
            b"John Doe,<EMAIL>,advisor,John,Doe\n"
            b"Jane Smith,<EMAIL>,csa,Jane,Smith"
        )

        csv_file = SimpleUploadedFile("users.csv", csv_content, content_type="text/csv")

        request = self.factory.post("/", {"apply": "yes", "csv_file": csv_file})
        self._add_session_and_messages(request)
        queryset = Organization.objects.filter(pk=self.organization.pk)
        response = self.organization_admin.import_users_using_csv(request, queryset)

        self.assertEqual(User.objects.count(), 2)
        john = User.objects.get(email="<EMAIL>")
        jane = User.objects.get(email="<EMAIL>")

        # Verify user attributes
        self.assertEqual(john.name, "John Doe")
        self.assertEqual(john.role, "advisor")
        self.assertEqual(john.first_name, "John")
        self.assertEqual(john.last_name, "Doe")

        self.assertEqual(jane.name, "Jane Smith")
        self.assertEqual(jane.role, "csa")
        self.assertEqual(jane.first_name, "Jane")
        self.assertEqual(jane.last_name, "Smith")

        # Check if licenses were created
        self.assertEqual(john.license_type, User.LicenseType.advisor)
        self.assertEqual(jane.license_type, User.LicenseType.csa)

        self.assertEqual(john.organization, self.organization)
        self.assertEqual(jane.organization, self.organization)

        expected_john_metadata = {
            "user_context": f"{self.organization.name} is a wealth management firm. John Doe is a advisor at {self.organization.name}.",
            "biasing_words": ["Zeplyn"],
        }
        expected_jane_metadata = {
            "user_context": f"{self.organization.name} is a wealth management firm. Jane Smith is a csa at {self.organization.name}.",
            "biasing_words": ["Zeplyn"],
        }
        self.assertEqual(john.metadata, expected_john_metadata)
        self.assertEqual(jane.metadata, expected_jane_metadata)

        self.assertEqual(response.status_code, 302)
        messages = list(get_messages(request))
        self.assertTrue(any("2 created" in str(msg) for msg in messages))

    def test_import_users_using_csv_invalid(self) -> None:
        request = self.factory.post("/", {"apply": "yes"})
        self._add_session_and_messages(request)
        queryset = Organization.objects.filter(pk=self.organization.pk)
        self.organization_admin.import_users_using_csv(request, queryset)

        messages = list(get_messages(request))
        self.assertTrue(
            any("Form is invalid. Error(s): * No file was uploaded." in str(message) for message in messages)
        )

    def test_import_users_using_csv_invalid_file(self) -> None:
        invalid_file = SimpleUploadedFile("file.txt", b"invalid content", content_type="text/plain")
        request = self.factory.post("/", {"apply": "yes", "csv_file": invalid_file})
        self._add_session_and_messages(request)
        queryset = Organization.objects.filter(pk=self.organization.pk)
        self.organization_admin.import_users_using_csv(request, queryset)

        messages = list(get_messages(request))
        self.assertTrue(
            any("Form is invalid. Error(s): * File must be a CSV document." in str(message) for message in messages)
        )

    def test_users_link(self) -> None:
        expected_url = f"/api/admin/users/user/?organization__pk__exact={self.organization.id}"
        result = self.organization_admin.users_link(self.organization)
        self.assertIn(expected_url, result)
        self.assertIn("View Users", result)

    @patch("deepinsights.meetingsapp.admin_utils.delete_organization_data_task")
    def test_delete_organization_and_associated_data(self, mock_delete_task: MagicMock) -> None:
        request = self.factory.post("/")
        self._add_session_and_messages(request)
        queryset = Organization.objects.filter(pk=self.organization.pk)

        self.organization_admin.delete_organization_and_associated_data(request, queryset)

        mock_delete_task.delay.assert_called_once_with(str(self.organization.id))
        messages = list(get_messages(request))
        expected_message = f"Organization deletion process started for {self.organization.name}. You will receive an email confirmation when complete."
        self.assertEqual(str(messages[0]), expected_message)

    def test_delete_organization_and_associated_data_multiple_orgs(self) -> None:
        request = self.factory.post("/")
        self._add_session_and_messages(request)
        org2 = Organization.objects.create(name="Test Org 2")
        queryset = Organization.objects.filter(pk__in=[self.organization.pk, org2.pk])

        self.organization_admin.delete_organization_and_associated_data(request, queryset)

        messages = list(get_messages(request))
        self.assertEqual(str(messages[0]), "Please select exactly one organization for this action.")

    def test_delete_organization_and_associated_data_no_org(self) -> None:
        request = self.factory.post("/")
        self._add_session_and_messages(request)
        queryset = Organization.objects.none()

        self.organization_admin.delete_organization_and_associated_data(request, queryset)

        messages = list(get_messages(request))
        self.assertEqual(str(messages[0]), "No organization selected.")

    def test_recording_image_valid(self) -> None:
        self._set_recording_image(base64.b64encode(b"valid image data").decode("utf-8"))

        result = self.organization_admin.recording_image(self.organization)
        self.assertIn('<img src="data:image/jpeg; base64,', result)
        self.assertIn('style="max-width: 400px; max-height: 400px;"', result)

    def test_recording_image_invalid_base64(self) -> None:
        self._set_recording_image("invalid base64 data")

        result = self.organization_admin.recording_image(self.organization)
        self.assertEqual(result, "<span>Invalid image data</span>")

    def test_recording_image_empty(self) -> None:
        self._set_recording_image("")
        self.organization.save()

        result = self.organization_admin.recording_image(self.organization)
        self.assertEqual(
            result,
            "<p><b>No org-level image set; this is the default</b></p>"
            f'<img src="data:image/jpeg; base64, {recall_b64data.recording_default_jpeg}" style="max-width: 400px; max-height: 400px;" />',
        )

    def test_recording_image_none(self) -> None:
        result = self.organization_admin.recording_image(self.organization)
        self.assertEqual(
            result,
            "<p><b>No org-level image set; this is the default</b></p>"
            f'<img src="data:image/jpeg; base64, {recall_b64data.recording_default_jpeg}" style="max-width: 400px; max-height: 400px;" />',
        )

    def test_not_recording_image_valid(self) -> None:
        self._set_not_recording_image(base64.b64encode(b"valid image data").decode("utf-8"))

        result = self.organization_admin.not_recording_image(self.organization)
        self.assertIn('<img src="data:image/jpeg; base64,', result)
        self.assertIn('style="max-width: 400px; max-height: 400px;"', result)

    def test_not_recording_image_invalid_base64(self) -> None:
        self._set_not_recording_image("invalid base64 data")

        result = self.organization_admin.not_recording_image(self.organization)
        self.assertEqual(result, "<span>Invalid image data</span>")

    def test_not_recording_image_empty(self) -> None:
        self._set_not_recording_image("")
        result = self.organization_admin.not_recording_image(self.organization)
        self.assertEqual(
            result,
            "<p><b>No org-level image set; this is the default</b></p>"
            f'<img src="data:image/jpeg; base64, {recall_b64data.not_recording_default_jpeg}" style="max-width: 400px; max-height: 400px;" />',
        )

    def test_not_recording_image_none(self) -> None:
        result = self.organization_admin.not_recording_image(self.organization)
        self.assertEqual(
            result,
            "<p><b>No org-level image set; this is the default</b></p>"
            f'<img src="data:image/jpeg; base64, {recall_b64data.not_recording_default_jpeg}" style="max-width: 400px; max-height: 400px;" />',
        )

    def test_plan_link_no_plan(self) -> None:
        result = self.organization_admin.plan_link(self.organization)
        self.assertEqual(result, "<span>No plan associated</span>")

    def test_plan_link_with_plan(self) -> None:
        plan = SubscriptionPlan.objects.create(
            title="Test Plan", start_date=datetime.date.today(), _entitlements={}, organization=self.organization
        )

        expected_url = reverse("admin:users_subscriptionplan_change", args=[plan.id])
        expected_html = format_html(f'<a href="{expected_url}" target="_blank">View Plan</a>')
        self.assertEqual(self.organization_admin.plan_link(self.organization), expected_html)


class AudioBufferAdminTest(TestCase):
    def setUp(self) -> None:
        self.site = AdminSite()
        self.audio_buffer_admin = AudioBufferAdmin(AudioBuffer, self.site)
        self.user = User.objects.create(email="<EMAIL>", name="Test User")
        self.note = Note.objects.create(
            note_owner=self.user,
            metadata={"meeting_name": "Test Meeting"},
            status="completed",
            note_type="meeting",
        )
        self.web_audio_buffer1 = AudioBuffer.objects.create(
            note=self.note, sequence=1, data=b"web audio data 1", duration=5, mime_type="audio/webm;codecs=opus"
        )
        self.web_audio_buffer2 = AudioBuffer.objects.create(
            note=self.note, sequence=2, data=b"web audio data 2", duration=10, mime_type="audio/webm;codecs=opus"
        )
        self.ios_audio_buffer1 = AudioBuffer.objects.create(
            note=self.note, sequence=1, data=b"ios audio data 1", duration=5, mime_type="audio/mp4"
        )
        self.ios_audio_buffer2 = AudioBuffer.objects.create(
            note=self.note, sequence=2, data=b"ios audio data 2", duration=10, mime_type="audio/mp4"
        )
        self.invalid_audio_buffer = AudioBuffer.objects.create(
            note=self.note, sequence=3, data=b"invalid audio data", duration=15, mime_type="audio/invalid"
        )

    def _request(self) -> HttpRequest:
        request = HttpRequest()

        # Add session middleware
        SessionMiddleware(lambda x: x).process_request(request)
        request.session.save()

        # Add message middleware
        MessageMiddleware(lambda x: x).process_request(request)

        return request

    def test_download_audio_data_empty(self) -> None:
        request = self._request()
        queryset = AudioBuffer.objects.none()

        response = self.audio_buffer_admin.download_audio_data(request, queryset)
        if not response:
            self.fail("Response is None")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response["Content-Disposition"], "attachment; filename=audio.opus")
        self.assertEqual(response["Content-Type"], "audio/ogg;codecs=opus")
        self.assertEqual(response.content, b"")

        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "No MIME type found for audio. Assumed audio/webm")

    def test_download_audio_data_no_mime_type(self) -> None:
        request = self._request()
        self.web_audio_buffer1.mime_type = None
        self.web_audio_buffer1.save()
        queryset = AudioBuffer.objects.filter(id=self.web_audio_buffer1.id)

        response = self.audio_buffer_admin.download_audio_data(request, queryset)
        if not response:
            self.fail("Response is None")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response["Content-Disposition"], "attachment; filename=audio.opus")
        self.assertEqual(response["Content-Type"], "audio/ogg;codecs=opus")
        self.assertEqual(response.content, b"web audio data 1")

        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "No MIME type found for audio. Assumed audio/webm")

    def test_download_invalid_audio_data(self) -> None:
        request = self._request()
        queryset = AudioBuffer.objects.filter(mime_type__startswith="audio/invalid")
        response = self.audio_buffer_admin.download_audio_data(request, queryset)

        self.assertIsNone(response)

        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Unsupported MIME type")

    def test_download_invalid_multiple_mime_types(self) -> None:
        request = self._request()
        queryset = AudioBuffer.objects.all()
        response = self.audio_buffer_admin.download_audio_data(request, queryset)

        self.assertIsNone(response)

        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Cannot download audio data with different MIME types")

    def test_download_ios_audio_data(self) -> None:
        request = self._request()
        queryset = AudioBuffer.objects.filter(mime_type__startswith="audio/mp4")
        response = self.audio_buffer_admin.download_audio_data(request, queryset)

        if not response:
            self.fail("Response is None")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response["Content-Disposition"], "attachment; filename=audio.zip")
        self.assertEqual(response["Content-Type"], "application/zip")

        with zipfile.ZipFile(io.BytesIO(response.content), "r") as zip_file:
            self.assertIn("playlist.m3u8", zip_file.namelist())

            chunk_one = f"{self.ios_audio_buffer1.id}.ts"
            self.assertIn(chunk_one, zip_file.namelist())
            self.assertEqual(zip_file.read(chunk_one), b"ios audio data 1")

            chunk_two = f"{self.ios_audio_buffer2.id}.ts"
            self.assertIn(chunk_two, zip_file.namelist())
            self.assertEqual(zip_file.read(chunk_two), b"ios audio data 2")

        self.assertEqual(len(get_messages(request)), 0)

    def test_download_web_audio_data(self) -> None:
        request = self._request()
        queryset = AudioBuffer.objects.filter(mime_type__startswith="audio/webm")
        response = self.audio_buffer_admin.download_audio_data(request, queryset)

        if not response:
            self.fail("Response is None")

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response["Content-Disposition"], "attachment; filename=audio.opus")
        self.assertEqual(response["Content-Type"], "audio/ogg;codecs=opus")

        expected_data = b"web audio data 1web audio data 2"
        self.assertEqual(response.content, expected_data)

        self.assertEqual(len(get_messages(request)), 0)


@pytest.mark.django_db
class TestScheduledEventAdmin:
    @pytest.fixture
    def admin_instance(self) -> ScheduledEventAdmin:
        return ScheduledEventAdmin(ScheduledEvent, AdminSite())

    def test_title_with_note(self, admin_instance: ScheduledEventAdmin) -> None:
        note = Note.objects.create(metadata={"meeting_name": "Test Meeting"})
        event = ScheduledEvent(note=note)
        assert admin_instance.title(event) == "Test Meeting"

    def test_title_with_source_data_title(self, admin_instance: ScheduledEventAdmin) -> None:
        event = ScheduledEvent(source_data={"title": "Source Data Meeting Title"})
        assert admin_instance.title(event) == "Source Data Meeting Title"

    def test_title_with_source_data_no_title_key(self, admin_instance: ScheduledEventAdmin) -> None:
        event = ScheduledEvent(source_data={"description": "Some description"})
        assert admin_instance.title(event) == "<No Title>"

    def test_title_with_no_note_no_source_data(self, admin_instance: ScheduledEventAdmin) -> None:
        event = ScheduledEvent()
        assert admin_instance.title(event) == "<No Title>"

    def test_title_with_note_and_source_data(self, admin_instance: ScheduledEventAdmin) -> None:
        note = Note.objects.create(metadata={"meeting_name": "Test Meeting"})
        event = ScheduledEvent(note=note, source_data={"title": "Source Data Title"})
        assert admin_instance.title(event) == "Test Meeting"
