from django.http import HttpRequest, JsonResponse
from rest_framework.decorators import api_view, authentication_classes, permission_classes
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from rest_framework_simplejwt.authentication import JWTAuthentication
from waffle.views import waffle_json


# A wrapper around the waffle_json view to adjust handling for signed-out users.
@api_view(["GET"])
@permission_classes([IsAuthenticatedOrReadOnly])
@authentication_classes([JWTAuthentication])
def get_flags(request: HttpRequest) -> JsonResponse:
    if not request.user or not request.user.is_authenticated:
        return JsonResponse({"flags": {}, "switches": {}, "samples": {}})
    return waffle_json(request)  # type: ignore[no-untyped-call, no-any-return]
