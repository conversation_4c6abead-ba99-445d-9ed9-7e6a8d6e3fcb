import pytest
from django.http import JsonResponse
from rest_framework import status
from rest_framework.test import APIClient

from deepinsights.users.models.flags import Flag
from deepinsights.users.models.user import User


@pytest.fixture(autouse=True)
def deleteAllFlags() -> None:
    Flag.objects.all().delete()


@pytest.fixture
def api_client() -> APIClient:
    return APIClient()


@pytest.mark.django_db
def test_flags_unauthenticated(api_client: APIClient) -> None:
    response = api_client.get("/api/v1/users/flags")
    assert response.status_code == status.HTTP_200_OK
    assert response.content == JsonResponse({"flags": {}, "switches": {}, "samples": {}}).content


def test_flags_authenticated(api_client: APIClient, django_user_model: User) -> None:
    user = django_user_model.objects.create_user("<EMAIL>", "<EMAIL>", "123")
    flag = Flag.objects.create(name="test", everyone=True)
    api_client.force_authenticate(user=user)
    response = api_client.get("/api/v1/users/flags")
    assert response.status_code == status.HTTP_200_OK
    # This is a bit odd, but Django's JSON encoder has custom logic to make datetimes conform to
    # ECMA standards, and it's simpler to use JsonResponse to get the same encoding behavior.
    assert (
        response.content
        == JsonResponse(
            {
                "flags": {
                    "test": {
                        "is_active": True,
                        "last_modified": flag.modified,  # type: ignore[has-type]
                    }
                },
                "switches": {},
                "samples": {},
            }
        ).content
    )
