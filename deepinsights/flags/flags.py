import logging
import os
from enum import Enum
from typing import TYPE_CHECKING, Any

from django.http import HttpRequest
from waffle import flag_is_active

if TYPE_CHECKING:
    from django.contrib.auth.models import AbstractBaseUser


# Environments in which flags can be enabled.
class Environment(Enum):
    # Local and
    DEV = 0
    # Customer Demos (demo..zeplyn.ai)
    DEMO = 1
    # Production or production-like environments
    PROD = 2

    # The flag environment for the current running app.
    @classmethod
    def current(self) -> "Environment":
        flag_environment = os.environ.get("FLAG_ENVIRONMENT")
        if flag_environment == "dev":
            return Environment.DEV
        elif flag_environment == "demo":
            return Environment.DEMO
        elif flag_environment == "prod":
            return Environment.PROD
        else:
            return Environment.PROD


# A feature flag.
#
# This is a lightweight wrapper around the Flag model, which makes it a bit simpler to constrct
# flags that are only available in certain environments.
#
# - available_environments: The environments in which the flag can be enabled. Environments in this
#   list will defer the flag status to other diversion criteria (i.e., enabled for certain
#   users/orgs/etc). An empty list means no environments are available (i.e., the flag cannot be
#   toggled via diversion criteria in any environment, though see active environments below, which
#   overrides this behavior).
# - active_environments: The environments in which the flag is force-enabled for everyone. This
#   takes effect whether or not the flag is in available_environments. An empty list means no
#   environments are forced-active. However, other flag diversion criteria can still apply and cause
#   the flag to be enbled for certain users/orgs/etc.
class FeatureFlag:
    def __init__(
        self,
        *,
        name: str,
        details: str,
        available_environments: list[Environment] = Environment._member_map_.values(),  # type: ignore[assignment]
        active_environments: list[Environment] = [],
    ):
        self.name = name
        self.details = details
        self.available_environments = available_environments
        self.active_environments = active_environments

    # Creates or updates this flag's state in the database.
    #
    # Returns the prmimary key of the flag.
    def load(self) -> Any:
        # Doing this here ensures that the model is imported after django_setup() is called.
        from deepinsights.users.models.flags import Flag

        # The field used on the Flag model to implement the per-environment behavior,
        # `override_enabled_by_environment`, has the following semantics:
        # - True: enabled regardless of other diversion criteria
        # - False: disabled regardless of other diversion criteriaj
        # - Unknown: defer to other diversion criteria
        # The sematics below are a bit intricate to ensure that None, True and False are handled
        # correctly.
        current_environment = Environment.current()
        always_enabled = True if current_environment in self.active_environments else None
        can_be_enabled = None if current_environment in self.available_environments else False

        flag, created = Flag.objects.update_or_create(
            name=self.name,
            defaults={"override_enabled_by_environment": always_enabled or can_be_enabled, "note": self.details},
        )
        if created:
            flag.superusers = False
            flag.save()
        update_string = "Created" if created else "Updated"
        logging.info(f"{update_string} flag {flag}")
        return flag.pk

    # Whether or not this flag is active for the given user.
    #
    # Note that the underlying flag library has functionality that depends on cookies, HTTP headers
    # and HTTP parameters in requests. This method will not support that functionality, but will
    # support user, group, and organization-based diversions.
    def is_active_for_user(self, user: "AbstractBaseUser", read_only: bool = False) -> bool | None:
        http_request = HttpRequest()
        http_request.user = user  # type: ignore[assignment]

        return flag_is_active(http_request, self.name, read_only=read_only)
