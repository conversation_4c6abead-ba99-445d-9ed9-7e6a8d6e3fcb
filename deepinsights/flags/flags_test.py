import os
from logging import ERROR
from unittest.mock import ANY, MagicMock, patch

import pytest
from django.contrib.auth.models import AnonymousUser, Group
from django.db import transaction
from django.http import HttpRequest
from pytest_django.fixtures import SettingsWrapper

from deepinsights.flags.flag_loader import load_flags
from deepinsights.flags.flagdefs import Flags
from deepinsights.flags.flags import Environment, FeatureFlag
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.flags import Flag
from deepinsights.users.models.user import User


@pytest.fixture(autouse=True)
def deleteAllFlags() -> None:
    Flag.objects.all().delete()


def test_environment_current() -> None:
    os.environ["FLAG_ENVIRONMENT"] = "dev"
    assert Environment.current() == Environment.DEV

    os.environ["FLAG_ENVIRONMENT"] = "demo"
    assert Environment.current() == Environment.DEMO

    os.environ["FLAG_ENVIRONMENT"] = "prod"
    assert Environment.current() == Environment.PROD

    os.environ["FLAG_ENVIRONMENT"] = "unknown"
    assert Environment.current() == Environment.PROD

    os.environ["FLAG_ENVIRONMENT"] = ""
    assert Environment.current() == Environment.PROD

    del os.environ["FLAG_ENVIRONMENT"]
    assert Environment.current() == Environment.PROD


@pytest.mark.django_db
def test_feature_flag_no_environments(monkeypatch: pytest.MonkeyPatch) -> None:
    monkeypatch.setattr(Environment, "current", lambda: Environment.DEV)
    assert Flag.objects.all().count() == 0
    FeatureFlag(name="TestFlag1", details="Details").load()
    flags = Flag.objects.all()
    assert flags.count() == 1
    assert flags[0].everyone is None
    assert flags[0].override_enabled_by_environment is None


@pytest.mark.django_db
@pytest.mark.parametrize(
    "available_environments, override_enabled_by_environment",
    [
        ([], False),
        ([Environment.DEV], None),
        ([Environment.DEMO], False),
        ([Environment.PROD, Environment.DEMO], False),
        ([Environment.DEV, Environment.PROD, Environment.DEMO], None),
        ([Environment.PROD, Environment.DEMO, Environment.DEV], None),
    ],
)
def test_feature_flag_available_environments(
    available_environments: list[Environment], override_enabled_by_environment: bool, monkeypatch: pytest.MonkeyPatch
) -> None:
    monkeypatch.setattr(Environment, "current", lambda: Environment.DEV)
    assert Flag.objects.all().count() == 0
    FeatureFlag(name="TestFlag", details="Details", available_environments=available_environments).load()
    flags = Flag.objects.all()
    assert flags.count() == 1
    assert flags[0].override_enabled_by_environment == override_enabled_by_environment


@pytest.mark.django_db
@pytest.mark.parametrize(
    "active_environments, override_enabled_by_environment",
    [
        ([], None),
        ([Environment.DEV], True),
        ([Environment.DEMO], None),
        ([Environment.PROD, Environment.DEMO], None),
        ([Environment.DEV, Environment.PROD, Environment.DEMO], True),
        ([Environment.PROD, Environment.DEMO, Environment.DEV], True),
    ],
)
def test_feature_flag_active_environments(
    active_environments: list[Environment], override_enabled_by_environment: bool, monkeypatch: pytest.MonkeyPatch
) -> None:
    monkeypatch.setattr(Environment, "current", lambda: Environment.DEV)
    assert Flag.objects.all().count() == 0
    FeatureFlag(name="TestFlag", details="Details", active_environments=active_environments).load()
    flags = Flag.objects.all()
    assert flags.count() == 1
    assert flags[0].override_enabled_by_environment == override_enabled_by_environment


@pytest.mark.django_db
@pytest.mark.parametrize(
    "available_environments, active_environments, override_enabled_by_environment",
    [
        ([], [], False),
        ([Environment.DEV], [], None),
        ([Environment.DEMO], [], False),
        ([], [Environment.DEV], True),
        ([], [Environment.DEMO], False),
        ([], [Environment.PROD, Environment.DEMO], False),
        ([Environment.DEV], [Environment.DEV], True),
        ([Environment.DEV], [Environment.DEMO], None),
        ([Environment.DEMO], [Environment.DEV], True),
        (
            [Environment.DEMO, Environment.PROD],
            [Environment.DEV, Environment.DEMO, Environment.PROD],
            True,
        ),
        (
            [Environment.DEV, Environment.DEMO, Environment.PROD],
            [Environment.DEMO, Environment.PROD],
            None,
        ),
        (
            [Environment.DEV, Environment.DEMO, Environment.PROD],
            [Environment.DEV, Environment.DEMO, Environment.PROD],
            True,
        ),
    ],
)
def test_feature_flag_available_and_active_environments(
    available_environments: list[Environment],
    active_environments: list[Environment],
    override_enabled_by_environment: bool,
    monkeypatch: pytest.MonkeyPatch,
) -> None:
    monkeypatch.setattr(Environment, "current", lambda: Environment.DEV)
    assert Flag.objects.all().count() == 0
    FeatureFlag(
        name="TestFlag",
        details="Details",
        available_environments=available_environments,
        active_environments=active_environments,
    ).load()
    flags = Flag.objects.all()
    assert flags.count() == 1
    assert flags[0].override_enabled_by_environment == override_enabled_by_environment


@pytest.mark.django_db
def test_load_flags_creation_deletion(monkeypatch: pytest.MonkeyPatch) -> None:
    monkeypatch.setattr(Environment, "current", lambda: Environment.DEV)
    test_cases = [
        (FeatureFlag(name="TestFlag1", details="Details", available_environments=[Environment.DEV]), None),
        (FeatureFlag(name="TestFlag2", details="Details", available_environments=[Environment.DEMO]), False),
        (FeatureFlag(name="TestFlag3", details="Details", active_environments=[Environment.DEV]), True),
    ]

    load_flags(list(zip(*test_cases))[0])

    for flag, override_enabled_by_environment in test_cases:
        db_flag = Flag.objects.filter(name=flag.name)
        assert db_flag.exists()
        assert db_flag[0].override_enabled_by_environment == override_enabled_by_environment

    test_cases.pop(0)
    test_cases.append((FeatureFlag(name="TestFlag4", details="Details"), None))

    load_flags(list(zip(*test_cases))[0])

    assert not Flag.objects.filter(name="TestFlag1").exists()
    for flag, override_enabled_by_environment in test_cases:
        db_flag = Flag.objects.filter(name=flag.name)
        assert db_flag.exists()
        assert db_flag[0].override_enabled_by_environment == override_enabled_by_environment


@pytest.mark.django_db
def test_load_flags_environment_updates(monkeypatch: pytest.MonkeyPatch) -> None:
    monkeypatch.setattr(Environment, "current", lambda: Environment.DEV)
    test_cases = [
        (FeatureFlag(name="TestFlag1", details="Details", available_environments=[Environment.DEV]), None),
        (FeatureFlag(name="TestFlag2", details="Details", available_environments=[Environment.DEMO]), False),
        (FeatureFlag(name="TestFlag3", details="Details", active_environments=[Environment.DEV]), True),
        (
            FeatureFlag(
                name="TestFlag4", details="Details", available_environments=[], active_environments=[Environment.PROD]
            ),
            False,
        ),
    ]

    load_flags(list(zip(*test_cases))[0])

    for flag, override_enabled_by_environment in test_cases:
        db_flag = Flag.objects.get(name=flag.name)
        assert db_flag.override_enabled_by_environment == override_enabled_by_environment
        db_flag.percent = 50
        db_flag.save()

    test_cases = [
        (FeatureFlag(name="TestFlag1", details="Details", active_environments=[Environment.DEV]), True),
        (FeatureFlag(name="TestFlag2", details="Details", available_environments=[Environment.DEV]), None),
        (FeatureFlag(name="TestFlag3", details="Details", active_environments=[Environment.PROD]), None),
        (
            FeatureFlag(
                name="TestFlag4", details="Details", available_environments=[], active_environments=[Environment.PROD]
            ),
            False,
        ),
    ]

    load_flags(list(zip(*test_cases))[0])

    for flag, override_enabled_by_environment in test_cases:
        db_flag = Flag.objects.get(name=flag.name)
        assert db_flag.override_enabled_by_environment == override_enabled_by_environment
        assert db_flag.percent == 50


def test_load_flags_superusers_disabled_for_new_flag(monkeypatch: pytest.MonkeyPatch, django_user_model: User) -> None:
    monkeypatch.setattr(Environment, "current", lambda: Environment.DEV)
    test_flags = [FeatureFlag(name="TestFlag1", details="Details", available_environments=[Environment.DEV])]

    load_flags(test_flags)

    flag = Flag.objects.get(name="TestFlag1")
    assert not flag.superusers


def test_load_flags_other_updates_preserved(monkeypatch: pytest.MonkeyPatch, django_user_model: User) -> None:
    monkeypatch.setattr(Environment, "current", lambda: Environment.DEV)
    test_flags = [FeatureFlag(name="TestFlag1", details="Details", available_environments=[Environment.DEV])]

    load_flags(test_flags)

    flag = Flag.objects.get(name="TestFlag1")
    user = django_user_model.objects.create_user(username="test_user")
    flag.users.add(user)
    group = Group.objects.create(name="test_group")
    flag.groups.add(group)
    organization = Organization.objects.create(name="test_organization")
    flag.organizations.add(organization)
    flag.percent = 50
    flag.testing = True
    flag.everyone = True
    flag.superusers = True
    flag.staff = True
    flag.authenticated = True
    flag.languages = ["en", "es"]
    flag.rollout = True
    flag.override_enabled_by_environment = False

    flag.save()

    expected = Flag.objects.get(name="TestFlag1")
    expected.override_enabled_by_environment = None

    load_flags(test_flags)

    actual = Flag.objects.get(name="TestFlag1")
    assert actual.users.contains(user)
    assert actual.groups.contains(group)
    assert actual.organizations.contains(organization)
    for field in Flag._meta.get_fields():
        if field.name in ["modified"]:
            continue
        assert getattr(actual, field.name) == getattr(expected, field.name)


@pytest.mark.django_db
def test_load_flags_exception(monkeypatch: pytest.MonkeyPatch, caplog: pytest.LogCaptureFixture) -> None:
    def raise_exception() -> None:
        raise Exception()

    monkeypatch.setattr(transaction, "atomic", raise_exception)
    test_flags = [
        FeatureFlag(name="TestFlag1", details="Details", available_environments=[Environment.DEV]),
        FeatureFlag(name="TestFlag2", details="Details", available_environments=[Environment.DEMO]),
        FeatureFlag(name="TestFlag3", details="Details", available_environments=[Environment.PROD]),
    ]

    load_flags(test_flags)
    assert len(caplog.records) == 1
    record = caplog.records[0]
    assert "Error loading flags" in record.message
    assert record.levelno == ERROR
    assert Flag.objects.all().count() == 0


@pytest.mark.django_db
@patch("deepinsights.flags.flags.flag_is_active", return_value=None)
def test_feature_flag_is_active_for_user_not_active(flag_is_active: MagicMock) -> None:
    assert not FeatureFlag(name="TestFlag", details="Details").is_active_for_user(None)  # type: ignore[arg-type]
    flag_is_active.assert_called_once_with(ANY, "TestFlag", read_only=False)
    request = flag_is_active.call_args.args[0]
    assert isinstance(request, HttpRequest)
    assert request.user is None


@pytest.mark.django_db
@patch("deepinsights.flags.flags.flag_is_active", return_value=False)
def test_feature_flag_is_active_for_user_not_active_false(flag_is_active: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create()
    assert not FeatureFlag(name="TestFlag", details="Details").is_active_for_user(user, read_only=True)
    flag_is_active.assert_called_once_with(ANY, "TestFlag", read_only=True)
    request = flag_is_active.call_args.args[0]
    assert isinstance(request, HttpRequest)
    assert request.user is user


@pytest.mark.django_db
@patch("deepinsights.flags.flags.flag_is_active", return_value=True)
def test_feature_flag_is_active_for_user_active(flag_is_active: MagicMock) -> None:
    user = AnonymousUser()
    assert FeatureFlag(name="TestFlag", details="Details").is_active_for_user(user)  # type: ignore[arg-type]
    flag_is_active.assert_called_once_with(ANY, "TestFlag", read_only=False)
    request = flag_is_active.call_args.args[0]
    assert isinstance(request, HttpRequest)
    assert request.user is user


@pytest.mark.django_db
@patch("deepinsights.flags.flags.flag_is_active", return_value=True)
def test_flag_enum_is_active_for_user(flag_is_active: MagicMock) -> None:
    user = AnonymousUser()
    assert Flags.EnableClientView.is_active_for_user(user)  # type: ignore[arg-type]
    flag_is_active.assert_called_once_with(ANY, "EnableClientView", read_only=False)
    request = flag_is_active.call_args.args[0]
    assert isinstance(request, HttpRequest)
    assert request.user is user


@pytest.mark.django_db
@patch.object(Environment, "current", lambda: Environment.DEV)
def test_load_flags_details_updates() -> None:
    load_flags(
        [
            FeatureFlag(name="TestFlag1", details="Details", active_environments=[Environment.DEV]),
            FeatureFlag(name="TestFlag2", details="Details"),
            FeatureFlag(name="TestFlag3", details="Details"),
        ]
    )
    db_flags = Flag.objects.all()
    for db_flag in db_flags:
        assert db_flag.note == "Details"

    load_flags(
        [
            FeatureFlag(name="TestFlag1", details="Details new 1"),
            FeatureFlag(
                name="TestFlag2",
                details="Details new 2",
                active_environments=[Environment.DEV],
            ),
            FeatureFlag(name="TestFlag3", details="Details"),
        ]
    )

    # Check that the details and environment settings were updated for the flags that were updated,
    # and that updates to both of these are correctly handled.
    flag_one = Flag.objects.get(name="TestFlag1")
    assert flag_one.note == "Details new 1"
    assert not flag_one.override_enabled_by_environment
    flag_two = Flag.objects.get(name="TestFlag2")
    assert flag_two.note == "Details new 2"
    assert flag_two.override_enabled_by_environment
    flag_three = Flag.objects.get(name="TestFlag3")
    assert flag_three.note == "Details"
    assert not flag_three.override_enabled_by_environment

    new_db_flags = Flag.objects.all()
    assert set([f.pk for f in db_flags]) == set([f.pk for f in new_db_flags])


@pytest.mark.django_db
def test_load_flags_flag_loading_disabled(settings: SettingsWrapper) -> None:
    settings.LOAD_FEATURE_FLAGS = False
    load_flags(
        [
            FeatureFlag(name="TestFlag1", details="Details", active_environments=[Environment.DEV]),
            FeatureFlag(name="TestFlag2", details="Details"),
            FeatureFlag(name="TestFlag3", details="Details"),
        ]
    )
    assert not Flag.objects.all().exists()
