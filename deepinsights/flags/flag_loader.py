import logging
from typing import Any, Iterable

from django.conf import settings
from django.db import transaction

from deepinsights.flags.flagdefs import Flags
from deepinsights.flags.flags import FeatureFlag


# Loads the flags defined in Flags, creating new flags and deleting flags that have been removed.
#
# After calling this, the flags in the database for this app will be in sync with the flags in
# Flags.
def load_flags(flags: Iterable[FeatureFlag] = [f.value for f in Flags]) -> None:  # type: ignore[name-defined]
    # Doing this here ensures that the model is imported after django_setup() is called.
    from deepinsights.users.models.flags import Flag

    if not settings.LOAD_FEATURE_FLAGS:
        logging.info("settings.LOAD_FEATURE_FLAGS is False. Skipping flag loading.")
        return

    try:
        with transaction.atomic():
            # Create and update valid flags.
            valid_flag_keys: list[Any] = []
            for flag in flags:
                valid_flag_keys.append(flag.load())

            # Delete obsolete flags.
            (count, _) = Flag.objects.exclude(pk__in=valid_flag_keys).delete()
            if count > 0:
                logging.info(f"Deleted flags: count {count}")
    except Exception as e:
        logging.error(f"Error loading flags into database: {e}")
