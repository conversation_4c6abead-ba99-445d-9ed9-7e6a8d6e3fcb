{"cells": [{"cell_type": "markdown", "id": "b1e5af87", "metadata": {}, "source": ["This notebook is generating eval reports on a set of ids for summary. This is the prototype of what will be the eval pipeline. refer to go/eval_proto for more details."]}, {"cell_type": "code", "execution_count": null, "id": "f355abf9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "id": "e0b3c2bb-db4a-480a-9f35-be1e8918a986", "metadata": {}, "outputs": [], "source": ["import json\n", "import logging\n", "from datetime import datetime\n", "from pathlib import Path\n", "from typing import Any, Dict, List, Optional\n", "\n", "import pandas as pd\n", "\n", "# Assuming these are your existing imports\n", "from deepinsights.core.ml.genai import call_gpt4o\n", "from deepinsights.core.ml.voice_memo import get_summary  # Assuming get_summary returns (summary_obj, has_content)\n", "from deepinsights.core.ml.voice_memo_utils import parse_response_into_json\n", "from deepinsights.meetingsapp.models.note import Note\n", "\n", "# Configure logging\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format=\"%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s\",\n", "    handlers=[logging.FileHandler(\"summary_generator_detailed_v3.log\", encoding=\"utf-8\"), logging.StreamHandler()],\n", ")\n", "logger = logging.getLogger(__name__)\n", "\n", "\n", "class SummaryGenerator:\n", "    \"\"\"\n", "    Generates summaries, comparison tables, and performs detailed analysis including\n", "    identification of important transcript points, coverage analysis, and inaccuracy checks.\n", "    \"\"\"\n", "\n", "    def __init__(self, output_dir: str = \"summary_generation_output\"):\n", "        self.base_output_dir = Path(output_dir)\n", "        self.base_output_dir.mkdir(exist_ok=True)\n", "\n", "        # Create a unique directory for this specific run\n", "        self.run_timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        self.run_output_dir = self.base_output_dir / self.run_timestamp\n", "        self.run_output_dir.mkdir(exist_ok=True)\n", "        logger.info(f\"All outputs for this run will be saved in: {self.run_output_dir.resolve()}\")\n", "\n", "    def _save_json_to_run_dir(self, data: Any, file_name_stem: str, uuid_suffix: Optional[str] = None) -> Path:\n", "        \"\"\"Saves data as JSON to a file within the run-specific output directory.\"\"\"\n", "        filename = f\"{file_name_stem}{'_' + uuid_suffix if uuid_suffix else ''}.json\"\n", "        output_path = self.run_output_dir / filename\n", "        try:\n", "            with open(output_path, \"w\", encoding=\"utf-8\") as f:\n", "                json.dump(data, f, indent=2)\n", "            logger.info(f\"Successfully saved JSON to: {output_path}\")\n", "            return output_path\n", "        except Exception as e:\n", "            logger.error(f\"Failed to save JSON to {output_path}: {str(e)}\", exc_info=True)\n", "            raise  # Or handle more gracefully depending on desired behavior\n", "\n", "    def _save_raw_llm_response_to_run_dir(self, response_text: str, operation_prefix: str, uuid: str) -> Path:\n", "        \"\"\"Saves raw LLM response text to the run-specific output directory.\"\"\"\n", "        filename = f\"raw_llm_{operation_prefix}_{uuid}.txt\"\n", "        output_path = self.run_output_dir / filename\n", "        try:\n", "            with open(output_path, \"w\", encoding=\"utf-8\") as f:\n", "                f.write(response_text)\n", "            logger.info(f\"Saved raw LLM response to: {output_path}\")\n", "            return output_path\n", "        except Exception as e:\n", "            logger.error(f\"Failed to save raw LLM response to {output_path}: {e}\", exc_info=True)\n", "            # Depending on policy, might not want to raise here, just log\n", "            raise\n", "\n", "    # --- Methods to keep from your existing structure ---\n", "    def process_notes(self, note_uuids: List[str]) -> List[Dict[str, Any]]:\n", "        \"\"\"Process notes and generate summaries. (Kept as is from previous version)\"\"\"\n", "        results = []\n", "        for uuid_str in note_uuids:\n", "            try:\n", "                note = Note.objects.get(uuid=uuid_str)\n", "                transcript = note.diarized_trans_with_names\n", "                old_summary_text = self._get_existing_summary(note)\n", "                new_summary_obj, has_content = get_summary(note, transcript)\n", "\n", "                new_summary_text = \"\"\n", "                if (\n", "                    has_content\n", "                    and new_summary_obj\n", "                    and hasattr(new_summary_obj, \"sections\")\n", "                    and new_summary_obj.sections\n", "                ):\n", "                    summary_parts = []\n", "                    for section in new_summary_obj.sections:\n", "                        section_text = f\"{section.topic}\"\n", "                        for bullet in section.bullets:  # Assuming section.bullets exists\n", "                            section_text += f\"\\n• {bullet}\"\n", "                        summary_parts.append(section_text)\n", "                    new_summary_text = \"\\n\\n\".join(summary_parts)\n", "\n", "                result = {\n", "                    \"uuid\": uuid_str,\n", "                    \"transcript\": transcript,\n", "                    \"old_summary\": old_summary_text,\n", "                    \"new_summary_text\": new_summary_text,\n", "                    \"has_content\": has_content,  # From the new summary generation\n", "                    \"timestamp\": datetime.now().isoformat(),\n", "                }\n", "                results.append(result)\n", "                logger.info(f\"Processed note {uuid_str} for initial summaries.\")\n", "            except Note.DoesNotExist:\n", "                logger.error(f\"Note {uuid_str} not found.\", exc_info=True)\n", "            except Exception as e:\n", "                logger.error(f\"Error processing note {uuid_str} for summaries: {str(e)}\", exc_info=True)\n", "        return results\n", "\n", "    def _get_existing_summary(self, note: Note) -> str:\n", "        \"\"\"Get the existing summary from the note. (Kept as is)\"\"\"\n", "        if not note.summary or not isinstance(note.summary, dict):\n", "            return \"\"\n", "        sections_data = note.summary.get(\"sections\", [])\n", "        if not sections_data:\n", "            return \"\"\n", "        summary_parts = []\n", "        for section in sections_data:\n", "            topic = section.get(\"topic\", \"Untitled Section\")\n", "            bullets = section.get(\"bullets\", [])\n", "            section_text = f\"{topic}\"\n", "            for bullet in bullets:\n", "                section_text += f\"\\n• {bullet}\"\n", "            summary_parts.append(section_text)\n", "        return \"\\n\\n\".join(summary_parts)\n", "\n", "    def create_comparison_table(self, processed_notes_results: List[Dict[str, Any]]) -> Path:\n", "        \"\"\"Create a side-by-side comparison table of old and new summaries. (Kept as is, but saves to run_output_dir)\"\"\"\n", "        # This saves the initial processed_notes_results (transcript, old/new summaries)\n", "        self._save_json_to_run_dir(processed_notes_results, \"initial_processed_notes_data\")\n", "\n", "        comparison_data = []\n", "        for result in processed_notes_results:\n", "            comparison_data.append(\n", "                {\n", "                    \"UUID\": result[\"uuid\"],\n", "                    \"Old Summary\": result[\"old_summary\"],\n", "                    \"New Summary\": result[\"new_summary_text\"] if result[\"has_content\"] else \"No new summary generated\",\n", "                }\n", "            )\n", "        df = pd.DataFrame(comparison_data)\n", "\n", "        html_content = f\"\"\"<html><head><style>{self._get_html_styles(is_comparison=True)}</style></head><body>\n", "        <h1>Summary Comparison Report</h1>\n", "        <p>Generated at: {datetime.now().isoformat()} (Run ID: {self.run_timestamp})</p>\n", "        <p>Total notes processed: {len(processed_notes_results)}</p>\n", "        <table><tr><th class=\"uuid\">UUID</th><th class=\"summary\">Old Summary</th><th class=\"summary\">New Summary</th></tr>\"\"\"\n", "        for _, row in df.iterrows():\n", "            html_content += f\"\"\"<tr><td class=\"uuid\">{row['UUID']}</td>\n", "            <td class=\"summary\"><pre>{row['Old Summary']}</pre></td>\n", "            <td class=\"summary\"><pre>{row['New Summary']}</pre></td></tr>\"\"\"\n", "        html_content += \"</table></body></html>\"\n", "\n", "        output_path = self.run_output_dir / \"comparison_table.html\"\n", "        with open(output_path, \"w\", encoding=\"utf-8\") as f:\n", "            f.write(html_content)\n", "        logger.info(f\"Comparison table saved to: {output_path}\")\n", "        return output_path\n", "\n", "    # --- New Detailed Analysis Workflow ---\n", "\n", "    def _call_llm_for_structured_json_analysis(\n", "        self,\n", "        prompt: str,\n", "        uuid: str,\n", "        analysis_step_name: str,  # e.g., \"transcript_points\", \"coverage\", \"inaccuracy\"\n", "        expected_top_level_key: str,\n", "        max_retries: int = 3,\n", "    ) -> Dict[str, Any]:\n", "        \"\"\"\n", "        Calls LLM, expects a JSON response with a specific top-level key,\n", "        saves raw response and parsed JSON as intermediate results.\n", "        Returns a dictionary: {\"uuid\": uuid, \"data\": <parsed_data_under_key>, \"raw_response_path\": path, \"parsed_json_path\": path}\n", "        If any error occurs, returns empty dict to avoid downstream hallucination.\n", "        \"\"\"\n", "        raw_response_file_path = None\n", "        parsed_json_file_path = None\n", "\n", "        for attempt in range(max_retries):\n", "            try:\n", "                logger.info(\"Calling LLM (attempt %d/%d)\", attempt + 1, max_retries)\n", "                llm_response_text = call_gpt4o(prompt)\n", "                logger.info(f\"LLM response text for note for step {analysis_step_name}: {llm_response_text}\")\n", "                if not llm_response_text:\n", "                    logger.warning(\"LLM returned empty response on attempt %d/%d\", attempt + 1, max_retries)\n", "                    if attempt == max_retries - 1:\n", "                        return {}\n", "                    continue\n", "                logger.info(\"LLM call successful on attempt %d/%d\", attempt + 1, max_retries)\n", "\n", "                try:\n", "                    raw_response_file_path = self._save_raw_llm_response_to_run_dir(\n", "                        llm_response_text, analysis_step_name, uuid\n", "                    )\n", "                except Exception as e:\n", "                    logger.error(\n", "                        \"Could not save raw LLM response for %s, note %s: %s\", analysis_step_name, uuid, str(e)\n", "                    )\n", "                    # Continue, as we still have the response in memory\n", "\n", "                try:\n", "                    parsed_data = parse_response_into_json(llm_response_text)\n", "                    if not isinstance(parsed_data, dict):\n", "                        logger.error(\n", "                            \"Parsed LLM data for %s, note %s, is not a dictionary. Type: %s\",\n", "                            analysis_step_name,\n", "                            uuid,\n", "                            type(parsed_data),\n", "                        )\n", "                        if attempt == max_retries - 1:\n", "                            return {}\n", "                        continue\n", "\n", "                    if expected_top_level_key not in parsed_data:\n", "                        logger.error(\n", "                            \"Expected key '%s' not in LLM JSON response for %s, note %s. Keys: %s\",\n", "                            expected_top_level_key,\n", "                            analysis_step_name,\n", "                            uuid,\n", "                            list(parsed_data.keys()),\n", "                        )\n", "                        if attempt == max_retries - 1:\n", "                            return {}\n", "                        continue\n", "\n", "                    data_under_key = parsed_data[expected_top_level_key]\n", "                    # Save the successfully parsed and validated JSON\n", "                    parsed_json_file_path = self._save_json_to_run_dir(\n", "                        parsed_data, f\"{analysis_step_name}_parsed_llm_output\", uuid\n", "                    )\n", "\n", "                    return {\n", "                        \"uuid\": uuid,\n", "                        \"data\": data_under_key,\n", "                        \"raw_response_path\": raw_response_file_path,\n", "                        \"parsed_json_path\": parsed_json_file_path,\n", "                    }\n", "\n", "                except json.JSONDecodeError as e:\n", "                    logger.error(\n", "                        \"Failed to parse LLM JSON response for %s, note %s (attempt %d/%d): %s. Raw text: %s...\",\n", "                        analysis_step_name,\n", "                        uuid,\n", "                        attempt + 1,\n", "                        max_retries,\n", "                        str(e),\n", "                        llm_response_text[:500],\n", "                        exc_info=True,\n", "                    )\n", "                    if attempt == max_retries - 1:\n", "                        return {}\n", "                    continue\n", "\n", "            except Exception as e:  # Catch any other parsing or logic errors\n", "                logger.error(\n", "                    \"Unexpected error processing LLM response for %s, note %s (attempt %d/%d): %s\",\n", "                    analysis_step_name,\n", "                    uuid,\n", "                    attempt + 1,\n", "                    max_retries,\n", "                    str(e),\n", "                    exc_info=True,\n", "                )\n", "                if attempt == max_retries - 1:\n", "                    return {}\n", "\n", "        return {}\n", "\n", "    def extract_important_points_from_transcripts(self, processed_notes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:\n", "        \"\"\"\n", "        For each note, extracts important points from its transcript using an LLM.\n", "        Saves intermediate results.\n", "        \"\"\"\n", "        all_notes_important_points = []\n", "        logger.info(f\"Starting: Extracting important points from {len(processed_notes)} transcripts.\")\n", "        for note_data in processed_notes:\n", "            uuid = note_data[\"uuid\"]\n", "            transcript = note_data[\"transcript\"]\n", "            if not transcript or not transcript.strip():\n", "                logger.warning(f\"Skipping important points extraction for note {uuid} due to empty transcript.\")\n", "                all_notes_important_points.append(\n", "                    {\n", "                        \"uuid\": uuid,\n", "                        \"data\": {\"error\": \"Empty transcript\"},\n", "                        \"raw_response_path\": None,\n", "                        \"parsed_json_path\": None,\n", "                    }\n", "                )\n", "                continue\n", "\n", "            prompt = f\"\"\"Analyze this meeting transcript and extract ALL important factual statements, decisions, action items, figures, and commitments.\n", "\n", "Be thorough and comprehensive - aim to identify at least 10-15 distinct points.\n", "\n", "Each point must be:\n", "1. A direct, concise quote or close paraphrase from the transcript\n", "2. Self-contained and specific (not vague)\n", "3. Factually accurate to the transcript\n", "\n", "Return a JSON object with key \"important_points\" containing the list of points.\n", "\n", "Transcript:\n", "\\\"\\\"\\\"\n", "{transcript}\n", "\\\"\\\"\\\"\n", "\n", "JSON Output:\n", "\"\"\"\n", "            analysis_result = self._call_llm_for_structured_json_analysis(\n", "                prompt, uuid, \"transcript_points_extraction\", \"important_points\"\n", "            )\n", "            all_notes_important_points.append(analysis_result)\n", "        logger.info(\"Finished: Extracting important points.\")\n", "        return all_notes_important_points\n", "\n", "    def analyze_coverage_of_important_points(\n", "        self, processed_notes: List[Dict[str, Any]], important_points_results: List[Dict[str, Any]]\n", "    ) -> List[Dict[str, Any]]:\n", "        \"\"\"\n", "        For each note, analyzes how well its old and new summaries cover the identified important transcript points.\n", "        Saves intermediate results.\n", "        \"\"\"\n", "        all_notes_coverage_analysis = []\n", "        logger.info(f\"Starting: Coverage analysis for {len(processed_notes)} notes.\")\n", "\n", "        # Create a lookup for important_points by UUID for easier access\n", "        points_lookup = {item[\"uuid\"]: item for item in important_points_results}\n", "\n", "        for note_data in processed_notes:\n", "            uuid = note_data[\"uuid\"]\n", "            transcript = note_data[\"transcript\"]  # For context, though LLM primarily uses points list\n", "            old_summary = note_data[\"old_summary\"]\n", "            new_summary = note_data[\"new_summary_text\"]\n", "\n", "            points_data_for_note = points_lookup.get(uuid)\n", "            if (\n", "                not points_data_for_note\n", "                or isinstance(points_data_for_note.get(\"data\"), dict)\n", "                and \"error\" in points_data_for_note[\"data\"]\n", "            ):\n", "                logger.warning(\n", "                    f\"Skipping coverage analysis for note {uuid} due to missing or errored important points data.\"\n", "                )\n", "                all_notes_coverage_analysis.append(\n", "                    {\n", "                        \"uuid\": uuid,\n", "                        \"data\": {\"error\": \"Prerequisite important points missing or errored\"},\n", "                        \"raw_response_path\": None,\n", "                        \"parsed_json_path\": None,\n", "                    }\n", "                )\n", "                continue\n", "\n", "            important_points_list = points_data_for_note[\"data\"]\n", "            if not isinstance(important_points_list, list) or not important_points_list:\n", "                logger.info(\n", "                    f\"No important points found or data is not a list for note {uuid}. Skipping coverage analysis for this note.\"\n", "                )\n", "                all_notes_coverage_analysis.append(\n", "                    {\n", "                        \"uuid\": uuid,\n", "                        \"data\": [],\n", "                        \"raw_response_path\": None,\n", "                        \"parsed_json_path\": self._save_json_to_run_dir([], \"coverage_analysis_output\", uuid),\n", "                    }\n", "                )  # Save empty result\n", "                continue\n", "\n", "            prompt = f\"\"\"You're evaluating how well two summaries cover important transcript points.\n", "\n", "Important Points from Transcript:\n", "{json.dumps(important_points_list, indent=2)}\n", "\n", "Old Summary:\n", "\\\"\\\"\\\"\n", "{old_summary if old_summary else \"N/A\"}\n", "\\\"\\\"\\\"\n", "\n", "New Summary:\n", "\\\"\\\"\\\"\n", "{new_summary if new_summary else \"N/A\"}\n", "\\\"\\\"\\\"\n", "\n", "For EACH important point, analyze:\n", "1. Is it fully covered in each summary? (2 points)\n", "2. Is it partially covered but missing details? (1 point)\n", "3. Is it completely missing? (0 points)\n", "\n", "Return JSON with key \"coverage_analysis\" containing:\n", "1. Analysis for each point with the following structure:\n", "{{\n", "  \"important_point_from_transcript\": \"The original important point text from the list above.\",\n", "  \"in_old_summary\": <boolean (true if present and accurate, false otherwise)>,\n", "  \"old_summary_evidence\": \"<String: Relevant quote/text from Old Summary if present, or a brief explanation like 'Not found', 'Partially addressed: [text]', or 'Misrepresented: [text]'. Be specific.>\",\n", "  \"old_summary_score\": <int: 0 if missing, 1 if partially covered, 2 if fully covered>,\n", "  \"in_new_summary\": <boolean (true if present and accurate, false otherwise)>,\n", "  \"new_summary_evidence\": \"<String: Relevant quote/text from New Summary if present, or a brief explanation. Be specific.>\",\n", "  \"new_summary_score\": <int: 0 if missing, 1 if partially covered, 2 if fully covered>\n", "}}\n", "2. Include a \"coverage_metrics\" object containing:\n", "  \"total_points\": <int: total number of important points>,\n", "  \"old_summary_coverage_score\": <float: percentage (0-100) of points covered (total score / max possible score * 100)>,\n", "  \"new_summary_coverage_score\": <float: percentage (0-100) of points covered>,\n", "  \"old_summary_fully_covered_count\": <int: number of fully covered points (score=2)>,\n", "  \"old_summary_partially_covered_count\": <int: number of partially covered points (score=1)>,\n", "  \"new_summary_fully_covered_count\": <int: number of fully covered points (score=2)>,\n", "  \"new_summary_partially_covered_count\": <int: number of partially covered points (score=1)>\n", "\n", "Be objective and comprehensive in your evaluation.\n", "\"\"\"\n", "            analysis_result = self._call_llm_for_structured_json_analysis(\n", "                prompt, uuid, \"point_coverage_analysis\", \"coverage_analysis\"\n", "            )\n", "            all_notes_coverage_analysis.append(analysis_result)\n", "        logger.info(\"Finished: Coverage analysis.\")\n", "        return all_notes_coverage_analysis\n", "\n", "    def analyze_inaccuracies_in_summaries(self, processed_notes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:\n", "        \"\"\"\n", "        For each note, identifies inaccuracies in its old and new summaries when compared to the transcript.\n", "        Saves intermediate results.\n", "        \"\"\"\n", "        all_notes_inaccuracy_analysis = []\n", "        logger.info(f\"Starting: Inaccuracy analysis for {len(processed_notes)} notes.\")\n", "        for note_data in processed_notes:\n", "            uuid = note_data[\"uuid\"]\n", "            transcript = note_data[\"transcript\"]\n", "            old_summary = note_data[\"old_summary\"]\n", "            new_summary = note_data[\"new_summary_text\"]\n", "\n", "            if not transcript or not transcript.strip():\n", "                logger.warning(f\"Skipping inaccuracy analysis for note {uuid} due to empty transcript.\")\n", "                all_notes_inaccuracy_analysis.append(\n", "                    {\n", "                        \"uuid\": uuid,\n", "                        \"data\": {\"error\": \"Empty transcript\"},\n", "                        \"raw_response_path\": None,\n", "                        \"parsed_json_path\": None,\n", "                    }\n", "                )\n", "                continue\n", "\n", "            prompt = f\"\"\"Analyze these summaries for factual inaccuracies compared to the transcript.\n", "\n", "Transcript:\n", "\\\"\\\"\\\"\n", "{transcript}\n", "\\\"\\\"\\\"\n", "\n", "Old Summary:\n", "\\\"\\\"\\\"\n", "{old_summary if old_summary else \"N/A\"}\n", "\\\"\\\"\\\"\n", "\n", "New Summary:\n", "\\\"\\\"\\\"\n", "{new_summary if new_summary else \"N/A\"}\n", "\\\"\\\"\\\"\n", "\n", "<PERSON>oughly check EACH statement in both summaries against the transcript.\n", "For ANY inaccurate statement, provide:\n", "1. The exact inaccurate text\n", "2. Which summary it's from\n", "3. The type of inaccuracy (fabrication, misrepresentation, etc.)\n", "4. Why it's inaccurate\n", "5. Evidence from transcript\n", "\n", "Return JSON with:\n", "1. \"inaccuracy_findings\": list of all inaccuracies found with the following structure:\n", "{{\n", "  \"summary_statement\": \"<The inaccurate text/statement directly from the summary.>\",\n", "  \"source_summary\": \"<String: 'Old Summary' or 'New Summary'>\",\n", "  \"type_of_inaccuracy\": \"<String: e.g., 'Factual Error', 'Fabrication', 'Misinterpretation', 'Exaggeration', 'Contradiction', 'Incorrect Attribution'>\",\n", "  \"reason_explanation\": \"<String: A brief explanation of why this statement is considered inaccurate based on the transcript.>\",\n", "  \"transcript_evidence_correction\": \"<String: Relevant quote(s) from the transcript that demonstrate the inaccuracy or provide the correct information. If fabricated, state that the point is not found in transcript.>\"\n", "}}\n", "\n", "2. \"inaccuracy_metrics\": {{\n", "   \"old_summary_inaccuracy_count\": <int: number of inaccuracies in old summary>,\n", "   \"new_summary_inaccuracy_count\": <int: number of inaccuracies in new summary>,\n", "   \"old_summary_inaccuracy_rate\": <float: estimated percentage of statements with inaccuracies>,\n", "   \"new_summary_inaccuracy_rate\": <float: estimated percentage of statements with inaccuracies>,\n", "   \"total_inaccuracies\": <int: total number of inaccuracies found>\n", "}}\n", "\n", "DO NOT limit your analysis to any specific number of findings.\n", "Examine BOTH summaries thoroughly and comprehensively.\n", "\"\"\"\n", "            analysis_result = self._call_llm_for_structured_json_analysis(\n", "                prompt, uuid, \"summary_inaccuracy_analysis\", \"inaccuracy_findings\"\n", "            )\n", "            all_notes_inaccuracy_analysis.append(analysis_result)\n", "        logger.info(\"Finished: Inaccuracy analysis.\")\n", "        return all_notes_inaccuracy_analysis\n", "\n", "    def _get_html_styles(self, is_comparison: bool = False) -> str:\n", "        \"\"\"Returns CSS styles for HTML reports.\"\"\"\n", "        common_styles = \"\"\"\n", "        body { font-family: Arial, sans-serif; margin: 20px; color: #333; line-height: 1.6; }\n", "        h1, h2, h3 { color: #2c3e50; margin-top: 1.5em; margin-bottom: 0.5em; }\n", "        h1 { font-size: 2em; } h2 { font-size: 1.5em; } h3 { font-size: 1.2em; }\n", "        table { border-collapse: collapse; width: 100%; margin: 20px 0; box-shadow: 0 2px 3px rgba(0,0,0,0.1); font-size: 0.9em; }\n", "        th, td { border: 1px solid #ccc; padding: 10px 12px; text-align: left; vertical-align: top; }\n", "        th { background-color: #e9ecef; font-weight: bold; color: #495057; }\n", "        tr:nth-child(even) td { background-color: #f8f9fa; }\n", "        tr:hover td { background-color: #e2e6ea; }\n", "        pre { white-space: pre-wrap; word-wrap: break-word; margin: 0; font-family: <PERSON><PERSON><PERSON>, 'Courier New', monospace; background-color: #f1f1f1; padding: 8px; border-radius: 4px; font-size: 0.95em;}\n", "        .uuid-header { font-family: monospace; font-size: 0.9em; color: #7f8c8d; margin-bottom: 15px; }\n", "        .error-message { color: #d9534f; font-weight: bold; }\n", "        .no-data { color: #777; font-style: italic; }\n", "        .evidence-text { font-style: italic; color: #555; }\n", "        .boolean-true { color: green; font-weight: bold; }\n", "        .boolean-false { color: red; font-weight: bold; }\n", "        caption { caption-side: top; font-weight: bold; font-size: 1.1em; margin-bottom: 10px; text-align: left; color: #333; }\n", "        .metrics-table { width: auto; margin: 20px auto; background-color: #f8f9fa; border: 1px solid #dee2e6; }\n", "        .metrics-table th { background-color: #e2e6ea; text-align: center; }\n", "        .metrics-table td { text-align: center; font-weight: bold; }\n", "        .metrics-card { background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }\n", "        .metrics-title { font-weight: bold; margin-bottom: 10px; color: #495057; }\n", "        .metric-value { font-size: 1.5em; font-weight: bold; color: #343a40; }\n", "        .metric-label { font-size: 0.9em; color: #6c757d; }\n", "        .metric-good { color: #28a745; }\n", "        .metric-warning { color: #ffc107; }\n", "        .metric-bad { color: #dc3545; }\n", "        \"\"\"\n", "        if is_comparison:\n", "            return (\n", "                common_styles\n", "                + \"\"\"\n", "        .uuid { width: 25%; font-family: monospace; }\n", "        .summary { width: 37.5%; }\n", "            \"\"\"\n", "            )\n", "        else:  # Detailed Analysis Report styles\n", "            return common_styles  # Add more specific styles if needed later\n", "\n", "    def create_new_detailed_analysis_report(\n", "        self,\n", "        processed_notes: List[Dict[str, Any]],  # For UUIDs and maybe displaying original summaries\n", "        important_points_results: List[Dict[str, Any]],\n", "        coverage_results: List[Dict[str, Any]],\n", "        inaccuracy_results: List[Dict[str, Any]],\n", "    ) -> Path:\n", "        \"\"\"\n", "        Generates a new detailed HTML report with the two requested analysis tables per note.\n", "        \"\"\"\n", "        logger.info(\"Starting: Generating new detailed analysis HTML report.\")\n", "        points_lookup = {item[\"uuid\"]: item.get(\"data\", []) for item in important_points_results}\n", "        coverage_lookup = {item[\"uuid\"]: item.get(\"data\", []) for item in coverage_results}\n", "        inaccuracy_lookup = {item[\"uuid\"]: item.get(\"data\", []) for item in inaccuracy_results}\n", "\n", "        html_parts = [\n", "            f\"\"\"<html><head><meta charset=\"UTF-8\"><title>Detailed Summary Analysis Report</title>\n", "                        <style>{self._get_html_styles()}</style></head><body>\n", "                        <h1>Detailed Summary Analysis Report</h1>\n", "                        <p>Generated at: {datetime.now().isoformat()} (Run ID: {self.run_timestamp})</p>\n", "                        <p>Total notes analyzed: {len(processed_notes)}</p>\"\"\"\n", "        ]\n", "\n", "        # Add an overall metrics section at the top\n", "        all_metrics = {\n", "            \"total_notes\": len(processed_notes),\n", "            \"notes_with_coverage_data\": 0,\n", "            \"notes_with_inaccuracy_data\": 0,\n", "            \"avg_old_summary_coverage\": 0,\n", "            \"avg_new_summary_coverage\": 0,\n", "            \"avg_old_summary_inaccuracies\": 0,\n", "            \"avg_new_summary_inaccuracies\": 0,\n", "        }\n", "\n", "        valid_coverage_data_count = 0\n", "        valid_inaccuracy_data_count = 0\n", "        total_old_coverage = 0\n", "        total_new_coverage = 0\n", "        total_old_inaccuracies = 0\n", "        total_new_inaccuracies = 0\n", "\n", "        for note_info in processed_notes:\n", "            uuid = note_info[\"uuid\"]\n", "            html_parts.append(f\"<hr><h2>Analysis for Note UUID: {uuid}</h2>\")\n", "\n", "            # Add metrics summary card for this note\n", "            html_parts.append(\"<div class='metrics-card'>\")\n", "            html_parts.append(\"<div class='metrics-title'>Summary Metrics</div>\")\n", "            html_parts.append(\"<table class='metrics-table'>\")\n", "            html_parts.append(\"<tr><th>Metric</th><th>Old Summary</th><th>New Summary</th></tr>\")\n", "\n", "            # Extract and display coverage metrics\n", "            coverage_data = coverage_lookup.get(uuid, {})\n", "            inaccuracy_data = inaccuracy_lookup.get(uuid, {})\n", "\n", "            coverage_metrics = {}\n", "            if isinstance(coverage_data, dict) and \"coverage_metrics\" in coverage_data:\n", "                coverage_metrics = coverage_data.get(\"coverage_metrics\", {})\n", "                valid_coverage_data_count += 1\n", "\n", "                if \"old_summary_coverage_score\" in coverage_metrics:\n", "                    old_coverage = coverage_metrics.get(\"old_summary_coverage_score\", 0)\n", "                    total_old_coverage += old_coverage\n", "                    old_coverage_class = \"metric-good\" if old_coverage >= 70 else \"metric-warning\" if old_coverage >= 40 else \"metric-bad\"\n", "\n", "                if \"new_summary_coverage_score\" in coverage_metrics:\n", "                    new_coverage = coverage_metrics.get(\"new_summary_coverage_score\", 0)\n", "                    total_new_coverage += new_coverage\n", "                    new_coverage_class = \"metric-good\" if new_coverage >= 70 else \"metric-warning\" if new_coverage >= 40 else \"metric-bad\"\n", "\n", "                html_parts.append(f\"<tr><td>Coverage Score</td>\" +\n", "                                 f\"<td class='{old_coverage_class}'>{coverage_metrics.get('old_summary_coverage_score', 'N/A')}%</td>\" +\n", "                                 f\"<td class='{new_coverage_class}'>{coverage_metrics.get('new_summary_coverage_score', 'N/A')}%</td></tr>\")\n", "\n", "                html_parts.append(f\"<tr><td>Fully Covered Points</td>\" +\n", "                                 f\"<td>{coverage_metrics.get('old_summary_fully_covered_count', 'N/A')}/{coverage_metrics.get('total_points', 'N/A')}</td>\" +\n", "                                 f\"<td>{coverage_metrics.get('new_summary_fully_covered_count', 'N/A')}/{coverage_metrics.get('total_points', 'N/A')}</td></tr>\")\n", "\n", "                html_parts.append(f\"<tr><td>Partially Covered Points</td>\" +\n", "                                 f\"<td>{coverage_metrics.get('old_summary_partially_covered_count', 'N/A')}</td>\" +\n", "                                 f\"<td>{coverage_metrics.get('new_summary_partially_covered_count', 'N/A')}</td></tr>\")\n", "\n", "            # Extract and display inaccuracy metrics\n", "            inaccuracy_metrics = {}\n", "            if isinstance(inaccuracy_data, dict) and \"inaccuracy_metrics\" in inaccuracy_data:\n", "                inaccuracy_metrics = inaccuracy_data.get(\"inaccuracy_metrics\", {})\n", "                valid_inaccuracy_data_count += 1\n", "\n", "                if \"old_summary_inaccuracy_count\" in inaccuracy_metrics:\n", "                    old_inaccuracies = inaccuracy_metrics.get(\"old_summary_inaccuracy_count\", 0)\n", "                    total_old_inaccuracies += old_inaccuracies\n", "                    old_inaccuracy_class = \"metric-good\" if old_inaccuracies <= 2 else \"metric-warning\" if old_inaccuracies <= 5 else \"metric-bad\"\n", "\n", "                if \"new_summary_inaccuracy_count\" in inaccuracy_metrics:\n", "                    new_inaccuracies = inaccuracy_metrics.get(\"new_summary_inaccuracy_count\", 0)\n", "                    total_new_inaccuracies += new_inaccuracies\n", "                    new_inaccuracy_class = \"metric-good\" if new_inaccuracies <= 2 else \"metric-warning\" if new_inaccuracies <= 5 else \"metric-bad\"\n", "\n", "                html_parts.append(f\"<tr><td>Inaccuracy Count</td>\" +\n", "                                 f\"<td class='{old_inaccuracy_class}'>{inaccuracy_metrics.get('old_summary_inaccuracy_count', 'N/A')}</td>\" +\n", "                                 f\"<td class='{new_inaccuracy_class}'>{inaccuracy_metrics.get('new_summary_inaccuracy_count', 'N/A')}</td></tr>\")\n", "\n", "                html_parts.append(f\"<tr><td>Inaccuracy Rate</td>\" +\n", "                                 f\"<td>{inaccuracy_metrics.get('old_summary_inaccuracy_rate', 'N/A')}%</td>\" +\n", "                                 f\"<td>{inaccuracy_metrics.get('new_summary_inaccuracy_rate', 'N/A')}%</td></tr>\")\n", "\n", "            html_parts.append(\"</table></div>\")\n", "\n", "            # Optional: display original summaries here if needed\n", "            # html_parts.append(f\"<h3>Old Summary:</h3><pre>{note_info.get('old_summary', 'N/A')}</pre>\")\n", "            # html_parts.append(f\"<h3>New Summary:</h3><pre>{note_info.get('new_summary_text', 'N/A')}</pre>\")\n", "\n", "            # --- Table 1: Coverage of Important Transcript Points ---\n", "            html_parts.append(\"<h3>Table 1: Coverage of Important Transcript Points</h3>\")\n", "            note_coverage_data = coverage_lookup.get(uuid, [])\n", "            note_important_points_list = points_lookup.get(uuid, [])  # This should be the list of strings\n", "\n", "            if isinstance(note_coverage_data, dict) and \"error\" in note_coverage_data:\n", "                html_parts.append(\n", "                    f\"<p class='error-message'>Error loading coverage data: {note_coverage_data['error']}</p>\"\n", "                )\n", "            elif (\n", "                not note_coverage_data\n", "                and isinstance(note_important_points_list, list)\n", "                and not note_important_points_list\n", "            ):  # No important points found for this UUID\n", "                html_parts.append(\n", "                    \"<p class='no-data'>No important points were identified from the transcript for this note, so coverage analysis was not performed.</p>\"\n", "                )\n", "            elif not note_coverage_data and not (\n", "                isinstance(note_important_points_list, list) and not note_important_points_list\n", "            ):  # Points identified, but coverage errored\n", "                html_parts.append(\n", "                    \"<p class='error-message'>Coverage analysis data is unexpectedly empty or missing for identified points.</p>\"\n", "                )\n", "            elif not note_coverage_data:  # Default for empty, non-error cases.\n", "                html_parts.append(\"<p class='no-data'>No coverage data available for this note.</p>\")\n", "            elif \"coverage_metrics\" in note_coverage_data:  # The new format with metrics\n", "                coverage_items = [item for item in note_coverage_data if isinstance(item, dict) and \"important_point_from_transcript\" in item]\n", "\n", "                html_parts.append(\n", "                    \"<table><caption>Coverage of Important Points from Transcript</caption><thead><tr>\"\n", "                    \"<th>Important Point from Transcript</th>\"\n", "                    \"<th>Old Summary Score</th><th>Evidence (Old Summary)</th>\"\n", "                    \"<th>New Summary Score</th><th>Evidence (New Summary)</th>\"\n", "                    \"</tr></thead><tbody>\"\n", "                )\n", "                for item in coverage_items:\n", "                    if not isinstance(item, dict):  # LLM might return a list of strings if structure is wrong\n", "                        html_parts.append(\n", "                            f\"<tr><td colspan='5' class='error-message'>Malformed coverage item: {str(item)[:100]}</td></tr>\"\n", "                        )\n", "                        continue\n", "\n", "                    old_score = item.get('old_summary_score', 0)\n", "                    new_score = item.get('new_summary_score', 0)\n", "                    old_score_class = \"metric-good\" if old_score == 2 else \"metric-warning\" if old_score == 1 else \"metric-bad\"\n", "                    new_score_class = \"metric-good\" if new_score == 2 else \"metric-warning\" if new_score == 1 else \"metric-bad\"\n", "\n", "                    html_parts.append(f\"\"\"<tr>\n", "                        <td><pre>{item.get('important_point_from_transcript', 'N/A')}</pre></td>\n", "                        <td class='{old_score_class}'>{old_score}/2</td>\n", "                        <td><pre class='evidence-text'>{item.get('old_summary_evidence', 'N/A')}</pre></td>\n", "                        <td class='{new_score_class}'>{new_score}/2</td>\n", "                        <td><pre class='evidence-text'>{item.get('new_summary_evidence', 'N/A')}</pre></td>\n", "                    </tr>\"\"\")\n", "                html_parts.append(\"</tbody></table>\")\n", "            else:  # The old format without metrics - fall back to boolean indicators\n", "                html_parts.append(\n", "                    \"<table><caption>Coverage of Important Points from Transcript</caption><thead><tr>\"\n", "                    \"<th>Important Point from Transcript</th>\"\n", "                    \"<th>Present in Old Summary?</th><th>Evidence (Old Summary)</th>\"\n", "                    \"<th>Present in New Summary?</th><th>Evidence (New Summary)</th>\"\n", "                    \"</tr></thead><tbody>\"\n", "                )\n", "                for item in note_coverage_data:\n", "                    if not isinstance(item, dict):  # LLM might return a list of strings if structure is wrong\n", "                        html_parts.append(\n", "                            f\"<tr><td colspan='5' class='error-message'>Malformed coverage item: {str(item)[:100]}</td></tr>\"\n", "                        )\n", "                        continue\n", "                    html_parts.append(f\"\"\"<tr>\n", "                        <td><pre>{item.get('important_point_from_transcript', 'N/A')}</pre></td>\n", "                        <td class='boolean-{'true' if item.get('in_old_summary') else 'false'}'>{str(item.get('in_old_summary', 'Error'))}</td>\n", "                        <td><pre class='evidence-text'>{item.get('old_summary_evidence', 'N/A')}</pre></td>\n", "                        <td class='boolean-{'true' if item.get('in_new_summary') else 'false'}'>{str(item.get('in_new_summary', 'Error'))}</td>\n", "                        <td><pre class='evidence-text'>{item.get('new_summary_evidence', 'N/A')}</pre></td>\n", "                    </tr>\"\"\")\n", "                html_parts.append(\"</tbody></table>\")\n", "\n", "            # --- Table 2: Inaccuracy Check of Summaries ---\n", "            html_parts.append(\"<h3>Table 2: Inaccuracy Check of Summaries</h3>\")\n", "            note_inaccuracy_data = inaccuracy_lookup.get(uuid, [])\n", "\n", "            if isinstance(note_inaccuracy_data, dict) and \"error\" in note_inaccuracy_data:\n", "                html_parts.append(\n", "                    f\"<p class='error-message'>Error loading inaccuracy data: {note_inaccuracy_data['error']}</p>\"\n", "                )\n", "            elif \"inaccuracy_metrics\" in note_inaccuracy_data:  # The new format with metrics\n", "                inaccuracy_items = [item for item in note_inaccuracy_data if isinstance(item, dict) and \"summary_statement\" in item]\n", "\n", "                if not inaccuracy_items:  # Could be an empty list if no inaccuracies found, which is fine\n", "                    html_parts.append(\n", "                        \"<p class='no-data'>No inaccuracies identified in the summaries for this note.</p>\"\n", "                    )\n", "                else:  # We have inaccuracy data\n", "                    html_parts.append(\n", "                        \"<table><caption>Inaccuracies Identified in Summaries</caption><thead><tr>\"\n", "                        \"<th>Summary Statement (from Old or New)</th><th>Source Summary</th>\"\n", "                        \"<th>Type of Inaccuracy</th><th>Reason/Explanation</th>\"\n", "                        \"<th>Transcript Evidence / Correction</th>\"\n", "                        \"</tr></thead><tbody>\"\n", "                    )\n", "                    for item in inaccuracy_items:\n", "                        if not isinstance(item, dict):\n", "                            html_parts.append(\n", "                                f\"<tr><td colspan='5' class='error-message'>Malformed inaccuracy item: {str(item)[:100]}</td></tr>\"\n", "                            )\n", "                            continue\n", "                        html_parts.append(f\"\"\"<tr>\n", "                            <td><pre>{item.get('summary_statement', 'N/A')}</pre></td>\n", "                            <td>{item.get('source_summary', 'N/A')}</td>\n", "                            <td>{item.get('type_of_inaccuracy', 'N/A')}</td>\n", "                            <td><pre>{item.get('reason_explanation', 'N/A')}</pre></td>\n", "                            <td><pre>{item.get('transcript_evidence_correction', 'N/A')}</pre></td>\n", "                        </tr>\"\"\")\n", "                    html_parts.append(\"</tbody></table>\")\n", "            elif not note_inaccuracy_data:  # Could be an empty list if no inaccuracies found, which is fine\n", "                html_parts.append(\n", "                    \"<p class='no-data'>No inaccuracies identified in the summaries for this note, or data is unavailable.</p>\"\n", "                )\n", "            else:  # The old format without metrics\n", "                html_parts.append(\n", "                    \"<table><caption>Inaccuracies Identified in Summaries</caption><thead><tr>\"\n", "                    \"<th>Summary Statement (from Old or New)</th><th>Source Summary</th>\"\n", "                    \"<th>Type of Inaccuracy</th><th>Reason/Explanation</th>\"\n", "                    \"<th>Transcript Evidence / Correction</th>\"\n", "                    \"</tr></thead><tbody>\"\n", "                )\n", "                for item in note_inaccuracy_data:\n", "                    if not isinstance(item, dict):\n", "                        html_parts.append(\n", "                            f\"<tr><td colspan='5' class='error-message'>Malformed inaccuracy item: {str(item)[:100]}</td></tr>\"\n", "                        )\n", "                        continue\n", "                    html_parts.append(f\"\"\"<tr>\n", "                        <td><pre>{item.get('summary_statement', 'N/A')}</pre></td>\n", "                        <td>{item.get('source_summary', 'N/A')}</td>\n", "                        <td>{item.get('type_of_inaccuracy', 'N/A')}</td>\n", "                        <td><pre>{item.get('reason_explanation', 'N/A')}</pre></td>\n", "                        <td><pre>{item.get('transcript_evidence_correction', 'N/A')}</pre></td>\n", "                    </tr>\"\"\")\n", "                html_parts.append(\"</tbody></table>\")\n", "\n", "        # Calculate overall metrics\n", "        if valid_coverage_data_count > 0:\n", "            all_metrics[\"notes_with_coverage_data\"] = valid_coverage_data_count\n", "            all_metrics[\"avg_old_summary_coverage\"] = round(total_old_coverage / valid_coverage_data_count, 2)\n", "            all_metrics[\"avg_new_summary_coverage\"] = round(total_new_coverage / valid_coverage_data_count, 2)\n", "\n", "        if valid_inaccuracy_data_count > 0:\n", "            all_metrics[\"notes_with_inaccuracy_data\"] = valid_inaccuracy_data_count\n", "            all_metrics[\"avg_old_summary_inaccuracies\"] = round(total_old_inaccuracies / valid_inaccuracy_data_count, 2)\n", "            all_metrics[\"avg_new_summary_inaccuracies\"] = round(total_new_inaccuracies / valid_inaccuracy_data_count, 2)\n", "\n", "        html_parts.append(\"</body></html>\")\n", "        final_html = \"\".join(html_parts)\n", "\n", "        report_path = self.run_output_dir / \"detailed_analysis_report.html\"\n", "        with open(report_path, \"w\", encoding=\"utf-8\") as f:\n", "            f.write(final_html)\n", "        logger.info(f\"New detailed analysis report saved to: {report_path}\")\n", "        return report_path\n", "\n", "\n", "# --- Example of how you might call this in your Django shell script ---\n", "def run_full_summary_generation_and_detailed_analysis(note_uuids_to_process: List[str]):\n", "    logger.info(\n", "        f\"SCRIPT STARTED: Full summary generation and detailed analysis for {len(note_uuids_to_process)} notes.\"\n", "    )\n", "    generator = SummaryGenerator(output_dir=\"final_summary_evaluations_v3\")  # Root output dir\n", "\n", "    if not note_uuids_to_process:\n", "        logger.warning(\"No note UUIDs provided. Exiting script.\")\n", "        return\n", "\n", "    # Step 1: Process notes (get transcript, old summary, generate new summary)\n", "    # This is an existing method, its output (processed_notes) is crucial.\n", "    processed_notes = generator.process_notes(note_uuids_to_process)\n", "    if not processed_notes:\n", "        logger.error(\"Initial note processing failed to produce results. Aborting further analysis.\")\n", "        return\n", "    logger.info(f\"Step 1/5 COMPLETED: Initial processing of {len(processed_notes)} notes.\")\n", "\n", "    # Step 2: Create the original comparison table (Old vs New Summary)\n", "    # This method also saves the 'processed_notes' data to a JSON file.\n", "    generator.create_comparison_table(processed_notes)\n", "    logger.info(\"Step 2/5 COMPLETED: Comparison table of old/new summaries generated.\")\n", "\n", "    # --- New Detailed Analysis Steps ---\n", "    # Step 3: Extract Important Points from Transcripts\n", "    important_points_data = generator.extract_important_points_from_transcripts(processed_notes)\n", "    logger.info(\n", "        f\"Step 3/5 COMPLETED: Important points extraction from transcripts. Results for {len(important_points_data)} notes.\"\n", "    )\n", "\n", "    # Step 4: Analyze Coverage of these Important Points in Old/New Summaries\n", "    # This step depends on 'processed_notes' (for summaries) and 'important_points_data'\n", "    coverage_analysis_data = generator.analyze_coverage_of_important_points(processed_notes, important_points_data)\n", "    logger.info(f\"Step 4/5 COMPLETED: Coverage analysis. Results for {len(coverage_analysis_data)} notes.\")\n", "\n", "    # Step 5: Analyze Inaccuracies in Old/New Summaries against Transcripts\n", "    # This step depends on 'processed_notes' (for summaries and transcript)\n", "    inaccuracy_analysis_data = generator.analyze_inaccuracies_in_summaries(processed_notes)\n", "    logger.info(f\"Step 5/5 COMPLETED: Inaccuracy analysis. Results for {len(inaccuracy_analysis_data)} notes.\")\n", "\n", "    # Step 6: Generate the New Detailed HTML Analysis Report\n", "    # This report will use data from all previous analysis steps.\n", "    generator.create_new_detailed_analysis_report(\n", "        processed_notes,  # Pass this for context if needed in report, like displaying original summaries\n", "        important_points_data,\n", "        coverage_analysis_data,\n", "        inaccuracy_analysis_data,\n", "    )\n", "    logger.info(\"Step 6/6 COMPLETED: New detailed analysis report generated.\")\n", "    logger.info(\n", "        f\"SCRIPT FINISHED. All outputs are in subdirector_ies of {generator.base_output_dir.resolve()} under run ID {generator.run_timestamp}\"\n", "    )\n", "\n", "\n", "# To use in Django shell:\n", "# from your_app.your_script_file import run_full_summary_generation_and_detailed_analysis\n", "# note_ids_list = [\"your-uuid-1\", \"your-uuid-2\", ...] # Populate with actual UUIDs\n", "# run_full_summary_generation_and_detailed_analysis(note_ids_list)"]}, {"cell_type": "code", "execution_count": null, "id": "297ce4ec-2d66-4a90-bc2f-217e44bbbd1c", "metadata": {}, "outputs": [], "source": ["  # Import necessary libraries\n", "  from generate_summaries import SummaryGenerator, run_full_summary_generation_and_detailed_analysis\n", "\n", "  # Define the UUIDs to process\n", "  note_uuids_to_process = [\n", "      \"fc4f0f41-d29c-4369-9ddf-92fb427be0a9\",\n", "      \"1d77cf32-db08-4c61-81f4-902a901d7e9d\",\n", "      \"01333c8f-bc2a-48ad-b9ec-17f3f7208049\",\n", "      \"987354b4-3d34-4a05-b03a-b177f94db12c\"\n", "  ]\n", "\n", "  # Run the full analysis pipeline\n", "  run_full_summary_generation_and_detailed_analysis(note_uuids_to_process)"]}], "metadata": {"kernelspec": {"display_name": "Django Shell-Plus", "language": "python", "name": "django_extensions"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}