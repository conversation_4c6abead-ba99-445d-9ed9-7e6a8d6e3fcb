import json
from typing import Callable
from unittest.mock import MagicMock
from uuid import uuid4

import pytest
from django.core import cache
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, status
from fastapi.security import HTTPAuthorizationCredentials
from oauth2_provider.models import AbstractApplication
from rest_framework_simplejwt.tokens import RefreshToken

from api.dependencies import user_from_access_token, user_from_authorization_header
from api.fastapi_with_adjusted_schema import EXTRA_SUPPORTS_INTERNAL_AUTH, EXTRA_SUPPORTS_OAUTH_AUTH
from api.oauth_server import client_credentials_server
from deepinsights.meetingsapp.models.oauth_server_models import OAuthApplication
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.user import User


@pytest.fixture(autouse=True)
def clear_cache() -> None:
    cache.cache.clear()


class TestPublicAppAuthentication:
    @pytest.fixture()
    def user(self, django_user_model: User) -> User:
        return django_user_model.objects.create(username="test")

    @pytest.fixture()
    def token_for_valid_app(self, user: User) -> str:
        OAuthApplication.objects.create(
            client_id="test",
            client_secret="test",
            authorization_grant_type=AbstractApplication.GRANT_CLIENT_CREDENTIALS,
            user=user,
        )

        _, body, _ = client_credentials_server.create_token_response(
            "http://example.com/test",
            body={
                "grant_type": "client_credentials",
                "client_id": "test",
                "client_secret": "test",
            },
        )
        return json.loads(body).get("access_token", "")  # type: ignore[no-any-return]

    @pytest.fixture
    def public_app_request(self) -> MagicMock:
        return MagicMock(spec=Request, app=MagicMock(spec=FastAPI, extra={EXTRA_SUPPORTS_OAUTH_AUTH: True}))

    @pytest.mark.parametrize("token", [None, "", "123"])
    def test_user_from_access_token_invalid_tokens(self, token: str, public_app_request: MagicMock) -> None:
        public_app_request.method = "POST"
        public_app_request.headers = {}
        public_app_request.url = f"http://example.com/test?api_key={token}"

        with pytest.raises(HTTPException) as e:
            assert not user_from_access_token(public_app_request, token)
        assert e.value.status_code == status.HTTP_401_UNAUTHORIZED

    def test_user_from_access_token_valid_token(
        self, user: User, token_for_valid_app: str, public_app_request: MagicMock
    ) -> None:
        public_app_request.method = "POST"
        public_app_request.headers = {}
        public_app_request.url = f"http://example.com/test?api_key={token_for_valid_app}"

        # The public API does not support API key authorization.
        with pytest.raises(HTTPException) as e:
            assert not user_from_access_token(public_app_request, token_for_valid_app)
        assert e.value.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.parametrize("token", ["", "123"])
    def test_user_from_authorization_header_invalid_tokens(self, token: str, public_app_request: MagicMock) -> None:
        public_app_request.method = "POST"
        public_app_request.headers = {
            "Authorization": f"Bearer {token}",
        }
        public_app_request.url = "http://example.com/test"

        with pytest.raises(HTTPException) as e:
            assert not user_from_authorization_header(
                public_app_request,
                HTTPAuthorizationCredentials(scheme="Bearer", credentials=token),
            )
        assert e.value.status_code == status.HTTP_401_UNAUTHORIZED

    def test_user_from_authorization_header_invalid_scheme(
        self, token_for_valid_app: str, public_app_request: MagicMock
    ) -> None:
        public_app_request.method = "POST"
        public_app_request.headers = {
            "Authorization": f"NotValid {token_for_valid_app}",
        }
        public_app_request.url = "http://example.com/test"
        with pytest.raises(HTTPException) as e:
            assert not user_from_authorization_header(
                public_app_request,
                HTTPAuthorizationCredentials(scheme="NotValid", credentials=token_for_valid_app),
            )
        assert e.value.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.parametrize("method", ["GET", "POST", "PUT", "DELETE"])
    def test_user_from_authorization_header_valid_token(
        self, user: User, token_for_valid_app: str, public_app_request: MagicMock, method: str
    ) -> None:
        public_app_request.method = method
        public_app_request.headers = {
            "Authorization": f"Bearer {token_for_valid_app}",
        }
        public_app_request.query_params = {}
        public_app_request.url = "http://example.com/test"
        assert (
            user_from_authorization_header(
                public_app_request,
                HTTPAuthorizationCredentials(scheme="Bearer", credentials=token_for_valid_app),
            )
            == user
        )

    def test_acting_user_query_param_non_superuser(
        self, token_for_valid_app: str, public_app_request: MagicMock
    ) -> None:
        public_app_request.method = "GET"
        public_app_request.query_params = {"acting_user_uuid": str(uuid4())}
        public_app_request.headers = {
            "Authorization": f"Bearer {token_for_valid_app}",
        }
        public_app_request.url = "http://example.com/test"

        with pytest.raises(HTTPException) as e:
            assert not user_from_access_token(public_app_request, token_for_valid_app)
        assert e.value.status_code == status.HTTP_401_UNAUTHORIZED

    def test_acting_user_query_param_different_org(
        self, token_for_valid_app: str, public_app_request: MagicMock, django_user_model: User, user: User
    ) -> None:
        org = Organization.objects.create(name="Test Org")
        other_org = Organization.objects.create(name="Other Org")
        user.is_superuser = True
        user.organization = org
        user.save()
        other_user = django_user_model.objects.create(username="other_org_user", organization=other_org)

        public_app_request.method = "GET"
        public_app_request.headers = {
            "Authorization": f"Bearer {token_for_valid_app}",
        }
        public_app_request.query_params = {"acting_user_uuid": str(other_user.uuid)}

        with pytest.raises(HTTPException) as e:
            assert not user_from_access_token(public_app_request, token_for_valid_app)
        assert e.value.status_code == status.HTTP_404_NOT_FOUND


class TestInternalAppAuthentication:
    @pytest.fixture
    def internal_app_request(self) -> Request:
        return MagicMock(spec=Request, app=MagicMock(spec=FastAPI, extra={EXTRA_SUPPORTS_INTERNAL_AUTH: True}))

    @pytest.mark.parametrize("token", [None, "", "123"])
    def test_user_from_access_token_invalid_tokens(self, token: str, internal_app_request: Request) -> None:
        with pytest.raises(HTTPException) as e:
            assert not user_from_access_token(internal_app_request, token)
        assert e.value.status_code == status.HTTP_401_UNAUTHORIZED

    def test_user_from_access_token_valid_token(self, django_user_model: User, internal_app_request: Request) -> None:
        user = django_user_model.objects.create(username="test")
        token = str(RefreshToken.for_user(user).access_token)
        assert user_from_access_token(internal_app_request, token) == user

    @pytest.mark.parametrize(
        "authorize",
        [
            lambda request, token: user_from_access_token(request, token),
            lambda request, token: user_from_authorization_header(
                request,
                HTTPAuthorizationCredentials(scheme="Bearer", credentials=token),
            ),
        ],
    )
    def test_public_token(
        self, django_user_model: User, internal_app_request: Request, authorize: Callable[[Request, str], User | None]
    ) -> None:
        user = django_user_model.objects.create(username="test")
        OAuthApplication.objects.create(
            client_id="test",
            client_secret="test",
            authorization_grant_type=AbstractApplication.GRANT_CLIENT_CREDENTIALS,
            user=user,
        )

        _, body, _ = client_credentials_server.create_token_response(
            "http://example.com/test",
            body={
                "grant_type": "client_credentials",
                "client_id": "test",
                "client_secret": "test",
            },
        )
        token = json.loads(body).get("access_token")

        with pytest.raises(HTTPException) as e:
            assert not authorize(internal_app_request, token)
        assert e.value.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.parametrize("token", ["", "123"])
    def test_user_from_authorization_header_invalid_tokens(self, token: str, internal_app_request: Request) -> None:
        with pytest.raises(HTTPException) as e:
            assert not user_from_authorization_header(
                internal_app_request,
                HTTPAuthorizationCredentials(scheme="Bearer", credentials=token),
            )
        assert e.value.status_code == status.HTTP_401_UNAUTHORIZED

    def test_user_from_authorization_header_invalid_scheme(
        self, django_user_model: User, internal_app_request: Request
    ) -> None:
        user = django_user_model.objects.create(username="test")
        token = str(RefreshToken.for_user(user).access_token)
        with pytest.raises(HTTPException) as e:
            assert not user_from_authorization_header(
                internal_app_request,
                HTTPAuthorizationCredentials(scheme="NotValid", credentials=token),
            )
        assert e.value.status_code == status.HTTP_401_UNAUTHORIZED

    def test_user_from_authorization_header_valid_token(
        self, django_user_model: User, internal_app_request: Request
    ) -> None:
        user = django_user_model.objects.create(username="test")
        token = str(RefreshToken.for_user(user).access_token)
        assert (
            user_from_authorization_header(
                internal_app_request,
                HTTPAuthorizationCredentials(scheme="Bearer", credentials=token),
            )
            == user
        )
