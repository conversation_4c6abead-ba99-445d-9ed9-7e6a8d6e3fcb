import pytest
from fastapi.testclient import Test<PERSON>lient
from oauth2_provider.models import AbstractApplication

from api.public_api import public_api
from deepinsights.meetingsapp.models.oauth_server_models import OAuthApplication
from deepinsights.users.models.user import User

client = TestClient(public_api)

pytestmark = [pytest.mark.django_db(transaction=True)]


@pytest.fixture
def oauth_application(django_user_model: User) -> OAuthApplication:
    user = django_user_model.objects.create()
    return OAuthApplication.objects.create(
        client_id="test_client_id",
        client_secret="test_client_secret",
        authorization_grant_type=AbstractApplication.GRANT_CLIENT_CREDENTIALS,
        user=user,
    )


@pytest.mark.parametrize(
    "request_json",
    [
        {"grant_type": "client_credentials"},
        {"client_id": "test_client_id"},
        {"client_secret": "test_client_secret"},
        {"grant_type": "client_credentials", "client_id": "test_client_id"},
        {"grant_type": "client_credentials", "client_secret": "test_client_secret"},
        {"client_id": "test_client_id", "client_secret": "test_client_secret"},
    ],
)
def test_get_oauth_token_invalid_data(request_json: dict[str, str]) -> None:
    response = client.post(
        "/api/public/v1/oauth/token",
        data=request_json,
    )
    assert response.status_code == 422


@pytest.mark.parametrize(
    "request_json, status, error",
    [
        (
            {
                "grant_type": "invalid",
                "client_id": "test_client_id",
                "client_secret": "test_client_secret",
            },
            400,
            "unsupported_grant_type",
        ),
        (
            {
                "grant_type": "password",
                "client_id": "test_client_id",
                "client_secret": "test_client_secret",
            },
            400,
            "unsupported_grant_type",
        ),
        (
            {
                "grant_type": "client_credentials",
                "client_id": "invalid",
                "client_secret": "test_client_secret",
            },
            401,
            "invalid_client",
        ),
        (
            {
                "grant_type": "client_credentials",
                "client_id": "test_client_id",
                "client_secret": "invalid",
            },
            401,
            "invalid_client",
        ),
    ],
)
def test_get_oauth_token_not_authorized(
    oauth_application: OAuthApplication, request_json: dict[str, str], status: int, error: str
) -> None:
    response = client.post("/api/public/v1/oauth/token", data=request_json)
    assert response.status_code == status
    assert response.json() == {"error": error}


def test_get_oauth_token_authorized_without_attached_user(oauth_application: OAuthApplication) -> None:
    oauth_application.user = None
    oauth_application.save()

    response = client.post(
        "/api/public/v1/oauth/token",
        data={
            "grant_type": "client_credentials",
            "client_id": "test_client_id",
            "client_secret": "test_client_secret",
        },
    )

    assert response.status_code == 200
    data = response.json()
    token = data.get("access_token")
    assert token is not None
    data.pop("access_token")
    assert data == {
        "token_type": "Bearer",
        "expires_in": 3600,
        "scope": "read write",
    }

    health_response = client.get("/api/public/v1/health", headers={"Authorization": f"Bearer {token}"})
    assert health_response.status_code == 401


def test_get_oauth_token_authorized(oauth_application: OAuthApplication) -> None:
    response = client.post(
        "/api/public/v1/oauth/token",
        data={
            "grant_type": "client_credentials",
            "client_id": "test_client_id",
            "client_secret": "test_client_secret",
        },
    )

    assert response.status_code == 200
    data = response.json()
    token = data.get("access_token")
    assert token is not None
    data.pop("access_token")
    assert data == {
        "token_type": "Bearer",
        "expires_in": 3600,
        "scope": "read write",
    }

    # Ensure that the token is usable.
    health_response = client.get("/api/public/v1/health", headers={"Authorization": f"Bearer {token}"})
    assert health_response.status_code == 204
