import logging
import os
from typing import Annotated, Any

from django.conf import settings
from fastapi import Depends, Form, Request, Response
from pydantic import BaseModel, Field

from api.dependencies import (
    acting_user_query_param,
    user_from_authorization_header,
)
from api.fastapi_with_adjusted_schema import EXTRA_SUPPORTS_OAUTH_AUTH, FastAPIWithRootPathAdjustedSchema
from api.oauth_server import client_credentials_server
from api.routers.calendar import public_router as calendar_router
from api.routers.client import public_router as client_router
from api.routers.crm import public_router as crm_router
from api.routers.meeting_artifacts import public_router as meeting_artifacts_router
from api.routers.note import public_router as note_router
from app.base_paths import PUBLIC_API_PATH
from deepinsights.users.models.user import User

_auth_kwargs: dict[str, Any] = {EXTRA_SUPPORTS_OAUTH_AUTH: True}

__description = """
## Getting started
This API requires a configured OAuth2 application to access, and only supports the
`client_credentials` OAuth2 flow. [Reach out to our support team](mailto:<EMAIL>) to get
an OAuth2 application set up for your organization, and to get your client ID and secret.

## Using this page
This page allows you to explore the public API endpoints. You can interact with the APIs and view
their responses directly on this page. You'll need to follow the steps below to get an access token
before you can use the APIs. Once you have an access token, click the "Authorize" button in the top
right below these instructions and above the API descriptions, and enter the token in the dialog
that appears. This will allow you to make authenticated requests to the API. You can then click on
the API endpoints to expand them and see their details, and click the "Try it out" button to make
requests to the API.

## Getting an access token
Once you have a client ID and secret, you can use the `/oauth/token` endpoint to get an
access token. The API does not yet support any scopes; tokens have access to all
endpoints.

For example, assuming you have a client ID `id` and a client secret `secret`, you can use the
following `curl` command to get an access token:

```bash
curl -X 'POST' \\
  'https://staging.app.zeplyn.ai/api/public/v1/oauth/token' \\
  -H 'accept: application/json' \\
  -H 'Content-Type: application/x-www-form-urlencoded' \\
  -d 'grant_type=client_credentials&client_id=id&client_secret=secret'
```

## Authenticating
Once you have an access token, you can use it to authenticate requests to the API by
including it in the `Authorization` header as a Bearer token:

```
Authorization: Bearer <token>
```

For example, replacing `your_token_here` with the token you received in the previous curl command,
you can use the following `curl` command to hit the health check endpoint (which will return a 204
and so not print anything to the console):

```bash
curl -X 'GET' \\
  'https://staging.app.zeplyn.ai/api/public/v1/health' \\
  -H 'accept: */*' \\
  -H 'Authorization: Bearer your_token_here'
```

### Acting as a user

APIs that allows you to access data on behalf of a user will also support an optional
`acting_user_uuid` query parameter. This allows you to perform actions on behalf of a
specific user, rather than as the robot user associated with the app-level integration.
The API documentation and generated OpenAPI schema will indicate which APIs support this
parameter.

For example, you can use the following `curl` command to get notes for user
"7a309eb0-8c0d-4cc1-9494-80dc69a63ef1":

```bash
curl -X 'GET' \\
  'https://staging.app.zeplyn.ai/api/public/v1/notes/?acting_user_uuid=7a309eb0-8c0d-4cc1-9494-80dc69a63ef1' \\
  -H 'accept: application/json' \\
  -H 'Authorization: Bearer your_token_here'
```

## Encryption
Data is encrypted in transit to the proxy fronting this API server using TLS. There is no
specific encryption applied to the data in transit other than that provided by the transport.
"""


# The FastAPI ASGI application for the public API (Zeplyn's third-party API).
public_api = FastAPIWithRootPathAdjustedSchema(
    debug=settings.DEBUG,
    # According to the FastAPI docs, the Swagger endpoint is only generated if the openapi_url is
    # not None.
    openapi_url="/openapi.json" if os.environ.get("OPENAPI_INTERNAL_SCHEMA_ENDPOINTS_ENABLED") else None,
    root_path=PUBLIC_API_PATH,
    root_path_in_servers=False,
    title="Zeplyn API",
    description=__description,
    version="1.0.0",
    servers=[
        {
            "url": settings.APP_DOMAIN,
            "description": "Zeplyn hosted API",
        },
    ]
    + [
        {
            "url": "http://localhost:8000",
            "description": "Local development server",
        },
    ]
    if settings.DEBUG
    else [],
    **_auth_kwargs,
)


class OAuthTokenRequest(BaseModel):
    grant_type: str = Field(
        ..., description="The type of grant being requested. Currently only `client_credentials` is supported."
    )
    client_id: str = Field(
        ...,
        description=(
            "The client ID of the OAuth2 Application. Get this from the "
            "[admin console](/api/admin/oauth2_provider/application/)."
        ),
    )
    client_secret: str = Field(
        ...,
        description=(
            "The client secret of the OAuth2 Application. Get this from the "
            "[admin console](/api/admin/oauth2_provider/application/)."
        ),
    )


class OAuthTokenResponse(BaseModel):
    access_token: str = Field(..., description="The access token.")
    token_type: str = Field(..., description="The type of token. Currently only `Bearer` is supported.")
    expires_in: int = Field(..., description="The number of seconds until the token expires.")
    scope: str = Field(..., description="The scope of the token. Currently unused.")


class OAuthTokenErrorResponse(BaseModel):
    error: str = Field(..., description="The error.")


@public_api.post(
    "/oauth/token",
    tags=["auth"],
    summary="Get an OAuth2 access token",
    description="Acquire an access token for an OAuth2 authorization flow.",
    response_model=OAuthTokenResponse,
    responses={
        200: {"description": "Success"},
        400: {"description": "Bad request", "model": OAuthTokenErrorResponse},
        401: {"description": "Unauthorized", "model": OAuthTokenErrorResponse},
        500: {"description": "Internal server error"},
    },
)
def get_oauth_token(request: Request, token_request: Annotated[OAuthTokenRequest, Form()]) -> Response:  # noqa: F821
    url = request.url
    method = request.method

    # There is a type stubs library for oauthlib, but its typing is more restrictive than the actual
    # types allowed by the library, so we don't use it.
    headers, body, status = client_credentials_server.create_token_response(
        str(url), method, token_request.model_dump(), request.headers
    )

    return Response(content=body, status_code=status, headers=headers)


@public_api.get("/health", status_code=204)
def health_check(user: User = Depends(user_from_authorization_header)) -> None:
    logging.info("user: %s", user)


public_api.include_router(client_router, prefix="/clients", dependencies=[Depends(acting_user_query_param)])
public_api.include_router(crm_router, prefix="/crm", dependencies=[Depends(acting_user_query_param)])
public_api.include_router(calendar_router, prefix="/events", dependencies=[Depends(acting_user_query_param)])
public_api.include_router(
    meeting_artifacts_router, prefix="/meeting_artifacts", dependencies=[Depends(acting_user_query_param)]
)
public_api.include_router(note_router, prefix="/notes", dependencies=[Depends(acting_user_query_param)])
