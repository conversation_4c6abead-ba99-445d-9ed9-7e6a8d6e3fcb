from unittest.mock import MagicMock, patch

from api.fastapi_with_adjusted_schema import FastAPIWithRootPathAdjustedSchema


@patch("app.fastapi.FastAPI.openapi")
def test_openapi_schema_adjustment_with_root_path(mock_openapi: MagicMock) -> None:
    spec = {
        "paths": {
            "/example": {"get": {"summary": "Example endpoint"}},
            "/test": {"post": {"summary": "Test endpoint"}},
        },
        "openapi": "3.1.0",
        "info": {
            "title": "Test API",
            "version": "1.0.0",
        },
        "servers": [
            {
                "url": "http://localhost",
                "description": "Internal server",
            },
        ],
    }
    mock_openapi.return_value = spec

    result = FastAPIWithRootPathAdjustedSchema(root_path="/api/internal").openapi()

    assert result["paths"] == {
        "/api/internal/example": {"get": {"summary": "Example endpoint"}},
        "/api/internal/test": {"post": {"summary": "Test endpoint"}},
    }
    # Assert that the rest of the object is unchanged.
    assert {**result, "paths": []} == {**spec, "paths": []}


@patch("app.fastapi.FastAPI.openapi")
def test_openapi_schema_adjustment_without_root_path(mock_openapi: MagicMock) -> None:
    spec = {
        "paths": {
            "/example": {"get": {"summary": "Example endpoint"}},
            "/test": {"post": {"summary": "Test endpoint"}},
        },
        "openapi": "3.1.0",
        "info": {
            "title": "Test API",
            "version": "1.0.0",
        },
        "servers": [
            {
                "url": "http://localhost",
                "description": "Internal server",
            },
        ],
    }
    mock_openapi.return_value = spec

    assert FastAPIWithRootPathAdjustedSchema().openapi() == spec
