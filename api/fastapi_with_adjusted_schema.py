from typing import Any

from fastapi import FastAP<PERSON>

# A key in the FastAPI.extra dictionary that indicates whether the API supports OAuth authentication.
#
# This is used to determine whether the API should handle OAuth tokens created by the OAuth (external)
# authentication system or treat them as unauthenticated.  To use this, pass it as a keyword argument
# to the FastAPI constructor, like this:
#
# ```python
# _auth_kwargs: dict[str, Any] = {EXTRA_SUPPORTS_OAUTH_AUTH: True}
# FastAPI(
#     ...,
#     _auth_kwargs,
# )
# ```
#
# To read it, use the `app.extra` dictionary in a FastAPI app:
#
# ```python
# supports_oauth = app.extra.get(EXTRA_SUPPORTS_OAUTH_AUTH)
# ```
EXTRA_SUPPORTS_OAUTH_AUTH = "supports_oauth_auth"

# A key in the FastAPI.extra dictionary that indicates whether the API supports internal
# authentication.
#
# This is used to determine whether the API should handle internal authentication tokens created by
# the internal authentication system or treat them as unauthenticated.  To use this, pass it as a
# keyword argument to the FastAPI constructor, like this:
#
# ```python
# _auth_kwargs: dict[str, Any] = {EXTRA_SUPPORTS_INTERNAL_AUTH: True}
# FastAPI(
#     ...,
#     _auth_kwargs,
# )
# ```
# To read it, use the `app.extra` dictionary in a FastAPI dependency:
#
# ```python
# supports_internal_auth = app.extra.get(EXTRA_SUPPORTS_INTERNAL_AUTH)
# ```
EXTRA_SUPPORTS_INTERNAL_AUTH = "supports_internal_auth"


class FastAPIWithRootPathAdjustedSchema(FastAPI):
    """
    A subclass of FastAPI that adjusts the OpenAPI schema to include the root path of the FastAPI
    instance.

    It's expected that FastAPI instances that use this will also not include the root path in the
    servers list, since it will be invalid: the root path will be prefixed to all of the paths,
    so including it in the servers list would cause schema readers to double up the root path
    when calling the APIs.
    """

    def openapi(self) -> dict[str, Any]:
        if self.openapi_schema:
            return self.openapi_schema

        openapi_schema = super().openapi()
        paths = openapi_schema.get("paths", {})
        keys = list(paths.keys())
        for k in keys:
            v = paths.pop(k)
            paths[f"{self.root_path}{k}"] = v
        openapi_schema["paths"] = paths
        self.openapi_schema = openapi_schema
        return self.openapi_schema
