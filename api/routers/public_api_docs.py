from fastapi import APIRouter, Depends, HTTPException
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.responses import HTMLResponse

from api.dependencies import user_from_authorization_header
from api.public_api import public_api
from deepinsights.users.models.user import User

router = APIRouter(
    tags=["public_apis"],
    generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}",
)


@router.get(
    "/docs",
    response_class=HTMLResponse,
    description="Returns a Swagger UI HTML document that can be used to explore the public API.",
)
def swagger_document(user: User = Depends(user_from_authorization_header)) -> HTMLResponse:
    if not user.groups.filter(name="public_api_docs_access").exists():
        raise HTTPException(status_code=403)
    return get_swagger_ui_html(
        # According to the Swagger UI docs, if there is a spec then the openapi_url is not used.
        openapi_url="_unused_",
        title=public_api.title,
        swagger_ui_parameters={"spec": public_api.openapi()},
        swagger_js_url="/api/static/js/swagger-ui-bundle.js",
        swagger_css_url="/api/static/css/swagger-ui.css",
        swagger_favicon_url="/favicon.ico",
    )
