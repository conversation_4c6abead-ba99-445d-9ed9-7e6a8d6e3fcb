import logging

from fastapi import APIRouter, HTTPException, status

from api.routers.onboarding_models import (
    OnboardingRequest,
    OnboardingResponse,
    process_organization_creation,
    process_users_creation,
    send_welcome_emails,
)

router = APIRouter(tags=["onboarding"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}")
logger = logging.getLogger(__name__)


async def process_onboarding(request_data: OnboardingRequest) -> OnboardingResponse:
    """Process the entire onboarding workflow."""
    response = OnboardingResponse(success=False)

    try:
        # Step 1: Create or update organization and preferences
        organization = await process_organization_creation(request_data.organization)
        response.organization_id = str(organization.id)

        # Step 2: Create or update users
        users_processed, users_to_email, user_errors = await process_users_creation(organization, request_data.users)
        response.users_processed = users_processed
        if user_errors:
            response.errors.extend(user_errors)

        # Step 3: Send welcome emails
        batch_emails_sent, email_errors = await send_welcome_emails(users_to_email)
        response.welcome_emails_sent = batch_emails_sent
        if email_errors:
            response.errors.extend(email_errors)

        # Mark as successful if we got this far
        response.success = True

    except HTTPException as e:
        response.errors.append(e.detail)
    except Exception as e:
        error_msg = f"Unexpected error during onboarding: {str(e)}"
        logger.error(error_msg, exc_info=e)
        response.errors.append(error_msg)

    return response


@router.post(
    "/onboard",
    response_model=OnboardingResponse,
    summary="Create or update an organization with users in a single API call",
    include_in_schema=False,
)
async def onboard_organization(request: OnboardingRequest) -> OnboardingResponse:
    """
    Create a new organization with users or update an existing one in a single API call.

    This endpoint is designed for integration with external systems like Hubspot.
    It performs the full onboarding process that's otherwise done through the admin wizard.

    The API will automatically:
    1. Check if the organization already exists by id and either creates or updates it
       along with the bot preferences(if provided)
    2. Process users (create new ones or update existing ones)
    3. Send welcome emails to designated users

    For explicit organization updates:
    - Set "existing" to true in the organization object
    - Include the organization "id" field

    For user updates:
    - Set "existing" to true in each user object you want to update

    Returns a response with:
    - success status
    - organization ID
    - count of users processed
    - count of welcome emails sent
    - any errors encountered
    """

    response = await process_onboarding(request)

    if not response.success and not response.organization_id:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Organization processing failed")

    return response
