import logging
from typing import Literal

from fastapi import APIRouter, Depends, HTTPException, Response, status
from pydantic import BaseModel, HttpUrl

from api.dependencies import user_from_authorization_header
from deepinsights.core.integrations.calendar.auto_join import update_recall_auto_join_integration
from deepinsights.core.integrations.meetingbot.recall_ai import <PERSON><PERSON>l<PERSON>otController
from deepinsights.core.integrations.oauth.advisor_engine import AdvisorEngineOAuth
from deepinsights.core.integrations.oauth.google import GoogleOAuth
from deepinsights.core.integrations.oauth.microsoft import MicrosoftOAuth
from deepinsights.core.integrations.oauth.microsoft_dynamics import MicrosoftDynamicsOAuth
from deepinsights.core.integrations.oauth.salesforce import SalesforceOAuth
from deepinsights.core.integrations.oauth.wealthbox import WealthBoxOAuth
from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.meetingsapp.tasks import reconcile_calendar_events, sync_crm_clients
from deepinsights.users.models.user import User

logger = logging.getLogger(__name__)

router = APIRouter(tags=["oauth"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}")


class OAuthRequest(BaseModel):
    authorization_code: str
    request_url: HttpUrl
    provider: Literal["wealthbox", "microsoft", "google", "salesforce", "microsoft_dynamics", "advisor_engine"]
    code_verifier: str | None = None  # Required for PKCE flows


def _set_user_crm_system(user: User, system: str) -> None:
    crm_config = user.get_crm_configuration()
    crm_config.crm_system = system
    user.crm_configuration = crm_config.to_dict()
    user.save()


@router.post(
    "/configure",
    responses={
        200: {"description": "OAuth integration setup successfully"},
        400: {"description": "Bad request - missing or invalid parameters"},
        401: {"description": "Unauthorized - invalid user"},
        500: {"description": "Internal server error during setup"},
    },
    summary="Set up OAuth integration",
    description="Set up OAuth integration for various providers (Wealthbox, Microsoft, Google, Salesforce)",
)
def set_up_oauth_integration(
    oauth_data: OAuthRequest,
    user: User = Depends(user_from_authorization_header),
) -> Response:
    try:
        if oauth_data.provider == "wealthbox":
            WealthBoxOAuth().exchange_tokens(oauth_data.authorization_code, user, str(oauth_data.request_url))
            _set_user_crm_system(user, "wealthbox")
            sync_crm_clients.delay_on_commit(user.uuid)

        elif oauth_data.provider == "microsoft":
            has_previous_credentials = OAuthCredentials.objects.filter(user=user, integration="microsoft").exists()

            MicrosoftOAuth().exchange_tokens(
                authorization_code=oauth_data.authorization_code, user=user, redirect_uri=str(oauth_data.request_url)
            )

            reconcile_calendar_events.delay_on_commit(user.uuid)

            if not has_previous_credentials:
                update_recall_auto_join_integration(
                    user=user,
                    enabled=True,
                    oauth=MicrosoftOAuth(),
                    calendar_link_function=RecallBotController.link_microsoft_calendar,
                )

        elif oauth_data.provider == "google":
            has_previous_credentials = OAuthCredentials.objects.filter(user=user, integration="google").exists()

            GoogleOAuth().exchange_tokens(
                authorization_code=oauth_data.authorization_code, user=user, redirect_uri=str(oauth_data.request_url)
            )

            reconcile_calendar_events.delay_on_commit(user.uuid)

            if not has_previous_credentials:
                update_recall_auto_join_integration(
                    user=user,
                    enabled=True,
                    oauth=GoogleOAuth(),
                    calendar_link_function=RecallBotController.link_google_calendar,
                )

        elif oauth_data.provider == "salesforce":
            SalesforceOAuth().exchange_tokens(
                authorization_code=oauth_data.authorization_code, user=user, redirect_uri=str(oauth_data.request_url)
            )
            _set_user_crm_system(user, "salesforce")

        elif oauth_data.provider == "microsoft_dynamics":
            resource_url = user.get_crm_configuration().dynamics.dynamics_resource_url
            MicrosoftDynamicsOAuth(resource_url).exchange_tokens(
                oauth_data.authorization_code, user, str(oauth_data.request_url)
            )
            _set_user_crm_system(user, "microsoft_dynamics")

        elif oauth_data.provider == "advisor_engine":
            if oauth_data.code_verifier is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="code_verifier is required for advisor_engine provider",
                )
            AdvisorEngineOAuth().exchange_tokens(
                authorization_code=oauth_data.authorization_code,
                code_verifier=oauth_data.code_verifier,
                user=user,
                redirect_uri=str(oauth_data.request_url),
            )
            _set_user_crm_system(user, "advisor_engine")

        return Response(status_code=status.HTTP_201_CREATED)
    except Exception as e:
        logger.error("Error setting up OAuth integration credentials ", exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to set up OAuth integration"
        )


@router.delete(
    "/delete",
    responses={
        204: {"description": "OAuth integration deleted successfully"},
        401: {"description": "Unauthorized - invalid user"},
        404: {"description": "OAuth credentials not found"},
        500: {"description": "Internal server error during deletion"},
    },
    summary="Delete OAuth integration",
    description="Delete stored OAuth integration credentials for a given provider.",
)
def delete_oauth_integration(
    provider: Literal["wealthbox", "salesforce", "microsoft_dynamics", "advisor_engine"],
    user: User = Depends(user_from_authorization_header),
) -> Response:
    try:
        credentials = OAuthCredentials.objects.filter(user=user, integration=provider).first()
        if not credentials:
            raise HTTPException(status_code=404, detail="OAuth credentials not found")

        credentials.delete()

        current_crm_system = user.crm_configuration.get("crm_system")
        # If the deleted integration is the active CRM system, reset it to None
        if current_crm_system == provider:
            user.crm_configuration["crm_system"] = None
        # Remove the deleted CRM's configuration from crm_configuration
        if provider in user.crm_configuration:
            user.crm_configuration.pop(provider)
        user.save()

        return Response(status_code=status.HTTP_204_NO_CONTENT)

    except HTTPException:
        raise
    except Exception as e:
        sanitized_provider = str(provider).replace("\n", "").replace("\r", "")
        logger.error("Error deleting OAuth integration for provider %s", sanitized_provider, exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to delete OAuth integration"
        )
