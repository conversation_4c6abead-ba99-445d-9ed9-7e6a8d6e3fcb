import csv
import io
import logging
import zipfile
from datetime import date, datetime
from decimal import Decimal
from enum import StrEnum
from typing import Any, Literal, Sequence
from uuid import UUID

from asgiref.sync import sync_to_async
from django.core.cache import cache
from django.db import models
from django.db.models.functions import Cast, Coalesce
from fastapi import APIRouter, Depends, HTTPException, Response, status
from pydantic import BaseModel, ValidationError

from api.dependencies import user_from_authorization_header
from api.routers.note_models import ProcessingStatus
from deepinsights.core.integrations.calendar.calendar_models import EventParticipant, EventParticipantsList
from deepinsights.core.ml.scripts.user_metrics import get_meeting_type
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.client import Client as DBClient
from deepinsights.meetingsapp.models.client_recap import ClientRecap, ClientRecapStatus
from deepinsights.meetingsapp.models.meeting_bot import MeetingBot
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingData
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.subscription_plan import EntitlementType
from deepinsights.users.models.user import User as DBUser

router = APIRouter(tags=["insights"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}")


class Tag(BaseModel):
    class Source(StrEnum):
        client = "client"
        advisor = "advisor"
        unknown = "unknown"

    name: str
    source: Source


class NoteInsightData(BaseModel):
    meeting_uuid: UUID
    authorized_user_uuids: list[UUID]
    source: Literal["Unknown", "Mic", "Notetaker", "Phone call"]
    duration_seconds: int | None
    status: ProcessingStatus
    client_uuids: list[UUID]
    meeting_prep_generated: bool
    follow_up_generated: bool
    scheduled_event_uuid: UUID | None
    start_time: datetime
    tags: list[Tag]


class ScheduledEventInsightData(BaseModel):
    scheduled_event_uuid: UUID
    user_uuid: UUID
    has_clients: bool


class TaskInsightData(BaseModel):
    task_uuid: UUID
    assignee_uuid: UUID | None
    creation_date: datetime
    completed: bool


class StructuredMeetingDataInsightData(BaseModel):
    meeting_data_uuid: UUID
    note_uuid: UUID | None
    title: str | None
    kind: str
    items_total: int
    items_completed: int


class User(BaseModel):
    uuid: UUID
    email: str
    name: str | None
    manager_uuid: UUID | None = None


class Client(BaseModel):
    uuid: UUID
    name: str
    life_phase: DBClient.LifePhase | None = None
    assets_under_management: Decimal | None = None
    segment: DBClient.Segment | None = None
    onboarding_date: date | None = None
    is_priority: bool | None = None
    has_client_recap: bool


class InsightsDashboardResponse(BaseModel):
    class Status(StrEnum):
        pending = "pending"
        success = "success"

    status: Status
    notes: list[NoteInsightData]
    scheduled_events: list[ScheduledEventInsightData]
    tasks: list[TaskInsightData]
    structured_meeting_data: list[StructuredMeetingDataInsightData]
    users: list[User]
    clients: list[Client]


def _insights_data_cache_key(user: DBUser) -> str:
    return f"insights_dashboard_data_{user.uuid}"


@router.get(
    "/data",
    response_model=InsightsDashboardResponse,
    summary="Get insights dashboard data",
    description="Returns aggregated data for the insights dashboard including notes, scheduled events, tasks, and structured meeting data",
    responses={
        403: {"description": "Not authorized"},
        500: {"description": "Internal server error"},
    },
)
async def get_insights_data(user: DBUser = Depends(user_from_authorization_header)) -> InsightsDashboardResponse:
    try:
        return await _insights_data(user)
    except Exception as e:
        logging.error("Error retrieving insights dashboard data", exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving insights dashboard data: {str(e)}",
        )


@router.get(
    "/raw_data",
    summary="Get raw insights data",
    description="Returns raw insights data in a ZIP file containing CSV files for notes, scheduled events, tasks, and structured meeting data",
    responses={
        403: {"description": "Not authorized"},
        500: {"description": "Internal server error"},
    },
)
async def get_raw_insights_data(user: DBUser = Depends(user_from_authorization_header)) -> Response:
    try:
        insights_data = await _insights_data(user)

        def write_data(
            zip_file: zipfile.ZipFile, file_name: str, model: type[BaseModel], items: Sequence[BaseModel]
        ) -> None:
            csv_data = io.StringIO()
            csv_writer = csv.DictWriter(csv_data, fieldnames=model.model_fields.keys())
            csv_writer.writeheader()
            [csv_writer.writerow(item.model_dump(mode="json")) for item in items]
            zip_file.writestr(file_name, csv_data.getvalue())

        zip_data = io.BytesIO()
        with zipfile.ZipFile(zip_data, mode="w", compression=zipfile.ZIP_DEFLATED, compresslevel=5) as zip_file:
            write_data(zip_file, "notes.csv", NoteInsightData, insights_data.notes)
            write_data(zip_file, "scheduled_events.csv", ScheduledEventInsightData, insights_data.scheduled_events)
            write_data(zip_file, "tasks.csv", TaskInsightData, insights_data.tasks)
            write_data(
                zip_file,
                "structured_meeting_data.csv",
                StructuredMeetingDataInsightData,
                insights_data.structured_meeting_data,
            )
            write_data(zip_file, "users.csv", User, insights_data.users)
            write_data(zip_file, "clients.csv", Client, insights_data.clients)

        return Response(
            content=zip_data.getbuffer(),
            media_type="application/zip",
            headers={"Content-Disposition": "attachment; filename=insights_data.zip"},
        )
    except Exception as e:
        logging.error("Error retrieving insights dashboard raw data", exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving insights dashboard raw data: {str(e)}",
        )


async def _insights_data(user: DBUser) -> InsightsDashboardResponse:
    if not await sync_to_async(Flags.EnableInsightsAPI.is_active_for_user)(user):
        return InsightsDashboardResponse(
            status=InsightsDashboardResponse.Status.pending,
            notes=[],
            scheduled_events=[],
            tasks=[],
            structured_meeting_data=[],
            users=[],
            clients=[],
        )
    if cached_response := cache.get(_insights_data_cache_key(user)):
        try:
            return InsightsDashboardResponse.model_validate(cached_response)
        except ValidationError:
            logging.error("Cached insights dashboard data is invalid, regenerating")
            cache.delete(_insights_data_cache_key(user))

    # Put a pending response in the cache to prevent multiple requests from hitting the database
    pending_response = InsightsDashboardResponse(
        status=InsightsDashboardResponse.Status.pending,
        notes=[],
        scheduled_events=[],
        tasks=[],
        structured_meeting_data=[],
        users=[],
        clients=[],
    )
    cache.set(_insights_data_cache_key(user), pending_response.model_dump(mode="json"), timeout=60)

    user_is_admin = EntitlementType.organization_admin in user.entitlements and bool(user.organization)

    notes_data = await _get_notes_data(user, user_is_admin)
    scheduled_events_data = await _get_scheduled_events_data(user, user_is_admin)
    tasks_data = await _get_tasks_data(user, user_is_admin)
    structured_data_items = await _get_structured_data_data(user, user_is_admin)

    users = (
        DBUser.objects.filter(organization=user.organization)
        .select_related("manager")
        .annotate(manager_uuid=models.F("manager__uuid"))
        .values("uuid", "name", "email", "manager_uuid")
        .order_by("name")
        if user_is_admin
        else [User(uuid=user.uuid, name=user.name, email=user.email)]
    )
    clients = (
        DBClient.objects.filter(organization=user.organization, authorized_users__isnull=False)
        .annotate(
            has_client_recap=models.Exists(
                ClientRecap.objects.filter(client_id=models.OuterRef("id"), status=ClientRecapStatus.PROCESSED)
            )
        )
        .values(
            "uuid",
            "name",
            "assets_under_management",
            "life_phase",
            "segment",
            "onboarding_date",
            "is_priority",
            "has_client_recap",
        )
        .order_by("-created")
        .distinct()
    )
    if user_is_admin:
        clients = clients.filter(authorized_users__organization=user.organization)
    else:
        clients = clients.filter(authorized_users=user)

    response = InsightsDashboardResponse(
        status=InsightsDashboardResponse.Status.success,
        notes=notes_data,
        scheduled_events=scheduled_events_data,
        tasks=tasks_data,
        structured_meeting_data=structured_data_items,
        users=users if isinstance(users, list) else [User.model_validate(u) async for u in users],
        clients=[Client.model_validate(c) async for c in clients],
    )

    cache.set(_insights_data_cache_key(user), response.model_dump(mode="json"), timeout=60 * 60)

    return response


async def _get_notes_data(user: DBUser, org_level_data: bool) -> list[NoteInsightData]:
    if org_level_data:
        notes_queryset = Note.objects.filter(note_owner__organization=user.organization)
    else:
        notes_queryset = Note.objects.filter(models.Q(authorized_users=user))

    notes_queryset = (
        notes_queryset.prefetch_related(
            models.Prefetch("authorized_users", queryset=DBUser.objects.only("uuid").order_by("id")),
            models.Prefetch(
                "attendees", queryset=Attendee.objects.select_related("client").only("note_id", "uuid", "client__uuid")
            ),
            models.Prefetch("_scheduled_event", queryset=ScheduledEvent.objects.only("note_id", "uuid", "start_time")),
            models.Prefetch("meetingbot_set", queryset=MeetingBot.objects.only("note_id", "uuid", "meeting_link")),
        )
        .select_related(
            "clientinteraction",
            "clientinteraction__agenda",
            "clientinteraction__advisor_notes",
        )
        .annotate(
            has_follow_up_email_contents=models.Q(follow_up_email_contents__isnull=False),
            metadata_scheduled_at=Cast(models.F("metadata__scheduled_at"), output_field=models.TextField()),
            start_time=Coalesce(
                models.F("_scheduled_event__start_time"),
                Cast(models.F("metadata_scheduled_at"), models.DateTimeField()),
                models.F("created"),
                output_field=models.DateTimeField(),
            ),
            has_prep_contents=(
                models.Q(clientinteraction__agenda__data__isnull=False)
                & models.Q(clientinteraction__advisor_notes__data__isnull=False)
            ),
        )
        .only(
            "uuid",
            "client",
            "metadata",
            "data_source",
            "status",
            "metadata",
            "created",
            "clientinteraction__created",
            "clientinteraction__agenda__data",
            "clientinteraction__advisor_notes__data",
            "_scheduled_event__uuid",
        )
        .distinct()
        .order_by("created")
    )

    notes_data = []
    async for note in notes_queryset:
        authorized_user_uuids = [u.uuid for u in note.authorized_users.all()]
        client_uuids = [attendee.client.uuid for attendee in note.attendees.all() if attendee.client]
        if note.client and (uuid := note.client.get("uuid")):
            try:
                UUID(uuid)  # Validate UUID format
                client_uuids.append(uuid)
            except ValueError:
                logging.error("Invalid client UUID in note %s: %s. Ignoring", note.uuid, uuid)

        metadata = note.metadata or {}
        duration = None
        if "meeting_duration" in metadata:
            try:
                duration = int(metadata["meeting_duration"])
            except (ValueError, TypeError):
                logging.error(
                    "Invalid meeting duration in note %s: %s. Ignoring", note.uuid, metadata["meeting_duration"]
                )

        tags: list[Tag] = []
        if "tags" in metadata:
            tag_strings: list[str] = metadata["tags"]
            if not isinstance(tag_strings, list):
                tag_strings = []
            tags = [Tag(name=t, source=Tag.Source.unknown) for t in tag_strings]

        meeting_prep_generated_in_advance = (
            note.has_prep_contents and (note.start_time - note.clientinteraction.created).total_seconds() > 60
        )

        scheduled_event_uuid = note.scheduled_event.uuid if note.scheduled_event else None

        note_data = NoteInsightData(
            meeting_uuid=note.uuid,
            authorized_user_uuids=authorized_user_uuids,
            source=get_meeting_type(
                note.data_source,
                {"uuid": bot.uuid, "meeting_link": bot.meeting_link}
                if (note.meetingbot_set.exists() and (bot := note.meetingbot_set.all()[0]))
                else {},
            ),
            duration_seconds=duration,
            status=note.status,
            client_uuids=client_uuids,
            meeting_prep_generated=meeting_prep_generated_in_advance,
            follow_up_generated=bool(note.has_follow_up_email_contents),
            scheduled_event_uuid=scheduled_event_uuid,
            tags=tags,
            start_time=note.scheduled_event.start_time
            if note.scheduled_event
            else metadata.get("scheduled_at", note.created),
        )
        notes_data.append(note_data)

    return notes_data


async def _get_scheduled_events_data(user: DBUser, org_level_data: bool) -> list[ScheduledEventInsightData]:
    scheduled_events_queryset = (
        ScheduledEvent.objects.select_related("user")
        .only("uuid", "user__uuid", "attendee_matched_participants")
        .filter(removed_from_provider=False)
        .distinct()
        .order_by("created")
    )
    if org_level_data:
        scheduled_events_queryset = scheduled_events_queryset.filter(user__organization=user.organization)
    else:
        scheduled_events_queryset = scheduled_events_queryset.filter(user=user)

    scheduled_events_data = []
    async for event in scheduled_events_queryset:
        user_uuid = event.user.uuid

        has_clients = False
        if participants := event.attendee_matched_participants:
            try:
                participants_list = EventParticipantsList.model_validate(participants).root
            except ValidationError:
                logging.error("Invalid attendee matching data for event %s. Assuming no clients.", event.uuid)
                participants_list = []
            has_clients = any([p.zeplyn_kind == EventParticipant.ZeplynKind.CLIENT for p in participants_list])

        event_data = ScheduledEventInsightData(
            scheduled_event_uuid=event.uuid, user_uuid=user_uuid, has_clients=has_clients
        )
        scheduled_events_data.append(event_data)
    return scheduled_events_data


async def _get_tasks_data(user: DBUser, org_level_data: bool) -> list[TaskInsightData]:
    tasks_queryset = (
        Task.objects.select_related("assignee")
        .only("uuid", "assignee__uuid", "created", "completed")
        .distinct()
        .order_by("created")
    )
    if org_level_data:
        tasks_queryset = tasks_queryset.filter(
            models.Q(task_owner__organization=user.organization) | models.Q(assignee__organization=user.organization)
        )
    else:
        tasks_queryset = tasks_queryset.filter(models.Q(task_owner=user) | models.Q(assignee=user))

    tasks_data = []
    async for task in tasks_queryset:
        assignee_uuid = task.assignee.uuid if task.assignee else None

        task_data = TaskInsightData(
            task_uuid=task.uuid,
            assignee_uuid=assignee_uuid,
            creation_date=task.created,
            completed=task.completed,
        )
        tasks_data.append(task_data)
    return tasks_data


def get_item_counts_for_review_entries(entries: list[Any]) -> tuple[int, int]:
    items_total = 0
    items_completed = 0
    for item in entries:
        if not isinstance(item, dict):
            raise ValueError(f"Expected each entry to be a dict, got {type(item).__name__} in entries: {entries}")
        discussed = item.get("discussed", False)
        if discussed is not None and not isinstance(discussed, bool):
            raise ValueError(f"Expected 'discussed' to be a boolean, got {type(discussed).__name__} in item: {item}")
        items_total += 1 if "discussed" in item else 0
        items_completed += 1 if discussed else 0
        if subentries := item.get("subentries", {}):
            if not isinstance(subentries, list):
                raise ValueError(f"Expected 'subentries' to be a list, got {type(subentries).__name__} in item: {item}")
            sub_total, sub_completed = get_item_counts_for_review_entries(subentries)
            items_total += sub_total
            items_completed += sub_completed
    return items_total, items_completed


async def _get_structured_data_data(user: DBUser, org_level_data: bool) -> list[StructuredMeetingDataInsightData]:
    structured_data_queryset = (
        StructuredMeetingData.objects.select_related("note")
        .only("note__uuid", "uuid", "data", "title", "kind")
        .distinct()
        .order_by("created")
    )
    if org_level_data:
        structured_data_queryset = structured_data_queryset.filter(
            models.Q(note__note_owner__organization=user.organization)
        )
    else:
        structured_data_queryset = structured_data_queryset.filter(models.Q(note__authorized_users=user))

    structured_data_items = []
    async for data_item in structured_data_queryset:
        try:
            items_total, items_completed = (
                get_item_counts_for_review_entries(data_item.data.get("review_entries", []))
                if data_item.data
                else (0, 0)
            )
        except Exception:
            logging.error(
                "Error processing structured meeting data %s. Defaulting item counts to 0.",
                data_item.uuid,
                exc_info=True,
            )
            items_total, items_completed = 0, 0
        structured_item = StructuredMeetingDataInsightData(
            meeting_data_uuid=data_item.uuid,
            note_uuid=data_item.note.uuid if data_item.note else None,
            title=data_item.title,
            kind=data_item.kind,
            items_total=items_total,
            items_completed=items_completed,
        )
        structured_data_items.append(structured_item)
    return structured_data_items
