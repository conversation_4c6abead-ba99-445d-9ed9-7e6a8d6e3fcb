from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi import HTT<PERSON>Exception, status

from api.routers.onboarding import onboard_organization, process_onboarding
from api.routers.onboarding_models import (
    OnboardingRequest,
    OnboardingResponse,
    OrganizationData,
    UserData,
)


@pytest.mark.asyncio
class TestProcessOnboarding:
    @patch("api.routers.onboarding.process_organization_creation")
    @patch("api.routers.onboarding.process_users_creation")
    @patch("api.routers.onboarding.send_welcome_emails")
    async def test_process_onboarding_success(
        self, mock_send_emails: AsyncMock, mock_process_users: AsyncMock, mock_process_org: AsyncMock
    ) -> None:
        mock_org = MagicMock()
        mock_org.id = "test-org-id"
        mock_process_org.return_value = mock_org

        mock_users = [MagicMock()]
        mock_process_users.return_value = (2, mock_users, [])

        mock_send_emails.return_value = (1, [])

        request_data = OnboardingRequest(
            organization=OrganizationData(name="Test Org"),
            users=[
                UserData(email="<EMAIL>", first_name="User", last_name="One"),
                UserData(email="<EMAIL>", first_name="User", last_name="Two"),
            ],
        )

        response = await process_onboarding(request_data)

        assert response.success is True
        assert response.organization_id == "test-org-id"
        assert response.users_processed == 2
        assert response.welcome_emails_sent == 1
        assert len(response.errors) == 0

        mock_process_org.assert_called_once_with(request_data.organization)
        mock_process_users.assert_called_once_with(mock_org, request_data.users)
        mock_send_emails.assert_called_once_with(mock_users)

    @patch("api.routers.onboarding.process_organization_creation")
    @patch("api.routers.onboarding.process_users_creation")
    @patch("api.routers.onboarding.send_welcome_emails")
    async def test_process_onboarding_with_user_errors(
        self, mock_send_emails: AsyncMock, mock_process_users: AsyncMock, mock_process_org: AsyncMock
    ) -> None:
        mock_org = MagicMock()
        mock_org.id = "test-org-id"
        mock_process_org.return_value = mock_org

        mock_users = [MagicMock()]
        mock_process_users.return_value = (1, mock_users, ["Error creating user2"])

        mock_send_emails.return_value = (1, [])

        request_data = OnboardingRequest(
            organization=OrganizationData(name="Test Org"),
            users=[
                UserData(email="<EMAIL>", first_name="User", last_name="One"),
                UserData(email="<EMAIL>", first_name="User", last_name="Two"),
            ],
        )

        response = await process_onboarding(request_data)

        assert response.success is True  # Still success overall
        assert response.organization_id == "test-org-id"
        assert response.users_processed == 1
        assert response.welcome_emails_sent == 1
        assert len(response.errors) == 1
        assert "Error creating user2" in response.errors

    @patch("api.routers.onboarding.process_organization_creation")
    @patch("api.routers.onboarding.process_users_creation")
    @patch("api.routers.onboarding.send_welcome_emails")
    async def test_process_onboarding_with_email_errors(
        self, mock_send_emails: AsyncMock, mock_process_users: AsyncMock, mock_process_org: AsyncMock
    ) -> None:
        mock_org = MagicMock()
        mock_org.id = "test-org-id"
        mock_process_org.return_value = mock_org

        mock_users = [MagicMock()]
        mock_process_users.return_value = (2, mock_users, [])

        mock_send_emails.return_value = (0, ["Failed to send <NAME_EMAIL>"])

        request_data = OnboardingRequest(
            organization=OrganizationData(name="Test Org"),
            users=[
                UserData(email="<EMAIL>", first_name="User", last_name="One"),
                UserData(email="<EMAIL>", first_name="User", last_name="Two"),
            ],
        )

        response = await process_onboarding(request_data)

        assert response.success is True  # Still success overall
        assert response.organization_id == "test-org-id"
        assert response.users_processed == 2
        assert response.welcome_emails_sent == 0
        assert len(response.errors) == 1
        assert "Failed to send email" in response.errors[0]

    @patch("api.routers.onboarding.process_organization_creation")
    async def test_process_onboarding_org_creation_fails(self, mock_process_org: AsyncMock) -> None:
        mock_process_org.side_effect = HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create organization",
        )

        request_data = OnboardingRequest(
            organization=OrganizationData(name="Test Org"),
            users=[UserData(email="<EMAIL>", first_name="User", last_name="One")],
        )

        response = await process_onboarding(request_data)

        assert response.success is False
        assert response.organization_id is None
        assert len(response.errors) == 1
        assert "Failed to create organization" in response.errors[0]

    @patch("api.routers.onboarding.process_organization_creation")
    async def test_process_onboarding_unexpected_error(self, mock_process_org: AsyncMock) -> None:
        mock_process_org.side_effect = Exception("Unexpected error occurred")

        request_data = OnboardingRequest(
            organization=OrganizationData(name="Test Org"),
            users=[UserData(email="<EMAIL>", first_name="User", last_name="One")],
        )

        response = await process_onboarding(request_data)

        assert response.success is False
        assert response.organization_id is None
        assert len(response.errors) == 1
        assert "Unexpected error during onboarding" in response.errors[0]


class TestOnboardOrganization:
    @patch("api.routers.onboarding.process_onboarding")
    @pytest.mark.asyncio
    async def test_onboard_organization_success(self, mock_process_onboarding: AsyncMock) -> None:
        mock_process_onboarding.return_value = OnboardingResponse(
            success=True,
            organization_id="test-org-id",
            users_processed=2,
            welcome_emails_sent=2,
            errors=[],
        )

        request_data = OnboardingRequest(
            organization=OrganizationData(name="Test Org"),
            users=[
                UserData(email="<EMAIL>", first_name="User", last_name="One"),
                UserData(email="<EMAIL>", first_name="User", last_name="Two"),
            ],
        )

        response = await onboard_organization(request_data)

        assert response.success is True
        assert response.organization_id == "test-org-id"
        assert response.users_processed == 2
        assert response.welcome_emails_sent == 2
        assert len(response.errors) == 0

        mock_process_onboarding.assert_called_once_with(request_data)

    @patch("api.routers.onboarding.process_onboarding")
    @pytest.mark.asyncio
    async def test_onboard_organization_failed_but_org_created(self, mock_process_onboarding: AsyncMock) -> None:
        mock_process_onboarding.return_value = OnboardingResponse(
            success=False,
            organization_id="test-org-id",  # Org was created
            users_processed=0,
            welcome_emails_sent=0,
            errors=["User creation failed"],
        )

        request_data = OnboardingRequest(
            organization=OrganizationData(name="Test Org"),
            users=[UserData(email="<EMAIL>", first_name="User", last_name="One")],
        )

        response = await onboard_organization(request_data)

        assert response.success is False
        assert response.organization_id == "test-org-id"
        assert response.users_processed == 0
        assert response.welcome_emails_sent == 0
        assert len(response.errors) == 1
        assert "User creation failed" in response.errors

    @patch("api.routers.onboarding.process_onboarding")
    @pytest.mark.asyncio
    async def test_onboard_organization_complete_failure(self, mock_process_onboarding: AsyncMock) -> None:
        mock_process_onboarding.return_value = OnboardingResponse(
            success=False,
            organization_id=None,
            users_processed=0,
            welcome_emails_sent=0,
            errors=["Organization processing failed"],
        )

        request_data = OnboardingRequest(
            organization=OrganizationData(name="Test Org"),
            users=[UserData(email="<EMAIL>", first_name="User", last_name="One")],
        )

        with pytest.raises(HTTPException) as exc_info:
            await onboard_organization(request_data)

        assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Organization processing failed" in exc_info.value.detail
