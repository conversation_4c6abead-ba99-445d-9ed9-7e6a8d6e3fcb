import logging
from typing import Any, Generator
from unittest.mock import MagicMock, patch

import jsonschema
import pytest
from fastapi.testclient import <PERSON><PERSON>lient
from starlette import status

from api.dependencies import user_from_authorization_header
from api.internal_api import internal_api
from deepinsights.core.integrations.meetingbot import recall_b64data
from deepinsights.core.preferences.preferences import Preferences, _deepgram_languages
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.user import User

_client = TestClient(internal_api)
pytestmark = [pytest.mark.django_db(transaction=True)]


@pytest.fixture
def client(test_user: User) -> Generator[TestClient, None, None]:
    internal_api.dependency_overrides[user_from_authorization_header] = lambda: test_user
    yield _client
    internal_api.dependency_overrides = {}


@pytest.fixture
def test_org() -> Organization:
    return Organization.objects.create(name="Test Organization")


@pytest.fixture
def test_user(django_user_model: User, test_org: Organization) -> User:
    user = django_user_model.objects.create(username="testuser", email="<EMAIL>", password="testpass123")
    user.organization = test_org
    user.is_superuser = True
    user.save()
    return user


class TestGetPreferencesSchemas:
    @pytest.fixture
    def base_user_preferences(self, test_user: User) -> list[dict[str, Any]]:
        return [
            {
                "title": "General",
                "json_schema": {
                    "title": "Preferences",
                    "type": "object",
                    "properties": {
                        "asr_language_code": {
                            "type": "string",
                            "default": "",
                            "title": "Language for speech recognition",
                            "description": "The language to use for speech recognition. This is used for transcribing your meetings.",
                            "oneOf": sorted(_deepgram_languages, key=lambda x: x["title"]),
                        },
                        "send_task_reminder_email": {
                            "type": "boolean",
                            "title": "Send a daily email with your tasks",
                            "description": "Whether or not to send you a daily email summarizing your recently-assigned and soon-to-be-due tasks.",
                            "default": False,
                        },
                        "default_meeting_type": {
                            "type": "string",
                            "title": "Default Meeting Type",
                            "description": "The default meeting type to use when creating new meetings.",
                            "oneOf": [{"const": "", "title": "System default"}]
                            + [
                                {"const": str(m.uuid), "title": m.name}
                                for m in MeetingType.for_user(test_user).order_by("name")
                            ],
                            "default": "",
                        },
                    },
                },
                "ui_schema": {
                    "type": "VerticalLayout",
                    "elements": [
                        {"type": "Control", "scope": "#/properties/send_task_reminder_email"},
                        {"type": "Control", "scope": "#/properties/asr_language_code"},
                        {"type": "Control", "scope": "#/properties/default_meeting_type"},
                    ],
                },
                "data": {
                    "asr_language_code": "en",
                    "send_task_reminder_email": False,
                    "default_meeting_type": "",
                },
            },
            {
                "title": "Email Configuration",
                "json_schema": {
                    "title": "EmailConfiguration",
                    "type": "object",
                    "properties": {
                        "ccs": {
                            "title": "CCed email addresses",
                            "description": "Email addresses to CC on follow-up emails",
                            "type": "array",
                            "items": {"type": "string", "format": "email"},
                        },
                        "bccs": {
                            "type": "array",
                            "title": "BCCed email addresses",
                            "description": "Email addresses to BCC on follow-up emails",
                            "items": {"type": "string", "format": "email"},
                        },
                    },
                },
                "ui_schema": {
                    "type": "VerticalLayout",
                    "elements": [
                        {"type": "Control", "scope": "#/properties/ccs"},
                        {"type": "Control", "scope": "#/properties/bccs"},
                    ],
                },
                "data": {
                    "ccs": [],
                    "bccs": [],
                },
            },
            {
                "title": "Notifications",
                "json_schema": {
                    "type": "object",
                    "title": "NotificationPreferences",
                    "properties": {
                        "meeting_processed_notification_enabled": {
                            "type": "boolean",
                            "title": "Meeting Processed Notification Enabled",
                            "description": "Whether or not to send you an email notification when a meeting is has been fully processed.",
                            "default": False,
                        },
                        "email_meeting_notes_post_sync": {
                            "type": "boolean",
                            "title": "Email Meeting Notes Post Sync",
                            "description": "Whether or not to email meeting notes to you after a meeting is processed",
                            "default": False,
                        },
                    },
                },
                "ui_schema": {
                    "type": "VerticalLayout",
                    "elements": [
                        {"type": "Control", "scope": "#/properties/meeting_processed_notification_enabled"},
                        {"type": "Control", "scope": "#/properties/email_meeting_notes_post_sync"},
                    ],
                },
                "data": {
                    "meeting_processed_notification_enabled": False,
                    "email_meeting_notes_post_sync": False,
                },
            },
            {
                "title": "Calendar Preferences",
                "json_schema": {
                    "type": "object",
                    "title": "CalendarPreferences",
                    "properties": {
                        "auto_join_ignored_keywords": {
                            "type": "array",
                            "title": "Auto-ignore meetings with these keywords",
                            "description": "Meetings containing these keywords in the title will be automatically ignored.",
                            "items": {"type": "string"},
                        },
                    },
                },
                "ui_schema": {
                    "type": "VerticalLayout",
                    "elements": [
                        {"type": "Control", "scope": "#/properties/auto_join_ignored_keywords"},
                    ],
                },
                "data": {
                    "auto_join_ignored_keywords": [],
                },
            },
        ]

    @pytest.fixture
    def base_org_preferences(self, base_user_preferences: list[dict[str, Any]]) -> list[dict[str, Any]]:
        return base_user_preferences + [
            {
                "title": "Notetaker",
                "json_schema": {
                    "title": "BotPreferences",
                    "type": "object",
                    "properties": {
                        "not_recording_image_b64": {
                            "title": "Notetaker image: not recording",
                            "description": "The image to display in notetaker meetings when the bot is not recording.",
                            "type": "string",
                            "format": "base64jpeg",
                            "default": "",
                        },
                        "recording_image_b64": {
                            "title": "Notetaker image: recording",
                            "description": "The image to display in notetaker meetings when the bot is recording.",
                            "type": "string",
                            "format": "base64jpeg",
                            "default": "",
                        },
                        "notetaker_name": {
                            "title": "Notetaker name",
                            "description": "The name of the notetaker to display in notetaker meetings.",
                            "default": "",
                            "type": "string",
                        },
                        "enable_video": {
                            "type": "boolean",
                            "title": "Show Notetaker background image",
                            "description": "Whether or not to show the background image for the notetaker in meetings.",
                            "default": False,
                        },
                        "enable_audio_output": {
                            "type": "boolean",
                            "title": "Play notetaking audio disclaimer",
                            "description": "Whether or not the notetaker should speak recording indicator disclaimer messages in video calls.",
                            "default": False,
                        },
                    },
                },
                "ui_schema": {
                    "type": "VerticalLayout",
                    "elements": [
                        {"type": "Control", "scope": "#/properties/not_recording_image_b64"},
                        {"type": "Control", "scope": "#/properties/recording_image_b64"},
                        {"type": "Control", "scope": "#/properties/notetaker_name"},
                        {"type": "Control", "scope": "#/properties/enable_video"},
                        {"type": "Control", "scope": "#/properties/enable_audio_output"},
                    ],
                },
                "data": {
                    "not_recording_image_b64": recall_b64data.not_recording_default_jpeg,
                    "recording_image_b64": recall_b64data.recording_default_jpeg,
                    "notetaker_name": "",
                    "enable_video": True,
                    "enable_audio_output": True,
                },
            },
            {
                "title": "CRM Certification",
                "json_schema": {
                    "type": "object",
                    "title": "SyncPreferences",
                    "properties": {
                        "advisor_certification_required": {
                            "type": "boolean",
                            "title": "Advisor Certification Required for CRM sync",
                            "description": "Whether or not to require advisor certification when syncing interactions to CRM.",
                            "default": False,
                        },
                        "advisor_certification_synced_text": {
                            "type": "string",
                            "title": "Certification sync text",
                            "description": (
                                "The text to display in the CRM record when an advisor syncs an interaction that requires "
                                "certification. You can use '{user_email}' and '{timestamp_with_tz}' (exactly as shown, "
                                "without quotes) to insert the user's email and the timestamp of the sync."
                            ),
                            "default": "Reviewed and certified by {user_email}, at {timestamp_with_tz}",
                        },
                        "advisor_certification_display_text": {
                            "type": "string",
                            "title": "Certification display text",
                            "description": "The text to display in the Zeplyn app before syncing to CRM when requesting certification.",
                            "default": (
                                "I certify that the summary of notes has been reviewed and verified, and is accurate to the best of my knowledge."
                            ),
                        },
                    },
                },
                "ui_schema": {
                    "type": "VerticalLayout",
                    "elements": [
                        {"type": "Control", "scope": "#/properties/advisor_certification_required"},
                        {"type": "Control", "scope": "#/properties/advisor_certification_synced_text"},
                        {"type": "Control", "scope": "#/properties/advisor_certification_display_text"},
                    ],
                },
                "data": {
                    "advisor_certification_required": False,
                    "advisor_certification_synced_text": "Reviewed and certified by {user_email}, at {timestamp_with_tz}",
                    "advisor_certification_display_text": "I certify that the summary of notes has been reviewed and verified, and is accurate to the best of my knowledge.",
                },
            },
        ]

    def test_get_preference_schemas_user_only_no_overrides(
        self, test_user: User, client: TestClient, base_user_preferences: list[dict[str, Any]]
    ) -> None:
        test_user.is_superuser = False
        test_user.save()

        response = client.get("/api/v2/preferences/")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        assert data.get("user_preferences") == base_user_preferences
        assert not data.get("org_preferences")

        for preferences in data["user_preferences"]:
            try:
                jsonschema.validate(preferences["data"], preferences["json_schema"])
            except jsonschema.ValidationError as e:
                pytest.fail(f"Validation error in org preferences schema: {e.message}")

    def test_get_preference_schemas_superuser_with_org(
        self,
        client: TestClient,
        test_user: User,
        base_user_preferences: list[dict[str, Any]],
        base_org_preferences: list[dict[str, Any]],
    ) -> None:
        response = client.get("/api/v2/preferences/")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        assert "user_preferences" in data
        assert "org_preferences" in data
        assert len(data["user_preferences"]) == 4
        assert data["org_preferences"] == base_org_preferences

        for preferences in data["org_preferences"]:
            try:
                jsonschema.validate(preferences["data"], preferences["json_schema"])
            except jsonschema.ValidationError as e:
                pytest.fail(f"Validation error in org preferences schema: {e.message}")

    def test_get_preference_schemas_superuser_no_org(self, client: TestClient, test_user: User) -> None:
        test_user.organization = None
        test_user.save()

        response = client.get("/api/v2/preferences/")

        assert response.status_code == status.HTTP_200_OK
        assert not response.json().get("org_preferences")

    def test_get_preference_schemas_data_content(
        self, client: TestClient, test_user: User, test_org: Organization
    ) -> None:
        test_user.preferences = {
            "attach_transcript_to_follow_up_emails": True,
            "bot_preferences": {"notetaker_name": "Test Bot", "enable_video": False},
            "notification_preferences": {"meeting_processed_notification_enabled": True},
            "email_settings": {"ccs": ["<EMAIL>"]},
        }
        test_user.save()
        test_org.preferences = {
            "attach_transcript_to_follow_up_emails": False,
            "asr_language_code": "en-US",
            "bot_preferences": {"notetaker_name": "Test Org Bot", "enable_audio_output": False},
            "notification_preferences": {"email_meeting_notes_post_sync": True},
            "sync_preferences": {"advisor_certification_required": False},
        }
        test_org.save()

        response = client.get("/api/v2/preferences/")

        assert response.status_code == status.HTTP_200_OK

        # Confirm user preferences

        user_preferences = response.json().get("user_preferences", [])

        general_schema = next(s for s in user_preferences if s["title"] == "General")
        email_schema = next(s for s in user_preferences if s["title"] == "Email Configuration")
        notification_schema = next(s for s in user_preferences if s["title"] == "Notifications")

        assert general_schema["data"] == {
            "asr_language_code": "en-US",
            "send_task_reminder_email": False,
            "default_meeting_type": "",
        }

        assert email_schema["data"] == {
            "ccs": ["<EMAIL>"],
            "bccs": [],
        }
        assert notification_schema["data"] == {
            "meeting_processed_notification_enabled": True,
            "email_meeting_notes_post_sync": True,
        }

        org_preferences = response.json().get("org_preferences", [])

        # Confirm org preferences

        org_general_schema = next(s for s in org_preferences if s["title"] == "General")
        org_bot_schema = next(s for s in org_preferences if s["title"] == "Notetaker")
        org_email_schema = next(s for s in org_preferences if s["title"] == "Email Configuration")
        org_notification_schema = next(s for s in org_preferences if s["title"] == "Notifications")
        org_sync_schema = next(s for s in org_preferences if s["title"] == "CRM Certification")

        assert org_general_schema["data"] == {
            "asr_language_code": "en-US",
            "send_task_reminder_email": False,
            "default_meeting_type": "",
        }

        assert org_bot_schema["data"] == {
            "not_recording_image_b64": recall_b64data.not_recording_default_jpeg,
            "recording_image_b64": recall_b64data.recording_default_jpeg,
            "notetaker_name": "Test Org Bot",
            "enable_video": True,
            "enable_audio_output": False,
        }

        assert org_email_schema["data"] == {
            "ccs": [],
            "bccs": [],
        }

        assert org_notification_schema["data"] == {
            "meeting_processed_notification_enabled": False,
            "email_meeting_notes_post_sync": True,
        }

        assert org_sync_schema["data"] == {
            "advisor_certification_required": False,
            "advisor_certification_synced_text": "Reviewed and certified by {user_email}, at {timestamp_with_tz}",
            "advisor_certification_display_text": (
                "I certify that the summary of notes has been reviewed and verified, and is accurate to the best of my knowledge."
            ),
        }

    def test_get_preferences_meeting_types(self, client: TestClient, test_user: User, test_org: Organization) -> None:
        user_meeting_type = MeetingType.objects.create(
            key="user_meeting_type",
            name="User Meeting Type",
        )
        user_meeting_type.users.add(test_user)
        user_meeting_type.save()
        org_meeting_type = MeetingType.objects.create(
            key="org_meeting_type",
            name="Organization Meeting Type",
        )
        org_meeting_type.organizations.add(test_org)
        org_meeting_type.save()

        org_and_everyone_meeting_type = MeetingType.objects.create(
            key="multiple_org_meeting_type",
            name="Multiple Org Meeting Type",
            everyone=True,
        )
        org_two = Organization.objects.create(name="Test Org Two")
        org_and_everyone_meeting_type.organizations.set([test_org, org_two])
        org_and_everyone_meeting_type.save()

        response = client.get("/api/v2/preferences/")

        assert response.status_code == status.HTTP_200_OK

        assert response.json().get("user_preferences")[0]["json_schema"]["properties"]["default_meeting_type"][
            "oneOf"
        ] == [
            {"const": "", "title": "System default"},
        ] + [
            {"const": str(m.uuid), "title": m.name} for m in MeetingType.objects.filter(everyone=True).order_by("name")
        ] + [
            {"const": str(org_meeting_type.uuid), "title": org_meeting_type.name},
            {"const": str(user_meeting_type.uuid), "title": user_meeting_type.name},
        ]

        assert response.json().get("org_preferences")[0]["json_schema"]["properties"]["default_meeting_type"][
            "oneOf"
        ] == [
            {"const": "", "title": "System default"},
        ] + [
            {"const": str(m.uuid), "title": m.name} for m in MeetingType.objects.filter(everyone=True).order_by("name")
        ] + [
            {"const": str(org_meeting_type.uuid), "title": org_meeting_type.name},
        ]

    @patch("api.routers.preferences.Preferences")
    def test_invalid_dataclass(
        self,
        mock_preferences: MagicMock,
        client: TestClient,
        base_user_preferences: list[dict[str, Any]],
        base_org_preferences: list[dict[str, Any]],
        caplog: pytest.LogCaptureFixture,
    ) -> None:
        del mock_preferences.__dataclass_fields__
        mock_preferences.__name__ = "InvalidPreferences"
        mock_preferences.from_dict = Preferences.from_dict

        with caplog.at_level(logging.ERROR):
            response = client.get("/api/v2/preferences/")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "user_preferences": base_user_preferences[1:],
            "org_preferences": base_org_preferences[1:],
        }

        assert len(caplog.messages) == 2
        assert "No fields found for InvalidPreferences. This indicates a programming error." in caplog.messages[0]
        assert "No fields found for InvalidPreferences. This indicates a programming error." in caplog.messages[1]


class TestUpdatePreferences:
    @pytest.mark.parametrize(
        "existing_preferences",
        [
            None,
            {},
            {"attach_transcript_to_follow_up_emails": False},
            {"attach_transcript_to_follow_up_emails": True, "asr_language_code": "en-US"},
            {"other_field": "value"},
        ],
    )
    @pytest.mark.parametrize("scope", ["user", "organization"])
    def test_update_main_preferences(
        self,
        client: TestClient,
        test_user: User,
        test_org: Organization,
        existing_preferences: dict[str, Any],
        scope: str,
    ) -> None:
        target = test_user if scope == "user" else test_org
        target.preferences = existing_preferences
        target.save()

        response = client.post(
            "/api/v2/preferences/",
            json={
                "scope": scope,
                "schema_title": "Preferences",
                "updated_values": {"attach_transcript_to_follow_up_emails": True},
            },
        )

        assert response.status_code == status.HTTP_200_OK

        target.refresh_from_db()
        assert target.get_preferences().attach_transcript_to_follow_up_emails is True
        assert target.preferences == {
            **(existing_preferences or {}),
            "attach_transcript_to_follow_up_emails": True,
        }

    @pytest.mark.parametrize("scope", ["user", "organization"])
    def test_update_bot_preferences_with_empty_preferences(
        self, client: TestClient, test_user: User, test_org: Organization, scope: str
    ) -> None:
        target = test_user if scope == "user" else test_org
        target.preferences = {}
        target.save()

        response = client.post(
            "/api/v2/preferences/",
            json={
                "scope": scope,
                "schema_title": "BotPreferences",
                "updated_values": {"notetaker_name": "New Bot Name", "enable_video": True},
            },
        )

        assert response.status_code == status.HTTP_200_OK
        target.refresh_from_db()
        prefs = target.get_preferences()
        assert prefs.bot_preferences.notetaker_name == "New Bot Name"
        assert prefs.bot_preferences.enable_video
        assert target.preferences == {
            "bot_preferences": {
                "notetaker_name": "New Bot Name",
                "enable_video": True,
            },
        }

    @pytest.mark.parametrize(
        "existing_bot_preferences",
        [
            None,
            {},
            {"enable_audio_output": True},
            {"notetaker_name": "Old Bot Name", "enable_video": False, "enable_audio_output": True},
            {"other_field": "value"},
        ],
    )
    @pytest.mark.parametrize("scope", ["user", "organization"])
    def test_update_bot_preferences_with_existing_preferences(
        self,
        client: TestClient,
        test_user: User,
        test_org: Organization,
        existing_bot_preferences: dict[str, Any],
        scope: str,
    ) -> None:
        target = test_user if scope == "user" else test_org
        target.preferences = {
            "bot_preferences": existing_bot_preferences,
        }
        target.save()

        response = client.post(
            "/api/v2/preferences/",
            json={
                "scope": scope,
                "schema_title": "BotPreferences",
                "updated_values": {"notetaker_name": "New Bot Name", "enable_video": True},
            },
        )

        assert response.status_code == status.HTTP_200_OK
        target.refresh_from_db()
        prefs = target.get_preferences()
        assert prefs.bot_preferences.notetaker_name == "New Bot Name"
        assert prefs.bot_preferences.enable_video
        assert target.preferences == {
            "bot_preferences": {
                **(existing_bot_preferences or {}),
                "notetaker_name": "New Bot Name",
                "enable_video": True,
            },
        }

    @pytest.mark.parametrize("scope", ["user", "organization"])
    def test_update_email_configuration_with_empty_preferences(
        self, client: TestClient, test_user: User, test_org: Organization, scope: str
    ) -> None:
        target = test_user if scope == "user" else test_org
        target.preferences = {}
        target.save()

        response = client.post(
            "/api/v2/preferences/",
            json={
                "scope": scope,
                "schema_title": "EmailConfiguration",
                "updated_values": {"ccs": ["<EMAIL>"]},
            },
        )

        assert response.status_code == status.HTTP_200_OK
        target.refresh_from_db()

        prefs = target.get_preferences()
        assert prefs.email_settings.ccs == ["<EMAIL>"]
        assert target.preferences == {
            "email_settings": {"ccs": ["<EMAIL>"]},
        }

    @pytest.mark.parametrize(
        "existing_email_preferences",
        [
            None,
            {},
            {"ccs": []},
            {"ccs": ["<EMAIL>"]},
            {"ccs": ["<EMAIL>"], "bccs": ["<EMAIL>"]},
            {"other_field": "value"},
        ],
    )
    @pytest.mark.parametrize("scope", ["user", "organization"])
    def test_update_email_preferences_with_existing_preferences(
        self,
        client: TestClient,
        test_user: User,
        test_org: Organization,
        scope: str,
        existing_email_preferences: dict[str, Any],
    ) -> None:
        target = test_user if scope == "user" else test_org
        target.preferences = {
            "email_settings": existing_email_preferences,
        }
        target.save()

        response = client.post(
            "/api/v2/preferences/",
            json={
                "scope": scope,
                "schema_title": "EmailConfiguration",
                "updated_values": {"ccs": ["<EMAIL>"], "bccs": []},
            },
        )

        assert response.status_code == status.HTTP_200_OK
        target.refresh_from_db()
        prefs = target.get_preferences()
        assert prefs.email_settings.ccs == ["<EMAIL>"]
        assert prefs.email_settings.bccs == []
        assert target.preferences == {
            "email_settings": {
                **(existing_email_preferences or {}),
                "ccs": ["<EMAIL>"],
                "bccs": [],
            },
        }

    @pytest.mark.parametrize("scope", ["user", "organization"])
    def test_update_notification_preferences_with_empty_preferences(
        self, client: TestClient, test_user: User, test_org: Organization, scope: str
    ) -> None:
        target = test_user if scope == "user" else test_org
        target.preferences = {}
        target.save()

        response = client.post(
            "/api/v2/preferences/",
            json={
                "scope": scope,
                "schema_title": "NotificationPreferences",
                "updated_values": {"meeting_processed_notification_enabled": True},
            },
        )

        assert response.status_code == status.HTTP_200_OK
        target.refresh_from_db()
        prefs = target.get_preferences()
        assert prefs.notification_preferences.meeting_processed_notification_enabled
        assert target.preferences == {
            "notification_preferences": {"meeting_processed_notification_enabled": True},
        }

    @pytest.mark.parametrize(
        "existing_notification_preferences",
        [
            None,
            {},
            {"meeting_processed_notification_enabled": False},
            {"meeting_processed_notification_enabled": True, "email_meeting_notes_post_sync": False},
            {"other_field": "value"},
        ],
    )
    @pytest.mark.parametrize("scope", ["user", "organization"])
    def test_update_notification_preferences_with_existing_preferences(
        self,
        client: TestClient,
        test_user: User,
        test_org: Organization,
        scope: str,
        existing_notification_preferences: dict[str, Any],
    ) -> None:
        target = test_user if scope == "user" else test_org
        target.preferences = {
            "notification_preferences": existing_notification_preferences,
        }
        target.save()

        response = client.post(
            "/api/v2/preferences/",
            json={
                "scope": scope,
                "schema_title": "NotificationPreferences",
                "updated_values": {"meeting_processed_notification_enabled": True},
            },
        )

        assert response.status_code == status.HTTP_200_OK
        target.refresh_from_db()
        prefs = target.get_preferences()
        assert prefs.notification_preferences.meeting_processed_notification_enabled
        assert target.preferences == {
            "notification_preferences": {
                **(existing_notification_preferences or {}),
                "meeting_processed_notification_enabled": True,
            },
        }

    @pytest.mark.parametrize("scope", ["user", "organization"])
    def test_update_sync_preferences_with_empty_preferences(
        self, client: TestClient, test_user: User, test_org: Organization, scope: str
    ) -> None:
        target = test_user if scope == "user" else test_org
        target.preferences = {}
        target.save()

        response = client.post(
            "/api/v2/preferences",
            json={
                "scope": scope,
                "schema_title": "SyncPreferences",
                "updated_values": {"advisor_certification_required": True},
            },
        )

        assert response.status_code == status.HTTP_200_OK
        target.refresh_from_db()

        assert target.get_preferences().sync_preferences.advisor_certification_required
        assert target.preferences == {
            "sync_preferences": {
                "advisor_certification_required": True,
            },
        }

    @pytest.mark.parametrize(
        "existing_sync_preferences",
        [
            None,
            {},
            {"advisor_certification_required": False},
            {"advisor_certification_required": True, "advisor_certification_synced_text": "Test Text"},
            {"other_field": "value"},
        ],
    )
    @pytest.mark.parametrize("scope", ["user", "organization"])
    def test_update_sync_preferences_with_existing_preferences(
        self,
        client: TestClient,
        test_user: User,
        test_org: Organization,
        scope: str,
        existing_sync_preferences: dict[str, Any],
    ) -> None:
        target = test_user if scope == "user" else test_org
        target.preferences = {"sync_preferences": existing_sync_preferences}
        target.save()

        response = client.post(
            "/api/v2/preferences",
            json={
                "scope": scope,
                "schema_title": "SyncPreferences",
                "updated_values": {"advisor_certification_required": True},
            },
        )

        assert response.status_code == status.HTTP_200_OK
        target.refresh_from_db()

        assert target.get_preferences().sync_preferences.advisor_certification_required
        assert target.preferences == {
            "sync_preferences": {
                **(existing_sync_preferences or {}),
                "advisor_certification_required": True,
            },
        }

    @pytest.mark.parametrize("scope", ["user", "organization"])
    def test_update_calendar_preferences_with_empty_preferences(
        self, client: TestClient, test_user: User, test_org: Organization, scope: str
    ) -> None:
        target = test_user if scope == "user" else test_org
        target.preferences = {}
        target.save()

        response = client.post(
            "/api/v2/preferences/",
            json={
                "scope": scope,
                "schema_title": "CalendarPreferences",
                "updated_values": {"auto_join_ignored_keywords": ["standup", "1:1"]},
            },
        )

        assert response.status_code == status.HTTP_200_OK
        target.refresh_from_db()
        prefs = target.get_preferences()
        assert prefs.calendar_preferences.auto_join_ignored_keywords == ["standup", "1:1"]
        assert target.preferences == {
            "calendar_preferences": {"auto_join_ignored_keywords": ["standup", "1:1"]},
        }

    @pytest.mark.parametrize(
        "existing_calendar_preferences",
        [
            None,
            {},
            {"auto_join_ignored_keywords": []},
            {"auto_join_ignored_keywords": ["daily"]},
            {"other_field": "value"},
        ],
    )
    @pytest.mark.parametrize("scope", ["user", "organization"])
    def test_update_calendar_preferences_with_existing_preferences(
        self,
        client: TestClient,
        test_user: User,
        test_org: Organization,
        scope: str,
        existing_calendar_preferences: dict[str, Any],
    ) -> None:
        target = test_user if scope == "user" else test_org
        target.preferences = {
            "calendar_preferences": existing_calendar_preferences,
        }
        target.save()

        response = client.post(
            "/api/v2/preferences/",
            json={
                "scope": scope,
                "schema_title": "CalendarPreferences",
                "updated_values": {"auto_join_ignored_keywords": ["standup", "retrospective"]},
            },
        )

        assert response.status_code == status.HTTP_200_OK
        target.refresh_from_db()
        prefs = target.get_preferences()
        assert prefs.calendar_preferences.auto_join_ignored_keywords == ["standup", "retrospective"]
        assert target.preferences == {
            "calendar_preferences": {
                **(existing_calendar_preferences or {}),
                "auto_join_ignored_keywords": ["standup", "retrospective"],
            },
        }

    @pytest.mark.parametrize(
        "scope",
        [
            "user",
            "organization",
        ],
    )
    def test_invalid_schema_title(self, client: TestClient, test_user: User, scope: str) -> None:
        response = client.post(
            "/api/v2/preferences/",
            json={"scope": scope, "schema_title": "InvalidSchema", "updated_values": {"some_field": "some_value"}},
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    @pytest.mark.parametrize(
        "scope",
        [
            "user",
            "organization",
        ],
    )
    def test_nonexistent_field(self, client: TestClient, test_user: User, scope: str) -> None:
        initial_prefs = test_user.get_preferences().to_dict()

        response = client.post(
            "/api/v2/preferences/",
            json={
                "scope": scope,
                "schema_title": "BotPreferences",
                "updated_values": {"nonexistent_field": "some_value"},
            },
        )

        assert response.status_code == status.HTTP_200_OK
        test_user.refresh_from_db()

        assert test_user.get_preferences().to_dict() == initial_prefs

    def test_update_preferences_for_org_no_org(self, client: TestClient, test_user: User) -> None:
        test_user.organization = None
        test_user.save()

        response = client.post(
            "/api/v2/preferences/",
            json={
                "scope": "organization",
                "schema_title": "Preferences",
                "updated_values": {"attach_transcript_to_follow_up_emails": True},
            },
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json() == {"detail": "Cannot update organization preferences for user"}

    def test_update_preferences_for_org_not_superuser(self, client: TestClient, test_user: User) -> None:
        test_user.is_superuser = False
        test_user.save()

        response = client.post(
            "/api/v2/preferences/",
            json={
                "scope": "organization",
                "schema_title": "Preferences",
                "updated_values": {"attach_transcript_to_follow_up_emails": True},
            },
        )

        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert response.json() == {"detail": "User does not have permission to update organization preferences"}
