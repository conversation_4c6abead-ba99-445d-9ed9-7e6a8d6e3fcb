import logging
import os
import uuid
from datetime import datetime, timedelta
from mimetypes import guess_extension

from django.contrib.postgres.search import SearchQuery, SearchVector
from django.db import transaction
from django.db.models import CharField, DateTimeField, F, Func, Prefetch, Q, Value
from django.db.models.functions import Cast, Coalesce
from django.utils import timezone
from fastapi import APIRouter, Body, Depends, Form, HTTPException, Path, Query, Response, UploadFile, status
from pydantic import BaseModel, ValidationError

from api.dependencies import user_from_authorization_header
from api.routers.note_models import (
    ActionItemUpdate,
    ActionType,
    CreateOrUpdateNoteResponse,
    EditNoteRequest,
    ListNotesResponse,
    MailtoResponse,
    NoteResponse,
    SummarySection,
    SwapAttendeesRequest,
    get_note_response,
    list_notes_response,
)
from deepinsights.core.integrations.meetingbot.bot_controller import <PERSON>t<PERSON>tat<PERSON>
from deepinsights.core.ml.emails import email_meeting_notes, generate_followup_email_contents
from deepinsights.core.ml.process_transcript import calculate_speaker_time, get_speaker_percentage
from deepinsights.core.ml.voice_memo_utils import clean_and_parse_string
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.audio_buffer import AudioBuffer
from deepinsights.meetingsapp.models.client import Client as DBClient
from deepinsights.meetingsapp.models.client_interaction import ClientInteraction
from deepinsights.meetingsapp.models.meeting_bot import MeetingBot
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note, get_agenda_template_content, is_authorized_to_view_note
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent
from deepinsights.meetingsapp.models.search_query import SearchQuery as SearchQueryModel
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingData
from deepinsights.meetingsapp.models.structured_meeting_data_schema import StructuredMeetingDataSchema
from deepinsights.meetingsapp.models.task import Task
from deepinsights.meetingsapp.tasks import (
    create_advisor_notes_and_fill_with_client_intelligence,
    generate_agenda,
    process_note_recording,
    reprocess_note_after_swapping,
)
from deepinsights.users.models.user import User

router = APIRouter(tags=["note"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}")
public_router = APIRouter(tags=["note"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}")


class SearchAddSectionRequest(BaseModel):
    """
    The request model for the add section to summary endpoint
    """

    search_query_id: uuid.UUID
    section_to_replace: int | None = None


class SearchAddSectionResponse(BaseModel):
    """
    The response model for the add section to summary endpoint
    """

    section_index: int


def get_or_create_note(note_id: uuid.UUID | None, user: User, current_time: datetime) -> Note:
    if note_id:
        logging.info("Handling update of note %s", note_id)
        try:
            note = Note.objects.get(uuid=note_id)
        except Note.DoesNotExist:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Note not found")
    else:
        note = Note.objects.create(note_owner=user, created=current_time)
        note.authorized_users.add(user)
        note.save()
        logging.info("Created new note %s", note.uuid)
    return note


def set_note_meeting_metadata(
    note: Note,
    scheduled_start_time: datetime | None,
    meeting_name: str | None,
    meeting_type: str | None,
    meeting_source_id: str | None,
    client_name: str | None,
    client_id: str | None,
) -> Note:
    metadata = note.metadata or {}
    if scheduled_start_time:
        metadata["scheduled_at"] = scheduled_start_time.isoformat()
    if meeting_name:
        metadata["meeting_name"] = meeting_name
    if meeting_type_id := meeting_type:
        try:
            note.meeting_type = MeetingType.objects.get(uuid=uuid.UUID(meeting_type_id))
        except (ValueError, MeetingType.DoesNotExist):
            try:
                note.meeting_type = MeetingType.objects.get(key=meeting_type_id)
            except MeetingType.DoesNotExist:
                metadata["meeting_type"] = meeting_type_id
    if meeting_source_id:
        metadata["meeting_source_id"] = meeting_source_id
    note.metadata = metadata
    if client_name and client_id:
        note.client = {
            "name": client_name,
            "uuid": client_id,
        }
    note.save()
    return note


@router.post(
    "/create_or_update",
    summary="Create or update a note",
    description=(
        "Create or update a note with the provided data. This method is intended to be somewhat "
        "intelligent, and will handle new and existing notes, requests with mic audio data and "
        "requests associated with a meeting bot."
    ),
    responses={
        400: {"description": "Bad request"},
        403: {"description": "Not authorized to view this note"},
        500: {"description": "Internal server error"},
    },
)
@transaction.atomic
def create_or_update_note(
    note_id: uuid.UUID | None = Form(
        None,
        description=(
            "The UUID of the note to update. If unset, a new note will be created; if set, the "
            "existing note will be updated."
        ),
    ),
    meeting_type: str | None = Form(
        None,
        description=(
            "The type of meeting that this note is associated with. This should be a UUID "
            "referencing a MeetingType object in the database; however, for compatability "
            "reasons, this can also be one of the strings 'client', 'internal' or 'debrief'."
        ),
    ),
    note_type: str | None = Form(None, description="The type of note."),
    file_type: str | None = Form(None, description="The file type of the audio data, a file extension (e.g., `.wav`)"),
    meeting_name: str | None = Form(None, description="The name of the meeting."),
    attendees: str | None = Form(
        None,
        description=(
            "A stringified JSON array of attendees. This will look as follows: "
            '`\'[{"name":"Username","type":"user","uuid":"11111111-**************-************"}]\'`'
        ),
    ),
    meeting_link: str | None = Form(
        None,
        description=(
            "A URL that can be used to join a video meeting. If provided, this will cause a bot to "
            "be created for the meeting, or else a bot to be updated for the meeting."
        ),
    ),
    meeting_source_id: str | None = Form(
        None,
        description=(
            "An identifier that links with a calendar entry (typically, but not necessarily) that "
            "provided the information for this meeting."
        ),
    ),
    scheduled_start_time: datetime | None = Form(
        None,
        description="The scheduled start time of the meeting.",
    ),
    scheduled_end_time: datetime | None = Form(
        None,
        description="The scheduled end time of the meeting.",
    ),
    scheduled_event_uuid: uuid.UUID | None = Form(
        None,
        description=(
            "The UUID of the scheduled event associated with this note. This will be used to "
            "associate the note with an existing scheduled event."
        ),
    ),
    client_name: str | None = Form(
        None,
        description=(
            "The name of the client associated with this note. This will only be used if the "
            "client ID is also provided."
        ),
    ),
    client_id: str | None = Form(
        None,
        description=(
            "The ID of the client associated with this note. This will only be used if the "
            "client name is also provided."
        ),
    ),
    audio_data: UploadFile | None = None,
    user: User = Depends(user_from_authorization_header),
) -> CreateOrUpdateNoteResponse:
    current_time = timezone.now()

    note = get_or_create_note(note_id, user, current_time)

    if not is_authorized_to_view_note(user, note):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN)

    # If the note is already completed, don't perform any more operations on it.
    if note.status and note.status != Note.PROCESSING_STATUS.scheduled:
        return CreateOrUpdateNoteResponse(note_id=note.uuid, completed=True)

    note_type = note_type or Note.NOTE_TYPE.voice_memo
    note.note_type = note_type

    # The logic for scheduled event handling is a bit convoluted and too tightly coupled to the
    # frontend behavior. The frontend:
    # - only passes up a scheduled event UUID when the user is trying to create a note from a
    #   scheduled event
    # - only passes up the start and end times when there is not a scheduled event UUID (i.e., the
    #   event is not being created from an external system)
    # - note that, once an event has been attached, the scheduled event UUID is still not sent back
    #   up with the note; it's
    # This was expedient when moving from directly-fetched calendar events to database-stored
    # scheduled events, but should be reworked once we are only using database-stored scheduled
    # events.

    if not scheduled_event_uuid and (scheduled_start_time and scheduled_end_time):
        scheduled_event, _ = ScheduledEvent.objects.get_or_create(
            note=note,
            defaults={
                "user": user,
                "start_time": scheduled_start_time,
                "end_time": scheduled_end_time,
            },
        )
        if not scheduled_event.is_from_external_system:
            scheduled_event.start_time = scheduled_start_time
            scheduled_event.end_time = scheduled_end_time
            scheduled_event.save()
    if scheduled_event_uuid:
        try:
            scheduled_event = ScheduledEvent.objects.get(uuid=scheduled_event_uuid, user=user)
            if (note_scheduled_event := note.scheduled_event) and note_scheduled_event != scheduled_event:
                logging.warning(
                    "Note %s already has linked scheduled event. Replacing with %s.",
                    note.uuid,
                    scheduled_event_uuid,
                )
                note_scheduled_event.note = None
                note_scheduled_event.save()
            scheduled_event.note = note
            scheduled_event.save()
        except ScheduledEvent.DoesNotExist:
            logging.warning("Scheduled event %s not found to link to note %s", scheduled_event_uuid, note.uuid)
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Scheduled event not found")

    should_update_empty_status_to_scheduled = bool(scheduled_start_time or scheduled_event_uuid)

    set_note_meeting_metadata(
        note, scheduled_start_time, meeting_name, meeting_type, meeting_source_id, client_name, client_id
    )

    if (parsed_attendees := Attendee.parse_attendees(attendees or "[]")) is not None:  # type: ignore[no-untyped-call]
        Attendee.reconcile_attendees(note=note, attendees=parsed_attendees)
        for attendee in note.attendees.filter(user__isnull=False):
            if attendee.user:
                note.authorized_users.add(attendee.user)
        note.save()

    # Create a client interaction for this note (if one does not already exist).
    if Flags.EnableClientIntelligencePreMeetingWorkflow.is_active_for_user(user) and note.meeting_type:
        create_or_update_interaction(note, user)

    # If the note has any audio data, assume it is an audio recording meeting and process it as such.
    if audio_data:
        try:
            file_type = file_type or _file_type_for_audio_data(audio_data) or "webm"
            # Detatch any bots that are associated with this note, but do not delete them yet. It's most
            # likely the case that the user wants the uploaded audio data to be associated with this note,
            # but better not to delete the bot until we are certain.
            for bot in MeetingBot.objects.filter(uuid=note.bot_uuid) if note.bot_uuid else []:
                bot.note = None
                bot.save()
            note.save_with_audio_data(user, note_type, current_time, file_type, audio_data.file)
            process_note_recording.delay_on_commit(user.uuid, note.uuid)
            return CreateOrUpdateNoteResponse(note_id=note.uuid, completed=True)
        except Exception as e:
            logging.error("Audio recording note not saved: %s", note.uuid, exc_info=e)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Error saving uploaded audio data."
            )

    # Handle bot updates.

    # When the save scheduled notes feature is enabled, the flow works quite differently because it
    # has to differentiate between saves that indicate a recording should start and saves of
    # scheduled notes for the future.
    if Flags.EnableSaveScheduledNotes.is_active_for_user(user):
        return _create_or_update_with_scheduled_note_feature(
            note, should_update_empty_status_to_scheduled, meeting_link
        )

    # If there is already a bot associated with this note, but no audio data, update the bot
    # recording session.
    if note.bot_uuid:
        # If bot creation failed previously, detatch the old bot and try again.
        bot = MeetingBot.objects.get(uuid=note.bot_uuid)
        if bot.get_status() == BotStatus.NOT_CREATED:
            bot.note = None
            bot.save()
        else:
            _finish_bot_recording_session(note)
            return CreateOrUpdateNoteResponse(note_id=note.uuid, completed=True)

    # If there is a meeting link, create a bot for the meeting. This happens after other checks for
    # a bot and for audio data, to ensure that audio data is given preference and to handle the case
    # of recovering from a failed bot creation.
    if meeting_link:
        try:
            _create_bot_for_note(note, meeting_link)
            return CreateOrUpdateNoteResponse(note_id=note.uuid, completed=False)
        except Exception as e:
            logging.error("Bot not created for meeting URL: %s", exc_info=e)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Error creating note with meeting bot."
            )

    # If nothing else applies to this note, it doesn't already have a status, and a start time was
    # provided, assume that it is meant to be a scheduled note.
    if not note.status and should_update_empty_status_to_scheduled:
        note.status = Note.PROCESSING_STATUS.scheduled
        note.save()

    return CreateOrUpdateNoteResponse(note_id=note.uuid, completed=False)


# Handles bot creation and updates when the feature to allow saving scheduled notes is enabled.
def _create_or_update_with_scheduled_note_feature(
    note: Note,
    should_update_empty_status_to_scheduled: bool,
    meeting_link: str | None,
) -> CreateOrUpdateNoteResponse:
    if meeting_link:
        _create_or_update_bot_for_note(note, meeting_link)

    # If nothing else applies to this note, it doesn't already have a status, and a start time was
    # provided, assume that it is meant to be a scheduled note.
    if not note.status and should_update_empty_status_to_scheduled:
        note.status = Note.PROCESSING_STATUS.scheduled
        note.save()

    bot = MeetingBot.objects.filter(note=note, bot_owner=note.note_owner).first()
    return CreateOrUpdateNoteResponse(note_id=note.uuid, completed=False, bot_id=bot.uuid if bot else None)


# Creates a new ClientInteraction for this note.
def create_or_update_interaction(note: Note, user: User) -> None:
    """
    Creates or updates a ClientInteraction for a note with the following flow:

    1. Creates/gets interaction and sets meeting type
    2. Updates interaction with clients from note attendees
    3. Handles advisor notes:
       - If no advisor notes exist:
         - For first client: Creates advisor notes with client intelligence
         - For no client: Creates empty advisor notes
    4. Handles agenda:
       - If no agenda exists:
         - Creates empty agenda with template content if available
         - For first client + template: Triggers async agenda generation with client intelligence
         - For no client: Creates empty agenda with template content

    Args:
        note (Note): The note to create/update interaction for
        user (User): The user performing the action
    """
    if not note.meeting_type:
        logging.error("Note %s does not have a meeting type, skipping interaction creation", note.uuid)
        return

    interaction, _ = ClientInteraction.objects.get_or_create(
        note=note,
        defaults={
            "meeting_type": note.meeting_type,
        },
    )
    # Update the interaction with the meeting type and attendees.
    #
    # The meeting type will be set by default above if the object was created; this ensures that the
    # meeting type is updated if the interaction had already been created.
    interaction.meeting_type = note.meeting_type
    attendee_client_uuids = [
        attendee.client.uuid for attendee in note.attendees.filter(client__isnull=False) if attendee.client is not None
    ]
    interaction.clients.set(DBClient.objects.filter(uuid__in=attendee_client_uuids))
    if not interaction.advisor_notes:
        # Create client recap for the first client in the meeting and add to advisor notes
        # TODO [ENG-1183]: This should be done for each client in the meeting
        logging.info(
            "No advisor notes found. Creating advisor notes generation for interaction %s",
            interaction.uuid,
        )
        first_client = interaction.clients.first()
        interaction.advisor_notes = StructuredMeetingData.objects.create(
            title="Advisor Notes",
            kind="advisor_notes",
            schema=StructuredMeetingDataSchema.objects.get(name="unstructured_text").schema,
            status=StructuredMeetingData.Status.CREATED,
            data={
                "content": "",
                "format": "markdown",
            },
        )
        if first_client:
            logging.info("Creating advisor notes generation for client %s", first_client.uuid)
            interaction.advisor_notes.status = StructuredMeetingData.Status.PROCESSING
            interaction.advisor_notes.save()
            create_advisor_notes_and_fill_with_client_intelligence.delay_on_commit(
                user.uuid, first_client.uuid, interaction.uuid
            )
        else:
            logging.info(
                "No client found for interaction %s, skipping advisor notes generation",
                interaction.uuid,
            )
            interaction.advisor_notes.status = StructuredMeetingData.Status.COMPLETED
            interaction.advisor_notes.save()

    # We should always have an agenda object. If it doesn't exist, create it. If no template content is available, create an empty string.
    if not interaction.agenda:
        # Handle agenda creation
        client = interaction.clients.first()
        agenda_template_content = get_agenda_template_content(note)
        logging.info(
            "Creating agenda for interaction %s - has client: %s, has meeting type: %s",
            interaction.uuid,
            bool(client),
            bool(note.meeting_type),
        )
        # Create an agenda with template content, or an empty agenda if there is no template content
        schema_unstructured = StructuredMeetingDataSchema.objects.get(name="unstructured_text")
        agenda = StructuredMeetingData.objects.create(
            title="Meeting Agenda",
            schema=schema_unstructured.schema,
            data={
                "content": agenda_template_content,
                "format": "markdown",
            },
            status=StructuredMeetingData.Status.CREATED,
            note=None,
        )
        interaction.agenda = agenda
        interaction.save()

        # Then if client and meeting type exist, trigger the async generation to fill in agenda with client intelligence
        if client and agenda_template_content:
            logging.info("Generating agenda for client interaction %s for client %s", interaction.uuid, client.uuid)
            interaction.agenda.status = StructuredMeetingData.Status.PROCESSING
            interaction.agenda.save()
            generate_agenda.delay_on_commit(user.uuid, client.uuid, interaction.uuid, agenda_template_content)
        else:
            logging.warning("Agenda template content is empty for client interaction %s", interaction.uuid)
            interaction.agenda.status = StructuredMeetingData.Status.COMPLETED
            interaction.agenda.save()

    interaction.save()


# Given an UploadFile, returns the file type based on the content type or file name.
def _file_type_for_audio_data(audio_data: UploadFile) -> str | None:
    if audio_data.content_type and (content_type := guess_extension(audio_data.content_type)):
        return content_type[1:]
    if audio_data.filename and (extension := os.path.splitext(audio_data.filename)[1]):
        return extension[1:]
    return None


# Updates an ongoing bot recording session for a note.
def _finish_bot_recording_session(note: Note) -> None:
    # Mark the note as uploaded and tell the bot to leave the meeting. This will trigger a webhook
    # that will update the note with the results of the meeting.
    note.status = Note.PROCESSING_STATUS.uploaded
    note.save()
    if not note.bot_uuid:
        logging.error("Note %s does not have a bot UUID, cannot finish recording session", note.uuid)
        return
    MeetingBot.objects.get(uuid=note.bot_uuid).leave_meeting()


# Creates a bot with the given meeting link for the given note.
def _create_bot_for_note(note: Note, meeting_link: str) -> None:
    note.file_path = None
    note.file_type = None
    note.status = Note.PROCESSING_STATUS.scheduled
    note.save()
    bot = MeetingBot.objects.create(note=note, bot_owner=note.note_owner, meeting_link=meeting_link)
    bot.create_bot_and_start_recording()
    logging.info("Bot created for note %s", note.uuid)


# Creates or updates a bot with the given meeting link for the given note.
def _create_or_update_bot_for_note(note: Note, meeting_link: str) -> MeetingBot:
    note.file_path = None
    note.file_type = None
    note.status = Note.PROCESSING_STATUS.scheduled
    note.save()

    bot, created = MeetingBot.objects.get_or_create(note=note, bot_owner=note.note_owner)
    if meeting_link != bot.meeting_link and bot.get_status() != BotStatus.NOT_CREATED:
        logging.error(
            "A meeting bot was already created for this note, with a different meeting link, and "
            "its status indicates that it has already been sent to a meeting. Creating a new bot "
            "and dissociating the old bot from the note.",
        )
        bot.note = None
        bot.save()
        bot = MeetingBot.objects.create(note=note, bot_owner=note.note_owner)
    bot.meeting_link = meeting_link
    bot.save()
    logging.info("Bot %s for note %s", "created" if created else "updated", note.uuid)
    return bot


@router.get("/list")
@public_router.get(
    "/",
    summary="List the notes for a user.",
    description="List the notes for a user. This is stateless; it reads previously-stored state.",
)
async def list_notes(
    not_before: float | None = None,
    not_after: float | None = None,
    q: str | None = None,
    user: User = Depends(user_from_authorization_header),
) -> list[ListNotesResponse]:
    logging.info("Listing notes for user: %s", user.uuid)
    notes = (
        Note.objects.defer(
            "raw_asr_response",
            "raw_transcript",
            "diarized_trans_with_names",
            "advisor_notes",
            "key_takeaways",
            "summary_by_topics",
            "summary",
            "raw_asr_response",
            "follow_up_email_contents",
        )
        .annotate(
            metadata_scheduled_at_text=Func(
                F("metadata"),
                Value("scheduled_at"),
                function="jsonb_extract_path_text",
                output_field=CharField(),
            )
        )
        .annotate(metadata_scheduled_at_dt=Cast("metadata_scheduled_at_text", output_field=DateTimeField()))
        .annotate(
            sort_time=Coalesce(
                F("_scheduled_event__start_time"),
                F("metadata_scheduled_at_dt"),
                F("created"),
                output_field=DateTimeField(),
            )
        )
    )
    # Note that the authorization logic here does not exactly match the logic for which notes a user
    # is allowed to view: superusers can view all notes, but we don't want them to get all notes in
    # the notes list, because that would create a huge load on the system and it is not useful. So,
    # superusers will get a note list that matches their license type.
    if user.license_type == User.LicenseType.csa or user.license_type == User.LicenseType.staff:
        notes = notes.filter(note_owner__organization=user.organization)
    else:
        # Check that the list of authorized users includes the user (and is not empty, as a
        # safeguard against an error where user is None)
        notes = notes.filter(authorized_users__isnull=False, authorized_users=user)
    if q:
        notes = notes.annotate(search=SearchVector("metadata")).filter(search=SearchQuery(q))
    if not_before and not_after:
        notes = notes.filter(sort_time__range=[datetime.fromtimestamp(not_before), datetime.fromtimestamp(not_after)])
    elif not_before:
        notes = notes.filter(sort_time__gte=datetime.fromtimestamp(not_before))
    elif not_after:
        notes = notes.filter(sort_time__lte=datetime.fromtimestamp(not_after))
    notes = (
        notes.order_by("-sort_time")
        .select_related("note_owner", "_scheduled_event")
        .prefetch_related(
            Prefetch("attendees", queryset=Attendee.objects.all().only("note", "uuid", "attendee_name")),
            Prefetch("meeting_type", queryset=MeetingType.objects.only("uuid", "name", "category")),
            Prefetch("meetingbot_set", queryset=MeetingBot.objects.only("uuid", "id", "note_id", "meeting_link")),
        )
    )

    response = []
    async for note in notes:
        try:
            response.append(list_notes_response(note))
        except ValidationError as v:
            logging.warning(f"Error serializing note {note.uuid}: {v}", exc_info=True)
            continue
    return response


@router.get(
    "/{note_id}",
    response_model=NoteResponse,
    responses={
        200: {"description": "Successful response"},
        403: {"description": "Not authorized to view this note"},
        404: {"description": "Note not found"},
        500: {"description": "An error occurred while retrieving the note"},
    },
    summary="Retrieve data of a specific note",
    description="Retrieve details of a specific note by its ID. The user must be authorized to view the note.",
)
@public_router.get(
    "/{note_id}",
    response_model=NoteResponse,
    responses={
        200: {"description": "Successful response"},
        403: {"description": "Not authorized to view this note"},
        404: {"description": "Note not found"},
        500: {"description": "An error occurred while retrieving the note"},
    },
    summary="Retrieve data of a specific note",
    description="Retrieve details of a specific note by its UUID. This is stateless, but returns previously-stored state.",
)
def get_note(
    note_id: uuid.UUID = Path(
        ..., examples=["123e4567-e89b-12d3-a456-************"], description="The UUID of the note to retrieve"
    ),
    user: User = Depends(user_from_authorization_header),
) -> NoteResponse:
    try:
        note = (
            Note.objects.select_related("note_owner", "clientinteraction")
            .prefetch_related(
                Prefetch("meetingbot_set", queryset=MeetingBot.objects.only("uuid", "id", "note_id")),
                Prefetch(
                    "task_set",
                    queryset=Task.objects.only("task_title", "uuid", "completed", "id", "note_id").order_by("created"),
                ),
                Prefetch(
                    "attendees",
                    queryset=Attendee.objects.all().order_by("-speaker_percentage").order_by("attendee_name"),
                ),
                Prefetch("attendees__user", queryset=User.objects.only("uuid", "email", "name")),
                Prefetch("attendees__client", queryset=DBClient.objects.only("uuid", "email", "name")),
                Prefetch("meeting_type", queryset=MeetingType.objects.only("uuid", "name", "category")),
                Prefetch("structuredmeetingdata_set"),
                Prefetch("authorized_users", queryset=User.objects.only("uuid").order_by("username")),
            )
            .get(uuid=note_id)
        )

        if not is_authorized_to_view_note(user, note):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view this note")

        return get_note_response(note, user)
    except Note.DoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Note not found")
    except HTTPException:
        raise
    except Exception as e:
        logging.error("Error viewing note: %s", note_id, exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An error occurred while retrieving the note"
        )


@router.delete(
    "/{note_id}",
    responses={
        204: {"description": "Successful response"},
        403: {"description": "Not authorized to view this note"},
        404: {"description": "Note not found"},
        400: {"description": "An error occurred while deleting the note"},
    },
    summary="Delete a specific note",
    description="Delete a specific note by its ID. The user must be authorized to view the note.",
)
@transaction.atomic
def delete_note(
    note_id: uuid.UUID = Path(
        ..., examples=["123e4567-e89b-12d3-a456-************"], description="The UUID of the note to delete"
    ),
    user: User = Depends(user_from_authorization_header),
) -> Response:
    try:
        note = Note.objects.select_related("note_owner__organization").get(uuid=note_id)

        if not is_authorized_to_view_note(user, note):
            raise HTTPException(status_code=403, detail="Not authorized to delete this note")

        if event := note.scheduled_event:
            event.removed_by_user = True
            event.note = None
            event.save()

        note.task_set.update(is_deleted=True)

        note.is_deleted = True
        note.save()

        return Response(status_code=status.HTTP_204_NO_CONTENT)
    except Note.DoesNotExist:
        logging.error(f"Error Deleting. Note with id {note_id} not found")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Note not found")
    except Exception as e:
        logging.error(f"Error deleting note with id {note_id}", exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An error occurred while deleting the note"
        )


@router.post(
    "/{note_id}/email",
    responses={
        204: {"description": "Successful response"},
        403: {"description": "Not authorized to view this note"},
        404: {"description": "Note not found"},
        500: {"description": "An error occurred while emailing the note"},
    },
    summary="Email a specific note",
    description="Email a specific note by its ID. The user must be authorized to view the note.",
)
def email_note(
    note_id: uuid.UUID = Path(
        ..., examples=["123e4567-e89b-12d3-a456-************"], description="The UUID of the note to email"
    ),
    user: User = Depends(user_from_authorization_header),
) -> Response:
    try:
        note = Note.objects.defer("raw_asr_response").select_related("note_owner__organization").get(uuid=note_id)

        if not is_authorized_to_view_note(user, note):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN)

        email_meeting_notes(str(note_id), user)

        return Response(status_code=204)

    except Note.DoesNotExist:
        logging.error(f"Note with id {note_id} not found")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logging.error(f"An error occurred while emailing the note with id {note_id}", exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An error occurred while emailing the note"
        )


@router.get(
    "/{note_id}/email-followup-mailto",
    response_model=MailtoResponse,
    responses={
        200: {"description": "Successful response"},
        403: {"description": "Not authorized to view this note"},
        404: {"description": "Note not found"},
        500: {"description": "An error occurred while generating the mailto link"},
    },
    summary="Generate mailto link for a specific note",
    description="Generate mailto link for a specific note by its ID. The user must be authorized to view the note.",
)
def email_followup_with_mailto_content(
    note_id: uuid.UUID = Path(
        ...,
        examples=["123e4567-e89b-12d3-a456-************"],
        description="The UUID of the note to generate mailto link",
    ),
    template: str = Query("", description="Template UUID for email generation"),
    user: User = Depends(user_from_authorization_header),
) -> MailtoResponse:
    try:
        logging.info(f"Generating mailto link for note with id {note_id}")
        logging.info(f"Template: {template}")
        note = Note.objects.select_related("note_owner__organization").get(uuid=note_id)

        if not is_authorized_to_view_note(user, note):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN)

        return MailtoResponse(
            mailto_link=generate_followup_email_contents(
                str(note_id),
                user=user,
                template_id=template,
            )
        )
    except Note.DoesNotExist:
        logging.error(f"Note with id {note_id} not found")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logging.error(f"Failed to generate mailto link for note with id {note_id}", exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while generating the mailto link",
        )


@router.post(
    "/swap-attendee-aliases",
    responses={
        200: {"description": "Attendee aliases swapped successfully"},
        400: {"description": "Invalid request format or processing error"},
        403: {"description": "Not authorized to modify this note"},
        404: {"description": "Note or attendee not found"},
        500: {"description": "Internal server error"},
    },
    summary="Swap attendee aliases in a note",
    description="Swap or assign speaker aliases between attendees in a meeting note",
)
def swap_attendee_aliases(
    request: SwapAttendeesRequest, user: User = Depends(user_from_authorization_header)
) -> Response:
    try:
        note = Note.objects.get(uuid=request.note_id)
    except Note.DoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Note not found")

    if not is_authorized_to_view_note(user, note):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to modify this note")

    try:
        # Build attendee lookup dictionaries
        attendees = note.attendees.all()
        name_to_attendee = {a.attendee_name: a for a in attendees}
        uuid_to_attendee = {str(a.uuid): a for a in name_to_attendee.values()}
        swapped_attendees = []

        speaker_percentages_and_times = False
        speaker_percentage = {}
        speaker_time: dict[str, float] = {}

        # Process each swap request
        for swap in request.swaps:
            current_attendee = name_to_attendee.get(swap.current_alias)
            new_attendee = uuid_to_attendee.get(swap.new_alias)

            if not current_attendee and not new_attendee:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Both attendees not found: {swap.current_alias}, {swap.new_alias}",
                )

            if current_attendee and new_attendee:
                # Skip if already swapped or same attendee
                if (new_attendee.uuid, current_attendee.uuid) in swapped_attendees or current_attendee == new_attendee:
                    continue

                # Swap attributes
                attrs_to_swap = ["speaker_alias", "speaker_time", "speaker_percentage"]
                for attr in attrs_to_swap:
                    current_value = getattr(current_attendee, attr)
                    setattr(current_attendee, attr, getattr(new_attendee, attr))
                    setattr(new_attendee, attr, current_value)

                current_attendee.save()
                new_attendee.save()
                swapped_attendees.append((current_attendee.uuid, new_attendee.uuid))

            elif not current_attendee and new_attendee:
                # Assignment flow
                try:
                    if not speaker_percentages_and_times:
                        if raw_transcript := note.raw_transcript:
                            parsed_transcript = clean_and_parse_string(raw_transcript)
                            speaker_time, total_time = calculate_speaker_time(parsed_transcript)
                            speaker_percentage = get_speaker_percentage(speaker_time, total_time)
                            speaker_percentages_and_times = True
                        else:
                            logging.warning(
                                "No raw transcript available for speaker time calculation for note %s. Speaker pecentages will not be correct.",
                                note.uuid,
                            )
                except Exception as e:
                    logging.warning("Error calculating speaker time and percentage for note %s", note.uuid, exc_info=e)

                new_attendee.speaker_alias = swap.current_alias
                new_attendee.speaker_percentage = speaker_percentage.get(swap.current_alias)
                new_attendee.speaker_time = (
                    timedelta(seconds=round(speaker_time.get(swap.current_alias)))  # type: ignore[arg-type]
                    if speaker_time and swap.current_alias in speaker_time
                    else None
                )
                new_attendee.save()
                swapped_attendees.append((new_attendee.uuid, new_attendee.uuid))

        # Update note status and reprocess
        note.task_set.all().delete()
        note.status = Note.PROCESSING_STATUS.uploaded
        note.save()

        speaker_mapping = {
            attendee.speaker_alias: attendee.attendee_name
            for attendee in note.attendees.all()
            if attendee.speaker_alias and attendee.attendee_name
        }

        # Queue reprocessing task
        reprocess_note_after_swapping.delay_on_commit(note.uuid, speaker_mapping=speaker_mapping)

        return Response(status_code=200)

    except Exception as e:
        logging.error("Error swapping attendee aliases:", exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while swapping attendee aliases",
        )


@router.post(
    "/{note_id}",
    responses={
        204: {"description": "Successful response"},
        403: {"description": "Not authorized to view this note"},
        404: {"description": "Note not found"},
        500: {"description": "An error occurred while editing the note"},
    },
    summary="Edit a specific note",
    description="Edit a specific note by its ID and changed note content. The user must be authorized to view the note.",
)
def edit_note(
    note_id: uuid.UUID = Path(
        ..., examples=["123e4567-e89b-12d3-a456-************"], description="The UUID of the note to edit"
    ),
    edit_request: EditNoteRequest = Body(...),
    user: User = Depends(user_from_authorization_header),
) -> Response:
    try:
        note = (
            Note.objects.defer("raw_asr_response", "diarized_trans_with_names", "summary", "summary_by_topics")
            .select_related("note_owner__organization")
            .get(uuid=note_id)
        )
    except Note.DoesNotExist:
        logging.error("Note with id %s not found", note_id)
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Note not found")

    if not is_authorized_to_view_note(user, note):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN)

    if note.status == Note.PROCESSING_STATUS.finalized:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Cannot edit a finalized note")

    try:
        # Update note fields if they are present in the request
        if edit_request.meetingName is not None:
            note.metadata = {**(note.metadata or {}), "meeting_name": edit_request.meetingName}
        if edit_request.keyTakeaways is not None:
            note.key_takeaways = edit_request.keyTakeaways
        if edit_request.advisorNotes is not None:
            note.advisor_notes = edit_request.advisorNotes
        if edit_request.client is not None:
            note.client = edit_request.client.model_dump()
        if edit_request.summary is not None:
            note.summary = edit_request.summary.to_dict()
        if edit_request.actionItems is not None:
            _process_action_items(note, edit_request.actionItems, user)
        if edit_request.attendees is not None:
            Attendee.reconcile_attendees(note, [a.model_dump(mode="json") for a in edit_request.attendees])

        note.save()

        logging.info("Note %s updated", note.uuid)
        return Response(status_code=status.HTTP_204_NO_CONTENT)

    except Exception as e:
        logging.error("Error editing note: %s", str(e), exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An error occurred while editing the note"
        )


def _process_action_items(note: Note, action_items: list[ActionItemUpdate], user: User) -> None:
    """
    Process note action items by creating, updating or deleting associated tasks.

    Each action item can specify one of three actions:
    - "delete": Removes the task with the matching UUID
    - "create": Creates a new task if one doesn't exist, or updates an existing one
    - "keep": Same behavior as "create", updates existing task or creates new one

    For new tasks, the due date is set based on the user's preferences offset from creation time.
    Existing tasks maintain their original due dates.

    Args:
        note (Note): Parent note object
        action_items (list[ActionItem]): Action items to process, containing action, uuid,
            status and content
        user (User): User performing the action
    """
    if not action_items:
        Task.objects.filter(note=note).delete()
        return

    for update in action_items:
        if update.action == ActionType.DELETE:
            Task.objects.filter(uuid=update.item.uuid, note=note).delete()
        elif update.action in [ActionType.CREATE, ActionType.KEEP]:
            task, created = Task.objects.get_or_create(
                uuid=update.item.uuid,
                defaults={"note": note, "task_owner": user},
            )
            task.completed = update.item.status == "complete"
            task.task_title = update.item.content
            if created:
                user_preferences = user.get_preferences()
                task.due_date = task.created + timedelta(seconds=user_preferences.due_date_offset_seconds)
            task.save()


@router.post(
    "/{note_id}/update-authorized-users",
    responses={
        204: {"description": "Successful response"},
        403: {"description": "Not authorized to view this note"},
        404: {"description": "Note not found"},
        500: {"description": "An error occurred while updating authorized users"},
    },
)
def update_authorized_users(
    note_id: uuid.UUID = Path(
        examples=["123e4567-e89b-12d3-a456-************"], description="The UUID of the note to edit"
    ),
    authorized_users: list[uuid.UUID] = Body(description="List of UUIDs of authorized users"),
    user: User = Depends(user_from_authorization_header),
) -> Response:
    try:
        note = Note.objects.prefetch_related("authorized_users").get(uuid=note_id)
    except Note.DoesNotExist:
        logging.error("Note with id %s not found", note_id)
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Note not found")

    if not is_authorized_to_view_note(user, note):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view this note")

    try:
        users = User.objects.filter(Q(uuid__in=[user.uuid]) | Q(uuid__in=authorized_users))
        note.authorized_users.set(users)
        note.save()

        logging.info("Authorized users for note %s updated", note.uuid)
        return Response(status_code=status.HTTP_204_NO_CONTENT)

    except Exception as e:
        logging.error("Error updating authorized users", exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while updating authorized users",
        )


@router.post(
    "/{note_id}/add_section/",
    response_model=SearchAddSectionResponse,
    responses={
        200: {"description": "Successfully added section to summary"},
        403: {"description": "Not authorized to view this note"},
        404: {"description": "Note not found"},
        500: {"description": "An error occurred while adding section to summary"},
    },
)
async def add_section_to_summary(
    note_id: uuid.UUID,
    request: SearchAddSectionRequest,
    user: User = Depends(user_from_authorization_header),
) -> SearchAddSectionResponse:
    try:
        note = (
            await Note.objects.select_related("note_owner", "note_owner__organization")
            .prefetch_related(Prefetch("authorized_users", queryset=User.objects.only("uuid")))
            .defer(
                "raw_asr_response",
                "raw_transcript",
                "diarized_trans_with_names",
                "advisor_notes",
                "key_takeaways",
                "raw_asr_response",
                "follow_up_email_contents",
            )
            .aget(uuid=note_id)
        )
    except Note.DoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Note not found")

    is_authorized = is_authorized_to_view_note(user, note)
    if not is_authorized:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view this note")
    try:
        search_query_id = request.search_query_id

        query = await SearchQueryModel.objects.aget(uuid=search_query_id)
        summary_section = SummarySection.model_validate(query.structured_response)

        section_index = await note.add_to_summary(summary_section, request.section_to_replace)

        return SearchAddSectionResponse(section_index=section_index)
    except Exception as e:
        logging.error("An error occurred while adding section to summary", exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while adding section to summary",
        )


@router.post("/{note_id}/upload_audio_chunk", status_code=status.HTTP_201_CREATED)
async def upload_audio_chunk(
    note_id: uuid.UUID,
    audio_data: UploadFile,
    sequence: int = Form(..., description="The sequence number of the audio chunk"),
    duration: int | None = Form(..., description="The duration of the audio chunk in seconds"),
    mime_type: str | None = Form(..., description="The MIME type of the audio data"),
    nonce: str | None = Form(..., description="A nonce to differentiate between different audio uploads."),
    user: User = Depends(user_from_authorization_header),
) -> Response:
    try:
        note = (
            await Note.objects.select_related("note_owner", "note_owner__organization")
            .prefetch_related(Prefetch("authorized_users", queryset=User.objects.only("uuid").order_by("username")))
            .defer(
                "raw_asr_response",
                "raw_transcript",
                "diarized_trans_with_names",
                "advisor_notes",
                "key_takeaways",
                "raw_asr_response",
                "summary_by_topics",
                "summary",
                "follow_up_email_contents",
            )
            .aget(uuid=note_id)
        )
    except Note.DoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
    if not is_authorized_to_view_note(user, note):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN)

    try:
        data = await audio_data.read()
        # Check if there is already a chunk with this sequence number and nonce. If there is and it's
        # different from the chunk that was sent in the request, raise an error.
        if buffer := await AudioBuffer.objects.filter(note=note, sequence=sequence, nonce=nonce).afirst():
            if bytes(buffer.data) != data:
                logging.error("Audio chunk %s for note %s already exists with different data.", sequence, note_id)
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, detail="Audio chunk already exists with different data."
                )
            return Response(status_code=status.HTTP_200_OK)
        await AudioBuffer.objects.acreate(
            note=note, sequence=sequence, data=data, duration=duration, mime_type=mime_type, nonce=nonce
        )
        return Response(status_code=status.HTTP_201_CREATED)
    except HTTPException:
        raise
    except Exception as e:
        logging.error("Error saving audio chunk %s for note %s", sequence, note_id, exc_info=e)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
