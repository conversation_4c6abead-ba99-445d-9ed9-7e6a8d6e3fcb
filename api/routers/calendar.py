import datetime
import html
import logging
from uuid import UUID

import pydantic
import pytz
from asgiref.sync import sync_to_async
from django.conf import settings
from django.core.cache import cache
from fastapi import APIRouter, Body, Depends, HTTPException, Query, status
from opentelemetry import metrics

from api.dependencies import user_from_authorization_header
from api.routers.calendar_models import CalendarLookahead
from deepinsights.core.integrations.calendar.calendar_data_parser import (
    extract_crm_linked_entity_info,
    should_ignore_event_by_keywords,
)
from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent as ModelCalendarEvent
from deepinsights.core.integrations.calendar.calendar_models import EventParticipant, EventParticipantsList
from deepinsights.core.integrations.calendar.events_fetcher import fetch_calendar_events
from deepinsights.core.integrations.calendar.participant_utils import events_with_zeplyn_attendees
from deepinsights.core.integrations.meetingbot.recall_ai import RecallBotController
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent as DBScheduledEvent
from deepinsights.meetingsapp.tasks import reconcile_calendar_events, update_autojoin_bots
from deepinsights.users.models.user import User

router = APIRouter(
    tags=["calendar"],
    generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}",
)

public_router = APIRouter(
    tags=["events"],
    generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}",
)

# The default lookahead interval.
#
# Note that the implementation of _time_interval_for_lookahead() makes an assumption that this is
# the default lookahead interval, but in a way that is not easily represented in a constant. Be sure
# to adjust that function if this changes.
DEFAULT_LOOKAHEAD = CalendarLookahead.END_OF_NEXT_WEEK


# Get the current datetime in the given timezone.
def _now() -> datetime.datetime:
    return datetime.datetime.now(datetime.timezone.utc)


# Given a timezone offset and a lookahead value, compute the time interval required to fetch events
# matching that lookahead.
def _time_interval_for_lookahead(time_zone: str, lookahead: CalendarLookahead) -> datetime.timedelta:
    timezone_timedelta = pytz.timezone(time_zone).utcoffset(datetime.datetime.now())
    timezone_offset = int(timezone_timedelta.total_seconds() // 60) if timezone_timedelta else 0
    timestamp = _now().astimezone(datetime.timezone(datetime.timedelta(minutes=timezone_offset)))

    timestamp_midnight = timestamp.replace(hour=0, minute=0, second=0, microsecond=0)

    beginning_of_tomorrow = timestamp_midnight + datetime.timedelta(days=1) - timestamp
    beginning_of_next_week = (
        timestamp_midnight
        + datetime.timedelta(weeks=1)
        - datetime.timedelta(days=timestamp.isoweekday() % 7)
        - timestamp
    )
    beginning_of_next_next_week = beginning_of_next_week + datetime.timedelta(weeks=1)

    lookahead_map = {
        CalendarLookahead.ONE_HOUR: datetime.timedelta(hours=1),
        CalendarLookahead.END_OF_DAY: beginning_of_tomorrow,
        CalendarLookahead.ONE_DAY: datetime.timedelta(days=1),
        CalendarLookahead.TWO_DAYS: datetime.timedelta(days=2),
        CalendarLookahead.END_OF_WEEK: beginning_of_next_week,
        CalendarLookahead.END_OF_NEXT_WEEK: beginning_of_next_next_week,
        CalendarLookahead.THIRTY_DAYS: datetime.timedelta(days=30),
    }
    return lookahead_map.get(lookahead, beginning_of_next_next_week)


# The API version of the calendar event.
class CalendarEvent(pydantic.BaseModel):
    class LinkedCRMEntity(pydantic.BaseModel):
        # The ID of the linked CRM entity.
        id: str

        # The name of the linked CRM entity.
        name: str

    # A string that indicates what calendar service this event is from (e.g., "google", "microsoft")
    provider: str

    # A unique identifier for the event, generated by the calendar service. This will not be
    # comparable across different calendar services.
    id: str

    # An identifier for this event, specific to the calendar being used. This may be the same as the
    # ID, but it will not necessarily be if the calendar provider uses different user-specific
    # identifiers for shared calendar events.
    user_specific_id: str

    # The title/name/brief summary of the event.
    title: str

    # Start time
    #
    # If this is an all-day event (i.e., all_day is True), this will be set to midnight UTC of the
    # start date of the event. If this is not an all-day event (i.e. all_day is False), even if the
    # start and end time are separated by one day, then this is an event that starts and ends at the
    # exact timezone-aware times specified.
    start_time: datetime.datetime

    # End time
    #
    # If this is an all-day event (i.e., all_day is True), this will be set to midnight UTC of the
    # day after the event ends (i.e., the event ends at 11:59:59... the date before). If this is not
    # an all-day event (i.e. all_day is False), even if the start and end time are separated by one
    # day, then this is an event that starts and ends at the exact timezone-aware times specified.
    end_time: datetime.datetime

    # Whether this is an all-day event.
    all_day: bool

    # The participants in the event
    participants: list[EventParticipant]

    # URLs that can be used to join the meeting.
    meeting_urls: list[pydantic.OnErrorOmit[pydantic.HttpUrl]]

    # Info about a linked entity in a CRM that is associated with the event, if any.
    linked_crm_entity: LinkedCRMEntity | None = None


_meter = metrics.get_meter(__name__)
_cache_counter = _meter.create_counter(
    "calendar_events.cache_hit_count",
    description="Count of the number of cache hits for the calendar event cache",
)


@router.get(
    "/list_events",
    response_model=list[CalendarEvent],
    responses={
        400: {"description": "Not found"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal Server Error"},
    },
)
@public_router.get(
    "/",
    response_model=list[CalendarEvent],
    responses={
        400: {"description": "Not found"},
        401: {"description": "Unauthorized"},
        500: {"description": "Internal Server Error"},
    },
    summary="Lists calendar events for a user",
    description=(
        "Lists calendar events for a user. The response includes the events that are "
        "scheduled in the user's calendar. This references data previously stored in "
        "the database, but does not store additional data."
    ),
)
async def list_events(
    time_zone: str = Query(description="The client timezone. Expected to be an IANA timezone string."),
    user: User = Depends(user_from_authorization_header),
) -> list[CalendarEvent]:
    lookahead_setting = CalendarLookahead(user.calendar_lookahead) if user.calendar_lookahead else DEFAULT_LOOKAHEAD
    lookahead = _time_interval_for_lookahead(time_zone, lookahead_setting)

    cache_key = f"api_routers_calendar_list_events_{user.uuid}_{lookahead_setting}"
    timeout = settings.CALENDAR_EVENT_CACHE_TTL_SECONDS
    if timeout and (cached_events := cache.get(cache_key)):
        _cache_counter.add(1)
        return cached_events  # type: ignore[no-any-return]

    events: list[CalendarEvent] = []
    all_succeeded = False
    try:
        user_preferences = await sync_to_async(lambda: user.get_preferences())()
        auto_join_ignored_keywords_list = (
            user_preferences.calendar_preferences.auto_join_ignored_keywords
            if user_preferences.calendar_preferences
            else []
        ) or []

        events_models, all_succeeded = await fetch_calendar_events(user, lookahead)
        for event in events_models:
            if should_ignore_event_by_keywords(event.title, auto_join_ignored_keywords_list):
                continue

            linked_crm_entity_info = extract_crm_linked_entity_info(html.unescape(event.body)) if event.body else None
            if linked_crm_entity_info and not linked_crm_entity_info["name"]:
                if client := await Client.objects.filter(
                    authorized_users__isnull=False, authorized_users=user, crm_id=linked_crm_entity_info["id"]
                ).afirst():
                    linked_crm_entity_info["name"] = client.name
                else:
                    linked_crm_entity_info["name"] = event.title

            events.append(
                CalendarEvent(
                    provider=event.provider,
                    id=event.id,
                    user_specific_id=event.user_specific_id,
                    title=event.title,
                    start_time=event.start_time,
                    end_time=event.end_time,
                    all_day=event.all_day,
                    participants=event.participants,
                    meeting_urls=event.meeting_urls,
                    linked_crm_entity=CalendarEvent.LinkedCRMEntity(
                        name=linked_crm_entity_info["name"], id=linked_crm_entity_info["id"]
                    )
                    if linked_crm_entity_info
                    else None,
                )
            )
    except Exception as e:
        logging.error("Error fetching calendar events", exc_info=e)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)

    if not user.show_events_without_meeting_urls:
        events = [event for event in events if event.meeting_urls]

    attendee_matched_events = await sync_to_async(events_with_zeplyn_attendees)(events, user)

    if all_succeeded:
        cache.add(cache_key, attendee_matched_events, timeout=timeout)

    return attendee_matched_events


class ScheduledEvent(CalendarEvent):
    scheduled_event_uuid: UUID
    autojoin_available: bool
    autojoin_enabled: bool
    autojoin_editable: bool


@router.get(
    "/events",
    response_model=list[ScheduledEvent],
    responses={
        401: {"description": "Unauthorized"},
        500: {"description": "Internal Server Error"},
    },
)
def scheduled_calendar_events(
    time_zone: str = Query(description="The client timezone. Expected to be an IANA timezone string."),
    user: User = Depends(user_from_authorization_header),
) -> list[ScheduledEvent]:
    lookahead_setting = CalendarLookahead(user.calendar_lookahead) if user.calendar_lookahead else DEFAULT_LOOKAHEAD
    lookahead = _time_interval_for_lookahead(time_zone, lookahead_setting)

    reconcile_calendar_events.delay_on_commit(user.uuid)

    events: list[ScheduledEvent] = []

    try:
        # Backfill the user's Recall calendar provider if it is not set.
        if user.recall_calendar_id and not user.recall_calendar_platform:
            user.recall_calendar_platform = RecallBotController.connected_calendar_platform(user.recall_calendar_id)
            user.save()
    except Exception as e:
        logging.error("Error updating user Recall calendar provider for the user", exc_info=e)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)

    user_preferences = user.get_preferences()
    auto_join_ignored_keywords_list = (
        user_preferences.calendar_preferences.auto_join_ignored_keywords
        if user_preferences.calendar_preferences
        else []
    ) or []

    # Find scheduled events that are within the lookahead period that:
    # - are from a provider (i.e., have a user-specific source ID)
    # - have not been deleted by the user or removed from the provider
    for scheduled_event in DBScheduledEvent.objects.filter(
        user=user,
        user_specific_source_id__isnull=False,
        end_time__gte=_now(),
        start_time__lte=_now() + lookahead,
        removed_by_user=False,
        removed_from_provider=False,
    ).order_by("start_time", "end_time", "uuid"):
        try:
            event = ModelCalendarEvent.model_validate(scheduled_event.source_data)
        except pydantic.ValidationError:
            logging.error("Error validating calendar event", exc_info=True)
            continue

        # Attempt to populate participants from the cached info on the scheduled event. If that
        # fails, then fall back to computing the participants in-line.
        attendee_matched_participants: list[EventParticipant] = []
        try:
            if scheduled_event.attendee_matched_participants:
                attendee_matched_participants = EventParticipantsList.model_validate(
                    scheduled_event.attendee_matched_participants
                ).root
        except pydantic.ValidationError:
            logging.error(
                "Error validating attendee matched participants. Falling back to in-line matching.", exc_info=True
            )
        finally:
            if not attendee_matched_participants:
                attendee_matched_participants = events_with_zeplyn_attendees([event], user)[0].participants

        if should_ignore_event_by_keywords(event.title, auto_join_ignored_keywords_list):
            continue

        linked_crm_entity_info = extract_crm_linked_entity_info(html.unescape(event.body)) if event.body else None
        if linked_crm_entity_info and not linked_crm_entity_info["name"]:
            if client := Client.objects.filter(
                authorized_users__isnull=False, authorized_users=user, crm_id=linked_crm_entity_info["id"]
            ).first():
                linked_crm_entity_info["name"] = client.name
            else:
                linked_crm_entity_info["name"] = event.title

        events.append(
            ScheduledEvent(
                provider=event.provider,
                id=event.id,
                user_specific_id=event.user_specific_id,
                title=event.title,
                start_time=event.start_time,
                end_time=event.end_time,
                all_day=event.all_day,
                participants=attendee_matched_participants,
                meeting_urls=event.meeting_urls,
                linked_crm_entity=CalendarEvent.LinkedCRMEntity(
                    name=linked_crm_entity_info["name"], id=linked_crm_entity_info["id"]
                )
                if linked_crm_entity_info
                else None,
                scheduled_event_uuid=scheduled_event.uuid,
                autojoin_available=scheduled_event.is_recall_autojoin_available(user),
                autojoin_enabled=scheduled_event.is_recall_autojoin_enabled(user),
                autojoin_editable=scheduled_event.start_time > _now(),
            )
        )

    if not user.show_events_without_meeting_urls:
        events = [event for event in events if event.meeting_urls]

    return events


@router.post("/update_autojoin")
def update_autojoin(
    scheduled_event_uuid: UUID = Body(..., description="The UUID of the scheduled event to update."),
    auto_join: bool = Body(..., description="Whether to enable or disable auto join."),
    user: User = Depends(user_from_authorization_header),
) -> None:
    try:
        scheduled_event = DBScheduledEvent.objects.get(uuid=scheduled_event_uuid, user=user)
    except DBScheduledEvent.DoesNotExist:
        raise HTTPException(status_code=404, detail="Scheduled event not found")
    scheduled_event.autojoin_behavior = (
        DBScheduledEvent.AutoJoinOverride.ENABLED if auto_join else DBScheduledEvent.AutoJoinOverride.DISABLED
    )
    scheduled_event.save()
    update_autojoin_bots.delay_on_commit(user.uuid, [scheduled_event_uuid], update_recall=True)
