import logging
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, OnErrorOmit

from api.dependencies import user_from_authorization_header
from api.routers.note_models import AttendeeInfo, AttendeeType
from deepinsights.core.preferences.preferences import Default<PERSON><PERSON><PERSON><PERSON>er
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User
from deepinsights.utils.phone_number import PhoneNumberE164OrNone

router = APIRouter(tags=["attendees"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}")
logger = logging.getLogger(__name__)


class UserResponse(BaseModel):
    uuid: UUID
    name: str
    role: str | None = None
    phone_number: PhoneNumberE164OrNone = None


class ClientResponse(BaseModel):
    uuid: UUID
    name: str
    crm_id: str | None = None
    job_title: str | None = None
    client_type: str | None = None
    phone_number: PhoneNumberE164OrNone = None


class ListAttendeesResponse(BaseModel):
    users: list[OnErrorOmit[UserResponse]]
    clients: list[OnErrorOmit[ClientResponse]]


@router.get(
    "/attendee_options/{user_id}",
    response_model=ListAttendeesResponse,
    responses={
        401: {"description": "Not authorized"},
        404: {"description": "User not found"},
        500: {"description": "Internal server error"},
    },
)
def list_attendee_options(
    user_id: UUID, q: str = "", user: User = Depends(user_from_authorization_header)
) -> ListAttendeesResponse:
    try:
        target_user = User.objects.get(uuid=user_id)
    except User.DoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    if target_user != user and not user.is_superuser:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Not authorized")

    try:
        users_queryset = (
            User.objects.filter(organization=target_user.organization)
            .exclude(name__isnull=True)
            .exclude(name__exact="")
            .prefetch_related("phone_numbers")
            .order_by("name")
        )
        users_list: list[UserResponse] = []
        for u in users_queryset:
            users_list.append(UserResponse(uuid=u.uuid, name=u.name, role=u.role, phone_number=u.primary_phone_number))

        clients_queryset = (
            Client.objects.filter(
                authorized_users__isnull=False,
                authorized_users=target_user,
                name__contains=q,
                crm_system=target_user.get_crm_configuration().crm_system,
            )
            .exclude(name__exact="")
            .exclude(crm_id__isnull=True)
            .exclude(crm_id__exact="")
        )
        match target_user.get_crm_configuration().default_client_filter:
            case DefaultClientFilter.ALL:
                pass
            case DefaultClientFilter.OWNED:
                clients_queryset = clients_queryset.filter(owner=target_user)
        clients_values_queryset = clients_queryset.values(
            "uuid", "name", "crm_id", "job_title", "client_type", "phone_number"
        ).order_by("name")
        for client in clients_values_queryset:
            if phone_number := client.get("phone_number"):
                client["phone_number"] = phone_number

        return ListAttendeesResponse(
            users=users_list,
            clients=[ClientResponse(**client_dict) for client_dict in clients_values_queryset],
        )

    except Exception as e:
        logger.error("Error listing attendees", exc_info=e)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/")
def list_attendees(user: User = Depends(user_from_authorization_header)) -> list[AttendeeInfo]:
    notes = Note.objects.all()

    if user.license_type == User.LicenseType.csa or user.license_type == User.LicenseType.staff:
        notes = notes.filter(note_owner__organization=user.organization)
    else:
        # Check that the list of authorized users includes the user (and is not empty, as a
        # safeguard against an error where user is None)
        notes = notes.filter(authorized_users__isnull=False, authorized_users=user)

    return [
        AttendeeInfo(
            uuid=attendee.uuid,
            name=attendee.attendee_name or "",
            type=AttendeeType.CLIENT
            if attendee.client
            else AttendeeType.USER
            if attendee.user
            else AttendeeType.UNKNOWN,
            client_uuid=attendee.client.uuid if attendee.client else None,
            user_uuid=attendee.user.uuid if attendee.user else None,
            speaker_time=attendee.speaker_time,
            speaker_percentage=attendee.speaker_percentage,
            speaker_alias=attendee.speaker_alias,
        )
        for attendee in Attendee.objects.filter(note__in=notes).select_related("client", "user").order_by("created")
    ]
