from datetime import datetime, timezone
from typing import Any
from unittest.mock import <PERSON><PERSON><PERSON><PERSON>, MagicMock, patch

import pytest
from fastapi import HTT<PERSON>Exception
from pydantic import ValidationError

from api.routers.onboarding_models import (
    BotPreferencesRequest,
    EmailConfigurationRequest,
    OnboardingRequest,
    OrganizationData,
    PreferencesRequest,
    UserData,
    _get_or_create_organization,
    _get_users_for_emails,
    _group_users_by_org,
    _prepare_csv_data,
    _prepare_preferences,
    _process_csv,
    _send_batch_emails,
    _update_existing_users,
    process_organization_creation,
)
from deepinsights.core.preferences.preferences import (
    BotPreferences,
    EmailConfiguration,
    get_default_preferences,
)
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.user import User

pytestmark = [pytest.mark.django_db(transaction=True)]


@pytest.fixture
def mock_get_default_preferences() -> dict[str, Any]:
    default_prefs = get_default_preferences()
    return default_prefs


class TestPydanticModels:
    def test_bot_preferences_request_model(self) -> None:
        data = {
            "notetaker_name": "Assistant",
            "recording_image_b64": "base64string",
            "not_recording_image_b64": "base64string",
            "enable_video": True,
            "enable_audio_output": False,
        }
        model = BotPreferencesRequest.model_validate(data)
        assert model.notetaker_name == "Assistant"
        assert model.recording_image_b64 == "base64string"
        assert model.not_recording_image_b64 == "base64string"
        assert model.enable_video is True
        assert model.enable_audio_output is False

    def test_bot_preferences_request_default_values(self) -> None:
        model = BotPreferencesRequest()
        assert model.notetaker_name is None
        assert model.recording_image_b64 is None
        assert model.not_recording_image_b64 is None
        assert model.recording_message_b64 is None
        assert model.enable_video is None
        assert model.enable_audio_output is None

    def test_email_configuration_request_model(self) -> None:
        data = {
            "ccs": ["<EMAIL>", "<EMAIL>"],
            "bccs": ["<EMAIL>"],
            "followup_email_format_prompt": "Follow up prompt",
            "meeting_notes_email_template": "Template",
        }
        model = EmailConfigurationRequest.model_validate(data)
        assert model.ccs == ["<EMAIL>", "<EMAIL>"]
        assert model.bccs == ["<EMAIL>"]
        assert model.followup_email_format_prompt == "Follow up prompt"
        assert model.meeting_notes_email_template == "Template"

    def test_preferences_request_model(self) -> None:
        data = {
            "attach_transcript_to_follow_up_emails": True,
            "show_transcript_in_frontend": True,
            "send_task_reminder_email": False,
            "delete_buffer": True,
            "due_date_offset_seconds": 86400,
            "asr_language_code": "en-US",
            "email_settings": {
                "ccs": ["<EMAIL>"],
                "bccs": [],
            },
            "bot_preferences": {
                "notetaker_name": "Assistant",
                "enable_video": True,
            },
        }
        model = PreferencesRequest.model_validate(data)
        assert model.attach_transcript_to_follow_up_emails is True
        assert model.show_transcript_in_frontend is True
        assert model.send_task_reminder_email is False
        assert model.delete_buffer is True
        assert model.due_date_offset_seconds == 86400
        assert model.asr_language_code == "en-US"

        assert isinstance(model.email_settings, EmailConfigurationRequest)
        assert model.email_settings.ccs == ["<EMAIL>"]

        assert isinstance(model.bot_preferences, BotPreferencesRequest)
        assert model.bot_preferences.notetaker_name == "Assistant"
        assert model.bot_preferences.enable_video is True

    def test_onboarding_request_model(self) -> None:
        data = {
            "organization": {
                "name": "Test Org",
                "description": "Test Desc",
                "transcript_ttl": 30,
            },
            "users": [
                {
                    "email": "<EMAIL>",
                    "first_name": "User",
                    "last_name": "One",
                    "type": "admin",
                }
            ],
            "api_key": "test_api_key",
        }
        model = OnboardingRequest.model_validate(data)
        assert model.organization.name == "Test Org"
        assert model.organization.description == "Test Desc"
        assert model.organization.transcript_ttl == 30

        assert len(model.users) == 1
        assert model.users[0].email == "<EMAIL>"
        assert model.users[0].first_name == "User"
        assert model.users[0].last_name == "One"
        assert model.users[0].type == "admin"

        assert model.api_key == "test_api_key"

    def test_onboarding_request_invalid_email(self) -> None:
        data = {
            "organization": {
                "name": "Test Org",
            },
            "users": [
                {
                    "email": "invalid_email",
                    "first_name": "User",
                    "last_name": "One",
                    "type": "admin",
                }
            ],
        }
        with pytest.raises(ValidationError):
            OnboardingRequest.model_validate(data)

    def test_onboarding_request_missing_required_fields(self) -> None:
        """Test validation for missing required fields in OnboardingRequest."""
        data = {
            "organization": {
                "description": "Test Desc",
            },
            "users": [
                {
                    "email": "<EMAIL>",
                    "first_name": "User",
                    "last_name": "One",
                }
            ],
        }
        with pytest.raises(ValidationError):
            OnboardingRequest.model_validate(data)

        data = {
            "organization": {
                "name": "Test Org",
            },
            "users": [
                {
                    "email": "<EMAIL>",
                    "last_name": "One",
                }
            ],
        }
        with pytest.raises(ValidationError):
            OnboardingRequest.model_validate(data)


class TestModelConversionMethods:
    """Tests for model conversion methods."""

    def test_bot_preferences_request_update_dataclass(self) -> None:
        request = BotPreferencesRequest(
            notetaker_name="Assistant",
            recording_image_b64="base64string",
            not_recording_image_b64="base64string",
            recording_message_b64="message",
            enable_video=True,
            enable_audio_output=False,
        )

        original = BotPreferences()
        assert original.notetaker_name == ""

        updated = request.update_dataclass(original)

        assert updated.notetaker_name == "Assistant"
        assert updated.recording_image_b64 == "base64string"
        assert updated.not_recording_image_b64 == "base64string"
        assert updated.recording_message_b64 == "message"
        assert updated.enable_video is True
        assert updated.enable_audio_output is False

    def test_email_configuration_request_update_dataclass(self) -> None:
        request = EmailConfigurationRequest(
            ccs=["<EMAIL>"],
            bccs=["<EMAIL>"],
            followup_email_format_prompt="Follow up prompt",
            meeting_notes_email_template="Template",
        )

        original = EmailConfiguration()

        updated = request.update_dataclass(original)

        assert updated.ccs == ["<EMAIL>"]
        assert updated.bccs == ["<EMAIL>"]
        assert updated.followup_email_format_prompt == "Follow up prompt"
        assert updated.meeting_notes_email_template == "Template"

    def test_preferences_request_to_dataclass(self) -> None:
        request = PreferencesRequest(
            attach_transcript_to_follow_up_emails=True,
            show_transcript_in_frontend=True,
            send_task_reminder_email=False,
            delete_buffer=True,
            due_date_offset_seconds=86400,
            asr_language_code="en-US",
            email_settings=EmailConfigurationRequest(
                ccs=["<EMAIL>"],
                bccs=[],
            ),
            bot_preferences=BotPreferencesRequest(
                notetaker_name="Assistant",
                enable_video=True,
            ),
        )

        result = request.to_dataclass()

        assert result.attach_transcript_to_follow_up_emails is True
        assert result.show_transcript_in_frontend is True
        assert result.send_task_reminder_email is False
        assert result.delete_buffer is True
        assert result.due_date_offset_seconds == 86400
        assert result.asr_language_code == "en-US"

        # Check nested objects
        assert result.email_settings.ccs == ["<EMAIL>"]
        assert result.email_settings.bccs == []

        assert result.bot_preferences.notetaker_name == "Assistant"
        assert result.bot_preferences.enable_video is True


class TestHelperFunctions:
    def test_prepare_preferences_with_data(self) -> None:
        prefs_request = PreferencesRequest(
            attach_transcript_to_follow_up_emails=True,
            show_transcript_in_frontend=True,
            asr_language_code="en-US",
        )

        result = _prepare_preferences(prefs_request)

        assert "delete_buffer" in result
        assert "bot_preferences" in result

        assert result["attach_transcript_to_follow_up_emails"] is True
        assert result["show_transcript_in_frontend"] is True
        assert result["asr_language_code"] == "en-US"

    def test_prepare_preferences_without_data(self) -> None:
        """Test _prepare_preferences returns default preferences when no data provided."""
        default_prefs = get_default_preferences()
        result = _prepare_preferences(None)

        assert result == default_prefs

    def test_get_or_create_organization_create_new(self) -> None:
        """Test _get_or_create_organization creates a new organization."""
        data = OrganizationData(
            name="New Org",
            description="New Description",
            transcript_ttl=45,
        )
        preferences = {"key": "value"}

        org = _get_or_create_organization(data, preferences)

        assert org.name == "New Org"
        assert org.description == "New Description"
        assert org.transcript_ttl == 45
        assert org.preferences == preferences

    def test_get_or_create_organization_existing_not_found(self) -> None:
        data = OrganizationData(
            name="Non-existent Org",
            existing=True,
            id="non-existent-id",
        )
        preferences = {"key": "value"}

        with pytest.raises(ValueError):
            _get_or_create_organization(data, preferences)

    def test_get_or_create_organization_existing_with_partial_update(self) -> None:
        existing_org = Organization.objects.create(
            name="Existing Org",
            description="Original Description",
            transcript_ttl=30,
            preferences={"existing_key": "existing_value"},
        )

        data = OrganizationData(
            name="Existing Org",
            transcript_ttl=60,
            existing=True,
            id=str(existing_org.id),
        )
        preferences = {"new_key": "new_value"}

        updated_org = _get_or_create_organization(data, preferences)

        assert updated_org.id == existing_org.id
        assert updated_org.description == "Original Description"  # Unchanged
        assert updated_org.transcript_ttl == 60  # Updated

        assert "existing_key" in updated_org.preferences  # type: ignore[operator]
        assert "new_key" in updated_org.preferences  # type: ignore[operator]
        assert updated_org.preferences["existing_key"] == "existing_value"  # type: ignore[index]
        assert updated_org.preferences["new_key"] == "new_value"  # type: ignore[index]

    @patch("django.utils.timezone.now")
    def test_get_or_create_organization_with_generated_description(self, mock_now: MagicMock) -> None:
        data = OrganizationData(
            name="New Org Without Description",
            transcript_ttl=45,
        )
        preferences = {"key": "value"}

        test_now = datetime(2024, 10, 15, 10, 30, 0, tzinfo=timezone.utc)
        mock_now.return_value = test_now

        org = _get_or_create_organization(data, preferences)

        assert "Created via API on 2024-10-15 10:30:00" in org.description
        assert org.name == "New Org Without Description"
        assert org.transcript_ttl == 45
        assert org.preferences == preferences

    def test_prepare_csv_data(self) -> None:
        users = [
            UserData(
                email="<EMAIL>",
                first_name="User",
                last_name="One",
                type="admin",
            ),
            UserData(
                email="<EMAIL>",
                first_name="User",
                last_name="Two",
                type="advisor",
            ),
        ]

        csv_content = _prepare_csv_data(users)

        assert "Email,Name,First Name,Last Name,License Type" in csv_content
        assert "<EMAIL>,User One,User,One,admin" in csv_content
        assert "<EMAIL>,User Two,User,Two,advisor" in csv_content

    @patch("api.routers.onboarding_models.process_users_from_csv")
    def test_process_csv(self, mock_process_users_from_csv: MagicMock) -> None:
        org = MagicMock()
        csv_content = "email,name,first_name,last_name,type\<EMAIL>,User Test,User,Test,admin"
        extra_context = {"source": "api_onboarding"}

        # 1 user created, no errors
        expected_result = (1, 0, 0, 0, [])  # type: ignore[var-annotated]
        mock_process_users_from_csv.return_value = expected_result

        result = _process_csv(org, csv_content, extra_context)

        mock_process_users_from_csv.assert_called_once_with(
            organization=org, csv_file=csv_content, extra_context=extra_context
        )

        assert result == expected_result

    def test_get_users_for_emails_existing_users(self) -> None:
        org = Organization.objects.create(name="Test Org")

        user1 = User.objects.create(
            username="User One",
            email="<EMAIL>",
            first_name="User",
            last_name="One",
            organization=org,
        )
        user2 = User.objects.create(
            username="User Two",
            email="<EMAIL>",
            first_name="User",
            last_name="Two",
            organization=org,
        )

        result = _get_users_for_emails(org, ["<EMAIL>", "<EMAIL>"])

        assert len(result) == 2
        assert result["<EMAIL>"] == user1
        assert result["<EMAIL>"] == user2

    def test_get_users_for_emails_nonexistent_users(self) -> None:
        org = Organization.objects.create(name="Test Org")

        result = _get_users_for_emails(org, ["<EMAIL>"])

        assert len(result) == 1
        assert result["<EMAIL>"] is None

    def test_get_users_for_emails_mixed_results(self) -> None:
        org = Organization.objects.create(name="Test Org")

        user1 = User.objects.create(
            username="User One",
            email="<EMAIL>",
            first_name="Existing",
            last_name="User",
            organization=org,
        )

        result = _get_users_for_emails(org, ["<EMAIL>", "<EMAIL>"])

        assert len(result) == 2
        assert result["<EMAIL>"] == user1
        assert result["<EMAIL>"] is None

    def test_update_existing_users_successful_updates(self) -> None:
        org = Organization.objects.create(name="Test Org")

        user = User.objects.create(
            username="Original User",
            email="<EMAIL>",
            first_name="Original",
            last_name="User",
            organization=org,
        )

        update_data = [
            UserData(
                email="<EMAIL>",
                first_name="Updated",
                last_name="Name",
                type="admin",
                preferences={"theme": "dark"},
            )
        ]

        updated_count, errors = _update_existing_users(org, update_data)

        user.refresh_from_db()

        assert updated_count == 1
        assert len(errors) == 0
        assert user.first_name == "Updated"
        assert user.last_name == "Name"
        assert user.name == "Updated Name"
        assert user.role == "admin"
        assert user.preferences.get("theme") == "dark"  # type: ignore[union-attr]

    def test_update_existing_users_user_not_found(self) -> None:
        org = Organization.objects.create(name="Test Org")

        update_data = [
            UserData(
                email="<EMAIL>",
                first_name="Non",
                last_name="Existent",
            )
        ]

        updated_count, errors = _update_existing_users(org, update_data)

        assert updated_count == 0
        assert len(errors) == 1
        assert "User <EMAIL> not found for update" in errors[0]

    def test_update_existing_users_wrong_organization(self) -> None:
        org1 = Organization.objects.create(name="Org One")
        org2 = Organization.objects.create(name="Org Two")

        user = User.objects.create(
            username="Original User",
            email="<EMAIL>",
            first_name="Original",
            last_name="User",
            organization=org2,
        )

        update_data = [
            UserData(
                email="<EMAIL>",
                first_name="Updated",
                last_name="Name",
            )
        ]

        updated_count, errors = _update_existing_users(org1, update_data)

        assert updated_count == 0
        assert len(errors) == 1
        assert "User <EMAIL> belongs to a different organization" in errors[0]

        user.refresh_from_db()
        assert user.first_name == "Original"
        assert user.last_name == "User"

    def test_update_existing_users_partial_success(self) -> None:
        org = Organization.objects.create(name="Test Org")

        user = User.objects.create(
            username="Original User",
            email="<EMAIL>",
            first_name="Original",
            last_name="User",
            organization=org,
        )

        update_data = [
            UserData(
                email="<EMAIL>",
                first_name="Updated",
                last_name="User",
            ),
            UserData(
                email="<EMAIL>",
                first_name="Non",
                last_name="Existent",
            ),
        ]

        updated_count, errors = _update_existing_users(org, update_data)

        user.refresh_from_db()

        assert updated_count == 1
        assert len(errors) == 1
        assert "User <EMAIL> not found for update" in errors[0]
        assert user.first_name == "Updated"

    @patch("api.routers.onboarding_models.handle_bulk_user_onboarding")
    def test_send_batch_emails_success(self, mock_handle_bulk_user_onboarding: MagicMock) -> None:
        mock_handle_bulk_user_onboarding.return_value = True

        user1 = MagicMock()
        user1.email = "<EMAIL>"
        user2 = MagicMock()
        user2.email = "<EMAIL>"

        org_data = {
            "name": "Test Org",
            "users": [user1, user2],
        }

        results = _send_batch_emails(org_data)

        assert len(results) == 2
        for email, success, error in results:
            assert success is True
            assert error is None

        mock_handle_bulk_user_onboarding.assert_called_once_with(
            users=[user1, user2],
            request=None,
            log_prefix="API Onboarding (Test Org):",
        )

    @patch("api.routers.onboarding_models.handle_bulk_user_onboarding")
    def test_send_batch_emails_failure(self, mock_handle_bulk_user_onboarding: MagicMock) -> None:
        mock_handle_bulk_user_onboarding.return_value = False

        user1 = MagicMock()
        user1.email = "<EMAIL>"
        user2 = MagicMock()
        user2.email = "<EMAIL>"

        org_data = {
            "name": "Test Org",
            "users": [user1, user2],
        }

        results = _send_batch_emails(org_data)

        assert len(results) == 2
        for email, success, error in results:
            assert success is False
            assert error == "Bulk sending failed"

    @patch("api.routers.onboarding_models.handle_bulk_user_onboarding")
    def test_send_batch_emails_exception(self, mock_handle_bulk_user_onboarding: MagicMock) -> None:
        mock_handle_bulk_user_onboarding.side_effect = Exception("Test exception")

        user1 = MagicMock()
        user1.email = "<EMAIL>"
        user2 = MagicMock()
        user2.email = "<EMAIL>"

        org_data = {
            "name": "Test Org",
            "users": [user1, user2],
        }

        results = _send_batch_emails(org_data)

        # Check results
        assert len(results) == 2
        for email, success, error in results:
            assert success is False
            assert error == "Test exception"

    @pytest.mark.asyncio
    @patch("api.routers.onboarding_models.User.objects.select_related")
    async def test_group_users_by_org_simplified(self, mock_select_related: AsyncMock) -> None:
        org1 = MagicMock(spec=["id", "name"])
        org1.id = 1
        org1.name = "Org One"

        org2 = MagicMock(spec=["id", "name"])
        org2.id = 2
        org2.name = "Org Two"

        user1 = MagicMock(spec=User)
        user1.pk = 1
        user1.email = "<EMAIL>"

        user2 = MagicMock(spec=User)
        user2.pk = 2
        user2.email = "<EMAIL>"

        user3 = MagicMock(spec=User)
        user3.pk = 3
        user3.email = "<EMAIL>"

        user1_result = MagicMock(spec=User)
        user1_result.pk = 1
        user1_result.email = "<EMAIL>"
        user1_result.organization = org1

        user2_result = MagicMock(spec=User)
        user2_result.pk = 2
        user2_result.email = "<EMAIL>"
        user2_result.organization = org1

        user3_result = MagicMock(spec=User)
        user3_result.pk = 3
        user3_result.email = "<EMAIL>"
        user3_result.organization = org2

        mock_aget = AsyncMock()
        mock_aget.side_effect = [user1_result, user2_result, user3_result]
        mock_select_related.return_value.aget = mock_aget

        result = await _group_users_by_org([user1, user2, user3])

        assert len(result) == 2
        assert 1 in result
        assert 2 in result

        assert result[1]["name"] == "Org One"
        assert len(result[1]["users"]) == 2
        user_emails_in_org1 = [u.email for u in result[1]["users"]]
        assert "<EMAIL>" in user_emails_in_org1
        assert "<EMAIL>" in user_emails_in_org1

        assert result[2]["name"] == "Org Two"
        assert len(result[2]["users"]) == 1
        assert result[2]["users"][0].email == "<EMAIL>"

    @pytest.mark.asyncio
    @patch("api.routers.onboarding_models._prepare_preferences")
    @patch("api.routers.onboarding_models._get_or_create_organization")
    async def test_process_organization_creation_exception(
        self, mock_get_or_create: AsyncMock, mock_prepare_prefs: AsyncMock
    ) -> None:
        org_data = OrganizationData(
            name="Test Org",
            description="Test Description",
        )

        mock_get_or_create.side_effect = Exception("Test exception")
        mock_prepare_prefs.return_value = {}

        with pytest.raises(HTTPException) as exc_info:
            await process_organization_creation(org_data)

        assert exc_info.value.status_code == 500
        assert "Error processing organization: Test exception" in exc_info.value.detail
