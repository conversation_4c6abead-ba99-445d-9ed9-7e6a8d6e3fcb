import datetime
import itertools
import logging
import uuid
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch

import pytest
from django.utils import timezone
from fastapi import status
from fastapi.testclient import TestClient
from pydantic import ValidationError

from api.dependencies import user_from_authorization_header
from api.internal_api import internal_api as app
from api.routers.meeting_artifacts import ClientInteraction as APIClientInteraction
from api.routers.note_models import FollowUp, FollowUpStatus
from deepinsights.meetingsapp.models.client_interaction import ClientInteraction
from deepinsights.meetingsapp.models.meeting_type import MeetingType as DBMeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingData
from deepinsights.users.models.user import User

client = TestClient(app)

pytestmark = [pytest.mark.django_db(transaction=True)]


@pytest.fixture(autouse=True)
def set_up_tear_down():  # type: ignore[no-untyped-def]
    app.dependency_overrides = {}
    # This test configures meeting types itself; delete any shared ones that may exist.
    DBMeetingType.objects.all().delete()
    yield
    app.dependency_overrides = {}


@pytest.fixture
def test_user(django_user_model: User) -> User:
    test_user = django_user_model.objects.create(username="<EMAIL>")
    app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.get(username="<EMAIL>")
    return test_user


class TestMeetingTypes:
    def test_unauthenticated(self) -> None:
        response = client.get("/api/v2/meeting_artifact/meeting_types")
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_no_meeting_types(self, test_user: User) -> None:
        response = client.get("/api/v2/meeting_artifact/meeting_types")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {"meeting_types": [], "default_meeting_type": None}

    def test_shared_meeting_types(self, test_user: User) -> None:
        client_type = DBMeetingType.objects.create(key="client", name="Client", category="client", everyone=True)
        internal_type = DBMeetingType.objects.create(
            key="internal", name="Internal", category="internal", everyone=True
        )
        debrief_type = DBMeetingType.objects.create(key="debrief", name="Debrief", category="debrief", everyone=True)

        response = client.get("/api/v2/meeting_artifact/meeting_types")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "meeting_types": [
                {
                    "uuid": str(client_type.uuid),
                    "name": "Client",
                    "category": "client",
                    "is_shared": True,
                },
                {
                    "uuid": str(debrief_type.uuid),
                    "name": "Debrief",
                    "category": "debrief",
                    "is_shared": True,
                },
                {
                    "uuid": str(internal_type.uuid),
                    "name": "Internal",
                    "category": "internal",
                    "is_shared": True,
                },
            ],
            "default_meeting_type": None,
        }

    def test_unavailable_meeting_types(self, test_user: User) -> None:
        unavailable_type = DBMeetingType.objects.create(key="client", name="Client", category="client")

        response = client.get("/api/v2/meeting_artifact/meeting_types")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "meeting_types": [],
            "default_meeting_type": None,
        }

    def test_per_org_meeting_types(self, django_user_model: User, test_user: User) -> None:
        org = Organization.objects.create(name="Test Organization")
        org_user = User.objects.create(username="<EMAIL>", organization=org)

        org_two = Organization.objects.create(name="Test Organization 2")
        org_two_user = django_user_model.objects.create(username="<EMAIL>", organization=org_two)

        shared_type = DBMeetingType.objects.create(name="Common", category="client", everyone=True)
        unavailable_type = DBMeetingType.objects.create(name="Unavailable", category="client", everyone=False)
        org_one_type = DBMeetingType.objects.create(name="Org one", category="client")
        org_one_type.organizations.add(org)
        org_one_type.save()
        org_two_type = DBMeetingType.objects.create(name="Org two", category="internal")
        org_two_type.organizations.add(org_two)
        org_two_type.save()
        org_both_type = DBMeetingType.objects.create(name="Both orgs", category="debrief")
        org_both_type.organizations.add(org, org_two)
        org_both_type.save()

        app.dependency_overrides[user_from_authorization_header] = lambda: test_user
        response = client.get("/api/v2/meeting_artifact/meeting_types")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "meeting_types": [
                {
                    "uuid": str(shared_type.uuid),
                    "name": "Common",
                    "category": "client",
                    "is_shared": True,
                }
            ],
            "default_meeting_type": None,
        }

        app.dependency_overrides[user_from_authorization_header] = lambda: org_user
        response = client.get("/api/v2/meeting_artifact/meeting_types")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "meeting_types": [
                {
                    "uuid": str(shared_type.uuid),
                    "name": "Common",
                    "category": "client",
                    "is_shared": True,
                },
                {
                    "uuid": str(org_both_type.uuid),
                    "name": "Both orgs",
                    "category": "debrief",
                    "is_shared": False,
                },
                {
                    "uuid": str(org_one_type.uuid),
                    "name": "Org one",
                    "category": "client",
                    "is_shared": False,
                },
            ],
            "default_meeting_type": None,
        }

        app.dependency_overrides[user_from_authorization_header] = lambda: org_two_user
        response = client.get("/api/v2/meeting_artifact/meeting_types")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "meeting_types": [
                {
                    "uuid": str(shared_type.uuid),
                    "name": "Common",
                    "category": "client",
                    "is_shared": True,
                },
                {
                    "uuid": str(org_both_type.uuid),
                    "name": "Both orgs",
                    "category": "debrief",
                    "is_shared": False,
                },
                {
                    "uuid": str(org_two_type.uuid),
                    "name": "Org two",
                    "category": "internal",
                    "is_shared": False,
                },
            ],
            "default_meeting_type": None,
        }

    def test_per_user_meeting_types(self, django_user_model: User, test_user: User) -> None:
        org = Organization.objects.create(name="Test Organization")
        org_user = django_user_model.objects.create(username="<EMAIL>", organization=org)
        org_user_two = User.objects.create(username="<EMAIL>", organization=org)

        org_two = Organization.objects.create(name="Test Organization 2")
        org_two_user = django_user_model.objects.create(username="<EMAIL>", organization=org_two)
        org_two_user_two = django_user_model.objects.create(username="<EMAIL>", organization=org_two)

        shared_type = DBMeetingType.objects.create(name="Common", category="client", everyone=True)
        unavailable_type = DBMeetingType.objects.create(name="Unavailable", category="client", everyone=False)

        user_one_type = DBMeetingType.objects.create(name="User one", category="client")
        user_one_type.users.add(org_user)
        user_one_type.save()

        org_two_user_type = DBMeetingType.objects.create(name="Org two user one", category="internal")
        org_two_user_type.users.add(org_two_user)
        org_two_user_type.save()

        user_one_and_org_two_type = DBMeetingType.objects.create(
            name="Org two and org one user one", category="internal"
        )
        user_one_and_org_two_type.users.add(org_user)
        user_one_and_org_two_type.organizations.add(org_two)
        user_one_and_org_two_type.save()

        all_users_type = DBMeetingType.objects.create(name="All users", category="debrief")
        all_users_type.users.add(org_user, org_user_two, org_two_user, org_two_user_two)
        all_users_type.save()

        app.dependency_overrides[user_from_authorization_header] = lambda: test_user
        response = client.get("/api/v2/meeting_artifact/meeting_types")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "meeting_types": [
                {
                    "uuid": str(shared_type.uuid),
                    "name": "Common",
                    "category": "client",
                    "is_shared": True,
                }
            ],
            "default_meeting_type": None,
        }

        app.dependency_overrides[user_from_authorization_header] = lambda: org_user
        response = client.get("/api/v2/meeting_artifact/meeting_types")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "meeting_types": [
                {
                    "uuid": str(shared_type.uuid),
                    "name": "Common",
                    "category": "client",
                    "is_shared": True,
                },
                {
                    "uuid": str(all_users_type.uuid),
                    "name": "All users",
                    "category": "debrief",
                    "is_shared": False,
                },
                {
                    "uuid": str(user_one_and_org_two_type.uuid),
                    "name": "Org two and org one user one",
                    "category": "internal",
                    "is_shared": False,
                },
                {
                    "uuid": str(user_one_type.uuid),
                    "name": "User one",
                    "category": "client",
                    "is_shared": False,
                },
            ],
            "default_meeting_type": None,
        }

        app.dependency_overrides[user_from_authorization_header] = lambda: org_user_two
        response = client.get("/api/v2/meeting_artifact/meeting_types")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "meeting_types": [
                {
                    "uuid": str(shared_type.uuid),
                    "name": "Common",
                    "category": "client",
                    "is_shared": True,
                },
                {
                    "uuid": str(all_users_type.uuid),
                    "name": "All users",
                    "category": "debrief",
                    "is_shared": False,
                },
            ],
            "default_meeting_type": None,
        }

        app.dependency_overrides[user_from_authorization_header] = lambda: org_two_user
        response = client.get("/api/v2/meeting_artifact/meeting_types")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "meeting_types": [
                {
                    "uuid": str(shared_type.uuid),
                    "name": "Common",
                    "category": "client",
                    "is_shared": True,
                },
                {
                    "uuid": str(all_users_type.uuid),
                    "name": "All users",
                    "category": "debrief",
                    "is_shared": False,
                },
                {
                    "uuid": str(user_one_and_org_two_type.uuid),
                    "name": "Org two and org one user one",
                    "category": "internal",
                    "is_shared": False,
                },
                {
                    "uuid": str(org_two_user_type.uuid),
                    "name": "Org two user one",
                    "category": "internal",
                    "is_shared": False,
                },
            ],
            "default_meeting_type": None,
        }

        app.dependency_overrides[user_from_authorization_header] = lambda: org_two_user_two
        response = client.get("/api/v2/meeting_artifact/meeting_types")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "meeting_types": [
                {
                    "uuid": str(shared_type.uuid),
                    "name": "Common",
                    "category": "client",
                    "is_shared": True,
                },
                {
                    "uuid": str(all_users_type.uuid),
                    "name": "All users",
                    "category": "debrief",
                    "is_shared": False,
                },
                {
                    "uuid": str(user_one_and_org_two_type.uuid),
                    "name": "Org two and org one user one",
                    "category": "internal",
                    "is_shared": False,
                },
            ],
            "default_meeting_type": None,
        }

    def test_user_org_meeting_type_overlap(self, test_user: User, django_user_model: User) -> None:
        org = Organization.objects.create(name="Test Organization")
        org_user = django_user_model.objects.create(username="<EMAIL>", organization=org)
        org_user_two = django_user_model.objects.create(username="<EMAIL>", organization=org)

        overlapping_type = DBMeetingType.objects.create(name="Overlapping", category="client")
        overlapping_type.users.set([org_user, org_user_two])
        overlapping_type.organizations.add(org)
        overlapping_type.save()

        app.dependency_overrides[user_from_authorization_header] = lambda: test_user
        response = client.get("/api/v2/meeting_artifact/meeting_types")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {"meeting_types": [], "default_meeting_type": None}

        app.dependency_overrides[user_from_authorization_header] = lambda: org_user
        response = client.get("/api/v2/meeting_artifact/meeting_types")
        assert response.status_code == status.HTTP_200_OK
        assert [m.get("uuid") for m in response.json()["meeting_types"]] == [str(overlapping_type.uuid)]

    def test_org_and_everyone(self, test_user: User, django_user_model: User) -> None:
        org = Organization.objects.create(name="Test Organization")
        test_user.organization = org
        test_user.save()
        org_two = Organization.objects.create(name="Test Organization 2")
        meeting_type = DBMeetingType.objects.create(name="Org Overlapping", category="internal", everyone=True)
        meeting_type.organizations.set([org, org_two])
        meeting_type.save()

        response = client.get("/api/v2/meeting_artifact/meeting_types")
        assert response.status_code == status.HTTP_200_OK
        assert [m.get("uuid") for m in response.json()["meeting_types"]] == [
            str(meeting_type.uuid),
        ]

    @pytest.mark.parametrize(
        "user_meeting_type, error_logged",
        [
            (None, False),
            ("", False),
            ("invalid_uuid", True),
            ("00000000-0000-0000-0000-000000000000", True),
        ],
    )
    def test_invalid_default_meeting_type(
        self, test_user: User, user_meeting_type: str | None, error_logged: bool, caplog: pytest.LogCaptureFixture
    ) -> None:
        shared_type = DBMeetingType.objects.create(name="Client", category="client", everyone=True)
        preferences = test_user.preferences or {}
        preferences["default_meeting_type"] = user_meeting_type
        test_user.preferences = preferences
        test_user.save()

        with caplog.at_level(logging.WARNING):
            response = client.get("/api/v2/meeting_artifact/meeting_types")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "meeting_types": [
                {
                    "uuid": str(shared_type.uuid),
                    "name": "Client",
                    "category": "client",
                    "is_shared": True,
                }
            ],
            "default_meeting_type": None,
        }
        assert len(caplog.records) == (1 if error_logged else 0)

    def test_default_meeting_type(self, test_user: User) -> None:
        shared_type = DBMeetingType.objects.create(name="Client", category="client", everyone=True)
        preferences = test_user.preferences or {}
        preferences["default_meeting_type"] = str(shared_type.uuid)
        test_user.preferences = preferences
        test_user.save()

        response = client.get("/api/v2/meeting_artifact/meeting_types")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "meeting_types": [
                {
                    "uuid": str(shared_type.uuid),
                    "name": "Client",
                    "category": "client",
                    "is_shared": True,
                }
            ],
            "default_meeting_type": str(shared_type.uuid),
        }


def test_update_follow_up_not_found(test_user: User) -> None:
    response = client.post(f"/api/v2/meeting_artifact/follow-up/{uuid.uuid4()}", json={})
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Follow-up not found"}


def test_update_follow_up_no_related_model(test_user: User) -> None:
    follow_up = StructuredMeetingData.objects.create(schema={}, data={})
    response = client.post(f"/api/v2/meeting_artifact/follow-up/{follow_up.uuid}", json={})
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Related model not found"}


def test_update_follow_up_not_authorized_for_note(test_user: User) -> None:
    note = Note.objects.create()
    follow_up = StructuredMeetingData.objects.create(schema={}, data={}, note=note)
    response = client.post(f"/api/v2/meeting_artifact/follow-up/{follow_up.uuid}", json={})
    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {"detail": "Not authorized to edit this follow-up"}


def test_update_follow_up_not_authorized_for_interaction_via_agenda(test_user: User, django_user_model: User) -> None:
    second_user = django_user_model.objects.create(username="test2")
    meeting_type = DBMeetingType.objects.create(name="Client", category=DBMeetingType.Category.CLIENT)
    note = Note.objects.create(note_owner=second_user, meeting_type=meeting_type)
    follow_up = StructuredMeetingData.objects.create(schema={}, data={}, note=note)
    ClientInteraction.objects.create(note=note, meeting_type=meeting_type, agenda=follow_up)
    response = client.post(f"/api/v2/meeting_artifact/follow-up/{follow_up.uuid}", json={})
    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {"detail": "Not authorized to edit this follow-up"}


def test_update_follow_up_not_authorized_for_interaction_via_client_prep(
    test_user: User, django_user_model: User
) -> None:
    second_user = django_user_model.objects.create(username="test2")
    meeting_type = DBMeetingType.objects.create(name="Client", category=DBMeetingType.Category.CLIENT)
    note = Note.objects.create(note_owner=second_user, meeting_type=meeting_type)
    follow_up = StructuredMeetingData.objects.create(schema={}, data={}, note=note)
    ClientInteraction.objects.create(note=note, meeting_type=meeting_type, client_prep=follow_up)
    response = client.post(f"/api/v2/meeting_artifact/follow-up/{follow_up.uuid}", json={})
    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {"detail": "Not authorized to edit this follow-up"}


def test_update_follow_up_not_authorized_for_interaction_via_advisor_notes(
    test_user: User, django_user_model: User
) -> None:
    second_user = django_user_model.objects.create(username="test2")
    meeting_type = DBMeetingType.objects.create(name="Client", category=DBMeetingType.Category.CLIENT)
    note = Note.objects.create(note_owner=second_user, meeting_type=meeting_type)
    follow_up = StructuredMeetingData.objects.create(schema={}, data={}, note=note)
    ClientInteraction.objects.create(note=note, meeting_type=meeting_type, advisor_notes=follow_up)
    response = client.post(f"/api/v2/meeting_artifact/follow-up/{follow_up.uuid}", json={})
    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {"detail": "Not authorized to edit this follow-up"}


def test_update_follow_up_invalid_data(test_user: User) -> None:
    note = Note.objects.create()
    note.authorized_users.add(test_user)
    note.save()
    schema = {
        "type": "object",
        "properties": {"test": {"type": "string"}},
        "required": ["test"],
    }
    follow_up = StructuredMeetingData.objects.create(note=note, schema=schema, data={"test": "data"})

    response = client.post(f"/api/v2/meeting_artifact/follow-up/{follow_up.uuid}", json={"invalid": "data"})

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {"detail": "Invalid follow-up data"}


def test_update_follow_up_empty_schema(test_user: User) -> None:
    note = Note.objects.create()
    note.authorized_users.add(test_user)
    note.save()
    follow_up = StructuredMeetingData.objects.create(note=note, data={"test": "data"})

    response = client.post(f"/api/v2/meeting_artifact/follow-up/{follow_up.uuid}", json={"new": "data"})

    assert response.status_code == status.HTTP_204_NO_CONTENT
    follow_up.refresh_from_db()
    assert follow_up.data == {"new": "data"}


def test_update_follow_up_success_with_note(test_user: User) -> None:
    note = Note.objects.create()
    note.authorized_users.add(test_user)
    note.save()
    follow_up = StructuredMeetingData.objects.create(note=note, schema={"type": "object"}, data={"test": "data"})

    response = client.post(f"/api/v2/meeting_artifact/follow-up/{follow_up.uuid}", json={"valid": "data"})

    assert response.status_code == status.HTTP_204_NO_CONTENT
    follow_up.refresh_from_db()
    assert follow_up.data == {"valid": "data"}


def test_update_follow_up_success_with_interaction_via_agenda(test_user: User) -> None:
    meeting_type = DBMeetingType.objects.create(name="Client", category=DBMeetingType.Category.CLIENT)
    follow_up = StructuredMeetingData.objects.create(schema={"type": "object"}, data={"test": "data"})
    note = Note.objects.create()
    note.authorized_users.add(test_user)
    note.save()
    ClientInteraction.objects.create(meeting_type=meeting_type, agenda=follow_up, note=note)

    response = client.post(f"/api/v2/meeting_artifact/follow-up/{follow_up.uuid}", json={"valid": "data"})

    assert response.status_code == status.HTTP_204_NO_CONTENT
    follow_up.refresh_from_db()
    assert follow_up.data == {"valid": "data"}


def test_update_follow_up_success_with_interaction_via_client_prep(test_user: User, django_user_model: User) -> None:
    org = Organization.objects.create(name="Test Organization")
    second_user = django_user_model.objects.create(username="test2", organization=org)
    test_user.organization = org
    test_user.license_type = User.LicenseType.csa
    test_user.save()
    meeting_type = DBMeetingType.objects.create(name="Client", category=DBMeetingType.Category.CLIENT)
    follow_up = StructuredMeetingData.objects.create(schema={"type": "object"}, data={"test": "data"})
    note = Note.objects.create(note_owner=second_user)
    ClientInteraction.objects.create(meeting_type=meeting_type, client_prep=follow_up, note=note)

    response = client.post(f"/api/v2/meeting_artifact/follow-up/{follow_up.uuid}", json={"valid": "data"})

    assert response.status_code == status.HTTP_204_NO_CONTENT
    follow_up.refresh_from_db()
    assert follow_up.data == {"valid": "data"}


def test_update_follow_up_success_with_interaction_via_advisor_notes(test_user: User) -> None:
    meeting_type = DBMeetingType.objects.create(name="Client", category=DBMeetingType.Category.CLIENT)
    follow_up = StructuredMeetingData.objects.create(schema={"type": "object"}, data={"test": "data"})
    test_user.is_superuser = True
    test_user.save()
    note = Note.objects.create()
    ClientInteraction.objects.create(meeting_type=meeting_type, advisor_notes=follow_up, note=note)

    response = client.post(f"/api/v2/meeting_artifact/follow-up/{follow_up.uuid}", json={"valid": "data"})

    assert response.status_code == status.HTTP_204_NO_CONTENT
    follow_up.refresh_from_db()
    assert follow_up.data == {"valid": "data"}


def test_update_follow_up_success_with_interaction_multiple_interaction_models(test_user: User) -> None:
    meeting_type = DBMeetingType.objects.create(name="Client", category=DBMeetingType.Category.CLIENT)
    follow_up = StructuredMeetingData.objects.create(schema={"type": "object"}, data={"test": "data"})
    follow_up_two = StructuredMeetingData.objects.create(schema={}, data={})
    follow_up_three = StructuredMeetingData.objects.create(schema={}, data={})
    note = Note.objects.create(note_owner=test_user)
    ClientInteraction.objects.create(
        meeting_type=meeting_type, agenda=follow_up, client_prep=follow_up_two, advisor_notes=follow_up_three, note=note
    )

    response = client.post(f"/api/v2/meeting_artifact/follow-up/{follow_up.uuid}", json={"valid": "data"})

    assert response.status_code == status.HTTP_204_NO_CONTENT
    follow_up.refresh_from_db()
    follow_up_two.refresh_from_db()
    follow_up_three.refresh_from_db()
    assert follow_up.data == {"valid": "data"}
    assert follow_up_two.data == {}
    assert follow_up_three.data == {}


def test_get_client_interaction_not_found(test_user: User) -> None:
    response = client.get(f"/api/v2/meeting_artifact/client_interaction/{uuid.uuid4()}")
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Interaction not found"}


def test_get_client_interaction_no_note(test_user: User, django_user_model: User) -> None:
    meeting_type = DBMeetingType.objects.create(name="Client", category=DBMeetingType.Category.CLIENT)
    interaction = ClientInteraction.objects.create(meeting_type=meeting_type)

    response = client.get(f"/api/v2/meeting_artifact/client_interaction/{interaction.uuid}")
    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {"detail": "Not authorized to view this interaction"}


def test_get_client_interaction_not_authorized(test_user: User, django_user_model: User) -> None:
    second_user = django_user_model.objects.create(username="test2")
    meeting_type = DBMeetingType.objects.create(name="Client", category=DBMeetingType.Category.CLIENT)
    note = Note.objects.create(note_owner=second_user, meeting_type=meeting_type)
    interaction = ClientInteraction.objects.create(note=note, meeting_type=meeting_type)

    response = client.get(f"/api/v2/meeting_artifact/client_interaction/{interaction.uuid}")
    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {"detail": "Not authorized to view this interaction"}


@patch("api.routers.meeting_artifacts.DBClientInteraction")
def test_get_client_interaction_validation_error(mock_client_interaction: MagicMock, test_user: User) -> None:
    note = Note.objects.create(note_owner=test_user)
    note.authorized_users.add(test_user)
    note.save()
    mock_interaction = MagicMock()
    mock_interaction.uuid = None
    mock_interaction.note = note
    mock_client_interaction.objects.select_related.return_value.prefetch_related.return_value.aget = AsyncMock(
        return_value=mock_interaction
    )
    mock_client_interaction.DoesNotExist = ClientInteraction.DoesNotExist

    response = client.get(f"/api/v2/meeting_artifact/client_interaction/{uuid.uuid4()}")
    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


def test_get_client_interaction_success(test_user: User) -> None:
    meeting_type = DBMeetingType.objects.create(name="Client", category=DBMeetingType.Category.CLIENT)
    note = Note.objects.create(note_owner=test_user, meeting_type=meeting_type)
    agenda = StructuredMeetingData.objects.create(schema={}, data={})
    client_prep = StructuredMeetingData.objects.create(schema={}, data={})
    advisor_notes = StructuredMeetingData.objects.create(schema={}, data={})
    interaction = ClientInteraction.objects.create(
        note=note,
        meeting_type=meeting_type,
        agenda=agenda,
        client_prep=client_prep,
        advisor_notes=advisor_notes,
    )

    response = client.get(f"/api/v2/meeting_artifact/client_interaction/{interaction.uuid}")
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {
        "uuid": str(interaction.uuid),
        "note_uuid": str(note.uuid),
        "agenda": {
            "uuid": str(agenda.uuid),
            "title": agenda.title,
            "kind": agenda.kind,
            "schema": agenda.schema,
            "data": agenda.data,
            "status": FollowUpStatus.COMPLETED,
        },
        "client_prep": None,
        "advisor_notes": {
            "uuid": str(advisor_notes.uuid),
            "title": advisor_notes.title,
            "kind": advisor_notes.kind,
            "schema": advisor_notes.schema,
            "data": advisor_notes.data,
            "status": FollowUpStatus.COMPLETED,
        },
    }


def test_get_client_interaction_success_as_authorized_user(test_user: User, django_user_model: User) -> None:
    meeting_type = DBMeetingType.objects.create(name="Client", category=DBMeetingType.Category.CLIENT)
    second_user = django_user_model.objects.create(username="test2")
    note = Note.objects.create(note_owner=second_user, meeting_type=meeting_type)
    note.authorized_users.add(test_user)
    note.save()
    agenda = StructuredMeetingData.objects.create(schema={}, data={})
    client_prep = StructuredMeetingData.objects.create(schema={}, data={})
    advisor_notes = StructuredMeetingData.objects.create(schema={}, data={})
    interaction = ClientInteraction.objects.create(
        note=note,
        meeting_type=meeting_type,
        agenda=agenda,
        client_prep=client_prep,
        advisor_notes=advisor_notes,
    )

    response = client.get(f"/api/v2/meeting_artifact/client_interaction/{interaction.uuid}")

    assert response.status_code == status.HTTP_200_OK
    assert response.json()["uuid"] == str(interaction.uuid)


def test_get_client_interaction_success_as_superuser(test_user: User, django_user_model: User) -> None:
    meeting_type = DBMeetingType.objects.create(name="Client", category=DBMeetingType.Category.CLIENT)
    second_user = django_user_model.objects.create(username="test2")
    note = Note.objects.create(note_owner=second_user, meeting_type=meeting_type)
    test_user.is_superuser = True
    test_user.save()
    agenda = StructuredMeetingData.objects.create(schema={}, data={})
    client_prep = StructuredMeetingData.objects.create(schema={}, data={})
    advisor_notes = StructuredMeetingData.objects.create(schema={}, data={})
    interaction = ClientInteraction.objects.create(
        note=note,
        meeting_type=meeting_type,
        agenda=agenda,
        client_prep=client_prep,
        advisor_notes=advisor_notes,
    )

    response = client.get(f"/api/v2/meeting_artifact/client_interaction/{interaction.uuid}")

    assert response.status_code == status.HTTP_200_OK
    assert response.json()["uuid"] == str(interaction.uuid)


@pytest.mark.parametrize("license_type", [User.LicenseType.csa, User.LicenseType.staff])
def test_get_client_interaction_success_as_csa(
    test_user: User, django_user_model: User, license_type: User.LicenseType
) -> None:
    org = Organization.objects.create(name="Test Organization")
    meeting_type = DBMeetingType.objects.create(name="Client", category=DBMeetingType.Category.CLIENT)
    second_user = django_user_model.objects.create(username="test2", organization=org)
    test_user.organization = org
    test_user.license_type = license_type
    test_user.save()

    note = Note.objects.create(note_owner=second_user, meeting_type=meeting_type)
    agenda = StructuredMeetingData.objects.create(schema={}, data={})
    client_prep = StructuredMeetingData.objects.create(schema={}, data={})
    advisor_notes = StructuredMeetingData.objects.create(schema={}, data={})
    interaction = ClientInteraction.objects.create(
        note=note,
        meeting_type=meeting_type,
        agenda=agenda,
        client_prep=client_prep,
        advisor_notes=advisor_notes,
    )

    response = client.get(f"/api/v2/meeting_artifact/client_interaction/{interaction.uuid}")

    assert response.status_code == status.HTTP_200_OK
    assert response.json()["uuid"] == str(interaction.uuid)


@pytest.mark.parametrize(
    "has_agenda, has_client_prep, has_advisor_notes",
    itertools.product([True, False], repeat=3),
)
def test_get_client_interaction_with_structured_data(
    test_user: User, has_agenda: bool, has_client_prep: bool, has_advisor_notes: bool
) -> None:
    meeting_type = DBMeetingType.objects.create(name="Client", category=DBMeetingType.Category.CLIENT)
    note = Note.objects.create(note_owner=test_user, meeting_type=meeting_type)
    agenda = StructuredMeetingData.objects.create(schema={}, data={})
    client_prep = StructuredMeetingData.objects.create(schema={}, data={})
    advisor_notes = StructuredMeetingData.objects.create(schema={}, data={})
    interaction = ClientInteraction.objects.create(
        note=note,
        meeting_type=meeting_type,
        agenda=agenda if has_agenda else None,
        client_prep=client_prep if has_client_prep else None,
        advisor_notes=advisor_notes if has_advisor_notes else None,
    )

    response = client.get(f"/api/v2/meeting_artifact/client_interaction/{interaction.uuid}")
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {
        "uuid": str(interaction.uuid),
        "note_uuid": str(note.uuid),
        "agenda": {
            "uuid": str(agenda.uuid),
            "title": agenda.title,
            "kind": agenda.kind,
            "schema": agenda.schema,
            "data": agenda.data,
            "status": FollowUpStatus.COMPLETED,
        }
        if has_agenda
        else None,
        # The client prep is always empty; it's unused for now.
        "client_prep": None,
        "advisor_notes": {
            "uuid": str(advisor_notes.uuid),
            "title": advisor_notes.title,
            "kind": advisor_notes.kind,
            "schema": advisor_notes.schema,
            "data": advisor_notes.data,
            "status": FollowUpStatus.COMPLETED,
        }
        if has_advisor_notes
        else None,
    }


class TestListInteractions:
    def _create_interaction(
        self,
        note_owner: User | None,
        authorized_users: list[User] | None = None,
        creation_time: datetime.datetime | None = None,
        *,
        agenda: StructuredMeetingData | None = None,
        client_prep: StructuredMeetingData | None = None,
        advisor_notes: StructuredMeetingData | None = None,
    ) -> ClientInteraction:
        meeting_type = DBMeetingType.objects.create(name="Client", category=DBMeetingType.Category.CLIENT)
        note = Note.objects.create(note_owner=note_owner, meeting_type=meeting_type)
        note.authorized_users.add(
            *authorized_users if authorized_users is not None else ([note_owner] if note_owner else [])
        )
        note.save()

        return ClientInteraction.objects.create(
            created=creation_time or timezone.now(),
            note=note,
            meeting_type=meeting_type,
            agenda=agenda,
            client_prep=client_prep,
            advisor_notes=advisor_notes,
        )

    def test_empty(self, test_user: User) -> None:
        response = client.get("/api/v2/meeting_artifact/client_interaction")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == []

    def test_authorization_no_license(self, test_user: User, django_user_model: User) -> None:
        org = Organization.objects.create(name="Test org")
        user = test_user
        second_user = django_user_model.objects.create(
            username="<EMAIL>", organization=org, license_type=User.LicenseType.csa
        )

        authorized_interactions = [
            self._create_interaction(user, [user]),
            self._create_interaction(second_user, [user]),
            self._create_interaction(user, [user, second_user]),
            self._create_interaction(second_user, [user, second_user]),
        ]
        self._create_interaction(user, [])
        self._create_interaction(second_user, [second_user])

        response = client.get("/api/v2/meeting_artifact/client_interaction")
        assert response.status_code == status.HTTP_200_OK
        assert set([interaction["uuid"] for interaction in response.json()]) == set(
            [str(interaction.uuid) for interaction in authorized_interactions]
        )

    @pytest.mark.parametrize("is_superuser", [True, False])
    def test_authorization_advisor_license(self, test_user: User, django_user_model: User, is_superuser: bool) -> None:
        org = Organization.objects.create(name="Test org")
        user = test_user
        user.license_type = User.LicenseType.advisor
        user.is_superuser = is_superuser
        user.first_name = "Test"
        user.last_name = "User"
        user.save()
        second_user = django_user_model.objects.create(
            username="<EMAIL>",
            organization=org,
            license_type=User.LicenseType.csa,
            first_name="Other",
            last_name="One",
        )

        authorized_interactions = [
            self._create_interaction(user, [user]),
            self._create_interaction(second_user, [user]),
            self._create_interaction(user, [user, second_user]),
            self._create_interaction(second_user, [user, second_user]),
            self._create_interaction(None, [user]),
        ]
        self._create_interaction(user, [])
        self._create_interaction(second_user, [second_user])
        self._create_interaction(None, None)

        response = client.get("/api/v2/meeting_artifact/client_interaction")
        assert response.status_code == status.HTTP_200_OK
        assert set([interaction["uuid"] for interaction in response.json()]) == set(
            [str(interaction.uuid) for interaction in authorized_interactions]
        )

    @pytest.mark.parametrize(
        "license_type, is_superuser",
        [
            (User.LicenseType.csa, True),
            (User.LicenseType.csa, False),
            (User.LicenseType.staff, True),
            (User.LicenseType.staff, False),
        ],
    )
    def test_authorization_for_high_privilege_users(
        self, test_user: User, django_user_model: User, license_type: User.LicenseType, is_superuser: bool
    ) -> None:
        org = Organization.objects.create(name="Test org")
        user = test_user
        user.organization = org
        user.license_type = license_type
        user.is_superuser = is_superuser
        user.save()
        second_user = django_user_model.objects.create(username="<EMAIL>", organization=org)

        org_two = Organization.objects.create(name="Test org two")
        third_user = django_user_model.objects.create(username="<EMAIL>", organization=org_two)

        authorized_interactions = [
            self._create_interaction(user, []),
            self._create_interaction(user, [user]),
            self._create_interaction(user, [user, second_user]),
            self._create_interaction(user, [second_user]),
            self._create_interaction(second_user, []),
            self._create_interaction(second_user, [user]),
            self._create_interaction(second_user, [user, second_user]),
            self._create_interaction(second_user, [second_user]),
        ]

        self._create_interaction(third_user, [])
        self._create_interaction(third_user, [user, second_user])

        response = client.get("/api/v2/meeting_artifact/client_interaction")
        assert response.status_code == status.HTTP_200_OK
        assert set([interaction["uuid"] for interaction in response.json()]) == set(
            [str(interaction.uuid) for interaction in authorized_interactions]
        )

    def test_ordering(self, test_user: User) -> None:
        user = test_user

        interaction_2021 = self._create_interaction(
            user, creation_time=datetime.datetime(2021, 1, 1, tzinfo=datetime.timezone.utc)
        )
        interaction_2020 = self._create_interaction(
            user, creation_time=datetime.datetime(2020, 1, 1, tzinfo=datetime.timezone.utc)
        )
        interaction_2018 = self._create_interaction(
            user, creation_time=datetime.datetime(2018, 1, 1, tzinfo=datetime.timezone.utc)
        )
        interaction_2022 = self._create_interaction(
            user, creation_time=datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc)
        )
        interaction_2024 = self._create_interaction(
            user, creation_time=datetime.datetime(2024, 1, 1, tzinfo=datetime.timezone.utc)
        )

        ordered_interactions = [
            interaction_2024,
            interaction_2022,
            interaction_2021,
            interaction_2020,
            interaction_2018,
        ]

        response = client.get("/api/v2/meeting_artifact/client_interaction")
        assert response.status_code == status.HTTP_200_OK
        assert [interaction["uuid"] for interaction in response.json()] == [
            str(interaction.uuid) for interaction in ordered_interactions
        ]

    def test_deleted_interactions_not_returned(self, test_user: User) -> None:
        user = test_user

        interactions = [
            self._create_interaction(user),
            self._create_interaction(user),
        ]
        deleted_interactions = [
            self._create_interaction(user),
            self._create_interaction(user),
        ]
        for interaction in deleted_interactions:
            interaction.is_deleted = True
            interaction.save()

        response = client.get("/api/v2/meeting_artifact/client_interaction")
        assert response.status_code == status.HTTP_200_OK
        assert set([interaction["uuid"] for interaction in response.json()]) == set(
            [str(interaction.uuid) for interaction in interactions]
        )

    def test_partial_failure(self, test_user: User) -> None:
        interaction = self._create_interaction(test_user)

        valid_interaction = APIClientInteraction(
            uuid=interaction.uuid,
            note_uuid=uuid.uuid4(),
            agenda=FollowUp.from_structured_meeting_data(StructuredMeetingData.objects.create(schema={}, data={})),
            client_prep=FollowUp.from_structured_meeting_data(StructuredMeetingData.objects.create(schema={}, data={})),
            advisor_notes=FollowUp.from_structured_meeting_data(
                StructuredMeetingData.objects.create(schema={}, data={})
            ),
        )

        with patch("api.routers.meeting_artifacts._interaction_response_from_interation") as mock_response:
            mock_response.side_effect = [ValidationError.from_exception_data("title", []), valid_interaction]
            response = client.get("/api/v2/meeting_artifact/client_interaction")
            assert response.status_code == status.HTTP_200_OK

    def test_multiple(self, test_user: User, django_user_model: User) -> None:
        second_user = django_user_model.objects.create(username="test2")
        interaction_one = self._create_interaction(
            test_user,
            agenda=StructuredMeetingData.objects.create(
                title="Agenda", kind="agenda", schema={"agenda": "schema"}, data={"agenda": "data"}
            ),
            client_prep=StructuredMeetingData.objects.create(
                title="Client prep", kind="client_prep", schema={"prep": "schema"}, data={"prep": "data"}
            ),
            advisor_notes=StructuredMeetingData.objects.create(
                title="Advisor notes", kind="advisor_notes", schema={"notes": "schema"}, data={"notes": "data"}
            ),
        )
        interaction_two = self._create_interaction(
            test_user,
            [test_user, second_user],
        )

        assert (note_one := interaction_one.note)
        assert (note_two := interaction_two.note)
        assert (agenda_one := interaction_one.agenda)
        assert (advisor_notes_one := interaction_one.advisor_notes)

        response = client.get("/api/v2/meeting_artifact/client_interaction")
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == [
            {
                "uuid": str(interaction_two.uuid),
                "note_uuid": str(note_two.uuid),
                "agenda": None,
                "client_prep": None,
                "advisor_notes": None,
            },
            {
                "uuid": str(interaction_one.uuid),
                "note_uuid": str(note_one.uuid),
                "agenda": {
                    "uuid": str(agenda_one.uuid),
                    "title": "Agenda",
                    "kind": "agenda",
                    "schema": {"agenda": "schema"},
                    "data": {"agenda": "data"},
                    "status": FollowUpStatus.COMPLETED,
                },
                "client_prep": None,
                "advisor_notes": {
                    "uuid": str(advisor_notes_one.uuid),
                    "title": "Advisor notes",
                    "kind": "advisor_notes",
                    "schema": {"notes": "schema"},
                    "data": {"notes": "data"},
                    "status": FollowUpStatus.COMPLETED,
                },
            },
        ]
