import datetime
import logging

from asgiref.sync import sync_to_async
from django.conf import settings
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status

from api.dependencies import user_from_authorization_header
from api.routers.calendar import DEFAULT_LOOKAHEAD
from api.routers.calendar_models import CalendarLookahead
from api.routers.settings_models import (
    IntegrationType,
    LabeledEntity,
    MenuItem,
    MenuItemId,
    OrganizationPlanDetails,
    PlanFeature,
    PlanUser,
    SaveRequest,
    SectionDetails,
    SectionItemBooleanField,
    SectionItemIntegrationCard,
    SectionItemIntegrationCards,
    SectionItemLink,
    SectionItemPlanDetails,
    SectionItemSingleChoiceField,
    SectionItemTextField,
    SectionItemType,
    SettingsFilter,
)
from deepinsights.core.integrations.calendar.auto_join import update_recall_auto_join_integration
from deepinsights.core.integrations.meetingbot.recall_ai import RecallBotController
from deepinsights.core.integrations.oauth.google import GoogleOAuth
from deepinsights.core.integrations.oauth.microsoft import Microsoft<PERSON><PERSON>
from deepinsights.core.integrations.oauth.wealthbox import <PERSON>althBoxOAuth
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.meetingsapp.models.structured_meeting_data import (
    StructuredMeetingDataTemplate,
    StructuredMeetingDataTemplateRule,
)
from deepinsights.users.models.subscription_plan import EntitlementType, SubscriptionTerm
from deepinsights.users.models.user import User

router = APIRouter(tags=["settings"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}")


# Routes


@router.get("/menu")
async def get_settings_menu_route(user: User = Depends(user_from_authorization_header)) -> list[MenuItem]:
    return await get_settings_menu(user)


@router.get("/details")
async def get_settings_details_route(
    request: Request,
    user: User = Depends(user_from_authorization_header),
    identifier: str = Query(""),
) -> SectionDetails:
    proto = request.headers.get("X-Forwarded-Proto")
    host = request.headers.get("X-Forwarded-Host")
    base_url: str = f"{proto}://{host}" if (proto and host) else settings.APP_DOMAIN
    return await get_settings_details(base_url, user, identifier)


@router.post("/save", status_code=status.HTTP_200_OK)
async def save(data: SaveRequest, user: User = Depends(user_from_authorization_header)) -> int:
    return await post_save(data, user)


# Implementations


async def get_settings_menu(user: User) -> list[MenuItem]:
    user_impersonation_settings = await _get_user_impersonation_settings(user)
    settings = [
        MenuItem(
            id=MenuItemId.MY_ACCOUNT,
            label="My Account",
            items=[
                s
                for s in [
                    MenuItem(
                        id=MenuItemId.INTEGRATIONS,
                        label="Integrations",
                    ),
                    MenuItem(
                        id=MenuItemId.SETTINGS,
                        label="Settings",
                    ),
                    MenuItem(
                        id=MenuItemId.PROFILE_DETAILS,
                        label="Profile Details",
                    ),
                    MenuItem(
                        id=MenuItemId.PLAN_DETAILS,
                        label="Plan and Features",
                    )
                    if await sync_to_async(Flags.EnablePlanInfoInSettings.is_active_for_user)(user)
                    else None,
                    MenuItem(
                        id=MenuItemId.USER_INTERFACE,
                        label="User Interface",
                    )
                    if await sync_to_async(Flags.EnableUISettings.is_active_for_user)(user)
                    else None,
                ]
                if s is not None
            ],
        ),
    ]

    settings.extend(
        [
            s
            for s in [
                MenuItem(
                    id=MenuItemId.USER_PREFERENCES,
                    label="Preferences",
                ),
                MenuItem(
                    id=MenuItemId.ORG_PREFERENCES,
                    label="Organization Preferences",
                )
                if user.is_organization_admin
                else None,
            ]
            if s is not None
        ]
        if await sync_to_async(Flags.EnablePreferencesInSettings.is_active_for_user)(user)
        else []
    )

    if user_impersonation_settings:
        settings.extend(
            [
                MenuItem(
                    id=MenuItemId.ADMIN,
                    label="Admin",
                    items=[
                        MenuItem(
                            id=MenuItemId.USER_IMPERSONATION,
                            label="User Impersonation",
                        ),
                    ],
                )
            ]
        )

    return settings


async def _get_user_impersonation_settings(user: User) -> bool:
    enable_user_impersonation = await sync_to_async(Flags.EnableUserImpersonation.is_active_for_user)(user)
    return bool(enable_user_impersonation and user.is_superuser)


async def get_settings_details(base_url: str, user: User, identifier: str) -> SectionDetails:
    # use match case on `identifier` to determine which settings to return
    match identifier:
        case MenuItemId.INTEGRATIONS.value:
            return await _get_settings_integrations(base_url, user)
        case MenuItemId.SETTINGS.value:
            return await _get_settings_calendar(user)
        case MenuItemId.PROFILE_DETAILS.value:
            return await _get_profile_details(user)
        case MenuItemId.PLAN_DETAILS.value:
            return await _get_plan_details(user)
        case _:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)


async def _get_settings_integrations(base_url: str, user: User) -> SectionDetails:
    oauth_integration_providers = set([o.integration async for o in OAuthCredentials.objects.filter(user=user)])
    integrations = await _get_integrations(base_url, oauth_integration_providers, user)
    return SectionDetails(
        label="Integrations",
        data=[
            SectionItemIntegrationCards(
                id="myIntegrations",
                label="My Integrations",
                filters=SettingsFilter,
                cards=[setting for setting in integrations if setting is not None and setting.isActive],
            ),
            SectionItemIntegrationCards(
                id="availableIntegrations",
                label="Available Integrations",
                filters=SettingsFilter,
                cards=[setting for setting in integrations if setting is not None and not setting.isActive],
            ),
        ],
    )


async def _get_integrations(
    base_url: str, oauth_integration_providers: set[str], user: User
) -> list[SectionItemIntegrationCard | None]:
    salesforce_oauth_enabled = await sync_to_async(Flags.EnableSalesforceOAuthIntegration.is_active_for_user)(user)
    advisor_engine_oauth_enabled = await sync_to_async(Flags.EnableAdvisorEngineOAuthIntegration.is_active_for_user)(
        user
    )
    wealthbox_status = (
        ""
        if "wealthbox" not in oauth_integration_providers
        else (
            "Integrated" if await sync_to_async(WealthBoxOAuth().check_integration_status)(user) else "Needs attention"
        )
    )
    dynamics_config = user.get_crm_configuration().dynamics
    dynamics_enabled = (
        dynamics_config and hasattr(dynamics_config, "dynamics_resource_url") and dynamics_config.dynamics_resource_url
    )

    return [
        SectionItemIntegrationCard(
            id="connect_redtail",
            label="Redtail",
            tag=IntegrationType.CRM,
            isActive=True if user.get_crm_configuration().redtail.user_key else False,
            redirectPath="/settings/redtail",
            value="Reconnect Redtail" if user.get_crm_configuration().redtail.user_key else "Connect Redtail",
            isInteractible=True,
        ),
        SectionItemIntegrationCard(
            id="connect_zoom",
            label="Zoom",
            tag=IntegrationType.MEETING_SOFTWARE,
            isActive=True,
        ),
        SectionItemIntegrationCard(
            id="connect_wealthbox",
            label="Wealthbox",
            tag=IntegrationType.CRM,
            isActive=wealthbox_status == "Integrated" if True else False,
            redirectPath=f"https://app.crmworkspace.com/oauth/authorize?client_id={settings.WEALTHBOX_CLIENT_ID}&redirect_uri={base_url}/oauth/wealthbox&response_type=code&scope=login+data",
            isInteractible=True,
        ),
        SectionItemIntegrationCard(
            id="connect_google_meet",
            label="Google Meet",
            tag=IntegrationType.MEETING_SOFTWARE,
            isActive=True,
        ),
        SectionItemIntegrationCard(
            id="connect_microsoft_teams",
            label="Microsoft Teams",
            tag=IntegrationType.MEETING_SOFTWARE,
            isActive=True,
        ),
        SectionItemIntegrationCard(
            id="connect_webex",
            label="Webex",
            tag=IntegrationType.MEETING_SOFTWARE,
            isActive=True,
        ),
        SectionItemIntegrationCard(
            id="connect_salesforce",
            label="Salesforce",
            tag=IntegrationType.CRM,
            isActive=True if "salesforce" in oauth_integration_providers else False,
            redirectPath=f"https://login.salesforce.com/services/oauth2/authorize?client_id={settings.SALESFORCE_CONSUMER_KEY}&redirect_uri={base_url}/oauth/salesforce&response_type=code",
            isInteractible=True,
        )
        if salesforce_oauth_enabled
        else SectionItemIntegrationCard(
            id="connect_salesforce_contact_support",
            label="Salesforce",
            tag=IntegrationType.CRM,
            isActive=False,
            redirectPath="mailto:<EMAIL>",
            value="Contact Support",
            isInteractible=True,
        ),
        SectionItemIntegrationCard(
            id="connect_ms_calendar",
            label="Microsoft Calendar",
            tag=IntegrationType.CALENDAR,
            isActive=True if "microsoft" in oauth_integration_providers else False,
            redirectPath=f"https://login.microsoftonline.com/common/oauth2/v2.0/authorize?client_id={settings.MSAL_CLIENT_ID}&response_type=code&redirect_uri={base_url}/auth/microsoft/callback&response_mode=query&scope=Calendars.Read+User.Read+offline_access&state=calendar_integration",
            isInteractible=True,
        ),
        SectionItemIntegrationCard(
            id="connect_google_calendar",
            label="Google Calendar",
            tag=IntegrationType.CALENDAR,
            isActive=True if "google" in oauth_integration_providers else False,
            redirectPath=f"https://accounts.google.com/o/oauth2/v2/auth?client_id={settings.GOOGLE_CLIENT_ID}&redirect_uri={base_url}/auth/google/callback&response_type=code&scope=https://www.googleapis.com/auth/calendar.readonly&state=calendar_integration&access_type=offline&prompt=consent",
            isInteractible=True,
        ),
        SectionItemIntegrationCard(
            id="connect_microsoft_dynamics",
            label="Microsoft Dynamics",
            tag=IntegrationType.CRM,
            isActive=True if "microsoft_dynamics" in oauth_integration_providers else False,
            redirectPath=f"https://login.microsoftonline.com/common/oauth2/v2.0/authorize?client_id={settings.MICROSOFT_DYNAMICS_CLIENT_ID}&response_type=code&redirect_uri={base_url}/oauth/microsoft_dynamics&response_mode=query&scope={dynamics_config.dynamics_resource_url}/user_impersonation+offline_access+openid+profile",
            isInteractible=True,
        )
        if dynamics_enabled
        else None,
        SectionItemIntegrationCard(
            id="connect_advisorengine",
            label="AdvisorEngine",
            tag=IntegrationType.CRM,
            isActive=True if "advisor_engine" in oauth_integration_providers else False,
            redirectPath=f"https://oauth.advisorengine.net/connect/authorize?client_id={settings.ADVISORENGINE_CLIENT_ID}&redirect_uri={base_url}/oauth/advisorengine&response_type=code&scope=openid+offline_access+ae_crm.ext_partner&state=advisorengine_integration&code_challenge_method=S256&code_challenge={{code_challenge}}",
            isInteractible=True,
        )
        if advisor_engine_oauth_enabled
        else None,
    ]


async def _get_settings_calendar(user: User) -> SectionDetails:
    oauth_integration_providers = set([o.integration async for o in OAuthCredentials.objects.filter(user=user)])
    account_preferences = await _get_account_preferences(oauth_integration_providers, user)
    return SectionDetails(label="Settings", data=account_preferences, showSaveButton=True)


async def _get_account_preferences(oauth_integration_providers: set[str], user: User) -> list[SectionItemType]:
    enable_bento_toggle = await sync_to_async(Flags.EnableBentoIntegrationSettingsToggle.is_active_for_user)(user)
    has_any_applicable_bento_tags_rule = (
        await StructuredMeetingDataTemplateRule.relevant_follow_up_templates(None, user)
        .filter(kind=StructuredMeetingDataTemplate.Kind.BENTO_LIFE_EVENTS)
        .aexists()
    )

    wealthbox_status = (
        ""
        if "wealthbox" not in oauth_integration_providers
        else (
            "Integrated" if await sync_to_async(WealthBoxOAuth().check_integration_status)(user) else "Needs attention"
        )
    )

    settings = [
        SectionItemBooleanField(
            id="calendarAutoJoin",
            label="Enable auto-join for calendar",
            value=user.recall_calendar_id is not None,
            errorMsg="Please connect your calendar to enable auto-join"
            if "google" not in oauth_integration_providers and "microsoft" not in oauth_integration_providers
            else None,
        ),
        SectionItemBooleanField(
            id="calendarShowMeetingsWithoutUrls",
            label="Show calendar meetings without meeting URLs",
            value=user.show_events_without_meeting_urls,
        ),
        SectionItemSingleChoiceField(
            id="calendarLookahead",
            label="Calendar lookahead",
            options=[
                LabeledEntity(id=CalendarLookahead.ONE_HOUR, label="One hour"),
                LabeledEntity(id=CalendarLookahead.END_OF_DAY, label="End of day"),
                LabeledEntity(id=CalendarLookahead.ONE_DAY, label="One day"),
                LabeledEntity(id=CalendarLookahead.TWO_DAYS, label="Two days"),
                LabeledEntity(id=CalendarLookahead.END_OF_WEEK, label="End of week"),
                LabeledEntity(id=CalendarLookahead.END_OF_NEXT_WEEK, label="End of next week"),
                LabeledEntity(id=CalendarLookahead.THIRTY_DAYS, label="Thirty days"),
            ],
            value=user.calendar_lookahead or DEFAULT_LOOKAHEAD,
        ),
        SectionItemBooleanField(
            id="bentoTagsEnabled",
            label="Enable Bento tags",
            value=has_any_applicable_bento_tags_rule,
        )
        if wealthbox_status == "Integrated" and enable_bento_toggle
        else None,
    ]

    return [item for item in settings if item is not None]


async def _get_profile_details(user: User) -> SectionDetails:
    user = await User.objects.select_related("manager").aget(id=user.id)
    return SectionDetails(
        label="Profile Details",
        data=[
            SectionItemTextField(
                id="name",
                label="Full Name",
                placeholder="John Doe",
                value=user.name,
                readonly=True,
            ),
            SectionItemTextField(
                id="email",
                label="Email ID",
                placeholder="<EMAIL>",
                value=user.email,
                readonly=True,
            ),
            SectionItemTextField(
                id="role",
                label="Role",
                placeholder="",
                value=user.role or "Not set",
                readonly=True,
            ),
            SectionItemTextField(
                id="manager",
                label="Manager",
                placeholder="",
                value=user.manager.email if user.manager else "Not set",
                readonly=True,
            ),
            SectionItemLink(
                id="contactSupport",
                label="Note",
                description="Please connect with us if you want to edit your profile information",
                text="Contact Support",
                appTag="contact-support",
            ),
        ],
    )


def _now() -> datetime.datetime:
    return datetime.datetime.now(datetime.timezone.utc)


async def _get_org_plan_details(user: User) -> OrganizationPlanDetails | None:
    if not user.organization:
        return None
    if not (plan := user.organization.plan):
        return None
    if not user.is_organization_admin:
        return None

    meetings_allowed = plan.meetings_allowed_per_subscription_term
    plan_term_in_days = 30 if plan.term == SubscriptionTerm.monthly else 365
    plan_start_date = plan.start_date.date()
    plan_term_start_date = plan_start_date + datetime.timedelta(
        days=plan_term_in_days * ((_now().date() - plan_start_date).days // plan_term_in_days)
    )
    meetings_count = await Note.objects.filter(
        note_owner__organization=user.organization,
        status__in=[Note.PROCESSING_STATUS.processed, Note.PROCESSING_STATUS.finalized],
        created__gte=plan_term_start_date,
        created__lt=plan_term_start_date + datetime.timedelta(days=plan_term_in_days),
    ).acount()

    logging.info(
        "Organization %s has %d meetings allowed per subscription term, and %d meetings created",
        user.organization.name,
        meetings_allowed,
        meetings_count,
    )

    return OrganizationPlanDetails(
        plan_term=SubscriptionTerm(plan.term).value,
        meetings_allowed_per_term=meetings_allowed,
        meetings_used_this_term=meetings_count,
        features=[
            PlanFeature(
                id=entitlement.type,
                label=EntitlementType(entitlement.type).label,
                license_count=entitlement.license_count,
                user_uuids=[user.uuid async for user in User.objects.filter(entitlements__contains=[entitlement.type])],
            )
            for entitlement in plan.entitlements
        ],
        users=[
            PlanUser(uuid=user.uuid, name=user.name, email=user.email)
            async for user in User.objects.filter(organization=user.organization)
        ],
    )


async def _get_plan_details(user: User) -> SectionDetails:
    entitlements = user.entitlements or []

    user = await User.objects.select_related("organization", "organization__plan").aget(id=user.id)

    if not user.organization or not hasattr(user.organization, "plan"):
        return SectionDetails(
            label="Your Plan and Features",
            data=[
                SectionItemLink(
                    id="contactSupport",
                    label="",
                    description="Please connect with us to view your plan and features",
                    text="Contact Support",
                    appTag="contact-support",
                ),
            ],
        )

    return SectionDetails(
        label="Your Plan and Features",
        data=[
            SectionItemPlanDetails(
                id="planDetails",
                label="Plan Details",
                plan_name=user.organization.plan.title,
                enabled_features=[EntitlementType(e).label for e in entitlements] if entitlements else [],
                organization_plan_details=await _get_org_plan_details(user),
            )
        ],
    )


# NOTE: For any data point, `None` indicates that the value should not be changed from its current server value
async def post_save(data: SaveRequest, user: User) -> int:
    oauth_integration_providers = set([o.integration async for o in OAuthCredentials.objects.filter(user=user)])

    if data.calendarAutoJoin is not None:
        if "google" in oauth_integration_providers:
            update_recall_auto_join_integration(
                user,
                data.calendarAutoJoin,
                GoogleOAuth(),
                RecallBotController().link_google_calendar,
            )
        elif "microsoft" in oauth_integration_providers:
            update_recall_auto_join_integration(
                user,
                data.calendarAutoJoin,
                MicrosoftOAuth(),
                RecallBotController().link_microsoft_calendar,
            )

    if data.calendarShowMeetingsWithoutUrls is not None:
        user.show_events_without_meeting_urls = data.calendarShowMeetingsWithoutUrls
        user.save()

    if data.calendarLookahead is not None:
        # TODO: @debojyotighosh cross-check the logic here; the "save" part should be inside `else` block.
        # Also default value is not being set, so alter the warning message accordingly
        if data.calendarLookahead in CalendarLookahead.values():
            user.calendar_lookahead = CalendarLookahead(data.calendarLookahead)
            user.save()

    # TODO: Test this before merging!!
    if data.bentoTagsEnabled is not None:
        try:
            bento_tags_template = StructuredMeetingDataTemplate.objects.get(
                kind=StructuredMeetingDataTemplate.Kind.BENTO_LIFE_EVENTS
            )
        except StructuredMeetingDataTemplate.DoesNotExist:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
        if data.bentoTagsEnabled:
            # Even if the user already has an org-level Bento tags rule, this setting is specific to
            # the user, so we create the rule regardless. We assume that the frontend is preventing
            # users from doing this when they should not, so we don't block creation here.
            has_user_level_bento_tags_rule = StructuredMeetingDataTemplateRule.objects.filter(
                users=user, follow_up_templates=bento_tags_template
            ).exists()
            if has_user_level_bento_tags_rule:
                return status.HTTP_200_OK
            rule = StructuredMeetingDataTemplateRule.objects.create(meeting_categories=["client"], everyone=False)
            rule.users.add(user)
            rule.follow_up_templates.add(bento_tags_template)
            rule.save()
        else:
            StructuredMeetingDataTemplateRule.objects.filter(
                users=user, follow_up_templates=bento_tags_template
            ).delete()

    return status.HTTP_200_OK
