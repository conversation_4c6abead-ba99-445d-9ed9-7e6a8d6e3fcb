import enum
import logging
from typing import Any
from uuid import UUID

from django.db.models import Q
from fastapi import APIRouter, Depends, HTTPException, status
from jsonschema import validate
from pydantic import BaseModel, ValidationError

from api.dependencies import user_from_authorization_header
from api.routers.note_models import FollowUp
from deepinsights.meetingsapp.models.client_interaction import ClientInteraction as DBClientInteraction
from deepinsights.meetingsapp.models.meeting_summary_email_template import (
    MeetingSummaryEmailTemplate as DBMeetingSummaryEmailTemplate,
)
from deepinsights.meetingsapp.models.meeting_type import MeetingType as DBMeetingType
from deepinsights.meetingsapp.models.note import is_authorized_to_view_note
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingData
from deepinsights.users.models.user import User

router = APIRouter(
    tags=["meeting_artifacts"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}"
)

public_router = APIRouter(
    tags=["meeting_artifacts"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}"
)


# A category for this meeting.
#
# Refer to the documentation on the MeetingType model for more information about what a "category"
# is.
class MeetingCategory(enum.StrEnum):
    CLIENT = "client"
    INTERNAL = "internal"
    DEBRIEF = "debrief"


# A meeting type.
#
# Refer to the MeetingType model for more information about what a "meeting type" is.
class MeetingType(BaseModel):
    # Identifier for this meeting type.
    uuid: UUID

    # The name of this meeting type, which can be in an user interface.
    name: str

    # The category for this meeting type.
    category: MeetingCategory

    # Whether or not this meeting type is shared with everyone.
    is_shared: bool


class MeetingTypesResponse(BaseModel):
    meeting_types: list[MeetingType]
    default_meeting_type: UUID | None


@router.get("/meeting_types")
@public_router.get(
    "/meeting_types",
    summary="Lists meeting types",
    description="This method is itself stateless, but it reads previously-stored state.",
)
async def meeting_types(user: User = Depends(user_from_authorization_header)) -> MeetingTypesResponse:
    user = await User.objects.select_related("organization").aget(uuid=user.uuid)
    default_meeting_type: UUID | None = None
    try:
        if user.get_preferences().default_meeting_type:
            default_meeting_type = UUID(user.get_preferences().default_meeting_type)
    except ValueError:
        logging.error(
            "User %s has an invalid default meeting type set: %s. Defaulting to None.",
            user.uuid,
            user.get_preferences().default_meeting_type,
        )
    meeting_types = [
        MeetingType(
            uuid=meeting_type.uuid,
            name=meeting_type.name,
            category=MeetingCategory(meeting_type.category),
            is_shared=meeting_type.everyone,
        )
        async for meeting_type in DBMeetingType.for_user(user).order_by("-everyone", "name")
    ]

    if default_meeting_type and not any([mt.uuid == default_meeting_type for mt in meeting_types]):
        logging.warning(
            "User %s has a default meeting type set that is not in their available meeting types. Defaulting to None.",
            user.uuid,
        )
        default_meeting_type = None
    return MeetingTypesResponse(
        meeting_types=meeting_types,
        default_meeting_type=default_meeting_type,
    )


class MeetingSummaryEmailTemplate(BaseModel):
    uuid: UUID
    name: str
    use_html: bool


@router.get("/meeting_summary_email_templates")
async def meeting_summary_email_templates(
    user: User = Depends(user_from_authorization_header),
) -> list[MeetingSummaryEmailTemplate]:
    templates_shared = [
        (
            template.everyone,
            MeetingSummaryEmailTemplate(
                uuid=template.uuid,
                name=template.name,
                use_html=template.use_html,
            ),
        )
        async for template in DBMeetingSummaryEmailTemplate.objects.filter(
            Q(everyone=True)
            | (Q(organizations__isnull=False) & Q(organizations=user.organization))
            | (Q(users__isnull=False) & Q(users=user))
        )
        .order_by("-everyone", "name")
        .distinct()
    ]

    return [t for _, t in templates_shared]


@router.post(
    "/follow-up/{follow_up_id}",
    description="Updates the data stored in a meeting follow up.",
    status_code=status.HTTP_204_NO_CONTENT,
    responses={
        400: {"description": "Invalid follow-up data"},
        403: {"description": "Not authorized to update this follow-up"},
        404: {"description": "Follow-up not found, or related note not found"},
    },
)
@public_router.post(
    "/follow-up/{follow_up_id}",
    summary="Updates the data stored in a meeting follow up.",
    description="The API will validate that the data is valid according to the schema for this follow-up. This is stateful, and will store data in the database.",
    status_code=status.HTTP_204_NO_CONTENT,
    responses={
        400: {"description": "Invalid follow-up data"},
        403: {"description": "Not authorized to update this follow-up"},
        404: {"description": "Follow-up not found, or related note not found"},
    },
)
def update_follow_up(
    follow_up_id: UUID, data: dict[str, Any], user: User = Depends(user_from_authorization_header)
) -> None:
    try:
        follow_up = StructuredMeetingData.objects.get(uuid=follow_up_id)
    except StructuredMeetingData.DoesNotExist:
        logging.error("Follow-up with id %s not found", follow_up_id)
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Follow-up not found")
    if (note := follow_up.note) and not is_authorized_to_view_note(user, note):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to edit this follow-up")
    for f in ["client_prep", "agenda", "advisor_notes"]:
        if interaction := DBClientInteraction.objects.filter(**{f: follow_up}).first():
            if not interaction.note or not is_authorized_to_view_note(user, interaction.note):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to edit this follow-up"
                )
            break
    if not note and not interaction:
        logging.error("Follow-up with ID %s has no related note or interaction", follow_up_id)
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Related model not found")
    try:
        if not follow_up.schema:
            logging.warning(
                "Follow-up with ID %s has no schema. The update will succeed, but this is unexpected.", follow_up_id
            )
        validate(data, follow_up.schema or True)
    except Exception as e:
        logging.error("Failed to validate follow-up data", exc_info=e)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid follow-up data")

    follow_up.data = data
    follow_up.save()


# A client interaction.
class ClientInteraction(BaseModel):
    # The ID of the interaction.
    uuid: UUID

    # The UUID of the related note.
    note_uuid: UUID | None

    # A structured meeting data object representing the client preparation.
    client_prep: FollowUp | None

    # A meeting data object representing the agenda.
    agenda: FollowUp | None

    # A meeting data object representing the advisor's notes.
    advisor_notes: FollowUp | None


def _interaction_response_from_interation(interaction: DBClientInteraction) -> ClientInteraction:
    return ClientInteraction(
        uuid=interaction.uuid,
        note_uuid=interaction.note.uuid if interaction.note else None,
        agenda=FollowUp.from_structured_meeting_data(interaction.agenda) if interaction.agenda else None,
        client_prep=None,
        advisor_notes=FollowUp.from_structured_meeting_data(interaction.advisor_notes)
        if interaction.advisor_notes
        else None,
    )


@router.get("/client_interaction/{interaction_uuid}")
async def get_client_interaction(
    interaction_uuid: UUID,
    user: User = Depends(user_from_authorization_header),
) -> ClientInteraction:
    user = await User.objects.select_related("organization").aget(uuid=user.uuid)
    try:
        interaction = (
            await DBClientInteraction.objects.select_related(
                "agenda", "advisor_notes", "note", "note__note_owner", "note__note_owner__organization"
            )
            .prefetch_related("note__authorized_users")
            .aget(uuid=interaction_uuid)
        )
        if not (note := interaction.note) or not is_authorized_to_view_note(user, note):
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view this interaction")
        return _interaction_response_from_interation(interaction)
    except DBClientInteraction.DoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Interaction not found")
    except ValidationError as e:
        logging.error("Failed to serialize interaction", exc_info=e)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to serialize interaction")


@router.get("/client_interaction")
@public_router.get(
    "/client_interaction",
    summary="Lists client interactions",
    description="This is itself stateless, but it reads previously-stored state.",
)
async def list_client_interactions(
    user: User = Depends(user_from_authorization_header),
) -> list[ClientInteraction]:
    user = await User.objects.select_related("organization").aget(uuid=user.uuid)
    interactions = (
        DBClientInteraction.objects.select_related(
            "agenda", "advisor_notes", "note", "note__note_owner", "note__note_owner__organization"
        )
        .order_by("-created")
        .only(
            "uuid",
            "agenda__uuid",
            "agenda__title",
            "agenda__kind",
            "agenda__schema",
            "agenda__data",
            "agenda__status",
            "advisor_notes__uuid",
            "advisor_notes__title",
            "advisor_notes__kind",
            "advisor_notes__schema",
            "advisor_notes__data",
            "advisor_notes__status",
            "note__uuid",
            "note__note_owner",
            "note__note_owner__organization",
            "note__authorized_users",
        )  # omit fields not needed due to the high memory cost of all the note fields
    )

    # Note that the authorization logic here does not exactly match the logic for which interactions
    # a user is allowed to view: superusers can view all interactions, but we don't want them to get
    # all interactions, because that would create a load on the system, and the interactions list is
    # likely only useful for matching up notes with interaction data or for listing interactions in
    # the UI.  So, superusers will get an interaction list that matches their license type.
    if user.license_type == User.LicenseType.csa or user.license_type == User.LicenseType.staff:
        interactions = interactions.filter(note__isnull=False, note__note_owner__organization=user.organization)
    else:
        # Check that the list of authorized users includes the user (and is not empty, as a
        # safeguard against an error where user is None)
        interactions = interactions.filter(
            note__isnull=False, note__authorized_users__isnull=False, note__authorized_users=user
        )
    response: list[ClientInteraction] = []
    async for interaction in interactions:
        try:
            response.append(_interaction_response_from_interation(interaction))
        except Exception as e:
            logging.error("Failed to serialize interaction", exc_info=e)
            continue
    return response
