import asyncio
import enum
import logging
from typing import AsyncIterator
from uuid import UUID

import phonenumbers
import pydantic
from asgiref.sync import sync_to_async
from django.core.exceptions import ValidationError
from django.db.models import QuerySet
from fastapi import APIRouter, Depends, Form, HTTPException, Request, Response, status
from fastapi.responses import StreamingResponse
from twilio.twiml.voice_response import Gather, VoiceResponse

from api.dependencies import user_from_access_token, user_from_authorization_header
from deepinsights.core.integrations.meetingbot.bot_controller import BotMeetingType, BotResponse, BotStatus
from deepinsights.core.integrations.meetingbot.twilio import (
    TwilioConferenceBotController,
    pin_webhook,
    recording_webhook,
)
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.meeting_bot import MeetingBot
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.tasks import process_phone_call_recording
from deepinsights.users.models.user import User

router = APIRouter(
    tags=["bot"],
    generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}",
)


# A logging handler that looks for a message indicating that the server is waiting for connections
# to close. If it receives such a message, it sets a flag to indicate that the connection is waiting
# to close.
#
# This is terribly hackish, but there isn't a great way to get this information from the worker
# otherwise: this doesn't trigger a signal, and all of the lifecycle callbacks that we would
# typically use wait for the connections to close before they are called. We need to know that the
# worker is waiting for connections to close in order that it can be shut down.
class CloseWaitLogHandler(logging.Handler):
    waiting_to_close: bool = False

    def emit(self, record: logging.LogRecord) -> None:
        if "Waiting for connections to close." not in record.getMessage():
            return
        logging.info("Setting waiting_to_close flag")
        self.waiting_to_close = True


close_wait_logger = logging.getLogger("uvicorn.error")
handler = CloseWaitLogHandler()
close_wait_logger.addHandler(CloseWaitLogHandler())


async def is_authorized_to_view_bot(user: User, bot: MeetingBot) -> bool:
    bot = (
        await MeetingBot.objects.select_related("note", "note__note_owner", "note__note_owner__organization")
        .prefetch_related("note__authorized_users")
        .aget(uuid=bot.uuid)
    )
    if not bot.note:
        return True
    return (
        user in bot.note.authorized_users.all()
        or user.is_superuser
        or (
            (user.license_type == User.LicenseType.csa or user.license_type == User.LicenseType.staff)
            and (user.organization == bot.note.note_owner.organization if bot.note.note_owner else False)
        )
    )


async def _get_authorized_bot_or_raise(user: User, bot_uuid: UUID) -> MeetingBot:
    try:
        bot: MeetingBot = await MeetingBot.objects.select_related("bot_owner").aget(uuid=bot_uuid)
    except MeetingBot.DoesNotExist:
        logging.error("Bot not found", exc_info=True)
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)
    if not await is_authorized_to_view_bot(user, bot):
        logging.error("User not authorized to access bot", exc_info=True)
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN)
    return bot


@router.get(
    "/{bot_uuid}/events",
    response_class=StreamingResponse,
    response_description=(
        "A server-sent event `text/event-stream`, with no event type and data being the "
        "underlying value of a BotStatus enum. e.g., \n```\ndata: in_waiting_room\\n\\n\n```"
    ),
)
async def stream_bot_events(
    request: Request, bot_uuid: UUID, user: User = Depends(user_from_access_token)
) -> StreamingResponse:
    bot = await _get_authorized_bot_or_raise(user, bot_uuid)
    bot_id = bot.recall_bot_id
    if not bot_id:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST)

    async def events_generator() -> AsyncIterator[str]:
        while not await request.is_disconnected() and not handler.waiting_to_close:
            bot_status = await bot.aget_status()
            yield f"data: {bot_status.value}\n\n"
            await asyncio.sleep(1)

    return StreamingResponse(
        events_generator(),
        media_type="text/event-stream",
        headers={"Cache-Control": "no-cache", "X-Accel-Buffering": "no"},
    )


class Bot(pydantic.BaseModel):
    uuid: UUID = pydantic.Field(description="The UUID of the bot")
    platform_id: str | None = pydantic.Field(
        description="The ID of the bot in the underlying bot platform (e.g., a Recall bot ID)"
    )
    meeting_link: str | None = pydantic.Field(
        description="The link to the meeting that the bot will try to join or has joined"
    )
    status: BotStatus = pydantic.Field(description="The current status of the bot")
    supports_pause_resume: bool = pydantic.Field(description="Whether the bot supports pausing and resuming recording")
    meeting_type: BotMeetingType = pydantic.Field(description="The type of meeting that the bot is in")


@router.get("/{bot_uuid}")
async def get_bot(bot_uuid: UUID, user: User = Depends(user_from_authorization_header)) -> Bot:
    bot = await _get_authorized_bot_or_raise(user, bot_uuid)
    return Bot(
        uuid=bot.uuid,
        platform_id=bot.recall_bot_id,
        meeting_link=bot.meeting_link,
        status=(await bot.aget_status()).value,
        supports_pause_resume=await sync_to_async(lambda: bot.supports_pause_resume)(),
        meeting_type=await sync_to_async(lambda: bot.meeting_type)(),
    )


class BotRecordingAction(enum.Enum):
    start = "start"
    pause = "pause"
    resume = "resume"
    leave = "leave"


async def _do_recording_action(
    user: User,
    bot_uuid: UUID,
    action: BotRecordingAction,
) -> Response:
    bot = await _get_authorized_bot_or_raise(user, bot_uuid)
    if action == BotRecordingAction.start:
        if bot.recall_bot_id:
            logging.error("Recall bot (%s) already exists for bot UUID %s", bot.recall_bot_id, bot_uuid)
            return Response(status_code=status.HTTP_400_BAD_REQUEST)
        await sync_to_async(bot.create_bot_and_start_recording)()
        if await bot.aget_status() == BotStatus.NOT_CREATED:
            result = BotResponse(status=status.HTTP_500_INTERNAL_SERVER_ERROR, details={})
        else:
            result = BotResponse(status=status.HTTP_200_OK, details={})
    elif action == BotRecordingAction.pause:
        result = await sync_to_async(bot.pause_recording)()
    elif action == BotRecordingAction.resume:
        result = await sync_to_async(bot.resume_recording)()
    elif action == BotRecordingAction.leave:
        result = await sync_to_async(bot.leave_meeting)()
    else:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST)

    if result.status != status.HTTP_200_OK:
        logging.error("Recording action '%s' failed with error %s", action, result.details, exc_info=True)
    return Response(status_code=result.status)


@router.post("/{bot_uuid}/start")
async def start_recording(bot_uuid: UUID, user: User = Depends(user_from_authorization_header)) -> Response:
    return await _do_recording_action(user, bot_uuid, BotRecordingAction.start)


@router.post("/{bot_uuid}/pause")
async def pause_recording(bot_uuid: UUID, user: User = Depends(user_from_authorization_header)) -> Response:
    return await _do_recording_action(user, bot_uuid, BotRecordingAction.pause)


@router.post("/{bot_uuid}/resume")
async def resume_recording(bot_uuid: UUID, user: User = Depends(user_from_authorization_header)) -> Response:
    return await _do_recording_action(user, bot_uuid, BotRecordingAction.resume)


@router.post("/{bot_uuid}/leave")
async def leave_meeting(bot_uuid: UUID, user: User = Depends(user_from_authorization_header)) -> Response:
    return await _do_recording_action(user, bot_uuid, BotRecordingAction.leave)


def _response_for_twilio_response(twilio_response: VoiceResponse) -> Response:
    """Returns an HTTP response with the given Twilio TwiML response."""
    return Response(
        status_code=status.HTTP_200_OK,
        content=str(twilio_response),
        media_type="text/xml",
    )


def _empty_twilio_response() -> Response:
    """Returns an empty HTTP response for Twilio webhooks."""
    return Response(status_code=status.HTTP_204_NO_CONTENT)


class RecordingEvent(pydantic.BaseModel):
    RecordingSid: str | None = None
    RecordingStatus: str | None = None
    ConferenceSid: str | None = None
    CallSid: str | None = None


@router.post("/twilio/recording", include_in_schema=False)
async def handle_twilio_recording_event(event: RecordingEvent = Form()) -> Response:
    logging.info("Twilio recording event received: %s", event)
    if not event.RecordingSid:
        logging.error("No recording SID found in Twilio recording status webhook callback")
        return Response(status_code=status.HTTP_400_BAD_REQUEST)
    if not (twilio_id := event.ConferenceSid or event.CallSid):
        logging.error("No conference or call SID found in Twilio recording status webhook request")
        return Response(status_code=status.HTTP_400_BAD_REQUEST)
    if not (recording_status := event.RecordingStatus):
        logging.error("No recording status found in Twilio recording status webhook request")
        return Response(status_code=status.HTTP_400_BAD_REQUEST)

    if recording_status != "completed":
        logging.info("Recording status is %s, not 'completed', skipping", recording_status)
        return _empty_twilio_response()

    try:
        bot = await MeetingBot.objects.select_related("note").aget(recall_bot_id=twilio_id)
    except MeetingBot.DoesNotExist:
        return _empty_twilio_response()

    if not bot.note:
        logging.error("No note found for bot with UUID %s", bot.uuid)
        return _empty_twilio_response()

    bot.note.status = Note.PROCESSING_STATUS.uploaded
    await bot.note.asave()
    process_phone_call_recording.delay(bot.uuid, False)
    return _empty_twilio_response()


class ConferenceEvent(pydantic.BaseModel):
    ConferenceSid: str | None = None
    StatusCallbackEvent: str | None = None


@router.post("/twilio/on_participant_join", include_in_schema=False)
async def handle_twilio_participant_join_event() -> Response:
    response = VoiceResponse()
    response.pause(8)
    response.say("This meeting is being transcribed.")
    return _response_for_twilio_response(response)


@router.post("/twilio/conference", include_in_schema=False)
async def handle_twilio_conference_event(event: ConferenceEvent = Form()) -> Response:
    logging.info("Twilio conference event received: %s", event)
    if not (conference_sid := event.ConferenceSid):
        logging.error("No conference SID found in Twilio conference status webhook request")
        return Response(status_code=status.HTTP_400_BAD_REQUEST)
    if not (conference_status := event.StatusCallbackEvent):
        logging.error("No conference status found in Twilio conference status webhook request")
        return Response(status_code=status.HTTP_400_BAD_REQUEST)
    try:
        bot = await MeetingBot.objects.select_related("note", "bot_owner", "bot_owner__organization").aget(
            recall_bot_id=conference_sid
        )
    except MeetingBot.DoesNotExist:
        logging.error("Meeting bot does not exist for conference SID %s", conference_sid)
        return _empty_twilio_response()

    if conference_status == "conference-end":
        if not bot.note:
            logging.error("No note found for bot with UUID %s", bot.uuid)
            return _empty_twilio_response()
        bot.note.status = Note.PROCESSING_STATUS.uploaded
        await bot.note.asave()
    elif conference_status == "participant-join":
        try:
            cleaned_client_phone_number = phonenumbers.parse(bot.meeting_link or "", "US")
        except phonenumbers.phonenumberutil.NumberParseException as e:
            logging.error("Invalid phone number requested as target phone number: %s", bot.meeting_link, exc_info=e)
            return Response(status_code=status.HTTP_400_BAD_REQUEST)
        bot_controller = TwilioConferenceBotController(conference_sid)
        try:
            added = await bot_controller.add_participant_if_not_in_call(
                cleaned_client_phone_number,
                bot.bot_owner.organization.phone_number if (bot.bot_owner and bot.bot_owner.organization) else None,
            )
            if not added:
                await bot_controller.make_recording_announcement(cleaned_client_phone_number)
        except Exception as e:
            logging.error(
                "Failed to add participant to Twilio conference, or make a recording announcement to a new participant: %s",
                e,
            )
    return _empty_twilio_response()


async def _start_recording_call_for_user(users: QuerySet[User], call_sid: str, from_number: str) -> Response:
    """Starts recording a call for the given user(s) and returns a Twilio response."""

    # Create a new note for the user(s).
    note_owner = await users.afirst()
    meeting_type = await MeetingType.objects.aget(key="client")
    if (
        note_owner
        and (
            meeting_type_user := await User.objects.select_related("organization")
            .only("preferences", "organization__preferences")
            .aget(id=note_owner.id)
        )
        and (user_meeting_type := meeting_type_user.get_preferences().default_meeting_type)
    ):
        try:
            meeting_type = await MeetingType.objects.aget(uuid=user_meeting_type)
        except (ValidationError, MeetingType.DoesNotExist):
            logging.warning(
                "User %s has a default meeting type %s that does not exist. Using default client meeting type instead.",
                note_owner.uuid,
                user_meeting_type,
            )
    note = await Note.objects.acreate(
        note_owner=note_owner,
        note_type=Note.NOTE_TYPE.meeting_recording,
        status=Note.PROCESSING_STATUS.scheduled,
        metadata={"meeting_name": "Recorded phone call"},
        meeting_type=meeting_type,
    )
    [await Attendee.objects.acreate(note=note, user=user, attendee_name=user.name) async for user in users]
    await note.authorized_users.aset(users)
    await note.asave()
    await MeetingBot.objects.acreate(
        note=note,
        bot_owner=note_owner,
        recall_bot_id=call_sid,
        meeting_link=from_number,  # This matches the user's number, indicating that this is a single-party call.
    )

    # Start recording.
    response = VoiceResponse()
    # If the first verb in a TwiML response is a <Pause> verb, Twilio will not pick up the call
    # until the pause is over.
    response.say("")
    response.pause(8)
    response.say("This meeting is being transcribed.")
    response.record(
        timeout=2 * 60 * 60,
        recording_status_callback=recording_webhook(),
        recording_status_callback_event="completed",
    )
    return _response_for_twilio_response(response)


class CallEvent(pydantic.BaseModel):
    CallSid: str | None = None
    CallStatus: str | None = None
    Direction: str | None = None
    From: str | None = None


@router.post("/twilio/call", include_in_schema=False)
async def handle_twilio_call_event(request: Request, event: CallEvent = Form()) -> Response:
    logging.info("Twilio call event received: %s", event)

    if not (call_sid := event.CallSid):
        logging.error("No call SID found in Twilio call status webhook request")
        return Response(status_code=status.HTTP_400_BAD_REQUEST)
    if not (call_status := event.CallStatus):
        logging.error("No call status found in Twilio call status webhook request")
        return Response(status_code=status.HTTP_400_BAD_REQUEST)
    is_inbound = event.Direction == "inbound"

    # If this is a new inbound call, we need to create a corresponding note and start recording.
    if is_inbound and call_status == "ringing":
        # Find a user with a matching phone number.
        if not (from_number := event.From) or not isinstance(from_number, str):
            logging.error("Missing or invalid 'From' number in Twilio inbound call request")
            return Response(status_code=status.HTTP_400_BAD_REQUEST)
        cleaned_from_number = phonenumbers.parse(from_number, "US")
        users = User.objects.filter(phone_numbers__number=cleaned_from_number).distinct().order_by("id")
        if await users.acount() > 0:
            return await _start_recording_call_for_user(users, call_sid, from_number)

        # If no user was found, check if the number belongs to an organization.
        try:
            await Organization.objects.aget(phone_number=cleaned_from_number)
        except (Organization.DoesNotExist, Organization.MultipleObjectsReturned) as e:
            logging.error("Cannot match inbound phone number to user or organization: %s", from_number, exc_info=e)
            response = VoiceResponse()
            response.reject()
            return _response_for_twilio_response(response)

        response = VoiceResponse()
        gather = Gather(
            action=pin_webhook(),
            finish_on_key="#",
            method="POST",
            timeout=20,
        )
        gather.say("Please enter your PIN, followed by the pound sign.")
        response.append(gather)
        response.hangup()
        return _response_for_twilio_response(response)

    if call_status != "completed":
        return _empty_twilio_response()

    # Completed call; prepare to process.
    try:
        bot = await MeetingBot.objects.select_related("note").aget(recall_bot_id=call_sid)
    except MeetingBot.DoesNotExist:
        logging.error("No bot found for call SID %s", call_sid)
        return _empty_twilio_response()
    if not bot.note:
        logging.error("No note found for bot with UUID %s", bot.uuid)
        return _empty_twilio_response()
    bot.note.status = Note.PROCESSING_STATUS.uploaded
    await bot.note.asave()
    return _empty_twilio_response()


class CallEventWithDigits(CallEvent):
    Digits: str | None = None


@router.post("/twilio/pin", include_in_schema=False)
async def handle_twilio_call_event_with_pin(event: CallEventWithDigits = Form()) -> Response:
    def say_and_hangup(message: str) -> Response:
        response = VoiceResponse()
        response.say(message)
        response.hangup()
        return _response_for_twilio_response(response)

    # Handle unexpectedly-invalid form data.
    if not (call_sid := event.CallSid):
        logging.error("No call SID found in Twilio call status webhook request")
        return Response(status_code=status.HTTP_400_BAD_REQUEST)
    if not (call_status := event.CallStatus):
        logging.error("No call status found in Twilio call status webhook request")
        return Response(status_code=status.HTTP_400_BAD_REQUEST)
    if not (from_number := event.From) or not isinstance(from_number, str):
        logging.error("Missing or invalid 'From' number in Twilio inbound call request")
        return Response(status_code=status.HTTP_400_BAD_REQUEST)

    # Valid form data that is not valid for our use case.
    is_inbound = event.Direction == "inbound"
    if not is_inbound:
        logging.error("PIN input webhook received for outbound call")
        return _empty_twilio_response()
    if call_status in ["completed", "failed", "canceled", "no-answer", "busy"]:
        logging.warning("Call %s ended before PIN could be processed. Status: %s", call_sid, call_status)
        return _empty_twilio_response()
    if not (pin := event.Digits):
        logging.error("No digits found in Twilio PIN input webhook request")
        return _empty_twilio_response()
    if not pin.isdigit():
        logging.error("Invalid PIN %s in Twilio PIN input webhook request", pin)
        return say_and_hangup("We're sorry, but we could not recognize that PIN. Please contact support.")

    cleaned_from_number = phonenumbers.parse(from_number, "US")
    if not (org := await Organization.objects.filter(phone_number=cleaned_from_number).afirst()):
        logging.error("Organization not found for number %s during PIN handling for call %s", from_number, call_sid)
        return say_and_hangup("We're sorry, but we could not recognize that PIN. Please contact support.")
    try:
        user = await User.objects.aget(organization=org, pin=pin)
    except User.DoesNotExist:
        logging.warning("User not found for PIN %s during PIN handling for call %s", pin, call_sid)
        return say_and_hangup("We're sorry, but we could not recognize that PIN. Please redial and try again.")
    except User.MultipleObjectsReturned:
        logging.error("Multiple users found for PIN %s during PIN handling for call %s", pin, call_sid)
        return say_and_hangup("We're sorry, but we could not recognize that PIN. Please contact support.")

    return await _start_recording_call_for_user(User.objects.filter(uuid=user.uuid), call_sid, from_number)
