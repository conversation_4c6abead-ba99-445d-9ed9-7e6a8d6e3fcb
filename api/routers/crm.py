import base64
import logging
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, Response, status
from pydantic import BaseModel, Field, ValidationError

from api.dependencies import user_from_authorization_header
from deepinsights.core.integrations.crm.crm_models import CRMSyncTargets, UploadNoteToCRMRequest
from deepinsights.core.integrations.crm.redtail import Redtail
from deepinsights.core.ml.emails import email_meeting_notes
from deepinsights.core.preferences.preferences import CrmConfiguration, DefaultClientFilter
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import CRMSyncTargetInfo, Note, is_authorized_to_view_note
from deepinsights.meetingsapp.tasks import sync_crm_clients
from deepinsights.users.models.user import User

logger = logging.getLogger(__name__)

router = APIRouter(tags=["crm"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}")
public_router = APIRouter(
    tags=["crm"],
    generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}",
)


class RedtailCredentials(BaseModel):
    username: str
    password: str


class ClientResponse(BaseModel):
    uuid: str
    name: str
    type: str


class CursorData(BaseModel):
    uuid: str
    name: str


class ClientListResponse(BaseModel):
    client_selection_enabled: bool
    clients: list[ClientResponse]
    crm_system: str | None
    next_page_token: str | None = Field(
        None, description="Token to fetch the next page. Null if there are no more results."
    )


def _validate_note_and_crm(note_id: UUID, user: User) -> tuple[Note, User]:
    try:
        note = Note.objects.get(uuid=note_id)
    except Note.DoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Could not find the note")

    if not is_authorized_to_view_note(user, note):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to access this note")

    if not note.note_owner:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Note does not have an owner")

    return note, note.note_owner


class CRMUploadTarget(BaseModel):
    """Information about a CRM entity that can be used as a target for uploading data to a CRM."""

    id: str = Field(
        ..., description="The identifier for the CRM target, typically a UUID or similar unique identifier."
    )

    name: str = Field(
        ..., description="A user-friendly name for the CRM target, which can be displayed in the user selection UI."
    )


class UploadNoteToCRMResponse(BaseModel):
    user_input_required: bool = Field(
        False, description="Indicates if user input is required before uploading the note to CRM"
    )
    note_sync_targets: list[CRMUploadTarget] | None = Field(
        None,
        description="List of CRM targets to which the note can be uploaded. Only provided if user input is required.",
    )


@router.post(
    "/upload_note/{note_id}",
    responses={
        204: {"description": "Note uploaded to CRM successfully"},
        400: {"description": "Bad request"},
        403: {"description": "Not authorized"},
        404: {"description": "Note not found"},
        500: {"description": "Internal server error"},
    },
)
@public_router.post(
    "/upload_note/{note_id}",
    responses={
        204: {"description": "Note uploaded to CRM successfully"},
        400: {"description": "Bad request"},
        403: {"description": "Not authorized"},
        404: {"description": "Note not found"},
        500: {"description": "Internal server error"},
    },
    summary="Uploads information from a note to the CRM",
    description=(
        "Uploads information from a note to the CRM. The note must be associated with a client. "
        "This is stateful: it copies note data from Zeplyn into the CRM."
    ),
)
def upload_note_to_crm(
    note_id: UUID,
    request: UploadNoteToCRMRequest = UploadNoteToCRMRequest(),
    sync_target_ids: list[str] | None = Query(None),
    user: User = Depends(user_from_authorization_header),
) -> UploadNoteToCRMResponse:
    note, note_owner = _validate_note_and_crm(note_id, user)
    try:
        sync_items = request.sync_items
        crm_handler = note_owner.crm_handler

        # Get any previous sync target info from the note.
        #
        # We store sync target info on the note to avoid having to pass the full sync target info to the client;
        # and, so that in case a sync session is interrupted, we can continue from where we left off.
        try:
            previous_sync_target_info = (
                CRMSyncTargetInfo.model_validate(note.crm_sync_targets) if note.crm_sync_targets else None
            )
        except ValidationError as e:
            logger.error("Error validating previous sync targets for note %s: %s. Resetting to default.", note_id, e)
            note.crm_sync_targets = None
            note.save()
            previous_sync_target_info = None

        # Filter out sync targets to only include those selected by the user.
        if (
            sync_target_ids
            and any(sync_target_ids)
            and previous_sync_target_info
            and previous_sync_target_info.sync_targets
        ):
            previous_sync_target_info.sync_targets.note_targets = [
                target
                for target in (previous_sync_target_info.sync_targets.note_targets or [])
                if target.crm_id in sync_target_ids
            ]

        # Resolve the next set of sync targets.
        sync_targets = crm_handler.resolve_sync_targets(
            note, note_owner, previous_sync_target_info.sync_targets if previous_sync_target_info else None
        )

        # Update the note with the updated sync targets.
        note.crm_sync_targets = CRMSyncTargetInfo(sync_targets=sync_targets).model_dump(mode="json")
        note.save()

        # If the user needs to select a client, return an appropriate error.
        if sync_targets.status == CRMSyncTargets.Status.CLIENTS_REQUIRED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Note does not have a CRM client associated with it"
            )

        # If the user needs to further resolve sync targets, return a response indicating that more
        # input is required.
        if sync_targets.status == CRMSyncTargets.Status.RESOLUTION_REQUIRED:
            return UploadNoteToCRMResponse(
                user_input_required=True,
                note_sync_targets=[
                    CRMUploadTarget(id=target.crm_id, name=target.name) for target in sync_targets.note_targets
                ],
            )

        # If the sync targets are final, proceed to upload the note.
        crm_handler.add_interaction_with_client(note, sync_targets, sync_items=sync_items)
        notification_preferences = user.get_preferences().notification_preferences
        if notification_preferences.email_meeting_notes_post_sync:
            email_meeting_notes(str(note_id), user)
        note.status = Note.PROCESSING_STATUS.finalized
        note.save()

        return UploadNoteToCRMResponse(user_input_required=False)
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error uploading note to CRM: %s", note_id, exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error uploading note to CRM: {str(e)}"
        )


@router.get(
    "/clients",
    response_model=ClientListResponse,
    responses={
        500: {"description": "Internal server error"},
    },
)
def get_client_list(
    q: str | None = None,
    cursor: str | None = None,
    page_size: int | None = None,
    user: User = Depends(user_from_authorization_header),
) -> ClientListResponse:
    use_pagination = page_size and page_size > 0

    if not (crm_system := user.get_crm_configuration().crm_system) or crm_system == "none":
        return ClientListResponse(client_selection_enabled=False, clients=[], crm_system=None, pagination_metadata=None)

    try:
        query_filters = {
            "authorized_users__isnull": False,
            "authorized_users": user,
            "crm_system": crm_system,
        }

        if q:
            query_filters["name__icontains"] = q

        match client_filter := user.get_crm_configuration().default_client_filter:
            case DefaultClientFilter.ALL:
                pass
            case DefaultClientFilter.OWNED:
                query_filters["owner"] = user
            case _:
                logging.warning("Unknown default client filter: %s", client_filter)

        base_query = Client.objects.filter(**query_filters).only("uuid", "name", "client_type")

        # If cursor is provided, add filter to get records after the cursor
        if cursor:
            try:
                json_str = base64.b64decode(cursor).decode("utf-8")
                cursor_data = CursorData.model_validate_json(json_str)
                greater_name_filter = base_query.filter(name__gt=cursor_data.name)
                same_name_filter = base_query.filter(name=cursor_data.name, uuid__gt=cursor_data.uuid)
                base_query = greater_name_filter | same_name_filter
            except Exception as e:
                logger.warning(f"Invalid cursor format: {cursor} - {e}")
        base_query = base_query.order_by("name", "uuid")

        if use_pagination:
            assert page_size is not None
            clients = base_query[: page_size + 1]
            has_next_page = len(clients) > page_size

            next_page_token = None
            if has_next_page:
                last_client = clients[page_size - 1]
                clients = clients[:page_size]

                cursor_data = CursorData(name=last_client.name, uuid=str(last_client.uuid))
                next_page_token = base64.b64encode(cursor_data.model_dump_json().encode("utf-8")).decode("utf-8")

        else:
            # No pagination, fetch all clients
            clients = base_query.all()
            next_page_token = None
        client_list = [
            ClientResponse(uuid=str(client.uuid), name=client.name, type=client.client_type) for client in clients
        ]
        return ClientListResponse(
            client_selection_enabled=True,
            clients=client_list,
            crm_system=crm_system,
            next_page_token=next_page_token,
        )
    except Exception as e:
        logger.error("Error getting client list for user %s", user.uuid, exc_info=e)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.post(
    "/redtail/generate-key",
    responses={
        204: {"description": "User key generated successfully"},
        400: {"description": "Bad request"},
        500: {"description": "Internal server error"},
    },
)
def generate_redtail_user_key(
    credentials: RedtailCredentials, user: User = Depends(user_from_authorization_header)
) -> Response:
    try:
        _set_user_crm_system(user, "redtail")
        user.refresh_from_db()

        crm_handler = user.crm_handler
        if not crm_handler or not isinstance(crm_handler, Redtail):
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="User is not in a redtail enabled org")

        user_key = crm_handler.generate_user_key_code(credentials.username, credentials.password)
        if not user_key:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to generate user key")

        updated_crm_config = CrmConfiguration.from_dict(user.crm_configuration)
        updated_crm_config.redtail.user_key = user_key
        user.crm_configuration = updated_crm_config.to_dict()
        user.save()

        sync_crm_clients.delay_on_commit(user.uuid)
        return Response(status_code=status.HTTP_204_NO_CONTENT)

    except Exception as e:
        logger.error("Failed to generate user key for user %s", user.uuid, exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to generate redtail user key"
        )


class RedtailStatusResponse(BaseModel):
    active: bool


@router.get(
    "/redtail/status",
    responses={
        200: {"description": "Success", "content": {"application/json": {"example": {"integration_status": "Active"}}}},
        400: {"description": "Bad request"},
        500: {"description": "Internal server error"},
    },
)
def get_redtail_integration_status(user: User = Depends(user_from_authorization_header)) -> RedtailStatusResponse:
    crm_handler = user.crm_handler
    if not crm_handler or not isinstance(crm_handler, Redtail):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="User and user's organization do not use Redtail as a CRM"
        )

    try:
        return RedtailStatusResponse(active=bool(user.get_crm_configuration().redtail.user_key))
    except Exception as e:
        logger.error("Failed to get Redtail integration status for user %s", user.uuid, exc_info=e)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to get Redtail status")


def _set_user_crm_system(user: User, system: str) -> None:
    crm_config = user.get_crm_configuration()
    crm_config.crm_system = system
    user.crm_configuration = crm_config.to_dict()
    user.save()
