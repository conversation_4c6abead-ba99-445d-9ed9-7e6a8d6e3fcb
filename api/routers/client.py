import datetime
import logging
import traceback
from typing import Any
from uuid import UUID, uuid4

from django.db.models import Prefetch
from fastapi import APIRouter, Body, Depends, HTTPException, Path, status
from pydantic import BaseModel, Field, HttpUrl, ValidationError

from api.dependencies import user_from_authorization_header
from api.routers.note_models import ListNotesResponse, list_notes_response
from api.routers.task_models import TaskResponse, get_lite_task_response
from deepinsights.core.integrations.crm.crm_models import CRMNote
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.client_recap import <PERSON>lientRecap as DBClientRecap
from deepinsights.meetingsapp.models.client_recap import ClientRecapStatus
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.tasks import process_client_recap
from deepinsights.users.models.user import User

public_router = APIRouter(tags=["client"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}")
internal_router = APIRouter(tags=["client"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}")

logger = logging.getLogger(__name__)


# A reference to a source of information in the recap.
class ClientRecapReference(BaseModel):
    # The ID of the reference.
    #
    # This could be a CRM ID, a Zeplyn note ID, or an arbitrary identifier related to the source.
    id: str

    # The source of this reference.
    #
    # For Zeplyn notes, this will be "Zeplyn"; for CRM notes, this will be the CRM system name; for
    # other data, this will have an arbitrary value.
    source: str

    # A web link that can be used to access the source material for this reference.
    link: HttpUrl | None

    # Text that can be shown to the user when hovering over the reference link.
    hover_text: str | None = None


# A bullet point in the recap.
class ClientRecapBullet(BaseModel):
    # The text of the bullet point.
    text: str

    # Indices into the array of references, indicating which references are related to this bullet.
    references: list[int]


# A category of bullet points in the recap.
class ClientRecapCategory(BaseModel):
    # The topic of the category
    topic: str

    # The bullet points in this category
    bullets: list[ClientRecapBullet]


# A client recap, providing LLM-generated information about a client.
class ClientRecap(BaseModel):
    # The recap itself.
    recap: list[ClientRecapCategory]

    # References to the sources of information in the recap.
    references: list[ClientRecapReference]

    # When the recap was created.
    created_at: datetime.datetime

    @classmethod
    def from_db_client_recap(cls, client_recap: DBClientRecap) -> "ClientRecap | None":
        summary = client_recap.get_client_recap_summary()
        return (
            cls.model_validate(
                {
                    "recap": summary.get("client_recap_summary", []),
                    "references": [{**r, "id": r.get("meeting_uuid")} for r in summary.get("references", [])],
                    "created_at": client_recap.created,
                }
            )
            if summary
            else None
        )


# Information about a client.
class ClientResponse(BaseModel):
    # The full name of the client.
    name: str

    # The status of the latest client recap for this client (whether valid or not).
    recap_status: ClientRecapStatus | None

    # The latest valid client recap for this client (which may not be the recap or which
    # recap_status was returned, if there is a newer but invalid recap).
    recap: ClientRecap | None

    # A list of notes that are related to this client.
    notes: list[ListNotesResponse]

    # Basic information (e.g., net worth, income) about the client.
    basic_info: dict[str, Any]

    # A list of tasks related to this client.
    action_items: list[TaskResponse] | None


# Info about a client recap.
class ClientRecapResponse(BaseModel):
    # The status of the client recap.
    status: ClientRecapStatus

    # The UUID of the client recap (if it exists).
    uuid: UUID | None = None


def __is_authorized_to_view_client(user: User, client: Client) -> bool:
    return (
        user in client.authorized_users.all()
        or user.is_superuser
        or (
            (user.license_type == User.LicenseType.csa or user.license_type == User.LicenseType.staff)
            and user.organization == client.organization
        )
    )


@internal_router.get(
    "/{client_id}",
    response_model=ClientResponse,
    responses={
        200: {"description": "Successfully retrieved client details"},
        403: {"description": "Not authorized to view this client"},
        404: {"description": "Client not found"},
        500: {"description": "Internal server error"},
    },
    summary="Get client details",
    description="Get detailed information about a specific client including the client recap, notes, and action items",
)
def get_client(
    client_id: UUID = Path(..., description="The UUID of the client to retrieve"),
    user: User = Depends(user_from_authorization_header),
) -> ClientResponse:
    try:
        client = Client.objects.get(uuid=client_id)
    except Client.DoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Client not found")

    if not __is_authorized_to_view_client(user, client):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view another user's client"
        )

    try:
        latest_processed_recap = (
            DBClientRecap.objects.filter(client=client, status=ClientRecapStatus.PROCESSED).order_by("-created").first()
        )
        client_recap_summary = latest_processed_recap.get_client_recap_summary() if latest_processed_recap else None
        client_recap_creation_date = latest_processed_recap.created if latest_processed_recap else None
        latest_recap = DBClientRecap.objects.filter(client=client).order_by("-created").first()

        notes = (
            Note.objects.filter(client__uuid=str(client.uuid))
            .defer(
                "raw_asr_response",
                "raw_transcript",
                "diarized_trans_with_names",
                "advisor_notes",
                "key_takeaways",
                "summary_by_topics",
                "summary",
                "raw_asr_response",
                "follow_up_email_contents",
            )
            .order_by("-created")
            .prefetch_related(
                Prefetch("attendees", queryset=Attendee.objects.all().only("note", "uuid", "attendee_name")),
            )
        )

        notes_list: list[ListNotesResponse] = []
        action_items: list[TaskResponse] = []

        for note in notes:
            try:
                notes_list.append(list_notes_response(note))
            except ValidationError as v:
                logger.warning("Error serializing note %s: ", note.uuid, exc_info=v)
                continue
            action_items_for_note = [get_lite_task_response(task) for task in note.task_set.all()]
            action_items.extend(action_items_for_note)

        try:
            basic_info = user.crm_handler.get_client_basic_info(client, user) or {}
        except Exception as e:
            logger.error("Error retrieving basic info for client %s: %s", client_id, e)
            basic_info = {}

        return ClientResponse(
            name=client.name,
            recap_status=(
                ClientRecapStatus(latest_recap.status)
                if latest_recap and latest_recap.status
                else ClientRecapStatus.UNKNOWN
            ),
            recap=ClientRecap.from_db_client_recap(latest_processed_recap) if latest_processed_recap else None,
            notes=notes_list,
            basic_info=basic_info,
            action_items=action_items,
        )

    except Exception as e:
        logging.error("Error getting client: %s", client_id, exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving client details",
        )


# A router that defines the callback URL for the client recap generation process.
#
# This is not used or served in the Zeplyn API, but helps document the structure of the callback.
callback_router = APIRouter(
    tags=["client_callback"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}"
)


# The contents of a callback made when client recap generation is complete.
class ClientRecapDetails(BaseModel):
    status: ClientRecapStatus = Field(..., description="The status of the client recap generation process.")
    recap: ClientRecap | None = Field(..., description="The client recap details, if available.")


class AdditionalRecapData(BaseModel):
    source: str = Field(..., description="The source of the additional recap data, e.g., CRM system name.")
    content: str = Field(..., description="The actual content.")
    creation_date: datetime.datetime | None = Field(None, description="The creation date of this information.")
    link: HttpUrl | None = Field(
        None,
        description=(
            "A link to the source of this information. This is meant to allow a user to view the source of data "
            "in a recap, so (if present) it should be a link to the source material that the user can use to "
            "view the material directly."
        ),
    )


@callback_router.post("{$callback_url}")
def client_recap_callback(_: ClientRecapDetails = Body()) -> None:
    pass


@public_router.post(
    "/{client_id}/generate-recap",
    response_model=ClientRecapResponse,
    responses={
        200: {"description": "Client recap generation initiated successfully"},
        403: {"description": "Not authorized to generate a client recap for this client"},
        404: {"description": "Client not found"},
        500: {"description": "Internal server error"},
    },
    summary="Generate client recap",
    description=(
        "Initiate the generation of a client recap for a specific client. This method is "
        "stateful, and will store the generated recap in the Zeplyn database for future "
        "reference."
    ),
    callbacks=callback_router.routes,
)
@internal_router.post(
    "/{client_id}/generate-recap",
    response_model=ClientRecapResponse,
    responses={
        200: {"description": "Client recap generation initiated successfully"},
        403: {"description": "Not authorized to generate a client recap for this client"},
        404: {"description": "Client not found"},
        500: {"description": "Internal server error"},
    },
    summary="Generate a client recap",
    description="Initiate the generation of a client recap for a specific client",
    callbacks=callback_router.routes,
)
def generate_client_recap(
    client_id: UUID = Path(..., description="The Zeplyn UUID of the client"),
    callback_url: HttpUrl | None = Body(
        None,
        description=(
            "An (optional) callback URL to receive the client recap. When the recap is ready, the "
            "system will send a POST request to this URL with the recap."
        ),
    ),
    additional_data: list[AdditionalRecapData] | None = Body(
        None, description="Additional data to include in the recap."
    ),
    user: User = Depends(user_from_authorization_header),
) -> ClientRecapResponse:
    try:
        client = Client.objects.get(uuid=client_id)
    except Client.DoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Client not found")

    if not __is_authorized_to_view_client(user, client):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to generate a recap for another user's client",
        )

    try:
        recap = DBClientRecap.objects.create(user=user, client=client)
        additional_notes = [
            CRMNote(
                crm_id=str(uuid4()),
                crm_system=f"External ({a.source})",
                content=a.content,
                created_at=a.creation_date,
                type=None,
                web_link=a.link,
            )
            for a in (additional_data or [])
        ]

        process_client_recap.delay_on_commit(
            recap.uuid,
            user.uuid,
            [a.model_dump_json() for a in additional_notes],
            str(callback_url) if callback_url else None,
        )

        return ClientRecapResponse(status=ClientRecapStatus.PROCESSING, uuid=recap.uuid)
    except Exception as e:
        logger.error(traceback.format_exception(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while generating the client recap",
        )


@public_router.get(
    "/client_for_crm_id/{crm_id}",
    responses={
        200: {"description": "Successfully retrieved client UUID from CRM ID"},
        404: {"description": "Client not found"},
        500: {"description": "Internal server error"},
    },
    summary="Get Zeplyn client UUID for CRM ID",
    description="Gets a UUID for the Zeplyn database representation of a client from the client's CRM ID. This method is stateless.",
)
def get_client_for_crm_id(
    crm_id: str = Path(..., description="The CRM ID of the client. This method stores no additional state."),
    user: User = Depends(user_from_authorization_header),
) -> UUID:
    clients = Client.objects.filter(crm_id=crm_id)
    if not clients.exists():
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Client not found")

    # Return the first client which this user is authorized to view. CRM ID is not a unique field in
    # our client model, so it's possible that there are multiple clients with the same CRM ID.
    for client in clients:
        if __is_authorized_to_view_client(user, client):
            return client.uuid

    raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)


@public_router.get(
    "/recap/{uuid}",
    responses={
        200: {},
        403: {"description": "Not authorized to client recap for this client"},
        404: {"description": "Client recap not found"},
        500: {"description": "Internal server error"},
    },
    summary="Get client recap",
    description=(
        "Get the details of a specific client recap. This method references existing "
        "state in the Zeplyn database, but does not create any new state."
    ),
)
def get_recap(
    uuid: str = Path(..., description="The UUID of the client recap to fetch"),
    user: User = Depends(user_from_authorization_header),
) -> ClientRecap:
    try:
        recap = DBClientRecap.objects.get(uuid=uuid)
    except DBClientRecap.DoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)

    if not __is_authorized_to_view_client(user, recap.client):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN)

    return ClientRecap.from_db_client_recap(recap) or ClientRecap(
        recap=[],
        references=[],
        created_at=recap.created,
    )
