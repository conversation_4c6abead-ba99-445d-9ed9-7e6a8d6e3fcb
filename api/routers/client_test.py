import uuid
from typing import Any, Generator
from unittest.mock import ANY, MagicMock, patch

import pytest
from django.utils import timezone
from fastapi import status
from fastapi.testclient import TestClient

from api.dependencies import user_from_authorization_header
from api.internal_api import internal_api as app
from api.public_api import public_api as public_app
from api.routers.client import ClientRecap
from api.routers.note_models import NoteAudioSource
from deepinsights.core.integrations.crm.crm_models import CRMNote
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.client_recap import ClientRecap as DBClientRecap
from deepinsights.meetingsapp.models.client_recap import ClientRecapStatus
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User

client = TestClient(app)
public_client = TestClient(public_app)

pytestmark = [pytest.mark.django_db(transaction=True)]

_prep_contents = {
    "recap": [
        {
            "topic": "Personal Information",
            "bullets": [
                {
                    "text": "<PERSON> the <PERSON> is married to <PERSON> and they have two adult children who are not financially dependent.",
                    "references": [
                        {
                            "source": "salesforce",
                            "summary": "Household Information",
                            "meeting_uuid": "123",
                            "reference_link": "https://example.com/123",
                        }
                    ],
                },
                {
                    "text": "Bob is 50 years old and owns a construction business with fluctuating income.",
                    "references": [
                        {
                            "source": "salesforce",
                            "summary": "Client History",
                            "meeting_uuid": "123",
                            "reference_link": "https://example.com/123",
                        }
                    ],
                },
            ],
            "scratchpad": "The structured data is missing, so I will rely on meeting summaries to gather personal information about the client and their family.",
        },
        {
            "topic": "Financial Goals and Objectives",
            "bullets": [
                {
                    "text": "Bob aims to retire comfortably by age 65, requiring an annual income of $80,000 in retirement.",
                    "references": [
                        {
                            "source": "salesforce",
                            "summary": "Client History",
                            "meeting_uuid": "123",
                            "reference_link": "https://example.com/123",
                        }
                    ],
                },
                {
                    "text": "Bob's investment portfolio is targeting a 7% annual return with a current value of $280,000.",
                    "references": [
                        {
                            "source": "salesforce",
                            "summary": "Investment Portfolio Review",
                            "meeting_uuid": "123",
                            "reference_link": "https://example.com/123",
                        }
                    ],
                },
                {
                    "text": "Bob is considering setting up a charitable giving account.",
                    "references": [
                        {
                            "source": "salesforce",
                            "summary": "Charitable Giving Discussion",
                            "meeting_uuid": "123",
                            "reference_link": "https://example.com/123",
                        }
                    ],
                },
                {
                    "text": "Bob plans to increase 529 plan contributions from $1,000 to $1,500 monthly starting January.",
                    "references": [
                        {
                            "source": "salesforce",
                            "summary": "College Savings Strategy",
                            "meeting_uuid": "456",
                            "reference_link": "https://example.com/456",
                        }
                    ],
                },
                {
                    "text": "Bob is leaning towards a combination of cash and financing for purchasing equipment, totaling $450,000.",
                    "references": [
                        {
                            "source": "salesforce",
                            "summary": "Business Equipment Acquisition",
                            "meeting_uuid": "456",
                            "reference_link": "https://example.com/456",
                        }
                    ],
                },
            ],
            "scratchpad": "I will consolidate financial goals from meeting summaries, focusing on specific amounts and deadlines.",
        },
        {
            "topic": "Conversation Starter",
            "bullets": [
                {
                    "text": "Discuss Bob's recent interest in setting up a charitable giving account.",
                    "references": [
                        {
                            "source": "salesforce",
                            "summary": "Charitable Giving Discussion",
                            "meeting_uuid": "456",
                            "reference_link": "https://example.com/456",
                        }
                    ],
                }
            ],
            "scratchpad": "I will use personal updates and milestones from meeting summaries to create conversation starters.",
        },
        {
            "topic": "Ongoing Conversations",
            "bullets": [
                {
                    "text": "Bob's inherited IRA account has been a recurring topic, with a balance of $250,000.",
                    "references": [
                        {
                            "source": "salesforce",
                            "summary": "Inherited IRA",
                            "meeting_uuid": "789",
                            "reference_link": "https://example.com/456",
                        },
                        {
                            "source": "Zeplyn",
                            "summary": "Brief Summary",
                            "meeting_uuid": "5ebcad6c-c7f6-42cb-87ca-464ab9962311",
                            "reference_link": "",
                        },
                    ],
                }
            ],
            "scratchpad": "I will highlight ongoing topics from multiple meetings, focusing on updates and evolving goals.",
        },
        {
            "topic": "Action Items and Next Steps",
            "bullets": [
                {
                    "text": "Request formal equipment financing proposals from three banks and schedule a meeting with a tax advisor regarding the equipment purchase.",
                    "references": [
                        {
                            "source": "salesforce",
                            "summary": "Action Items",
                            "meeting_uuid": "789",
                            "reference_link": "https://example.com/456",
                        }
                    ],
                },
                {
                    "text": "Increase 529 plan contributions starting January and review insurance coverage for new business ventures.",
                    "references": [
                        {
                            "source": "salesforce",
                            "summary": "Action Items",
                            "meeting_uuid": "789",
                            "reference_link": "https://example.com/456",
                        }
                    ],
                },
            ],
            "scratchpad": "I will list action items from meeting summaries, including deadlines and responsible parties.",
        },
    ]
}


@pytest.fixture(autouse=True)
def setUpTearDown() -> Generator[None, Any, None]:
    app.dependency_overrides = {}
    public_app.dependency_overrides = {}
    yield
    app.dependency_overrides = {}
    public_app.dependency_overrides = {}


@pytest.fixture
def test_user(django_user_model: User) -> User:
    test_user = django_user_model.objects.create(
        username="<EMAIL>",
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
    )
    app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.get(username="<EMAIL>")
    public_app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.get(
        username="<EMAIL>"
    )
    return test_user


def test_unauthenticated() -> None:
    client_id = str(uuid.uuid4())

    response_get = client.get(f"/api/v2/client/{client_id}")
    response_post = client.post(f"/api/v2/client/{client_id}/generate-recap")

    assert response_get.status_code == status.HTTP_403_FORBIDDEN
    assert response_post.status_code == status.HTTP_403_FORBIDDEN


def test_get_client_not_found(test_user: User) -> None:
    non_existent_uuid = uuid.uuid4()
    response = client.get(f"/api/v2/client/{non_existent_uuid}")

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Client not found"}


def test_get_client_unauthorized(test_user: User, django_user_model: User) -> None:
    org = Organization.objects.create(name="Other Org")
    other_user = django_user_model.objects.create(username="<EMAIL>", organization=org)
    test_client = Client.objects.create(name="Test Client", organization=org)

    response = client.get(f"/api/v2/client/{test_client.uuid}")

    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {"detail": "Not authorized to view another user's client"}


@patch("deepinsights.users.models.user.User.crm_handler")
def test_get_client_success(mock_crm_handler: MagicMock, test_user: User) -> None:
    org = Organization.objects.create(name="Test Org")
    test_user.organization = org
    test_user.save()

    test_client = Client.objects.create(name="Test Client", organization=org)
    test_client.authorized_users.add(test_user)

    recap = DBClientRecap.objects.create(
        client=test_client, user=test_user, status=ClientRecapStatus.PROCESSED, summary=_prep_contents
    )

    note = Note.objects.create(
        note_owner=test_user,
        note_type="meeting_recording",
        status=Note.PROCESSING_STATUS.processed,
        metadata={"meeting_name": "Test Meeting"},
        client={"uuid": str(test_client.uuid), "name": test_client.name, "email": ""},
    )
    note.authorized_users.add(test_user)

    task = Task.objects.create(
        task_owner=test_user,
        note=note,
        completed=False,
        task_title="Test Task",
        task_desc="Description",
        due_date=timezone.now(),
    )

    mock_crm_handler.get_client_basic_info.return_value = {
        "gross_annual_income": 1000,
        "assets": 2000,
        "liabilities": 500,
        "non_liquid_assets": 1000,
    }

    response = client.get(f"/api/v2/client/{test_client.uuid}")

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    client_recap_summary = recap.get_client_recap_summary()
    assert response_data == {
        "name": test_client.name,
        "recap_status": ClientRecapStatus.PROCESSED,
        "recap": ClientRecap.model_validate(
            {
                "recap": client_recap_summary.get("client_recap_summary", []),
                "references": [{**r, "id": r.get("meeting_uuid")} for r in client_recap_summary.get("references", [])],
                "created_at": recap.created,
            }
        ).model_dump(mode="json"),
        "notes": [
            {
                "uuid": str(note.uuid),
                "created": note.created.isoformat(),
                "modified": note.modified.isoformat(),
                "note_type": note.note_type,
                "status": Note.PROCESSING_STATUS.processed,
                "meeting_name": "Test Meeting",
                "client": {"uuid": str(test_client.uuid), "name": test_client.name, "email": ""},
                "scheduled_start_time": None,
                "scheduled_end_time": None,
                "tags": [],
                "meeting_source_id": None,
                "scheduled_event_uuid": None,
                "attendees": [],
                "meeting_type": None,
                "meeting_category": None,
                "owner": {"uuid": str(test_user.uuid), "name": "Test User"},
                "audio_source": NoteAudioSource.MIC,
                "meeting_duration_seconds": 0.0,
                "autojoin_available": False,
                "autojoin_enabled": False,
                "autojoin_editable": False,
                "meeting_link": None,
            }
        ],
        "basic_info": {
            "gross_annual_income": 1000,
            "assets": 2000,
            "liabilities": 500,
            "non_liquid_assets": 1000,
        },
        "action_items": [
            {
                "uuid": str(task.uuid),
                "created": task.created.isoformat().replace("+00:00", "Z"),
                "modified": task.modified.isoformat().replace("+00:00", "Z"),
                "title": task.task_title,
                "description": task.task_desc,
                "due_date": task.due_date.isoformat().replace("+00:00", "Z") if task.due_date else None,
                "completed": task.completed,
                "owner": {"uuid": str(test_user.uuid), "name": "Test User"},
                "assignee": {"uuid": str(test_user.uuid), "name": "Test User"},
                "parent_note_uuid": None,
                "assignees": [{"uuid": str(test_user.uuid), "name": "Test User"}],
                "workflow_type_name": None,
                "crm_assignee": task.crm_assignee if task.crm_assignee else None,
            }
        ],
    }


def test_get_client_success_no_related_models(test_user: User) -> None:
    org = Organization.objects.create(name="Test Org")
    test_user.organization = org
    test_user.save()

    test_client = Client.objects.create(name="Test Client", organization=org)
    test_client.authorized_users.add(test_user)

    response = client.get(f"/api/v2/client/{test_client.uuid}")

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    assert response_data == {
        "name": test_client.name,
        "recap_status": ClientRecapStatus.UNKNOWN,
        "recap": None,
        "notes": [],
        "basic_info": {},
        "action_items": [],
    }


def test_get_client_with_different_license_types(test_user: User, django_user_model: User) -> None:
    org = Organization.objects.create(name="Test Org")
    test_client = Client.objects.create(name="Test Client", organization=org)

    test_user.organization = org
    test_user.license_type = User.LicenseType.csa
    test_user.save()

    DBClientRecap.objects.create(client=test_client, user=test_user, status=ClientRecapStatus.PROCESSED)

    response = client.get(f"/api/v2/client/{test_client.uuid}")
    assert response.status_code == status.HTTP_200_OK
    assert response.json()["name"] == test_client.name

    test_user.license_type = User.LicenseType.staff
    test_user.save()

    response = client.get(f"/api/v2/client/{test_client.uuid}")
    assert response.status_code == status.HTTP_200_OK
    assert response.json()["name"] == test_client.name

    test_user.is_superuser = True
    test_user.save()

    response = client.get(f"/api/v2/client/{test_client.uuid}")
    assert response.status_code == status.HTTP_200_OK
    assert response.json()["name"] == test_client.name


@patch("api.routers.client.process_client_recap")
def test_generate_recap_success(mock_process_client_recap: MagicMock, test_user: User) -> None:
    org = Organization.objects.create(name="Test Org")
    test_user.organization = org
    test_user.save()

    test_client = Client.objects.create(name="Test Client", organization=org)
    test_client.authorized_users.add(test_user)

    response = client.post(f"/api/v2/client/{test_client.uuid}/generate-recap")

    assert response.status_code == status.HTTP_200_OK
    recap = DBClientRecap.objects.get(client=test_client)
    assert recap.user == test_user
    assert response.json() == {"status": ClientRecapStatus.PROCESSING, "uuid": str(recap.uuid)}

    mock_process_client_recap.delay_on_commit.assert_called_once_with(recap.uuid, test_user.uuid, [], None)


@patch("api.routers.client.process_client_recap")
def test_generate_recap_success_with_additional_data(mock_process_client_recap: MagicMock, test_user: User) -> None:
    org = Organization.objects.create(name="Test Org")
    test_user.organization = org
    test_user.save()

    test_client = Client.objects.create(name="Test Client", organization=org)
    test_client.authorized_users.add(test_user)

    creation_date = timezone.now()

    response = client.post(
        f"/api/v2/client/{test_client.uuid}/generate-recap",
        json={
            "additional_data": [
                {
                    "source": "external",
                    "content": "This is some additional data for the recap.",
                    "creation_date": creation_date.isoformat(),
                    "link": "http://example.com/additional_data",
                },
            ],
        },
    )
    assert response.status_code == status.HTTP_200_OK

    recap = DBClientRecap.objects.get(client=test_client)
    assert recap.user == test_user
    assert response.json() == {"status": ClientRecapStatus.PROCESSING, "uuid": str(recap.uuid)}

    mock_process_client_recap.delay_on_commit.assert_called_once_with(
        recap.uuid,
        test_user.uuid,
        ANY,
        None,
    )
    actual_additional_data = mock_process_client_recap.delay_on_commit.call_args[0][2]
    assert len(actual_additional_data) == 1
    crm_note = CRMNote.model_validate_json(actual_additional_data[0])
    assert crm_note == CRMNote(
        crm_id=crm_note.crm_id,
        crm_system="External (external)",
        content="This is some additional data for the recap.",
        created_at=creation_date,
        web_link="http://example.com/additional_data",
    )


@patch("deepinsights.users.models.user.User.crm_handler")
def test_get_client_crm_exception_handling(mock_crm_handler: MagicMock, test_user: User) -> None:
    org = Organization.objects.create(name="Test Org")
    test_user.organization = org
    test_user.save()

    test_client = Client.objects.create(name="Test Client", organization=org)
    test_client.authorized_users.add(test_user)

    recap = DBClientRecap.objects.create(
        client=test_client, user=test_user, status=ClientRecapStatus.PROCESSED, summary=_prep_contents
    )

    note = Note.objects.create(
        note_owner=test_user,
        note_type="meeting_recording",
        status=Note.PROCESSING_STATUS.processed,
        metadata={"meeting_name": "Test Meeting"},
        client={"uuid": str(test_client.uuid), "name": test_client.name, "email": ""},
    )
    note.authorized_users.add(test_user)

    mock_crm_handler.get_client_basic_info.side_effect = Exception("CRM authentication failed")

    response = client.get(f"/api/v2/client/{test_client.uuid}")

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()

    assert response_data["name"] == test_client.name
    assert response_data["recap_status"] == ClientRecapStatus.PROCESSED
    assert response_data["recap"] is not None
    assert len(response_data["notes"]) == 1
    assert response_data["notes"][0]["meeting_name"] == "Test Meeting"

    assert response_data["basic_info"] == {}

    mock_crm_handler.get_client_basic_info.assert_called_once_with(test_client, test_user)


@patch("api.routers.client.process_client_recap")
def test_generate_recap_success_with_callback_url(mock_process_client_recap: MagicMock, test_user: User) -> None:
    org = Organization.objects.create(name="Test Org")
    test_user.organization = org
    test_user.save()

    test_client = Client.objects.create(name="Test Client", organization=org)
    test_client.authorized_users.add(test_user)

    response = client.post(
        f"/api/v2/client/{test_client.uuid}/generate-recap",
        json={"callback_url": "http://example.com/callback"},
    )
    assert response.status_code == status.HTTP_200_OK

    recap = DBClientRecap.objects.get(client=test_client)
    assert recap.user == test_user
    assert response.json() == {"status": ClientRecapStatus.PROCESSING, "uuid": str(recap.uuid)}

    mock_process_client_recap.delay_on_commit.assert_called_once_with(
        recap.uuid, test_user.uuid, [], "http://example.com/callback"
    )


def test_generate_recap_client_not_found(test_user: User) -> None:
    non_existent_uuid = uuid.uuid4()
    response = client.post(f"/api/v2/client/{non_existent_uuid}/generate-recap")

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Client not found"}


def test_generate_recap_unauthorized(test_user: User) -> None:
    org = Organization.objects.create(name="Other Org")
    test_client = Client.objects.create(name="Test Client", organization=org)

    response = client.post(f"/api/v2/client/{test_client.uuid}/generate-recap")

    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {"detail": "Not authorized to generate a recap for another user's client"}


@patch("api.routers.client.DBClientRecap.objects.create")
def test_generate_recap_error(mock_create_client_recap: MagicMock, test_user: User) -> None:
    org = Organization.objects.create(name="Test Org")
    test_user.organization = org
    test_user.save()

    test_client = Client.objects.create(name="Test Client", organization=org)
    test_client.authorized_users.add(test_user)

    mock_create_client_recap.side_effect = Exception("Database error")

    response = client.post(f"/api/v2/client/{test_client.uuid}/generate-recap")

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {"detail": "An error occurred while generating the client recap"}


def test_get_client_with_invalid_notes(test_user: User) -> None:
    org = Organization.objects.create(name="Test Org")
    test_user.organization = org
    test_user.save()

    test_client = Client.objects.create(name="Test Client", organization=org)
    test_client.authorized_users.add(test_user)

    # Create client recap
    DBClientRecap.objects.create(client=test_client, user=test_user, status=ClientRecapStatus.PROCESSED)

    # valid note
    valid_note = Note.objects.create(
        note_owner=test_user,
        note_type="meeting_recording",
        status=Note.PROCESSING_STATUS.processed,
        metadata={"meeting_name": "Valid Meeting"},
        client={"uuid": str(test_client.uuid), "name": test_client.name, "email": ""},
    )
    valid_note.authorized_users.add(test_user)

    # invalid note (missing required fields)
    invalid_note = Note.objects.create(
        note_owner=test_user, client={"uuid": str(test_client.uuid), "name": test_client.name, "email": ""}
    )
    invalid_note.authorized_users.add(test_user)

    response = client.get(f"/api/v2/client/{test_client.uuid}")

    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()

    # Verify only valid note is included
    assert len(response_data["notes"]) == 1
    assert response_data["notes"][0]["meeting_name"] == "Valid Meeting"


def test_get_recap_not_found(test_user: User) -> None:
    response = public_client.get(f"/api/public/v1/clients/recap/{uuid.uuid4()}")

    assert response.status_code == status.HTTP_404_NOT_FOUND


def test_get_recap_unauthorized(test_user: User, django_user_model: User) -> None:
    org = Organization.objects.create(name="Other Org")
    other_user = django_user_model.objects.create(username="<EMAIL>", organization=org)
    test_client = Client.objects.create(name="Test Client", organization=org)

    recap = DBClientRecap.objects.create(client=test_client, user=other_user, status=ClientRecapStatus.PROCESSED)

    response = public_client.get(f"/api/public/v1/clients/recap/{recap.uuid}")

    assert response.status_code == status.HTTP_403_FORBIDDEN


def test_get_recap_success(test_user: User) -> None:
    org = Organization.objects.create(name="Test Org")
    test_user.organization = org
    test_user.save()

    test_client = Client.objects.create(name="Test Client", organization=org)
    test_client.authorized_users.add(test_user)

    recap = DBClientRecap.objects.create(
        client=test_client, user=test_user, status=ClientRecapStatus.PROCESSED, summary=_prep_contents
    )

    response = public_client.get(f"/api/public/v1/clients/recap/{recap.uuid}")

    recap_summary = recap.get_client_recap_summary()
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == ClientRecap.model_validate(
        {
            "recap": recap_summary.get("client_recap_summary", []),
            "references": [{**r, "id": r.get("meeting_uuid")} for r in recap_summary.get("references", [])],
            "created_at": recap.created,
        }
    ).model_dump(mode="json")


def test_get_recap_with_superuser(test_user: User, django_user_model: User) -> None:
    org = Organization.objects.create(name="Other Org")
    other_user = django_user_model.objects.create(username="<EMAIL>", organization=org)
    test_client = Client.objects.create(name="Test Client", organization=org)

    test_user.is_superuser = True
    test_user.save()

    recap = DBClientRecap.objects.create(client=test_client, user=other_user, status=ClientRecapStatus.PROCESSED)

    response = public_client.get(f"/api/public/v1/clients/recap/{recap.uuid}")

    assert response.status_code == status.HTTP_200_OK


def test_get_recap_with_csa_license(test_user: User, django_user_model: User) -> None:
    org = Organization.objects.create(name="Test Org")
    other_user = django_user_model.objects.create(username="<EMAIL>", organization=org)
    test_client = Client.objects.create(name="Test Client", organization=org)

    test_user.organization = org
    test_user.license_type = User.LicenseType.csa
    test_user.save()

    recap = DBClientRecap.objects.create(client=test_client, user=other_user, status=ClientRecapStatus.PROCESSED)

    response = public_client.get(f"/api/public/v1/clients/recap/{recap.uuid}")

    assert response.status_code == status.HTTP_200_OK


def test_get_client_for_crm_id_not_found(test_user: User) -> None:
    response = public_client.get("/api/public/v1/clients/client_for_crm_id/nonexistent")

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Client not found"}


def test_get_client_for_crm_id_unauthorized(test_user: User, django_user_model: User) -> None:
    org = Organization.objects.create(name="Other Org")
    other_user = django_user_model.objects.create(username="<EMAIL>", organization=org)
    test_client = Client.objects.create(name="Test Client", organization=org, crm_id="test_crm_id")

    response = public_client.get("/api/public/v1/clients/client_for_crm_id/test_crm_id")

    assert response.status_code == status.HTTP_404_NOT_FOUND


def test_get_client_for_crm_id_success(test_user: User) -> None:
    org = Organization.objects.create(name="Test Org")
    test_user.organization = org
    test_user.save()

    test_client = Client.objects.create(name="Test Client", organization=org, crm_id="test_crm_id")
    test_client.authorized_users.add(test_user)

    response = public_client.get("/api/public/v1/clients/client_for_crm_id/test_crm_id")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == str(test_client.uuid)


def test_get_client_for_crm_id_multiple_clients_authorized(test_user: User) -> None:
    org = Organization.objects.create(name="Test Org")
    test_user.organization = org
    test_user.save()

    # Create multiple clients with same CRM ID
    client1 = Client.objects.create(name="Test Client 1", organization=org, crm_id="shared_crm_id")
    client2 = Client.objects.create(name="Test Client 2", organization=org, crm_id="shared_crm_id")

    client1.authorized_users.add(test_user)
    client2.authorized_users.add(test_user)

    response = public_client.get("/api/public/v1/clients/client_for_crm_id/shared_crm_id")

    assert response.status_code == status.HTTP_200_OK
    # Should return the first authorized client's UUID
    returned_uuid = response.json()
    assert returned_uuid in [str(client1.uuid), str(client2.uuid)]


def test_get_client_for_crm_id_multiple_clients_partial_auth(test_user: User, django_user_model: User) -> None:
    org = Organization.objects.create(name="Test Org")
    other_org = Organization.objects.create(name="Other Org")
    test_user.organization = org
    test_user.save()

    other_user = django_user_model.objects.create(username="<EMAIL>", organization=other_org)

    # Create multiple clients with same CRM ID, but user only authorized for one
    unauthorized_client = Client.objects.create(
        name="Unauthorized Client", organization=other_org, crm_id="shared_crm_id"
    )
    authorized_client = Client.objects.create(name="Authorized Client", organization=org, crm_id="shared_crm_id")

    authorized_client.authorized_users.add(test_user)

    response = public_client.get("/api/public/v1/clients/client_for_crm_id/shared_crm_id")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == str(authorized_client.uuid)


def test_get_client_for_crm_id_with_superuser(test_user: User, django_user_model: User) -> None:
    org = Organization.objects.create(name="Other Org")
    other_user = django_user_model.objects.create(username="<EMAIL>", organization=org)
    test_client = Client.objects.create(name="Test Client", organization=org, crm_id="test_crm_id")

    test_user.is_superuser = True
    test_user.save()

    response = public_client.get("/api/public/v1/clients/client_for_crm_id/test_crm_id")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == str(test_client.uuid)


def test_get_client_for_crm_id_with_csa_license(test_user: User, django_user_model: User) -> None:
    org = Organization.objects.create(name="Test Org")
    other_user = django_user_model.objects.create(username="<EMAIL>", organization=org)
    test_client = Client.objects.create(name="Test Client", organization=org, crm_id="test_crm_id")

    test_user.organization = org
    test_user.license_type = User.LicenseType.csa
    test_user.save()

    response = public_client.get("/api/public/v1/clients/client_for_crm_id/test_crm_id")
