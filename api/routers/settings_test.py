import datetime
from typing import Any, Generator
from unittest import mock
from unittest.mock import <PERSON><PERSON><PERSON>

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from api.dependencies import user_from_authorization_header
from api.internal_api import internal_api as app
from api.routers.calendar_models import CalendarLookahead
from api.routers.settings_models import SectionDetails
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.flags import Flag
from deepinsights.users.models.subscription_plan import (
    Entitlement,
    EntitlementType,
    MeetingAssistantEntitlementDetails,
    SubscriptionPlan,
    SubscriptionTerm,
)
from deepinsights.users.models.user import User

client = TestClient(app)

pytestmark = [pytest.mark.django_db(transaction=True)]


@pytest.fixture(autouse=True)
def set_up_tear_down() -> Generator[None, None, None]:
    yield
    app.dependency_overrides = {}


@pytest.fixture
def test_user(django_user_model: User) -> User:
    test_user = django_user_model.objects.create(username="<EMAIL>", password="123")
    app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.select_related("organization").get(
        username="<EMAIL>"
    )
    return test_user


def _set_flag_active(flag: Flags, active: bool) -> None:
    f = Flag.objects.get(name=flag.name)
    f.everyone = active
    f.override_enabled_by_environment = None  # To ensure no environment override
    f.save()


def test_settings_menu_without_plan_info(test_user: User) -> None:
    response = client.get("/api/v2/settings/menu")
    assert response.status_code == status.HTTP_200_OK, "Expected 200 OK for regular user"
    assert response.json() == [
        {
            "id": "my-account",
            "label": "My Account",
            "items": [
                {"id": "integrations", "label": "Integrations", "items": None},
                {"id": "settings", "label": "Settings", "items": None},
                {"id": "profile-details", "label": "Profile Details", "items": None},
            ],
        }
    ]


def test_settings_menu_with_plan_info(test_user: User) -> None:
    _set_flag_active(Flags.EnablePlanInfoInSettings, True)

    response = client.get("/api/v2/settings/menu")
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == [
        {
            "id": "my-account",
            "label": "My Account",
            "items": [
                {"id": "integrations", "label": "Integrations", "items": None},
                {"id": "settings", "label": "Settings", "items": None},
                {"id": "profile-details", "label": "Profile Details", "items": None},
                {"id": "plan-details", "label": "Plan and Features", "items": None},
            ],
        }
    ]


def test_settings_menu_with_ui(test_user: User) -> None:
    _set_flag_active(Flags.EnableUISettings, True)

    response = client.get("/api/v2/settings/menu")
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == [
        {
            "id": "my-account",
            "label": "My Account",
            "items": [
                {"id": "integrations", "label": "Integrations", "items": None},
                {"id": "settings", "label": "Settings", "items": None},
                {"id": "profile-details", "label": "Profile Details", "items": None},
                {"id": "user-interface", "label": "User Interface", "items": None},
            ],
        }
    ]


def test_settings_menu_with_preferences(test_user: User) -> None:
    _set_flag_active(Flags.EnablePreferencesInSettings, True)

    response = client.get("/api/v2/settings/menu")
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == [
        {
            "id": "my-account",
            "label": "My Account",
            "items": [
                {"id": "integrations", "label": "Integrations", "items": None},
                {"id": "settings", "label": "Settings", "items": None},
                {"id": "profile-details", "label": "Profile Details", "items": None},
            ],
        },
        {
            "id": "preferences",
            "label": "Preferences",
            "items": None,
        },
    ]


def test_settings_menu_with_admin_preferences(test_user: User) -> None:
    test_user.is_superuser = True
    test_user.save()
    _set_flag_active(Flags.EnablePreferencesInSettings, True)

    response = client.get("/api/v2/settings/menu")
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == [
        {
            "id": "my-account",
            "label": "My Account",
            "items": [
                {"id": "integrations", "label": "Integrations", "items": None},
                {"id": "settings", "label": "Settings", "items": None},
                {"id": "profile-details", "label": "Profile Details", "items": None},
            ],
        },
        {
            "id": "preferences",
            "label": "Preferences",
            "items": None,
        },
    ]


def test_settings_menu_for_admin(test_user: User) -> None:
    _set_flag_active(Flags.EnableUserImpersonation, True)
    test_user.is_superuser = True
    test_user.save()

    response = client.get("/api/v2/settings/menu")
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == [
        {
            "id": "my-account",
            "label": "My Account",
            "items": [
                {"id": "integrations", "label": "Integrations", "items": None},
                {"id": "settings", "label": "Settings", "items": None},
                {"id": "profile-details", "label": "Profile Details", "items": None},
            ],
        },
        {
            "id": "admin",
            "label": "Admin",
            "items": [
                {"id": "user-impersonation", "label": "User Impersonation", "items": None},
            ],
        },
    ]


@pytest.mark.parametrize("value", ["integrations", "settings", "profile-details", "plan-details"])
def test_get_settings_details_route(value: str, test_user: User) -> None:
    response = client.get("/api/v2/settings/details", params={"identifier": value})
    assert response.status_code == status.HTTP_200_OK

    response_data = response.json()
    section_details = SectionDetails.model_validate(response_data)

    # Verify that it's a valid SectionDetails object with proper types
    assert isinstance(section_details.label, str)
    assert isinstance(section_details.data, list)
    assert isinstance(section_details.showSaveButton, bool)

    # Additional validations based on the identifier
    if value == "integrations":
        assert section_details.label == "Integrations"
        assert not section_details.showSaveButton
        assert len(section_details.data) >= 2  # At least "My Integrations" and "Available Integrations"

        # Validate that integration cards have the correct structure
        for item in section_details.data:
            assert hasattr(item, "kind")
            assert hasattr(item, "id")
            assert hasattr(item, "label")
            if hasattr(item, "cards"):
                assert isinstance(item.cards, list)
            if hasattr(item, "filters"):
                assert isinstance(item.filters, list)

    elif value == "settings":
        assert section_details.label == "Settings"
        assert section_details.showSaveButton
        # Settings data can be empty if no feature flags are enabled, which is valid behavior
        assert isinstance(section_details.data, list)

        # If settings exist, validate their structure
        for item in section_details.data:
            assert hasattr(item, "kind")
            assert hasattr(item, "id")
            assert hasattr(item, "label")
            assert hasattr(item, "value")

    elif value == "profile-details":
        assert section_details.label == "Profile Details"
        assert not section_details.showSaveButton
        # Should have profile-related fields
        assert len(section_details.data) >= 3  # At least name, email, and contact support

        # Validate profile fields structure
        for item in section_details.data:
            assert hasattr(item, "kind")
            assert hasattr(item, "id")
            assert hasattr(item, "label")
            if hasattr(item, "value"):
                assert isinstance(item.value, (str, type(None)))
            if hasattr(item, "disabled"):
                assert isinstance(item.disabled, bool)


def test_get_settings_details_route_invalid_identifier(test_user: User) -> None:
    response = client.get("/api/v2/settings/details", params={"identifier": "invalid-path"})
    assert response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.parametrize(
    "route, method",
    [
        ("/api/v2/settings/menu", "get"),
        ("/api/v2/settings/details", "get"),
        ("/api/v2/settings/save", "post"),
    ],
)
def test_unauthenticated_routes(route: str, method: str) -> None:
    match method:
        case "get":
            response = client.get(route)
        case "post":
            response = client.post(route)
    assert response.status_code == status.HTTP_403_FORBIDDEN


@pytest.mark.parametrize(
    "value",
    [
        CalendarLookahead.ONE_HOUR,
        CalendarLookahead.END_OF_DAY,
        CalendarLookahead.ONE_DAY,
        CalendarLookahead.TWO_DAYS,
        CalendarLookahead.END_OF_WEEK,
        CalendarLookahead.END_OF_NEXT_WEEK,
        CalendarLookahead.THIRTY_DAYS,
    ],
)
def test_save_calendar_lookahead(value: str, test_user: User) -> None:
    payload = {"calendarLookahead": value}
    response = client.post("/api/v2/settings/save", json=payload)
    assert response.status_code == status.HTTP_200_OK
    test_user.refresh_from_db()
    assert test_user.calendar_lookahead == value


@pytest.mark.parametrize("value", [True, False])
def test_save_calendar_show_meetings_without_urls(value: bool, test_user: User) -> None:
    payload = {"calendarShowMeetingsWithoutUrls": value}
    response = client.post("/api/v2/settings/save", json=payload)
    assert response.status_code == status.HTTP_200_OK
    test_user.refresh_from_db()
    assert test_user.show_events_without_meeting_urls == value


# @pytest.mark.parametrize("value", [True, False])
# @patch("api.routers.settings.update_recall_auto_join_integration")
# def test_save_ms_calendar_autojoin(
#     mock_update_recall_auto_join_integration: MagicMock, value: bool, test_user: User
# ) -> None:
#     payload = {"msCalendarAutojoin": value}
#     response = client.post("/api/v2/settings/save", json=payload)
#     assert response.status_code == status.HTTP_200_OK
#     mock_update_recall_auto_join_integration.assert_called_once_with(
#         test_user,
#         value,
#         ANY,
#         RecallBotController.link_microsoft_calendar,
#     )
#     assert isinstance(mock_update_recall_auto_join_integration.call_args[0][2], MicrosoftOAuth)


# @pytest.mark.parametrize("value", [True, False])
# @patch("api.routers.settings.update_recall_auto_join_integration")
# def test_save_google_calendar_autojoin(
#     mock_update_recall_auto_join_integration: MagicMock, value: bool, test_user: User
# ) -> None:
#     payload = {"googleCalendarAutojoin": value}
#     response = client.post("/api/v2/settings/save", json=payload)
#     assert response.status_code == status.HTTP_200_OK
#     mock_update_recall_auto_join_integration.assert_called_once_with(
#         test_user, value, ANY, RecallBotController.link_google_calendar
#     )
#     assert isinstance(mock_update_recall_auto_join_integration.call_args[0][2], GoogleOAuth)


def test_get_settings_menu_route_with_preferences_non_staff_user(test_user: User) -> None:
    _set_flag_active(Flags.EnablePreferencesInSettings, True)

    response = client.get("/api/v2/settings/menu")
    assert response.status_code == status.HTTP_200_OK

    menu_data = response.json()
    assert {
        "id": "preferences",
        "label": "Preferences",
        "items": None,
    } in menu_data
    assert not any(item["id"] == "org-preferences" for item in menu_data)


def test_get_settings_menu_route_with_preferences_for_org_admin(test_user: User) -> None:
    _set_flag_active(Flags.EnablePreferencesInSettings, True)
    test_user.organization = Organization.objects.create(name="Test Organization")
    test_user.entitlements = [EntitlementType.organization_admin]
    test_user.save()

    response = client.get("/api/v2/settings/menu")
    assert response.status_code == status.HTTP_200_OK

    menu_data = response.json()
    assert {
        "id": "preferences",
        "label": "Preferences",
        "items": None,
    } in menu_data
    assert {
        "id": "org-preferences",
        "label": "Organization Preferences",
        "items": None,
    } in menu_data


def find_field_by_id(data: dict[str, Any], field_id: str) -> dict[str, Any] | None:
    return next((item for item in data["data"] if item["id"] == field_id), None)


def test_get_settings_details_profile_empty(test_user: User) -> None:
    test_user.name = ""
    test_user.email = ""
    test_user.role = None
    test_user.manager = None
    test_user.save()

    response = client.get("/api/v2/settings/details", params={"identifier": "profile-details"})
    assert response.status_code == status.HTTP_200_OK

    data = response.json()
    assert data == {
        "label": "Profile Details",
        "data": [
            {
                "id": "name",
                "label": "Full Name",
                "kind": "text-field",
                "placeholder": "John Doe",
                "value": "",
                "disabled": False,
                "readonly": True,
            },
            {
                "id": "email",
                "label": "Email ID",
                "kind": "text-field",
                "placeholder": "<EMAIL>",
                "value": "",
                "disabled": False,
                "readonly": True,
            },
            {
                "id": "role",
                "label": "Role",
                "kind": "text-field",
                "placeholder": "",
                "value": "Not set",
                "disabled": False,
                "readonly": True,
            },
            {
                "id": "manager",
                "label": "Manager",
                "kind": "text-field",
                "placeholder": "",
                "value": "Not set",
                "disabled": False,
                "readonly": True,
            },
            {
                "id": "contactSupport",
                "label": "Note",
                "kind": "link",
                "description": "Please connect with us if you want to edit your profile information",
                "text": "Contact Support",
                "appTag": "contact-support",
            },
        ],
        "showSaveButton": False,
    }


def test_get_settings_details_profile_populated(test_user: User, django_user_model: User) -> None:
    test_user.name = "Test User"
    test_user.email = "<EMAIL>"
    test_user.role = "Manager"
    test_user.manager = django_user_model.objects.create(username="manager", email="<EMAIL>")
    test_user.save()

    response = client.get("/api/v2/settings/details", params={"identifier": "profile-details"})
    assert response.status_code == status.HTTP_200_OK

    data = response.json()
    assert data == {
        "label": "Profile Details",
        "data": [
            {
                "id": "name",
                "label": "Full Name",
                "kind": "text-field",
                "placeholder": "John Doe",
                "value": "Test User",
                "disabled": False,
                "readonly": True,
            },
            {
                "id": "email",
                "label": "Email ID",
                "kind": "text-field",
                "placeholder": "<EMAIL>",
                "value": "<EMAIL>",
                "disabled": False,
                "readonly": True,
            },
            {
                "id": "role",
                "label": "Role",
                "kind": "text-field",
                "placeholder": "",
                "value": "Manager",
                "disabled": False,
                "readonly": True,
            },
            {
                "id": "manager",
                "label": "Manager",
                "kind": "text-field",
                "placeholder": "",
                "value": "<EMAIL>",
                "disabled": False,
                "readonly": True,
            },
            {
                "id": "contactSupport",
                "label": "Note",
                "kind": "link",
                "description": "Please connect with us if you want to edit your profile information",
                "text": "Contact Support",
                "appTag": "contact-support",
            },
        ],
        "showSaveButton": False,
    }


def test_get_settings_details_plan_info_no_organization(test_user: User) -> None:
    response = client.get("/api/v2/settings/details", params={"identifier": "plan-details"})
    assert response.status_code == status.HTTP_200_OK

    data = response.json()
    assert data == {
        "label": "Your Plan and Features",
        "data": [
            {
                "appTag": "contact-support",
                "description": "Please connect with us to view your plan and features",
                "id": "contactSupport",
                "kind": "link",
                "label": "",
                "text": "Contact Support",
            },
        ],
        "showSaveButton": False,
    }


def test_get_settings_details_plan_info_no_plan(test_user: User) -> None:
    test_org = Organization.objects.create(name="Test Organization")
    test_user.organization = test_org
    test_user.save()

    response = client.get("/api/v2/settings/details", params={"identifier": "plan-details"})

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data == {
        "label": "Your Plan and Features",
        "data": [
            {
                "appTag": "contact-support",
                "description": "Please connect with us to view your plan and features",
                "id": "contactSupport",
                "kind": "link",
                "label": "",
                "text": "Contact Support",
            },
        ],
        "showSaveButton": False,
    }


def test_get_settings_details_plan_info_with_plan(test_user: User) -> None:
    org = Organization.objects.create(name="Test Organization")
    test_user.organization = org
    test_user.save()
    SubscriptionPlan.objects.create(
        organization=org,
        title="Premium Plan",
        start_date=datetime.datetime(2023, 1, 1, tzinfo=datetime.timezone.utc),
        _entitlements=[
            Entitlement(
                type="meeting_assistant", license_count=5, details=MeetingAssistantEntitlementDetails(meetings_count=10)
            ).model_dump(mode="json"),
            Entitlement(type="client_intelligence", license_count=3).model_dump(mode="json"),
        ],
    )

    response = client.get("/api/v2/settings/details", params={"identifier": "plan-details"})

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data == {
        "label": "Your Plan and Features",
        "data": [
            {
                "id": "planDetails",
                "kind": "plan-details",
                "label": "Plan Details",
                "plan_name": "Premium Plan",
                "organization_plan_details": None,
                "enabled_features": [],
            }
        ],
        "showSaveButton": False,
    }


def test_get_settings_details_plan_info_with_user_entitlements(test_user: User) -> None:
    org = Organization.objects.create(name="Test Organization")
    test_user.organization = org
    test_user.entitlements = [EntitlementType.meeting_assistant, EntitlementType.client_intelligence]
    test_user.save()
    SubscriptionPlan.objects.create(
        organization=org,
        title="Premium Plan",
        start_date=datetime.datetime(2023, 1, 1, tzinfo=datetime.timezone.utc),
        _entitlements=[
            Entitlement(
                type="meeting_assistant", license_count=5, details=MeetingAssistantEntitlementDetails(meetings_count=10)
            ).model_dump(mode="json"),
            Entitlement(type="client_intelligence", license_count=3).model_dump(mode="json"),
        ],
    )

    response = client.get("/api/v2/settings/details", params={"identifier": "plan-details"})

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["data"][0]["enabled_features"] == [
        "Meeting Assistant",
        "Client Intelligence",
    ]


def test_get_settings_details_plan_info_organization_no_organization(test_user: User) -> None:
    org = Organization.objects.create(name="Test Organization")
    test_user.entitlements = [EntitlementType.organization_admin, EntitlementType.meeting_assistant]
    test_user.save()
    SubscriptionPlan.objects.create(
        organization=org,
        term="monthly",
        title="Premium Plan",
        start_date=datetime.datetime(2023, 1, 1, tzinfo=datetime.timezone.utc),
        _entitlements=[
            Entitlement(
                type="meeting_assistant", license_count=5, details=MeetingAssistantEntitlementDetails(meetings_count=10)
            ).model_dump(mode="json"),
            Entitlement(type="organization_admin", license_count=1).model_dump(mode="json"),
        ],
    )

    response = client.get("/api/v2/settings/details", params={"identifier": "plan-details"})
    assert response.status_code == status.HTTP_200_OK
    assert not response.json()["data"][0].get("organization_plan_details")


def test_get_settings_details_plan_info_organization_no_plan(test_user: User) -> None:
    org = Organization.objects.create(name="Test Organization")
    test_user.entitlements = [EntitlementType.organization_admin, EntitlementType.meeting_assistant]
    test_user.organization = org
    test_user.save()
    SubscriptionPlan.objects.create(
        organization=Organization.objects.create(name="Another Organization"),
        term="monthly",
        title="Premium Plan",
        start_date=datetime.datetime(2023, 1, 1, tzinfo=datetime.timezone.utc),
        _entitlements=[
            Entitlement(
                type="meeting_assistant", license_count=5, details=MeetingAssistantEntitlementDetails(meetings_count=10)
            ).model_dump(mode="json"),
            Entitlement(type="organization_admin", license_count=1).model_dump(mode="json"),
        ],
    )

    response = client.get("/api/v2/settings/details", params={"identifier": "plan-details"})
    assert response.status_code == status.HTTP_200_OK
    assert not response.json()["data"][0].get("organization_plan_details")


def test_get_settings_details_plan_info_with_organization_plan_details(test_user: User) -> None:
    org = Organization.objects.create(name="Test Organization")
    test_user.organization = org
    test_user.entitlements = [EntitlementType.organization_admin, EntitlementType.meeting_assistant]
    test_user.save()
    SubscriptionPlan.objects.create(
        organization=org,
        term="monthly",
        title="Premium Plan",
        start_date=datetime.datetime(2023, 1, 1, tzinfo=datetime.timezone.utc),
        _entitlements=[
            Entitlement(
                type="meeting_assistant", license_count=5, details=MeetingAssistantEntitlementDetails(meetings_count=10)
            ).model_dump(mode="json"),
            Entitlement(type="organization_admin", license_count=1).model_dump(mode="json"),
        ],
    )

    response = client.get("/api/v2/settings/details", params={"identifier": "plan-details"})

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data == {
        "label": "Your Plan and Features",
        "data": [
            {
                "id": "planDetails",
                "kind": "plan-details",
                "label": "Plan Details",
                "plan_name": "Premium Plan",
                "enabled_features": ["Organization Admin", "Meeting Assistant"],
                "organization_plan_details": {
                    "plan_term": "monthly",
                    "meetings_allowed_per_term": 50,
                    "meetings_used_this_term": 0,
                    "features": [
                        {
                            "id": "meeting_assistant",
                            "label": "Meeting Assistant",
                            "license_count": 5,
                            "user_uuids": [
                                str(test_user.uuid),
                            ],
                        },
                        {
                            "id": "organization_admin",
                            "label": "Organization Admin",
                            "license_count": 1,
                            "user_uuids": [
                                str(test_user.uuid),
                            ],
                        },
                    ],
                    "users": [
                        {
                            "uuid": str(test_user.uuid),
                            "name": test_user.name,
                            "email": test_user.email,
                        }
                    ],
                },
            }
        ],
        "showSaveButton": False,
    }


@pytest.mark.parametrize(
    "plan_term, plan_start_date, current_date, completed_meeting_dates, expected_meetings_used",
    [
        (
            SubscriptionTerm.monthly,
            datetime.datetime(2023, 1, 1, tzinfo=datetime.timezone.utc),
            datetime.datetime(2023, 1, 24, tzinfo=datetime.timezone.utc),
            [
                datetime.datetime(2022, 12, 31, tzinfo=datetime.timezone.utc),
                datetime.datetime(2023, 1, 1, tzinfo=datetime.timezone.utc),
                datetime.datetime(2023, 1, 15, tzinfo=datetime.timezone.utc),
                datetime.datetime(2023, 1, 30, tzinfo=datetime.timezone.utc),
                datetime.datetime(2023, 1, 31, tzinfo=datetime.timezone.utc),
            ],
            3,
        ),
        (
            SubscriptionTerm.monthly,
            datetime.datetime(2023, 1, 1, 11, 59, tzinfo=datetime.timezone.utc),
            datetime.datetime(2023, 1, 24, tzinfo=datetime.timezone.utc),
            [
                datetime.datetime(2022, 12, 31, tzinfo=datetime.timezone.utc),
                datetime.datetime(2023, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
                datetime.datetime(2023, 1, 15, tzinfo=datetime.timezone.utc),
                datetime.datetime(2023, 1, 30, 23, 59, tzinfo=datetime.timezone.utc),
                datetime.datetime(2023, 1, 31, 0, 0, tzinfo=datetime.timezone.utc),
            ],
            3,
        ),
        (
            SubscriptionTerm.yearly,
            datetime.datetime(2023, 1, 1, tzinfo=datetime.timezone.utc),
            datetime.datetime(2023, 6, 15, tzinfo=datetime.timezone.utc),
            [
                datetime.datetime(2022, 6, 14, tzinfo=datetime.timezone.utc),
                datetime.datetime(2022, 12, 31, tzinfo=datetime.timezone.utc),
                datetime.datetime(2023, 1, 1, tzinfo=datetime.timezone.utc),
                datetime.datetime(2023, 3, 10, tzinfo=datetime.timezone.utc),
                datetime.datetime(2023, 5, 20, tzinfo=datetime.timezone.utc),
                datetime.datetime(2023, 6, 1, tzinfo=datetime.timezone.utc),
                datetime.datetime(2023, 12, 31, tzinfo=datetime.timezone.utc),
                datetime.datetime(2024, 1, 1, tzinfo=datetime.timezone.utc),
            ],
            5,
        ),
        (
            SubscriptionTerm.yearly,
            datetime.datetime(2023, 1, 1, 11, 59, tzinfo=datetime.timezone.utc),
            datetime.datetime(2023, 6, 15, tzinfo=datetime.timezone.utc),
            [
                datetime.datetime(2022, 12, 31, 23, 59, tzinfo=datetime.timezone.utc),
                datetime.datetime(2023, 1, 1, 0, 1, tzinfo=datetime.timezone.utc),
                datetime.datetime(2023, 12, 31, 23, 59, tzinfo=datetime.timezone.utc),
                datetime.datetime(2024, 1, 1, 0, 1, tzinfo=datetime.timezone.utc),
            ],
            2,
        ),
        (
            SubscriptionTerm.monthly,
            datetime.datetime(2023, 1, 1, tzinfo=datetime.timezone.utc),
            datetime.datetime(2023, 1, 24, tzinfo=datetime.timezone.utc),
            [],
            0,
        ),
        (
            SubscriptionTerm.yearly,
            datetime.datetime(2023, 1, 1, tzinfo=datetime.timezone.utc),
            datetime.datetime(2023, 6, 15, tzinfo=datetime.timezone.utc),
            [],
            0,
        ),
    ],
    ids=[
        "monthly_plan",
        "monthly_plan_with_start_time",
        "yearly_plan",
        "yearly_plan_with_start_time",
        "monthly_plan_with_no_completed_meetings",
        "yearly_plan_with_no_completed_meetings",
    ],
)
@mock.patch("api.routers.settings._now")
def test_get_settings_details_plan_info_meetings_used_this_term_with_completed_meetings(
    mock_now: MagicMock,
    plan_term: SubscriptionTerm,
    plan_start_date: datetime.datetime,
    current_date: datetime.datetime,
    completed_meeting_dates: list[datetime.datetime],
    expected_meetings_used: int,
    test_user: User,
) -> None:
    org = Organization.objects.create(name="Test Organization")
    test_user.organization = org
    test_user.entitlements = [EntitlementType.organization_admin]
    test_user.save()

    mock_now.return_value = current_date

    for date in completed_meeting_dates:
        Note.objects.create(note_owner=test_user, created=date, status=Note.PROCESSING_STATUS.processed)

    SubscriptionPlan.objects.create(
        organization=org,
        term=plan_term,
        title="Premium Plan",
        start_date=plan_start_date,
        _entitlements=[
            Entitlement(
                type="meeting_assistant", license_count=5, details=MeetingAssistantEntitlementDetails(meetings_count=10)
            ).model_dump(mode="json"),
            Entitlement(type="organization_admin", license_count=1).model_dump(mode="json"),
        ],
    )

    response = client.get("/api/v2/settings/details", params={"identifier": "plan-details"})

    assert response.status_code == status.HTTP_200_OK
    assert response.json()["data"][0]["organization_plan_details"]["meetings_used_this_term"] == expected_meetings_used


def test_get_settings_details_plan_info_meetings_used_this_term_with_other_organization(
    test_user: User, django_user_model: User
) -> None:
    org1 = Organization.objects.create(name="Test Organization 1")
    test_user.organization = org1
    test_user.entitlements = [EntitlementType.organization_admin]
    test_user.save()

    other_user = django_user_model.objects.create(username="test1", organization=org1)

    org2 = Organization.objects.create(name="Test Organization 2")
    other_org_user = django_user_model.objects.create(username="test2")
    other_org_user.organization = org2
    other_org_user.save()

    Note.objects.create(
        note_owner=other_user,
        status=Note.PROCESSING_STATUS.finalized,
    )
    Note.objects.create(
        note_owner=other_org_user,
        status=Note.PROCESSING_STATUS.processed,
    )
    Note.objects.create(
        note_owner=test_user,
        status=Note.PROCESSING_STATUS.processed,
    )

    SubscriptionPlan.objects.create(
        organization=org1,
        title="Premium Plan",
        term=SubscriptionTerm.monthly,
        start_date=datetime.datetime.now(tz=datetime.timezone.utc) - datetime.timedelta(days=1),
        _entitlements=[
            Entitlement(type="organization_admin", license_count=1).model_dump(mode="json"),
        ],
    )

    response = client.get("/api/v2/settings/details", params={"identifier": "plan-details"})

    assert response.status_code == status.HTTP_200_OK
    assert response.json()["data"][0]["organization_plan_details"]["meetings_used_this_term"] == 2


def test_get_settings_details_plan_info_meetings_used_this_term_with_non_finished_meetings(test_user: User) -> None:
    org = Organization.objects.create(name="Test Organization")
    test_user.organization = org
    test_user.entitlements = [EntitlementType.organization_admin]
    test_user.save()

    SubscriptionPlan.objects.create(
        organization=org,
        title="Premium Plan",
        term=SubscriptionTerm.monthly,
        start_date=datetime.datetime.now(tz=datetime.timezone.utc) - datetime.timedelta(days=1),
        _entitlements=[
            Entitlement(type="organization_admin", license_count=1).model_dump(mode="json"),
        ],
    )

    Note.objects.create(
        note_owner=test_user,
        status=Note.PROCESSING_STATUS.uploaded,
    )
    Note.objects.create(
        note_owner=test_user,
        status=Note.PROCESSING_STATUS.scheduled,
    )
    Note.objects.create(
        note_owner=test_user,
        status=Note.PROCESSING_STATUS.missing,
    )
    Note.objects.create(
        note_owner=test_user,
        status=Note.PROCESSING_STATUS.processed,
    )

    response = client.get("/api/v2/settings/details", params={"identifier": "plan-details"})

    assert response.status_code == status.HTTP_200_OK
    # Only one meeting; the others are not processed
    assert response.json()["data"][0]["organization_plan_details"]["meetings_used_this_term"] == 1
