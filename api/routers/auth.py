import logging
from datetime import datetime, timedelta, timezone
from enum import St<PERSON><PERSON><PERSON>
from typing import Callable
from urllib.parse import urlencode
from uuid import UUID

import jwt
from asgiref.sync import sync_to_async
from django.conf import settings
from django.contrib.auth.tokens import default_token_generator
from fastapi import APIRouter, Depends, HTTPException, Request, Response, status
from fastapi.responses import RedirectResponse
from pydantic import BaseModel, EmailStr
from rest_framework_simplejwt.exceptions import TokenError
from rest_framework_simplejwt.serializers import TokenRefreshSerializer
from rest_framework_simplejwt.tokens import AccessToken, RefreshToken

from api.dependencies import user_from_authorization_header
from deepinsights.core.email_service import ZeplynEmailService
from deepinsights.core.integrations.meetingbot.bot_controller import ZoomStatus
from deepinsights.core.integrations.meetingbot.recall_ai import RecallBotController
from deepinsights.core.integrations.oauth.google import Google<PERSON><PERSON>
from deepinsights.core.integrations.oauth.microsoft import MicrosoftOAuth
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.user_impersonation import UserImpersonation
from deepinsights.meetingsapp.tasks import reconcile_calendar_events, sync_crm_clients
from deepinsights.users.models.user import User

router = APIRouter(tags=["auth"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}")
logger = logging.getLogger(__name__)

MAX_USER_IMPERSONATION_TTL_SECONDS = 3600


# Models


class UserLicenseType(StrEnum):
    advisor = User.LicenseType.advisor.value
    csa = User.LicenseType.csa.value
    staff = User.LicenseType.staff.value


class UserDetails(BaseModel):
    uuid: UUID
    email: EmailStr
    is_active: bool
    status: str
    first_name: str | None = None
    last_name: str | None = None
    org_id: str | None = None
    license_type: UserLicenseType

    class Config:
        from_attributes = True

    @classmethod
    async def from_user(cls, user: User) -> "UserDetails":
        """Create UserDetails from User model asynchronously"""
        # Get organization ID in async-safe way
        org_id = await sync_to_async(lambda: str(user.organization.id) if user.organization else None)()

        return cls(
            uuid=user.uuid,
            email=user.email,
            is_active=user.is_active,
            status=user.status,
            first_name=user.first_name,
            last_name=user.last_name,
            org_id=org_id,
            license_type=UserLicenseType(user.license_type),
        )


class LoginRequest(BaseModel):
    email: EmailStr
    password: str


class LoginResponse(BaseModel):
    user_profile: UserDetails
    refresh_token: str
    access_token: str


class AccessTokenAuthRequest(BaseModel):
    access_token: str


class RedtailAuthRequest(BaseModel):
    user_id: UUID
    username: str
    password: str


class RefreshTokensResponse(BaseModel):
    access_token: str
    # May be an empty string if the refresh token is not rotated.
    refresh_token: str


ValidatorType = Callable[[str], tuple[str | None, str | None, str | None, str | None]]
SetPlatformIdType = Callable[[User, str], None]


# Runs async tasks required after the user logs in.
def _process_async_tasks_at_login(user: User) -> None:
    try:
        sync_crm_clients.delay(user.uuid)
        reconcile_calendar_events.delay(user.uuid)
    except Exception as e:
        logging.error("CRM: client sync failed ", exc_info=e)


# Given a validated existing user, finishes signing them in by generating tokens.
#
# If the user is not allowlisted, this will raise a 403 Forbidden error.
async def _sign_in_user(user: User) -> LoginResponse:
    if user.status != User.STATUS_CHOICES.active:
        logging.error("AUTH: User not active: %s (status %s)", user.email, user.status)
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Account verification required")
    try:
        refresh = await sync_to_async(RefreshToken.for_user)(user)
        user_details = await UserDetails.from_user(user)

        logging.info("AUTH: User logged in: %s", user.email)
        _process_async_tasks_at_login(user)

        return LoginResponse(
            user_profile=user_details,
            refresh_token=str(refresh),
            access_token=str(refresh.access_token),
        )
    except Exception as e:
        logging.error("AUTH: Unexpected error", exc_info=e)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An error occurred during login")


# Common logic for SSO sign-in using access tokens.
async def _access_token_sign_in(
    request: AccessTokenAuthRequest, validator: ValidatorType, set_platform_id: SetPlatformIdType
) -> LoginResponse:
    try:
        email, first_name, last_name, platform_id = validator(request.access_token)
        if not email:
            logging.error("AUTH: Invalid access token")
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Unable to validate user")
        try:
            user = await User.objects.aget(username__iexact=email)
        except User.DoesNotExist:
            logging.info("AUTH: First time login: %s", email)
            full_name = f"{first_name} {last_name}" if first_name and last_name else email
            user = await User.objects.acreate(
                username=email.lower(),
                first_name=first_name,
                last_name=last_name,
                name=full_name,
                status=User.STATUS_CHOICES.waitlisted,
                email=email,
            )
        if platform_id:
            set_platform_id(user, platform_id)
            await user.asave()
        return await _sign_in_user(user)
    except HTTPException:
        raise
    except Exception as e:
        logging.error("AUTH: Error during SSO login", exc_info=e)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Unable to validate user")


@router.post("/login", response_model=LoginResponse, summary="Logs in a user by username and password.")
async def login(request: LoginRequest) -> LoginResponse:
    try:
        user = await User.objects.aget(username__iexact=request.email)
    except User.DoesNotExist:
        logging.error("AUTH: User not found: %s", request.email)
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    if not user.check_password(request.password):
        logging.info("AUTH: Invalid password for user with email %s", request.email)
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")

    return await _sign_in_user(user)


@router.post("/forgot_password", summary="Sends a password reset email to the user.")
async def forgot_password(request: Request, email: EmailStr) -> Response:
    try:
        user = await User.objects.aget(username__iexact=email)
    except User.DoesNotExist:
        logging.error("AUTH: User not found: %s", email)
        # Don't return a failure message to avoid leaking user existence.
        return Response(status_code=status.HTTP_200_OK)

    proto = request.headers.get("X-Forwarded-Proto")
    host = request.headers.get("X-Forwarded-Host")
    base_url = f"{proto}://{host}" if (proto and host) else settings.APP_DOMAIN
    if not base_url:
        logging.error("AUTH: could not determine the URL host for the password reset link")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="URL host not available")

    query_params = urlencode({"email": user.username, "code": default_token_generator.make_token(user)})
    email_contents = f"""
    Hello,
    <br /><br/ >
    We received a request to reset your password for your Zeplyn account. Click the button below to reset your password.
    <br /><br />
    <a href='{base_url}/auth/reset-password?{query_params}'>Reset Password</a>
    <br /><br />
    If you didn't request this password reset, you can safely ignore this email.
    <br /><br />
    Thanks,
    The Zeplyn Team
    """
    ZeplynEmailService().send_email(
        subject="[Zeplyn] Password Reset Request",
        body=email_contents,
        recipients=[user.email],
        is_html=True,
    )

    return Response(status_code=status.HTTP_200_OK)


@router.post("/reset_password", summary="Resets the user's password.")
async def reset_password(email: str, code: str, new_password: str) -> Response:
    try:
        user = await User.objects.aget(username__iexact=email)
    except User.DoesNotExist:
        logging.error("AUTH: User not found: %s", email)
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    if not default_token_generator.check_token(user, code):
        logging.error("AUTH: Invalid token for user with email %s", email)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid token")

    user.set_password(new_password)
    await user.asave()

    return Response(status_code=status.HTTP_200_OK)


@router.post("/google-signin", response_model=LoginResponse, summary="Logs in a user via Google SSO")
async def google_signin(request: AccessTokenAuthRequest) -> LoginResponse:
    def validator(access_token: str) -> tuple[str | None, str | None, str | None, str | None]:
        user_data = GoogleOAuth.get_user_info(access_token)
        if not user_data:
            return (None, None, None, None)
        email = user_data.get("email")
        first_name = user_data.get("given_name")
        last_name = user_data.get("family_name")
        platform_id = user_data.get("id")
        return (email, first_name, last_name, platform_id)

    def set_platform_id(user: User, platform_id: str) -> None:
        user.google_id = platform_id

    return await _access_token_sign_in(request, validator, set_platform_id)


@router.post("/microsoft-signin", response_model=LoginResponse, summary="Logs in a user via Microsoft SSO")
async def microsoft_signin(request: AccessTokenAuthRequest) -> LoginResponse:
    def validator(access_token: str) -> tuple[str | None, str | None, str | None, str | None]:
        user_data = MicrosoftOAuth.get_user_info(access_token)
        if not user_data:
            return (None, None, None, None)
        email = user_data.get("mail")
        first_name = user_data.get("givenName")
        last_name = user_data.get("surname")
        platform_id = user_data.get("id")
        return (email, first_name, last_name, platform_id)

    def set_platform_id(user: User, platform_id: str) -> None:
        user.microsoft_id = platform_id

    return await _access_token_sign_in(request, validator, set_platform_id)


# Handle Zoom OAuth authorization callback and redirect to settings page
@router.get("/zoomauth")
async def handle_zoom_authorization(code: str) -> RedirectResponse:
    try:
        zoom_status = await sync_to_async(RecallBotController.register_user_oauth_credential)(code)
        integration_status = "false"
        match zoom_status:
            case ZoomStatus.FAILURE:
                integration_status = "false"
            case ZoomStatus.SUCCESS:
                integration_status = "true"
            case ZoomStatus.RETRY_REQUIRED:
                integration_status = "retryRequired"

        params = {"integration": "Zoom", "integrationStatus": integration_status}

        # Redirect to settings page with status parameters
        redirect_url = f"/settings?{urlencode(params)}"
        return RedirectResponse(
            url=redirect_url,
            status_code=status.HTTP_303_SEE_OTHER,
        )

    except Exception as e:
        logging.error("Error during Zoom authorization ", exc_info=e)

    # This is a bit simplistic, but ultimately, whatever happens, we don't have
    # a better place to direct the user. Even in the case where the integration
    # fails, the easiest thing to do is return them to settings to try again.
    params = {"integration": "Zoom", "integrationStatus": "false"}
    redirect_url = f"/settings?{urlencode(params)}"
    return RedirectResponse(url=redirect_url, status_code=status.HTTP_303_SEE_OTHER)


# Generate a new access token (and potentially a new refresh token) from an existing refresh token.
@router.post("/refresh_tokens")
async def refresh_tokens(refresh_token: str) -> RefreshTokensResponse:
    try:
        refresh_token_serializer = TokenRefreshSerializer(data={"refresh": refresh_token})
        if not await sync_to_async(refresh_token_serializer.is_valid)():
            logging.error("AUTH: Invalid refresh token")
            raise TokenError("Invalid refresh token")
        data = refresh_token_serializer.validated_data
        return RefreshTokensResponse(access_token=data.get("access", ""), refresh_token=data.get("refresh", ""))
    except TokenError as e:
        logging.error("AUTH: Invalid token", exc_info=e)
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
    except Exception as e:
        logging.error("AUTH: Error refreshing token", exc_info=e)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)


@router.post("/impersonate_user", status_code=status.HTTP_200_OK)
async def impersonate_user(
    username: str,
    purpose: str,
    ttl_seconds: int = MAX_USER_IMPERSONATION_TTL_SECONDS,
    user: User = Depends(user_from_authorization_header),
) -> LoginResponse:
    enable_user_impersonation = await sync_to_async(Flags.EnableUserImpersonation.is_active_for_user)(user)
    if not user.is_superuser or not enable_user_impersonation:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN)

    effective_ttl_seconds = min(ttl_seconds, MAX_USER_IMPERSONATION_TTL_SECONDS)

    try:
        user_to_impersonate = await User.objects.aget(username=username)
        user_details = await UserDetails.from_user(user_to_impersonate)
        current_time: datetime = datetime.now(timezone.utc)

        # Existing Impersonation session
        try:
            user_impersonation_session = (
                await UserImpersonation.objects.filter(user_impersonated=user_to_impersonate, user_impersonating=user)
                .order_by("-created")
                .afirst()
            )
            if user_impersonation_session:
                try:
                    token = AccessToken(user_impersonation_session.access_token)  # type: ignore[arg-type]
                    if token.payload.get("exp", 0) > current_time.timestamp():
                        logging.info(
                            "AUTH: Using existing valid impersonation session: %s", user_impersonation_session.uuid
                        )
                        return LoginResponse(
                            user_profile=user_details,
                            refresh_token="",
                            access_token=user_impersonation_session.access_token,
                        )
                except (jwt.PyJWTError, TokenError):
                    logging.info(
                        "AUTH: Existing impersonation token is invalid: %s", user_impersonation_session.access_token
                    )
        except UserImpersonation.DoesNotExist:
            logging.info("AUTH: No existing impersonation session found")

        # New Impersonation Session
        refresh = await sync_to_async(RefreshToken.for_user)(user_to_impersonate)
        await sync_to_async(refresh.blacklist)()

        access_token = refresh.access_token
        access_token.payload["exp"] = int((current_time + timedelta(seconds=effective_ttl_seconds)).timestamp())

        user_impersonation_session = await UserImpersonation.objects.acreate(
            user_impersonated=user_to_impersonate,
            impersonated_email=user_to_impersonate.email,
            user_impersonating=user,
            impersonating_email=user.email,
            access_token=str(access_token),
            purpose=purpose,
        )
        logging.info("AUTH: User impersonation session created: %s", user_impersonation_session.uuid)

        return LoginResponse(
            user_profile=user_details,
            refresh_token="",
            access_token=str(access_token),
        )

    except User.DoesNotExist as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND) from e
    except Exception as e:
        logging.error("AUTH: Unexpected error", exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An error occurred during Impersonation"
        ) from e
