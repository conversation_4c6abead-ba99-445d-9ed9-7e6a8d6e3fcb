import datetime
import json
import logging
import mimetypes
import uuid
from typing import Any, Bin<PERSON><PERSON>, Generator
from unittest.mock import ANY, Async<PERSON><PERSON>, <PERSON>Mock, patch

import pytest
from asgiref.sync import async_to_sync
from django.core.files.uploadedfile import SimpleUploadedFile
from django.test import override_settings
from django.utils import timezone
from fastapi import status
from fastapi.testclient import TestClient
from pytest_django import DjangoAssertNumQueries

from api.dependencies import user_from_authorization_header
from api.internal_api import internal_api as app
from api.routers.note import create_or_update_interaction, get_note, list_notes
from api.routers.note_models import FollowUpStatus, NoteAudioSource, ProcessingStatus
from deepinsights.core.integrations.meetingbot.bot_controller import BotStatus
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.audio_buffer import AudioBuffer
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.client_interaction import ClientInteraction
from deepinsights.meetingsapp.models.meeting_bot import MeetingBot
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent
from deepinsights.meetingsapp.models.search_query import SearchQuery
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingData, StructuredMeetingDataTemplate
from deepinsights.meetingsapp.models.structured_meeting_data_schema import StructuredMeetingDataSchema
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User

client = TestClient(app)

pytestmark = [pytest.mark.django_db(transaction=True)]


# Creates and returns a new, valid note.
def _create_note(
    note_owner: User | None,
    authorized_users: list[User] | None = None,
    title: str = "Test meeting",
    created: datetime.datetime | None = None,
    summary: dict[str, Any] | None = None,
) -> Note:
    n: Note = Note.objects.create(
        note_owner=note_owner,
        uuid=uuid.uuid4(),
        note_type="meeting_recording",
        status="uploaded",
        metadata={"meeting_name": title},
        created=created or timezone.now(),
        raw_transcript="[{'speaker': 'speaker0', 'start': datetime.timedelta(microseconds=640000), 'end': datetime.timedelta(seconds=10, microseconds=20000), 'utt': \"Welcome to TRS.\"}, {'speaker': 'speaker1', 'start': datetime.timedelta(seconds=10, microseconds=320000), 'end': datetime.timedelta(seconds=12, microseconds=99999), 'utt': 'Thank you. Yeah. Genuinely'}]",
        summary=summary,
    )
    n.authorized_users.add(*authorized_users if authorized_users is not None else ([note_owner] if note_owner else []))
    n.save()
    return n


@pytest.fixture(autouse=True)
def teardown() -> Generator[None, Any, None]:
    app.dependency_overrides = {}
    yield
    app.dependency_overrides = {}


@pytest.fixture()
def test_org() -> Organization:
    return Organization.objects.create(name="Test org")


@pytest.fixture
def test_user(django_user_model: User, test_org: Organization) -> User:
    test_user = django_user_model.objects.create(
        username="<EMAIL>",
        preferences={
            "show_transcript_in_frontend": True,
            "send_task_reminder_email": False,
            "delete_buffer": True,
            "due_date_offset_seconds": 0,
            "email_settings": {
                "ccs": [],
                "bccs": [],
                "followup_email_format_prompt": "",
                "meeting_notes_email_template": None,
            },
            "bot_preferences": {
                "not_recording_image_b64": None,
                "recording_image_b64": None,
                "recording_message_b64": None,
                "notetaker_name": None,
                "enable_video": True,
            },
        },
        organization=test_org,
    )
    app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.select_related("organization").get(
        username="<EMAIL>"
    )
    return test_user


def test_processing_status() -> None:
    assert ProcessingStatus("uploaded") == ProcessingStatus.UPLOADED
    assert ProcessingStatus("processed") == ProcessingStatus.PROCESSED
    assert ProcessingStatus("missing") == ProcessingStatus.MISSING
    assert ProcessingStatus("scheduled") == ProcessingStatus.SCHEDULED
    assert ProcessingStatus("finalized") == ProcessingStatus.FINALIZED
    assert ProcessingStatus("unknown") == ProcessingStatus.UNKNOWN
    assert ProcessingStatus("invalid_value") == ProcessingStatus.UNKNOWN
    assert ProcessingStatus(None) == ProcessingStatus.UNKNOWN
    assert ProcessingStatus._missing_("invalid_value") == ProcessingStatus.UNKNOWN


def test_follow_up_status(caplog: pytest.LogCaptureFixture) -> None:
    with caplog.at_level(logging.ERROR):
        assert FollowUpStatus("created") == FollowUpStatus.CREATED
        assert FollowUpStatus("processing") == FollowUpStatus.PROCESSING
        assert FollowUpStatus("completed") == FollowUpStatus.COMPLETED
        assert FollowUpStatus("failed") == FollowUpStatus.FAILED
        assert FollowUpStatus("unknown") == FollowUpStatus.UNKNOWN
        assert len(caplog.records) == 0

    with caplog.at_level(logging.ERROR):
        assert FollowUpStatus("invalid_value") == FollowUpStatus.UNKNOWN
        assert FollowUpStatus("") == FollowUpStatus.UNKNOWN
        assert caplog.messages == [
            "Invalid FollowUpStatus value: invalid_value",
            "Invalid FollowUpStatus value: <empty value>",
        ]


def test_unauthenticated() -> None:
    response = client.get("/api/v2/note/list")
    response_delete_note = client.delete("/api/v2/note/123e4567-e89b-12d3-a456-************")
    response_email_note = client.post("/api/v2/note/123e4567-e89b-12d3-a456-************/email")
    response_email_followup_mailto = client.get(
        "/api/v2/note/123e4567-e89b-12d3-a456-************/email-followup-mailto"
    )

    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response_delete_note.status_code == status.HTTP_403_FORBIDDEN
    assert response_email_note.status_code == status.HTTP_403_FORBIDDEN
    assert response_email_followup_mailto.status_code == status.HTTP_403_FORBIDDEN


class TestListNotes:
    def test_no_notes(self, test_user: User) -> None:
        response = client.get("/api/v2/note/list")
        assert response.status_code == status.HTTP_200_OK

    def test_authorization_no_license(self, test_user: User, django_user_model: User) -> None:
        org = Organization.objects.create(name="Test org two")
        user = test_user
        second_user = django_user_model.objects.create(
            username="<EMAIL>", organization=org, license_type=User.LicenseType.csa
        )

        authorized_notes = [
            _create_note(user, [user]),
            _create_note(second_user, [user]),
            _create_note(user, [user, second_user]),
            _create_note(second_user, [user, second_user]),
        ]
        _create_note(user, [])
        _create_note(second_user, [second_user])

        response = client.get("/api/v2/note/list")
        assert response.status_code == status.HTTP_200_OK
        assert set([note["uuid"] for note in response.json()]) == set([str(note.uuid) for note in authorized_notes])

    @pytest.mark.parametrize("is_superuser", [True, False])
    def test_authorization_advisor_license(self, test_user: User, django_user_model: User, is_superuser: bool) -> None:
        org = Organization.objects.create(name="Test org two ")
        user = test_user
        user.license_type = User.LicenseType.advisor
        user.is_superuser = is_superuser
        user.first_name = "Test"
        user.last_name = "User"
        user.save()
        second_user = django_user_model.objects.create(
            username="<EMAIL>",
            organization=org,
            license_type=User.LicenseType.csa,
            first_name="Other",
            last_name="One",
        )

        authorized_notes = [
            _create_note(user, [user]),
            _create_note(second_user, [user]),
            _create_note(user, [user, second_user]),
            _create_note(second_user, [user, second_user]),
        ]
        _create_note(user, [])
        _create_note(second_user, [second_user])
        _create_note(None, None)

        # The user is authorized to view this note, but since there is no owner, it is not returned.
        _create_note(None, [user])

        response = client.get("/api/v2/note/list")
        assert response.status_code == status.HTTP_200_OK
        notes = response.json()
        assert set([note["uuid"] for note in notes]) == set([str(note.uuid) for note in authorized_notes])
        assert [note["owner"] for note in notes] == [
            {"name": "Other One", "uuid": str(second_user.uuid)},
            {"name": "Test User", "uuid": str(user.uuid)},
            {"name": "Other One", "uuid": str(second_user.uuid)},
            {"name": "Test User", "uuid": str(user.uuid)},
        ]

    @pytest.mark.parametrize(
        "license_type, is_superuser",
        [
            (User.LicenseType.csa, True),
            (User.LicenseType.csa, False),
            (User.LicenseType.staff, True),
            (User.LicenseType.staff, False),
        ],
    )
    def test_authorization_for_high_privilege_users(
        self,
        test_user: User,
        django_user_model: User,
        license_type: User.LicenseType,
        is_superuser: bool,
        test_org: Organization,
    ) -> None:
        user = test_user
        user.license_type = license_type
        user.is_superuser = is_superuser
        user.save()
        second_user = django_user_model.objects.create(username="<EMAIL>", organization=test_org)

        org_two = Organization.objects.create(name="Test org two")
        third_user = django_user_model.objects.create(username="<EMAIL>", organization=org_two)

        authorized_notes = [
            _create_note(user, []),
            _create_note(user, [user]),
            _create_note(user, [user, second_user]),
            _create_note(user, [second_user]),
            _create_note(second_user, []),
            _create_note(second_user, [user]),
            _create_note(second_user, [user, second_user]),
            _create_note(second_user, [second_user]),
        ]

        _create_note(third_user, [])
        _create_note(third_user, [user, second_user])

        response = client.get("/api/v2/note/list")

        assert response.status_code == status.HTTP_200_OK
        assert set([note["uuid"] for note in response.json()]) == set([str(note.uuid) for note in authorized_notes])

    def test_query(self, test_user: User) -> None:
        user = test_user

        note_one = _create_note(user, title="One")
        note_two = _create_note(user, title="Two")
        note_one_two = _create_note(user, title="One Two")
        note_one_two_different_casing = _create_note(user, title="oNe tWo")
        note_three_two = _create_note(user, title="three two")

        for query, notes in [
            ("one", [note_one, note_one_two, note_one_two_different_casing]),
            ("ONE", [note_one, note_one_two, note_one_two_different_casing]),
            ("two", [note_two, note_one_two, note_one_two_different_casing, note_three_two]),
            ("three", [note_three_two]),
            ("one two", [note_one_two, note_one_two_different_casing]),
            ("one  two", [note_one_two, note_one_two_different_casing]),
            ("three two", [note_three_two]),
        ]:
            response = client.get(f"/api/v2/note/list?q={query}")
            assert response.status_code == status.HTTP_200_OK
            assert set([note["uuid"] for note in response.json()]) == set([str(note.uuid) for note in notes])

    def test_ordering(self, test_user: User) -> None:
        user = test_user

        note_2021 = _create_note(user, created=datetime.datetime(2021, 1, 1, tzinfo=datetime.timezone.utc))
        note_2020 = _create_note(user, created=datetime.datetime(2020, 1, 1, tzinfo=datetime.timezone.utc))
        note_2018 = _create_note(user, created=datetime.datetime(2018, 1, 1, tzinfo=datetime.timezone.utc))
        note_2022 = _create_note(user, created=datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc))
        note_2024 = _create_note(user, created=datetime.datetime(2024, 1, 1, tzinfo=datetime.timezone.utc))

        ordered_notes = [
            note_2024,
            note_2022,
            note_2021,
            note_2020,
            note_2018,
        ]

        response = client.get("/api/v2/note/list")
        assert response.status_code == status.HTTP_200_OK
        assert [note["uuid"] for note in response.json()] == [str(note.uuid) for note in ordered_notes]

    def test_deleted_notes_not_returned(self, test_user: User) -> None:
        user = test_user

        notes = [
            _create_note(user),
            _create_note(user),
        ]
        deleted_notes = [
            _create_note(user),
            _create_note(user),
        ]
        for note in deleted_notes:
            note.is_deleted = True
            note.save()

        response = client.get("/api/v2/note/list")
        assert response.status_code == status.HTTP_200_OK
        assert set([note["uuid"] for note in response.json()]) == set([str(note.uuid) for note in notes])

    def test_partial_failure(self, test_user: User) -> None:
        user = test_user

        valid_notes = [
            _create_note(user),
            _create_note(user),
        ]
        another_valid_note = Note.objects.create(
            note_owner=user,
            note_type="meeting_recording",
        )
        another_valid_note.authorized_users.add(user)
        another_valid_note.save()
        valid_notes.append(another_valid_note)

        invalid_note = Note.objects.create(
            note_owner=user,
            status="scheduled",
        )
        invalid_note.authorized_users.add(user)
        invalid_note.save()

        response = client.get("/api/v2/note/list")
        assert response.status_code == status.HTTP_200_OK
        assert set([note["uuid"] for note in response.json()]) == set([str(note.uuid) for note in valid_notes])

    def test_returned_note_contents(self, test_user: User) -> None:
        user = test_user

        minimal_note = Note.objects.create(
            note_owner=user,
            created=datetime.datetime(2021, 12, 31, tzinfo=datetime.timezone.utc),
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={
                "meeting_name": "Meeting name",
                "meeting_duration": 10,
            },
        )
        minimal_note.authorized_users.add(user)
        minimal_note.save()

        meeting_type = MeetingType.objects.create(name="Internal", category=MeetingType.Category.INTERNAL)
        full_note = Note.objects.create(
            note_owner=user,
            created=datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc),
            uuid=uuid.uuid4(),
            note_type="voice_memo",
            status="scheduled",
            metadata={
                "meeting_name": "Meeting name",
                "meeting_duration": 20,
                "more": "stuff",
                "here": "we go!",
                "scheduled_at": datetime.datetime(2022, 1, 2, tzinfo=datetime.timezone.utc).isoformat(),
                "meeting_source_id": "source_id",
                "tags": ["hello", "world"],
            },
            client={"uuid": "client-uuid", "email": "<EMAIL>", "name": "Test Client"},
            meeting_type=meeting_type,
        )
        full_note.authorized_users.add(user)
        full_note.save()

        attendee_one = Attendee.objects.create(note=minimal_note, attendee_name="Attendee One")
        attendee_two = Attendee.objects.create(note=minimal_note, attendee_name="")
        attendee_three = Attendee.objects.create(note=minimal_note)

        interaction = ClientInteraction.objects.create(note=full_note, meeting_type=meeting_type)

        response = client.get("/api/v2/note/list")
        assert response.status_code == status.HTTP_200_OK
        notes = response.json()
        assert len(notes) == 2
        assert notes == [
            {
                "uuid": str(full_note.uuid),
                "created": full_note.created.isoformat(),
                "modified": full_note.modified.isoformat(),
                "note_type": "voice_memo",
                "status": ProcessingStatus.SCHEDULED,
                "meeting_name": "Meeting name",
                "client": {"uuid": "client-uuid", "email": "<EMAIL>", "name": "Test Client"},
                "scheduled_start_time": datetime.datetime(2022, 1, 2, tzinfo=datetime.timezone.utc).isoformat(),
                "scheduled_end_time": None,
                "meeting_source_id": "source_id",
                "scheduled_event_uuid": None,
                "meeting_type": "Internal",
                "meeting_category": MeetingType.Category.INTERNAL,
                "tags": ["hello", "world"],
                "attendees": [],
                "owner": {"name": "None None", "uuid": str(user.uuid)},
                "meeting_duration_seconds": 20.0,
                "audio_source": NoteAudioSource.MIC,
                "autojoin_available": False,
                "autojoin_enabled": False,
                "autojoin_editable": False,
                "meeting_link": None,
            },
            {
                "uuid": str(minimal_note.uuid),
                "created": minimal_note.created.isoformat(),
                "modified": minimal_note.modified.isoformat(),
                "note_type": "meeting_recording",
                "status": ProcessingStatus.UPLOADED,
                "meeting_name": "Meeting name",
                "client": None,
                "scheduled_start_time": None,
                "scheduled_end_time": None,
                "meeting_source_id": None,
                "scheduled_event_uuid": None,
                "meeting_category": None,
                "meeting_type": None,
                "tags": [],
                "attendees": [
                    {"uuid": str(attendee_one.uuid), "name": "Attendee One"},
                    {"uuid": str(attendee_two.uuid), "name": ""},
                    {"uuid": str(attendee_three.uuid), "name": ""},
                ],
                "owner": {"name": "None None", "uuid": str(user.uuid)},
                "meeting_duration_seconds": 10.0,
                "audio_source": NoteAudioSource.MIC,
                "autojoin_available": False,
                "autojoin_enabled": False,
                "autojoin_editable": False,
                "meeting_link": None,
            },
        ]

    def test_audio_source(self, test_user: User) -> None:
        user = test_user

        mic_note = Note.objects.create(
            note_owner=user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={
                "meeting_name": "Meeting name",
                "meeting_duration": 10,
            },
        )
        mic_note.authorized_users.add(user)
        mic_note.save()

        video_bot_note = Note.objects.create(
            note_owner=user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={
                "meeting_name": "Meeting name",
                "meeting_duration": 10,
            },
        )
        video_bot_note.authorized_users.add(user)
        video_bot_note.save()

        MeetingBot.objects.create(meeting_link="https://example.com", bot_owner=user, note=video_bot_note)

        phone_call_note = Note.objects.create(
            note_owner=user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={
                "meeting_name": "Meeting name",
                "meeting_duration": 10,
            },
        )
        phone_call_note.authorized_users.add(user)
        phone_call_note.save()
        MeetingBot.objects.create(meeting_link="+12125551212", bot_owner=user, note=phone_call_note)

        response = client.get("/api/v2/note/list")
        assert response.status_code == status.HTTP_200_OK
        notes = response.json()
        assert len(notes) == 3
        assert notes[0]["audio_source"] == NoteAudioSource.PHONE
        assert notes[0]["meeting_link"] == "+12125551212"
        assert notes[1]["audio_source"] == NoteAudioSource.VIDEO_CALL
        assert notes[1]["meeting_link"] == "https://example.com"
        assert notes[2]["audio_source"] == NoteAudioSource.MIC
        assert not notes[2]["meeting_link"]

    def test_start_and_end_times(self, test_user: User, django_user_model: User) -> None:
        no_times_note = Note.objects.create(
            created=datetime.datetime(2022, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            note_owner=test_user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={
                "meeting_name": "Meeting name",
                "meeting_duration": 10,
            },
        )
        no_times_note.authorized_users.add(test_user)
        no_times_note.save()

        scheduled_at_note = Note.objects.create(
            created=datetime.datetime(2022, 1, 1, 12, 0, tzinfo=datetime.timezone.utc),
            note_owner=test_user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={
                "meeting_name": "Meeting name",
                "meeting_duration": 10,
                "scheduled_at": datetime.datetime(2022, 1, 2, 10, 0, tzinfo=datetime.timezone.utc).isoformat(),
            },
        )
        scheduled_at_note.authorized_users.add(test_user)
        scheduled_at_note.save()

        scheduled_event_note = Note.objects.create(
            created=datetime.datetime(2022, 1, 1, 6, 0, tzinfo=datetime.timezone.utc),  # Arbitrary mid-time
            note_owner=test_user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={
                "meeting_name": "Meeting name",
                "meeting_duration": 10,
            },
        )
        scheduled_event_note.authorized_users.add(test_user)
        scheduled_event_note.save()
        ScheduledEvent.objects.create(
            user=test_user,
            note=scheduled_event_note,
            start_time=datetime.datetime(2022, 1, 2, 11, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2022, 1, 2, 12, 0, tzinfo=datetime.timezone.utc),
        )

        response = client.get("/api/v2/note/list")
        assert response.status_code == status.HTTP_200_OK
        assert [
            {key: note[key] for key in {"scheduled_start_time", "scheduled_end_time"}} for note in response.json()
        ] == [
            {
                "scheduled_start_time": "2022-01-02T11:00:00+00:00",
                "scheduled_end_time": "2022-01-02T12:00:00+00:00",
            },
            {
                "scheduled_start_time": "2022-01-02T10:00:00+00:00",
                "scheduled_end_time": None,
            },
            {
                "scheduled_start_time": None,
                "scheduled_end_time": None,
            },
        ]

    def test_scheduled_events_auto_join_behavior(self, test_user: User, django_user_model: User) -> None:
        # Note with no bot.
        note_without_bot = Note.objects.create(
            note_owner=test_user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
        )
        note_without_bot.authorized_users.add(test_user)
        note_without_bot.save()
        note_without_bot_event = ScheduledEvent.objects.create(
            user=test_user,
            note=note_without_bot,
            start_time=datetime.datetime(2022, 1, 2, 11, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2022, 1, 2, 12, 0, tzinfo=datetime.timezone.utc),
        )

        # Note with a bot that doesn't have a video URL link.
        note_with_phone_bot = Note.objects.create(
            note_owner=test_user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
        )
        note_with_phone_bot.authorized_users.add(test_user)
        note_with_phone_bot.save()
        note_with_phone_bot_event = ScheduledEvent.objects.create(
            user=test_user,
            note=note_with_phone_bot,
            start_time=datetime.datetime(2022, 1, 2, 11, 1, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2022, 1, 2, 12, 1, tzinfo=datetime.timezone.utc),
        )
        MeetingBot.objects.create(
            meeting_link="+12125551212",
            bot_owner=test_user,
            note=note_with_phone_bot,
        )

        note_with_video_bot = Note.objects.create(
            note_owner=test_user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
        )
        note_with_video_bot.authorized_users.add(test_user)
        note_with_video_bot.save()
        note_with_video_bot_event = ScheduledEvent.objects.create(
            user=test_user,
            note=note_with_video_bot,
            start_time=datetime.datetime(2022, 1, 2, 11, 2, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2022, 1, 2, 12, 2, tzinfo=datetime.timezone.utc),
        )
        MeetingBot.objects.create(
            meeting_link="https://example.com",
            bot_owner=test_user,
            note=note_with_video_bot,
        )

        autojoin_enabled_note_with_video_bot = Note.objects.create(
            note_owner=test_user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
        )
        autojoin_enabled_note_with_video_bot.authorized_users.add(test_user)
        autojoin_enabled_note_with_video_bot.save()
        autojoin_enabled_note_with_video_bot_event = ScheduledEvent.objects.create(
            user=test_user,
            note=autojoin_enabled_note_with_video_bot,
            start_time=datetime.datetime(2022, 1, 2, 11, 3, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2022, 1, 2, 12, 3, tzinfo=datetime.timezone.utc),
            autojoin_behavior=ScheduledEvent.AutoJoinOverride.ENABLED,
        )
        MeetingBot.objects.create(
            meeting_link="https://example.com/2",
            bot_owner=test_user,
            note=autojoin_enabled_note_with_video_bot,
        )

        response = client.get("/api/v2/note/list")
        assert response.status_code == status.HTTP_200_OK
        assert [
            {
                key: response[key]
                for key in {
                    "scheduled_event_uuid",
                    "autojoin_available",
                    "autojoin_enabled",
                    "meeting_link",
                }
            }
            # Reverse the order so that the order of the expected values below
            # matches the order of the creation calls above.
            for response in reversed(response.json())
        ] == [
            {
                "scheduled_event_uuid": str(note_without_bot_event.uuid),
                "autojoin_available": False,
                "autojoin_enabled": False,
                "meeting_link": None,
            },
            {
                "scheduled_event_uuid": str(note_with_phone_bot_event.uuid),
                "autojoin_available": False,
                "autojoin_enabled": False,
                "meeting_link": "+12125551212",
            },
            {
                "scheduled_event_uuid": str(note_with_video_bot_event.uuid),
                "autojoin_available": True,
                "autojoin_enabled": False,
                "meeting_link": "https://example.com",
            },
            {
                "scheduled_event_uuid": str(autojoin_enabled_note_with_video_bot_event.uuid),
                "autojoin_available": True,
                "autojoin_enabled": True,
                "meeting_link": "https://example.com/2",
            },
        ]

    def test_scheduled_events_auto_join_editable(self, test_user: User, django_user_model: User) -> None:
        date = timezone.now() + datetime.timedelta(minutes=1)

        # Fully-future event
        future_note = Note.objects.create(
            note_owner=test_user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
        )
        future_note.authorized_users.add(test_user)
        future_note.save()
        future_note_event = ScheduledEvent.objects.create(
            user=test_user,
            note=future_note,
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
        )

        # Ongoing event
        ongoing_note = Note.objects.create(
            note_owner=test_user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
        )
        ongoing_note.authorized_users.add(test_user)
        ongoing_note.save()
        ongoing_note_event = ScheduledEvent.objects.create(
            user=test_user,
            note=ongoing_note,
            start_time=date - datetime.timedelta(minutes=30),
            end_time=date,
        )

        response = client.get("/api/v2/note/list")
        assert response.status_code == status.HTTP_200_OK
        assert [
            {key: response[key] for key in {"scheduled_event_uuid", "autojoin_editable"}}
            # Reverse the order so that the order of the expected values below
            # matches the order of the creation calls above.
            for response in reversed(response.json())
        ] == [
            {
                "scheduled_event_uuid": str(ongoing_note_event.uuid),
                "autojoin_editable": False,
            },
            {
                "scheduled_event_uuid": str(future_note_event.uuid),
                "autojoin_editable": True,
            },
        ]

    def test_query_limits(
        self, test_user: User, django_user_model: User, django_assert_max_num_queries: DjangoAssertNumQueries
    ) -> None:
        user = test_user
        note = Note.objects.create(
            note_owner=user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={
                "meeting_name": "Meeting name",
                "meeting_duration": 10,
            },
        )
        note.authorized_users.add(
            *django_user_model.objects.bulk_create([User(username=f"{i}@example.com") for i in range(100)])
        )
        note.authorized_users.add(user)
        note.save()

        Attendee.objects.bulk_create([Attendee(note=note, attendee_name=f"Attendee {i}") for i in range(100)])

        with django_assert_max_num_queries(4):
            # This has to call the method directly, or else the context manager will not work.
            async_to_sync(list_notes)(None, None, None, user)

    def test_sorting_priority_order(self, test_user: User) -> None:
        user = test_user

        base_created_time = datetime.datetime(2022, 1, 1, 0, 0, tzinfo=datetime.timezone.utc)

        creation_only_note = Note.objects.create(
            created=base_created_time + datetime.timedelta(days=3),
            note_owner=user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={
                "meeting_name": "Creation Only",
                "meeting_duration": 10,
            },
        )
        creation_only_note.authorized_users.add(user)
        creation_only_note.save()

        metadata_scheduled_note = Note.objects.create(
            created=base_created_time + datetime.timedelta(days=1),
            note_owner=user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={
                "meeting_name": "Metadata Scheduled",
                "meeting_duration": 10,
                "scheduled_at": datetime.datetime(2022, 1, 5, 14, 0, tzinfo=datetime.timezone.utc).isoformat(),
            },
        )
        metadata_scheduled_note.authorized_users.add(user)
        metadata_scheduled_note.save()

        scheduled_event_note = Note.objects.create(
            created=base_created_time,
            note_owner=user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={
                "meeting_name": "Scheduled Event",
                "meeting_duration": 10,
            },
        )
        scheduled_event_note.authorized_users.add(user)
        scheduled_event_note.save()
        ScheduledEvent.objects.create(
            user=user,
            note=scheduled_event_note,
            start_time=datetime.datetime(2022, 1, 5, 15, 0, tzinfo=datetime.timezone.utc),  # Latest scheduled time
            end_time=datetime.datetime(2022, 1, 5, 16, 0, tzinfo=datetime.timezone.utc),
        )

        response = client.get("/api/v2/note/list")
        assert response.status_code == status.HTTP_200_OK

        notes = response.json()
        assert len(notes) == 3

        assert notes[0]["meeting_name"] == "Scheduled Event"
        assert notes[1]["meeting_name"] == "Metadata Scheduled"
        assert notes[2]["meeting_name"] == "Creation Only"

    def test_sorting_with_same_scheduled_times(self, test_user: User) -> None:
        user = test_user

        note1 = Note.objects.create(
            created=datetime.datetime(2022, 1, 1, 10, 0, tzinfo=datetime.timezone.utc),
            note_owner=user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={"meeting_name": "First Note", "meeting_duration": 10},
        )
        note1.authorized_users.add(user)
        note1.save()
        ScheduledEvent.objects.create(
            user=user,
            note=note1,
            start_time=datetime.datetime(2022, 1, 2, 10, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2022, 1, 2, 11, 0, tzinfo=datetime.timezone.utc),
        )

        note2 = Note.objects.create(
            created=datetime.datetime(2022, 1, 1, 11, 0, tzinfo=datetime.timezone.utc),
            note_owner=user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={"meeting_name": "Second Note", "meeting_duration": 10},
        )
        note2.authorized_users.add(user)
        note2.save()
        ScheduledEvent.objects.create(
            user=user,
            note=note2,
            start_time=datetime.datetime(2022, 1, 2, 10, 0, tzinfo=datetime.timezone.utc),  # Same time
            end_time=datetime.datetime(2022, 1, 2, 11, 0, tzinfo=datetime.timezone.utc),
        )

        response = client.get("/api/v2/note/list")
        assert response.status_code == status.HTTP_200_OK

        notes = response.json()
        assert len(notes) == 2

    def test_sorting_with_null_scheduled_at_in_metadata(self, test_user: User) -> None:
        user = test_user

        null_scheduled_note = Note.objects.create(
            created=datetime.datetime(2022, 1, 1, 12, 0, tzinfo=datetime.timezone.utc),
            note_owner=user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={
                "meeting_name": "Null Scheduled",
                "meeting_duration": 10,
                "scheduled_at": None,
            },
        )
        null_scheduled_note.authorized_users.add(user)
        null_scheduled_note.save()

        no_scheduled_note = Note.objects.create(
            created=datetime.datetime(2022, 1, 1, 10, 0, tzinfo=datetime.timezone.utc),
            note_owner=user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={
                "meeting_name": "No Scheduled Key",
                "meeting_duration": 10,
            },
        )
        no_scheduled_note.authorized_users.add(user)
        no_scheduled_note.save()

        response = client.get("/api/v2/note/list")
        assert response.status_code == status.HTTP_200_OK

        notes = response.json()
        assert len(notes) == 2

        assert notes[0]["meeting_name"] == "Null Scheduled"
        assert notes[1]["meeting_name"] == "No Scheduled Key"

    def test_sorting_preserves_existing_functionality(self, test_user: User) -> None:
        user = test_user

        note1 = Note.objects.create(
            created=datetime.datetime(2022, 1, 1, 10, 0, tzinfo=datetime.timezone.utc),
            note_owner=user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={
                "meeting_name": "Important Meeting Alpha",
                "meeting_duration": 10,
                "scheduled_at": datetime.datetime(2022, 1, 2, 14, 0, tzinfo=datetime.timezone.utc).isoformat(),
            },
        )
        note1.authorized_users.add(user)
        note1.save()

        note2 = Note.objects.create(
            created=datetime.datetime(2022, 1, 1, 11, 0, tzinfo=datetime.timezone.utc),
            note_owner=user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={
                "meeting_name": "Important Meeting Beta",
                "meeting_duration": 10,
                "scheduled_at": datetime.datetime(2022, 1, 2, 13, 0, tzinfo=datetime.timezone.utc).isoformat(),
            },
        )
        note2.authorized_users.add(user)
        note2.save()

        response = client.get("/api/v2/note/list?q=Important")
        assert response.status_code == status.HTTP_200_OK

        notes = response.json()
        assert len(notes) == 2

        assert notes[0]["meeting_name"] == "Important Meeting Alpha"
        assert notes[1]["meeting_name"] == "Important Meeting Beta"

        response = client.get("/api/v2/note/list?q=Alpha")
        assert response.status_code == status.HTTP_200_OK

        notes = response.json()
        assert len(notes) == 1
        assert notes[0]["meeting_name"] == "Important Meeting Alpha"

    def test_sorting_edge_case_mixed_scenarios(self, test_user: User) -> None:
        user = test_user

        future_event_note = Note.objects.create(
            created=datetime.datetime(2022, 1, 1, 10, 0, tzinfo=datetime.timezone.utc),
            note_owner=user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="scheduled",
            metadata={"meeting_name": "Future Event", "meeting_duration": 10},
        )
        future_event_note.authorized_users.add(user)
        future_event_note.save()
        ScheduledEvent.objects.create(
            user=user,
            note=future_event_note,
            start_time=datetime.datetime(2024, 12, 31, 10, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2024, 12, 31, 11, 0, tzinfo=datetime.timezone.utc),
        )

        past_event_note = Note.objects.create(
            created=datetime.datetime(2022, 1, 1, 11, 0, tzinfo=datetime.timezone.utc),
            note_owner=user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={"meeting_name": "Past Event", "meeting_duration": 10},
        )
        past_event_note.authorized_users.add(user)
        past_event_note.save()
        ScheduledEvent.objects.create(
            user=user,
            note=past_event_note,
            start_time=datetime.datetime(2021, 1, 1, 10, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2021, 1, 1, 11, 0, tzinfo=datetime.timezone.utc),
        )

        recent_creation_note = Note.objects.create(
            created=datetime.datetime(2023, 1, 1, 10, 0, tzinfo=datetime.timezone.utc),
            note_owner=user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="uploaded",
            metadata={"meeting_name": "Recent Creation", "meeting_duration": 10},
        )
        recent_creation_note.authorized_users.add(user)
        recent_creation_note.save()

        response = client.get("/api/v2/note/list")
        assert response.status_code == status.HTTP_200_OK

        notes = response.json()
        assert len(notes) == 3

        assert notes[0]["meeting_name"] == "Future Event"
        assert notes[1]["meeting_name"] == "Recent Creation"
        assert notes[2]["meeting_name"] == "Past Event"


class TestGetNote:
    def test_content(self, test_user: User) -> None:
        preferences = dict(test_user.preferences or {})
        preferences["show_transcript_in_frontend"] = False
        test_user.preferences = preferences
        test_user.save()
        note = Note.objects.create(
            note_owner=test_user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="processed",
            metadata={
                "meeting_name": "Test Meeting",
                "meeting_duration": 60,
                "scheduled_at": "2022-01-02T10:00:00+00:00",
                "tags": ["test", "api"],
            },
            advisor_notes=["Important note"],
            key_takeaways=["Key point 1", "Key point 2"],
            diarized_trans_with_names='speaker0\t"0:00:04.720000-->0:00:55.829998\nDummy text speaker 0\n\nspeaker1\t"0:00:56.870000-->0:01:25.350006\nDummy text speaker 1\n\nspeaker0\t"0:01:25.730000-->0:01:38.235000\nDummy text speaker 0\n\nspeaker1\t"0:01:39.095000-->0:02:07.024940\nDummy text speaker 1',
            summary={"sections": [{"topic": "Personal Information", "bullets": ["Bullet 1", "Bullet 2"]}]},
        )
        note.authorized_users.add(test_user)
        note.save()
        attendee_one = Attendee.objects.create(note=note, attendee_name="Attendee One")
        attendee_two = Attendee.objects.create(note=note, attendee_name="Attendee Two")

        response = client.get(f"/api/v2/note/{note.uuid}")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "uuid": str(note.uuid),
            "created": note.created.replace(tzinfo=None).isoformat() + "Z",
            "modified": note.modified.replace(tzinfo=None).isoformat() + "Z",
            "note_type": note.note_type,
            "status": note.status,
            "meeting_name": "Test Meeting",
            "meeting_duration_seconds": 60.0,
            "meeting_type_uuid": None,
            "meeting_category": "client",
            "attendees": [
                {
                    "uuid": str(attendee_one.uuid),
                    "name": "Attendee One",
                    "type": "unknown",
                    "speaker_time": None,
                    "speaker_percentage": None,
                    "speaker_alias": None,
                    "client_uuid": None,
                    "user_uuid": None,
                },
                {
                    "uuid": str(attendee_two.uuid),
                    "name": "Attendee Two",
                    "type": "unknown",
                    "speaker_time": None,
                    "speaker_percentage": None,
                    "speaker_alias": None,
                    "client_uuid": None,
                    "user_uuid": None,
                },
            ],
            "tags": ["test", "api"],
            "action_items": [],
            "advisor_notes": ["Important note"],
            "key_takeaways": ["Key point 1", "Key point 2"],
            "client": None,
            "transcript": {"utterances": []},
            "summary_by_topics": {"sections": [{"topic": "Personal Information", "bullets": ["Bullet 1", "Bullet 2"]}]},
            "times_editable": True,
            "scheduled_start_time": "2022-01-02T10:00:00Z",
            "scheduled_end_time": None,
            "bot_id": None,
            "is_deleted": False,
            "features": [],
            "follow_ups": [],
            "authorized_user_uuids": [str(test_user.uuid)],
            "interaction_uuid": None,
        }

    def test_content_with_transcript(self, test_user: User) -> None:
        note = Note.objects.create(
            note_owner=test_user,
            uuid=uuid.uuid4(),
            note_type="meeting_recording",
            status="processed",
            metadata={
                "meeting_name": "Test Meeting",
                "meeting_duration": 60,
                "tags": ["test", "api"],
            },
            advisor_notes=["Important note"],
            key_takeaways=["Key point 1", "Key point 2"],
            diarized_trans_with_names='speaker0\t"0:00:04.720000-->0:00:55.829998\nDummy text speaker 0\n\nspeaker1\t"0:00:56.870000-->0:01:25.350006\nDummy text speaker 1\n\nspeaker0\t"0:01:25.730000-->0:01:38.235000\nDummy text speaker 0\n\nspeaker1\t"0:01:39.095000-->0:02:07.024940\nDummy text speaker 1',
            summary={"sections": [{"topic": "Personal Information", "bullets": ["Bullet 1", "Bullet 2"]}]},
        )
        note.authorized_users.add(test_user)
        note.save()

        attendee_one = Attendee.objects.create(note=note, attendee_name="Attendee One")
        attendee_two = Attendee.objects.create(note=note, attendee_name="Attendee Two")

        response = client.get(f"/api/v2/note/{note.uuid}")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "uuid": str(note.uuid),
            "created": note.created.replace(tzinfo=None).isoformat() + "Z",
            "modified": note.modified.replace(tzinfo=None).isoformat() + "Z",
            "note_type": note.note_type,
            "status": ProcessingStatus.PROCESSED,
            "meeting_name": "Test Meeting",
            "meeting_duration_seconds": 60.0,
            "meeting_type_uuid": None,
            "meeting_category": "client",
            "attendees": [
                {
                    "uuid": str(attendee_one.uuid),
                    "name": "Attendee One",
                    "type": "unknown",
                    "speaker_time": None,
                    "speaker_percentage": None,
                    "speaker_alias": None,
                    "client_uuid": None,
                    "user_uuid": None,
                },
                {
                    "uuid": str(attendee_two.uuid),
                    "name": "Attendee Two",
                    "type": "unknown",
                    "speaker_time": None,
                    "speaker_percentage": None,
                    "speaker_alias": None,
                    "client_uuid": None,
                    "user_uuid": None,
                },
            ],
            "tags": ["test", "api"],
            "action_items": [],
            "advisor_notes": ["Important note"],
            "key_takeaways": ["Key point 1", "Key point 2"],
            "client": None,
            "transcript": {
                "utterances": [
                    {
                        "speaker": "speaker0",
                        "start": "0:00:04.720000",
                        "end": "0:00:55.829998",
                        "text": "Dummy text speaker 0",
                    },
                    {
                        "speaker": "speaker1",
                        "start": "0:00:56.870000",
                        "end": "0:01:25.350006",
                        "text": "Dummy text speaker 1",
                    },
                    {
                        "speaker": "speaker0",
                        "start": "0:01:25.730000",
                        "end": "0:01:38.235000",
                        "text": "Dummy text speaker 0",
                    },
                    {
                        "speaker": "speaker1",
                        "start": "0:01:39.095000",
                        "end": "0:02:07.024940",
                        "text": "Dummy text speaker 1",
                    },
                ]
            },
            "summary_by_topics": {"sections": [{"topic": "Personal Information", "bullets": ["Bullet 1", "Bullet 2"]}]},
            "times_editable": True,
            "scheduled_start_time": None,
            "scheduled_end_time": None,
            "bot_id": None,
            "is_deleted": False,
            "features": [],
            "follow_ups": [],
            "authorized_user_uuids": [str(test_user.uuid)],
            "interaction_uuid": None,
        }

    def test_unknown_status(self, test_user: User) -> None:
        note = Note.objects.create(
            note_owner=test_user,
            note_type="meeting_recording",
        )
        note.authorized_users.add(test_user)
        note.save()

        response = client.get(f"/api/v2/note/{note.uuid}")

        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == ProcessingStatus.UNKNOWN

    def test_content_with_related_models(
        self, test_user: User, django_user_model: User, django_assert_max_num_queries: DjangoAssertNumQueries
    ) -> None:
        meeting_type = MeetingType.objects.create(name="Internal", category=MeetingType.Category.INTERNAL)
        note = Note.objects.create(
            note_owner=test_user,
            note_type="voice_memo",
            status=Note.PROCESSING_STATUS.processed,
            metadata={
                "meeting_name": "Test Meeting",
                "meeting_duration": 60,
                "tags": ["test", "api"],
                "meeting_type": "client",
            },
            advisor_notes=["Important note"],
            key_takeaways=["Key point 1", "Key point 2"],
            diarized_trans_with_names='speaker0\t"0:00:04.720000-->0:00:55.829998\nDummy text speaker 0\n\nspeaker1\t"0:00:56.870000-->0:01:25.350006\nDummy text speaker 1\n\nspeaker0\t"0:01:25.730000-->0:01:38.235000\nDummy text speaker 0\n\nspeaker1\t"0:01:39.095000-->0:02:07.024940\nDummy text speaker 1',
            summary={"sections": [{"topic": "Personal Information", "bullets": ["Bullet 1", "Bullet 2"]}]},
            meeting_type=meeting_type,
        )
        second_user = User.objects.create_user(username="<EMAIL>")
        note.authorized_users.set([test_user, second_user])
        note.save()

        org = Organization.objects.create(name="Test org")

        client_one = Client.objects.create(
            name="Test client", email="<EMAIL>", organization=org, crm_system="wealthbox"
        )
        user_two = django_user_model.objects.create(email="<EMAIL>")

        attendee_one = Attendee.objects.create(note=note, attendee_name="Attendee One", client=client_one)
        attendee_two = Attendee.objects.create(note=note, attendee_name="Attendee Two", user=user_two)

        bot = MeetingBot.objects.create(note=note)

        follow_up = StructuredMeetingData.objects.create(
            note=note, title="Follow-up", kind="test", schema={"test": "schema"}, data={"test": "data"}
        )

        interaction = ClientInteraction.objects.create(note=note, meeting_type=meeting_type)

        response = client.get(f"/api/v2/note/{note.uuid}")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "uuid": str(note.uuid),
            "created": note.created.replace(tzinfo=None).isoformat() + "Z",
            "modified": note.modified.replace(tzinfo=None).isoformat() + "Z",
            "note_type": note.note_type,
            "status": ProcessingStatus.PROCESSED,
            "meeting_name": "Test Meeting",
            "meeting_duration_seconds": 60.0,
            "meeting_type_uuid": str(meeting_type.uuid),
            "meeting_category": "internal",
            "attendees": [
                {
                    "uuid": str(attendee_one.uuid),
                    "name": "Attendee One",
                    "type": "client",
                    "speaker_time": None,
                    "speaker_percentage": None,
                    "speaker_alias": None,
                    "client_uuid": str(client_one.uuid),
                    "user_uuid": None,
                },
                {
                    "uuid": str(attendee_two.uuid),
                    "name": "Attendee Two",
                    "type": "user",
                    "speaker_time": None,
                    "speaker_percentage": None,
                    "speaker_alias": None,
                    "client_uuid": None,
                    "user_uuid": str(user_two.uuid),
                },
            ],
            "tags": ["test", "api"],
            "action_items": [],
            "advisor_notes": ["Important note"],
            "key_takeaways": ["Key point 1", "Key point 2"],
            "client": None,
            "transcript": {
                "utterances": [
                    {
                        "speaker": "speaker0",
                        "start": "0:00:04.720000",
                        "end": "0:00:55.829998",
                        "text": "Dummy text speaker 0",
                    },
                    {
                        "speaker": "speaker1",
                        "start": "0:00:56.870000",
                        "end": "0:01:25.350006",
                        "text": "Dummy text speaker 1",
                    },
                    {
                        "speaker": "speaker0",
                        "start": "0:01:25.730000",
                        "end": "0:01:38.235000",
                        "text": "Dummy text speaker 0",
                    },
                    {
                        "speaker": "speaker1",
                        "start": "0:01:39.095000",
                        "end": "0:02:07.024940",
                        "text": "Dummy text speaker 1",
                    },
                ]
            },
            "summary_by_topics": {"sections": [{"topic": "Personal Information", "bullets": ["Bullet 1", "Bullet 2"]}]},
            "times_editable": True,
            "scheduled_start_time": None,
            "scheduled_end_time": None,
            "bot_id": str(bot.uuid),
            "is_deleted": False,
            "features": [],
            "follow_ups": [
                {
                    "uuid": str(follow_up.uuid),
                    "title": "Follow-up",
                    "kind": "test",
                    "schema": {"test": "schema"},
                    "data": {"test": "data"},
                    "status": FollowUpStatus.COMPLETED,
                }
            ],
            "authorized_user_uuids": [str(second_user.uuid), str(test_user.uuid)],
            "interaction_uuid": str(interaction.uuid),
        }

        # Create more related objects to test for query explosions.
        clients = [
            Client.objects.create(name=f"Client {i}", email=f"client{i}@example.com", organization=org)
            for i in range(10)
        ]
        users = [
            django_user_model.objects.create(username=f"user{i}@example.com", email=f"user{i}@example.com")
            for i in range(10)
        ]

        [Attendee.objects.create(note=note, attendee_name=f"{client.uuid}", client=client) for client in clients]
        [Attendee.objects.create(note=note, attendee_name=f"{user.uuid}", user=user) for user in users]
        [MeetingBot.objects.create(note=note) for _ in range(10)]
        [Task.objects.create(note=note, task_title=f"Task {i}", task_desc=f"Task {i} description") for i in range(10)]
        [StructuredMeetingData.objects.create(note=note, title=f"Follow-up {i}", schema={}, data={}) for i in range(10)]

        with django_assert_max_num_queries(10):
            # This has to call the method directly, or else the context manager will not work.
            get_note(note.uuid, test_user)

    def test_content_with_scheduled_events(self, test_user: User) -> None:
        note = Note.objects.create(
            note_owner=test_user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            metadata={"scheduled_at": "2022-01-02T10:00:00+00:00"},
        )
        note.authorized_users.add(test_user)
        note.save()

        ScheduledEvent.objects.create(
            user=test_user,
            note=note,
            start_time=datetime.datetime(2022, 1, 2, 10, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2022, 1, 2, 11, 0, tzinfo=datetime.timezone.utc),
        )

        response = client.get(f"/api/v2/note/{note.uuid}")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["times_editable"] is True
        assert data["scheduled_start_time"] == "2022-01-02T10:00:00Z"
        assert data["scheduled_end_time"] == "2022-01-02T11:00:00Z"

    def test_content_with_calendar_scheduled_events(self, test_user: User) -> None:
        note = Note.objects.create(
            note_owner=test_user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            metadata={"scheduled_at": "2022-01-02T10:00:00+00:00"},
        )
        note.authorized_users.add(test_user)
        note.save()

        ScheduledEvent.objects.create(
            user=test_user,
            note=note,
            user_specific_source_id="calendar_event_1",
            shared_source_id="calendar_event_shared_1",
            source_data={"test": "data"},
            start_time=datetime.datetime(2022, 1, 2, 10, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2022, 1, 2, 11, 0, tzinfo=datetime.timezone.utc),
        )

        response = client.get(f"/api/v2/note/{note.uuid}")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["times_editable"] is False
        assert data["scheduled_start_time"] == "2022-01-02T10:00:00Z"
        assert data["scheduled_end_time"] == "2022-01-02T11:00:00Z"

    def test_note_not_found(self, test_user: User) -> None:
        non_existent_uuid = uuid.uuid4()
        response = client.get(f"/api/v2/note/{non_existent_uuid}")

        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json() == {"detail": "Note not found"}

    def test_unauthorized(self, test_user: User, django_user_model: User) -> None:
        second_user = django_user_model.objects.create(email="<EMAIL>")
        note = Note.objects.create(
            note_owner=second_user,
            note_type="meeting_recording",
            status="processed",
            metadata={
                "meeting_name": "Test Meeting",
                "meeting_duration": 60,
                "tags": ["test", "api"],
            },
        )
        response = client.get(f"/api/v2/note/{note.uuid}")

        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert response.json() == {"detail": "Not authorized to view this note"}

    @patch("api.routers.note.Note.objects.select_related")
    def test_error_retrieving(self, mock_select_related: MagicMock, test_user: User) -> None:
        mock_select_related.side_effect = Exception("Database error")

        note = _create_note(test_user)
        response = client.get(f"/api/v2/note/{note.uuid}")

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert response.json() == {"detail": "An error occurred while retrieving the note"}


def test_delete_note_success(test_user: User) -> None:
    note = _create_note(test_user)
    response = client.delete(f"/api/v2/note/{note.uuid}")

    assert response.status_code == status.HTTP_204_NO_CONTENT

    updated_note = Note.objects_with_deleted.get(uuid=note.uuid)
    assert updated_note.is_deleted is True


def test_delete_note_with_tasks(test_user: User) -> None:
    """Test that associated tasks are marked as deleted when note is deleted"""
    note = _create_note(test_user)

    # Create some associated tasks
    task1 = Task.objects.create(note=note, task_title="Task 1", task_desc="Task 1 description")
    task2 = Task.objects.create(note=note, task_title="Task 2", task_desc="Task 2 description")

    response = client.delete(f"/api/v2/note/{note.uuid}")
    assert response.status_code == status.HTTP_204_NO_CONTENT

    # Verify both tasks are marked as deleted
    updated_task1 = Task.objects_with_deleted.get(id=task1.id)
    updated_task2 = Task.objects_with_deleted.get(id=task2.id)
    assert updated_task1.is_deleted is True
    assert updated_task2.is_deleted is True


def test_delete_note_with_scheduled_event(test_user: User) -> None:
    note = _create_note(test_user)
    event = ScheduledEvent.objects.create(
        user=test_user,
        note=note,
        start_time=datetime.datetime(2022, 1, 2, 10, 0, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2022, 1, 2, 11, 0, tzinfo=datetime.timezone.utc),
    )
    response = client.delete(f"/api/v2/note/{note.uuid}")

    assert response.status_code == status.HTTP_204_NO_CONTENT

    updated_note = Note.objects_with_deleted.get(uuid=note.uuid)
    assert updated_note.is_deleted
    event.refresh_from_db()
    assert event.removed_by_user
    assert not event.removed_from_provider


def test_delete_note_not_found(test_user: User) -> None:
    # test with non-existent dummy note uuid
    non_existent_uuid = uuid.uuid4()
    response = client.delete(f"/api/v2/note/{non_existent_uuid}")

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Note not found"}


@patch("api.routers.note.Note.objects.select_related")
def test_delete_note_error_deleting(mock_select_related: MagicMock, test_user: User) -> None:
    mock_select_related.side_effect = Exception("Database error")

    note = _create_note(test_user)
    response = client.delete(f"/api/v2/note/{note.uuid}")

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {"detail": "An error occurred while deleting the note"}


def test_tasks_not_deleted_if_note_deletion_fails(test_user: User) -> None:
    note = _create_note(test_user)
    task1 = Task.objects.create(note=note, task_title="Task 1", task_desc="Task 1 description")
    task2 = Task.objects.create(note=note, task_title="Task 2", task_desc="Task 2 description")

    # Mocking Note.save() to raise an exception
    with patch("deepinsights.meetingsapp.models.note.Note.save", side_effect=Exception("Note deletion failed")):
        response = client.delete(f"/api/v2/note/{note.uuid}")

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert response.json() == {"detail": "An error occurred while deleting the note"}

    updated_task1 = Task.objects.get(id=task1.id)
    updated_task2 = Task.objects.get(id=task2.id)
    assert updated_task1.is_deleted is False
    assert updated_task2.is_deleted is False


@patch("django.db.models.query.QuerySet.update", side_effect=Exception("Task deletion failed"))
def test_note_not_deleted_if_task_deletion_fails(mock_task_update: MagicMock, test_user: User) -> None:
    note = _create_note(test_user)
    task1 = Task.objects.create(note=note, task_title="Task 1", task_desc="Task 1 description")
    task2 = Task.objects.create(note=note, task_title="Task 2", task_desc="Task 2 description")

    response = client.delete(f"/api/v2/note/{note.uuid}")

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {"detail": "An error occurred while deleting the note"}

    updated_note = Note.objects.get(uuid=note.uuid)
    assert updated_note.is_deleted is False
    updated_task1 = Task.objects.get(id=task1.id)
    updated_task2 = Task.objects.get(id=task2.id)
    assert updated_task1.is_deleted is False
    assert updated_task2.is_deleted is False

    mock_task_update.assert_called_once()


@patch("api.routers.note.email_meeting_notes")
def test_email_note_success(mock_email_meeting_notes: MagicMock, test_user: User) -> None:
    note = _create_note(test_user)

    mock_email_meeting_notes.return_value = None
    response = client.post(f"/api/v2/note/{note.uuid}/email")

    assert response.status_code == status.HTTP_204_NO_CONTENT

    mock_email_meeting_notes.assert_called_once_with(str(note.uuid), test_user)


def test_email_note_not_found(test_user: User) -> None:
    # test with non-existent dummy note uuid
    non_existent_uuid = uuid.uuid4()
    response = client.post(f"/api/v2/note/{non_existent_uuid}/email")

    assert response.status_code == status.HTTP_404_NOT_FOUND


@patch("api.routers.note.Note.objects.defer")
def test_email_note_error_emailing(mock_defer: MagicMock, test_user: User) -> None:
    mock_defer.side_effect = Exception("Database error")
    note = _create_note(test_user)

    response = client.post(f"/api/v2/note/{note.uuid}/email")

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {"detail": "An error occurred while emailing the note"}


@patch("deepinsights.core.ml.emails._get_followup_email")
def test_email_followup_with_mailto_content_success(mock_get_follow_up_email: MagicMock, test_user: User) -> None:
    note = _create_note(test_user)

    mock_get_follow_up_email.return_value = "Email contents"
    response = client.get(f"/api/v2/note/{note.uuid}/email-followup-mailto")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {
        "mailto_link": "mailto:?subject=%5BDRAFT%5D%20Follow-up%20email%20for%20Test%20meeting&body=Email%20contents&content_type=plain"
    }


@patch("deepinsights.core.ml.emails._get_followup_email")
def test_email_followup_with_mailto_content_success_with_template(
    mock_get_follow_up_email: MagicMock, test_user: User
) -> None:
    note = _create_note(test_user)

    mock_get_follow_up_email.return_value = "Email contents"
    uuid_sample = uuid.uuid4()
    response = client.get(f"/api/v2/note/{note.uuid}/email-followup-mailto?template={uuid_sample}")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {
        "mailto_link": "mailto:?subject=%5BDRAFT%5D%20Follow-up%20email%20for%20Test%20meeting&body=Email%20contents&content_type=plain"
    }


@patch("deepinsights.core.ml.emails._get_followup_email")
def test_email_followup_with_mailto_content_success_with_template_non_uuid_string(
    mock_get_follow_up_email: MagicMock, test_user: User
) -> None:
    note = _create_note(test_user)

    mock_get_follow_up_email.return_value = "Email contents"
    uuid_sample = "default"
    response = client.get(f"/api/v2/note/{note.uuid}/email-followup-mailto?template={uuid_sample}")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {
        "mailto_link": "mailto:?subject=%5BDRAFT%5D%20Follow-up%20email%20for%20Test%20meeting&body=Email%20contents&content_type=plain"
    }


def test_email_followup_with_mailto_content_note_not_found(test_user: User) -> None:
    # test with non-existent dummy note uuid
    non_existent_uuid = uuid.uuid4()
    response = client.get(f"/api/v2/note/{non_existent_uuid}/email-followup-mailto")

    assert response.status_code == status.HTTP_404_NOT_FOUND


@patch("api.routers.note.Note.objects.select_related")
def test_email_followup_with_mailto_content_error(mock_select_related: MagicMock, test_user: User) -> None:
    mock_select_related.side_effect = Exception("Database error")
    note = _create_note(test_user)

    response = client.get(f"/api/v2/note/{note.uuid}/email-followup-mailto")

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {"detail": "An error occurred while generating the mailto link"}


@patch("deepinsights.core.ml.emails._get_followup_email")
def test_email_followup_with_mailto_content_with_attendees(
    mock_get_follow_up_email: MagicMock, test_user: User
) -> None:
    note = _create_note(test_user)
    Attendee.objects.create(note=note, attendee_name="Attendee One")
    Attendee.objects.create(note=note, attendee_name="Attendee Two")

    mock_get_follow_up_email.return_value = "Email contents"
    response = client.get(f"/api/v2/note/{note.uuid}/email-followup-mailto")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {
        "mailto_link": "mailto:?subject=%5BDRAFT%5D%20Follow-up%20email%20for%20Test%20meeting&body=Email%20contents&content_type=plain"
    }


class TestCreateOrUpdateWithAndWithoutScheduledPrecreateEnabled:
    @pytest.fixture(
        autouse=True, params=[True, False], ids=["save_scheduled_notes_enabled", "save_scheduled_notes_disabled"]
    )
    def set_scheduled_note_flag_status(self, request: pytest.FixtureRequest) -> Generator[None, Any, None]:
        with patch("api.routers.note.Flags") as mock_flags:
            mock_flags.EnableSaveScheduledNotes.is_active_for_user.return_value = request.param
            yield

    def test_create_or_update_note_unauthenticated(self) -> None:
        response = client.post("/api/v2/note/create_or_update")
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_create_or_update_note_not_found(self, test_user: User) -> None:
        response = client.post(
            "/api/v2/note/create_or_update", data={"note_id": "123e4567-e89b-12d3-a456-************"}
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_create_or_update_note_unauthorized(self, test_user: User, django_user_model: User) -> None:
        user = django_user_model.objects.create(email="<EMAIL>")
        note = Note.objects.create(note_owner=user)
        note.authorized_users.add(user)
        note.save()

        response = client.post("/api/v2/note/create_or_update", data={"note_id": str(note.uuid)})
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert not note.status

    @pytest.mark.parametrize(
        "note_status",
        [
            Note.PROCESSING_STATUS.processed,
            Note.PROCESSING_STATUS.uploaded,
            Note.PROCESSING_STATUS.missing,
            Note.PROCESSING_STATUS.finalized,
        ],
    )
    def test_create_or_update_note_completed_status(self, test_user: User, note_status: str) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)
        note.status = note_status
        note.save()

        response = client.post(
            "/api/v2/note/create_or_update",
            data={
                "note_id": str(note.uuid),
                "meeting_name": "Updated Meeting",
                "note_type": "meeting_recording",
                "meeting_source_id": "0987654321",
            },
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "note_id": str(note.uuid),
            "completed": True,
            "bot_id": None,
        }
        note.refresh_from_db()
        assert not note.metadata
        assert note.note_type != "meeting_recording"

    @pytest.mark.parametrize(
        "note_status",
        [
            None,
            Note.PROCESSING_STATUS.scheduled,
        ],
    )
    def test_create_or_update_note_pending_status(self, test_user: User, note_status: str | None) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)
        note.status = note_status
        note.save()

        response = client.post(
            "/api/v2/note/create_or_update",
            data={
                "note_id": str(note.uuid),
                "meeting_name": "Updated Meeting",
                "note_type": "meeting_recording",
                "meeting_source_id": "0987654321",
            },
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "note_id": str(note.uuid),
            "completed": False,
            "bot_id": None,
        }
        note.refresh_from_db()
        assert (metadata := note.metadata)
        assert metadata.get("meeting_name") == "Updated Meeting"
        assert metadata.get("meeting_source_id") == "0987654321"
        assert note.note_type == "meeting_recording"

    @patch.object(Note, "save_with_audio_data")
    @patch.object(MeetingBot, "save")
    @patch("api.routers.note.process_note_recording")
    @patch("api.routers.note._finish_bot_recording_session")
    @patch("api.routers.note._create_bot_for_note")
    @patch("api.routers.note.timezone")
    def test_create_or_update_note_empty(
        self,
        mock_timezone: MagicMock,
        mock_create_bot: MagicMock,
        mock_finish: MagicMock,
        mock_process: MagicMock,
        mock_meetingbot_save: MagicMock,
        mock_save_note: MagicMock,
        test_user: User,
    ) -> None:
        mock_timezone.now.return_value = datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc)
        response = client.post("/api/v2/note/create_or_update")

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]
        assert not response.json()["bot_id"]

        note = Note.objects.get(uuid=response.json().get("note_id"))
        assert note.created == datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc)
        assert not note.metadata
        assert not note.client
        assert note.note_type == "voice_memo"
        assert list(note.authorized_users.all()) == [test_user]
        assert not note.status

        mock_meetingbot_save.assert_not_called()
        mock_save_note.assert_not_called()
        mock_process.assert_not_called()
        mock_finish.assert_not_called()
        mock_create_bot.assert_not_called()

    @patch.object(Note, "save_with_audio_data")
    @patch.object(MeetingBot, "save")
    @patch("api.routers.note.process_note_recording")
    @patch("api.routers.note._finish_bot_recording_session")
    @patch("api.routers.note._create_bot_for_note")
    @patch("api.routers.note.timezone")
    def test_create_or_update_note_with_start_time(
        self,
        mock_timezone: MagicMock,
        mock_create_bot: MagicMock,
        mock_finish: MagicMock,
        mock_process: MagicMock,
        mock_meetingbot_save: MagicMock,
        mock_save_note: MagicMock,
        test_user: User,
    ) -> None:
        mock_timezone.now.return_value = datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc)
        response = client.post("/api/v2/note/create_or_update", data={"scheduled_start_time": "2022-01-01 00:00:00Z"})

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]
        assert not response.json()["bot_id"]

        note = Note.objects.get(uuid=response.json().get("note_id"))
        assert note.created == datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc)
        assert note.metadata == {"scheduled_at": "2022-01-01T00:00:00+00:00"}
        assert not note.client
        assert note.note_type == "voice_memo"
        assert list(note.authorized_users.all()) == [test_user]
        assert note.status == Note.PROCESSING_STATUS.scheduled
        assert not note.scheduled_event

        mock_meetingbot_save.assert_not_called()
        mock_save_note.assert_not_called()
        mock_process.assert_not_called()
        mock_finish.assert_not_called()
        mock_create_bot.assert_not_called()

    @patch.object(Note, "save_with_audio_data")
    @patch.object(MeetingBot, "save")
    @patch("api.routers.note.process_note_recording")
    @patch("api.routers.note._finish_bot_recording_session")
    @patch("api.routers.note._create_bot_for_note")
    @patch("api.routers.note.timezone")
    def test_create_or_update_note_create_with_start_time_and_end_time(
        self,
        mock_timezone: MagicMock,
        mock_create_bot: MagicMock,
        mock_finish: MagicMock,
        mock_process: MagicMock,
        mock_meetingbot_save: MagicMock,
        mock_save_note: MagicMock,
        test_user: User,
    ) -> None:
        # Create an unrelated event, to ensure that only relevant events are used.
        other_event = ScheduledEvent.objects.create(
            user=test_user,
            start_time=datetime.datetime(2022, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2022, 1, 1, 1, 0, tzinfo=datetime.timezone.utc),
        )

        mock_timezone.now.return_value = datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc)
        response = client.post(
            "/api/v2/note/create_or_update",
            data={"scheduled_start_time": "2022-01-01 00:00:00Z", "scheduled_end_time": "2022-01-01 01:00:00Z"},
        )

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]
        assert not response.json()["bot_id"]

        note = Note.objects.get(uuid=response.json().get("note_id"))
        assert note.created == datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc)
        assert note.metadata == {"scheduled_at": "2022-01-01T00:00:00+00:00"}
        assert not note.client
        assert note.note_type == "voice_memo"
        assert list(note.authorized_users.all()) == [test_user]
        assert note.status == Note.PROCESSING_STATUS.scheduled
        assert note.scheduled_event
        assert note.scheduled_event != other_event
        assert note.scheduled_event.user == test_user
        assert note.scheduled_event.start_time == datetime.datetime(2022, 1, 1, 0, 0, tzinfo=datetime.timezone.utc)
        assert note.scheduled_event.end_time == datetime.datetime(2022, 1, 1, 1, 0, tzinfo=datetime.timezone.utc)

        mock_meetingbot_save.assert_not_called()
        mock_save_note.assert_not_called()
        mock_process.assert_not_called()
        mock_finish.assert_not_called()
        mock_create_bot.assert_not_called()

    @patch.object(Note, "save_with_audio_data")
    @patch.object(MeetingBot, "save")
    @patch("api.routers.note.process_note_recording")
    @patch("api.routers.note._finish_bot_recording_session")
    @patch("api.routers.note._create_bot_for_note")
    @patch("api.routers.note.timezone")
    def test_create_or_update_note_update_with_start_time_and_end_time_updates_times(
        self,
        mock_timezone: MagicMock,
        mock_create_bot: MagicMock,
        mock_finish: MagicMock,
        mock_process: MagicMock,
        mock_meetingbot_save: MagicMock,
        mock_save_note: MagicMock,
        test_user: User,
    ) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)
        note.save()

        event = ScheduledEvent.objects.create(
            user=test_user,
            note=note,
            start_time=datetime.datetime(2022, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2022, 1, 1, 1, 0, tzinfo=datetime.timezone.utc),
        )

        mock_timezone.now.return_value = datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc)
        response = client.post(
            "/api/v2/note/create_or_update",
            data={
                "note_id": str(note.uuid),
                "scheduled_start_time": "2022-01-02 00:00:00Z",
                "scheduled_end_time": "2022-01-02 01:00:00Z",
            },
        )

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]
        assert not response.json()["bot_id"]

        note.refresh_from_db()
        assert note.metadata == {"scheduled_at": "2022-01-02T00:00:00+00:00"}
        assert not note.client
        assert note.note_type == "voice_memo"
        assert list(note.authorized_users.all()) == [test_user]
        assert note.status == Note.PROCESSING_STATUS.scheduled
        assert note.scheduled_event == event
        assert note.scheduled_event.start_time == datetime.datetime(2022, 1, 2, 0, 0, tzinfo=datetime.timezone.utc)
        assert note.scheduled_event.end_time == datetime.datetime(2022, 1, 2, 1, 0, tzinfo=datetime.timezone.utc)

        mock_meetingbot_save.assert_not_called()
        mock_save_note.assert_not_called()
        mock_process.assert_not_called()
        mock_finish.assert_not_called()
        mock_create_bot.assert_not_called()

    @patch.object(Note, "save_with_audio_data")
    @patch.object(MeetingBot, "save")
    @patch("api.routers.note.process_note_recording")
    @patch("api.routers.note._finish_bot_recording_session")
    @patch("api.routers.note._create_bot_for_note")
    @patch("api.routers.note.timezone")
    def test_create_or_update_note_update_with_start_time_and_end_time_does_not_update_times_for_calendar_event(
        self,
        mock_timezone: MagicMock,
        mock_create_bot: MagicMock,
        mock_finish: MagicMock,
        mock_process: MagicMock,
        mock_meetingbot_save: MagicMock,
        mock_save_note: MagicMock,
        test_user: User,
    ) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)
        note.save()

        event = ScheduledEvent.objects.create(
            user=test_user,
            note=note,
            user_specific_source_id="12345",
            start_time=datetime.datetime(2022, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2022, 1, 1, 1, 0, tzinfo=datetime.timezone.utc),
        )

        mock_timezone.now.return_value = datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc)
        response = client.post(
            "/api/v2/note/create_or_update",
            data={
                "note_id": str(note.uuid),
                "scheduled_start_time": "2022-01-02 00:00:00Z",
                "scheduled_end_time": "2022-01-02 01:00:00Z",
            },
        )

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]
        assert not response.json()["bot_id"]

        note.refresh_from_db()
        assert note.metadata == {"scheduled_at": "2022-01-02T00:00:00+00:00"}
        assert not note.client
        assert note.note_type == "voice_memo"
        assert list(note.authorized_users.all()) == [test_user]
        assert note.status == Note.PROCESSING_STATUS.scheduled
        assert note.scheduled_event == event
        assert note.scheduled_event.start_time == datetime.datetime(2022, 1, 1, 0, 0, tzinfo=datetime.timezone.utc)
        assert note.scheduled_event.end_time == datetime.datetime(2022, 1, 1, 1, 0, tzinfo=datetime.timezone.utc)

        mock_meetingbot_save.assert_not_called()
        mock_save_note.assert_not_called()
        mock_process.assert_not_called()
        mock_finish.assert_not_called()
        mock_create_bot.assert_not_called()

    @patch.object(Note, "save_with_audio_data")
    @patch.object(MeetingBot, "save")
    @patch("api.routers.note.process_note_recording")
    @patch("api.routers.note._finish_bot_recording_session")
    @patch("api.routers.note._create_bot_for_note")
    @patch("api.routers.note.timezone")
    def test_create_or_update_note_create_with_start_time_and_end_time_and_scheduled_event(
        self,
        mock_timezone: MagicMock,
        mock_create_bot: MagicMock,
        mock_finish: MagicMock,
        mock_process: MagicMock,
        mock_meetingbot_save: MagicMock,
        mock_save_note: MagicMock,
        test_user: User,
    ) -> None:
        mock_timezone.now.return_value = datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc)
        event = ScheduledEvent.objects.create(
            user=test_user,
            start_time=datetime.datetime(2022, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2022, 1, 1, 1, 0, tzinfo=datetime.timezone.utc),
        )
        other_event = ScheduledEvent.objects.create(
            user=test_user,
            start_time=datetime.datetime(2022, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2022, 1, 1, 1, 0, tzinfo=datetime.timezone.utc),
        )
        response = client.post(
            "/api/v2/note/create_or_update",
            data={
                "scheduled_start_time": "2022-01-02 00:00:00Z",
                "scheduled_end_time": "2022-01-02 01:00:00Z",
                "scheduled_event_uuid": str(event.uuid),
            },
        )

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]
        assert not response.json()["bot_id"]

        note = Note.objects.get(uuid=response.json().get("note_id"))
        assert note.status == Note.PROCESSING_STATUS.scheduled
        assert note.scheduled_event == event
        assert note.scheduled_event.start_time == datetime.datetime(2022, 1, 1, 0, 0, tzinfo=datetime.timezone.utc)
        assert note.scheduled_event.end_time == datetime.datetime(2022, 1, 1, 1, 0, tzinfo=datetime.timezone.utc)

        mock_meetingbot_save.assert_not_called()
        mock_save_note.assert_not_called()
        mock_process.assert_not_called()
        mock_finish.assert_not_called()
        mock_create_bot.assert_not_called()

    @patch.object(Note, "save_with_audio_data")
    @patch.object(MeetingBot, "save")
    @patch("api.routers.note.process_note_recording")
    @patch("api.routers.note._finish_bot_recording_session")
    @patch("api.routers.note._create_bot_for_note")
    @patch("api.routers.note.timezone")
    def test_create_or_update_note_create_with_scheduled_event(
        self,
        mock_timezone: MagicMock,
        mock_create_bot: MagicMock,
        mock_finish: MagicMock,
        mock_process: MagicMock,
        mock_meetingbot_save: MagicMock,
        mock_save_note: MagicMock,
        test_user: User,
    ) -> None:
        mock_timezone.now.return_value = datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc)
        event = ScheduledEvent.objects.create(
            user=test_user,
            start_time=datetime.datetime(2022, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2022, 1, 1, 1, 0, tzinfo=datetime.timezone.utc),
        )
        other_event = ScheduledEvent.objects.create(
            user=test_user,
            start_time=datetime.datetime(2022, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2022, 1, 1, 1, 0, tzinfo=datetime.timezone.utc),
        )
        response = client.post(
            "/api/v2/note/create_or_update",
            data={
                "scheduled_event_uuid": str(event.uuid),
            },
        )

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]
        assert not response.json()["bot_id"]

        note = Note.objects.get(uuid=response.json().get("note_id"))
        assert note.status == Note.PROCESSING_STATUS.scheduled
        assert note.scheduled_event == event
        assert note.scheduled_event.start_time == datetime.datetime(2022, 1, 1, 0, 0, tzinfo=datetime.timezone.utc)
        assert note.scheduled_event.end_time == datetime.datetime(2022, 1, 1, 1, 0, tzinfo=datetime.timezone.utc)

        mock_meetingbot_save.assert_not_called()
        mock_save_note.assert_not_called()
        mock_process.assert_not_called()
        mock_finish.assert_not_called()
        mock_create_bot.assert_not_called()

    @patch.object(Note, "save_with_audio_data")
    @patch.object(MeetingBot, "save")
    @patch("api.routers.note.process_note_recording")
    @patch("api.routers.note._finish_bot_recording_session")
    @patch("api.routers.note._create_bot_for_note")
    @patch("api.routers.note.timezone")
    def test_create_or_update_note_create_replaces_scheduled_event(
        self,
        mock_timezone: MagicMock,
        mock_create_bot: MagicMock,
        mock_finish: MagicMock,
        mock_process: MagicMock,
        mock_meetingbot_save: MagicMock,
        mock_save_note: MagicMock,
        test_user: User,
    ) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)
        note.save()
        previous_event = ScheduledEvent.objects.create(
            user=test_user,
            start_time=datetime.datetime(2022, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2022, 1, 1, 1, 0, tzinfo=datetime.timezone.utc),
            note=note,
        )
        event = ScheduledEvent.objects.create(
            user=test_user,
            start_time=datetime.datetime(2022, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2022, 1, 1, 1, 0, tzinfo=datetime.timezone.utc),
        )

        response = client.post(
            "/api/v2/note/create_or_update",
            data={
                "note_id": str(note.uuid),
                "scheduled_start_time": "2022-01-01 00:00:00Z",
                "scheduled_end_time": "2022-01-01 01:00:00Z",
                "scheduled_event_uuid": str(event.uuid),
            },
        )

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]
        assert not response.json()["bot_id"]

        note = Note.objects.get(uuid=response.json().get("note_id"))
        assert note.status == Note.PROCESSING_STATUS.scheduled
        assert note.scheduled_event == event

        previous_event.refresh_from_db()
        assert not previous_event.note

        mock_meetingbot_save.assert_not_called()
        mock_save_note.assert_not_called()
        mock_process.assert_not_called()
        mock_finish.assert_not_called()
        mock_create_bot.assert_not_called()

    @patch.object(Note, "save_with_audio_data")
    @patch.object(MeetingBot, "save")
    @patch("api.routers.note.process_note_recording")
    @patch("api.routers.note._finish_bot_recording_session")
    @patch("api.routers.note._create_bot_for_note")
    @patch("api.routers.note.timezone")
    def test_create_or_update_note_create_with_scheduled_event_not_found(
        self,
        mock_timezone: MagicMock,
        mock_create_bot: MagicMock,
        mock_finish: MagicMock,
        mock_process: MagicMock,
        mock_meetingbot_save: MagicMock,
        mock_save_note: MagicMock,
        test_user: User,
    ) -> None:
        mock_timezone.now.return_value = datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc)
        response = client.post(
            "/api/v2/note/create_or_update",
            data={
                "scheduled_start_time": "2022-01-01 00:00:00Z",
                "scheduled_end_time": "2022-01-01 01:00:00Z",
                "scheduled_event_uuid": str(uuid.uuid4()),
            },
        )

        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert not Note.objects.exists()

        mock_meetingbot_save.assert_not_called()
        mock_save_note.assert_not_called()
        mock_process.assert_not_called()
        mock_finish.assert_not_called()
        mock_create_bot.assert_not_called()

    @patch.object(Note, "save_with_audio_data")
    @patch.object(MeetingBot, "save")
    @patch("api.routers.note.process_note_recording")
    @patch("api.routers.note._finish_bot_recording_session")
    @patch("api.routers.note._create_bot_for_note")
    @patch("api.routers.note.timezone")
    def test_create_or_update_note_updating_values(
        self,
        mock_timezone: MagicMock,
        mock_create_bot: MagicMock,
        mock_finish: MagicMock,
        mock_process: MagicMock,
        mock_meetingbot_save: MagicMock,
        mock_save_note: MagicMock,
        test_user: User,
    ) -> None:
        created = datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc)
        note = Note.objects.create(
            note_owner=test_user,
            created=created,
            metadata={
                "meeting_name": "Test Meeting",
                "meeting_type": "client",
                "meeting_source_id": "1234567890",
            },
            client={
                "name": "Test Client",
                "email": "<EMAIL>",
            },
        )
        note.authorized_users.add(test_user)
        note.save()

        mock_timezone.now.return_value = datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc)
        attendees = f'[{{"name": "Bob", "type": "unknown", "uuid": "{uuid.uuid4()}"}}]'
        response = client.post(
            "/api/v2/note/create_or_update",
            data={
                "note_id": str(note.uuid),
                "meeting_name": "Updated Meeting",
                "meeting_type": "new",
                "note_type": "meeting_recording",
                "meeting_source_id": "0987654321",
                "scheduled_start_time": "2022-01-01 00:00:00Z",
                "attendees": attendees,
                "client_name": "Updated Test Client",
                "client_id": "1234567890",
            },
        )

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]
        assert not response.json()["bot_id"]

        note = Note.objects.get(uuid=response.json().get("note_id"))
        assert note.created == created
        assert note.metadata == {
            "meeting_name": "Updated Meeting",
            "meeting_type": "new",
            "meeting_source_id": "0987654321",
            "scheduled_at": "2022-01-01T00:00:00+00:00",
        }
        assert note.client == {
            "uuid": "1234567890",
            "name": "Updated Test Client",
        }
        assert not note.meeting_type
        assert note.note_type == "meeting_recording"
        assert note.status == Note.PROCESSING_STATUS.scheduled
        assert list(note.authorized_users.all()) == [test_user]

        assert list(Attendee.objects.all().values("note", "attendee_name")) == [
            {"note": note.pk, "attendee_name": "Bob"}
        ]
        assert not note.scheduled_event

        mock_meetingbot_save.assert_not_called()
        mock_save_note.assert_not_called()
        mock_process.assert_not_called()
        mock_finish.assert_not_called()
        mock_create_bot.assert_not_called()

        assert not ClientInteraction.objects.all()

    @patch.object(Note, "save_with_audio_data")
    @patch.object(MeetingBot, "save")
    @patch("api.routers.note.process_note_recording")
    @patch("api.routers.note._finish_bot_recording_session")
    @patch("api.routers.note._create_bot_for_note")
    @pytest.mark.parametrize(
        "client_name, client_id, is_client_set",
        [
            (None, None, False),
            (None, "1234567890", False),
            ("Test Client", None, False),
            ("Test Client", "1234567890", True),
        ],
    )
    def test_create_or_update_note_client(
        self,
        mock_create_bot: MagicMock,
        mock_finish: MagicMock,
        mock_process: MagicMock,
        mock_meetingbot_save: MagicMock,
        mock_save_note: MagicMock,
        test_user: User,
        client_name: str | None,
        client_id: str | None,
        is_client_set: bool,
    ) -> None:
        created = datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc)
        note = Note.objects.create(
            note_owner=test_user,
            created=created,
        )
        note.authorized_users.add(test_user)
        note.save()

        response = client.post(
            "/api/v2/note/create_or_update",
            data={
                "note_id": str(note.uuid),
                **({"client_name": client_name} if client_name else {}),
                **({"client_id": client_id} if client_id else {}),
            },
        )

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]
        assert not response.json()["bot_id"]

        note = Note.objects.get(uuid=response.json().get("note_id"))
        if is_client_set:
            assert note.client == {
                "uuid": client_id,
                "name": client_name,
            }
        else:
            assert not note.client

        mock_meetingbot_save.assert_not_called()
        mock_save_note.assert_not_called()
        mock_process.assert_not_called()
        mock_finish.assert_not_called()
        mock_create_bot.assert_not_called()

    @patch("api.routers.note.Flags")
    def test_create_or_update_note_update_attendees(
        self, mock_flags: MagicMock, test_user: User, test_org: Organization
    ) -> None:
        test_client = Client.objects.create(name="Test Client", organization=test_org)
        test_client.authorized_users.add(test_user)
        test_client.save()

        second_user = User.objects.create(username="<EMAIL>", organization=test_org)
        third_user = User.objects.create(username="<EMAIL>", organization=test_org)

        first_attendees = [
            {"name": "Bob", "type": "unknown", "uuid": str(uuid.uuid4())},
            {"name": "Test Client", "type": "client", "uuid": str(test_client.uuid)},
            {"name": "Second User", "type": "user", "uuid": str(second_user.uuid)},
        ]

        response = client.post("/api/v2/note/create_or_update", data={"attendees": json.dumps(first_attendees)})

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]

        note = Note.objects.get(uuid=response.json().get("note_id"))

        assert list(Attendee.objects.all().values("note", "attendee_name", "client", "user")) == [
            {"note": note.pk, "attendee_name": "Bob", "client": None, "user": None},
            {"note": note.pk, "attendee_name": "Test Client", "client": test_client.pk, "user": None},
            {"note": note.pk, "attendee_name": "Second User", "client": None, "user": second_user.pk},
        ]
        assert set(note.authorized_users.all()) == set([test_user, second_user])

        # Remove and add some attendees.
        second_attendees = [
            first_attendees[2],
            {"name": "Joe", "type": "unknown", "uuid": str(uuid.uuid4())},
            {"name": "Third User", "type": "user", "uuid": str(third_user.uuid)},
        ]

        response = client.post(
            "/api/v2/note/create_or_update",
            data={"note_id": str(note.uuid), "attendees": json.dumps(second_attendees)},
        )

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]

        note = Note.objects.get(uuid=response.json().get("note_id"))

        assert list(Attendee.objects.all().values("note", "attendee_name", "client", "user")) == [
            {"note": note.pk, "attendee_name": "Second User", "client": None, "user": second_user.pk},
            {"note": note.pk, "attendee_name": "Joe", "client": None, "user": None},
            {"note": note.pk, "attendee_name": "Third User", "client": None, "user": third_user.pk},
        ]
        assert set(note.authorized_users.all()) == set([test_user, second_user, third_user])

        # Go back to the first list of attendees.
        response = client.post(
            "/api/v2/note/create_or_update",
            data={"note_id": str(note.uuid), "attendees": json.dumps(first_attendees)},
        )

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]

        note = Note.objects.get(uuid=response.json().get("note_id"))

        assert list(Attendee.objects.all().values("note", "attendee_name", "client", "user")) == [
            {"note": note.pk, "attendee_name": "Bob", "client": None, "user": None},
            {"note": note.pk, "attendee_name": "Test Client", "client": test_client.pk, "user": None},
            {"note": note.pk, "attendee_name": "Second User", "client": None, "user": second_user.pk},
        ]
        assert set(note.authorized_users.all()) == set([test_user, second_user, third_user])

        # Remove all attendees.
        response = client.post(
            "/api/v2/note/create_or_update",
            data={"note_id": str(note.uuid), "attendees": "[]"},
        )

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]

        note = Note.objects.get(uuid=response.json().get("note_id"))

        assert not Attendee.objects.count()
        assert set(note.authorized_users.all()) == set([test_user, second_user, third_user])

        assert not ClientInteraction.objects.all()

    @patch.object(Note, "save_with_audio_data")
    @patch.object(MeetingBot, "save")
    @patch("api.routers.note.process_note_recording")
    @patch("api.routers.note._finish_bot_recording_session")
    @patch("api.routers.note._create_bot_for_note")
    @patch("api.routers.note.timezone")
    def test_create_or_update_note_update_with_start_time_and_end_time(
        self,
        mock_timezone: MagicMock,
        mock_create_bot: MagicMock,
        mock_finish: MagicMock,
        mock_process: MagicMock,
        mock_meetingbot_save: MagicMock,
        mock_save_note: MagicMock,
        test_user: User,
    ) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)
        note.save()

        ScheduledEvent.objects.create(
            user=test_user,
            note=note,
            start_time=datetime.datetime(2022, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2022, 1, 1, 1, 0, tzinfo=datetime.timezone.utc),
        )

        mock_timezone.now.return_value = datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc)
        response = client.post(
            "/api/v2/note/create_or_update",
            data={
                "note_id": str(note.uuid),
                "scheduled_start_time": "2022-01-02 00:00:00Z",
                "scheduled_end_time": "2022-01-02 01:00:00Z",
            },
        )

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]
        assert not response.json()["bot_id"]

        note = Note.objects.get(uuid=response.json().get("note_id"))
        assert note.metadata == {"scheduled_at": "2022-01-02T00:00:00+00:00"}
        assert not note.client
        assert note.note_type == "voice_memo"
        assert list(note.authorized_users.all()) == [test_user]
        assert note.status == Note.PROCESSING_STATUS.scheduled
        assert note.scheduled_event
        assert note.scheduled_event.start_time == datetime.datetime(2022, 1, 2, 0, 0, tzinfo=datetime.timezone.utc)
        assert note.scheduled_event.end_time == datetime.datetime(2022, 1, 2, 1, 0, tzinfo=datetime.timezone.utc)

    @override_settings(AWS_S3_FILE_FOLDER="test_folder")
    @patch("api.routers.note.process_note_recording")
    @patch("deepinsights.meetingsapp.models.note.AWS")
    @patch("api.routers.note.timezone")
    def test_create_or_update_note_with_audio_data(
        self,
        mock_timezone: MagicMock,
        mock_aws: MagicMock,
        mock_process: MagicMock,
        test_user: User,
    ) -> None:
        mock_timezone.now.return_value = datetime.datetime(2022, 1, 1, tzinfo=datetime.timezone.utc)

        def check_audio_data(_: str, content: BinaryIO) -> None:
            assert content.read() == b"audio data"

        mock_aws.return_value.upload_file.side_effect = check_audio_data

        # The containerized environment doesn't have a default mimetype for webm audio files. Add one so
        # we can test the functionality in this test.
        mimetypes.add_type("audio/webm", ".weba")

        response = client.post(
            "/api/v2/note/create_or_update",
            data={
                "meeting_name": "Test Meeting",
                "meeting_type": "client",
            },
            files={"audio_data": ("test.webm", b"audio data", "audio/webm")},
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.json()["completed"]

        note = Note.objects.get(uuid=response.json().get("note_id"))
        assert note.note_type == "voice_memo"
        key = f"{test_user.uuid}/voice_memo/2022-01-01/{note.uuid}.weba"
        assert note.file_path == f"test_folder/{key}"
        assert note.status == Note.PROCESSING_STATUS.uploaded
        mock_aws.return_value.upload_file.assert_called_once_with(note.file_path, ANY)
        mock_process.delay_on_commit.assert_called_once_with(test_user.uuid, note.uuid)

    @patch.object(Note, "save_with_audio_data")
    @patch("api.routers.note.process_note_recording")
    def test_create_or_update_note_with_audio_data_type(
        self, mock_process: MagicMock, mock_save_note: MagicMock, test_user: User
    ) -> None:
        response = client.post(
            "/api/v2/note/create_or_update",
            data={
                "meeting_name": "Test Meeting",
                "meeting_type": "client",
                "file_type": "foo",
            },
            files={"audio_data": ("test.webm", b"audio data", "audio/webm")},
        )

        assert response.status_code == status.HTTP_200_OK
        mock_save_note.assert_called_once_with(ANY, ANY, ANY, "foo", ANY)
        mock_process.delay_on_commit.assert_called_once_with(test_user.uuid, uuid.UUID(response.json().get("note_id")))

    @patch.object(Note, "save_with_audio_data")
    @patch("api.routers.note.process_note_recording")
    def test_create_or_update_note_with_audio_file_type_default(
        self, mock_process: MagicMock, mock_save_note: MagicMock, test_user: User
    ) -> None:
        response = client.post(
            "/api/v2/note/create_or_update",
            files={"audio_data": ("test", "audio data", "")},
        )

        assert response.status_code == status.HTTP_200_OK
        Note.objects.get(uuid=response.json().get("note_id"))
        mock_save_note.assert_called_once_with(ANY, ANY, ANY, "webm", ANY)
        mock_process.delay_on_commit.assert_called_once()

    @patch.object(Note, "save_with_audio_data")
    @patch("api.routers.note.process_note_recording")
    def test_create_or_update_note_with_audio_file_type_filename(
        self, mock_process: MagicMock, mock_save_note: MagicMock, test_user: User
    ) -> None:
        response = client.post(
            "/api/v2/note/create_or_update",
            files={"audio_data": ("test.audio", "audio data", "")},
        )

        assert response.status_code == status.HTTP_200_OK
        Note.objects.get(uuid=response.json().get("note_id"))
        mock_save_note.assert_called_once_with(ANY, ANY, ANY, "audio", ANY)
        mock_process.delay_on_commit.assert_called_once()

    @patch.object(Note, "save_with_audio_data")
    def test_create_or_update_note_with_audio_data_failure(self, mock_save_note: MagicMock, test_user: User) -> None:
        mock_save_note.side_effect = Exception("Error saving audio data")
        response = client.post(
            "/api/v2/note/create_or_update",
            data={
                "meeting_name": "Test Meeting",
                "meeting_type": "client",
            },
            files={"audio_data": ("test.webm", b"audio data", "audio/webm")},
        )

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert response.json() == {"detail": "Error saving uploaded audio data."}

    @patch("api.routers.note.process_note_recording")
    @patch("deepinsights.meetingsapp.models.note.AWS")
    @patch("api.routers.note._finish_bot_recording_session")
    @patch("api.routers.note._create_bot_for_note")
    def test_create_or_update_note_with_audio_data_takes_precedence_over_bot(
        self,
        mock_create_bot: MagicMock,
        mock_finish: MagicMock,
        mock_aws: MagicMock,
        mock_process: MagicMock,
        test_user: User,
    ) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)
        note.save()
        bot = MeetingBot.objects.create(note=note, bot_owner=test_user)

        response = client.post(
            "/api/v2/note/create_or_update",
            data={"note_id": str(note.uuid), "meeting_link": "https://example.com/meeting"},
            files={"audio_data": ("test.wav", b"audio data", "audio/wav")},
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.json()["completed"]

        note.refresh_from_db()
        bot.refresh_from_db()
        assert not bot.note
        assert not note.meetingbot_set.count()
        assert note.status == Note.PROCESSING_STATUS.uploaded

        mock_finish.assert_not_called()
        mock_create_bot.assert_not_called()
        mock_process.delay_on_commit.assert_called_once_with(test_user.uuid, note.uuid)


class TestCreateOrUpdateNoteBotHandlingWithScheduledPrecreateDisabled:
    @pytest.fixture(autouse=True)
    def set_scheduled_note_flag_disabled(self) -> Generator[None, Any, None]:
        with patch("api.routers.note.Flags") as mock_flags:
            mock_flags.EnableSaveScheduledNotes.is_active_for_user.return_value = False
            yield

    def test_create_or_update_note_with_existing_failed_bot(self, test_user: User) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)
        note.save()
        bot = MeetingBot.objects.create(
            note=note,
            bot_owner=test_user,
            meeting_link="https://example.com/meeting",
        )
        response = client.post(
            "/api/v2/note/create_or_update",
            data={"note_id": str(note.uuid)},
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {"note_id": str(note.uuid), "completed": False, "bot_id": None}

        bot.refresh_from_db()
        assert not bot.note

        note.refresh_from_db()
        assert not note.status

    @patch("api.routers.note.MeetingBot")
    def test_create_or_update_note_with_existing_bot(self, mock_meeting_bot: MagicMock, test_user: User) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)
        note.save()
        bot = MeetingBot.objects.create(
            note=note,
            bot_owner=test_user,
            meeting_link="https://example.com/meeting",
        )

        mock_meeting_bot.objects.get.return_value.get_status.return_value = BotStatus.IN_CALL_RECORDING

        response = client.post(
            "/api/v2/note/create_or_update",
            data={"note_id": str(note.uuid), "meeting_link": "https://example.com/meeting2"},
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {"note_id": str(note.uuid), "completed": True, "bot_id": None}
        mock_meeting_bot.objects.get.return_value.leave_meeting.assert_called_once()

        assert MeetingBot.objects.count() == 1

        note.refresh_from_db()
        assert note.status == Note.PROCESSING_STATUS.uploaded

        bot.refresh_from_db()
        assert bot.meeting_link == "https://example.com/meeting"

    @patch.object(MeetingBot, "create_bot_and_start_recording")
    def test_create_or_update_note_with_meeting_link(self, mock_create_bot: MagicMock, test_user: User) -> None:
        response = client.post(
            "/api/v2/note/create_or_update",
            data={"meeting_link": "https://example.com/meeting"},
        )

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]

        note = Note.objects.get(uuid=response.json().get("note_id"))
        assert note.meetingbot_set.count() == 1
        assert note.status == Note.PROCESSING_STATUS.scheduled

        bot = note.meetingbot_set.first()
        assert bot
        assert bot.meeting_link == "https://example.com/meeting"
        assert bot.bot_owner == test_user
        mock_create_bot.assert_called_once()

    def test_create_or_update_note_with_meeting_link_updates_file_parameters(self, test_user: User) -> None:
        note = Note.objects.create(note_owner=test_user, file_path="test_folder/test.wav", file_type="wav")
        response = client.post(
            "/api/v2/note/create_or_update",
            data={"note_id": str(note.uuid), "meeting_link": "https://example.com/meeting"},
        )

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]

        note.refresh_from_db()
        assert not note.file_path
        assert not note.file_type

    @patch("api.routers.note._create_bot_for_note")
    def test_create_or_update_note_with_meeting_link_error(self, mock_create_bot: MagicMock, test_user: User) -> None:
        mock_create_bot.side_effect = Exception("Error creating bot")
        response = client.post(
            "/api/v2/note/create_or_update",
            data={"meeting_link": "https://example.com/meeting"},
        )

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert response.json() == {"detail": "Error creating note with meeting bot."}
        mock_create_bot.assert_called_once()


class TestCreateOrUpdateNoteBotHandlingWithScheduledPrecreateEnabled:
    @pytest.fixture(autouse=True)
    def set_scheduled_note_flag_enabled(self) -> Generator[None, Any, None]:
        with patch("api.routers.note.Flags") as mock_flags:
            mock_flags.EnableSaveScheduledNotes.is_active_for_user.return_value = True
            yield

    def test_create_or_update_note_with_existing_uncreated_bot_no_link_no_bot_id(self, test_user: User) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)
        note.save()
        bot = MeetingBot.objects.create(
            note=note,
            bot_owner=test_user,
            meeting_link="https://example.com/meeting",
        )
        response = client.post(
            "/api/v2/note/create_or_update",
            data={"note_id": str(note.uuid)},
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {"note_id": str(note.uuid), "completed": False, "bot_id": str(bot.uuid)}

        bot.refresh_from_db()
        assert bot.note == note
        assert bot.meeting_link == "https://example.com/meeting"
        assert MeetingBot.objects.count() == 1

        note.refresh_from_db()
        assert not note.status

    def test_create_or_update_note_with_existing_uncreated_bot_no_link_no_bot_id_with_start_time(
        self, test_user: User
    ) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)
        note.save()
        bot = MeetingBot.objects.create(
            note=note,
            bot_owner=test_user,
            meeting_link="https://example.com/meeting",
        )
        response = client.post(
            "/api/v2/note/create_or_update",
            data={"note_id": str(note.uuid), "scheduled_start_time": "2022-01-01 00:00:00Z"},
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {"note_id": str(note.uuid), "completed": False, "bot_id": str(bot.uuid)}

        bot.refresh_from_db()
        assert bot.note == note
        assert bot.meeting_link == "https://example.com/meeting"
        assert MeetingBot.objects.count() == 1

        note.refresh_from_db()
        assert note.status == Note.PROCESSING_STATUS.scheduled

    def test_create_or_update_note_with_existing_uncreated_bot_same_link_no_bot_id(self, test_user: User) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)
        note.save()
        bot = MeetingBot.objects.create(
            note=note,
            bot_owner=test_user,
            meeting_link="https://example.com/meeting",
        )
        response = client.post(
            "/api/v2/note/create_or_update",
            data={"note_id": str(note.uuid), "meeting_link": "https://example.com/meeting"},
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {"note_id": str(note.uuid), "completed": False, "bot_id": str(bot.uuid)}

        bot.refresh_from_db()
        assert bot.note == note
        assert bot.meeting_link == "https://example.com/meeting"
        assert MeetingBot.objects.count() == 1

        note.refresh_from_db()
        assert note.status == Note.PROCESSING_STATUS.scheduled

    @pytest.mark.parametrize(
        "bot_status", [BotStatus.NOT_CREATED, BotStatus.IN_CALL_RECORDING, BotStatus.CALL_ENDED, BotStatus.ERROR]
    )
    @patch.object(MeetingBot, "get_status")
    def test_create_or_update_note_with_existing_uncreated_bot_not_created_same_link(
        self, mock_get_status: MagicMock, bot_status: BotStatus, test_user: User
    ) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)
        note.save()
        bot = MeetingBot.objects.create(
            note=note,
            recall_bot_id="recall_bot_id",
            bot_owner=test_user,
            meeting_link="https://example.com/meeting",
        )

        mock_get_status.return_value = bot_status

        response = client.post(
            "/api/v2/note/create_or_update",
            data={"note_id": str(note.uuid), "meeting_link": "https://example.com/meeting"},
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {"note_id": str(note.uuid), "completed": False, "bot_id": str(bot.uuid)}

        bot.refresh_from_db()
        assert bot.note == note
        assert bot.meeting_link == "https://example.com/meeting"
        assert MeetingBot.objects.count() == 1

        note.refresh_from_db()
        assert note.status == Note.PROCESSING_STATUS.scheduled

    def test_create_or_update_note_with_existing_uncreated_bot_with_different_meeting_link(
        self, test_user: User
    ) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)
        note.save()
        bot = MeetingBot.objects.create(
            note=note,
            bot_owner=test_user,
            meeting_link="https://example.com/meeting",
        )
        response = client.post(
            "/api/v2/note/create_or_update",
            data={"note_id": str(note.uuid), "meeting_link": "https://example.com/meeting2"},
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {"note_id": str(note.uuid), "completed": False, "bot_id": str(bot.uuid)}

        bot.refresh_from_db()
        assert bot.note == note
        assert bot.meeting_link == "https://example.com/meeting2"
        assert MeetingBot.objects.count() == 1

        note.refresh_from_db()
        assert note.status == Note.PROCESSING_STATUS.scheduled

    @pytest.mark.parametrize("bot_status", [BotStatus.IN_CALL_RECORDING, BotStatus.CALL_ENDED, BotStatus.ERROR])
    @patch.object(MeetingBot, "get_status")
    def test_create_or_update_note_with_existing_created_bot_with_different_meeting_link(
        self, mock_get_status: MagicMock, bot_status: bool, test_user: User
    ) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)
        note.save()
        bot = MeetingBot.objects.create(
            note=note,
            recall_bot_id="recall_bot_id",
            bot_owner=test_user,
            meeting_link="https://example.com/meeting",
        )
        mock_get_status.return_value = bot_status

        response = client.post(
            "/api/v2/note/create_or_update",
            data={"note_id": str(note.uuid), "meeting_link": "https://example.com/meeting2"},
        )

        assert response.status_code == status.HTTP_200_OK

        bot.refresh_from_db()
        assert not bot.note
        assert bot.meeting_link == "https://example.com/meeting"
        assert MeetingBot.objects.count() == 2

        note.refresh_from_db()
        assert note.status == Note.PROCESSING_STATUS.scheduled
        assert note.meetingbot_set.count() == 1
        new_bot = note.meetingbot_set.all().first()
        assert new_bot is not None
        assert new_bot.meeting_link == "https://example.com/meeting2"

        assert response.json() == {"note_id": str(note.uuid), "completed": False, "bot_id": str(new_bot.uuid)}

    def test_create_or_update_note_with_meeting_link_updates_file_parameters(self, test_user: User) -> None:
        note = Note.objects.create(note_owner=test_user, file_path="test_folder/test.wav", file_type="wav")
        response = client.post(
            "/api/v2/note/create_or_update",
            data={"note_id": str(note.uuid), "meeting_link": "https://example.com/meeting"},
        )

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]

        note.refresh_from_db()
        assert not note.file_path
        assert not note.file_type


class TestCreateOrUpdateWithClientInteractions:
    @pytest.fixture(autouse=True)
    def set_client_intelligence_flag_enabled(self) -> Generator[None, Any, None]:
        with patch("api.routers.note.Flags") as mock_flags, patch(
            "api.routers.note.generate_agenda.delay"
        ) as mock_generate_agenda:
            mock_flags.EnableClientIntelligencePreMeetingWorkflow.is_active_for_user.return_value = True
            mock_generate_agenda.return_value = None
            yield

    def test_create_or_update_note_no_interaction_without_meeting_type(self, test_user: User) -> None:
        response = client.post("/api/v2/note/create_or_update")

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]
        assert not response.json()["bot_id"]

        note = Note.objects.get(uuid=response.json().get("note_id"))

        assert not note.has_client_interaction()

    def test_create_or_update_note_interaction_with_meeting_type(self, test_user: User) -> None:
        meeting_type = MeetingType.objects.create(name="Client", category=MeetingType.Category.CLIENT)
        response = client.post(
            "/api/v2/note/create_or_update",
            data={"meeting_type": str(meeting_type.uuid)},
        )

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]

        note = Note.objects.get(uuid=response.json().get("note_id"))
        assert note.has_client_interaction()
        interaction = note.clientinteraction

        assert interaction.meeting_type == meeting_type
        assert interaction.clients.count() == 0

    def test_create_or_update_note_interaction_existing_interaction(self, test_user: User) -> None:
        meeting_type = MeetingType.objects.create(name="Client", category=MeetingType.Category.CLIENT)
        note = Note.objects.create(
            note_owner=test_user,
            metadata={
                "meeting_name": "Test Meeting",
                "meeting_type": "client",
                "meeting_source_id": "1234567890",
            },
        )
        note.authorized_users.add(test_user)
        note.save()
        interaction = ClientInteraction.objects.create(note=note, meeting_type=meeting_type)

        response = client.post(
            "/api/v2/note/create_or_update",
            data={
                "note_id": str(note.uuid),
                "meeting_name": "Updated Meeting",
            },
        )

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]

        note = Note.objects.get(uuid=response.json().get("note_id"))
        assert note.has_client_interaction()
        assert note.clientinteraction == interaction
        assert ClientInteraction.objects.count() == 1

    def test_create_or_update_note_interaction_existing_interaction_updates_clients(self, test_user: User) -> None:
        # Create a note, which will create an interaction.
        meeting_type = MeetingType.objects.create(name="Client", category=MeetingType.Category.CLIENT)
        org = Organization.objects.create(name="Test org")
        test_client = Client.objects.create(name="Test Client", organization=org)
        test_client.authorized_users.add(test_user)
        test_client.save()
        first_attendees = [
            {"name": "Bob", "type": "unknown", "uuid": str(uuid.uuid4())},
            {"name": "Test Client", "type": "client", "uuid": str(test_client.uuid)},
            {"name": "Test User", "type": "user", "uuid": str(test_user.uuid)},
        ]
        response = client.post(
            "/api/v2/note/create_or_update",
            data={"attendees": json.dumps(first_attendees), "meeting_type": str(meeting_type.uuid)},
        )

        assert response.status_code == status.HTTP_200_OK

        note = Note.objects.get(uuid=response.json().get("note_id"))
        assert note.has_client_interaction()
        interaction = note.clientinteraction
        assert set(interaction.clients.all()) == set([test_client])

        # Update the attendees.
        test_client_two = Client.objects.create(name="Test Client Two", organization=org)
        test_client_two.authorized_users.add(test_user)
        test_client_two.save()

        second_attendees = [
            {"name": "Test Client", "type": "client", "uuid": str(test_client.uuid)},
            {"name": "Test Client Two", "type": "client", "uuid": str(test_client_two.uuid)},
        ]
        response = client.post(
            "/api/v2/note/create_or_update",
            data={
                "note_id": str(note.uuid),
                "attendees": json.dumps(second_attendees),
            },
        )

        assert response.status_code == status.HTTP_200_OK
        assert not response.json()["completed"]

        note.refresh_from_db()
        assert note.has_client_interaction()
        assert note.clientinteraction == interaction

        interaction.refresh_from_db()
        assert interaction.meeting_type == meeting_type
        assert set(interaction.clients.all()) == set([test_client, test_client_two])


def test_edit_note_success(test_user: User, test_org: Organization) -> None:
    test_client = Client.objects.create(name="Test Client", organization=test_org)
    note = _create_note(test_user)
    edit_request = {
        "meetingName": "Updated Meeting",
        "advisorNotes": ["Note 1", "Note 2"],
        "keyTakeaways": ["Key point 1", "Key point 2"],
        "client": {"uuid": "client123", "name": "Test Client", "email": "<EMAIL>"},
        "summary": {
            "sections": [{"topic": "Test Section", "bullets": ["Test bullet"]}],
        },
        "attendees": [
            {"uuid": str(test_user.uuid), "name": "User", "type": "user"},
            {"uuid": str(test_client.uuid), "name": "Client", "type": "client"},
            {
                "uuid": str(uuid.uuid4()),
                "name": "Unknown",
                "type": "unknown",
            },
        ],
    }

    response = client.post(f"/api/v2/note/{note.uuid}", json=edit_request)

    assert response.status_code == status.HTTP_204_NO_CONTENT

    updated_note = Note.objects.get(uuid=note.uuid)
    assert updated_note.metadata
    assert updated_note.metadata["meeting_name"] == "Updated Meeting"
    assert updated_note.advisor_notes == ["Note 1", "Note 2"]
    assert updated_note.key_takeaways == ["Key point 1", "Key point 2"]
    assert updated_note.client == {"uuid": "client123", "name": "Test Client", "email": "<EMAIL>"}
    assert updated_note.summary == {"sections": [{"topic": "Test Section", "bullets": ["Test bullet"]}]}
    assert list(note.attendees.all().order_by("attendee_name").values("attendee_name", "client", "user")) == [
        {"attendee_name": "Client", "client": test_client.id, "user": None},
        {"attendee_name": "Unknown", "client": None, "user": None},
        {"attendee_name": "User", "client": None, "user": test_user.id},
    ]


def test_edit_note_no_changes(test_user: User, test_org: Organization) -> None:
    test_client = Client.objects.create(name="Test Client", organization=test_org)
    note = _create_note(test_user)
    note.advisor_notes = ["Note 1", "Note 2"]
    note.key_takeaways = ["Key point 1", "Key point 2"]
    note.client = {"uuid": "client123", "name": "Test Client", "email": "<EMAIL>"}
    note.summary = {
        "sections": [{"topic": "Test Section", "bullets": ["Test bullet"]}],
    }
    attendees = [
        Attendee.objects.create(note=note, attendee_name="Client", client=test_client),
        Attendee.objects.create(note=note, attendee_name="User", user=test_user),
    ]
    note.save()

    edit_request = {
        "meetingName": None,
        "advisorNotes": None,
        "keyTakeaways": None,
        "client": None,
        "summary": None,
        "attendees": None,
    }

    response = client.post(f"/api/v2/note/{note.uuid}", json=edit_request)

    assert response.status_code == status.HTTP_204_NO_CONTENT

    updated_note = Note.objects.get(uuid=note.uuid)
    assert updated_note.metadata
    assert updated_note.metadata["meeting_name"] == "Test meeting"
    assert updated_note.advisor_notes == ["Note 1", "Note 2"]
    assert updated_note.key_takeaways == ["Key point 1", "Key point 2"]
    assert updated_note.client == {"uuid": "client123", "name": "Test Client", "email": "<EMAIL>"}
    assert updated_note.summary == {"sections": [{"topic": "Test Section", "bullets": ["Test bullet"]}]}
    assert list(note.attendees.all().order_by("attendee_name").values("attendee_name", "client", "user")) == [
        {
            "attendee_name": a.attendee_name,
            "client": a.client.id if a.client else None,
            "user": a.user.id if a.user else None,
        }
        for a in attendees
    ]


def test_edit_note_updates_to_empty(test_user: User, test_org: Organization) -> None:
    test_client = Client.objects.create(name="Test Client", organization=test_org)
    note = _create_note(test_user)
    note.advisor_notes = ["Note 1", "Note 2"]
    note.key_takeaways = ["Key point 1", "Key point 2"]
    note.client = {"uuid": "client123", "name": "Test Client", "email": "<EMAIL>"}
    note.summary = {
        "sections": [{"topic": "Test Section", "bullets": ["Test bullet"]}],
    }
    (Attendee.objects.create(note=note, attendee_name="Client", client=test_client),)
    (Attendee.objects.create(note=note, attendee_name="User", user=test_user),)
    note.save()

    edit_request: dict[str, Any] = {
        "meetingName": "",
        "advisorNotes": [],
        "keyTakeaways": [],
        "client": None,  # It's not possible to pass an empty client object
        "summary": {},
        "attendees": [],
    }

    response = client.post(f"/api/v2/note/{note.uuid}", json=edit_request)

    assert response.status_code == status.HTTP_204_NO_CONTENT

    updated_note = Note.objects.get(uuid=note.uuid)
    assert updated_note.metadata
    assert not updated_note.metadata["meeting_name"]
    assert not updated_note.advisor_notes
    assert not updated_note.key_takeaways
    assert updated_note.summary == {"sections": []}
    assert not note.attendees.exists()


def test_edit_note_partial_update(test_user: User) -> None:
    note = _create_note(test_user)

    edit_request = {"meetingName": "Updated Meeting", "keyTakeaways": ["New key point"]}

    response = client.post(f"/api/v2/note/{note.uuid}", json=edit_request)

    assert response.status_code == status.HTTP_204_NO_CONTENT

    updated_note = Note.objects.get(uuid=note.uuid)
    assert updated_note.metadata
    assert updated_note.metadata["meeting_name"] == "Updated Meeting"
    assert updated_note.key_takeaways == ["New key point"]
    # Verify other fields remain unchanged
    assert updated_note.advisor_notes == note.advisor_notes
    assert updated_note.client == note.client
    assert updated_note.summary == note.summary


def test_edit_note_not_found(test_user: User) -> None:
    non_existent_uuid = uuid.uuid4()
    edit_request = {"meetingName": "Updated Meeting"}

    response = client.post(f"/api/v2/note/{non_existent_uuid}", json=edit_request)

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Note not found"}


def test_edit_note_unauthorized(django_user_model: User) -> None:
    other_user = django_user_model.objects.create(username="<EMAIL>")
    note = _create_note(other_user)

    edit_request = {"meetingName": "Updated Meeting"}
    response = client.post(f"/api/v2/note/{note.uuid}", json=edit_request)

    assert response.status_code == status.HTTP_403_FORBIDDEN


def test_edit_finalized_note(test_user: User) -> None:
    note = _create_note(test_user)
    note.status = Note.PROCESSING_STATUS.finalized
    note.save()

    edit_request = {"meetingName": "Updated Meeting"}
    response = client.post(f"/api/v2/note/{note.uuid}", json=edit_request)

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {"detail": "Cannot edit a finalized note"}


def test_edit_note_empty_attendees(test_user: User, test_org: Organization) -> None:
    test_client = Client.objects.create(name="Test Client", organization=test_org)
    note = _create_note(test_user)
    Attendee.objects.create(note=note, attendee_name="Client", client=test_client)
    Attendee.objects.create(note=note, attendee_name="User", user=test_user)
    edit_request = {"attendees": []}  # type: ignore[var-annotated]

    response = client.post(f"/api/v2/note/{note.uuid}", json=edit_request)

    assert response.status_code == status.HTTP_204_NO_CONTENT

    updated_note = Note.objects.get(uuid=note.uuid)
    assert not updated_note.attendees.exists()


@pytest.mark.parametrize(
    "action,task_status",
    [
        ("create", "incomplete"),
        ("create", "complete"),
        ("keep", "incomplete"),
        ("keep", "complete"),
        ("delete", None),
    ],
)
def test_process_action_items(test_user: User, action: str, task_status: str | None) -> None:
    note = _create_note(test_user)
    action_item_uuid = uuid.uuid4()

    edit_request = {
        "actionItems": [
            {
                "item": {
                    "content": "Test action item",
                    "uuid": str(action_item_uuid),
                    "status": task_status if task_status else "incomplete",
                },
                "action": action,
            }
        ]
    }

    response = client.post(f"/api/v2/note/{note.uuid}", json=edit_request)

    assert response.status_code == status.HTTP_204_NO_CONTENT

    if action == "delete":
        assert not Task.objects.filter(uuid=action_item_uuid).exists()
    else:
        task = Task.objects.get(uuid=action_item_uuid)
        assert task.task_title == "Test action item"
        assert task.completed == (task_status == "complete")
        assert task.note == note
        assert task.task_owner == test_user


def test_multiple_action_items(test_user: User) -> None:
    note = _create_note(test_user)

    existing_task_1 = Task.objects.create(
        uuid=uuid.uuid4(), note=note, task_owner=test_user, task_title="Existing task 1"
    )
    existing_task_2 = Task.objects.create(
        uuid=uuid.uuid4(), note=note, task_owner=test_user, task_title="Existing task 2"
    )

    new_task_uuid = uuid.uuid4()
    edit_request = {
        "actionItems": [
            {
                "item": {"content": "Existing task updated", "uuid": str(existing_task_1.uuid), "status": "complete"},
                "action": "keep",
            },
            {"item": {"content": "New task", "uuid": str(new_task_uuid), "status": "incomplete"}, "action": "create"},
            {
                "item": {"content": "To be deleted", "uuid": str(existing_task_2.uuid), "status": "incomplete"},
                "action": "delete",
            },
        ]
    }

    response = client.post(f"/api/v2/note/{note.uuid}", json=edit_request)

    assert response.status_code == status.HTTP_204_NO_CONTENT

    tasks = Task.objects.filter(note=note)
    assert len(tasks) == 2

    updated_task = Task.objects.get(uuid=existing_task_1.uuid)
    assert updated_task.task_title == "Existing task updated"
    assert updated_task.completed is True

    new_task = Task.objects.get(uuid=new_task_uuid)
    assert new_task.task_title == "New task"
    assert new_task.completed is False

    assert not Task.objects.filter(uuid=existing_task_2.uuid).exists()


def test_create_or_update_interaction_no_meeting_type(test_user: User, test_org: Organization) -> None:
    note = _create_note(test_user)

    create_or_update_interaction(note, test_user)

    assert not ClientInteraction.objects.exists()


def test_create_or_update_interaction_new(test_user: User, test_org: Organization) -> None:
    """Test creating a new interaction when none exists"""
    # Set up
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", key="test_meeting_type", category="client")

    note = _create_note(test_user)
    note.meeting_type = meeting_type
    note.save()

    # Create a client WITHOUT assigning it to the attendee
    # This will make the code follow the "else" branch where it creates an agenda directly
    Client.objects.create(name="Test Client", email="<EMAIL>", organization=test_org)

    # Make sure the unstructured_text schema exists
    schema, created = StructuredMeetingDataSchema.objects.get_or_create(
        name="unstructured_text",
        defaults={
            "schema": {"type": "object", "properties": {"content": {"type": "string"}, "format": {"type": "string"}}}
        },
    )

    # Call the function
    create_or_update_interaction(note, test_user)

    # Check interaction created
    interaction = ClientInteraction.objects.filter(note=note).first()
    assert interaction is not None
    assert interaction.meeting_type == meeting_type

    # Check advisor notes created
    assert interaction.advisor_notes is not None
    assert interaction.advisor_notes.title == "Advisor Notes"
    assert interaction.advisor_notes.kind == "advisor_notes"

    # Check agenda created
    assert interaction.agenda is not None
    assert interaction.agenda.title == "Meeting Agenda"


@patch("deepinsights.meetingsapp.tasks.generate_agenda.delay")
def test_create_or_update_interaction_update(
    mock_generate_agenda: MagicMock, test_user: User, test_org: Organization
) -> None:
    """Test updating an existing interaction - focuses on client relationship"""
    # Set up
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", key="test_meeting_type", category="client")
    second_meeting_type = MeetingType.objects.create(
        name="Second Meeting Type", key="second_meeting_type", category="client"
    )

    note = _create_note(test_user)
    note.meeting_type = second_meeting_type
    note.save()

    # Create initial interaction
    interaction = ClientInteraction.objects.create(meeting_type=meeting_type, note=note)
    advisor_notes = StructuredMeetingData.objects.create(
        title="Advisor Notes",
        kind="advisor_notes",
        schema={},
        data={"content": "Test advisor notes", "format": "markdown"},
    )
    interaction.advisor_notes = advisor_notes
    interaction.save()

    # Create a client in the database
    test_client = Client.objects.create(name="Test Client", email="<EMAIL>", organization=test_org)

    # Add client attendee
    Attendee.objects.create(note=note, attendee_name=test_client.name, client=test_client)

    # Mock the generate_agenda.delay to avoid Redis connection
    mock_generate_agenda.return_value = None

    # Call function
    create_or_update_interaction(note, test_user)

    # Get updated interaction
    updated_interaction = ClientInteraction.objects.get(id=interaction.id)
    assert updated_interaction.clients.count() == 1
    assert updated_interaction.meeting_type == second_meeting_type
    assert list(updated_interaction.clients.all()) == [test_client]
    assert updated_interaction.advisor_notes == advisor_notes
    assert updated_interaction.advisor_notes.data["content"] == "Test advisor notes"


@patch("deepinsights.meetingsapp.tasks.generate_agenda.delay")
def test_create_or_update_interaction_multiple_clients(
    mock_generate_agenda: MagicMock, test_user: User, test_org: Organization
) -> None:
    """Test with multiple clients"""
    # Set up
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", key="test_meeting_type", category="client")

    note = _create_note(test_user)
    note.meeting_type = meeting_type
    note.save()

    # Create clients
    client1 = Client.objects.create(name="Client 1", email="<EMAIL>", organization=test_org)
    client2 = Client.objects.create(name="Client 2", email="<EMAIL>", organization=test_org)

    # Add attendees
    Attendee.objects.create(note=note, attendee_name=client1.name, client=client1)
    Attendee.objects.create(note=note, attendee_name=client2.name, client=client2)

    # Mock the generate_agenda.delay to avoid Redis connection
    mock_generate_agenda.return_value = None

    # Call function
    create_or_update_interaction(note, test_user)

    # Check interaction
    interaction = ClientInteraction.objects.get(note=note)
    assert interaction.clients.count() == 2
    assert set(interaction.clients.all()) == {client1, client2}


@patch("deepinsights.meetingsapp.tasks.generate_agenda.delay")
def test_create_or_update_interaction_creates_agenda(mock_generate_agenda: MagicMock, test_user: User) -> None:
    """Test that interaction gets an agenda created"""
    # Set up
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", key="test_meeting_type", category="client")

    note = _create_note(test_user)
    note.meeting_type = meeting_type
    note.save()

    # Mock the generate_agenda.delay to avoid Redis connection
    mock_generate_agenda.return_value = None

    # Call function
    create_or_update_interaction(note, test_user)

    # Check interaction has agenda
    interaction = ClientInteraction.objects.get(note=note)
    assert interaction.agenda is not None
    assert interaction.agenda.title == "Meeting Agenda"
    assert "content" in interaction.agenda.data


# Tests for client interaction pathways within create_or_update_note function:

# 1. Empty agenda (no client, no template) -> creates empty agenda
# 2. Template-only agenda (no client, has template) -> creates agenda with template content
# 3. AI-generated agenda (has client, has template) -> calls generate_agenda
# 4. Empty advisor notes (no client) -> creates empty advisor notes
# 5. Client intelligence (has client) -> calls create_advisor_notes_and_fill_with_client_intelligence
# 6. Complete flow (has client, has template) -> calls both generate_agenda and intelligence


@patch("api.routers.note.generate_agenda.delay")
def test_create_interaction_empty_agenda(mock_generate_agenda: MagicMock, test_user: User) -> None:
    """Test case 1: Empty agenda is created when no client and no template content exist"""
    # Set up schema for structured data
    schema = StructuredMeetingDataSchema.objects.get_or_create(
        name="unstructured_text",
        defaults={
            "schema": {"type": "object", "properties": {"content": {"type": "string"}, "format": {"type": "string"}}}
        },
    )[0]

    # Create note with meeting type but no agenda templates
    # (This ensures get_agenda_template_content returns empty string)
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", key="empty_agenda", category="client")
    note = _create_note(test_user)
    note.meeting_type = meeting_type
    note.save()

    # Create interaction
    create_or_update_interaction(note, test_user)

    # Verify interaction was created with empty agenda
    interaction = ClientInteraction.objects.get(note=note)
    assert interaction.agenda is not None
    assert interaction.agenda.data["content"] == ""
    assert interaction.agenda.data["format"] == "markdown"

    # Verify agenda generation was NOT called (no client and no template)
    mock_generate_agenda.assert_not_called()


@patch("api.routers.note.generate_agenda.delay")
def test_create_interaction_template_only_agenda(mock_generate_agenda: MagicMock, test_user: User) -> None:
    """Test case 2: Template-only agenda is created when no client but template content exists"""
    # Set up meeting type
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", key="test_meeting_type", category="client")
    note = _create_note(test_user)
    note.meeting_type = meeting_type
    note.save()

    # Create a schema for the agenda template
    schema = StructuredMeetingDataSchema.objects.get_or_create(
        name="unstructured_text",
        defaults={
            "schema": {"type": "object", "properties": {"content": {"type": "string"}, "format": {"type": "string"}}}
        },
    )[0]

    # Create an agenda template and associate it with the meeting type
    template_content = "# Sample Agenda\n\n- Item 1\n- Item 2\n- Item 3"
    agenda_template = StructuredMeetingDataTemplate.objects.create(
        title="Meeting Agenda Template",
        internal_name="test_agenda_template",
        kind="agenda_template",
        schema_definition=schema,
        initial_data={"content": template_content, "format": "markdown"},
    )

    # Associate template with meeting type
    meeting_type.agenda_templates.add(agenda_template)

    # Mock the generate_agenda.delay to avoid Redis connection
    mock_generate_agenda.return_value = None

    # Call function
    create_or_update_interaction(note, test_user)

    # Check interaction uses the template
    interaction = ClientInteraction.objects.get(note=note)
    assert interaction.agenda is not None
    assert interaction.agenda.title == "Meeting Agenda"
    assert interaction.agenda.data["content"] == template_content

    # Verify agenda generation was NOT called (no client)
    mock_generate_agenda.assert_not_called()


@patch("api.routers.note.generate_agenda.delay")
def test_create_interaction_ai_generated_agenda(
    mock_generate_agenda: MagicMock, test_user: User, test_org: Organization
) -> None:
    """Test case 3: AI-generated agenda is created when client and template content exist"""
    # Set up meeting type
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", key="test_meeting_type", category="client")
    note = _create_note(test_user)
    note.meeting_type = meeting_type
    note.save()

    # Create a schema for the agenda template
    schema = StructuredMeetingDataSchema.objects.get_or_create(
        name="unstructured_text",
        defaults={
            "schema": {"type": "object", "properties": {"content": {"type": "string"}, "format": {"type": "string"}}}
        },
    )[0]

    # Create an agenda template and associate it with the meeting type
    template_content = "# Sample Agenda\n\n- Item 1\n- Item 2\n- Item 3"
    agenda_template = StructuredMeetingDataTemplate.objects.create(
        title="Meeting Agenda Template",
        internal_name="test_agenda_template",
        kind="agenda_template",
        schema_definition=schema,
        initial_data={"content": template_content, "format": "markdown"},
    )

    # Associate template with meeting type
    meeting_type.agenda_templates.add(agenda_template)

    # Add client as attendee
    test_client = Client.objects.create(name="Test Client", email="<EMAIL>", organization=test_org)
    Attendee.objects.create(note=note, attendee_name=test_client.name, client=test_client)

    # Create interaction
    create_or_update_interaction(note, test_user)

    # Verify interaction was created with template content in agenda
    interaction = ClientInteraction.objects.get(note=note)
    assert interaction.agenda is not None
    assert interaction.agenda.data["content"] == template_content

    # Verify agenda generation WAS called (has client and template)
    mock_generate_agenda.assert_called_once()
    args = mock_generate_agenda.call_args[0]
    assert args[0] == test_user.uuid
    assert args[1] == test_client.uuid
    assert args[2] == interaction.uuid
    assert args[3] == template_content


@patch("api.routers.note.create_advisor_notes_and_fill_with_client_intelligence.delay")
def test_create_interaction_empty_advisor_notes(mock_client_intelligence: MagicMock, test_user: User) -> None:
    """Test case 4: Empty advisor notes are created when no client exists"""
    # Set up schema for structured data
    schema = StructuredMeetingDataSchema.objects.get_or_create(
        name="unstructured_text",
        defaults={
            "schema": {"type": "object", "properties": {"content": {"type": "string"}, "format": {"type": "string"}}}
        },
    )[0]

    # Create note with meeting type but no client
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", key="empty_notes", category="client")
    note = _create_note(test_user)
    note.meeting_type = meeting_type
    note.save()

    # Create interaction
    create_or_update_interaction(note, test_user)

    # Verify interaction was created with empty advisor notes
    interaction = ClientInteraction.objects.get(note=note)
    assert interaction.advisor_notes is not None
    assert interaction.advisor_notes.kind == "advisor_notes"
    assert interaction.advisor_notes.data["content"] == ""
    assert interaction.advisor_notes.data["format"] == "markdown"

    # Verify client intelligence generation was NOT called (no client)
    mock_client_intelligence.assert_not_called()


@patch("api.routers.note.create_advisor_notes_and_fill_with_client_intelligence.delay")
def test_create_interaction_client_intelligence(
    mock_client_intelligence: MagicMock, test_user: User, test_org: Organization
) -> None:
    """Test case 5: Client intelligence is generated when a client is present"""
    # Set up schema for structured data
    schema = StructuredMeetingDataSchema.objects.get_or_create(
        name="unstructured_text",
        defaults={
            "schema": {"type": "object", "properties": {"content": {"type": "string"}, "format": {"type": "string"}}}
        },
    )[0]

    # Create note with meeting type
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", key="client_intel", category="client")
    note = _create_note(test_user)
    note.meeting_type = meeting_type
    note.save()

    # Add client as attendee
    test_client = Client.objects.create(name="Test Client", email="<EMAIL>", organization=test_org)
    Attendee.objects.create(note=note, attendee_name=test_client.name, client=test_client)

    # Create interaction
    create_or_update_interaction(note, test_user)

    # Verify client intelligence generation was triggered
    mock_client_intelligence.assert_called_once()
    args = mock_client_intelligence.call_args[0]
    assert args[0] == test_user.uuid
    assert args[1] == test_client.uuid

    # Verify interaction was created
    interaction = ClientInteraction.objects.get(note=note)
    assert args[2] == interaction.uuid


@patch("api.routers.note.create_advisor_notes_and_fill_with_client_intelligence.delay")
@patch("api.routers.note.generate_agenda.delay")
def test_create_interaction_complete_flow(
    mock_generate_agenda: MagicMock, mock_client_intelligence: MagicMock, test_user: User, test_org: Organization
) -> None:
    """Test case 6: Complete flow with both client and template triggers both agenda and intelligence generation"""
    # Set up meeting type
    meeting_type = MeetingType.objects.create(name="Test Meeting Type", key="test_meeting_type", category="client")
    note = _create_note(test_user)
    note.meeting_type = meeting_type
    note.save()

    # Create a schema for the agenda template
    schema = StructuredMeetingDataSchema.objects.get_or_create(
        name="unstructured_text",
        defaults={
            "schema": {"type": "object", "properties": {"content": {"type": "string"}, "format": {"type": "string"}}}
        },
    )[0]

    # Create an agenda template and associate it with the meeting type
    template_content = "# Sample Agenda\n\n- Item 1\n- Item 2\n- Item 3"
    agenda_template = StructuredMeetingDataTemplate.objects.create(
        title="Meeting Agenda Template",
        internal_name="test_agenda_template",
        kind="agenda_template",
        schema_definition=schema,
        initial_data={"content": template_content, "format": "markdown"},
    )

    # Associate template with meeting type
    meeting_type.agenda_templates.add(agenda_template)

    # Add client as attendee
    test_client = Client.objects.create(name="Test Client", email="<EMAIL>", organization=test_org)
    Attendee.objects.create(note=note, attendee_name=test_client.name, client=test_client)

    # Create interaction
    create_or_update_interaction(note, test_user)

    # Verify interaction was created
    interaction = ClientInteraction.objects.get(note=note)
    assert interaction is not None

    # Verify both agenda and client intelligence were generated
    mock_generate_agenda.assert_called_once()
    mock_client_intelligence.assert_called_once()

    # Verify agenda has template content
    assert interaction.agenda is not None
    assert interaction.agenda.data["content"] == template_content


def test_edit_note_server_error(test_user: User) -> None:
    """Test handling of server errors during note editing."""
    note = _create_note(test_user)

    with patch.object(Note, "save", side_effect=Exception("Database error")):
        response = client.post(f"/api/v2/note/{note.uuid}", json={"meetingName": "Updated Meeting"})

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {"detail": "An error occurred while editing the note"}


def test_action_items_with_due_date(test_user: User) -> None:
    note = _create_note(test_user)
    test_user.preferences["due_date_offset_seconds"] = 86400  # type: ignore[index] # 1 day
    test_user.save()

    action_item_uuid = uuid.uuid4()
    edit_request = {
        "actionItems": [
            {
                "item": {"content": "Test action item", "uuid": str(action_item_uuid), "status": "incomplete"},
                "action": "create",
            }
        ]
    }

    response = client.post(f"/api/v2/note/{note.uuid}", json=edit_request)

    assert response.status_code == status.HTTP_204_NO_CONTENT
    task = Task.objects.get(uuid=action_item_uuid)
    expected_due_date = task.created + datetime.timedelta(days=1)
    assert task.due_date is not None
    assert task.due_date.date() == expected_due_date.date()


def test_empty_edit_request(test_user: User) -> None:
    note = _create_note(test_user)
    original_note = Note.objects.get(uuid=note.uuid)

    response = client.post(f"/api/v2/note/{note.uuid}", json={})

    assert response.status_code == status.HTTP_204_NO_CONTENT
    updated_note = Note.objects.get(uuid=note.uuid)
    # Verify nothing changed
    assert updated_note.metadata == original_note.metadata
    assert updated_note.advisor_notes == original_note.advisor_notes
    assert updated_note.key_takeaways == original_note.key_takeaways
    assert updated_note.client == original_note.client
    assert updated_note.summary == original_note.summary


@patch("deepinsights.meetingsapp.tasks.reprocess_note_after_swapping.delay_on_commit")
@patch("deepinsights.meetingsapp.models.note.Note.task_set")
def test_swap_attendee_aliases_success(
    mock_task_set: MagicMock, mock_reprocess_note: MagicMock, test_user: User
) -> None:
    note = _create_note(test_user)
    attendee1 = Attendee.objects.create(
        attendee_name="Jon Doe",
        note=note,
        speaker_alias="speaker0",
        speaker_time=datetime.timedelta(seconds=1),
        speaker_percentage=33,
    )
    attendee2 = Attendee.objects.create(
        attendee_name="Jane Doe",
        note=note,
        speaker_alias="speaker1",
        speaker_time=datetime.timedelta(seconds=2),
        speaker_percentage=67,
    )

    mock_task_all = MagicMock()
    mock_task_set.all.return_value = mock_task_all

    payload = {
        "note_id": str(note.uuid),
        "swaps": [
            {"current_alias": attendee1.attendee_name, "new_alias": str(attendee2.uuid)},
            {"current_alias": attendee2.attendee_name, "new_alias": str(attendee1.uuid)},
        ],
    }

    response = client.post("/api/v2/note/swap-attendee-aliases", json=payload)
    assert response.status_code == status.HTTP_200_OK

    # Verify database changes
    attendee1.refresh_from_db()
    attendee2.refresh_from_db()
    assert attendee1.speaker_alias == "speaker1"
    assert attendee1.speaker_time == datetime.timedelta(seconds=2)
    assert attendee1.speaker_percentage == 67
    assert attendee2.speaker_alias == "speaker0"
    assert attendee2.speaker_time == datetime.timedelta(seconds=1)
    assert attendee2.speaker_percentage == 33

    # Verify mocked calls
    mock_task_all.delete.assert_called_once()
    mock_reprocess_note.assert_called_once_with(
        note.uuid, speaker_mapping={"speaker0": "Jane Doe", "speaker1": "Jon Doe"}
    )


def test_swap_attendee_aliases_to_non_speaker(test_user: User) -> None:
    note = _create_note(test_user)
    attendee1 = Attendee.objects.create(
        attendee_name="Jon Doe",
        note=note,
        speaker_alias="speaker0",
        speaker_time=datetime.timedelta(seconds=1),
        speaker_percentage=33,
    )
    attendee3 = Attendee.objects.create(attendee_name="Bob Doe", note=note, speaker_alias=None)

    with (
        patch("deepinsights.meetingsapp.models.note.Note.task_set") as mock_task_set,
        patch("deepinsights.meetingsapp.tasks.reprocess_note_after_swapping.delay_on_commit") as mock_reprocess_note,
    ):
        mock_task_all = MagicMock()
        mock_task_set.all.return_value = mock_task_all

        payload = {
            "note_id": str(note.uuid),
            "swaps": [{"current_alias": attendee1.attendee_name, "new_alias": str(attendee3.uuid)}],
        }

        response = client.post("api/v2/note/swap-attendee-aliases", json=payload)
        assert response.status_code == status.HTTP_200_OK

        # Verify database changes
        attendee1.refresh_from_db()
        attendee3.refresh_from_db()
        assert not attendee1.speaker_alias
        assert not attendee1.speaker_time
        assert not attendee1.speaker_percentage
        assert attendee3.speaker_alias == "speaker0"
        assert attendee3.speaker_percentage == 33
        assert attendee3.speaker_time == datetime.timedelta(seconds=1)

        # Verify mocked calls
        mock_task_all.delete.assert_called_once()
        mock_reprocess_note.assert_called_once_with(note.uuid, speaker_mapping={"speaker0": "Bob Doe"})


def test_swap_attendee_aliases_from_non_attendee(test_user: User) -> None:
    note = _create_note(test_user)
    attendee = Attendee.objects.create(attendee_name="Jon Doe", note=note)

    with (
        patch("deepinsights.meetingsapp.models.note.Note.task_set") as mock_task_set,
        patch("deepinsights.meetingsapp.tasks.reprocess_note_after_swapping.delay_on_commit") as mock_reprocess_note,
    ):
        mock_task_all = MagicMock()
        mock_task_set.all.return_value = mock_task_all

        payload = {
            "note_id": str(note.uuid),
            "swaps": [{"current_alias": "speaker0", "new_alias": str(attendee.uuid)}],
        }

        response = client.post("api/v2/note/swap-attendee-aliases", json=payload)

        assert response.status_code == status.HTTP_200_OK
        attendee.refresh_from_db()
        assert attendee.speaker_alias == "speaker0"
        assert attendee.speaker_time == datetime.timedelta(seconds=9)
        assert attendee.speaker_percentage == 84.1

        mock_task_all.delete.assert_called_once()
        mock_reprocess_note.assert_called_once_with(note.uuid, speaker_mapping={"speaker0": "Jon Doe"})


def test_swap_attendee_aliases_from_non_attendee_without_transcript(test_user: User) -> None:
    note = _create_note(test_user)
    note.raw_transcript = None
    note.save()
    attendee = Attendee.objects.create(attendee_name="Jon Doe", note=note)

    with (
        patch("deepinsights.meetingsapp.models.note.Note.task_set") as mock_task_set,
        patch("deepinsights.meetingsapp.tasks.reprocess_note_after_swapping.delay_on_commit") as mock_reprocess_note,
    ):
        mock_task_all = MagicMock()
        mock_task_set.all.return_value = mock_task_all

        payload = {
            "note_id": str(note.uuid),
            "swaps": [{"current_alias": "speaker0", "new_alias": str(attendee.uuid)}],
        }

        response = client.post("api/v2/note/swap-attendee-aliases", json=payload)

        assert response.status_code == status.HTTP_200_OK
        attendee.refresh_from_db()
        assert attendee.speaker_alias == "speaker0"
        assert not attendee.speaker_time
        assert not attendee.speaker_percentage

        mock_task_all.delete.assert_called_once()
        mock_reprocess_note.assert_called_once_with(note.uuid, speaker_mapping={"speaker0": "Jon Doe"})


def test_swap_attendee_aliases_unauthorized(test_user: User) -> None:
    note = _create_note(test_user)
    attendee1 = Attendee.objects.create(attendee_name="Jon Doe", note=note, speaker_alias="Speaker 1")
    attendee2 = Attendee.objects.create(attendee_name="Jane Doe", note=note, speaker_alias="Speaker 2")

    unauthorized_user = User.objects.create(
        username="<EMAIL>",
        email="<EMAIL>",
    )

    # Override the authentication dependency
    original_override = app.dependency_overrides.get(user_from_authorization_header)
    app.dependency_overrides[user_from_authorization_header] = lambda: unauthorized_user

    payload = {
        "note_id": str(note.uuid),
        "swaps": [{"current_alias": str(attendee1.attendee_name), "new_alias": str(attendee2.uuid)}],
    }

    response = client.post("/api/v2/note/swap-attendee-aliases", json=payload)
    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {"detail": "Not authorized to modify this note"}


def test_swap_attendee_aliases_note_not_found(test_user: User) -> None:
    payload = {"note_id": str(uuid.uuid4()), "swaps": [{"current_alias": "Test Name", "new_alias": str(uuid.uuid4())}]}

    response = client.post("api/v2/note/swap-attendee-aliases", json=payload)
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json()["detail"] == "Note not found"


def test_swap_attendee_aliases_both_attendees_not_found(test_user: User) -> None:
    note = _create_note(test_user)
    payload = {"note_id": str(note.uuid), "swaps": [{"current_alias": "Non Existent", "new_alias": str(uuid.uuid4())}]}

    response = client.post("/api/v2/note/swap-attendee-aliases", json=payload)
    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


def test_swap_attendee_aliases_unauthenticated() -> None:
    app.dependency_overrides = {}  # Remove auth override

    payload = {"note_id": str(uuid.uuid4()), "swaps": [{"current_alias": "Test Name", "new_alias": str(uuid.uuid4())}]}

    response = client.post("api/v2/note/swap-attendee-aliases", json=payload)
    assert response.status_code == status.HTTP_403_FORBIDDEN


def test_create_or_update_note_meeting_types(test_user: User) -> None:
    meeting_type = MeetingType.objects.create(name="Client", category=MeetingType.Category.CLIENT, key="client_test")
    response = client.post(
        "/api/v2/note/create_or_update",
        data={"meeting_type": "client_test"},
    )

    assert response.status_code == status.HTTP_200_OK
    assert not response.json()["completed"]

    note = Note.objects.get(uuid=response.json().get("note_id"))
    assert not note.metadata
    assert note.meeting_type == meeting_type


def test_create_or_update_note_meeting_types_by_uuid(test_user: User) -> None:
    meeting_type = MeetingType.objects.create(name="Client", category=MeetingType.Category.CLIENT)
    response = client.post(
        "/api/v2/note/create_or_update",
        data={"meeting_type": str(meeting_type.uuid)},
    )

    assert response.status_code == status.HTTP_200_OK
    assert not response.json()["completed"]

    note = Note.objects.get(uuid=response.json().get("note_id"))
    assert not note.metadata
    assert note.meeting_type == meeting_type


def test_note_category_with_meeting_type(test_user: User) -> None:
    meeting_type = MeetingType.objects.create(name="Internal", category=MeetingType.Category.INTERNAL)
    note = Note.objects.create(
        note_owner=test_user,
        meeting_type=meeting_type,
        metadata={"meeting_name": "Test Meeting", "meeting_type": "client"},
    )
    assert note.category == "internal"


def test_note_category_with_metadata(test_user: User) -> None:
    note = Note.objects.create(
        note_owner=test_user,
        metadata={"meeting_name": "Test Meeting", "meeting_type": "internal"},
    )
    assert note.category == "internal"


def test_note_category_with_no_meeting_type_or_metadata(test_user: User) -> None:
    note = Note.objects.create(
        note_owner=test_user,
        metadata={"meeting_name": "Test Meeting"},
    )
    assert note.category == "client"


def test_update_authorized_users_success(test_user: User, django_user_model: User) -> None:
    note = _create_note(test_user)
    new_user = django_user_model.objects.create(username="<EMAIL>")

    response = client.post(
        f"/api/v2/note/{note.uuid}/update-authorized-users",
        json=[str(new_user.uuid), str(test_user.uuid)],
    )

    assert response.status_code == status.HTTP_204_NO_CONTENT
    note.refresh_from_db()
    assert set(note.authorized_users.all()) == {test_user, new_user}


def test_update_authorized_users_note_not_found(test_user: User) -> None:
    response = client.post(
        f"/api/v2/note/{uuid.uuid4()}/update-authorized-users",
        json=[str(test_user.uuid)],
    )

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Note not found"}


def test_update_authorized_users_unauthorized(test_user: User, django_user_model: User) -> None:
    other_user = django_user_model.objects.create(username="<EMAIL>")
    note = _create_note(other_user)

    response = client.post(
        f"/api/v2/note/{note.uuid}/update-authorized-users",
        json=[str(test_user.uuid)],
    )

    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {"detail": "Not authorized to view this note"}
    assert list(note.authorized_users.all()) == [other_user]


@patch("api.routers.note.User.objects.filter")
def test_update_authorized_users_error(mock_user_filter: MagicMock, test_user: User) -> None:
    note = _create_note(test_user)
    mock_user_filter.side_effect = Exception("Database error")

    response = client.post(
        f"/api/v2/note/{note.uuid}/update-authorized-users",
        json=[str(test_user.uuid)],
    )

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {"detail": "An error occurred while updating authorized users"}
    assert list(note.authorized_users.all()) == [test_user]


def test_list_notes_not_before(test_user: User) -> None:
    note_2021 = _create_note(test_user, created=datetime.datetime(2021, 1, 1, 14, 30, 45, tzinfo=datetime.timezone.utc))
    note_2020 = _create_note(test_user, created=datetime.datetime(2020, 1, 1, 9, 15, 30, tzinfo=datetime.timezone.utc))
    note_2018 = _create_note(test_user, created=datetime.datetime(2018, 1, 1, 8, 45, 15, tzinfo=datetime.timezone.utc))
    note_2022 = _create_note(test_user, created=datetime.datetime(2022, 1, 1, 16, 20, 10, tzinfo=datetime.timezone.utc))
    note_2024 = _create_note(test_user, created=datetime.datetime(2024, 1, 1, 10, 5, 25, tzinfo=datetime.timezone.utc))

    # Created and scheduled before the limit.
    note_created_2018_scheduled_2020 = _create_note(
        test_user, created=datetime.datetime(2018, 1, 1, 8, 45, tzinfo=datetime.timezone.utc)
    )
    ScheduledEvent.objects.create(
        user=test_user,
        note=note_created_2018_scheduled_2020,
        start_time=datetime.datetime(2020, 1, 1, 9, 15, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2020, 1, 1, 10, 0, tzinfo=datetime.timezone.utc),
    )

    # Created before the limit, but scheduled after the limit.
    note_created_2020_scheduled_2022 = _create_note(
        test_user, created=datetime.datetime(2020, 1, 1, 9, 15, tzinfo=datetime.timezone.utc)
    )
    ScheduledEvent.objects.create(
        user=test_user,
        note=note_created_2020_scheduled_2022,
        start_time=datetime.datetime(2022, 1, 1, 16, 20, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2022, 1, 1, 18, 30, tzinfo=datetime.timezone.utc),
    )

    # Created after the limit, scheduled before the limit.
    note_created_2022_scheduled_2021 = _create_note(
        test_user, created=datetime.datetime(2022, 1, 1, 16, 20, tzinfo=datetime.timezone.utc)
    )
    ScheduledEvent.objects.create(
        user=test_user,
        note=note_created_2022_scheduled_2021,
        start_time=datetime.datetime(2021, 1, 1, 14, 30, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2021, 1, 1, 16, 20, tzinfo=datetime.timezone.utc),
    )

    # Scheduled to start before the limit, but scheduled to end after the limit.
    note_scheduled_start_2020_scheduled_end_2022 = _create_note(
        test_user, created=datetime.datetime(2020, 1, 1, 16, 20, tzinfo=datetime.timezone.utc)
    )
    ScheduledEvent.objects.create(
        user=test_user,
        note=note_scheduled_start_2020_scheduled_end_2022,
        start_time=datetime.datetime(2020, 1, 1, 14, 30, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2022, 1, 1, 16, 20, tzinfo=datetime.timezone.utc),
    )

    # Scheduled to start and end after the limit.
    note_created_2022_scheduled_start_2022 = _create_note(
        test_user, created=datetime.datetime(2022, 1, 1, 16, 20, tzinfo=datetime.timezone.utc)
    )
    ScheduledEvent.objects.create(
        user=test_user,
        note=note_created_2022_scheduled_start_2022,
        start_time=datetime.datetime(2022, 1, 1, 18, 30, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2022, 1, 1, 20, 20, tzinfo=datetime.timezone.utc),
    )

    # Metadata scheduled to start before the limit.
    note_metadata_scheduled_2021 = _create_note(
        test_user, created=datetime.datetime(2018, 1, 1, 16, 20, tzinfo=datetime.timezone.utc)
    )
    note_metadata_scheduled_2021.metadata = {
        "scheduled_at": datetime.datetime(2021, 1, 1, 14, 30, tzinfo=datetime.timezone.utc).isoformat(),
    }
    note_metadata_scheduled_2021.save()

    # Metadata scheduled to start after the limit.
    note_metadata_scheduled_2022 = _create_note(
        test_user, created=datetime.datetime(2021, 1, 1, 16, 20, tzinfo=datetime.timezone.utc)
    )
    note_metadata_scheduled_2022.metadata = {
        "scheduled_at": datetime.datetime(2022, 1, 1, 18, 30, tzinfo=datetime.timezone.utc).isoformat(),
    }
    note_metadata_scheduled_2022.save()

    response = client.get("/api/v2/note/list?not_before=1609511445")  # Timestamp for 2021-01-01T14:30:45Z
    assert response.status_code == status.HTTP_200_OK
    assert set([note["uuid"] for note in response.json()]) == set(
        [
            str(note_2021.uuid),
            str(note_2022.uuid),
            str(note_2024.uuid),
            str(note_created_2020_scheduled_2022.uuid),
            str(note_created_2022_scheduled_start_2022.uuid),
            str(note_metadata_scheduled_2022.uuid),
        ]
    )


def test_list_notes_not_after(test_user: User) -> None:
    user = test_user

    note_2021 = _create_note(user, created=datetime.datetime(2021, 1, 1, 14, 30, 45, tzinfo=datetime.timezone.utc))
    note_2020 = _create_note(user, created=datetime.datetime(2020, 1, 1, 9, 15, 30, tzinfo=datetime.timezone.utc))
    note_2018 = _create_note(user, created=datetime.datetime(2018, 1, 1, 8, 45, 15, tzinfo=datetime.timezone.utc))
    note_2022 = _create_note(user, created=datetime.datetime(2022, 1, 1, 16, 20, 10, tzinfo=datetime.timezone.utc))
    note_2024 = _create_note(user, created=datetime.datetime(2024, 1, 1, 10, 5, 25, tzinfo=datetime.timezone.utc))

    # Created and scheduled before the limit.
    note_created_2018_scheduled_2020 = _create_note(
        user, created=datetime.datetime(2018, 1, 1, 8, 45, tzinfo=datetime.timezone.utc)
    )
    ScheduledEvent.objects.create(
        user=user,
        note=note_created_2018_scheduled_2020,
        start_time=datetime.datetime(2020, 1, 1, 9, 15, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2020, 1, 1, 10, 0, tzinfo=datetime.timezone.utc),
    )

    # Created before the limit, but scheduled after the limit.
    note_created_2020_scheduled_2022 = _create_note(
        user, created=datetime.datetime(2020, 1, 1, 9, 15, tzinfo=datetime.timezone.utc)
    )
    ScheduledEvent.objects.create(
        user=user,
        note=note_created_2020_scheduled_2022,
        start_time=datetime.datetime(2022, 1, 1, 16, 20, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2022, 1, 1, 18, 30, tzinfo=datetime.timezone.utc),
    )

    # Created after the limit, scheduled before the limit.
    note_created_2022_scheduled_2021 = _create_note(
        user, created=datetime.datetime(2022, 1, 1, 16, 20, tzinfo=datetime.timezone.utc)
    )
    ScheduledEvent.objects.create(
        user=user,
        note=note_created_2022_scheduled_2021,
        start_time=datetime.datetime(2021, 1, 1, 14, 30, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2021, 1, 1, 16, 20, tzinfo=datetime.timezone.utc),
    )

    # Scheduled to start before the limit, but scheduled to end after the limit.
    note_scheduled_start_2020_scheduled_end_2022 = _create_note(
        user, created=datetime.datetime(2020, 1, 1, 16, 20, tzinfo=datetime.timezone.utc)
    )
    ScheduledEvent.objects.create(
        user=user,
        note=note_scheduled_start_2020_scheduled_end_2022,
        start_time=datetime.datetime(2020, 1, 1, 14, 30, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2022, 1, 1, 16, 20, tzinfo=datetime.timezone.utc),
    )

    # Scheduled to start and end after the limit.
    note_created_2022_scheduled_start_2022 = _create_note(
        user, created=datetime.datetime(2022, 1, 1, 16, 20, tzinfo=datetime.timezone.utc)
    )
    ScheduledEvent.objects.create(
        user=user,
        note=note_created_2022_scheduled_start_2022,
        start_time=datetime.datetime(2022, 1, 1, 18, 30, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2022, 1, 1, 20, 20, tzinfo=datetime.timezone.utc),
    )

    # Metadata scheduled to start before the limit.
    note_metadata_scheduled_2021 = _create_note(
        user, created=datetime.datetime(2018, 1, 1, 16, 20, tzinfo=datetime.timezone.utc)
    )
    note_metadata_scheduled_2021.metadata = {
        "scheduled_at": datetime.datetime(2021, 1, 1, 14, 30, tzinfo=datetime.timezone.utc).isoformat(),
    }
    note_metadata_scheduled_2021.save()

    # Metadata scheduled to start after the limit.
    note_metadata_scheduled_2022 = _create_note(
        user, created=datetime.datetime(2021, 1, 1, 16, 20, tzinfo=datetime.timezone.utc)
    )
    note_metadata_scheduled_2022.metadata = {
        "scheduled_at": datetime.datetime(2022, 1, 1, 18, 30, tzinfo=datetime.timezone.utc).isoformat(),
    }
    note_metadata_scheduled_2022.save()

    response = client.get("/api/v2/note/list?not_after=1609511445")  # Timestamp for 2021-01-01T14:30:45Z
    assert response.status_code == status.HTTP_200_OK
    assert set([note["uuid"] for note in response.json()]) == set(
        [
            str(note_2021.uuid),
            str(note_2020.uuid),
            str(note_2018.uuid),
            str(note_created_2018_scheduled_2020.uuid),
            str(note_created_2022_scheduled_2021.uuid),
            str(note_scheduled_start_2020_scheduled_end_2022.uuid),
            str(note_metadata_scheduled_2021.uuid),
        ]
    )


def test_list_notes_not_before_and_not_after_inclusive(test_user: User) -> None:
    user = test_user

    note_2021 = _create_note(user, created=datetime.datetime(2021, 1, 1, 14, 30, tzinfo=datetime.timezone.utc))
    note_2020 = _create_note(user, created=datetime.datetime(2020, 1, 1, 9, 15, tzinfo=datetime.timezone.utc))
    note_2018 = _create_note(user, created=datetime.datetime(2018, 1, 1, 8, 45, tzinfo=datetime.timezone.utc))
    note_2022 = _create_note(user, created=datetime.datetime(2022, 1, 1, 16, 20, tzinfo=datetime.timezone.utc))
    note_2024 = _create_note(user, created=datetime.datetime(2024, 1, 1, 10, 5, tzinfo=datetime.timezone.utc))

    # Created before the interval, but scheduled during the interval.
    note_created_2018_scheduled_2020 = _create_note(
        user, created=datetime.datetime(2018, 1, 1, 8, 45, tzinfo=datetime.timezone.utc)
    )
    ScheduledEvent.objects.create(
        user=user,
        note=note_created_2018_scheduled_2020,
        start_time=datetime.datetime(2020, 1, 1, 9, 15, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2020, 1, 1, 10, 0, tzinfo=datetime.timezone.utc),
    )

    # Created during the interval, but scheduled before the interval.
    note_created_2020_scheduled_2018 = _create_note(
        user, created=datetime.datetime(2020, 1, 1, 9, 15, tzinfo=datetime.timezone.utc)
    )
    ScheduledEvent.objects.create(
        user=user,
        note=note_created_2020_scheduled_2018,
        start_time=datetime.datetime(2018, 1, 1, 8, 45, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2018, 1, 1, 9, 0, tzinfo=datetime.timezone.utc),
    )

    # Scheduled to start during the interval, but scheduled to end after the interval.
    note_scheduled_start_2021_scheduled_end_2022 = _create_note(
        user, created=datetime.datetime(2020, 1, 1, 16, 20, tzinfo=datetime.timezone.utc)
    )
    ScheduledEvent.objects.create(
        user=user,
        note=note_scheduled_start_2021_scheduled_end_2022,
        start_time=datetime.datetime(2021, 1, 1, 14, 30, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2022, 1, 1, 16, 20, tzinfo=datetime.timezone.utc),
    )

    # Scheduled to start and end after the interval.
    note_created_2022_scheduled_start_2022 = _create_note(
        user, created=datetime.datetime(2022, 1, 1, 16, 20, tzinfo=datetime.timezone.utc)
    )
    ScheduledEvent.objects.create(
        user=user,
        note=note_created_2022_scheduled_start_2022,
        start_time=datetime.datetime(2022, 1, 1, 18, 30, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2022, 1, 1, 20, 20, tzinfo=datetime.timezone.utc),
    )

    # Metadata scheduled to start during the interval.
    note_metadata_scheduled_2021 = _create_note(
        user, created=datetime.datetime(2018, 1, 1, 16, 20, tzinfo=datetime.timezone.utc)
    )
    note_metadata_scheduled_2021.metadata = {
        "scheduled_at": datetime.datetime(2021, 1, 1, 14, 30, tzinfo=datetime.timezone.utc).isoformat(),
    }
    note_metadata_scheduled_2021.save()

    # Metadata scheduled to start after the interval.
    note_metadata_scheduled_2022 = _create_note(
        user, created=datetime.datetime(2021, 1, 1, 16, 20, tzinfo=datetime.timezone.utc)
    )
    note_metadata_scheduled_2022.metadata = {
        "scheduled_at": datetime.datetime(2022, 1, 1, 18, 30, tzinfo=datetime.timezone.utc).isoformat(),
    }
    note_metadata_scheduled_2022.save()

    response = client.get(
        "/api/v2/note/list?not_before=1577836800&not_after=1640995200"
    )  # Timestamps for 2020-01-01T00:00:00Z and 2022-01-01T00:00:00Z
    assert response.status_code == status.HTTP_200_OK
    assert set([note["uuid"] for note in response.json()]) == set(
        [
            str(note_2020.uuid),
            str(note_2021.uuid),
            str(note_created_2018_scheduled_2020.uuid),
            str(note_scheduled_start_2021_scheduled_end_2022.uuid),
            str(note_metadata_scheduled_2021.uuid),
        ]
    )


def test_add_section_to_summary_success_summary_not_empty(test_user: User) -> None:
    """Test successful addition of section to summary."""
    note = _create_note(
        note_owner=test_user,
        summary={
            "sections": [
                {
                    "topic": "MORTGAGE REFINANCING OPTIONS",
                    "bullets": [
                        "Aaron Price informed me that their construction loan at 3.85% is no longer viable as the construction company requires the final payment at closing. The bank has proposed a cash-out refinance with higher rates.",
                        "The new proposed rates are 5.875% or 5.625% if they reduce the loan amount to $620,000 by paying $35,000 out of pocket.",
                    ],
                }
            ]
        },
    )
    summary_section = {"topic": "Test", "bullets": ["test1", "test2", "test3", "test4"]}
    search_query = SearchQuery.objects.create(query="test", requestor=test_user, structured_response=summary_section)
    search_query.notes.add(note)

    response = client.post(f"/api/v2/note/{note.uuid}/add_section/", json={"search_query_id": str(search_query.uuid)})

    assert response.status_code == status.HTTP_200_OK
    assert response.json()["section_index"] == 1
    note.refresh_from_db()
    assert note.summary == {
        "sections": [
            {
                "topic": "MORTGAGE REFINANCING OPTIONS",
                "bullets": [
                    "Aaron Price informed me that their construction loan at 3.85% is no longer viable as the construction company requires the final payment at closing. The bank has proposed a cash-out refinance with higher rates.",
                    "The new proposed rates are 5.875% or 5.625% if they reduce the loan amount to $620,000 by paying $35,000 out of pocket.",
                ],
            },
            summary_section,
        ]
    }


def test_add_section_to_summary_success_summary_empty(test_user: User) -> None:
    """Test successful addition of section to summary."""
    note = _create_note(note_owner=test_user)
    summary_section = {"topic": "Test", "bullets": ["test1", "test2", "test3", "test4"]}
    search_query = SearchQuery.objects.create(query="test", requestor=test_user, structured_response=summary_section)
    search_query.notes.add(note)

    response = client.post(f"/api/v2/note/{note.uuid}/add_section/", json={"search_query_id": str(search_query.uuid)})

    assert response.status_code == status.HTTP_200_OK
    assert response.json()["section_index"] == 0
    note.refresh_from_db()
    assert note.summary == {
        "sections": [
            summary_section,
        ]
    }


def test_add_section_to_summary_note_not_found(test_user: User) -> None:
    """Test adding section to summary when note is not found."""
    search_query_id = uuid.uuid4()
    response = client.post(f"/api/v2/note/{uuid.uuid4()}/add_section/", json={"search_query_id": str(search_query_id)})

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json()["detail"] == "Note not found"


def test_upload_audio_chunk_success(test_user: User) -> None:
    note = _create_note(test_user)

    response = client.post(
        f"/api/v2/note/{note.uuid}/upload_audio_chunk",
        data={"sequence": "1", "duration": "10", "mime_type": "audio/wav", "nonce": "123"},
        files={"audio_data": SimpleUploadedFile("audio_chunk.wav", b"audio data", "audio/wav")},
    )

    assert response.status_code == status.HTTP_201_CREATED
    buffers = AudioBuffer.objects.filter(note=note, sequence=1)
    assert buffers.count() == 1
    assert bytes(buffers[0].data) == b"audio data"
    assert buffers[0].duration == 10
    assert buffers[0].mime_type == "audio/wav"
    assert buffers[0].nonce == "123"


def test_upload_audio_chunk_note_not_found(test_user: User) -> None:
    non_existent_uuid = uuid.uuid4()

    response = client.post(
        f"/api/v2/note/{non_existent_uuid}/upload_audio_chunk",
        data={"sequence": "1", "duration": "10", "mime_type": "audio/wav", "nonce": "123"},
        files={"audio_data": SimpleUploadedFile("audio_chunk.wav", b"audio data", "audio/wav")},
    )

    assert response.status_code == status.HTTP_404_NOT_FOUND


def test_upload_audio_chunk_unauthorized(test_user: User, django_user_model: User) -> None:
    test_user.license_type = User.LicenseType.csa
    test_user.save()
    second_user = django_user_model.objects.create(email="<EMAIL>")
    note = _create_note(second_user)

    response = client.post(
        f"/api/v2/note/{note.uuid}/upload_audio_chunk",
        data={"sequence": "1", "duration": "10", "mime_type": "audio/wav", "nonce": "123"},
        files={"audio_data": SimpleUploadedFile("audio_chunk.wav", b"audio data", "audio/wav")},
    )

    assert response.status_code == status.HTTP_403_FORBIDDEN


def test_upload_audio_chunk_same_sequence_different_nonce(test_user: User) -> None:
    note = _create_note(test_user)
    AudioBuffer.objects.create(note=note, sequence=1, data=b"audio data", duration=10, nonce="123")

    response = client.post(
        f"/api/v2/note/{note.uuid}/upload_audio_chunk",
        data={"sequence": "1", "duration": "10", "mime_type": "audio/wav", "nonce": "234"},
        files={"audio_data": SimpleUploadedFile("audio_chunk.wav", b"new audio data", "audio/wav")},
    )

    assert response.status_code == status.HTTP_201_CREATED
    assert AudioBuffer.objects.filter(note=note, sequence=1).count() == 2
    assert (
        bytes(AudioBuffer.objects.filter(note=note, sequence=1, nonce="123").values_list("data", flat=True)[0])
        == b"audio data"
    )
    assert (
        bytes(AudioBuffer.objects.filter(note=note, sequence=1, nonce="234").values_list("data", flat=True)[0])
        == b"new audio data"
    )


def test_upload_audio_chunk_already_exists_with_same_data(test_user: User) -> None:
    note = _create_note(test_user)
    AudioBuffer.objects.create(note=note, sequence=1, data=b"audio data", duration=10, nonce="123")

    response = client.post(
        f"/api/v2/note/{note.uuid}/upload_audio_chunk",
        data={"sequence": "1", "duration": "10", "mime_type": "audio/wav", "nonce": "123"},
        files={"audio_data": SimpleUploadedFile("audio_chunk.wav", b"audio data", "audio/wav")},
    )

    assert response.status_code == status.HTTP_200_OK
    assert AudioBuffer.objects.filter(note=note, sequence=1).count() == 1


def test_upload_audio_chunk_already_exists_with_different_data(test_user: User) -> None:
    note = _create_note(test_user)
    AudioBuffer.objects.create(note=note, sequence=1, data=b"existing audio data", duration=10, nonce="123")

    response = client.post(
        f"/api/v2/note/{note.uuid}/upload_audio_chunk",
        data={"sequence": "1", "duration": "10", "mime_type": "audio/wav", "nonce": "123"},
        files={"audio_data": SimpleUploadedFile("audio_chunk.wav", b"audio data", "audio/wav")},
    )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {"detail": "Audio chunk already exists with different data."}


@patch("api.routers.note.AudioBuffer.objects.acreate", new_callable=AsyncMock)
def test_upload_audio_chunk_internal_server_error(mock_create: AsyncMock, test_user: User) -> None:
    note = _create_note(test_user)

    mock_create.side_effect = Exception("Database error")
    response = client.post(
        f"/api/v2/note/{note.uuid}/upload_audio_chunk",
        data={"sequence": "1", "duration": "10", "mime_type": "audio/wav", "nonce": "123"},
        files={"audio_data": SimpleUploadedFile("audio_chunk.wav", b"audio data", "audio/wav")},
    )

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


class TestClientInteractionStatusTransitions:
    """Test class for verifying status transitions in client interactions"""

    def test_advisor_notes_status_transitions(self, test_user: User, test_org: Organization) -> None:
        """Test advisor notes status transitions based on client presence"""
        # Set up meeting type
        meeting_type = MeetingType.objects.create(name="Test Meeting Type", key="test_meeting_type", category="client")
        note = _create_note(test_user)
        note.meeting_type = meeting_type
        note.save()

        # Create interaction without client
        create_or_update_interaction(note, test_user)
        interaction = ClientInteraction.objects.get(note=note)

        # Verify initial status is COMPLETED when no client
        assert interaction.advisor_notes is not None
        assert interaction.advisor_notes.status == StructuredMeetingData.Status.COMPLETED
        assert interaction.advisor_notes.data["content"] == ""
        assert interaction.advisor_notes.data["format"] == "markdown"

        # Add client and update interaction
        test_client = Client.objects.create(name="Test Client", email="<EMAIL>", organization=test_org)
        Attendee.objects.create(note=note, attendee_name=test_client.name, client=test_client)

        # Delete existing advisor notes so they can be recreated
        interaction.advisor_notes.delete()
        interaction.refresh_from_db()

        with patch(
            "api.routers.note.create_advisor_notes_and_fill_with_client_intelligence.delay"
        ) as mock_intelligence:
            create_or_update_interaction(note, test_user)

            # Verify status changed to PROCESSING when client added
            interaction.refresh_from_db()
            interaction.advisor_notes.refresh_from_db()
            assert interaction.advisor_notes.status == StructuredMeetingData.Status.PROCESSING
            mock_intelligence.assert_called_once()

    def test_agenda_status_transitions_with_template(self, test_user: User, test_org: Organization) -> None:
        """Test agenda status transitions based on client and template presence"""
        # Set up meeting type with template
        meeting_type = MeetingType.objects.create(name="Test Meeting Type", key="test_meeting_type", category="client")
        note = _create_note(test_user)
        note.meeting_type = meeting_type
        note.save()

        # Create agenda template
        schema = StructuredMeetingDataSchema.objects.get_or_create(
            name="unstructured_text",
            defaults={
                "schema": {
                    "type": "object",
                    "properties": {"content": {"type": "string"}, "format": {"type": "string"}},
                }
            },
        )[0]
        template_content = "# Sample Agenda\n\n- Item 1\n- Item 2"
        agenda_template = StructuredMeetingDataTemplate.objects.create(
            title="Meeting Agenda Template",
            internal_name="test_agenda_template",
            kind="agenda_template",
            schema_definition=schema,
            initial_data={"content": template_content, "format": "markdown"},
        )
        meeting_type.agenda_templates.add(agenda_template)

        # Create interaction without client
        create_or_update_interaction(note, test_user)
        interaction = ClientInteraction.objects.get(note=note)

        # Verify initial status is COMPLETED when no client
        assert interaction.agenda
        assert interaction.agenda.status == StructuredMeetingData.Status.COMPLETED
        assert interaction.agenda.data["content"] == template_content

        interaction.agenda.delete()
        interaction.refresh_from_db()

        # Add client and update interaction
        test_client = Client.objects.create(name="Test Client", email="<EMAIL>", organization=test_org)
        Attendee.objects.create(note=note, attendee_name=test_client.name, client=test_client)

        with patch("api.routers.note.generate_agenda.delay") as mock_generate_agenda:
            create_or_update_interaction(note, test_user)

            # Verify status changed to PROCESSING when client added
            interaction.refresh_from_db()
            assert interaction.agenda.status == StructuredMeetingData.Status.PROCESSING
            mock_generate_agenda.assert_called_once()

    def test_agenda_status_without_template(self, test_user: User, test_org: Organization) -> None:
        """Test agenda status when no template content exists"""
        # Set up meeting type without template
        meeting_type = MeetingType.objects.create(name="Test Meeting Type", key="test_meeting_type", category="client")
        note = _create_note(test_user)
        note.meeting_type = meeting_type
        note.save()

        # Create interaction
        create_or_update_interaction(note, test_user)
        interaction = ClientInteraction.objects.get(note=note)

        # Verify status is COMPLETED with empty content
        assert interaction.agenda
        assert interaction.agenda.status == StructuredMeetingData.Status.COMPLETED
        assert interaction.agenda.data["content"] == ""
        assert interaction.agenda.data["format"] == "markdown"

        # Add client and update interaction
        test_client = Client.objects.create(name="Test Client", email="<EMAIL>", organization=test_org)
        Attendee.objects.create(note=note, attendee_name=test_client.name, client=test_client)

        with patch("api.routers.note.generate_agenda.delay") as mock_generate_agenda:
            create_or_update_interaction(note, test_user)

            # Verify status remains COMPLETED when no template content
            interaction.refresh_from_db()
            assert interaction.agenda.status == StructuredMeetingData.Status.COMPLETED
            mock_generate_agenda.assert_not_called()
