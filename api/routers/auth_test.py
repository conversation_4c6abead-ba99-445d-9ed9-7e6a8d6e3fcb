from datetime import datetime, timedelta, timezone
from unittest.mock import MagicMock, patch

import jwt
import pytest
from django.core.mail import EmailMessage, EmailMultiAlternatives
from fastapi import status
from fastapi.testclient import TestClient
from pytest_django.fixtures import Settings<PERSON>rapper
from rest_framework_simplejwt.exceptions import TokenError
from rest_framework_simplejwt.tokens import RefreshToken

from api.dependencies import user_from_access_token
from api.internal_api import internal_api as app
from api.routers.auth import UserLicenseType
from deepinsights.core.integrations.meetingbot.bot_controller import ZoomStatus
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.user_impersonation import UserImpersonation
from deepinsights.users.models.user import User

client = TestClient(app)

pytestmark = [pytest.mark.django_db(transaction=True)]


@pytest.fixture
def test_organization() -> Organization:
    return Organization.objects.create(name="Test Org")


@pytest.fixture
def test_user(django_user_model: User) -> User:
    return django_user_model.objects.create_user(
        username="<EMAIL>",
        email="<EMAIL>",
        password="123",
        first_name="Test",
        last_name="User",
        is_active=True,
        status=User.STATUS_CHOICES.active,
    )


def test_login_nonexistent() -> None:
    response = client.post("/api/v2/auth/login", json={"email": "<EMAIL>", "password": "123"})
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "User not found"}


def test_login_email_password_invalid(test_user: User) -> None:
    response = client.post("/api/v2/auth/login", json={"email": "<EMAIL>", "password": "432"})

    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    assert response.json() == {"detail": "Invalid credentials"}


@patch("api.routers.auth.RefreshToken.for_user", side_effect=TokenError)
def test_login_email_password_token_generation_exception(_: MagicMock, test_user: User) -> None:
    response = client.post("/api/v2/auth/login", json={"email": "<EMAIL>", "password": "123"})

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {"detail": "An error occurred during login"}


@pytest.mark.parametrize(
    "has_org",
    [
        True,
        False,
    ],
    ids=[
        "with-org",
        "no-org",
    ],
)
@patch("api.routers.auth.sync_crm_clients")
@patch("api.routers.auth.reconcile_calendar_events")
def test_login_email_password(
    mock_reconcile_calendar_events: MagicMock,
    mock_sync_crm_clients: MagicMock,
    test_user: User,
    test_organization: Organization,
    has_org: bool,
) -> None:
    if has_org:
        test_user.organization = test_organization
    test_user.save()
    response = client.post("/api/v2/auth/login", json={"email": "<EMAIL>", "password": "123"})

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert "refresh_token" in data
    assert "access_token" in data
    assert data.get("user_profile") == {
        "uuid": str(test_user.uuid),
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "User",
        "is_active": True,
        "status": "active",
        "org_id": str(test_organization.id) if has_org else None,
        "license_type": UserLicenseType.advisor,
    }

    mock_sync_crm_clients.delay.assert_called_once_with(test_user.uuid)
    mock_reconcile_calendar_events.delay.assert_called_once_with(test_user.uuid)


@pytest.mark.parametrize(
    "user_status",
    (
        User.STATUS_CHOICES.unknown,
        User.STATUS_CHOICES.expired,
        User.STATUS_CHOICES.waitlisted,
        None,
    ),
)
@patch("api.routers.auth.sync_crm_clients")
@patch("api.routers.auth.reconcile_calendar_events")
def test_login_email_password_inactive_status(
    mock_reconcile_calendar_events: MagicMock,
    mock_sync_crm_clients: MagicMock,
    test_user: User,
    user_status: str | None,
) -> None:
    test_user.status = user_status
    test_user.save()

    response = client.post("/api/v2/auth/login", json={"email": "<EMAIL>", "password": "123"})

    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {"detail": "Account verification required"}

    mock_sync_crm_clients.delay.assert_not_called()
    mock_reconcile_calendar_events.delay.assert_not_called()


def test_login_email_casing(test_user: User) -> None:
    uppercase_response = client.post("/api/v2/auth/login", json={"email": "<EMAIL>", "password": "123"})
    lowercase_response = client.post("/api/v2/auth/login", json={"email": "<EMAIL>", "password": "123"})
    assert uppercase_response.status_code == lowercase_response.status_code
    assert uppercase_response.json()["user_profile"] == lowercase_response.json()["user_profile"]


@pytest.mark.parametrize(
    "license_type, expected_license_type",
    [
        (User.LicenseType.advisor, UserLicenseType.advisor),
        (User.LicenseType.csa, UserLicenseType.csa),
        (User.LicenseType.staff, UserLicenseType.staff),
    ],
)
@patch("api.routers.auth.sync_crm_clients")
@patch("api.routers.auth.reconcile_calendar_events")
def test_login_email_password_licence_type(
    mock_reconcile_calendar_events: MagicMock,
    mock_sync_crm_clients: MagicMock,
    test_user: User,
    test_organization: Organization,
    license_type: User.LicenseType,
    expected_license_type: UserLicenseType,
) -> None:
    test_user.license_type = license_type
    test_user.save()
    response = client.post("/api/v2/auth/login", json={"email": "<EMAIL>", "password": "123"})

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data.get("user_profile").get("license_type") == expected_license_type


@patch("api.routers.auth.GoogleOAuth.get_user_info", side_effect=Exception)
def test_google_token_validation_exception(mock_get_user_info: MagicMock, django_user_model: User) -> None:
    response = client.post("/api/v2/auth/google-signin", json={"access_token": "123"})

    assert response.json() == {"detail": "Unable to validate user"}
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert django_user_model.objects.all().count() == 0
    mock_get_user_info.assert_called_once_with("123")


@patch("api.routers.auth.GoogleOAuth.get_user_info")
def test_google_validation_empty_user_info(mock_get_user_info: MagicMock, django_user_model: User) -> None:
    mock_get_user_info.return_value = None

    response = client.post("/api/v2/auth/google-signin", json={"access_token": "123"})

    assert response.json() == {"detail": "Unable to validate user"}
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert django_user_model.objects.all().count() == 0
    mock_get_user_info.assert_called_once_with("123")


@patch("api.routers.auth.GoogleOAuth.get_user_info")
def test_google_validation_missing_email(mock_get_user_info: MagicMock, django_user_model: User) -> None:
    mock_get_user_info.return_value = {
        "given_name": "Test",
        "family_name": "User",
    }

    response = client.post("/api/v2/auth/google-signin", json={"access_token": "123"})

    assert response.json() == {"detail": "Unable to validate user"}
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert django_user_model.objects.all().count() == 0
    mock_get_user_info.assert_called_once_with("123")


@patch("api.routers.auth.RefreshToken.for_user", side_effect=TokenError)
@patch("api.routers.auth.GoogleOAuth.get_user_info")
def test_google_token_generation_exception(mock_get_user_info: MagicMock, _: MagicMock, test_user: User) -> None:
    mock_get_user_info.return_value = {
        "email": "<EMAIL>",
        "given_name": "Test",
        "family_name": "User",
    }

    response = client.post("/api/v2/auth/google-signin", json={"access_token": "123"})

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {"detail": "An error occurred during login"}


@patch("api.routers.auth.GoogleOAuth.get_user_info")
def test_google_create_user(mock_get_user_info: MagicMock) -> None:
    mock_get_user_info.return_value = {
        "email": "<EMAIL>",
        "given_name": "Test2",
        "family_name": "User2",
    }

    response = client.post("/api/v2/auth/google-signin", json={"access_token": "123"})

    assert response.status_code == status.HTTP_403_FORBIDDEN
    user = User.objects.get(email="<EMAIL>")
    assert user.username == "<EMAIL>"
    assert user.first_name == "Test2"
    assert user.last_name == "User2"
    assert user.name == "Test2 User2"
    assert not user.google_id
    mock_get_user_info.assert_called_once_with("123")


@patch("api.routers.auth.GoogleOAuth.get_user_info")
@patch("api.routers.auth.sync_crm_clients")
@patch("api.routers.auth.reconcile_calendar_events")
def test_google_create_user_with_id(
    mock_reconcile_calendar_events: MagicMock,
    mock_sync_crm_clients: MagicMock,
    mock_get_user_info: MagicMock,
) -> None:
    mock_get_user_info.return_value = {
        "email": "<EMAIL>",
        "given_name": "Test2",
        "family_name": "User2",
        "id": "**********",
    }

    response = client.post("/api/v2/auth/google-signin", json={"access_token": "123"})

    assert response.status_code == status.HTTP_403_FORBIDDEN
    user = User.objects.get(email="<EMAIL>")
    assert user.username == "<EMAIL>"
    assert user.first_name == "Test2"
    assert user.last_name == "User2"
    assert user.name == "Test2 User2"
    assert not user.microsoft_id
    assert user.google_id == "**********"
    mock_get_user_info.assert_called_once_with("123")
    mock_sync_crm_clients.delay.assert_not_called()
    mock_reconcile_calendar_events.delay.assert_not_called()


@patch("api.routers.auth.GoogleOAuth.get_user_info")
def test_google_create_user_missing_name(mock_get_user_info: MagicMock) -> None:
    mock_get_user_info.return_value = {"email": "<EMAIL>"}

    response = client.post("/api/v2/auth/google-signin", json={"access_token": "123"})

    assert response.status_code == status.HTTP_403_FORBIDDEN
    user = User.objects.get(email="<EMAIL>")
    assert user.username == "<EMAIL>"
    assert not user.first_name
    assert not user.last_name
    assert user.name == "<EMAIL>"
    mock_get_user_info.assert_called_once_with("123")


@patch("api.routers.auth.GoogleOAuth.get_user_info")
@patch("api.routers.auth.sync_crm_clients")
@patch("api.routers.auth.reconcile_calendar_events")
def test_google_user_waitlisted(
    mock_reconcile_calendar_events: MagicMock,
    mock_sync_crm_clients: MagicMock,
    mock_get_user_info: MagicMock,
    test_user: User,
) -> None:
    test_user.status = User.STATUS_CHOICES.waitlisted
    test_user.save()

    mock_get_user_info.return_value = {
        "email": "<EMAIL>",
        "given_name": "Test",
        "family_name": "User",
    }

    response = client.post("/api/v2/auth/google-signin", json={"access_token": "123"})
    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {"detail": "Account verification required"}
    mock_get_user_info.assert_called_once_with("123")
    mock_sync_crm_clients.delay.assert_not_called()
    mock_reconcile_calendar_events.delay.assert_not_called()


@patch("api.routers.auth.GoogleOAuth.get_user_info")
@patch("api.routers.auth.sync_crm_clients")
@patch("api.routers.auth.reconcile_calendar_events")
def test_active_google_user(
    mock_reconcile_calendar_events: MagicMock,
    mock_sync_crm_clients: MagicMock,
    mock_get_user_info: MagicMock,
    test_user: User,
) -> None:
    test_user.status = User.STATUS_CHOICES.active
    test_user.save()

    mock_get_user_info.return_value = {
        "email": "<EMAIL>",
        "given_name": "Test",
        "family_name": "User",
        "id": "**********",
    }

    response = client.post("/api/v2/auth/google-signin", json={"access_token": "123"})
    test_user.refresh_from_db()
    assert test_user.google_id == "**********"
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert "access_token" in data
    assert "refresh_token" in data
    assert data.get("user_profile") == {
        "uuid": str(test_user.uuid),
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "User",
        "is_active": True,
        "status": "active",
        "org_id": None,
        "license_type": UserLicenseType.advisor,
    }
    mock_get_user_info.assert_called_once_with("123")
    mock_sync_crm_clients.delay.assert_called_once_with(test_user.uuid)
    mock_reconcile_calendar_events.delay.assert_called_once_with(test_user.uuid)


@patch("api.routers.auth.GoogleOAuth.get_user_info")
def test_existing_google_user_no_new_id(mock_get_user_info: MagicMock, test_user: User) -> None:
    test_user.status = User.STATUS_CHOICES.active
    test_user.google_id = "**********"
    test_user.save()

    mock_get_user_info.return_value = {
        "email": "<EMAIL>",
        "given_name": "Test",
        "family_name": "User",
    }

    response = client.post("/api/v2/auth/google-signin", json={"access_token": "123"})

    assert response.status_code == status.HTTP_200_OK
    test_user.refresh_from_db()
    assert test_user.google_id == "**********"


@patch("api.routers.auth.GoogleOAuth.get_user_info")
def test_google_signin_with_organization(
    mock_get_user_info: MagicMock, test_user: User, test_organization: Organization
) -> None:
    test_user.organization = test_organization
    test_user.save()
    mock_get_user_info.return_value = {
        "email": "<EMAIL>",
        "given_name": "Test",
        "family_name": "Example",
    }
    response = client.post("/api/v2/auth/google-signin", json={"access_token": "123"})

    assert response.status_code == status.HTTP_200_OK
    user_profile = response.json().get("user_profile", {})
    assert user_profile.get("org_id") == str(test_organization.id)
    assert user_profile.get("uuid") == str(test_user.uuid)
    mock_get_user_info.assert_called_once_with("123")


@pytest.mark.parametrize(
    "license_type, expected_license_type",
    [
        (User.LicenseType.advisor, UserLicenseType.advisor),
        (User.LicenseType.csa, UserLicenseType.csa),
        (User.LicenseType.staff, UserLicenseType.staff),
    ],
)
@patch("api.routers.auth.GoogleOAuth.get_user_info")
@patch("api.routers.auth.sync_crm_clients")
@patch("api.routers.auth.reconcile_calendar_events")
def test_google_signin_license_type(
    mock_reconcile_calendar_events: MagicMock,
    mock_sync_crm_clients: MagicMock,
    mock_get_user_info: MagicMock,
    test_user: User,
    license_type: User.LicenseType,
    expected_license_type: UserLicenseType,
) -> None:
    test_user.license_type = license_type
    test_user.save()

    mock_get_user_info.return_value = {
        "email": "<EMAIL>",
        "given_name": "Test",
        "family_name": "User",
        "id": "**********",
    }

    response = client.post("/api/v2/auth/google-signin", json={"access_token": "123"})

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data.get("user_profile").get("license_type") == expected_license_type


@patch("api.routers.auth.MicrosoftOAuth.get_user_info", side_effect=Exception)
def test_microsoft_token_validation_exception(mock_microsoft: MagicMock) -> None:
    response = client.post("/api/v2/auth/microsoft-signin", json={"access_token": "123"})
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {"detail": "Unable to validate user"}
    mock_microsoft.assert_called_once_with("123")


@patch("api.routers.auth.MicrosoftOAuth.get_user_info")
def test_microsoft_token_validation_empty_user_info(mock_microsoft: MagicMock) -> None:
    mock_microsoft.return_value = None

    response = client.post("/api/v2/auth/microsoft-signin", json={"access_token": "123"})

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {"detail": "Unable to validate user"}
    mock_microsoft.assert_called_once_with("123")


@patch("api.routers.auth.MicrosoftOAuth.get_user_info")
def test_microsoft_validation_missing_email(mock_microsoft: MagicMock, django_user_model: User) -> None:
    mock_microsoft.return_value = {
        "givenName": "Test",
        "surname": "User",
    }

    response = client.post("/api/v2/auth/microsoft-signin", json={"access_token": "123"})

    assert response.json() == {"detail": "Unable to validate user"}
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert django_user_model.objects.all().count() == 0
    mock_microsoft.assert_called_once_with("123")


@patch("api.routers.auth.RefreshToken.for_user", side_effect=TokenError)
@patch("api.routers.auth.MicrosoftOAuth.get_user_info")
def test_microsoft_token_generation_exception(mock_microsoft: MagicMock, _: MagicMock, test_user: User) -> None:
    mock_microsoft.return_value = {
        "mail": "<EMAIL>",
        "givenName": "Test",
        "surname": "User",
    }

    response = client.post("/api/v2/auth/microsoft-signin", json={"access_token": "123"})

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {"detail": "An error occurred during login"}


@patch("api.routers.auth.MicrosoftOAuth.get_user_info")
@patch("api.routers.auth.sync_crm_clients")
@patch("api.routers.auth.reconcile_calendar_events")
def test_microsoft_create_user(
    mock_reconcile_calendar_events: MagicMock, mock_sync_crm_clients: MagicMock, mock_microsoft: MagicMock
) -> None:
    mock_microsoft.return_value = {
        "mail": "<EMAIL>",
        "givenName": "Test2",
        "surname": "User2",
    }

    response = client.post("/api/v2/auth/microsoft-signin", json={"access_token": "123"})

    assert response.status_code == status.HTTP_403_FORBIDDEN
    user = User.objects.get(email="<EMAIL>")
    assert user.username == "<EMAIL>"
    assert user.first_name == "Test2"
    assert user.last_name == "User2"
    assert user.name == "Test2 User2"
    assert not user.microsoft_id
    mock_microsoft.assert_called_once_with("123")
    mock_sync_crm_clients.delay.assert_not_called()
    mock_reconcile_calendar_events.delay.assert_not_called()


@patch("api.routers.auth.MicrosoftOAuth.get_user_info")
def test_microsoft_create_user_with_id(mock_microsoft: MagicMock) -> None:
    mock_microsoft.return_value = {
        "mail": "<EMAIL>",
        "givenName": "Test2",
        "surname": "User2",
        "id": "**********",
    }

    response = client.post("/api/v2/auth/microsoft-signin", json={"access_token": "123"})

    assert response.status_code == status.HTTP_403_FORBIDDEN
    user = User.objects.get(email="<EMAIL>")
    assert user.username == "<EMAIL>"
    assert user.first_name == "Test2"
    assert user.last_name == "User2"
    assert user.name == "Test2 User2"
    assert not user.google_id
    assert user.microsoft_id == "**********"
    mock_microsoft.assert_called_once_with("123")


@patch("api.routers.auth.MicrosoftOAuth.get_user_info")
def test_microsoft_create_user_missing_name(mock_microsoft: MagicMock) -> None:
    mock_microsoft.return_value = {"mail": "<EMAIL>"}

    response = client.post("/api/v2/auth/microsoft-signin", json={"access_token": "123"})

    assert response.status_code == status.HTTP_403_FORBIDDEN
    user = User.objects.get(email="<EMAIL>")
    assert user.username == "<EMAIL>"
    assert not user.first_name == "Test2"
    assert not user.last_name == "User2"
    assert user.name == "<EMAIL>"
    mock_microsoft.assert_called_once_with("123")


@patch("api.routers.auth.MicrosoftOAuth.get_user_info")
@patch("api.routers.auth.sync_crm_clients")
@patch("api.routers.auth.reconcile_calendar_events")
def test_microsoft_user_waitlisted(
    mock_reconcile_calendar_events: MagicMock,
    mock_sync_crm_clients: MagicMock,
    mock_microsoft: MagicMock,
    test_user: User,
) -> None:
    test_user.status = User.STATUS_CHOICES.waitlisted
    test_user.save()

    mock_microsoft.return_value = {
        "mail": "<EMAIL>",
        "givenName": "Test",
        "surname": "User",
    }

    response = client.post("/api/v2/auth/microsoft-signin", json={"access_token": "123"})
    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {"detail": "Account verification required"}
    mock_microsoft.assert_called_once_with("123")
    mock_sync_crm_clients.delay.assert_not_called()
    mock_reconcile_calendar_events.delay.assert_not_called()


@patch("api.routers.auth.MicrosoftOAuth.get_user_info")
@patch("api.routers.auth.sync_crm_clients")
@patch("api.routers.auth.reconcile_calendar_events")
def test_active_microsoft_user(
    mock_reconcile_calendar_events: MagicMock,
    mock_sync_crm_clients: MagicMock,
    mock_microsoft: MagicMock,
    test_user: User,
) -> None:
    test_user.status = User.STATUS_CHOICES.active
    test_user.save()

    mock_microsoft.return_value = {
        "mail": "<EMAIL>",
        "givenName": "Test",
        "surname": "User",
        "id": "**********",
    }

    response = client.post("/api/v2/auth/microsoft-signin", json={"access_token": "123"})

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    test_user.refresh_from_db()
    assert test_user.microsoft_id == "**********"
    assert "access_token" in data
    assert "refresh_token" in data
    assert data.get("user_profile") == {
        "uuid": str(test_user.uuid),
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "User",
        "is_active": True,
        "status": "active",
        "org_id": None,
        "license_type": UserLicenseType.advisor,
    }
    mock_microsoft.assert_called_once_with("123")
    mock_sync_crm_clients.delay.assert_called_once_with(test_user.uuid)
    mock_reconcile_calendar_events.delay.assert_called_once_with(test_user.uuid)


@patch("api.routers.auth.MicrosoftOAuth.get_user_info")
def test_existing_microsoft_user_no_new_id(mock_microsoft: MagicMock, test_user: User) -> None:
    test_user.status = User.STATUS_CHOICES.active
    test_user.microsoft_id = "**********"
    test_user.save()

    mock_microsoft.return_value = {
        "mail": "<EMAIL>",
        "givenName": "Test",
        "surname": "User",
    }

    response = client.post("/api/v2/auth/microsoft-signin", json={"access_token": "123"})

    assert response.status_code == status.HTTP_200_OK
    test_user.refresh_from_db()
    assert test_user.microsoft_id == "**********"


@patch("api.routers.auth.MicrosoftOAuth.get_user_info")
def test_microsoft_signin_with_organization(
    mock_get_user_info: MagicMock, test_user: User, test_organization: Organization
) -> None:
    test_user.organization = test_organization
    test_user.save()

    mock_get_user_info.return_value = {
        "mail": "<EMAIL>",
        "givenName": "Test",
        "surname": "User",
    }

    response = client.post("/api/v2/auth/microsoft-signin", json={"access_token": "123"})

    assert response.status_code == status.HTTP_200_OK
    user_profile = response.json().get("user_profile", {})
    assert user_profile.get("org_id") == str(test_organization.id)
    assert user_profile.get("uuid") == str(test_user.uuid)
    mock_get_user_info.assert_called_once_with("123")


@pytest.mark.parametrize(
    "license_type, expected_license_type",
    [
        (User.LicenseType.advisor, UserLicenseType.advisor),
        (User.LicenseType.csa, UserLicenseType.csa),
        (User.LicenseType.staff, UserLicenseType.staff),
    ],
)
@patch("api.routers.auth.MicrosoftOAuth.get_user_info")
@patch("api.routers.auth.sync_crm_clients")
@patch("api.routers.auth.reconcile_calendar_events")
def test_microsoft_signin_license_type(
    mock_reconcile_calendar_events: MagicMock,
    mock_sync_crm_clients: MagicMock,
    mock_microsoft: MagicMock,
    test_user: User,
    license_type: User.LicenseType,
    expected_license_type: UserLicenseType,
) -> None:
    test_user.license_type = license_type
    test_user.save()

    mock_microsoft.return_value = {
        "mail": "<EMAIL>",
        "givenName": "Test",
        "surname": "User",
        "id": "**********",
    }

    response = client.post("/api/v2/auth/microsoft-signin", json={"access_token": "123"})

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data.get("user_profile").get("license_type") == expected_license_type


def test_zoom_auth_invalid_parameters() -> None:
    response = client.get("/api/v2/auth/zoomauth?")
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.parametrize(
    "zoom_status, expected",
    [
        (ZoomStatus.SUCCESS, "true"),
        (ZoomStatus.FAILURE, "false"),
        (ZoomStatus.RETRY_REQUIRED, "retryRequired"),
    ],
)
@patch("api.routers.auth.RecallBotController")
def test_zoom_auth_status(mock_bot_controller: MagicMock, zoom_status: ZoomStatus, expected: str) -> None:
    mock_bot_controller.register_user_oauth_credential.return_value = zoom_status
    response = client.get("/api/v2/auth/zoomauth?code=some_code", follow_redirects=False)
    assert response.status_code == status.HTTP_303_SEE_OTHER
    assert f"integration=Zoom&integrationStatus={expected}" in response.headers["location"]
    mock_bot_controller.register_user_oauth_credential.assert_called_once_with("some_code")


@patch("api.routers.auth.RecallBotController")
def test_zoom_auth_error(mock_bot_controller: MagicMock) -> None:
    mock_bot_controller.register_user_oauth_credential.side_effect = Exception()
    response = client.get("/api/v2/auth/zoomauth?code=some_code", follow_redirects=False)
    assert response.status_code == status.HTTP_303_SEE_OTHER
    assert "integration=Zoom&integrationStatus=false" in response.headers["location"]
    mock_bot_controller.register_user_oauth_credential.assert_called_once_with("some_code")


def test_refresh_tokens_invalid_parameters() -> None:
    assert client.post("/api/v2/auth/refresh_tokens?").status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


def test_refresh_tokens_invalid() -> None:
    assert client.post("/api/v2/auth/refresh_tokens?refresh_token=invalid").status_code == status.HTTP_401_UNAUTHORIZED


@patch("api.routers.auth.TokenRefreshSerializer")
def test_refresh_tokens_server_error(mock_token_serializer: MagicMock) -> None:
    mock_token_serializer.side_effect = Exception()

    assert (
        client.post("/api/v2/auth/refresh_tokens?refresh_token=not_relevant").status_code
        == status.HTTP_500_INTERNAL_SERVER_ERROR
    )


@patch("api.routers.auth.TokenRefreshSerializer")
def test_refresh_tokens_invalid_token_serializer(mock_token_serializer: MagicMock) -> None:
    mock_token_serializer.return_value.is_valid.return_value = False

    assert (
        client.post("/api/v2/auth/refresh_tokens?refresh_token=not_relevant").status_code
        == status.HTTP_401_UNAUTHORIZED
    )


def test_refresh_tokens_valid(test_user: User) -> None:
    token = RefreshToken.for_user(user=test_user)

    response = client.post(f"/api/v2/auth/refresh_tokens?refresh_token={str(token)}")

    assert response.status_code == status.HTTP_200_OK

    data = response.json()
    assert not data.get("refresh_token")
    assert test_user.pk == user_from_access_token(MagicMock(app=app), data.get("access_token")).pk


@pytest.fixture
def superuser(django_user_model: User) -> User:
    return django_user_model.objects.create_user(
        username="<EMAIL>",
        email="<EMAIL>",
        password="adminpass",
        first_name="Admin",
        last_name="User",
        is_active=True,
        is_superuser=True,
        status=User.STATUS_CHOICES.active,
    )


@pytest.fixture
def regular_user(django_user_model: User) -> User:
    return django_user_model.objects.create_user(
        username="<EMAIL>",
        email="<EMAIL>",
        password="userpass",
        first_name="Regular",
        last_name="User",
        is_active=True,
        status=User.STATUS_CHOICES.active,
    )


@pytest.fixture
def auth_headers(superuser: User) -> dict[str, str]:
    response = client.post("/api/v2/auth/login", json={"email": superuser.email, "password": "adminpass"})
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


class TestImpersonateUser:
    @patch("api.routers.auth.Flags.EnableUserImpersonation.is_active_for_user")
    def test_impersonate_user_flag_disabled(
        self, mock_flag: MagicMock, auth_headers: dict[str, str], regular_user: User
    ) -> None:
        mock_flag.return_value = False

        response = client.post(
            "/api/v2/auth/impersonate_user",
            params={"username": regular_user.username, "purpose": "Testing", "ttl_seconds": 300},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert UserImpersonation.objects.count() == 0
        mock_flag.assert_called_once()

    def test_impersonate_user_not_superuser(self, regular_user: User) -> None:
        login_response = client.post(
            "/api/v2/auth/login", json={"email": regular_user.username, "password": "userpass"}
        )
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}

        response = client.post(
            "/api/v2/auth/impersonate_user",
            params={"username": regular_user.username, "purpose": "Testing", "ttl_seconds": 300},
            headers=headers,
        )

        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert UserImpersonation.objects.count() == 0

    def test_impersonate_nonexistent_user(self, auth_headers: dict[str, str]) -> None:
        response = client.post(
            "/api/v2/auth/impersonate_user",
            params={"username": "<EMAIL>", "purpose": "Testing", "ttl_seconds": 300},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert UserImpersonation.objects.count() == 0

    @patch("api.routers.auth.Flags.EnableUserImpersonation.is_active_for_user")
    @patch("api.routers.auth.RefreshToken.blacklist")
    def test_impersonate_user_successful(
        self,
        mock_blacklist: MagicMock,
        mock_flag: MagicMock,
        auth_headers: dict[str, str],
        superuser: User,
        regular_user: User,
    ) -> None:
        mock_flag.return_value = True

        response = client.post(
            "/api/v2/auth/impersonate_user",
            params={"username": "<EMAIL>", "purpose": "Testing", "ttl_seconds": 300},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        assert "user_profile" in data
        assert "refresh_token" in data
        assert "access_token" in data

        assert data["user_profile"]["uuid"] == str(regular_user.uuid)
        assert data["user_profile"]["email"] == regular_user.email
        assert data["refresh_token"] == ""

        impersonation_sessions = UserImpersonation.objects.all()
        assert impersonation_sessions.count() == 1
        session = impersonation_sessions.first()
        assert session is not None
        assert session.user_impersonated == regular_user
        assert session.impersonated_email == regular_user.email
        assert session.user_impersonating == superuser
        assert session.impersonating_email == superuser.email
        assert session.purpose == "Testing"

        mock_blacklist.assert_called_once()

    @patch("api.routers.auth.Flags.EnableUserImpersonation.is_active_for_user")
    @patch("api.routers.auth.AccessToken")
    def test_impersonate_existing_valid_session(
        self,
        mock_access_token: MagicMock,
        mock_flag: MagicMock,
        auth_headers: dict[str, str],
        superuser: User,
        regular_user: User,
    ) -> None:
        mock_flag.return_value = True

        # Mock the token to return a valid expiration time
        mock_token_instance = MagicMock()
        future_time = (datetime.now(timezone.utc) + timedelta(hours=1)).timestamp()
        mock_token_instance.payload.get.return_value = future_time
        mock_access_token.return_value = mock_token_instance

        # Create existing session
        existing_session = UserImpersonation.objects.create(
            user_impersonated=regular_user,
            impersonated_email=regular_user.email,
            user_impersonating=superuser,
            impersonating_email=superuser.email,
            access_token="test_access_token",
            purpose="Previous testing",
        )

        response = client.post(
            "/api/v2/auth/impersonate_user",
            params={"username": regular_user.username, "purpose": "New testing", "ttl_seconds": 300},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        assert data["refresh_token"] == ""
        assert data["access_token"] == "test_access_token"

        # Should still have only one session
        assert UserImpersonation.objects.count() == 1

        # Original session should be unchanged
        existing_session.refresh_from_db()
        assert existing_session.purpose == "Previous testing"

    @patch("api.routers.auth.Flags.EnableUserImpersonation.is_active_for_user")
    @patch("api.routers.auth.AccessToken")
    @patch("api.routers.auth.RefreshToken.blacklist")
    def test_impersonate_existing_expired_session(
        self,
        mock_blacklist: MagicMock,
        mock_access_token: MagicMock,
        mock_flag: MagicMock,
        auth_headers: dict[str, str],
        superuser: User,
        regular_user: User,
    ) -> None:
        mock_flag.return_value = True

        # Mock the token to return an expired time
        mock_token_instance = MagicMock()
        past_time = (datetime.now(timezone.utc) - timedelta(hours=1)).timestamp()
        mock_token_instance.payload.get.return_value = past_time
        mock_access_token.return_value = mock_token_instance

        # Create existing session with expired token
        existing_session = UserImpersonation.objects.create(
            user_impersonated=regular_user,
            impersonated_email=regular_user.email,
            user_impersonating=superuser,
            impersonating_email=superuser.email,
            access_token="expired_access_token",
            purpose="Expired session",
        )

        response = client.post(
            "/api/v2/auth/impersonate_user",
            params={"username": regular_user.username, "purpose": "New testing", "ttl_seconds": 300},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Should have a new access token
        assert data["access_token"] != "expired_access_token"

        # Should now have two sessions
        assert UserImpersonation.objects.count() == 2

        # Original session should be unchanged
        existing_session.refresh_from_db()
        assert existing_session.purpose == "Expired session"
        assert existing_session.access_token == "expired_access_token"

    @patch("api.routers.auth.Flags.EnableUserImpersonation.is_active_for_user")
    @patch("api.routers.auth.RefreshToken.blacklist")
    def test_impersonate_user_max_ttl_limit(
        self,
        mock_blacklist: MagicMock,
        mock_flag: MagicMock,
        auth_headers: dict[str, str],
        superuser: User,
        regular_user: User,
    ) -> None:
        mock_flag.return_value = True

        # Request a TTL that exceeds the maximum (3600 seconds)
        response = client.post(
            "/api/v2/auth/impersonate_user",
            params={"username": regular_user.username, "purpose": "Testing", "ttl_seconds": 7200},  # 2 hours
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK

        # To verify the TTL was capped, we'd need more complex mocking
        # For now, just confirm a session was created
        assert UserImpersonation.objects.count() == 1

    @patch("api.routers.auth.Flags.EnableUserImpersonation.is_active_for_user")
    @patch("api.routers.auth.AccessToken")
    @patch("api.routers.auth.RefreshToken.blacklist")
    def test_impersonate_invalid_token_session(
        self,
        mock_blacklist: MagicMock,
        mock_access_token: MagicMock,
        mock_flag: MagicMock,
        auth_headers: dict[str, str],
        superuser: User,
        regular_user: User,
    ) -> None:
        mock_flag.return_value = True

        # Make the token validation raise an exception
        mock_access_token.side_effect = jwt.PyJWTError("Invalid token")

        # Create existing session
        existing_session = UserImpersonation.objects.create(
            user_impersonated=regular_user,
            impersonated_email=regular_user.email,
            user_impersonating=superuser,
            impersonating_email=superuser.email,
            access_token="invalid_token",
            purpose="Invalid session",
        )

        response = client.post(
            "/api/v2/auth/impersonate_user",
            params={"username": regular_user.username, "purpose": "New testing", "ttl_seconds": 300},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Should have a new access token
        assert data["access_token"] != "invalid_token"

        # Should now have two sessions
        assert UserImpersonation.objects.count() == 2

    @patch("api.routers.auth.Flags.EnableUserImpersonation.is_active_for_user")
    @patch("api.routers.auth.AccessToken")
    @patch("api.routers.auth.RefreshToken.blacklist")
    def test_impersonate_token_error(
        self,
        mock_blacklist: MagicMock,
        mock_access_token: MagicMock,
        mock_flag: MagicMock,
        auth_headers: dict[str, str],
        superuser: User,
        regular_user: User,
    ) -> None:
        mock_flag.return_value = True

        # Make the token validation raise a TokenError
        mock_access_token.side_effect = TokenError("Token error")

        # Create existing session
        existing_session = UserImpersonation.objects.create(
            user_impersonated=regular_user,
            impersonated_email=regular_user.email,
            user_impersonating=superuser,
            impersonating_email=superuser.email,
            access_token="error_token",
            purpose="Error session",
        )

        response = client.post(
            "/api/v2/auth/impersonate_user",
            params={"username": regular_user.username, "purpose": "New testing", "ttl_seconds": 300},
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        # Should have a new access token
        assert data["access_token"] != "error_token"

        # Should now have two sessions
        assert UserImpersonation.objects.count() == 2

    @patch("api.routers.auth.Flags.EnableUserImpersonation.is_active_for_user")
    @patch("api.routers.auth.RefreshToken.blacklist")
    def test_default_ttl_when_not_provided(
        self,
        mock_blacklist: MagicMock,
        mock_flag: MagicMock,
        auth_headers: dict[str, str],
        superuser: User,
        regular_user: User,
    ) -> None:
        mock_flag.return_value = True

        # Don't provide ttl_seconds to test the default
        response = client.post(
            "/api/v2/auth/impersonate_user",
            params={"username": regular_user.username, "purpose": "Testing"},  # No ttl_seconds
            headers=auth_headers,
        )

        assert response.status_code == status.HTTP_200_OK

        # Check a session was created with default TTL
        assert UserImpersonation.objects.count() == 1


@patch("api.routers.auth.default_token_generator")
def test_forgot_password_user_not_found(
    mock_token_generator: MagicMock, test_user: User, mailoutbox: list[EmailMessage]
) -> None:
    response = client.post("/api/v2/auth/forgot_password?email=<EMAIL>")

    assert response.status_code == status.HTTP_200_OK
    mock_token_generator.make_token.assert_not_called()
    assert not mailoutbox


@patch("api.routers.auth.default_token_generator")
def test_forgot_password_no_host(
    mock_token_generator: MagicMock, test_user: User, mailoutbox: list[EmailMessage], settings: SettingsWrapper
) -> None:
    settings.APP_DOMAIN = None
    response = client.post(f"/api/v2/auth/forgot_password?email={test_user.username}")

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    mock_token_generator.make_token.assert_not_called()
    assert not mailoutbox


expected_message = """
    Hello,
    <br /><br/ >
    We received a request to reset your password for your Zeplyn account. Click the button below to reset your password.
    <br /><br />
    <a href='https://testapp.com/auth/reset-password?email=test%40example.com&code=test_token'>Reset Password</a>
    <br /><br />
    If you didn't request this password reset, you can safely ignore this email.
    <br /><br />
    Thanks,
    The Zeplyn Team
    """


@patch("api.routers.auth.default_token_generator")
def test_forgot_password_user_found_no_headers(
    mock_token_generator: MagicMock,
    test_user: User,
    mailoutbox: list[EmailMessage],
    settings: SettingsWrapper,
) -> None:
    settings.APP_DOMAIN = "https://testapp.com"
    mock_token_generator.make_token.return_value = "test_token"

    response = client.post(f"/api/v2/auth/forgot_password?email={test_user.username}")

    assert response.status_code == status.HTTP_200_OK
    mock_token_generator.make_token.assert_called_once_with(test_user)
    assert len(mailoutbox) == 1
    message = mailoutbox[0]
    assert message.subject == "[Zeplyn] Password Reset Request"
    assert message.to == [test_user.email]
    assert isinstance(message, EmailMultiAlternatives)
    multi_message: EmailMultiAlternatives = message
    assert multi_message.alternatives == [(expected_message, "text/html")]


@patch("api.routers.auth.default_token_generator")
def test_forgot_password_user_found_with_headers(
    mock_token_generator: MagicMock, test_user: User, mailoutbox: list[EmailMessage], settings: SettingsWrapper
) -> None:
    settings.APP_DOMAIN = "https://testapptwo.com"
    mock_token_generator.make_token.return_value = "test_token"
    headers = {"X-Forwarded-Proto": "https", "X-Forwarded-Host": "testapp.com"}

    response = client.post(f"/api/v2/auth/forgot_password?email={test_user.username}", headers=headers)

    assert response.status_code == status.HTTP_200_OK
    mock_token_generator.make_token.assert_called_once_with(test_user)
    assert len(mailoutbox) == 1
    message = mailoutbox[0]
    assert message.subject == "[Zeplyn] Password Reset Request"
    assert message.to == [test_user.email]
    assert isinstance(message, EmailMultiAlternatives)
    multi_message: EmailMultiAlternatives = message
    assert multi_message.alternatives == [(expected_message, "text/html")]


@patch("api.routers.auth.default_token_generator")
def test_forgot_password_email_casing(
    mock_token_generator: MagicMock, test_user: User, mailoutbox: list[EmailMessage], settings: SettingsWrapper
) -> None:
    settings.APP_DOMAIN = "https://testapp.com"
    mock_token_generator.make_token.return_value = "test_token"

    response = client.post(f"/api/v2/auth/forgot_password?email={test_user.username.upper()}")

    assert response.status_code == status.HTTP_200_OK
    mock_token_generator.make_token.assert_called_once_with(test_user)
    assert len(mailoutbox) == 1
    message = mailoutbox[0]
    assert isinstance(message, EmailMultiAlternatives)
    multi_message: EmailMultiAlternatives = message
    assert multi_message.alternatives == [(expected_message, "text/html")]


@patch("api.routers.auth.default_token_generator")
def test_reset_password_user_not_found(mock_token_generator: MagicMock, test_user: User) -> None:
    response = client.post(
        "/api/v2/auth/reset_password?email=<EMAIL>&code=test_code&new_password=newpass"
    )

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "User not found"}
    mock_token_generator.check_token.assert_not_called()


@patch("api.routers.auth.default_token_generator")
def test_reset_password_invalid_token(mock_token_generator: MagicMock, test_user: User) -> None:
    mock_token_generator.check_token.return_value = False

    response = client.post(
        f"/api/v2/auth/reset_password?email={test_user.username}&code=invalid_code&new_password=newpass"
    )

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {"detail": "Invalid token"}
    mock_token_generator.check_token.assert_called_once_with(test_user, "invalid_code")
    test_user.refresh_from_db()
    assert test_user.check_password("123")


@patch("api.routers.auth.default_token_generator")
def test_reset_password_successful(mock_token_generator: MagicMock, test_user: User) -> None:
    mock_token_generator.check_token.return_value = True

    response = client.post(
        f"/api/v2/auth/reset_password?email={test_user.username}&code=valid_code&new_password=newpass"
    )

    assert response.status_code == status.HTTP_200_OK
    mock_token_generator.check_token.assert_called_once_with(test_user, "valid_code")
    test_user.refresh_from_db()
    assert test_user.check_password("newpass")
    assert not test_user.check_password("123")  # Old password should not work


@patch("api.routers.auth.default_token_generator")
def test_reset_password_email_casing(mock_token_generator: MagicMock, test_user: User) -> None:
    mock_token_generator.check_token.return_value = True

    response = client.post(
        f"/api/v2/auth/reset_password?email={test_user.username.upper()}&code=valid_code&new_password=newpass"
    )

    assert response.status_code == status.HTTP_200_OK
    mock_token_generator.check_token.assert_called_once_with(test_user, "valid_code")
    test_user.refresh_from_db()
    assert test_user.check_password("newpass")
