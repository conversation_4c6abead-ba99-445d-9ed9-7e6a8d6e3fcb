import ast
import csv
import io
import logging
import uuid
import zipfile
from datetime import date, datetime, timedelta
from datetime import timezone as datetime_timezone
from decimal import Decimal
from typing import Any, Generator
from unittest.mock import MagicMock, call, patch

import pytest
from django.core.cache import cache
from django.utils import timezone
from fastapi import status
from fastapi.testclient import TestClient

from api.dependencies import user_from_authorization_header
from api.internal_api import internal_api as app
from api.routers.insights import InsightsDashboardResponse, _insights_data_cache_key
from deepinsights.core.integrations.calendar.calendar_models import EventParticipant, EventParticipantsList
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.client_interaction import ClientInteraction
from deepinsights.meetingsapp.models.client_recap import ClientRecap, ClientRecapStatus
from deepinsights.meetingsapp.models.meeting_bot import MeetingBot
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingData
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.subscription_plan import EntitlementType
from deepinsights.users.models.user import User

client = TestClient(app)

pytestmark = [pytest.mark.django_db(transaction=True)]


@pytest.fixture
def test_organization() -> Organization:
    return Organization.objects.create(name="Test Org")


@pytest.fixture
def test_user(django_user_model: User, test_organization: Organization) -> User:
    test_user = django_user_model.objects.create(
        username="<EMAIL>",
        email="<EMAIL>",
        name="Test User",
        organization=test_organization,
    )
    app.dependency_overrides[user_from_authorization_header] = lambda: test_user
    return test_user


@pytest.fixture
def other_user(django_user_model: User, test_organization: Organization) -> User:
    return django_user_model.objects.create(
        username="<EMAIL>",
        email="<EMAIL>",
        name="Other User",
        organization=test_organization,
    )


@pytest.fixture
def admin_user(django_user_model: User, test_organization: Organization) -> User:
    return django_user_model.objects.create(
        username="<EMAIL>",
        email="<EMAIL>",
        name="Admin User",
        organization=test_organization,
        entitlements=[EntitlementType.organization_admin.value],
    )


@pytest.fixture
def other_organization() -> Organization:
    return Organization.objects.create(name="Other Org")


@pytest.fixture
def other_org_user(django_user_model: User, other_organization: Organization) -> User:
    return django_user_model.objects.create(
        username="<EMAIL>",
        organization=other_organization,
    )


@pytest.fixture
def no_org_user(django_user_model: User) -> User:
    return django_user_model.objects.create(username="<EMAIL>")


@pytest.fixture
def test_client_obj(test_organization: Organization, test_user: User) -> Client:
    client_obj = Client.objects.create(
        name="Test Client",
        email="<EMAIL>",
        crm_system="test_crm",
        organization=test_organization,
        life_phase=Client.LifePhase.accumulation,
        assets_under_management=Decimal("1000000.00"),
        segment=Client.Segment.gold,
        onboarding_date=date(2023, 1, 1),
        is_priority=True,
    )
    client_obj.authorized_users.add(test_user)
    client_obj.save()
    return client_obj


@pytest.fixture
def second_test_client(test_organization: Organization, test_user: User) -> Client:
    client_obj = Client.objects.create(
        name="Second Test Client",
        crm_system="other_test_crm",
        email="<EMAIL>",
        organization=test_organization,
    )
    client_obj.authorized_users.add(test_user)
    client_obj.save()
    return client_obj


class TestGetInsightsData:
    @pytest.fixture(autouse=True)
    def setup_teardown(self) -> Generator[Any, None, None]:
        app.dependency_overrides = {}
        cache.clear()
        yield
        app.dependency_overrides = {}
        cache.clear()

    def _response_data_dict_subset(
        self, response_data_list: list[dict[str, Any]], keys: set[str]
    ) -> list[dict[str, Any]]:
        """Given a list of response data dictionaries, return a list of those dictionaries with only specified keys."""
        return [{k: v for k, v in d.items() if k in keys} for d in response_data_list]

    @pytest.fixture(autouse=True)
    def mock_enabled_insights_flag(self) -> Generator[MagicMock, None, None]:
        with patch("api.routers.insights.Flags.EnableInsightsAPI.is_active_for_user") as mock_flag:
            mock_flag.return_value = True
            yield mock_flag
        mock_flag.return_value = False

    def test_unauthenticated(self) -> None:
        response = client.get("/api/v2/insights/data")
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert response.json() == {"detail": "Not authenticated"}

    def test_flag_disabled(self, test_user: User, mock_enabled_insights_flag: MagicMock) -> None:
        mock_enabled_insights_flag.return_value = False
        response = client.get("/api/v2/insights/data")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "status": "pending",
            "notes": [],
            "scheduled_events": [],
            "tasks": [],
            "structured_meeting_data": [],
            "users": [],
            "clients": [],
        }

    def test_empty(self, test_user: User) -> None:
        response = client.get("/api/v2/insights/data")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "status": "success",
            "notes": [],
            "scheduled_events": [],
            "tasks": [],
            "structured_meeting_data": [],
            "users": [
                {
                    "uuid": str(test_user.uuid),
                    "email": "<EMAIL>",
                    "name": "Test User",
                    "manager_uuid": None,
                },
            ],
            "clients": [],
        }

    def test_caching(self, test_user: User) -> None:
        response = client.get("/api/v2/insights/data")

        assert response.status_code == status.HTTP_200_OK
        empty_response = {
            "status": "success",
            "notes": [],
            "scheduled_events": [],
            "tasks": [],
            "structured_meeting_data": [],
            "users": [
                {
                    "uuid": str(test_user.uuid),
                    "email": "<EMAIL>",
                    "name": "Test User",
                    "manager_uuid": None,
                },
            ],
            "clients": [],
        }
        assert response.json() == empty_response

        note = Note.objects.create(
            uuid=uuid.uuid4(),
            note_owner=test_user,
        )
        note.authorized_users.add(test_user)
        note.save()

        assert client.get("/api/v2/insights/data").json() == empty_response

    # Realistically testing this is non trivial, since we can't guarantee that a second call (which
    # should return the pending response) will happen before the first call populates the cache with
    # a completed response. So, we mock the cache and confirm that it is updated how we expect it to
    # be updated.
    @patch("api.routers.insights.cache")
    def test_pending_response(self, mock_cache: MagicMock, test_user: User) -> None:
        mock_cache.get.return_value = None
        response = client.get("/api/v2/insights/data")

        assert response.status_code == status.HTTP_200_OK
        key = _insights_data_cache_key(test_user)
        mock_cache.assert_has_calls(
            [
                call.get(key),
                call.set(
                    key,
                    {
                        "status": "pending",
                        "notes": [],
                        "scheduled_events": [],
                        "tasks": [],
                        "structured_meeting_data": [],
                        "users": [],
                        "clients": [],
                    },
                    timeout=60,
                ),
                call.set(
                    key,
                    {
                        "status": "success",
                        "notes": [],
                        "scheduled_events": [],
                        "tasks": [],
                        "structured_meeting_data": [],
                        "users": [
                            {
                                "uuid": str(test_user.uuid),
                                "email": "<EMAIL>",
                                "name": "Test User",
                                "manager_uuid": None,
                            },
                        ],
                        "clients": [],
                    },
                    timeout=3600,
                ),
            ]
        )

    def test_invalid_cached_data(self, test_user: User, caplog: pytest.LogCaptureFixture) -> None:
        cache_key = _insights_data_cache_key(test_user)
        cache.set(cache_key, {"invalid": "data"}, timeout=3600)

        with caplog.at_level(logging.ERROR):
            response = client.get("/api/v2/insights/data")

        assert len(caplog.records) == 1
        assert caplog.messages[0] == "Cached insights dashboard data is invalid, regenerating"

        assert response.status_code == status.HTTP_200_OK
        assert InsightsDashboardResponse.model_validate(response.json())

    @pytest.mark.parametrize(
        "client_dict",
        [
            None,
            {"uuid": None},
            {"uuid": ""},
        ],
    )
    def test_empty_or_invalid_note_client(self, test_user: User, client_dict: dict[str, Any] | None) -> None:
        note = Note.objects.create(
            note_owner=test_user,
            client=client_dict,
        )
        note.authorized_users.add(test_user)
        note.save()

        response = client.get("/api/v2/insights/data")

        assert response.status_code == status.HTTP_200_OK

        assert response.json()["notes"][0]["client_uuids"] == []

    def test_notes(
        self,
        test_user: User,
        test_client_obj: Client,
        second_test_client: Client,
        other_user: User,
    ) -> None:
        note_scheduled_date = timezone.now() - timedelta(hours=1)
        note = Note.objects.create(
            note_owner=test_user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.processed,
            data_source=Note.DataSource.AUDIO_FILE,
            metadata={
                "meeting_duration": 3600,
                "scheduled_at": note_scheduled_date.isoformat(),
                "tags": ["important", "client-meeting"],
            },
            follow_up_email_contents="Follow up email content",
            client={"uuid": str(test_client_obj.uuid)},
        )
        note.authorized_users.add(test_user, other_user)
        note.save()

        Attendee.objects.create(note=note, client=second_test_client)
        Attendee.objects.create(note=note)
        Attendee.objects.create(note=note, user=test_user)

        scheduled_event = ScheduledEvent.objects.create(
            user=test_user,
            start_time=note_scheduled_date,
            end_time=timezone.now(),
            note=note,
        )

        second_note = Note.objects.create(
            note_owner=test_user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.finalized,
            data_source=Note.DataSource.RECALL,
        )
        second_note.authorized_users.add(test_user)
        second_note.save()

        ignored_invalid_data_note = Note.objects.create(
            note_owner=other_user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.uploaded,
            data_source=Note.DataSource.TWILIO,
            client={"uuid": "invalid-uuid-format"},  # Invalid UUID format
            metadata={
                "meeting_duration": "invalid_duration",  # Invalid duration
                "tags": "not_a_list",  # Invalid tags format
            },
        )
        ignored_invalid_data_note.authorized_users.add(test_user)
        ignored_invalid_data_note.save()

        other_user_note = Note.objects.create(
            note_owner=other_user,
        )
        other_user_note.authorized_users.add(other_user)
        other_user_note.save()

        response = client.get("/api/v2/insights/data")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert not data["tasks"]
        assert not data["structured_meeting_data"]
        assert data["notes"] == [
            {
                "meeting_uuid": str(note.uuid),
                "authorized_user_uuids": [str(test_user.uuid), str(other_user.uuid)],
                "source": "Mic",
                "duration_seconds": 3600,
                "status": "processed",
                "client_uuids": [str(second_test_client.uuid), str(test_client_obj.uuid)],
                "meeting_prep_generated": False,
                "follow_up_generated": True,
                "scheduled_event_uuid": str(scheduled_event.uuid),
                "tags": [{"name": "important", "source": "unknown"}, {"name": "client-meeting", "source": "unknown"}],
                "start_time": note_scheduled_date.isoformat().replace("+00:00", "Z"),
            },
            {
                "meeting_uuid": str(second_note.uuid),
                "authorized_user_uuids": [str(test_user.uuid)],
                "source": "Notetaker",
                "duration_seconds": None,
                "status": "finalized",
                "client_uuids": [],
                "meeting_prep_generated": False,
                "follow_up_generated": False,
                "scheduled_event_uuid": None,
                "tags": [],
                "start_time": second_note.created.isoformat().replace("+00:00", "Z"),
            },
            {
                "meeting_uuid": str(ignored_invalid_data_note.uuid),
                "authorized_user_uuids": [str(test_user.uuid)],
                "source": "Phone call",
                "duration_seconds": None,
                "status": "uploaded",
                "client_uuids": [],
                "meeting_prep_generated": False,
                "follow_up_generated": False,
                "scheduled_event_uuid": None,
                "tags": [],
                "start_time": ignored_invalid_data_note.created.isoformat().replace("+00:00", "Z"),
            },
        ]

    @pytest.mark.parametrize(
        "data_source, bot_link, expected_source",
        [
            (Note.DataSource.AUDIO_BUFFERS, None, "Mic"),
            (Note.DataSource.AUDIO_FILE, None, "Mic"),
            (Note.DataSource.RECALL, None, "Notetaker"),
            (Note.DataSource.TWILIO, None, "Phone call"),
            (Note.DataSource.AUDIO_BUFFERS, "https://zoom.us/j/123456789", "Mic"),
            (None, None, "Notetaker"),  # Since there is a bot, the fallback behavior is to assume Notetaker
            (None, "https://zoom.us/j/123456789", "Notetaker"),
            (None, "+12125551212", "Phone call"),
        ],
    )
    def test_notes_meeting_source(
        self,
        test_user: User,
        data_source: Note.DataSource | None,
        bot_link: str | None,
        expected_source: str,
    ) -> None:
        note = Note.objects.create(data_source=data_source)
        note.authorized_users.add(test_user)
        note.save()

        MeetingBot.objects.create(
            note=note,
            meeting_link=bot_link,
        )

        response = client.get("/api/v2/insights/data")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        assert data["notes"][0]["source"] == expected_source

    def test_notes_meeting_prep_generated(self, test_user: User) -> None:
        reference_date = datetime(2023, 1, 1)

        note_with_pregenerated_prep = Note.objects.create(note_owner=test_user)
        note_with_pregenerated_prep.authorized_users.add(test_user)
        note_with_pregenerated_prep.save()

        ScheduledEvent.objects.create(
            user=test_user,
            start_time=reference_date,
            end_time=reference_date + timedelta(hours=1),
            note=note_with_pregenerated_prep,
        )
        ClientInteraction.objects.create(
            note=note_with_pregenerated_prep,
            created=reference_date - timedelta(minutes=60),
            meeting_type=MeetingType.objects.all()[0],
            agenda=StructuredMeetingData.objects.create(
                kind="test",
                data={"test": "data"},
            ),
            advisor_notes=StructuredMeetingData.objects.create(
                kind="test",
                data={"test": "data"},
            ),
        )

        note_with_late_pregenerated_prep = Note.objects.create(note_owner=test_user)
        note_with_late_pregenerated_prep.authorized_users.add(test_user)
        note_with_late_pregenerated_prep.save()
        ScheduledEvent.objects.create(
            user=test_user,
            start_time=reference_date,
            end_time=reference_date + timedelta(hours=1),
            note=note_with_late_pregenerated_prep,
        )
        ClientInteraction.objects.create(
            note=note_with_late_pregenerated_prep,
            created=reference_date,
            meeting_type=MeetingType.objects.all()[0],
            agenda=StructuredMeetingData.objects.create(
                kind="test",
                data={"test": "data"},
            ),
            advisor_notes=StructuredMeetingData.objects.create(
                kind="test",
                data={"test": "data"},
            ),
        )

        note_with_prep_generated_after_note = Note.objects.create(note_owner=test_user)
        note_with_prep_generated_after_note.authorized_users.add(test_user)
        note_with_prep_generated_after_note.save()
        ScheduledEvent.objects.create(
            user=test_user,
            start_time=reference_date,
            end_time=reference_date + timedelta(hours=1),
            note=note_with_prep_generated_after_note,
        )
        ClientInteraction.objects.create(
            note=note_with_prep_generated_after_note,
            created=reference_date + timedelta(hours=2),
            meeting_type=MeetingType.objects.all()[0],
            agenda=StructuredMeetingData.objects.create(
                kind="test",
                data={"test": "data"},
            ),
            advisor_notes=StructuredMeetingData.objects.create(
                kind="test",
                data={"test": "data"},
            ),
        )

        response = client.get("/api/v2/insights/data")

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        assert self._response_data_dict_subset(data["notes"], {"meeting_uuid", "meeting_prep_generated"}) == [
            {"meeting_uuid": str(note_with_pregenerated_prep.uuid), "meeting_prep_generated": True},
            {"meeting_uuid": str(note_with_late_pregenerated_prep.uuid), "meeting_prep_generated": False},
            {"meeting_uuid": str(note_with_prep_generated_after_note.uuid), "meeting_prep_generated": False},
        ]

    def test_notes_has_client_recap(
        self,
        test_user: User,
        test_client_obj: Client,
        second_test_client: Client,
        test_organization: Organization,
        django_user_model: User,
    ) -> None:
        ClientRecap.objects.create(
            client=test_client_obj,
            status=ClientRecapStatus.PROCESSED,
        )
        ClientRecap.objects.create(
            client=second_test_client,
            status=ClientRecapStatus.PROCESSING,
        )

        multi_recap_client = Client.objects.create(
            name="Multi recap Client",
            email="<EMAIL>",
            organization=test_organization,
        )
        multi_recap_client.authorized_users.add(test_user)
        multi_recap_client.save()
        ClientRecap.objects.create(
            client=multi_recap_client,
            status=ClientRecapStatus.CREATED,
        )
        ClientRecap.objects.create(
            client=multi_recap_client,
            status=ClientRecapStatus.FAILED,
        )
        ClientRecap.objects.create(
            client=multi_recap_client,
            status=ClientRecapStatus.PROCESSED,
        )

        no_recap_client = Client.objects.create(
            name="No recap Client",
            email="<EMAIL>",
            organization=test_organization,
        )
        no_recap_client.authorized_users.add(test_user)
        no_recap_client.save()

        response = client.get("/api/v2/insights/data")
        assert response.status_code == status.HTTP_200_OK
        assert self._response_data_dict_subset(response.json()["clients"], {"uuid", "has_client_recap"}) == [
            {
                "uuid": str(no_recap_client.uuid),
                "has_client_recap": False,
            },
            {
                "uuid": str(multi_recap_client.uuid),
                "has_client_recap": True,
            },
            {
                "uuid": str(second_test_client.uuid),
                "has_client_recap": False,
            },
            {
                "uuid": str(test_client_obj.uuid),
                "has_client_recap": True,
            },
        ]

    def test_notes_for_admin(
        self,
        admin_user: User,
        test_user: User,
        other_user: User,
        other_org_user: User,
        no_org_user: User,
    ) -> None:
        # Notes that should be included
        test_user_note = Note.objects.create(
            note_owner=test_user,
        )
        test_user_note.authorized_users.add(test_user)
        test_user_note.save()

        no_authorized_users_org_note = Note.objects.create(note_owner=other_user)

        admin_authorized_user_org_note = Note.objects.create(
            note_owner=admin_user,
        )
        admin_authorized_user_org_note.authorized_users.add(admin_user)
        admin_authorized_user_org_note.save()

        # Notes that should not be included

        other_org_note = Note.objects.create(note_owner=other_org_user)

        other_org_note_with_authorized_user = Note.objects.create(
            note_owner=other_org_user,
        )
        other_org_note_with_authorized_user.authorized_users.add(test_user)
        other_org_note_with_authorized_user.save()

        no_org_note = Note.objects.create(note_owner=no_org_user)

        # Act

        app.dependency_overrides[user_from_authorization_header] = lambda: admin_user
        response = client.get("/api/v2/insights/data")

        assert response.status_code == status.HTTP_200_OK
        assert [n["meeting_uuid"] for n in response.json()["notes"]] == [
            str(test_user_note.uuid),
            str(no_authorized_users_org_note.uuid),
            str(admin_authorized_user_org_note.uuid),
        ]

    def test_scheduled_events(self, test_user: User, other_user: User) -> None:
        participants_data = EventParticipantsList(
            root=[
                EventParticipant(
                    id="1",
                    email_address="<EMAIL>",
                    name="Test Client",
                    zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                ),
                EventParticipant(
                    id="2",
                    email_address="<EMAIL>",
                    name="Test User",
                    zeplyn_kind=EventParticipant.ZeplynKind.USER,
                ),
            ]
        )
        no_client_participants_data = EventParticipantsList(
            root=[
                EventParticipant(
                    id="1",
                    email_address="<EMAIL>",
                    name="Other User",
                    zeplyn_kind=EventParticipant.ZeplynKind.USER,
                )
            ]
        )

        scheduled_event = ScheduledEvent.objects.create(
            user=test_user,
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2),
            attendee_matched_participants=participants_data.model_dump(mode="json"),
        )

        no_client_event = ScheduledEvent.objects.create(
            user=test_user,
            start_time=timezone.now() + timedelta(days=1),
            end_time=timezone.now() + timedelta(days=1, hours=1),
            attendee_matched_participants=no_client_participants_data.model_dump(mode="json"),
        )

        no_matched_attendees_event = ScheduledEvent.objects.create(
            user=test_user,
            start_time=timezone.now() + timedelta(days=1),
            end_time=timezone.now() + timedelta(days=1, hours=1),
        )

        invalid_matched_attendees_event = ScheduledEvent.objects.create(
            user=test_user,
            start_time=timezone.now() + timedelta(days=1),
            end_time=timezone.now() + timedelta(days=1, hours=1),
            attendee_matched_participants={"invalid": "data"},  # Invalid data
        )

        deleted_from_provider_event = ScheduledEvent.objects.create(
            user=test_user,
            start_time=timezone.now() + timedelta(days=1),
            end_time=timezone.now() + timedelta(days=1, hours=1),
            removed_from_provider=True,
        )

        other_user_event = ScheduledEvent.objects.create(
            user=other_user,
            start_time=timezone.now() + timedelta(days=2),
            end_time=timezone.now() + timedelta(days=2, hours=1),
        )

        response = client.get("/api/v2/insights/data")

        assert response.status_code == status.HTTP_200_OK

        assert response.json()["scheduled_events"] == [
            {
                "scheduled_event_uuid": str(scheduled_event.uuid),
                "user_uuid": str(test_user.uuid),
                "has_clients": True,
            },
            {
                "scheduled_event_uuid": str(no_client_event.uuid),
                "user_uuid": str(test_user.uuid),
                "has_clients": False,
            },
            {
                "scheduled_event_uuid": str(no_matched_attendees_event.uuid),
                "user_uuid": str(test_user.uuid),
                "has_clients": False,
            },
            {
                "scheduled_event_uuid": str(invalid_matched_attendees_event.uuid),
                "user_uuid": str(test_user.uuid),
                "has_clients": False,
            },
        ]

    def test_scheduled_events_for_admin(
        self, test_user: User, admin_user: User, other_org_user: User, no_org_user: User, other_user: User
    ) -> None:
        # Scheduled events that should be included
        test_user_event = ScheduledEvent.objects.create(
            user=test_user,
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2),
        )
        other_user_event = ScheduledEvent.objects.create(
            user=other_user,
            start_time=timezone.now() + timedelta(days=1),
            end_time=timezone.now() + timedelta(days=1, hours=1),
        )
        admin_user_event = ScheduledEvent.objects.create(
            user=admin_user,
            start_time=timezone.now() + timedelta(days=2),
            end_time=timezone.now() + timedelta(days=2, hours=1),
        )

        # Scheduled events that should not be included
        other_org_event = ScheduledEvent.objects.create(
            user=other_org_user,
            start_time=timezone.now() + timedelta(days=3),
            end_time=timezone.now() + timedelta(days=3, hours=1),
        )

        no_org_event = ScheduledEvent.objects.create(
            user=no_org_user,
            start_time=timezone.now() + timedelta(days=4),
            end_time=timezone.now() + timedelta(days=4, hours=1),
        )

        app.dependency_overrides[user_from_authorization_header] = lambda: admin_user
        response = client.get("/api/v2/insights/data")

        assert response.status_code == status.HTTP_200_OK
        assert [e["scheduled_event_uuid"] for e in response.json()["scheduled_events"]] == [
            str(test_user_event.uuid),
            str(other_user_event.uuid),
            str(admin_user_event.uuid),
        ]

    def test_tasks(self, test_user: User, admin_user: User) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)

        task = Task.objects.create(
            task_title="Test Task",
            task_desc="Test Description",
            completed=False,
            task_owner=test_user,
            note=note,
            assignee=test_user,
            created=timezone.now() - timedelta(days=1),
        )

        task_two = Task.objects.create(
            task_title="Test Task Two",
            task_desc="Test Description Two",
            completed=True,
            task_owner=admin_user,
            note=note,
            assignee=test_user,
            created=timezone.now() - timedelta(days=2),
        )

        other_assignee_task = Task.objects.create(
            task_title="Other Assignee Task",
            task_desc="Other Assignee Description",
            completed=False,
            task_owner=test_user,
            note=note,
            assignee=admin_user,
            created=timezone.now() - timedelta(days=3),
        )

        other_user_task = Task.objects.create(
            task_title="Other User Task",
            task_desc="Other User Description",
            completed=False,
            task_owner=admin_user,
            note=note,
            assignee=admin_user,
            created=timezone.now() - timedelta(days=4),
        )

        response = client.get("/api/v2/insights/data")

        assert response.status_code == status.HTTP_200_OK

        assert response.json()["tasks"] == [
            {
                "task_uuid": str(other_assignee_task.uuid),
                "assignee_uuid": str(admin_user.uuid),
                "creation_date": other_assignee_task.created.isoformat().replace("+00:00", "Z"),
                "completed": False,
            },
            {
                "task_uuid": str(task_two.uuid),
                "assignee_uuid": str(test_user.uuid),
                "creation_date": task_two.created.isoformat().replace("+00:00", "Z"),
                "completed": True,
            },
            {
                "task_uuid": str(task.uuid),
                "assignee_uuid": str(test_user.uuid),
                "creation_date": task.created.isoformat().replace("+00:00", "Z"),
                "completed": False,
            },
        ]

    def test_tasks_for_admin(
        self, test_user: User, admin_user: User, other_org_user: User, no_org_user: User, other_user: User
    ) -> None:
        # Tasks that should be included
        test_user_task = Task.objects.create(
            task_owner=test_user,
            assignee=test_user,
        )
        admin_user_task = Task.objects.create(
            task_owner=admin_user,
            assignee=admin_user,
        )
        assigned_to_other_org_user_task = Task.objects.create(
            task_owner=test_user,
            assignee=other_org_user,
        )
        assigned_from_other_org_user_task = Task.objects.create(
            task_owner=other_org_user,
            assignee=test_user,
        )

        # Tasks that should not be included
        other_org_user_task = Task.objects.create(
            task_owner=other_org_user,
            assignee=other_org_user,
        )
        no_org_user_task = Task.objects.create(
            task_owner=no_org_user,
            assignee=no_org_user,
        )

        app.dependency_overrides[user_from_authorization_header] = lambda: admin_user
        response = client.get("/api/v2/insights/data")
        assert response.status_code == status.HTTP_200_OK
        assert [t["task_uuid"] for t in response.json()["tasks"]] == [
            str(test_user_task.uuid),
            str(admin_user_task.uuid),
            str(assigned_to_other_org_user_task.uuid),
            str(assigned_from_other_org_user_task.uuid),
        ]

    def test_structured_meeting_data(self, test_user: User, other_user: User) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)
        note.save()

        structured_data = StructuredMeetingData.objects.create(
            note=note,
            title="Life Events Review",
            kind="life_events",
            data={
                "review_entries": [
                    {"topic": "Retirement Planning", "discussed": True},
                    {"topic": "Estate Planning", "discussed": False},
                    {"topic": "Tax Strategy", "discussed": True},
                ]
            },
        )

        non_review_items_structured_data = StructuredMeetingData.objects.create(
            note=note,
            title="Agenda",
            kind="non_review_items",
            data={
                "format": "markdown",
                "content": "This is a non-review item.",
            },
        )

        not_authorized_note = Note.objects.create(note_owner=test_user)
        not_authorized_structured_data = StructuredMeetingData.objects.create(
            note=not_authorized_note, kind="life_events", data={}
        )

        no_note_structured_data = StructuredMeetingData.objects.create(kind="life_events", data={})

        other_user_note = Note.objects.create(note_owner=other_user)
        other_user_note.authorized_users.add(other_user)
        other_user_note.save()

        other_user_structured_data = StructuredMeetingData.objects.create(
            note=other_user_note, kind="life_events", data={}
        )

        response = client.get("/api/v2/insights/data")

        assert response.status_code == status.HTTP_200_OK
        assert response.json()["structured_meeting_data"] == [
            {
                "meeting_data_uuid": str(structured_data.uuid),
                "note_uuid": str(note.uuid),
                "title": "Life Events Review",
                "kind": "life_events",
                "items_total": 3,
                "items_completed": 2,
            },
            {
                "meeting_data_uuid": str(non_review_items_structured_data.uuid),
                "note_uuid": str(note.uuid),
                "title": "Agenda",
                "kind": "non_review_items",
                "items_total": 0,
                "items_completed": 0,
            },
        ]

    @pytest.mark.parametrize(
        "data",
        [
            ["not", "a", "dict"],
            {"review_entries": "not a list"},
            {"review_entries": ["not", "a", "dict"]},
            {"review_entries": [{"topic": "Test", "discussed": "not a boolean"}]},
            {"review_entries": [{"topic": "Test", "subentries": "not a list"}]},
        ],
    )
    def test_structured_meeting_data_invalid(
        self, test_user: User, data: Any, caplog: pytest.LogCaptureFixture
    ) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)
        note.save()
        structured_meeting_data = StructuredMeetingData.objects.create(
            note=note, kind="invalid", title="Invalid Data", data=data
        )

        with caplog.at_level(logging.ERROR):
            response = client.get("/api/v2/insights/data")

        assert response.status_code == status.HTTP_200_OK
        assert response.json()["structured_meeting_data"] == [
            {
                "meeting_data_uuid": str(structured_meeting_data.uuid),
                "note_uuid": str(note.uuid),
                "title": "Invalid Data",
                "kind": "invalid",
                "items_total": 0,
                "items_completed": 0,
            }
        ]
        assert len(caplog.records) == 1
        assert (
            caplog.messages[0]
            == f"Error processing structured meeting data {structured_meeting_data.uuid}. Defaulting item counts to 0."
        )

    @pytest.mark.parametrize(
        "review_entries, expected_items_total, expected_items_completed",
        [
            ([], 0, 0),
            ([{"topic": "Test"}], 0, 0),
            ([{"topic": "Test"}, {"topic": "Test2", "discussed": True}], 1, 1),
            ([{"topic": "Test", "discussed": True}], 1, 1),
            ([{"topic": "Test", "discussed": False}], 1, 0),
            ([{"topic": "Test", "discussed": None}], 1, 0),
            ([{"topic": "Test", "discussed": True}, {"topic": "Test2", "discussed": True}], 2, 2),
            ([{"topic": "Test", "discussed": False}, {"topic": "Test2", "discussed": False}], 2, 0),
            (
                [{"topic": "Test", "subentries": [{"topic": "Sub one"}]}],
                0,
                0,
            ),
            (
                [{"topic": "Test", "subentries": [{"topic": "Sub one", "discussed": True}]}],
                1,
                1,
            ),
            (
                [{"topic": "Test", "subentries": [{"topic": "Sub one", "discussed": False}]}],
                1,
                0,
            ),
            (
                [{"topic": "Test", "discussed": True, "subentries": [{"topic": "Sub one", "discussed": False}]}],
                2,
                1,
            ),
            (
                [
                    {
                        "topic": "Test",
                        "discussed": True,
                        "subentries": [
                            {"topic": "Sub one", "discussed": False},
                            {"topic": "Sub two", "discussed": True},
                        ],
                    },
                    {
                        "topic": "Test two",
                        "discussed": False,
                        "subentries": [
                            {"topic": "Sub three", "discussed": True},
                            {"topic": "Sub four", "discussed": False},
                        ],
                    },
                ],
                6,
                3,
            ),
            (
                [
                    {
                        "topic": "Test",
                        "discussed": True,
                        "subentries": [
                            {
                                "topic": "Sub one",
                                "subentries": [
                                    {"topic": "Sub sub one", "discussed": True},
                                    {"topic": "Sub sub two", "discussed": False},
                                ],
                            },
                            {"topic": "Sub two", "discussed": True},
                        ],
                    },
                    {
                        "topic": "Test two",
                        "subentries": [
                            {"topic": "Sub three", "discussed": True},
                            {
                                "topic": "Sub four",
                                "discussed": False,
                                "subentries": [
                                    {"topic": "Sub sub three", "discussed": True},
                                    {"topic": "Sub sub four", "discussed": False},
                                ],
                            },
                        ],
                    },
                ],
                8,
                5,
            ),
        ],
    )
    def test_structured_meeting_data_item_counting(
        self,
        test_user: User,
        review_entries: list[dict[str, Any]],
        expected_items_total: int,
        expected_items_completed: int,
    ) -> None:
        note = Note.objects.create(note_owner=test_user)
        note.authorized_users.add(test_user)
        note.save()
        structured_meeting_data = StructuredMeetingData.objects.create(
            note=note,
            kind="counting",
            title="Counting",
            data={
                "review_entries": review_entries,
            },
        )

        response = client.get("/api/v2/insights/data")

        assert response.status_code == status.HTTP_200_OK
        assert self._response_data_dict_subset(
            response.json()["structured_meeting_data"],
            {
                "items_total",
                "items_completed",
            },
        ) == [{"items_total": expected_items_total, "items_completed": expected_items_completed}]

    def test_structured_meeting_data_for_admin(
        self,
        admin_user: User,
        test_user: User,
        other_user: User,
        other_org_user: User,
        no_org_user: User,
    ) -> None:
        # Structured meeting data that should be included
        admin_note = Note.objects.create(note_owner=admin_user)
        admin_structured_data = StructuredMeetingData.objects.create(
            note=admin_note,
            title="data",
            kind="data",
            data={},
        )

        test_user_note = Note.objects.create(note_owner=test_user)
        test_user_structured_data = StructuredMeetingData.objects.create(
            note=test_user_note,
            title="data",
            kind="data",
            data={},
        )

        other_user_note = Note.objects.create(note_owner=other_user)
        other_user_structured_data = StructuredMeetingData.objects.create(
            note=other_user_note,
            title="data",
            kind="data",
            data={},
        )

        # Structured meeting data that should not be included
        other_org_note = Note.objects.create(note_owner=other_org_user)
        other_org_note.authorized_users.add(test_user)
        other_org_note.save()
        other_org_structured_data = StructuredMeetingData.objects.create(
            note=other_org_note,
            title="data",
            kind="data",
            data={},
        )
        no_org_note = Note.objects.create(note_owner=no_org_user)
        no_org_structured_data = StructuredMeetingData.objects.create(
            note=no_org_note,
            title="data",
            kind="data",
            data={},
        )

        app.dependency_overrides[user_from_authorization_header] = lambda: admin_user
        response = client.get("/api/v2/insights/data")
        assert response.status_code == status.HTTP_200_OK
        assert [s["meeting_data_uuid"] for s in response.json()["structured_meeting_data"]] == [
            str(admin_structured_data.uuid),
            str(test_user_structured_data.uuid),
            str(other_user_structured_data.uuid),
        ]

    @patch("api.routers.insights.Note.objects.filter", side_effect=Exception("Database error"))
    def test_database_error(self, _: MagicMock, test_user: User, caplog: pytest.LogCaptureFixture) -> None:
        with caplog.at_level(logging.ERROR):
            response = client.get("/api/v2/insights/data")

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Error retrieving insights dashboard data" in response.json()["detail"]
        assert len(caplog.records) == 1
        assert caplog.records[0].message == "Error retrieving insights dashboard data"

    def test_users_for_org_admin(
        self, admin_user: User, test_user: User, test_organization: Organization, django_user_model: User
    ) -> None:
        other_user = django_user_model.objects.create(
            username="<EMAIL>",
            email="<EMAIL>",
            manager=test_user,
            organization=test_organization,
            name="Other User",
        )
        other_user_two = django_user_model.objects.create(
            username="<EMAIL>",
            email="<EMAIL>",
            organization=test_organization,
        )
        other_org_user = django_user_model.objects.create(username="<EMAIL>")

        app.dependency_overrides[user_from_authorization_header] = lambda: admin_user
        response = client.get("/api/v2/insights/data")
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["users"] == [
            {
                "uuid": str(other_user_two.uuid),
                "name": "",
                "email": "<EMAIL>",
                "manager_uuid": None,
            },
            {
                "uuid": str(admin_user.uuid),
                "name": "Admin User",
                "email": "<EMAIL>",
                "manager_uuid": None,
            },
            {
                "uuid": str(other_user.uuid),
                "name": "Other User",
                "email": "<EMAIL>",
                "manager_uuid": str(test_user.uuid),
            },
            {
                "uuid": str(test_user.uuid),
                "name": "Test User",
                "email": "<EMAIL>",
                "manager_uuid": None,
            },
        ]

    def test_org_admin_without_org_treated_as_user(
        self, admin_user: User, test_user: User, test_organization: Organization, django_user_model: User
    ) -> None:
        other_user = django_user_model.objects.create(
            username="<EMAIL>",
            email="<EMAIL>",
            manager=test_user,
            organization=test_organization,
            name="Other User",
        )
        other_user_two = django_user_model.objects.create(
            username="<EMAIL>",
            email="<EMAIL>",
            organization=test_organization,
        )
        other_org_user = django_user_model.objects.create(username="<EMAIL>")

        admin_user.organization = None
        admin_user.save()

        app.dependency_overrides[user_from_authorization_header] = lambda: admin_user
        response = client.get("/api/v2/insights/data")
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["users"] == [
            {"uuid": str(admin_user.uuid), "name": "Admin User", "email": "<EMAIL>", "manager_uuid": None}
        ]

    def test_clients_for_org_admin(
        self,
        test_user: User,
        admin_user: User,
        test_client_obj: Client,
        second_test_client: Client,
        test_organization: Organization,
        django_user_model: User,
    ) -> None:
        other_user = django_user_model.objects.create(
            username="<EMAIL>",
            organization=test_organization,
        )
        other_user_client = Client.objects.create(
            name="Other User Client",
            email="<EMAIL>",
            organization=test_organization,
        )
        other_user_client.authorized_users.add(other_user)
        other_user_client.save()

        admin_client = Client.objects.create(
            name="Admin Client",
            email="<EMAIL>",
            organization=test_organization,
        )
        admin_client.authorized_users.add(test_user)
        admin_client.save()

        no_authorized_users_client = Client.objects.create(
            name="No Authorized Users Client",
            email="<EMAIL>",
            organization=test_organization,
        )

        other_org = Organization.objects.create(name="Other Org")
        other_org_client = Client.objects.create(
            name="Other Org Client",
            email="<EMAIL>",
            organization=other_org,
        )
        other_org_client.authorized_users.add(test_user)
        other_org_client.save()

        app.dependency_overrides[user_from_authorization_header] = lambda: admin_user
        response = client.get("/api/v2/insights/data")
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["clients"] == [
            {
                "uuid": str(admin_client.uuid),
                "name": "Admin Client",
                "life_phase": None,
                "assets_under_management": None,
                "segment": None,
                "onboarding_date": None,
                "is_priority": False,
                "has_client_recap": False,
            },
            {
                "uuid": str(other_user_client.uuid),
                "name": "Other User Client",
                "life_phase": None,
                "assets_under_management": None,
                "segment": None,
                "onboarding_date": None,
                "is_priority": False,
                "has_client_recap": False,
            },
            {
                "uuid": str(second_test_client.uuid),
                "name": "Second Test Client",
                "life_phase": None,
                "assets_under_management": None,
                "segment": None,
                "onboarding_date": None,
                "is_priority": False,
                "has_client_recap": False,
            },
            {
                "uuid": str(test_client_obj.uuid),
                "name": "Test Client",
                "life_phase": "accumulation",
                "assets_under_management": "1000000.00",
                "segment": "gold",
                "onboarding_date": "2023-01-01",
                "is_priority": True,
                "has_client_recap": False,
            },
        ]

    def test_clients_for_user(
        self,
        test_user: User,
        admin_user: User,
        test_client_obj: Client,
        second_test_client: Client,
        test_organization: Organization,
        django_user_model: User,
    ) -> None:
        other_user = django_user_model.objects.create(
            username="<EMAIL>",
            organization=test_organization,
        )
        other_user_client = Client.objects.create(
            name="Other User Client",
            email="<EMAIL>",
            organization=test_organization,
        )
        other_user_client.authorized_users.add(other_user)
        other_user_client.save()

        admin_client = Client.objects.create(
            name="Admin Client",
            email="<EMAIL>",
            organization=test_organization,
        )
        admin_client.authorized_users.add(test_user)
        admin_client.save()

        no_authorized_users_client = Client.objects.create(
            name="No Authorized Users Client",
            email="<EMAIL>",
            organization=test_organization,
        )

        other_org = Organization.objects.create(name="Other Org")
        other_org_client = Client.objects.create(
            name="Other Org Client",
            email="<EMAIL>",
            organization=other_org,
        )
        other_org_client.authorized_users.add(test_user)
        other_org_client.save()

        app.dependency_overrides[user_from_authorization_header] = lambda: test_user
        response = client.get("/api/v2/insights/data")
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["clients"] == [
            {
                "uuid": str(admin_client.uuid),
                "name": "Admin Client",
                "life_phase": None,
                "assets_under_management": None,
                "segment": None,
                "onboarding_date": None,
                "is_priority": False,
                "has_client_recap": False,
            },
            {
                "uuid": str(second_test_client.uuid),
                "name": "Second Test Client",
                "life_phase": None,
                "assets_under_management": None,
                "segment": None,
                "onboarding_date": None,
                "is_priority": False,
                "has_client_recap": False,
            },
            {
                "uuid": str(test_client_obj.uuid),
                "name": "Test Client",
                "life_phase": "accumulation",
                "assets_under_management": "1000000.00",
                "segment": "gold",
                "onboarding_date": "2023-01-01",
                "is_priority": True,
                "has_client_recap": False,
            },
        ]


class TestGetRawInsightsData:
    @pytest.fixture(autouse=True)
    def setup_teardown(self) -> Generator[Any, None, None]:
        app.dependency_overrides = {}
        cache.clear()
        yield
        app.dependency_overrides = {}
        cache.clear()

    @pytest.fixture(autouse=True)
    def mock_enabled_insights_flag(self) -> Generator[MagicMock, None, None]:
        with patch("api.routers.insights.Flags.EnableInsightsAPI.is_active_for_user") as mock_flag:
            mock_flag.return_value = True
            yield mock_flag
        mock_flag.return_value = False

    def test_unauthenticated(self) -> None:
        response = client.get("/api/v2/insights/raw_data")
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert response.json() == {"detail": "Not authenticated"}

    def test_empty_data(self, test_user: User) -> None:
        response = client.get("/api/v2/insights/raw_data")

        assert response.status_code == status.HTTP_200_OK
        assert response.headers["content-type"] == "application/zip"
        assert response.headers["content-disposition"] == "attachment; filename=insights_data.zip"

        zip_data = io.BytesIO(response.content)
        with zipfile.ZipFile(zip_data, mode="r") as zip_file:
            assert set(zip_file.namelist()) == {
                "notes.csv",
                "scheduled_events.csv",
                "tasks.csv",
                "structured_meeting_data.csv",
                "users.csv",
                "clients.csv",
            }

            users_reader = csv.DictReader(io.StringIO(zip_file.read("users.csv").decode("utf-8")))
            assert users_reader.fieldnames == ["uuid", "email", "name", "manager_uuid"]
            rows = list(users_reader)
            assert len(rows) == 1
            assert rows[0] == {
                "uuid": str(test_user.uuid),
                "email": "<EMAIL>",
                "name": "Test User",
                "manager_uuid": "",
            }

            notes_csv = zip_file.read("notes.csv").decode("utf-8")

            # Since the file only has a headers row, the dict reader should not yield any rows
            assert len(list(csv.DictReader(io.StringIO(notes_csv)))) == 0

    def test_with_sample_data(self, test_user: User, test_client_obj: Client) -> None:
        note = Note.objects.create(
            note_owner=test_user,
            status=Note.PROCESSING_STATUS.processed,
            data_source=Note.DataSource.AUDIO_FILE,
            metadata={"meeting_duration": 1800},
            client={"uuid": str(test_client_obj.uuid)},
            created=datetime(2023, 10, 1, 12, 0, 0, tzinfo=datetime_timezone.utc),
        )
        note.authorized_users.add(test_user)
        note.save()

        scheduled_event = ScheduledEvent.objects.create(
            user=test_user,
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=1),
        )

        task = Task.objects.create(
            task_title="Test Task",
            task_owner=test_user,
            assignee=test_user,
            completed=False,
        )

        structured_data = StructuredMeetingData.objects.create(
            note=note,
            title="Test Structured Data",
            kind="test_kind",
            data={"review_entries": [{"topic": "Test", "discussed": True}]},
        )

        response = client.get("/api/v2/insights/raw_data")

        assert response.status_code == status.HTTP_200_OK
        assert response.headers["content-type"] == "application/zip"

        zip_data = io.BytesIO(response.content)
        with zipfile.ZipFile(zip_data, mode="r") as zip_file:
            notes_reader = csv.DictReader(io.StringIO(zip_file.read("notes.csv").decode("utf-8")))
            assert notes_reader.fieldnames == [
                "meeting_uuid",
                "authorized_user_uuids",
                "source",
                "duration_seconds",
                "status",
                "client_uuids",
                "meeting_prep_generated",
                "follow_up_generated",
                "scheduled_event_uuid",
                "start_time",
                "tags",
            ]
            notes_rows = list(notes_reader)
            assert len(notes_rows) == 1
            assert notes_rows[0] == {
                "meeting_uuid": str(note.uuid),
                "authorized_user_uuids": f"{[str(test_user.uuid)]}",
                "source": "Mic",
                "duration_seconds": "1800",
                "status": "processed",
                "client_uuids": f"{[str(test_client_obj.uuid)]}",
                "meeting_prep_generated": "False",
                "follow_up_generated": "False",
                "scheduled_event_uuid": "",
                "start_time": "2023-10-01T12:00:00Z",
                "tags": "[]",
            }

            scheduled_events_reader = csv.DictReader(io.StringIO(zip_file.read("scheduled_events.csv").decode("utf-8")))
            assert scheduled_events_reader.fieldnames == [
                "scheduled_event_uuid",
                "user_uuid",
                "has_clients",
            ]
            scheduled_events_rows = list(scheduled_events_reader)
            assert len(scheduled_events_rows) == 1
            assert scheduled_events_rows[0] == {
                "scheduled_event_uuid": str(scheduled_event.uuid),
                "user_uuid": str(test_user.uuid),
                "has_clients": "False",
            }

            tasks_reader = csv.DictReader(io.StringIO(zip_file.read("tasks.csv").decode("utf-8")))
            assert tasks_reader.fieldnames == [
                "task_uuid",
                "assignee_uuid",
                "creation_date",
                "completed",
            ]
            tasks_rows = list(tasks_reader)
            assert len(tasks_rows) == 1
            assert tasks_rows[0] == {
                "task_uuid": str(task.uuid),
                "assignee_uuid": str(test_user.uuid),
                "creation_date": task.created.isoformat().replace("+00:00", "Z"),
                "completed": "False",
            }

            structured_reader = csv.DictReader(
                io.StringIO(zip_file.read("structured_meeting_data.csv").decode("utf-8"))
            )
            assert structured_reader.fieldnames == [
                "meeting_data_uuid",
                "note_uuid",
                "title",
                "kind",
                "items_total",
                "items_completed",
            ]
            structured_rows = list(structured_reader)
            assert len(structured_rows) == 1
            assert structured_rows[0] == {
                "meeting_data_uuid": str(structured_data.uuid),
                "note_uuid": str(note.uuid),
                "title": "Test Structured Data",
                "kind": "test_kind",
                "items_total": "1",
                "items_completed": "1",
            }

            clients_reader = csv.DictReader(io.StringIO(zip_file.read("clients.csv").decode("utf-8")))
            assert clients_reader.fieldnames == [
                "uuid",
                "name",
                "life_phase",
                "assets_under_management",
                "segment",
                "onboarding_date",
                "is_priority",
                "has_client_recap",
            ]
            clients_rows = list(clients_reader)
            assert len(clients_rows) == 1
            assert clients_rows[0] == {
                "uuid": str(test_client_obj.uuid),
                "name": "Test Client",
                "life_phase": "accumulation",
                "assets_under_management": "1000000.00",
                "segment": "gold",
                "onboarding_date": "2023-01-01",
                "is_priority": "True",
                "has_client_recap": "False",
            }

    def test_csv_format_validation(self, test_user: User) -> None:
        # Create a note with special characters to test CSV escaping
        note = Note.objects.create(
            note_owner=test_user,
            metadata={"tags": ["test,with,commas", 'test"with"quotes']},
        )
        note.authorized_users.add(test_user)
        note.save()

        response = client.get("/api/v2/insights/raw_data")

        assert response.status_code == status.HTTP_200_OK

        zip_data = io.BytesIO(response.content)
        with zipfile.ZipFile(zip_data, mode="r") as zip_file:
            notes_csv = zip_file.read("notes.csv").decode("utf-8")

            csv_reader = csv.DictReader(io.StringIO(notes_csv))
            rows = list(csv_reader)
            assert len(rows) == 1
            assert rows[0]["meeting_uuid"] == str(note.uuid)

            tags_data = ast.literal_eval(rows[0]["tags"])
            assert len(tags_data) == 2
            assert tags_data[0]["name"] == "test,with,commas"
            assert tags_data[1]["name"] == 'test"with"quotes'

    @patch("api.routers.insights._insights_data")
    def test_handles_errors(self, mock_insights_data: MagicMock, test_user: User) -> None:
        mock_insights_data.side_effect = Exception("Error retrieving insights dashboard data")

        response = client.get("/api/v2/insights/raw_data")

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Error retrieving insights dashboard raw data" in response.json()["detail"]

    def test_flag_disabled_returns_empty_zip(self, test_user: User, mock_enabled_insights_flag: MagicMock) -> None:
        mock_enabled_insights_flag.return_value = False

        response = client.get("/api/v2/insights/raw_data")

        assert response.status_code == status.HTTP_200_OK
        assert response.headers["content-type"] == "application/zip"

        zip_data = io.BytesIO(response.content)
        with zipfile.ZipFile(zip_data, mode="r") as zip_file:
            for file_name in zip_file.namelist():
                content = zip_file.read(file_name).decode("utf-8").strip()
                # Should only contain headers, no data rows
                assert len(content.split("\n")) == 1
