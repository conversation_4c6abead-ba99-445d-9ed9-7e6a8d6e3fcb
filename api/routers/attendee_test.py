import datetime
import uuid
from typing import Any, Generator

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from api.dependencies import user_from_authorization_header
from api.internal_api import internal_api as app
from deepinsights.core.preferences.preferences import DefaultClientFilter
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.phone_number import PhoneNumber
from deepinsights.users.models.user import User

client = TestClient(app)

pytestmark = [pytest.mark.django_db(transaction=True)]


@pytest.fixture(autouse=True)
def teardown() -> Generator[Any, None, None]:
    app.dependency_overrides = {}
    yield
    app.dependency_overrides = {}


@pytest.fixture
def test_organization() -> Organization:
    return Organization.objects.create(name="Test Org")


@pytest.fixture
def test_user(django_user_model: User, test_organization: Organization) -> User:
    test_user = django_user_model.objects.create(
        username="<EMAIL>",
        name="Test User",
        organization=test_organization,
        license_type=User.LicenseType.advisor,
    )
    test_user.phone_numbers.add(
        PhoneNumber.objects.create(number="**********", type=PhoneNumber.Type.mobile, primary=True)
    )
    test_user.save()
    app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.get(username="<EMAIL>")
    return test_user


@pytest.fixture
def test_crm_config(test_organization: User) -> None:
    test_organization.crm_configuration = {"crm_system": "test_crm", "settings": {}}
    test_organization.save()


@pytest.fixture
def test_client(test_user: User, test_crm_config: None, test_organization: Organization) -> Client:
    client = Client.objects.create(
        name="Test Client",
        crm_id="CRM123",
        job_title="CEO",
        client_type="individual",
        crm_system="test_crm",
        organization=test_organization,
        phone_number="**********",
        owner=test_user,
    )
    client.authorized_users.add(test_user)
    client.save()
    return client


@pytest.mark.parametrize(
    "default_client_filter",
    [DefaultClientFilter.ALL, DefaultClientFilter.OWNED, None],
)
def test_list_attendee_options_success(test_user: User, default_client_filter: DefaultClientFilter | None) -> None:
    response = client.get(f"/api/v2/attendee/attendee_options/{test_user.uuid}")

    if default_client_filter is not None:
        test_user.crm_configuration = {**test_user.crm_configuration, "default_client_filter": default_client_filter}
        test_user.save()

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {
        "users": [
            {
                "name": "Test User",
                "phone_number": "+1**********",
                "role": None,
                "uuid": str(test_user.uuid),
            }
        ],
        "clients": [],
    }


class TestListAttendeeOptionsClientFilter:
    @pytest.fixture(autouse=True)
    def setUp(self, test_user: User, test_client: Client, test_organization: Organization) -> None:
        self.test_user = test_user
        self.test_client = test_client
        self.client_two = Client.objects.create(
            name="Test Client 2",
            crm_id="CRM234",
            job_title="CEO",
            client_type="individual",
            crm_system="test_crm",
            organization=test_organization,
            phone_number="**********",
        )
        self.client_two.authorized_users.add(self.test_user)
        self.client_two.save()

        self.user_two = User.objects.create(
            username="test2",
            organization=self.test_user.organization,
            name="Test User 2",
            role="role",
        )
        self.user_two.phone_numbers.add(
            PhoneNumber.objects.create(number="2125551214", type=PhoneNumber.Type.mobile, primary=True)
        )
        self.user_two.save()

        self.client_three = Client.objects.create(
            name="Test Client 3",
            crm_id="CRM345",
            job_title="CEO",
            client_type="individual",
            crm_system="test_crm",
            organization=test_organization,
            phone_number="**********",
            owner=self.user_two,
        )
        self.client_three.authorized_users.add(self.test_user, self.user_two)
        self.client_three.save()

        self.client_four = Client.objects.create(
            name="Test Client 4",
            crm_id="CRM456",
            job_title="CEO",
            client_type="individual",
            crm_system="test_crm",
            organization=test_organization,
            phone_number="**********",
            owner=self.user_two,
        )
        self.client_four.authorized_users.add(self.user_two)
        self.client_four.save()

        self.expected_users = [
            {
                "name": "Test User",
                "phone_number": "+1**********",
                "role": None,
                "uuid": str(self.test_user.uuid),
            },
            {
                "name": "Test User 2",
                "phone_number": "+12125551214",
                "role": "role",
                "uuid": str(self.user_two.uuid),
            },
        ]

        self.expected_client_one = {
            "name": "Test Client",
            "crm_id": "CRM123",
            "job_title": "CEO",
            "client_type": "individual",
            "phone_number": "+1**********",
            "uuid": str(test_client.uuid),
        }
        self.expected_client_two = {
            "name": "Test Client 2",
            "crm_id": "CRM234",
            "job_title": "CEO",
            "client_type": "individual",
            "phone_number": "+1**********",
            "uuid": str(self.client_two.uuid),
        }
        self.expected_client_three = {
            "name": "Test Client 3",
            "crm_id": "CRM345",
            "job_title": "CEO",
            "client_type": "individual",
            "phone_number": "+1**********",
            "uuid": str(self.client_three.uuid),
        }
        self.expected_client_four = {
            "name": "Test Client 4",
            "crm_id": "CRM456",
            "job_title": "CEO",
            "client_type": "individual",
            "phone_number": "+1**********",
            "uuid": str(self.client_four.uuid),
        }

    def test_none_filter(self) -> None:
        response = client.get(f"/api/v2/attendee/attendee_options/{self.test_user.uuid}")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "users": self.expected_users,
            "clients": [self.expected_client_one, self.expected_client_two, self.expected_client_three],
        }

    def test_invalid_filter(self) -> None:
        self.test_user.crm_configuration = {
            **self.test_user.crm_configuration,
            "default_client_filter": "invalid",
        }
        self.test_user.save()
        response = client.get(f"/api/v2/attendee/attendee_options/{self.test_user.uuid}")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "users": self.expected_users,
            "clients": [self.expected_client_one, self.expected_client_two, self.expected_client_three],
        }

    def test_all_filter(self) -> None:
        self.test_user.crm_configuration = {
            **self.test_user.crm_configuration,
            "default_client_filter": DefaultClientFilter.ALL,
        }
        self.test_user.save()

        response = client.get(f"/api/v2/attendee/attendee_options/{self.test_user.uuid}")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "users": self.expected_users,
            "clients": [self.expected_client_one, self.expected_client_two, self.expected_client_three],
        }

    def test_owned_filter(self) -> None:
        self.test_user.crm_configuration = {
            **self.test_user.crm_configuration,
            "default_client_filter": DefaultClientFilter.OWNED,
        }
        self.test_user.save()

        response = client.get(f"/api/v2/attendee/attendee_options/{self.test_user.uuid}")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "users": self.expected_users,
            "clients": [self.expected_client_one],
        }

    def test_none_filter_for_user_two(self) -> None:
        app.dependency_overrides[user_from_authorization_header] = lambda: self.user_two

        response = client.get(f"/api/v2/attendee/attendee_options/{self.user_two.uuid}")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "users": self.expected_users,
            "clients": [self.expected_client_three, self.expected_client_four],
        }

    def test_all_filter_for_user_two(self) -> None:
        app.dependency_overrides[user_from_authorization_header] = lambda: self.user_two
        self.user_two.crm_configuration = {
            **self.user_two.crm_configuration,
            "default_client_filter": DefaultClientFilter.ALL,
        }
        self.user_two.save()

        response = client.get(f"/api/v2/attendee/attendee_options/{self.user_two.uuid}")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "users": self.expected_users,
            "clients": [self.expected_client_three, self.expected_client_four],
        }

    def test_owned_filter_for_user_two(self) -> None:
        app.dependency_overrides[user_from_authorization_header] = lambda: self.user_two
        self.user_two.crm_configuration = {
            **self.user_two.crm_configuration,
            "default_client_filter": DefaultClientFilter.OWNED,
        }
        self.user_two.save()

        response = client.get(f"/api/v2/attendee/attendee_options/{self.user_two.uuid}")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "users": self.expected_users,
            "clients": [self.expected_client_three, self.expected_client_four],
        }


def test_list_attendee_options_unauthorized(
    test_user: User, django_user_model: User, test_organization: Organization
) -> None:
    other_user = django_user_model.objects.create(
        username="<EMAIL>",
        organization=test_organization,
        license_type=User.LicenseType.advisor,
    )

    response = client.get(f"/api/v2/attendee/attendee_options/{other_user.uuid}")
    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    assert response.json() == {"detail": "Not authorized"}


def test_list_attendee_options_superuser(
    test_user: User, django_user_model: User, test_organization: Organization
) -> None:
    other_user = django_user_model.objects.create(
        username="<EMAIL>",
        organization=test_organization,
    )
    test_user.is_superuser = True
    test_user.save()

    response = client.get(f"/api/v2/attendee/attendee_options/{other_user.uuid}")
    assert response.status_code == status.HTTP_200_OK


def test_list_attendee_options_exclude_empty_names(test_user: User, django_user_model: User) -> None:
    User.objects.create(username="<EMAIL>", organization=test_user.organization, name="")
    response = client.get(f"/api/v2/attendee/attendee_options/{test_user.uuid}")
    assert response.status_code == status.HTTP_200_OK
    users = response.json()["users"]
    assert not any(user["name"] == "" for user in users)


def test_list_attendee_options_user_not_found(test_user: User) -> None:
    non_existent_uuid = uuid.uuid4()
    response = client.get(f"/api/v2/attendee/attendee_options/{non_existent_uuid}")

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "User not found"}


def test_list_attendee_options(test_user: User, test_client: Client, test_organization: Organization) -> None:
    user_two = User.objects.create(
        username="test2",
        organization=test_user.organization,
        name="A Test User 2",
        role="role",
    )
    user_two.phone_numbers.add(
        PhoneNumber.objects.create(number="2125551214", type=PhoneNumber.Type.mobile, primary=True)
    )
    user_two.save()
    client_two = Client.objects.create(
        name="A Test Client 2",
        crm_id="CRM456",
        job_title="CTO",
        client_type="individual",
        crm_system="test_crm",
        organization=test_organization,
        phone_number="2125551215",
        owner=test_user,
    )
    client_two.authorized_users.add(test_user)
    client_two.save()

    non_returned_client = Client.objects.create(
        name="NonReturnedClient",
        organization=test_organization,
        crm_id="ClientThreeID",
        job_title="TestJobTwo",
        client_type="TesterTwo",
        crm_system="redtail",
    )
    non_returned_client.authorized_users.add(user_two)
    non_returned_client.save()
    # Create a client with no authorized users.
    Client.objects.create(
        name="EmptyAuthClient",
        organization=test_organization,
        crm_id="ClientFourId",
        job_title="TestJobTwo",
        client_type="TesterTwo",
        crm_system="redtail",
    )
    org_two = Organization.objects.create(name="Test Org 2")
    other_org_client = Client.objects.create(
        name="OtherOrgClient",
        organization=org_two,
        crm_id="ClientFiveID",
        job_title="TestJobTwo",
        client_type="TesterTwo",
        crm_system="redtail",
    )
    other_org_client.authorized_users.add(test_user)
    other_org_client.save()
    # Create a user in a different organization.
    User.objects.create(username="test3", organization=org_two, name="A Test User 3")

    response = client.get(f"/api/v2/attendee/attendee_options/{test_user.uuid}")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {
        "clients": [
            {
                "name": "A Test Client 2",
                "crm_id": "CRM456",
                "job_title": "CTO",
                "client_type": "individual",
                "phone_number": "+12125551215",
                "uuid": str(client_two.uuid),
            },
            {
                "name": "Test Client",
                "crm_id": "CRM123",
                "job_title": "CEO",
                "client_type": "individual",
                "phone_number": "+1**********",
                "uuid": str(test_client.uuid),
            },
        ],
        "users": [
            {
                "name": "A Test User 2",
                "phone_number": "+12125551214",
                "role": "role",
                "uuid": str(user_two.uuid),
            },
            {
                "name": "Test User",
                "phone_number": "+1**********",
                "role": None,
                "uuid": str(test_user.uuid),
            },
        ],
    }


def test_list_attendee_options_exclude_empty_client_fields(
    test_user: User, test_crm_config: None, test_organization: Organization
) -> None:
    empty_client = Client.objects.create(
        name="Empty Fields Client",
        crm_id="",  # Empty CRM ID
        crm_system="test_crm",
        organization=test_organization,
    )
    empty_client.authorized_users.add(test_user)

    null_client = Client.objects.create(
        name="Null Fields Client",
        crm_id=None,  # Null CRM ID
        crm_system="test_crm",
        organization=test_organization,
    )
    null_client.authorized_users.add(test_user)

    response = client.get(f"/api/v2/attendee/attendee_options/{test_user.uuid}")

    assert response.status_code == status.HTTP_200_OK
    clients = response.json()["clients"]
    assert len(clients) == 0  # Both clients should be excluded


def test_list_attendee_options_client_search(
    test_user: User, test_client: Client, test_organization: Organization
) -> None:
    # Create another client that doesn't match the search
    other_client = Client.objects.create(
        name="Other Client",
        crm_id="CRM456",
        crm_system="test_crm",
        organization=test_organization,
    )
    other_client.authorized_users.add(test_user)

    # Search for "Test" in client names
    response = client.get(f"/api/v2/attendee/attendee_options/{test_user.uuid}?q=Test")

    assert response.status_code == status.HTTP_200_OK
    clients = response.json()["clients"]
    assert len(clients) == 1
    assert clients[0]["name"] == "Test Client"


def test_list_attendee_options_unauthorized_client(
    test_user: User, test_client: Client, django_user_model: User, test_organization: Organization
) -> None:
    unauthorized_user = django_user_model.objects.create(
        username="<EMAIL>",
        organization=test_organization,
        license_type=User.LicenseType.advisor,
    )
    app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.get(
        username="<EMAIL>"
    )

    response = client.get(f"/api/v2/attendee/attendee_options/{unauthorized_user.uuid}")

    assert response.status_code == status.HTTP_200_OK
    clients = response.json()["clients"]
    assert len(clients) == 0  # Unauthorized user should see no clients


@pytest.mark.parametrize("license_type", [User.LicenseType.csa, User.LicenseType.staff])
def test_list_attendees_high_privilege_license(
    test_user: User, django_user_model: User, test_organization: Organization, license_type: User.LicenseType
) -> None:
    test_user.license_type = license_type
    test_user.save()

    other_user = django_user_model.objects.create(username="other_user", organization=test_organization)

    note = Note.objects.create(note_owner=test_user)
    attendee = Attendee.objects.create(attendee_name="Test Attendee", note=note, user=test_user)

    note_two = Note.objects.create(note_owner=other_user)
    attendee_two = Attendee.objects.create(attendee_name="Test Attendee Two", note=note_two, user=test_user)

    response = client.get("/api/v2/attendee/")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == [
        {
            "uuid": str(attendee.uuid),
            "name": "Test Attendee",
            "type": "user",
            "client_uuid": None,
            "user_uuid": str(test_user.uuid),
            "speaker_time": None,
            "speaker_percentage": None,
            "speaker_alias": None,
        },
        {
            "uuid": str(attendee_two.uuid),
            "name": "Test Attendee Two",
            "type": "user",
            "client_uuid": None,
            "user_uuid": str(test_user.uuid),
            "speaker_time": None,
            "speaker_percentage": None,
            "speaker_alias": None,
        },
    ]


def test_list_attendees_authorized_user(
    test_user: User, django_user_model: User, test_organization: Organization
) -> None:
    other_user = django_user_model.objects.create(username="other_user", organization=test_organization)
    note = Note.objects.create(note_owner=other_user)
    note.authorized_users.add(test_user)
    note.save()
    attendee = Attendee.objects.create(attendee_name="Test Attendee", note=note, user=test_user)

    note_two = Note.objects.create(note_owner=other_user)
    Attendee.objects.create(attendee_name="Test Attendee Two", note=note_two, user=test_user)

    response = client.get("/api/v2/attendee/")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == [
        {
            "uuid": str(attendee.uuid),
            "name": "Test Attendee",
            "type": "user",
            "client_uuid": None,
            "user_uuid": str(test_user.uuid),
            "speaker_time": None,
            "speaker_percentage": None,
            "speaker_alias": None,
        }
    ]


def test_list_attendees_no_authorized_users(
    test_user: User, django_user_model: User, test_organization: Organization
) -> None:
    note = Note.objects.create()
    Attendee.objects.create(attendee_name="Test Attendee", note=note, user=test_user)

    response = client.get("/api/v2/attendee/")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == []


def test_list_attendees_with_clients(test_user: User, test_client: Client) -> None:
    note = Note.objects.create(note_owner=test_user)
    note.authorized_users.add(test_user)
    note.save()
    attendee = Attendee.objects.create(attendee_name="Test Attendee", note=note, client=test_client)
    attendee_two = Attendee.objects.create(attendee_name="Test Attendee Two", note=note, user=test_user)
    attendee_three = Attendee.objects.create(attendee_name="Test Attendee Three", note=note)

    response = client.get("/api/v2/attendee/")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == [
        {
            "uuid": str(attendee.uuid),
            "name": "Test Attendee",
            "type": "client",
            "client_uuid": str(test_client.uuid),
            "user_uuid": None,
            "speaker_time": None,
            "speaker_percentage": None,
            "speaker_alias": None,
        },
        {
            "uuid": str(attendee_two.uuid),
            "name": "Test Attendee Two",
            "type": "user",
            "client_uuid": None,
            "user_uuid": str(test_user.uuid),
            "speaker_time": None,
            "speaker_percentage": None,
            "speaker_alias": None,
        },
        {
            "uuid": str(attendee_three.uuid),
            "name": "Test Attendee Three",
            "type": "unknown",
            "client_uuid": None,
            "user_uuid": None,
            "speaker_time": None,
            "speaker_percentage": None,
            "speaker_alias": None,
        },
    ]


def test_list_attendees_speaker_attributes(test_user: User) -> None:
    note = Note.objects.create(note_owner=test_user)
    note.authorized_users.add(test_user)
    note.save()
    attendee = Attendee.objects.create(
        attendee_name="Test Attendee",
        note=note,
        speaker_time=datetime.timedelta(seconds=100),
        speaker_percentage=50.0,
        speaker_alias="Test Alias",
    )
    attendee_two = Attendee.objects.create(
        attendee_name="Test Attendee Two",
        note=note,
        speaker_time=datetime.timedelta(seconds=10),
        speaker_percentage=5.0,
        speaker_alias="Test Alias Two",
    )

    response = client.get("/api/v2/attendee/")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == [
        {
            "uuid": str(attendee.uuid),
            "name": "Test Attendee",
            "type": "unknown",
            "client_uuid": None,
            "user_uuid": None,
            "speaker_time": "0:01:40",
            "speaker_percentage": 50.0,
            "speaker_alias": "Test Alias",
        },
        {
            "uuid": str(attendee_two.uuid),
            "name": "Test Attendee Two",
            "type": "unknown",
            "client_uuid": None,
            "user_uuid": None,
            "speaker_time": "0:00:10",
            "speaker_percentage": 5.0,
            "speaker_alias": "Test Alias Two",
        },
    ]
