import logging
from uuid import UUID

from asgiref.sync import sync_to_async
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel

from api.dependencies import user_from_authorization_header
from api.routers.note_models import SummarySection
from deepinsights.core.ml.client_search import search_client_notes
from deepinsights.core.ml.search import search_note
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note, is_authorized_to_view_note
from deepinsights.meetingsapp.models.search_query import SearchQuery
from deepinsights.users.models.user import User

router = APIRouter(tags=["search"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}")


async def __additional_context(note: Note, user: User) -> str:
    """
    Get the contextual query for the user based on past search queries
    """
    search_queries = SearchQuery.objects.filter(notes__in=[note], requestor=user).order_by("created")
    history = ""
    async for search_query in search_queries:
        history += f"User: {search_query.query}\nResponse: {search_query.structured_response}\n"

    additonal_context = (
        ""
        if not history
        else (
            f"Here is a history of your search queries for this note:\n{history}"
            + "\n here is the meeting summary for your reference:\n{note.summary}"
        )
    )
    return additonal_context


class SearchResponse(BaseModel):
    """
    The response model for both note and client search operations
    """

    answer: SummarySection
    search_query_id: UUID


@router.get(
    "/{note_id}",
    response_model=SearchResponse,
    responses={
        200: {"description": "Successful response"},
        403: {"description": "Not authorized to view this note"},
        404: {"description": "Note not found"},
        500: {"description": "An error occurred while doing search on the note"},
    },
    summary="Perform a search on the note using the search query provided by the user",
    description="Perform a search on the note using the search query provided by the user, the search is done on the transcript and if not available use the summary instead",
)
async def search(
    note_id: str,
    query: str,
    user: User = Depends(user_from_authorization_header),
) -> SearchResponse:
    try:
        note = (
            await Note.objects.defer("raw_asr_response").select_related("note_owner__organization").aget(uuid=note_id)
        )
    except Note.DoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Note not found")

    is_authorized = await sync_to_async(is_authorized_to_view_note)(user, note)

    if not is_authorized:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view this note")

    try:
        input_data = note.diarized_trans_with_names or note.summary
        if not input_data:
            raise ValueError("No transcript or summary found on note")
        additional_context = await __additional_context(note, user)
        structured_response = await search_note(input_data, query, additional_context)
        new_search_query = await SearchQuery.objects.acreate(
            requestor=user,
            query=query,
            structured_response=structured_response.model_dump(),
        )
        await new_search_query.notes.aadd(note)

        return SearchResponse(answer=structured_response, search_query_id=new_search_query.uuid)
    except ValueError as e:
        logging.error("No transcript or summary found on note", exc_info=e)
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No transcript or summary found on note")
    except Exception as e:
        logging.error("An error occurred while doing search on the note", exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An error occurred while doing search on the note"
        )


@router.get(
    "/client/{client_id}",
    response_model=SearchResponse,
    responses={
        200: {"description": "Successful response"},
        403: {"description": "Not authorized to view this client"},
        404: {"description": "Client not found"},
        500: {"description": "An error occurred while searching client notes"},
    },
    summary="Search across all notes and information for a client",
    description="Search across all notes and information for a client using the provided query",
)
async def search_client(
    client_id: UUID,
    query: str,
    user: User = Depends(user_from_authorization_header),
) -> SearchResponse:
    try:
        client = await Client.objects.select_related("organization").aget(uuid=client_id)
    except Client.DoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Client not found")

    # Check if user is authorized to view this client
    if not await client.authorized_users.filter(id=user.id).aexists():
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view this client")

    try:
        structured_response = await search_client_notes(client, user, query)

        # Create search query record
        new_search_query = await SearchQuery.objects.acreate(
            requestor=user,
            query=query,
            structured_response=structured_response.model_dump(),
        )

        return SearchResponse(answer=structured_response, search_query_id=new_search_query.uuid)

    except ValueError as e:
        logging.error("Failed to search client notes", exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An error occurred while searching client notes"
        )
