import logging
from enum import StrEnum
from typing import Any, Awaitable, Callable, Literal, Type

from django.db.models import Q
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field, TypeAdapter
from starlette import status

from api.dependencies import user_from_authorization_header
from deepinsights.core.preferences.dataclass_mixins import ZeplynDataclassJsonMixin
from deepinsights.core.preferences.preferences import (
    BotPreferences,
    CalendarPreferences,
    EmailConfiguration,
    NotificationPreferences,
    Preferences,
    PreferencesSchemaPopulators,
    SyncPreferences,
    get_default_preferences,
)
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.user import User


class UISchemaControl(BaseModel):
    type: str = Field(..., description="Type of the UI control")
    scope: str = Field(..., description="Reference to the property in the JSON schema")


class UISchema(BaseModel):
    type: str = Field(..., description="Type of the UI schema")
    elements: list[UISchemaControl] = Field(..., description="Elements of the UI schema")


class PreferenceSchema(BaseModel):
    title: str = Field(..., description="Title of the preference object")
    json_schema: dict[str, Any] = Field(..., description="JSON schema for the preference object")
    ui_schema: UISchema = Field(..., description="UI schema for the preference object")
    data: dict[str, Any] = Field(..., description="The current preference values")


router = APIRouter(tags=["preferences"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}")


def _get_non_object_fields(cls: Type[ZeplynDataclassJsonMixin]) -> list[str]:
    fields: list[str] = []
    if not hasattr(cls, "__dataclass_fields__"):
        return fields

    for field_name, field in cls.__dataclass_fields__.items():
        # Skip if field is another dataclass
        field_type = field.type
        if hasattr(field_type, "__dataclass_fields__"):
            continue

        # Skip fields that should not be shown in the UI.
        if field.metadata.get("ui_shown") is False:
            continue

        fields.append(field_name)
    return fields


def _create_ui_schema(fields: list[str]) -> UISchema:
    return UISchema(
        type="VerticalLayout",
        elements=[UISchemaControl(type="Control", scope=f"#/properties/{field}") for field in fields],
    )


class PreferencesTarget(StrEnum):
    USER = "user"
    ORGANIZATION = "organization"


async def _preference_schemas(
    user: User, preferences: Preferences, target: PreferencesTarget
) -> list[PreferenceSchema]:
    """Generates PreferenceSchema response objects for the given preferences."""
    user_preference_classes: list[tuple[Type[ZeplynDataclassJsonMixin], str]] = [
        (Preferences, "General"),
        (EmailConfiguration, "Email Configuration"),
        (NotificationPreferences, "Notifications"),
        (CalendarPreferences, "Calendar Preferences"),
    ]
    org_preference_classes = user_preference_classes + [
        (BotPreferences, "Notetaker"),
        (SyncPreferences, "CRM Certification"),
    ]
    schemas = []
    for cls, title in user_preference_classes if target == PreferencesTarget.USER else org_preference_classes:
        fields = _get_non_object_fields(cls)
        if not fields:
            logging.error("No fields found for %s. This indicates a programming error.", cls.__name__)
            continue

        json_schema = TypeAdapter(cls).json_schema()
        json_schema["properties"] = {k: v for k, v in json_schema["properties"].items() if k in fields}

        # Populate the schema with dynamic values, as requested by the x-populator custom field.
        async def populate_meeting_types(v: dict[str, Any]) -> dict[str, Any]:
            meeting_types = MeetingType.for_user(user)
            if target == PreferencesTarget.ORGANIZATION:
                meeting_types = meeting_types.filter(Q(everyone=True) | Q(organizations=user.organization))
            v["oneOf"] = [{"const": "", "title": "System default"}] + [
                {"const": str(t.uuid), "title": t.name} async for t in meeting_types.order_by("name")
            ]
            del v["x-populator"]
            return v

        populator_map: dict[str, Callable[[dict[str, Any]], Awaitable[dict[str, Any]]]] = {
            PreferencesSchemaPopulators.MEETING_TYPES: populate_meeting_types
        }
        for k, v in json_schema["properties"].items():
            if (populator_name := v.get("x-populator")) and (populator := populator_map.get(populator_name)):
                json_schema["properties"][k] = await populator(v)

        if "$defs" in json_schema:
            del json_schema["$defs"]
        ui_schema = _create_ui_schema(fields)

        # Get the corresponding user data for this preference type
        data = {}
        if cls == Preferences:
            data = {field: getattr(preferences, field) for field in fields}
        elif cls == BotPreferences:
            data = {field: getattr(preferences.bot_preferences, field) for field in fields}
        elif cls == EmailConfiguration:
            data = {field: getattr(preferences.email_settings, field) for field in fields}
        elif cls == NotificationPreferences:
            data = {field: getattr(preferences.notification_preferences, field) for field in fields}
        elif cls == SyncPreferences:
            data = {field: getattr(preferences.sync_preferences, field) for field in fields}
        elif cls == CalendarPreferences:
            data = {field: getattr(preferences.calendar_preferences, field) for field in fields}
        schemas.append(
            PreferenceSchema(
                title=title,
                json_schema=json_schema,
                ui_schema=ui_schema,
                data=data,
            )
        )

    return schemas


class PreferencesResponse(BaseModel):
    user_preferences: list[PreferenceSchema] = Field(..., description="User's preferences")
    org_preferences: list[PreferenceSchema] = Field(
        ..., description="Organization-level preferences (if the user has access to these preferences)."
    )


def _default_preferences() -> Preferences:
    """Returns preferences with default values, as they are used in the application.

    This is a hack to make sure that the data we pass down to the frontend matches the existing way
    that preferences work in the application. The behavior of preferences is a bit wonky, so we need to
    account for that here.
    """
    preferences = Preferences.from_dict(get_default_preferences())
    preferences.bot_preferences.recording_image_b64 = preferences.bot_preferences.recording_image_b64_or_default()
    preferences.bot_preferences.not_recording_image_b64 = (
        preferences.bot_preferences.not_recording_image_b64_or_default()
    )
    preferences.bot_preferences.recording_message_b64 = preferences.bot_preferences.recording_message_b64_or_default()
    return preferences


@router.get("/")
async def get_preference_schemas(
    user: User = Depends(user_from_authorization_header),
) -> PreferencesResponse:
    user_preferences = _default_preferences()
    if user.organization:
        user_preferences.update(user.organization.preferences or {})
    user_preferences.update(user.preferences or {})

    org_preferences: Preferences | None = None
    if user.is_superuser and user.organization:
        org_preferences = _default_preferences()
        org_preferences.update(user.organization.preferences or {})

    return PreferencesResponse(
        user_preferences=await _preference_schemas(user, user_preferences, PreferencesTarget.USER),
        org_preferences=await _preference_schemas(user, org_preferences, PreferencesTarget.ORGANIZATION)
        if org_preferences
        else [],
    )


class PreferenceUpdate(BaseModel):
    scope: Literal["user", "organization"] = Field(..., description="Scope of the preference update")
    schema_title: str = Field(..., description="Name of the preference object to update")
    updated_values: dict[str, Any] = Field(..., description="Values to update in the preference object")


@router.post("/")
def update_preferences(
    update: PreferenceUpdate,
    user: User = Depends(user_from_authorization_header),
) -> None:
    preference_target: User | Organization = user
    if update.scope == "organization":
        if not user.organization:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot update organization preferences for user",
            )
        if not user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User does not have permission to update organization preferences",
            )
        preference_target = user.organization

    # Ensure that we have a reference to the preferences dictionary on the user
    preference_target.preferences = preference_target.preferences or {}
    preferences = preference_target.preferences

    preference_map = {
        BotPreferences.__name__: "bot_preferences",
        EmailConfiguration.__name__: "email_settings",
        NotificationPreferences.__name__: "notification_preferences",
        SyncPreferences.__name__: "sync_preferences",
        CalendarPreferences.__name__: "calendar_preferences",
    }

    preference_key = preference_map.get(update.schema_title)
    if preference_key:
        if preference_key not in preferences or preferences.get(preference_key) is None:
            preferences[preference_key] = {}
        target_preferences = preferences[preference_key]
    elif update.schema_title == Preferences.__name__:
        target_preferences = preferences
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid schema title: {update.schema_title}. Valid titles are: {', '.join(preference_map.keys())}",
        )
    target_preferences.update(update.updated_values)
    preference_target.save()
