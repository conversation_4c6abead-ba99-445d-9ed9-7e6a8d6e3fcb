from enum import Enum
from typing import List, Literal, Optional, Union
from uuid import UUID

from pydantic import BaseModel


# The types of integrations that can be displayed.
class IntegrationType(str, Enum):
    CRM = "CRM"

    MEETING_SOFTWARE = "MeetingSoftware"

    CALENDAR = "Calendar"


class MenuItemId(Enum):
    MY_ACCOUNT = "my-account"
    INTEGRATIONS = "integrations"
    SETTINGS = "settings"
    PROFILE_DETAILS = "profile-details"
    PLAN_DETAILS = "plan-details"
    USER_INTERFACE = "user-interface"

    USER_PREFERENCES = "preferences"
    ORG_PREFERENCES = "org-preferences"

    ADMIN = "admin"
    USER_IMPERSONATION = "user-impersonation"


class MenuItem(BaseModel):
    # unique identifier for the menu item
    id: MenuItemId

    # name of the menu item
    label: str

    # sub-menu items
    items: list["MenuItem"] | None = None


# NOTE: the union results in "flattening of the children types" as well
# i.e., all fields such as `cards` & `filters` are now assumed to be mandatory on every item
# in `SectionDetails.data` - unless explicitly marked as optional
SectionItemType = Union[
    "SectionItemTextBlock",
    "SectionItemTextField",
    "SectionItemSingleChoiceField",
    "SectionItemMultiChoiceField",
    "SectionItemBooleanField",
    "SectionItemAcknowledgementField",
    "SectionItemLink",
    "SectionItemIntegrationCards",
    "SectionItemPlanDetails",
]


class SectionItemFieldType(Enum):
    TEXT_BLOCK = "text-block"
    TEXT = "text-field"
    SINGLE_CHOICE = "single-choice-field"
    MULTI_CHOICE = "multi-choice-field"
    BOOLEAN = "boolean-field"
    ACKNOWLEDGEMENT = "acknowledgement-field"
    INTEGRATION_CARDS = "integration-cards"
    INTEGRATION_CARD = "integration-card"
    LINK = "link"
    PLAN_DETAILS = "plan-details"


class SectionDetails(BaseModel):
    # name of the section
    label: str

    # list of data items in the section
    data: List[SectionItemType]

    showSaveButton: bool = False


class SectionItemBase(BaseModel):
    # unique identifier
    id: str

    # name of the data item
    label: str


class SectionItemTextBlock(SectionItemBase):
    kind: SectionItemFieldType = SectionItemFieldType.TEXT_BLOCK
    details: str | None = None


class SectionItemTextField(SectionItemBase):
    # type of the data item
    kind: SectionItemFieldType = SectionItemFieldType.TEXT
    placeholder: str | None = None
    value: str | None = None  # value of the field
    disabled: bool | None = False  # if true, disable the field
    readonly: bool | None = False  # if true, the field is read-only


class LabeledEntity(BaseModel):
    id: str
    label: str


SettingsFilter = [
    LabeledEntity(id=IntegrationType.CRM, label="CRM"),
    LabeledEntity(id=IntegrationType.CALENDAR, label="Calendar"),
    LabeledEntity(id=IntegrationType.MEETING_SOFTWARE, label="Meeting Software"),
]


class SectionItemSingleChoiceField(SectionItemBase):
    kind: SectionItemFieldType = SectionItemFieldType.SINGLE_CHOICE
    options: Optional[list[LabeledEntity]] = None
    value: Optional[str] = None  # value of the field


class SectionItemMultiChoiceField(SectionItemBase):
    kind: SectionItemFieldType = SectionItemFieldType.MULTI_CHOICE
    options: Optional[list[LabeledEntity]] = None
    value: Optional[List[str]] = None  # value of the field


class SectionItemBooleanField(SectionItemBase):
    kind: SectionItemFieldType = SectionItemFieldType.BOOLEAN
    value: Optional[bool] = False  # value of the field
    # errorMsg: Optional[str] = None


class SectionItemAcknowledgementField(SectionItemBase):
    kind: SectionItemFieldType = SectionItemFieldType.ACKNOWLEDGEMENT
    value: Optional[bool] = False  # value of the field


class SectionItemLink(SectionItemBase):
    kind: SectionItemFieldType = SectionItemFieldType.LINK
    description: str | None = None
    text: str | None = None
    appTag: str | None = None


class SectionItemIntegrationCards(SectionItemBase):
    kind: SectionItemFieldType = SectionItemFieldType.INTEGRATION_CARDS
    filters: Optional[list[LabeledEntity]] = None

    # list of integration cards
    cards: Optional[List["SectionItemIntegrationCard"]] = None


class SectionItemIntegrationCard(SectionItemBase):
    # type of the data item
    kind: SectionItemFieldType = SectionItemFieldType.INTEGRATION_CARD

    # tag name by which to filter
    tag: str

    # indicates whether user has completed this integration
    isActive: bool

    redirectPath: Optional[str] = None

    value: Optional[str] = None

    isInteractible: Optional[bool] = False


class PlanUser(BaseModel):
    uuid: UUID
    name: str
    email: str


class PlanFeature(BaseModel):
    id: str
    label: str
    license_count: int
    user_uuids: list[UUID]


class OrganizationPlanDetails(BaseModel):
    plan_term: Literal["monthly", "yearly"]
    meetings_allowed_per_term: int
    meetings_used_this_term: int
    features: list[PlanFeature]
    users: list[PlanUser] = []


class SectionItemPlanDetails(SectionItemBase):
    kind: SectionItemFieldType = SectionItemFieldType.PLAN_DETAILS
    plan_name: str | None = None
    enabled_features: list[str] = []
    organization_plan_details: OrganizationPlanDetails | None = None


class PlanFeaturesUpdate(BaseModel):
    feature_id: str
    user_uuids: list[UUID] | None = None


# This is used to save settings that are editable by the user.
class SaveRequest(BaseModel):
    name: str | None = None
    companyName: str | None = None
    calendarAutoJoin: bool | None = None
    calendarLookahead: str | None = None
    calendarShowMeetingsWithoutUrls: bool | None = None
    bentoTagsEnabled: bool | None = None
