from typing import Any, Generator
from unittest.mock import MagicMock, patch

import pytest
from django.utils import timezone
from fastapi import status
from fastapi.testclient import TestClient

from api.dependencies import user_from_authorization_header
from api.internal_api import internal_api as app
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.flags import Flag
from deepinsights.users.models.user import User

client = TestClient(app)

pytestmark = [pytest.mark.django_db(transaction=True)]


@pytest.fixture(autouse=True)
def setUpTearDown() -> Generator[Any, None, None]:
    app.dependency_overrides = {}
    yield
    app.dependency_overrides = {}


@pytest.fixture
def test_organization() -> Organization:
    return Organization.objects.create(name="Test Org")


@pytest.fixture
def test_user(django_user_model: User, test_organization: Organization) -> User:
    test_user = django_user_model.objects.create(
        username="<EMAIL>",
        first_name="Test",
        last_name="User",
        organization=test_organization,
        license_type=User.LicenseType.advisor,
    )
    app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.get(username="<EMAIL>")
    return test_user


def _setFlagActive(flag: Flags, is_active: bool) -> None:
    # Sets the provided flag to the given active state.
    f = Flag.objects.get(name=flag.name)
    f.everyone = is_active
    f.override_enabled_by_environment = None
    f.save()


def test_setup_oauth_unauthenticated() -> None:
    response = client.post("/api/v2/oauth/configure")
    assert response.status_code == status.HTTP_403_FORBIDDEN


def test_setup_oauth_invalid_data(test_user: User) -> None:
    response = client.post("/api/v2/oauth/configure", json={"invalid": "data"})
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


@pytest.mark.parametrize(
    "test_input,expected_status",
    [
        # Invalid provider test
        (
            {"provider": "invalid", "authorization_code": "test", "request_url": "http://example.com"},
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        ),
        # Missing authorization_code test
        ({"request_url": "http://example.com", "provider": "microsoft"}, status.HTTP_422_UNPROCESSABLE_ENTITY),
        # Missing provider test
        ({"authorization_code": "test", "request_url": "http://example.com"}, status.HTTP_422_UNPROCESSABLE_ENTITY),
        # Missing request_url test
        ({"authorization_code": "test", "provider": "microsoft"}, status.HTTP_422_UNPROCESSABLE_ENTITY),
        # Invalid URL format test
        (
            {"provider": "microsoft", "authorization_code": "test", "request_url": "not-a-valid-url"},
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        ),
    ],
)
def test_oauth_configuration_validation(test_user: User, test_input: dict[str, str], expected_status: int) -> None:
    response = client.post("/api/v2/oauth/configure", json=test_input)
    assert response.status_code == expected_status


@patch("api.routers.oauth.update_recall_auto_join_integration")
@patch("deepinsights.core.integrations.oauth.microsoft.MicrosoftOAuth.exchange_tokens")
@patch("api.routers.oauth.reconcile_calendar_events")
def test_microsoft_oauth_success(
    mock_reconcile_calendar_events: MagicMock,
    mock_exchange_tokens: MagicMock,
    mock_update_recall_auto_join: MagicMock,
    test_user: User,
) -> None:
    mock_exchange_tokens.return_value = None
    mock_update_recall_auto_join.return_value = None

    response = client.post(
        "/api/v2/oauth/configure",
        json={"provider": "microsoft", "authorization_code": "test", "request_url": "http://example.com/"},
    )
    assert response.status_code == status.HTTP_201_CREATED

    # Get the actual calls made to the mock
    assert mock_exchange_tokens.call_count == 1
    call_args = mock_exchange_tokens.call_args

    assert call_args.kwargs["authorization_code"] == "test"
    assert call_args.kwargs["user"] == test_user
    assert str(call_args.kwargs["redirect_uri"]) == "http://example.com/"

    mock_reconcile_calendar_events.delay_on_commit.assert_called_once_with(test_user.uuid)
    mock_update_recall_auto_join.assert_called_once()


@patch("deepinsights.core.integrations.oauth.microsoft.MicrosoftOAuth.exchange_tokens")
@patch("api.routers.oauth.update_recall_auto_join_integration")
@patch("api.routers.oauth.reconcile_calendar_events")
def test_microsoft_oauth_existing_credentials(
    mock_reconcile_calendar_events: MagicMock,
    update_recall_auto_join_integration: MagicMock,
    mock_exchange_tokens: MagicMock,
    test_user: User,
) -> None:
    mock_exchange_tokens.return_value = None

    # Create existing credentials
    OAuthCredentials.objects.create(
        user=test_user,
        integration="microsoft",
        access_token="test",
        refresh_token="test",
        expires_in=timezone.now(),
        refresh_token_expires_in=timezone.now(),
    )

    response = client.post(
        "/api/v2/oauth/configure",
        json={"provider": "microsoft", "authorization_code": "test", "request_url": "http://example.com"},
    )
    assert response.status_code == status.HTTP_201_CREATED
    update_recall_auto_join_integration.assert_not_called()
    mock_reconcile_calendar_events.delay_on_commit.assert_called_once_with(test_user.uuid)


@patch("api.routers.oauth.update_recall_auto_join_integration")
@patch("deepinsights.core.integrations.oauth.google.GoogleOAuth.exchange_tokens")
@patch("api.routers.oauth.reconcile_calendar_events")
def test_google_oauth_success(
    mock_reconcile_calendar_events: MagicMock,
    mock_exchange_tokens: MagicMock,
    mock_update_recall_auto_join: MagicMock,
    test_user: User,
) -> None:
    mock_exchange_tokens.return_value = None
    mock_update_recall_auto_join.return_value = None

    response = client.post(
        "/api/v2/oauth/configure",
        json={"provider": "google", "authorization_code": "test", "request_url": "http://example.com/"},
    )
    assert response.status_code == status.HTTP_201_CREATED
    mock_exchange_tokens.assert_called_once_with(
        authorization_code="test", user=test_user, redirect_uri="http://example.com/"
    )
    mock_reconcile_calendar_events.delay_on_commit.assert_called_once_with(test_user.uuid)
    mock_update_recall_auto_join.assert_called_once()


@patch("deepinsights.core.integrations.oauth.wealthbox.WealthBoxOAuth.exchange_tokens")
@patch("api.routers.oauth.sync_crm_clients")
@patch("api.routers.oauth.reconcile_calendar_events")
def test_wealthbox_oauth_success(
    mock_reconcile_calendar_events: MagicMock,
    sync_crm_clients: MagicMock,
    mock_exchange_tokens: MagicMock,
    test_user: User,
) -> None:
    mock_exchange_tokens.return_value = None

    response = client.post(
        "/api/v2/oauth/configure",
        json={"provider": "wealthbox", "authorization_code": "test", "request_url": "http://example.com/"},
    )
    assert response.status_code == status.HTTP_201_CREATED

    mock_exchange_tokens.assert_called_once_with("test", test_user, "http://example.com/")
    sync_crm_clients.delay_on_commit.assert_called_once_with(test_user.uuid)
    mock_reconcile_calendar_events.delay_on_commit.assert_not_called()


@patch("deepinsights.core.integrations.oauth.salesforce.SalesforceOAuth.exchange_tokens")
def test_salesforce_oauth_success(mock_exchange_tokens: MagicMock, test_user: User) -> None:
    mock_exchange_tokens.return_value = None
    response = client.post(
        "/api/v2/oauth/configure",
        json={"provider": "salesforce", "authorization_code": "test", "request_url": "http://example.com/"},
    )
    assert response.status_code == status.HTTP_201_CREATED

    mock_exchange_tokens.assert_called_once()


@pytest.mark.parametrize(
    "provider,oauth_class_name",
    [
        ("microsoft", "MicrosoftOAuth"),
        ("google", "GoogleOAuth"),
        ("wealthbox", "WealthBoxOAuth"),
        ("salesforce", "SalesforceOAuth"),
    ],
)
@patch("api.routers.oauth.reconcile_calendar_events")
def test_oauth_provider_error_handling(
    mock_reconcile_calendar_events: MagicMock, provider: str, oauth_class_name: str, test_user: User
) -> None:
    patch_path = f"deepinsights.core.integrations.oauth.{provider}.{oauth_class_name}"

    with patch(patch_path) as mock_oauth:
        mock_oauth.return_value.exchange_tokens.side_effect = Exception("Test error")

        response = client.post(
            "/api/v2/oauth/configure",
            json={"provider": provider, "authorization_code": "test", "request_url": "http://example.com"},
        )

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert response.json() == {"detail": "Failed to set up OAuth integration"}

        mock_reconcile_calendar_events.delay_on_commit.assert_not_called()


def test_delete_oauth_integration_success(test_user: User) -> None:
    provider = "salesforce"
    test_user.crm_configuration = {
        "crm_system": "salesforce",
        "salesforce": {"endpoint": "salesforce.example.com"},
        "default_client_filter": "all",
    }
    test_user.save()
    OAuthCredentials.objects.create(
        user=test_user,
        integration=provider,
        access_token="test",
        refresh_token="test",
        expires_in=timezone.now(),
        refresh_token_expires_in=timezone.now(),
    )

    response = client.delete(f"/api/v2/oauth/delete?provider={provider}")
    test_user.refresh_from_db()

    assert response.status_code == status.HTTP_204_NO_CONTENT

    assert not OAuthCredentials.objects.filter(user=test_user, integration=provider).exists()
    # crm_system should be reset, and 'salesforce' key removed
    assert test_user.crm_configuration["crm_system"] is None
    assert "salesforce" not in test_user.crm_configuration


def test_delete_oauth_integration_removes_provider_but_preserves_active_crm(test_user: User) -> None:
    test_user.crm_configuration = {
        "crm_system": "wealthbox",
        "wealthbox": {"endpoint": "wealthbox.example.com"},
        "salesforce": {"endpoint": "salesforce.example.com"},
        "default_client_filter": "all",
    }
    test_user.save()

    OAuthCredentials.objects.create(
        user=test_user,
        integration="salesforce",
        access_token="test",
        refresh_token="test",
        expires_in=timezone.now(),
        refresh_token_expires_in=timezone.now(),
    )

    response = client.delete("/api/v2/oauth/delete?provider=salesforce")
    test_user.refresh_from_db()

    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not OAuthCredentials.objects.filter(user=test_user, integration="salesforce").exists()
    assert test_user.crm_configuration["crm_system"] == "wealthbox"
    assert "salesforce" not in test_user.crm_configuration


def test_delete_active_crm_resets_crm_system(test_user: User) -> None:
    # User has salesforce and wealthbox integrated
    OAuthCredentials.objects.create(
        user=test_user,
        integration="wealthbox",
        access_token="wealthbox_token",
        refresh_token="wealthbox_refresh",
        expires_in=timezone.now(),
        refresh_token_expires_in=timezone.now(),
    )
    OAuthCredentials.objects.create(
        user=test_user,
        integration="salesforce",
        access_token="salesforce_token",
        refresh_token="salesforce_refresh",
        expires_in=timezone.now(),
        refresh_token_expires_in=timezone.now(),
    )

    test_user.crm_configuration = {
        "crm_system": "salesforce",
        "wealthbox": {"endpoint": "https://wealthbox.example.com"},
        "salesforce": {"salesforce_endpoint": "https://salesforce.example.com"},
    }
    test_user.save()

    response = client.delete("/api/v2/oauth/delete?provider=salesforce")
    assert response.status_code == status.HTTP_204_NO_CONTENT

    test_user.refresh_from_db()

    assert not OAuthCredentials.objects.filter(user=test_user, integration="salesforce").exists()
    # crm_system should now be reset
    assert test_user.crm_configuration["crm_system"] is None
    # salesforce config removed
    assert "salesforce" not in test_user.crm_configuration
    # wealthbox config remains intact
    assert test_user.crm_configuration["wealthbox"]["endpoint"] == "https://wealthbox.example.com"


def test_delete_oauth_integration_when_crm_system_already_none(test_user: User) -> None:
    test_user.crm_configuration = {
        "crm_system": None,
        "salesforce": {"endpoint": "salesforce.example.com"},
    }
    test_user.save()

    OAuthCredentials.objects.create(
        user=test_user,
        integration="salesforce",
        access_token="test",
        refresh_token="test",
        expires_in=timezone.now(),
        refresh_token_expires_in=timezone.now(),
    )

    response = client.delete("/api/v2/oauth/delete?provider=salesforce")
    test_user.refresh_from_db()

    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not OAuthCredentials.objects.filter(user=test_user, integration="salesforce").exists()
    assert "salesforce" not in test_user.crm_configuration
    assert test_user.crm_configuration["crm_system"] is None


def test_delete_oauth_integration_with_no_crm_config(test_user: User) -> None:
    test_user.crm_configuration = {}
    test_user.save()

    OAuthCredentials.objects.create(
        user=test_user,
        integration="salesforce",
        access_token="test",
        refresh_token="test",
        expires_in=timezone.now(),
        refresh_token_expires_in=timezone.now(),
    )

    response = client.delete("/api/v2/oauth/delete?provider=salesforce")
    test_user.refresh_from_db()

    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not OAuthCredentials.objects.filter(user=test_user, integration="salesforce").exists()
    assert "salesforce" not in test_user.crm_configuration
    assert test_user.crm_configuration.get("crm_system") is None


def test_delete_oauth_integration_returns_404_when_credentials_not_found(test_user: User) -> None:
    # Ensure user has no OAuthCredentials for 'salesforce'
    assert not OAuthCredentials.objects.filter(user=test_user, integration="salesforce").exists()

    response = client.delete("/api/v2/oauth/delete?provider=salesforce")

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json()["detail"] == "OAuth credentials not found"


def test_delete_integration_invalid_provider(test_user: User) -> None:
    response = client.delete("/api/v2/oauth/delete?provider=google")
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


def test_delete_oauth_integration_unauthenticated() -> None:
    response = client.delete("/api/v2/oauth/delete?provider=wealthbox")
    assert response.status_code == status.HTTP_403_FORBIDDEN


@patch("deepinsights.meetingsapp.models.oauth_credentials.OAuthCredentials.objects")
def test_delete_oauth_integration_internal_error(mock_objects: MagicMock, test_user: User) -> None:
    mock_objects.filter.side_effect = Exception("DB error")
    response = client.delete("/api/v2/oauth/delete?provider=wealthbox")
    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {"detail": "Failed to delete OAuth integration"}
