from unittest.mock import ANY, MagicMock, patch

import pytest
from django.contrib.auth.models import Group
from fastapi.openapi.models import OpenAPI
from fastapi.responses import HTMLResponse
from fastapi.testclient import TestClient

from api.dependencies import user_from_authorization_header
from api.internal_api import internal_api
from api.public_api import public_api
from deepinsights.users.models.user import User

client = TestClient(internal_api)

pytestmark = pytest.mark.django_db(transaction=True)


@pytest.fixture
def test_user() -> User:
    user = User.objects.create(username="<EMAIL>")
    internal_api.dependency_overrides[user_from_authorization_header] = lambda: User.objects.get(pk=user.pk)
    return user


class TestSwaggerDocument:
    @patch("api.routers.public_api_docs.get_swagger_ui_html")
    def test_swagger_document_success(self, mock_get_swagger_ui_html: MagicMock, test_user: User) -> None:
        group = Group.objects.create(name="public_api_docs_access")
        test_user.groups.add(group)
        test_user.save()

        mock_html_response = HTMLResponse("<html></html>")
        mock_get_swagger_ui_html.return_value = mock_html_response

        response = client.get("/api/v2/docs/public/docs")

        assert response.status_code == 200
        assert response.headers["content-type"] == "text/html; charset=utf-8"

        mock_get_swagger_ui_html.assert_called_once_with(
            openapi_url="_unused_",
            title=public_api.title,
            swagger_ui_parameters={"spec": ANY},
            swagger_js_url="/api/static/js/swagger-ui-bundle.js",
            swagger_css_url="/api/static/css/swagger-ui.css",
            swagger_favicon_url="/favicon.ico",
        )

        assert response.content == mock_html_response.body

        # Confirm that the spec is valid, and confirm a few properties.
        spec = OpenAPI(**mock_get_swagger_ui_html.call_args[1]["swagger_ui_parameters"]["spec"])
        assert spec
        assert spec.info.title == public_api.title
        assert spec.paths

    def test_swagger_document_access_denied(self, test_user: User) -> None:
        response = client.get("/api/v2/docs/public/docs")
        assert response.status_code == 403
