import logging
from datetime import datetime, timedelta
from enum import Enum, StrEnum
from typing import Any
from uuid import UUID

from django.utils import timezone
from pydantic import UUID4, BaseModel, Field

from deepinsights.core.ml.voice_memo_utils import replace_aliases_with_names
from deepinsights.meetingsapp.models.meeting_bot import MeetingBot
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingData
from deepinsights.users.models.user import User

## get note
# Model objects


class SummarySection(BaseModel):
    topic: str
    bullets: list[str]

    @classmethod
    def from_string(cls, section_string: str) -> "SummarySection":
        """Creates a SummarySection instance from a formatted string.

        Args:
            section_string: String in format "Topic: bullet1\nbullet2\nbullet3"

        Returns:
            SummarySection instance with parsed topic and bullets
        """
        topic, text = section_string.split(":", maxsplit=1)
        topic = topic.strip(" 1234567890.*#")
        bullets = [x.strip(" -*") for x in text.strip().split("\n")]
        return cls(topic=topic, bullets=bullets)

    @classmethod
    def from_frontend_format(cls, section_frontend_object: dict[str, Any]) -> "SummarySection":
        topic = section_frontend_object["text"]
        bullets = [x["text"] for x in section_frontend_object["nodes"]]
        return cls(topic=topic, bullets=bullets)

    def to_dict(self) -> dict[str, Any]:
        return {"topic": self.topic, "bullets": self.bullets}


class Summary(BaseModel):
    sections: list[SummarySection] = Field(default_factory=list)

    @classmethod
    def get_summary_for_note(cls, note: Note) -> "Summary":
        try:
            if note.summary:
                return cls.model_validate(note.summary)
            if note.summary_by_topics:
                summary_sections = [SummarySection.from_string(section_text) for section_text in note.summary_by_topics]
                return cls(sections=summary_sections)
        except Exception:
            logging.error("summary corrupted for note %s", note.uuid, exc_info=True)
        return cls(sections=[])

    @classmethod
    def from_frontend_format(cls, summary_frontend_object: list[dict[str, Any]]) -> "Summary":
        sections = [SummarySection.from_frontend_format(section) for section in summary_frontend_object]
        return cls(sections=sections)

    def to_dict(self) -> dict[str, Any]:
        return {"sections": [section.to_dict() for section in self.sections]}


class Utterance(BaseModel):
    speaker: str
    start: str
    end: str
    text: str


class Client(BaseModel):
    uuid: str
    name: str | None
    email: str | None


class Transcript(BaseModel):
    utterances: list[Utterance] = Field(default_factory=list)

    @classmethod
    def from_string(cls, transcript_string: str) -> "Transcript":
        """
        This method takes a string and returns a Transcript object.
        :param transcript_string: string
        :return: Transcript
        """
        if not transcript_string:
            return cls(utterances=[])

        utterances = []
        try:
            for utterance_string in transcript_string.split("\n\n"):
                if utterance_string.strip() != "":
                    speaker, time_and_text = utterance_string.split("\t", maxsplit=1)
                    timestamp, text = time_and_text.split("\n", maxsplit=1)
                    if "-->" in timestamp:
                        start, end = timestamp.split("-->", maxsplit=1)
                        start = start.strip('"\\')
                    else:
                        start = end = timestamp
                    utterances.append(Utterance(speaker=speaker, start=start, end=end, text=text))
        except Exception as e:
            logging.error('Error parsing utterance "%s"', utterance_string, exc_info=True)
        return cls(utterances=utterances)


class ActionItem(BaseModel):
    uuid: UUID
    content: str
    status: str


class NoteType(str, Enum):
    """Enum matching the Note.NOTE_TYPE choices in Django model"""

    MEETING_RECORDING = "meeting_recording"
    IMAGE = "image"
    VOICE_MEMO = "voice_memo"
    TRANSCRIPT = "transcript"


class ProcessingStatus(str, Enum):
    """Enum (mostly) matching the Note.PROCESSING_STATUS choices in Django model

    Besides the statuses in Note.PROCESSING_STATUS, this enum also includes an UNKNOWN status, which
    matches an empty status on the Django model.
    """

    UPLOADED = "uploaded"
    PROCESSED = "processed"
    MISSING = "missing"
    SCHEDULED = "scheduled"
    FINALIZED = "finalized"
    UNKNOWN = "unknown"

    @classmethod
    def _missing_(cls, _: Any) -> "ProcessingStatus":
        return cls.UNKNOWN


class AttendeeType(Enum):
    CLIENT = "client"
    USER = "user"
    UNKNOWN = "unknown"


class AttendeeInfoLite(BaseModel):
    uuid: UUID
    name: str


class AttendeeInfo(BaseModel):
    uuid: UUID
    name: str
    type: AttendeeType = AttendeeType.UNKNOWN
    speaker_time: timedelta | None = None
    speaker_percentage: float | None = None
    speaker_alias: str | None = None
    client_uuid: UUID | None = None
    user_uuid: UUID | None = None

    class Config:
        from_attributes = True
        json_encoders = {timedelta: lambda v: str(v), UUID4: lambda v: str(v)}


class FollowUpStatus(StrEnum):
    CREATED = "created"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    UNKNOWN = "unknown"

    @classmethod
    def _missing_(self, value: Any) -> "FollowUpStatus":
        logging.error("Invalid FollowUpStatus value: %s", value or "<empty value>")
        return FollowUpStatus.UNKNOWN


class FollowUp(BaseModel):
    uuid: UUID
    title: str | None = Field(
        description="The title of the follow-up. This is a human-readable name that is meant to be shown to users."
    )
    kind: str | None = Field(
        description="The kind of follow-up. This is meant to allow specialized handling for specific follow-ups."
    )
    schema_: dict[str, Any] | None = Field(
        alias="schema",
        description="The JSONSchema that describes the structure of the data field. This will be a valid JSONSchema as defined at https://json-schema.org/.",
    )
    data: dict[str, Any] | None = Field(
        description="The JSON data for this follow-up. It will match the structure in the schema field."
    )
    status: FollowUpStatus = Field(description="The status of the follow-up.")

    @classmethod
    def from_structured_meeting_data(cls, structured_meeting_data: StructuredMeetingData) -> "FollowUp":
        return cls(
            uuid=structured_meeting_data.uuid,
            title=structured_meeting_data.title,
            kind=structured_meeting_data.kind,
            schema=structured_meeting_data.schema,
            data=structured_meeting_data.data,
            status=FollowUpStatus(structured_meeting_data.status),
        )


class NoteResponse(BaseModel):
    uuid: UUID
    created: datetime
    modified: datetime
    note_type: NoteType | None
    status: ProcessingStatus
    meeting_name: str
    meeting_duration_seconds: float
    meeting_type_uuid: UUID | None
    meeting_category: str
    attendees: list[AttendeeInfo] = Field(default_factory=list)
    tags: list[str] = Field(default_factory=list)
    action_items: list[ActionItem] = Field(default_factory=list)
    advisor_notes: list[str] | None = None
    key_takeaways: list[str] | None = None
    client: Client | None = None
    transcript: Transcript
    summary_by_topics: Summary = Field(default_factory=lambda: Summary(sections=[]))
    bot_id: UUID | None = None
    is_deleted: bool
    features: list[str] = Field(default_factory=list)
    follow_ups: list[FollowUp] = Field(default_factory=list)
    authorized_user_uuids: list[UUID] = Field(default_factory=list)
    interaction_uuid: UUID | None = None
    times_editable: bool
    scheduled_start_time: datetime | None = None
    scheduled_end_time: datetime | None = None

    class Config:
        # Allow string values to be automatically converted to enums
        use_enum_values = True


# helper methods


def get_action_items(note: Note) -> list[ActionItem]:
    ais: list[ActionItem] = [
        ActionItem(content=task.task_title, uuid=task.uuid, status=task.get_completed()) for task in note.task_set.all()
    ]
    return ais


def get_utterances(note: Note) -> list[Utterance]:
    transcript_with_names = replace_aliases_with_names(note)
    return Transcript.from_string(transcript_with_names).utterances


def get_note_response(note: Note, user: User) -> NoteResponse:
    note_age_days = (timezone.now().date() - note.created.date()).days

    if user.get_preferences().show_transcript_in_frontend and note_age_days <= 30:
        transcript = Transcript(utterances=get_utterances(note))
    else:
        transcript = Transcript()
    client = note.client
    metadata = note.metadata or {}

    scheduled_start_time: datetime | str | None = None
    scheduled_end_time: datetime | str | None = None
    times_editable = True
    if scheduled_event := note.scheduled_event:
        scheduled_start_time = scheduled_event.start_time.isoformat()
        scheduled_end_time = scheduled_event.end_time.isoformat()
        times_editable = not bool(scheduled_event.user_specific_source_id)
    else:
        scheduled_start_time = note.scheduled_at
        scheduled_end_time = None
        times_editable = True

    return NoteResponse(
        uuid=note.uuid,
        created=note.created,
        modified=note.modified,
        note_type=NoteType(note.note_type),
        status=ProcessingStatus(note.status),
        meeting_name=metadata.get("meeting_name", ""),
        meeting_duration_seconds=metadata.get("meeting_duration", 0),
        meeting_type_uuid=note.meeting_type.uuid if note.meeting_type else None,
        meeting_category=note.category,
        attendees=[
            AttendeeInfo(
                uuid=attendee_info.uuid,
                name=attendee_info.attendee_name or "",
                type=AttendeeType.CLIENT
                if attendee_info.client
                else AttendeeType.USER
                if attendee_info.user
                else AttendeeType.UNKNOWN,
                speaker_time=attendee_info.speaker_time,
                speaker_percentage=attendee_info.speaker_percentage,
                speaker_alias=attendee_info.speaker_alias,
                client_uuid=attendee_info.client.uuid if attendee_info.client else None,
                user_uuid=attendee_info.user.uuid if attendee_info.user else None,
            )
            for attendee_info in note.attendees.all()
        ],
        tags=metadata.get("tags", []),
        action_items=get_action_items(note),
        advisor_notes=note.advisor_notes,
        key_takeaways=note.key_takeaways,
        client=(
            Client(uuid=client.get("uuid"), email=client.get("email"), name=client.get("name"))
            if client and client.get("uuid")
            else None
        ),
        transcript=transcript,
        summary_by_topics=Summary.get_summary_for_note(note),
        bot_id=note.bot_uuid,
        is_deleted=note.is_deleted,
        features=note.features,
        follow_ups=[
            FollowUp(
                uuid=structured_meeting_data.uuid,
                title=structured_meeting_data.title,
                kind=structured_meeting_data.kind,
                schema=structured_meeting_data.schema,
                data=structured_meeting_data.data,
                status=FollowUpStatus(structured_meeting_data.status),
            )
            for structured_meeting_data in note.structuredmeetingdata_set.all()
        ],
        authorized_user_uuids=[u.uuid for u in note.authorized_users.all()],
        interaction_uuid=note.clientinteraction.uuid if note.has_client_interaction() else None,
        times_editable=times_editable,
        scheduled_start_time=scheduled_start_time,
        scheduled_end_time=scheduled_end_time,
    )


## list notes


class NoteAudioSource(StrEnum):
    MIC = "mic"
    PHONE = "phone"
    VIDEO_CALL = "video_call"


class ListNotesResponse(BaseModel):
    uuid: UUID
    created: str
    modified: str
    note_type: str
    status: ProcessingStatus
    meeting_name: str
    client: Client | None
    scheduled_start_time: str | None
    scheduled_end_time: str | None
    tags: list[str]
    meeting_source_id: str | None
    scheduled_event_uuid: UUID | None
    attendees: list[AttendeeInfoLite]
    meeting_category: MeetingType.Category | None
    meeting_type: str | None
    owner: AttendeeInfoLite
    audio_source: NoteAudioSource
    meeting_duration_seconds: float | None = None
    autojoin_available: bool | None = None
    autojoin_enabled: bool | None = None
    autojoin_editable: bool | None = None
    meeting_link: str | None = None


def list_notes_response(note: Note) -> ListNotesResponse:
    client = note.client
    metadata = note.metadata or {}
    source = NoteAudioSource.MIC
    meeting_link: str | None = None
    autojoin_available_for_bot: bool = False
    if note.meetingbot_set.exists():
        bot = note.meetingbot_set.all()[0]
        link_type, _ = bot.link_type()
        source = NoteAudioSource.PHONE if link_type == MeetingBot.LinkType.PHONE_NUMBER else NoteAudioSource.VIDEO_CALL
        meeting_link = bot.meeting_link
        autojoin_available_for_bot = bot.supports_scheduled_bots

    scheduled_start_time: datetime | str | None = None
    scheduled_end_time: datetime | str | None = None
    scheduled_event_uuid: UUID | None = None
    autojoin_available = False
    autojoin_enabled = False
    autojoin_editable = False
    if scheduled_event := note.scheduled_event:
        scheduled_start_time = scheduled_event.start_time.isoformat()
        scheduled_end_time = scheduled_event.end_time.isoformat()
        scheduled_event_uuid = scheduled_event.uuid
        autojoin_available = (not scheduled_event.is_from_external_system) and autojoin_available_for_bot
        autojoin_enabled = scheduled_event.autojoin_behavior == ScheduledEvent.AutoJoinOverride.ENABLED
        autojoin_editable = scheduled_event.start_time > timezone.now()
    else:
        scheduled_start_time = note.scheduled_at
        scheduled_end_time = None
        scheduled_event_uuid = None
        autojoin_available = False
        autojoin_enabled = False
        autojoin_editable = False

    return ListNotesResponse(
        owner=AttendeeInfoLite(uuid=note.note_owner.uuid, name=note.note_owner.get_full_name())
        if note.note_owner
        else None,
        uuid=note.uuid,
        created=note.created.isoformat() if note.created else "",
        modified=note.modified.isoformat() if note.modified else "",
        # The Pydantic validation handles the case where the model values are
        # unexpectedly null, so we ignore the type errors.
        note_type=note.note_type,
        status=ProcessingStatus(note.status),
        meeting_name=metadata.get("meeting_name", ""),
        client=(
            Client(uuid=client.get("uuid"), email=client.get("email"), name=client.get("name"))
            if client and client.get("uuid")
            else None
        ),
        tags=metadata.get("tags", []),
        meeting_source_id=metadata.get("meeting_source_id"),
        scheduled_event_uuid=scheduled_event_uuid,
        attendees=[
            AttendeeInfoLite(uuid=attendee_info.uuid, name=attendee_info.attendee_name or "")
            for attendee_info in note.attendees.all()
        ],
        meeting_type=note.meeting_type.name if note.meeting_type else None,
        meeting_category=note.meeting_type.category if note.meeting_type else None,
        audio_source=source,
        meeting_duration_seconds=metadata.get("meeting_duration", 0),
        scheduled_start_time=scheduled_start_time,
        scheduled_end_time=scheduled_end_time,
        autojoin_available=autojoin_available,
        autojoin_enabled=autojoin_enabled,
        autojoin_editable=autojoin_editable,
        meeting_link=meeting_link,
    )


class EmailTemplateResponse(BaseModel):
    uuid: str
    name: str
    description: str | None = None


# email_followup_with_mailto_content
# Model Objects


class MailtoResponse(BaseModel):
    mailto_link: str


# create_or_update_note
# Model objects


class CreateOrUpdateNoteResponse(BaseModel):
    # The ID of the note that was created or updated.
    note_id: UUID

    # Whether or not the note is in a "finished" state (i.e., it has audio data or a bot recording
    # for a meeting that can be processed).
    completed: bool

    # The UUID of the bot that is associated with this note (if any).
    #
    # This is only set if the flag for enabling saving of scheduled notes is enabled. Further, it may
    # not be set for create/update requets that include audio data (which short circuit the handling of
    # bots, becuase we prioritize processing the audio data).
    bot_id: UUID | None = None


# swap_attendees
# Model objects


class SwapPair(BaseModel):
    current_alias: str = Field(description="Current speaker alias (Name)")
    new_alias: str = Field(description="New speaker alias (UUID)")


class SwapAttendeesRequest(BaseModel):
    note_id: str = Field(description="UUID of the note")
    swaps: list[SwapPair] = Field(description="List of alias swaps to perform")


# edit_note
# Model objects


class ActionType(str, Enum):
    DELETE = "delete"
    CREATE = "create"
    KEEP = "keep"


class ActionItemUpdate(BaseModel):
    action: ActionType
    item: ActionItem


class EditNoteRequest(BaseModel):
    """
    Request model for editing a note. Fields that are None indicate no changes requested,
    while empty lists/values indicate explicit clearing of that field.
    """

    meetingName: str | None = None
    advisorNotes: list[str] | None = None  # None = no change, [] = clear all notes
    keyTakeaways: list[str] | None = None  # None = no change, [] = clear all takeaways
    actionItems: list[ActionItemUpdate] | None = Field(
        None,
        description="List of action item updates to apply. None means no changes to existing "
        "items, empty list means remove all items. Each update specifies whether "
        "to create/keep/delete individual items.",
    )
    client: Client | None = None
    summary: Summary | None = None
    attendees: list[AttendeeInfo] | None = None
