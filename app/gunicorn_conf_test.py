from unittest.mock import MagicMock, patch

from app.gunicorn_conf import post_worker_init


def test_post_worker_init_logs_worker_spawned() -> None:
    worker_mock = MagicMock()
    worker_mock.pid = 1234

    post_worker_init(worker_mock)

    worker_mock.log.info.assert_called_once_with("Worker process initialized (pid: %s)", worker_mock.pid)


@patch("app.gunicorn_conf.configure_telemetry")
def test_post_worker_init_configures_opentelemetry(mock_configure_telemetry: MagicMock) -> None:
    post_worker_init(MagicMock())

    mock_configure_telemetry.assert_called_once_with("django")
