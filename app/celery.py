import logging
import os
from typing import Any

from celery import Celery

# This import exists, but the celery-types library does not include it.
from celery.app.log import TaskFormatter  # type: ignore[attr-defined]
from celery.app.task import Task
from celery.schedules import crontab
from celery.signals import after_setup_logger, after_setup_task_logger, worker_process_init
from celery.utils.log import ColorFormatter
from django.conf import settings

from app.telemetry import TraceIDLogFilter, configure_telemetry

# https://github.com/sbdchd/celery-types?tab=readme-ov-file#install
Task.__class_getitem__ = classmethod(lambda cls, *args, **kwargs: cls)  # type: ignore[attr-defined]


@worker_process_init.connect(weak=False)
def worker_process_init_handler(*args: Any, **kwargs: Any) -> Any:
    configure_telemetry("celery")


# Extends the Celery TaskFormatter to add trace-related data to the record being logged.
class ColorTraceIDFormatter(ColorFormatter):
    def format(self, record: logging.LogRecord) -> str:
        TraceIDLogFilter().filter(record)
        return super().format(record)


# Extends the Celery TaskFormatter to add trace-related data to the record being logged.
class TaskTraceIDFormatter(TaskFormatter):  # type: ignore[misc]
    def format(self, record: logging.LogRecord) -> str:
        TraceIDLogFilter().filter(record)
        return super().format(record)  # type: ignore[no-any-return]


@after_setup_logger.connect
def after_setup_logger_handler(logger: logging.Logger, colorize: bool, **_: Any) -> Any:
    # Ensure that span and trace IDs are attached to the Celery main process logger.
    #
    # We experimented with a few different approaches for getting this to work, most of which did
    # not work:
    # - setting the celery log format in the Django settings and adding a log filter that would add
    #   the trace_id and span_id in this signal handler
    # - adding a log filter to the logger to add the trace_id and span_id and using the base
    #   ColorFormatter here
    # It's possible that there is a cleaner way to handle this, but this seems to work, and this
    # Celery signal is meant for configuring the logger after it is otherwise set up.
    for handler in logger.handlers:
        handler.setFormatter(
            ColorTraceIDFormatter(
                "[%(asctime)s: %(levelname)s/%(processName)s] [trace_id=%(otelTraceID)s span_id=%(otelSpanID)s] %(message)s",
                use_color=colorize,
            )
        )


@after_setup_task_logger.connect
def after_setup_task_logger_handler(logger: logging.Logger, colorize: bool, **_: Any) -> Any:
    # Ensure that span and trace IDs are attached to the Celery task process loggers.
    #
    # See above for a bit more context.
    for handler in logger.handlers:
        handler.setFormatter(
            TaskTraceIDFormatter(
                "[%(asctime)s: %(levelname)s/%(processName)s] [trace_id=%(otelTraceID)s span_id=%(otelSpanID)s] %(task_name)s[%(task_id)s] %(message)s",
                use_color=colorize,
            )
        )


# Set the default Django settings module for Celery
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.settings")

# Create a new instance of Celery
app = Celery("deepinsight")

# Load configuration from your Django settings
app.config_from_object("django.conf:settings", namespace="CELERY")


@app.task
def send_agenda_nudges() -> None:
    from deepinsights.meetingsapp.tasks import send_agenda_nudges

    send_agenda_nudges()


@app.task
def send_task_reminders():  # type: ignore[no-untyped-def]
    from deepinsights.core.notifications.task_reminder import send_task_reminders

    send_task_reminders()


@app.task
def delete_old_transcripts():  # type: ignore[no-untyped-def]
    from deepinsights.meetingsapp.tasks import delete_old_transcripts

    delete_old_transcripts()


@app.task
def delete_older_notes():  # type: ignore[no-untyped-def]
    from deepinsights.meetingsapp.tasks import delete_older_notes

    delete_older_notes()


@app.task(soft_time_limit=600, time_limit=720)
def export_org_metrics():  # type: ignore[no-untyped-def]
    from deepinsights.meetingsapp.tasks import export_org_metrics

    export_org_metrics()


@app.task
def email_waitlisted_user_report() -> None:
    from deepinsights.meetingsapp.tasks import email_waitlisted_user_report

    email_waitlisted_user_report()


@app.task(soft_time_limit=600, time_limit=720)
def export_monthly_calendar_events(
    org_ids: list[str] | None = None,
    user_ids: list[str] | None = None,
    start_date: str | None = None,
    end_date: str | None = None,
    filepath: str | None = None,
) -> None:
    from deepinsights.meetingsapp.tasks import export_calendar_events_data

    export_calendar_events_data(
        org_ids=org_ids, user_ids=user_ids, start_date=start_date, end_date=end_date, filepath=filepath
    )


@app.task
def send_meeting_prep_advance_email() -> None:
    from deepinsights.meetingsapp.tasks_meeting_prep import send_meeting_prep_advance_email

    send_meeting_prep_advance_email()


@app.task
def create_meeting_prep_in_advance() -> None:
    from deepinsights.meetingsapp.tasks_meeting_prep import create_meeting_prep_in_advance

    create_meeting_prep_in_advance()


@app.on_after_finalize.connect
def setup_periodic_tasks(sender, **_):  # type: ignore[no-untyped-def]
    locally_disabled_tasks = []

    sender.add_periodic_task(
        crontab(minute="*"),
        send_agenda_nudges,
    )

    sender.add_periodic_task(
        crontab(hour="5", minute="30"),
        send_task_reminders,
    )

    sender.add_periodic_task(
        crontab(hour="6", minute="0"),
        delete_old_transcripts,
    )

    sender.add_periodic_task(
        crontab(hour="6", minute="0"),
        delete_older_notes,
    )

    sender.add_periodic_task(
        crontab(hour="0", minute="0"),  # midnight UTC, 5pm Pacific
        create_meeting_prep_in_advance,
    )

    sender.add_periodic_task(
        crontab(hour="1", minute="0"),
        send_meeting_prep_advance_email,
    )

    locally_disabled_tasks.append(
        sender.add_periodic_task(
            crontab(hour="22", minute="30", day_of_month="1"),
            export_monthly_calendar_events,
        )
    )

    locally_disabled_tasks.append(
        sender.add_periodic_task(
            crontab(hour="22", minute="0", day_of_week="friday"),
            export_org_metrics,
            name="export_org_metrics_weekly",
        )
    )
    # Also send the metrics on the first of the month, at midnight Pacific standard time, for cases
    # where we need monthly metrics in a meeting that falls before the first Friday of the month.
    # Experimentally, Celery's crontab determines the next run date that matches both the day of the
    # week and the day of the month, rather than the next run date that matches either.
    # cf https://zeplyn.atlassian.net/browse/ENG-1223
    locally_disabled_tasks.append(
        sender.add_periodic_task(
            crontab(hour="8", minute="0", day_of_month="1"),
            export_org_metrics,
            name="export_org_metrics_monthly",
        )
    )

    locally_disabled_tasks.append(
        sender.add_periodic_task(crontab(hour="22", minute="0", day_of_week="friday"), email_waitlisted_user_report)
    )

    if "localhost:3000" not in settings.APP_DOMAIN:
        return

    from django_celery_beat.models import PeriodicTask

    for task_key in locally_disabled_tasks:
        try:
            task = PeriodicTask.objects.get(name=task_key)
        except PeriodicTask.DoesNotExist:
            logging.info("Attempted to disable task that does not exist, skipping: %s", task_key)
            continue
        logging.info("Disabling task for localhost: %s", task)
        task.enabled = False
        task.save()


# Discover tasks modules in your Django project
app.autodiscover_tasks()
