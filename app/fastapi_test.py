import logging
from typing import Any
from unittest.mock import AsyncMock, Mock, patch

import pytest
from pytest_django.fixtures import SettingsWrapper

from app.fastapi import (
    get_admin_action,
    get_admin_post_data,
    get_model_name,
    get_user_from_session,
    log_admin_action,
    log_admin_requests,
    log_requests,
)


@pytest.fixture
def mock_request() -> Mock:
    request = Mock()
    request.method = "POST"
    request.cookies = {"sessionid": "test_session"}
    request.url = Mock()
    request.url.path = "/api/admin/meetingsapp/organization/"
    request.headers = {"content-type": "application/x-www-form-urlencoded"}
    request.body = AsyncMock(return_value=b"action=delete_selected&selected=1")
    return request


@pytest.fixture
def mock_response() -> Mock:
    response = Mock()
    response.status_code = 302
    return response


@pytest.fixture
def mock_call_next() -> AsyncMock:
    return AsyncMock(return_value=Mo<PERSON>(status_code=200))


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_case",
    [
        {
            "name": "success_case",
            "input": b"action=delete_selected&selected=1,2,3",
            "expected": {"action": "delete_selected", "selected": "1,2,3"},
            "description": "Basic successful case with multiple values",
        },
        {
            "name": "url_encoded_chars",
            "input": b"action=test%20action&data=special%26chars",
            "expected": {"action": "test%20action", "data": "special%26chars"},
            "description": "URL-encoded special characters",
        },
        {
            "name": "malformed_data",
            "input": b"invalid=data&noequals",
            "expected": {"invalid": "data"},
            "description": "Malformed but partially parseable data",
        },
        {"name": "empty_data", "input": b"", "expected": {}, "description": "Empty input data"},
        {
            "name": "invalid_utf8",
            "input": b"\xff\xfe invalid utf-8",
            "expected": {},
            "description": "Invalid UTF-8 encoded data",
        },
    ],
)
async def test_get_admin_post_data(test_case: dict[str, Any]) -> None:
    """Test various scenarios for parsing POST data"""
    result = await get_admin_post_data(test_case["input"])
    assert result == test_case["expected"], f"Failed on {test_case['name']}: {test_case['description']}"


@pytest.mark.parametrize(
    "path,expected",
    [
        ("/api/admin/meetingsapp/organization/", "meetingsapp"),
        ("/api/admin/users/user/", "users"),
        ("/api/admin/", None),
        ("invalid/path", None),
    ],
)
def test_get_model_name(path: str, expected: str | None) -> None:
    """Test model name extraction from paths"""
    assert get_model_name(path) == expected


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_case",
    [
        {
            "name": "add_url_pattern",
            "body": b"",
            "path": "/api/admin/meetingsapp/organization/add/",
            "content_type": None,
            "expected": "add",
        },
        {
            "name": "delete_url_pattern",
            "body": b"",
            "path": "/api/admin/meetingsapp/organization/delete/",
            "content_type": None,
            "expected": "delete",
        },
        {
            "name": "change_url_pattern",
            "body": b"",
            "path": "/api/admin/meetingsapp/organization/123/change/",
            "content_type": None,
            "expected": "change",
        },
        {
            "name": "form_data_action",
            "body": b"action=delete_selected&selected=1",
            "path": "/api/admin/meetingsapp/organization/",
            "content_type": "application/x-www-form-urlencoded",
            "expected": "delete_selected",
        },
        {
            "name": "multipart_form_data",
            "body": b'--boundary\r\nContent-Disposition: form-data; name="action"\r\n\r\ndelete_selected\r\n--boundary--',
            "path": "/api/admin/meetingsapp/organization/",
            "content_type": "multipart/form-data; boundary=boundary",
            "expected": "viewed",
        },
        {
            "name": "invalid_utf8_data",
            "body": b"\xff\xfe" + b"action=delete_selected",
            "path": "/api/admin/meetingsapp/organization/",
            "content_type": "application/x-www-form-urlencoded",
            "expected": "viewed",
        },
    ],
)
async def test_get_admin_action(test_case: dict[str, Any]) -> None:
    result = await get_admin_action(test_case["body"], test_case["path"], test_case["content_type"])
    assert result == test_case["expected"], f"Failed on test case: {test_case['name']}"


@pytest.mark.asyncio
async def test_get_user_from_session(monkeypatch: pytest.MonkeyPatch) -> None:
    """Test session handling for user retrieval"""
    # Success case
    mock_session = Mock(get_decoded=Mock(return_value={"_auth_user_id": "123"}))

    class MockQuerySet:
        async def afirst(self):  # type: ignore[no-untyped-def]
            return mock_session

    monkeypatch.setattr("django.contrib.sessions.models.Session.objects", Mock(filter=lambda **kwargs: MockQuerySet()))

    result = await get_user_from_session("test_session")
    assert result == "123"

    # Test no session case
    monkeypatch.setattr(
        "django.contrib.sessions.models.Session.objects",
        Mock(filter=lambda **kwargs: Mock(afirst=AsyncMock(return_value=None))),
    )
    assert await get_user_from_session("test_session") is None

    # Test exception case
    monkeypatch.setattr(
        "django.contrib.sessions.models.Session.objects",
        Mock(filter=lambda **kwargs: Mock(afirst=AsyncMock(side_effect=Exception("Test error")))),
    )
    assert await get_user_from_session("test_session") is None

    # Test invalid session data
    mock_session = Mock(get_decoded=Mock(return_value={}))
    monkeypatch.setattr(
        "django.contrib.sessions.models.Session.objects",
        Mock(filter=lambda **kwargs: Mock(afirst=AsyncMock(return_value=mock_session))),
    )
    assert await get_user_from_session("test_session") is None


@pytest.mark.asyncio
async def test_log_admin_action_admin_logging_disabled(
    mock_request: Mock, mock_call_next: AsyncMock, settings: SettingsWrapper, caplog: pytest.LogCaptureFixture
) -> None:
    settings.ENABLE_ADMIN_LOGGING = False

    with caplog.at_level(logging.INFO):
        await log_admin_requests(mock_request, mock_call_next)
        assert len(caplog.records) == 0
        mock_request.assert_not_called()
        mock_call_next.assert_called_once_with(mock_request)


@pytest.mark.asyncio
async def test_log_admin_action(mock_request: Mock, mock_response: Mock, caplog: pytest.LogCaptureFixture) -> None:
    """Test admin action logging"""
    # Test successful logging
    with caplog.at_level(logging.INFO):
        with patch("app.fastapi.get_user_from_session", new_callable=AsyncMock) as mock_get_user:
            mock_get_user.return_value = "123"
            await log_admin_action(mock_request, mock_response, "/api/admin/meetingsapp/organization/", "viewed")
            assert all(text in caplog.text for text in ["Admin Action: User 123", "meetingsapp", "viewed"])

    # Clear the log for the next test
    caplog.clear()

    # Test no session case
    mock_request.cookies = {}
    with caplog.at_level(logging.INFO):
        await log_admin_action(mock_request, mock_response, "/api/admin/meetingsapp/organization/", "viewed")
        assert not caplog.text  # Check that nothing was logged

    # Clear the log again
    caplog.clear()

    # Test error case
    mock_request.cookies = {"sessionid": "invalid_session"}
    with caplog.at_level(logging.ERROR):
        with patch("app.fastapi.get_user_from_session", AsyncMock(side_effect=Exception("Test error"))):
            await log_admin_action(mock_request, mock_response, "/api/admin/meetingsapp/organization/", "viewed")
            assert "Error processing admin request" in caplog.text


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_case",
    [
        {
            "name": "normal_request",
            "path": "/api/v2/health",
            "should_log": True,
            "description": "Normal request should be logged",
        },
        {
            "name": "static_path",
            "path": "/static/image.png",
            "should_log": False,
            "description": "Static files should not be logged",
        },
        {
            "name": "admin_path",
            "path": "/api/admin/meetingsapp/organization/",
            "should_log": True,
            "description": "Admin paths should be logged",
        },
    ],
)
async def test_log_requests_middleware(
    mock_request: Mock, mock_call_next: AsyncMock, test_case: dict[str, Any]
) -> None:
    mock_request.url.path = test_case["path"]
    mock_request.method = "POST"

    with patch("app.fastapi.logger") as mock_logger:
        response = await log_requests(mock_request, mock_call_next)
        assert response.status_code == 200

        if test_case["should_log"]:
            mock_logger.info.assert_called_once_with(
                "request: %s %s, response %s", mock_request.method, mock_request.url.path, 200
            )
        else:
            mock_logger.info.assert_not_called()


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_case",
    [
        {
            "name": "normal_form_request",
            "path": "/api/admin/meetingsapp/organization/",
            "method": "POST",
            "headers": {"content-type": "application/x-www-form-urlencoded"},
            "body": b"action=delete_selected",
            "should_log": True,
            "expected_action": "delete_selected",
            "description": "Normal form POST request should be logged with action",
        },
        {
            "name": "multipart_form_request",
            "path": "/api/admin/meetingsapp/organization/",
            "method": "POST",
            "headers": {"content-type": "multipart/form-data; boundary=boundary"},
            "body": b'--boundary\r\nContent-Disposition: form-data; name="file"\r\n\r\ntest\r\n--boundary--',
            "should_log": True,
            "expected_action": "viewed",
            "description": "Multipart form request should be logged as viewed",
        },
        {
            "name": "static_path",
            "path": "/api/admin/static/image.png",
            "method": "GET",
            "headers": {},
            "body": b"",
            "should_log": False,
            "expected_action": None,
            "description": "Static files in admin should not be logged",
        },
        {
            "name": "non_admin_path",
            "path": "/api/v2/health",
            "method": "GET",
            "headers": {},
            "body": b"",
            "should_log": False,
            "expected_action": None,
            "description": "Non-admin paths should be skipped entirely",
        },
    ],
)
async def test_log_admin_requests_middleware(
    mock_request: Mock, mock_call_next: AsyncMock, test_case: dict[str, Any], settings: SettingsWrapper
) -> None:
    settings.ENABLE_ADMIN_LOGGING = True
    mock_request.url.path = test_case["path"]
    mock_request.method = test_case["method"]
    mock_request.headers = test_case["headers"]
    mock_request.body = AsyncMock(return_value=test_case["body"])

    with patch("app.fastapi.log_admin_action") as mock_log_admin:
        response = await log_admin_requests(mock_request, mock_call_next)
        assert response.status_code == 200

        if test_case["should_log"]:
            if test_case["expected_action"]:
                mock_log_admin.assert_awaited_once_with(
                    mock_request, response, test_case["path"], test_case["expected_action"]
                )
            else:
                mock_log_admin.assert_awaited_once()
        else:
            assert not mock_log_admin.called
