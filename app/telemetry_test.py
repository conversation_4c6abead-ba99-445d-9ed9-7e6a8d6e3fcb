import logging
from unittest.mock import MagicMock, patch

from opentelemetry import metrics, trace

from app.telemetry import <PERSON><PERSON><PERSON>og<PERSON>ilter, configure_telemetry


@patch("app.telemetry.trace.get_current_span")
def test_filter_adds_trace_and_span_ids(mock_get_current_span: MagicMock) -> None:
    mock_get_current_span.return_value.get_span_context.return_value = MagicMock(
        is_valid=True,
        trace_id=0x12345,
        span_id=0x6789,
    )

    record = logging.LogRecord(
        name="test",
        level=logging.INFO,
        pathname="test_path",
        lineno=10,
        msg="Test message",
        args=None,
        exc_info=None,
    )
    assert not hasattr(record, "otelSpanID")
    assert not hasattr(record, "otelTraceID")

    result = TraceIDLogFilter().filter(record)

    assert result
    assert hasattr(record, "otelSpanID") and record.otelSpanID == "0000000000006789"
    assert hasattr(record, "otelTraceID") and record.otelTraceID == "00000000000000000000000000012345"


@patch("app.telemetry.trace.get_current_span")
def test_filter_adds_trace_and_span_ids_for_invalid_span(mock_get_current_span: MagicMock) -> None:
    mock_get_current_span.return_value = trace.INVALID_SPAN

    record = logging.LogRecord(
        name="test",
        level=logging.INFO,
        pathname="test_path",
        lineno=10,
        msg="Test message",
        args=None,
        exc_info=None,
    )

    result = TraceIDLogFilter().filter(record)

    assert result
    assert hasattr(record, "otelSpanID") and record.otelSpanID == "0000000000000000"
    assert hasattr(record, "otelTraceID") and record.otelTraceID == "00000000000000000000000000000000"


def test_configure_telemetry() -> None:
    configure_telemetry("test_service")

    tracer = trace.get_tracer_provider()
    assert tracer is not None

    meter = metrics.get_meter_provider()
    assert meter is not None

    # Testing that each of the instrumentations are called would be a change detector. Testing that
    # they configure without crashing (what this test does) is not a great test, but it is not
    # useless.
