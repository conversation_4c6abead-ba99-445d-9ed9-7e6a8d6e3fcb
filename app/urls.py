from django.conf import settings
from django.contrib import admin
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.staticfiles.urls import staticfiles_urlpatterns
from django.http import HttpResponse
from django.urls import URL<PERSON>attern, URLResolver, include, path

from deepinsights.meetingsapp.organization_wizard import OrganizationOnboardingWizard

urlpatterns: list[URLPattern | URLResolver] = [
    # Health check
    path("api/v1/health/", lambda request: HttpResponse(status=200)),
    # User management
    path("api/v1/users/", include("deepinsights.users.urls", namespace="users")),
]

if settings.ENABLE_ADMIN:
    # Google SSO for Django admin
    urlpatterns += [
        path("api/admin/google_sso/", include("django_google_sso.urls", namespace="django_google_sso")),
        # Main admin pages
        path("api/admin/", admin.site.urls),
        path(
            "api/admin/organization-onboarding/",
            staff_member_required(OrganizationOnboardingWizard.as_view()),
            name="organization_onboarding",
        ),
    ]

# Static file serving, copied from django.conf.urls.static
urlpatterns += staticfiles_urlpatterns()
