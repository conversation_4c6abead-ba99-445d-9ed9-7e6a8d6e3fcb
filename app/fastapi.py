# Import this before other modules that may import Django models, to ensure that Django is set up.
from app.django_wsgi import application as django_application  # isort:skip

import logging
import re
from asyncio import Future
from typing import Callable

from a2wsgi import WSGIMiddleware
from asgiref.sync import sync_to_async
from django.conf import settings
from django.contrib.sessions.models import Session
from django.utils import timezone
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor

from api.internal_api import internal_api
from api.public_api import public_api

logger = logging.getLogger(__name__)

# The root Fast API ASGI application.
#
# This application does not have any routes or routers of its own. It only mounts other FastAPI apps
# (which have APIs) and the Django WSGI app.
app = FastAPI(
    debug=settings.DEBUG,
    openapi_url=None,
    root_path_in_servers=False,
)

# Mount the FastAPI applications for the public and internal APIs before the catchall mount for the
# Django application.
app.mount(public_api.root_path, public_api)
app.mount(internal_api.root_path, internal_api)

# This must be the last thing mounted: as a catchall, we want all other
# requests to go to the Django WSGI app.
app.mount("/", WSGIMiddleware(django_application))  # type: ignore[arg-type]


# Add the middleware after the WSGI application is loaded, to ensure that the Django
# application is available (so we can reuse the CORS settings).
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=settings.CORS_ALLOW_HEADERS,
)


@app.middleware("http")
async def log_requests(request: Request, call_next: Callable[[Request], Future[Response]]) -> Response:
    """Middleware to log all incoming requests except static files and admin actions."""
    response = await call_next(request)
    try:
        path = request.url.path

        # Skip logging static files
        if "/static/" not in path:
            logger.info("request: %s %s, response %s", request.method, path, response.status_code)

    except Exception as e:
        logger.error("Critical middleware error: ", exc_info=e)
        return await call_next(request) if "response" not in locals() else response
    return response


# Safely get user_id from session
async def get_user_from_session(session_key: str) -> str | None:
    try:
        get_session = Session.objects.filter(session_key=session_key, expire_date__gt=timezone.now()).afirst
        session = await get_session()
        if session:
            session_data = await sync_to_async(session.get_decoded)()
            return session_data.get("_auth_user_id")
    except Exception as e:
        logger.error("Error getting user from session: ", exc_info=e)
    return None


# Safely extract model name from path
def get_model_name(path: str) -> str | None:
    try:
        path_parts = [p for p in path.split("/") if p]
        return path_parts[2] if len(path_parts) > 2 else None
    except Exception:
        return None


async def get_admin_post_data(body_bytes: bytes) -> dict:  # type: ignore[type-arg]
    """
    Safely attempts to get POST data from raw bytes into a dictionary
    """
    try:
        body_str = body_bytes.decode()
        data = {}
        for item in body_str.split("&"):
            if "=" in item:
                key, value = item.split("=", 1)
                data[key] = value
        return data
    except Exception as e:
        logger.error("Error parsing POST data:", exc_info=e)
        return {}


async def get_admin_action(body: bytes | None, path: str, content_type: str | None = None) -> str:
    if "/add/" in path:
        return "add"
    elif "/delete/" in path:
        return "delete"
    elif re.search(r"/\d+/change/?$", path):
        return "change"

    # Only try to parse body if it's form data, not multipart
    if body and content_type and "multipart/form-data" not in content_type:
        try:
            post_data = await get_admin_post_data(body)
            action = post_data.get("action", "")
            if action:
                return action  # type: ignore[no-any-return]
        except UnicodeDecodeError:
            logger.warning("Could not decode POST body, skipping action extraction")

    if any(part.isdigit() for part in path.split("/")):
        return "change"

    return "viewed"


async def log_admin_action(request: Request, response: Response, path: str, action: str) -> None:
    try:
        session_key = request.cookies.get("sessionid")
        if not session_key:
            return

        user_id = await get_user_from_session(session_key)
        if not user_id:
            return

        model = get_model_name(path)
        if not model:
            return

        logger.info(
            "Admin Action: User %s %s %s, path: %s, method: %s, response: %s",
            user_id,
            action,
            model,
            path,
            request.method,
            response.status_code,
            extra={
                "user_id": user_id,
                "action": action,
                "model": model,
                "path": path,
                "method": request.method,
                "status_code": response.status_code,
            },
        )
    except Exception as e:
        logger.error("Error processing admin request: ", exc_info=e, extra={"path": path})


@app.middleware("http")
async def log_admin_requests(request: Request, call_next: Callable[[Request], Future[Response]]):  # type: ignore[no-untyped-def]
    """Middleware to specifically handle logging of admin actions."""
    if not settings.ENABLE_ADMIN_LOGGING:
        return await call_next(request)

    response = None
    try:
        path = request.url.path

        # Only process admin paths
        if not path.startswith("/api/admin/"):
            return await call_next(request)

        # Handle POST requests by storing the body
        # This is necessary because the body is a stream and can only be read once
        # If we don't clone it, the body will be empty when we try to read it later
        # This is a known issue with Starlette and FastAPI
        body = None
        if request.method == "POST":
            body = await request.body()

        # Call next middleware and get response
        response = await call_next(request)

        # Skip static files
        if "/static/" in path:
            return response

        # Determine admin action and log it
        content_type = request.headers.get("content-type")
        action = "viewed" if request.method == "GET" else await get_admin_action(body, path, content_type)
        await log_admin_action(request, response, path, action)

        return response
    except Exception as e:
        logger.error("Critical middleware error in admin logging: ", exc_info=e)
        if response:
            return response if response else await call_next(request)


FastAPIInstrumentor().instrument_app(app)
