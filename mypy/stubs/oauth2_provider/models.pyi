from typing import Sequence

from django.db import models

from deepinsights.users.models.user import User

class AbstractApplication(models.Model):
    GRANT_CLIENT_CREDENTIALS: str
    user: models.ForeignKey[User | None]

    class Meta:
        abstract: bool

class AbstractGrant(models.Model):
    class Meta:
        abstract: bool

class AbstractAccessToken(models.Model):
    class Meta:
        abstract: bool

class AbstractRefreshToken(models.Model):
    class Meta:
        abstract: bool
        unique_together: Sequence[Sequence[str] | str]

class AbstractIDToken(models.Model):
    class Meta:
        abstract: bool
