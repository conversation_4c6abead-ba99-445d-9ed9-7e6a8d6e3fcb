#!/bin/bash -e

function clean_up()
{
    echo "Exiting..."
    kill %1
    exit
}

trap clean_up EXIT

WORKER_COUNT=${1:-1}

# Starting Celery beat
celery -A app beat --loglevel=info &

# Starting Celery workers
CELERY_WORKER_CMD="celery -A app worker --loglevel=info --concurrency ${WORKER_COUNT}"
if [ -n "${ENABLE_VSCODE_DEBUGGING}" ]; then
  echo "Attach VSCode debugger (0.0.0.0:5679) to proceed..."
  if [[ "$(uname -s)" == "Darwin" ]]; then
    export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES
  fi
  python3 -m debugpy --listen 0.0.0.0:5679 --wait-for-client -m ${CELERY_WORKER_CMD}
else
  ${CELERY_WORKER_CMD}
fi
