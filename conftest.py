import uuid

import pytest
from django.test.signals import clear_cache_handlers
from pytest_django.fixtures import SettingsWrapper

from deepinsights.flags import flag_loader
from deepinsights.meetingsapp.migrations import (
    life_event_data,
    pre_meeting_agenda_generator_prompt_text,
    search_prompt,
    template_prompt,
    unstructured_meeting_data_schema,
    unstructured_meeting_data_schema_name,
)
from deepinsights.meetingsapp.models.meeting_summary_email_template import MeetingSummaryEmailTemplate
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.prompt import Prompt
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingDataTemplate
from deepinsights.meetingsapp.models.structured_meeting_data_schema import StructuredMeetingDataSchema
from deepinsights.ml_agents.migrations import (
    agenda_followup_agentconfig_user_prompt,
    meetingtype_for_note_prompt,
    workflow_for_task_prompt,
)
from deepinsights.ml_agents.models.agent_config import AgentConfig


def _ensure_bento_template_present() -> None:
    prompt, _ = Prompt.objects.update_or_create(
        name="Test Prompt",
        unique_name="test_prompt",
        user_prompt="Schema: {schema}\nData: {data}\nExample: {example}\nTranscript: {transcript}",
    )

    schema, _ = StructuredMeetingDataSchema.objects.update_or_create(schema={"key": "value"})
    StructuredMeetingDataTemplate.objects.update_or_create(
        title="Life Events",
        internal_name="Bento life events Test",
        kind=StructuredMeetingDataTemplate.Kind.BENTO_LIFE_EVENTS,
        initial_data=life_event_data,
        schema_definition=schema,
        prompt=prompt,
    )


def _ensure_meeting_types_present() -> None:
    MeetingType.objects.update_or_create(key="client", name="Client", category="client", everyone=True)
    MeetingType.objects.update_or_create(key="debrief", name="Debrief", category="debrief", everyone=True)
    MeetingType.objects.update_or_create(key="internal", name="Internal", category="internal", everyone=True)


def _ensure_template_generator_prompt_present() -> None:
    Prompt.objects.update_or_create(
        name="Template Generator Prompt",
        unique_name="template_generator_prompt_v1",
        version="1.0",
        user_prompt=template_prompt,
        version_notes="Initial version of template generator prompt",
    )


def _ensure_unstructured_meeting_data_schema_present() -> None:
    # First ensure the main schema exists
    try:
        StructuredMeetingDataSchema.objects.update_or_create(
            name=unstructured_meeting_data_schema_name,
            defaults={
                "schema": unstructured_meeting_data_schema,
                "description": "Schema for unstructured textual content, like agendas",
            },
        )
    except Exception:
        # If update_or_create fails, try to get the existing schema
        schema = StructuredMeetingDataSchema.objects.get(name=unstructured_meeting_data_schema_name)
        schema.schema = unstructured_meeting_data_schema
        schema.description = "Schema for unstructured textual content, like agendas"
        schema.save()

    # Then ensure the unstructured_text schema exists
    try:
        StructuredMeetingDataSchema.objects.update_or_create(
            name="unstructured_text", defaults={"schema": {}, "description": "Schema for unstructured text data"}
        )
    except Exception:
        # If update_or_create fails, try to get the existing schema
        schema = StructuredMeetingDataSchema.objects.get(name="unstructured_text")
        schema.schema = {}
        schema.description = "Schema for unstructured text data"
        schema.save()


def _ensure_structured_data_schema_present() -> None:
    # Create schema with the structure needed for migration 0127
    structured_review_schema = {
        "$defs": {
            "review_entry": {"properties": {"kind": {"enum": ["header", "toggle", "select", "multiselect", "bullet"]}}}
        }
    }

    StructuredMeetingDataSchema.objects.update_or_create(
        name="structured_data_schema",
        defaults={
            "schema": structured_review_schema,
            "description": "JSON Schema for structured meeting follow-up data",
        },
    )


def _ensure_agenda_followup_prompt_present() -> None:
    """Ensure the agenda followup prompt from migration 0112 is present."""
    Prompt.objects.update_or_create(
        name="Agenda Follow-up Generator",
        unique_name="agenda_followup_generator",
        defaults={"user_prompt": "non-empty", "version": "1.0"},
    )


def _ensure_meeting_prompts_present() -> None:
    """Ensure meeting-related prompts for prompt_builder tests are present."""
    # Create prompts with the exact format used in build_prompt_for_meeting: "{category}_meeting_{prompt_type.value}"
    # For client meeting type
    Prompt.objects.update_or_create(
        name="Client Meeting Summary",
        unique_name="client_meeting_summary",
        defaults={"user_prompt": "Context: {context}\nTranscript: {transcript}"},
        version="test",
    )
    Prompt.objects.update_or_create(
        name="Client Meeting Tasks",
        unique_name="client_meeting_tasks_and_takeaways",
        defaults={"user_prompt": "REGULAR Context: {context}\nTranscript: {transcript}"},
        version="test",
    )
    Prompt.objects.update_or_create(
        name="Client Meeting Detailed Tasks",
        unique_name="client_meeting_tasks_and_takeaways_detailed",
        defaults={"user_prompt": "DETAILED Context: {context}\nTranscript: {transcript}"},
        version="test",
    )

    # For internal meeting type
    Prompt.objects.update_or_create(
        name="Internal Meeting Summary",
        unique_name="internal_meeting_summary",
        defaults={"user_prompt": "Context: {context}\nTranscript: {transcript}"},
        version="test",
    )
    Prompt.objects.update_or_create(
        name="Internal Meeting Tasks",
        unique_name="internal_meeting_tasks_and_takeaways",
        defaults={"user_prompt": "Context: {context}\nTranscript: {transcript}"},
        version="test",
    )

    # Additional prompt for structured data tests
    Prompt.objects.update_or_create(
        name="Test Prompt Without Format Keys",
        unique_name="test_prompt_no_format",
        defaults={"user_prompt": "Prompt with no format keys."},
        version="test",
    )

    # Meeting Agenda Generator prompt
    Prompt.objects.update_or_create(
        name="Meeting Agenda Generator",
        unique_name="meeting_agenda_generator",
        defaults={"user_prompt": pre_meeting_agenda_generator_prompt_text, "version": "1.0"},
    )

    Prompt.objects.update_or_create(
        name="Agenda Completion Generator",
        unique_name="agenda_completion_generator",
        defaults={
            "user_prompt": "# AGENDA TEMPLATE (MARKDOWN FORMAT): {agenda_template} # MEETING TRANSCRIPT {transcript}",
            "version": "1.0",
        },
    )


def _ensure_search_prompt_present() -> None:
    Prompt.objects.update_or_create(
        name="search prompt",
        unique_name="search_prompt_v1",
        defaults={
            "user_prompt": search_prompt,
            "version_notes": "Initial version of search prompt",
            "version": "1.0",
        },
    )


def _ensure_agentic_agenda_followup_present() -> None:
    AgentConfig.objects.update_or_create(
        name="agenda_followup_generator",
        user_prompt=agenda_followup_agentconfig_user_prompt,
        version_notes="Initial version",
        user_prompt_metadata_example={"agenda_template": "", "transcript": ""},
        system_prompt="",
        system_prompt_metadata={"org_name": "Test Org"},
        output_validation_schema="structured_text",
        llm_model="gpt-4o-2024-08-06",
    )


def _ensure_default_email_template_present() -> None:
    """Ensure the default email template is present for testing."""

    MeetingSummaryEmailTemplate.objects.create(
        name="Default Template", everyone=True, email_template_content="Default content"
    )


def _ensure_autopopulate_email_template_present() -> None:
    """Ensure the autopopulate email template is present for testing."""
    MeetingSummaryEmailTemplate.objects.update_or_create(
        internal_name="autopopulate_email_prompt",
        defaults={
            "name": "Auto-populate Email Prompt Template",
            "generation_prompt": "Test default generation prompt",
            "system_prompt": "Test system prompt",
            "everyone": False,
            "use_html": False,
        },
    )


def _ensure_agentconfig_workflow_for_task_present() -> None:
    AgentConfig.objects.update_or_create(
        name="workflow_for_task",
        user_prompt=workflow_for_task_prompt,
        version_notes="First version",
        user_prompt_metadata_example={"note_context": "test", "transcript": "test"},
        user_prompt_metadata_schema=["note_context", "transcript"],
        system_prompt=None,
        system_prompt_metadata=None,
        output_validation_schema="list_subset",
        llm_model="gpt-4o-2024-08-06",
    )


def _ensure_agentconfig_meetingtype_for_note_present() -> None:
    AgentConfig.objects.update_or_create(
        name="meetingtype_for_note",
        user_prompt=meetingtype_for_note_prompt,
        version_notes="First version",
        user_prompt_metadata_example={"note_context": "test"},
        user_prompt_metadata_schema=["note_context"],
        system_prompt=None,
        system_prompt_metadata=None,
        output_validation_schema="list_subset",
        llm_model="gpt-4o-2024-08-06",
    )


@pytest.fixture(autouse=True)
def ensure_data_migration_objects_present(db: None) -> None:
    _ensure_meeting_types_present()
    _ensure_bento_template_present()
    _ensure_template_generator_prompt_present()
    _ensure_unstructured_meeting_data_schema_present()
    _ensure_structured_data_schema_present()
    _ensure_agenda_followup_prompt_present()
    _ensure_meeting_prompts_present()
    _ensure_search_prompt_present()
    _ensure_agentic_agenda_followup_present()
    _ensure_default_email_template_present()
    _ensure_autopopulate_email_template_present()
    _ensure_agentconfig_workflow_for_task_present()
    _ensure_agentconfig_meetingtype_for_note_present()


@pytest.fixture(autouse=True)
def load_flags(db: None) -> None:
    flag_loader.load_flags()


@pytest.fixture(autouse=True)
def isolated_cache(settings: SettingsWrapper) -> None:
    cache_version = uuid.uuid4().hex

    for name in settings.CACHES.keys():
        settings.CACHES[name]["VERSION"] = cache_version

    clear_cache_handlers(setting="CACHES")
