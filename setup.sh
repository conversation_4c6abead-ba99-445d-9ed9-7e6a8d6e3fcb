set -e

# Check the python version
MINOR_VERSION=$(python3 -c "import sys; print(sys.version_info[1])")
if [ ${MINOR_VERSION} -ne "11" ]; then
  echo "You must install and use python 3.11 for this project. For"\
    "the purposes of this setup, ensure that the python3 command is"\
    "a Python 3.11 binary."
  exit 1
fi

# Create a virtual environment
python3 -m venv venv

echo `pwd`
# Activate the virtual environment
source venv/bin/activate

pip install pip-tools

pip-sync requirements/local.txt

# Deactivate the virtual environment
deactivate


cd ../frontend-app

# Install npm dependencies
npm install
