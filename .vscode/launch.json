{"version": "0.2.0", "configurations": [{"name": "Debug running Django app", "type": "debugpy", "request": "attach", "connect": {"host": "localhost", "port": 5678}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "."}]}, {"name": "Debug running Celery app", "type": "debugpy", "request": "attach", "connect": {"host": "localhost", "port": 5679}, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "."}]}]}