{"[python]": {"editor.defaultFormatter": "charliermarsh.ruff", "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit"}}, "editor.formatOnSave": true, "python.analysis.packageIndexDepths": [{"name": "", "depth": 4, "includeAllSymbols": true}], "python.experiments.optInto": ["pythonTestAdapter"], "python.testing.pytestEnabled": true, "python.analysis.autoImportCompletions": true, "ruff.configuration": "pyproject.toml", "mypy-type-checker.importStrategy": "fromEnvironment", "mypy-type-checker.ignorePatterns": [".mypy_cache", "*__pycache__*", "staticfiles/*", "venv/*"]}