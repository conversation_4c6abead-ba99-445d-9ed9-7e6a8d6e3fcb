#!/bin/bash

# Set your working directory
BASE_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

cd $BASE_DIR
echo "Working directory: $BASE_DIR"

# Kill existing session if it exists
screen -X -S webapp_run quit > /dev/null 2>&1

# Start a new screen session
screen -dmS webapp_run

# Wait for session to be created
sleep 1
# rename the window to zsh
screen -S webapp_run -X screen -t "zsh_"
screen -S webapp_run  -p "zsh_" -X stuff "source ./venv/bin/activate
"
screen -S webapp_run -p "zsh_" -X stuff "echo 'zsh window started'
"
echo "started"
sleep 1
screen -S webapp_run -p "zsh_" -X stuff "source ./.env.secrets
"
screen -S webapp_run  -p "zsh_" -X stuff "source ./.env.vars
"

# Window 1: Django
screen -S webapp_run -X screen -t "django"
screen -S webapp_run -p "django" -X stuff "cd ${BASE_DIR}
"
screen -S webapp_run -p "django" -X stuff "source ./venv/bin/activate
"
screen -S webapp_run -p "django" -X stuff "echo 'Django window started'
"
echo "First window started"
sleep 1
screen -S webapp_run -p "django" -X stuff "source ./.env.secrets
"
screen -S webapp_run -p "django" -X stuff "source ./.env.vars
"
screen -S webapp_run -p "django" -X stuff "./start_django.sh
"

# Window 2: Frontend
screen -S webapp_run -X screen -t "FE"
screen -S webapp_run -p "FE" -X stuff "cd ${BASE_DIR}
"
screen -S webapp_run -p "FE" -X stuff "source ./venv/bin/activate
"
screen -S webapp_run -p "FE" -X stuff "echo 'Frontend window started'
"
echo "Second window started"
sleep 1
screen -S webapp_run -p "FE" -X stuff "source ./.env.secrets
"
screen -S webapp_run -p "FE" -X stuff "source ./.env.vars
"
screen -S webapp_run -p "FE" -X stuff "./start_frontend.sh
"

# Window 3: Celery
screen -S webapp_run -X screen -t "celery"
screen -S webapp_run -p "celery" -X stuff "cd ${BASE_DIR}
"
screen -S webapp_run -p "celery" -X stuff "source ./venv/bin/activate
"
screen -S webapp_run -p "celery" -X stuff "echo 'Celery window started'
"
echo "Starting celery"
sleep 1
screen -S webapp_run -p "celery" -X stuff "source ./.env.secrets
"
screen -S webapp_run -p "celery" -X stuff "source ./.env.vars
"
screen -S webapp_run -p "celery" -X stuff "./start_celery.sh
"

# Attach to the session
screen -r webapp_run
