set -e

SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )

if [ ! $(which pip-compile) ]; then
  echo "ERROR: pip-tools not installed; to install, run \`pip install pip-tools\`."
  exit 1
fi

function compile() {
  echo "Updating requirements from $1..."
  pip-compile --no-annotate --no-strip-extras -q "${@:2}" "$1"
}

cd "${SCRIPT_DIR}"

# Order is important; base must come before the others.
compile base.in "$@"
compile local.in "$@"
compile production.in "$@"

# Return a non-zero status if there are diffs.
git diff --quiet "*in" "*txt"
