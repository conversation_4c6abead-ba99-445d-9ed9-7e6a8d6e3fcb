-r base.txt

aioresponses
annotated-types
black  # https://github.com/psf/black
celery-types
coverage  # https://github.com/nedbat/coveragepy
debugpy
django-coverage-plugin==3.0.0  # https://github.com/nedbat/django_coverage_plugin
django-migration-linter  # https://github.com/3YOURMIND/django-migration-linter
djangorestframework-stubs  # https://github.com/typeddjango/djangorestframework-stubs
flake8  # https://github.com/PyCQA/flake8
flake8-isort  # https://github.com/gforcada/flake8-isort
mypy  # https://github.com/python/mypy
pre-commit  # https://github.com/pre-commit/pre-commit
psycopg2-binary==2.9.6  # https://github.com/psycopg/psycopg2
pylint-django  # https://github.com/PyCQA/pylint-django
pytest  # https://github.com/pytest-dev/pytest
pytest-asyncio
pytest-cov
pytest-django  # https://github.com/pytest-dev/pytest-django
pytest-dotenv
pytest-httpx
pytest-md  # https://github.com/hackebrot/pytest-md
pytest-rerunfailures  # https://github.com/pytest-dev/pytest-rerunfailures
pytest-sugar  # https://github.com/Frozenball/pytest-sugar
pytest-xdist
ruff
simple-mockforce  # https://github.com/Kicksaw-Consulting/simple-mockforce/
types-Deprecated
types-Markdown
types-PyYAML
types-Pygments
types-boto3
types-cachetools
types-cffi
types-colorama
types-docutils
types-jmespath
types-jsonschema
types-psutil
types-psycopg2
types-pyOpenSSL
types-python-dateutil
types-pytz
types-redis
types-requests
types-setuptools
types-ujson
