# ==== pytest ====
[tool.pytest.ini_options]
minversion = "6.0"
addopts = "--ds=config.settings.test --reuse-db -n auto --dist loadfile -p no:ddtrace -p no:ddtrace.pytest_bdd -p no:ddtrace.pytest_benchmark"
python_files = [
    "tests.py",
    "test_*.py",
    "*test.py",
    "*tests.py",
]
asyncio_default_fixture_loop_scope = "function"
env_files = [
    ".env.vars",
    ".env.secrets",
]

# ==== Coverage ====
[tool.coverage.run]
include = [
    "api/**",
    "app/**",
    "config/**",
    "deepinsights/**",
    "prod_scripts/**",
]
omit = [
    "*/migrations/*",
    "*/tests/*",
    "*test.py",
    "venv/*",
    "requirements/*txt",
    "staticfiles/*",
]
plugins = ["django_coverage_plugin"]


# ==== black ====
[tool.black]
line-length = 120
target-version = ['py311']


# ==== isort ====
[tool.isort]
profile = "black"
line_length = 120
known_first_party = [
    "api",
    "app",
    "config",
    "deepinsights",
]
skip = ["venv/"]
skip_glob = ["**/migrations/*.py"]


# ==== mypy ====
[tool.mypy]
python_version = "3.11"
strict = true
plugins = [
    "mypy_django_plugin.main",
    "mypy_drf_plugin.main",
    "pydantic.mypy",
    "mypy/django_celery_task_plugin.py",
]
mypy_path = "mypy/stubs"

[[tool.mypy.overrides]]
# Django migrations should not produce any errors:
module = "*.migrations.*"
ignore_errors = true

[[tool.mypy.overrides]]
module = [
    "celery.*",
    "config.celery",
    "deepgram",
    "deepgram._types",
    "deepgram.transcription",
    "django_celery_beat.*",
    "django_google_sso.*",
    "django_json_schema_editor.fields",
    "django_json_widget.*",
    "django_jsonform.*",
    "environ",
    "formtools.*",
    "google_auth_oauthlib.*",
    "googleapiclient.*",
    "gunicorn.arbiter",
    "gunicorn.workers.base",
    "m3u8_generator",
    "msal",
    # This has type info, but is missing a py.typed file.
    "msgraph_core",
    # OAuthLib has a stubs library, but the types for the APIs that we needed
    # to use were overly restrictive and didn't match the implementation.
    "oauthlib.*",
    "oauth2_provider.*",
    "office365.*",
    "phonenumber_field.modelfields",
    "phonenumber_field.phonenumber",
    "simple_history.*",
    "simple_mockforce",
    "twilio.*",  # Twilio has a stubs library, but in practice it seemed to make type checking worse.
    "whitenoise",
]
ignore_missing_imports = true

[tool.django-stubs]
django_settings_module = "config.settings.test"


# ==== PyLint ====
[tool.pylint.MASTER]
load-plugins = [
    "pylint_django",
]
django-settings-module = "config.settings.test"

[tool.pylint.FORMAT]
max-line-length = 120

[tool.pylint."MESSAGES CONTROL"]
disable = [
    "missing-docstring",
    "invalid-name",
]

[tool.pylint.DESIGN]
max-parents = 13

[tool.pylint.TYPECHECK]
generated-members = [
    "REQUEST",
    "acl_users",
    "aq_parent",
    "[a-zA-Z]+_set{1,2}",
    "save",
    "delete",
]

# ==== Ruff ====

[tool.ruff]
fix = true

ignore = ["F405", "F403", "F841", "F601", "E721"]
line-length = 120
exclude = [
    "migrations/",
    "__init__.py",
    "manage.py",
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["E402"]


[tool.ruff.format]
exclude = [
    "migrations/",
    "__init__.py",
    "manage.py",
]

# ==== Django Migration Linter ====
[tool.django_migration_linter]
sql-analyser = "postgres"
unapplied_migrations = true
warnings-as-errors = true
