services:
  # Reverse proxy
  nginx:
    image: nginx:otel
    ports:
      - 80:80
      - 443:443
    environment:
      - NGINX_HOST=${NGINX_HOST:-localhost}
    restart: always
    volumes:
      - ./nginx/conf/:/etc/nginx/templates/:ro
      - ./nginx/startup_scripts/:/docker-entrypoint.d/startup_scripts/:ro
      - ./nginx/www/:/www/:ro
      - ./certbot/www:/var/www/certbot/:ro
      - ./certbot/conf/:/etc/nginx/ssl/:ro
    depends_on:
      - django
      - web
    labels:
      com.datadoghq.ad.check_names: '["nginx"]'
      com.datadoghq.ad.init_configs: "[{}]"
      com.datadoghq.ad.instances: '[{"nginx_status_url": "http://%%host%%:81/nginx_status/"}]'
      com.datadoghq.ad.logs: '[{"source":"nginx","service":"nginx"}]'

  # Guide to using prod certbot:
  # https://mindsers.blog/post/https-using-nginx-certbot-docker/
  # Renew certs: $ docker compose run --rm certbot renew
  certbot:
    image: certbot/certbot:latest
    volumes:
      - ./certbot/www/:/var/www/certbot/:rw
      - ./certbot/conf/:/etc/letsencrypt/:rw

  # Database and cache
  db:
    profiles: [local_database]
    image: public.ecr.aws/docker/library/postgres:14-alpine
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=deepinsights
    expose:
      - 5432
    volumes:
      - db:/var/lib/postgresql/data
    container_name: deepinsight_db
    labels:
      com.datadoghq.ad.logs: >-
        [{"source": "postgresql" }]
  redis:
    restart: always
    image: public.ecr.aws/docker/library/redis:7.4.3-alpine
    expose:
      - 6379
    labels:
      com.datadoghq.ad.logs: >-
        [{ "source": "redis" }]

  # Monitoring
  datadog:
    profiles: [datadog]
    image: gcr.io/datadoghq/agent:7
    container_name: dd-agent
    restart: always
    environment:
      - DD_API_KEY=${DATADOG_API_KEY:-""}
      - DD_SITE=us5.datadoghq.com
      # We have been (ab)using the Datadog hostname to identify the environment. This maintains that
      # consistency (for now); in the future, this should use the real hostname of the machine (since
      # it's possible to have multiple hosts for an environment and vice versa).
      - DD_HOSTNAME=${ENV:-""}
      - DD_ENV=${ENV:-dev}
      - DD_APM_ENABLED=true
      - DD_APM_ENV=${ENV:-dev}
      - DD_APM_NON_LOCAL_TRAFFIC=true
      - DD_DOGSTATSD_NON_LOCAL_TRAFFIC=true
      - DD_LOGS_ENABLED=true
      - DD_LOGS_CONFIG_CONTAINER_COLLECT_ALL=true
      - DD_DOGSTATSD_NON_LOCAL_TRAFFIC=true
      - DD_CONTAINER_EXCLUDE=image:gcr.io/datadoghq/agent*
      - DD_CONTAINER_EXCLUDE_METRICS=image:gcr.io/datadoghq/agent*
      - DD_CONTAINER_EXCLUDE_LOGS=image:gcr.io/datadoghq/agent*
      - DD_TAGS=backend_release_version:${BACKEND_TAG:-unknown} frontend_release_version:${FRONTEND_TAG:-unknown}
      - DD_OTLP_CONFIG_RECEIVER_PROTOCOLS_GRPC_ENDPOINT=0.0.0.0:4317
      - DD_OTLP_CONFIG_RECEIVER_PROTOCOLS_HTTP_ENDPOINT=0.0.0.0:4318
      # Workaround for an issue with OpenTelemetry collection for some Datadog
      # agent versions:
      # https://docs.datadoghq.com/opentelemetry/setup/otlp_ingest_in_the_agent/?tab=docker#enabling-otlp-ingestion-on-the-datadog-agent
      - HOST_PROC=/proc
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /proc/:/host/proc/:ro
      - /sys/fs/cgroup/:/host/sys/fs/cgroup:ro
    expose:
      - 4317
      - 4318

  # Application
  django:
    restart: always
    image: 415362788362.dkr.ecr.us-east-1.amazonaws.com/zeplyn-backend:${BACKEND_TAG:-main}
    env_file:
      - ${BACKEND_ENV_FILE:-env.backend}
    environment:
      - DD_VERSION=${BACKEND_TAG:-main}
      - DD_SERVICE=django
      - DD_ENV=${ENV:-dev}
      - DD_AGENT_HOST=dd-agent
      # The v2 stack does not work well with Gunicorn.
      - DD_PROFILING_STACK_V2_ENABLED=false
    expose:
      - 8000
    container_name: deepinsight_django
    entrypoint: ./django_entrypoint
    healthcheck:
      test: curl --fail http://localhost:8000/api/v1/health/ || exit 1
      interval: 1m
      timeout: 3s
      start_period: 30s
      start_interval: 5s
      retries: 2
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: 10
    labels:
      com.datadoghq.ad.logs: >-
        [{
          "source": "python",
          "log_processing_rules": [
            {
              "type": "multi_line",
              "name": "django_logs",
              "pattern" : "([A-Z]+\\s*)?\\[?\\d{4}-(0?[1-9]|1[012])-(0?[1-9]|[12][0-9]|3[01])"
            }
          ]
        }]
  web:
    restart: always
    image: 415362788362.dkr.ecr.us-east-1.amazonaws.com/zeplyn-web:${FRONTEND_TAG:-main}
    env_file:
      - ${FRONTEND_ENV_FILE:-env.frontend}
    depends_on:
      - django
    expose:
      - 3000
    container_name: deepinsight_web
    environment:
      - USE_DOCKER=yes
      - NODE_ENV=production
      - DD_ENV=${ENV:-dev}
      - DD_SOURCE=frontend
      - DD_SERVICE=frontend
      - ZEPLYN_RELEASE_VERSION=${FRONTEND_TAG:-unknown}
      - ZEPLYN_ENV=${ENV:-dev}
    healthcheck:
      test: curl --fail http://localhost:3000 || exit 1
      interval: 1m
      timeout: 3s
      start_period: 30s
      start_interval: 5s
      retries: 2
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: 20
    labels:
      com.datadoghq.ad.logs: >-
        [{
          "source": "nodejs",
          "log_processing_rules": [{
              "type": "multi_line",
              "name": "start_with_bracketed_timestamp",
              "pattern" : "\\[(0?[1-9]|[12][0-9]|3[01]).(0?[1-9]|1[012]).\\d{4}"
          }]
        }]
  celery:
    restart: always
    image: 415362788362.dkr.ecr.us-east-1.amazonaws.com/zeplyn-backend:${BACKEND_TAG:-main}
    env_file:
      - ${BACKEND_ENV_FILE:-env.backend}
    environment:
      - DD_VERSION=${BACKEND_TAG:-main}
      - DD_SOURCE=backend
      - DD_SERVICE=celery
      - DD_ENV=${ENV:-dev}
      - DD_AGENT_HOST=dd-agent
    depends_on:
      redis:
        condition: service_started
      django:
        condition: service_healthy
    container_name: deepinsight_celery
    command: ./celery_entrypoint 3
    healthcheck:
      test: celery -A app inspect ping
      interval: 1m
      timeout: 10s
      start_period: 30s
      start_interval: 5s
      retries: 2
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: 10
    labels:
      com.datadoghq.ad.logs: >-
        [{
          "source": "python",
          "log_processing_rules": [
            {
              "type": "multi_line",
              "name": "start_with_timestamp",
              "pattern" : "\\[\\d{4}-(0?[1-9]|1[012])-(0?[1-9]|[12][0-9]|3[01])"
            }
          ]
        }]
  admin:
    restart: always
    image: 415362788362.dkr.ecr.us-east-1.amazonaws.com/zeplyn-backend:${BACKEND_TAG:-main}
    env_file:
      - ${BACKEND_ENV_FILE:-env.backend}
    environment:
      - DD_VERSION=${BACKEND_TAG:-main}
      - DD_SOURCE=backend
      - DD_SERVICE=admin
      - DD_ENV=${ENV:-dev}
      - DJANGO_ENABLE_ADMIN=True
      - DJANGO_LOAD_FLAGS=False
      - DD_AGENT_HOST=dd-agent
      # The v2 stack does not work well with Gunicorn.
      - DD_PROFILING_STACK_V2_ENABLED=false
    depends_on:
      - redis
    container_name: deepinsight_admin
    command: ./django_entrypoint admin
    healthcheck:
      test: curl --fail http://localhost:8000/api/admin || exit 1
      interval: 1m
      timeout: 3s
      start_period: 30s
      start_interval: 5s
      retries: 2
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: 10
    labels:
      com.datadoghq.ad.logs: >-
        [{
          "source": "python",
          "log_processing_rules": [
            {
              "type": "multi_line",
              "name": "django_logs",
              "pattern" : "([A-Z]+\\s*)?\\[?\\d{4}-(0?[1-9]|1[012])-(0?[1-9]|[12][0-9]|3[01])"
            }
          ]
        }]
    deploy:
      resources:
        limits:
          cpus: "0.25"
          memory: 1536M
        reservations:
          memory: 128M

volumes:
  db:
    driver: local
