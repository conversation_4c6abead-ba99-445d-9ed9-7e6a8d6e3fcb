#!/bin/bash -e

# Set up a temporary environment to run the tests.
tmpdir=$(mktemp -d 2>/dev/null || mktemp -d -t 'mytmpdir')
trap "rm -rf ${tmpdir}" EXIT
cp -r * "${tmpdir}"
cd "${tmpdir}"

# Validate the Docker configurations.
touch env.backend env.frontend
docker compose config -q
docker compose -f docker-compose.development.yml config -q
echo "Docker Compose config OK"

# Validate the Nginx config.

# Create certs so that the files are available.
certdir="certbot/conf/live/localhost"
mkdir -p "${certdir}"
openssl req -subj "/C=US/CN=localhost" -x509 -nodes -days 1 -newkey rsa:2048 \
  -keyout "${certdir}/privkey.pem" \
  -out "${certdir}/fullchain.pem"

# Validate the configuration
docker compose run --no-deps nginx nginx -t

echo "Nginx config OK"
